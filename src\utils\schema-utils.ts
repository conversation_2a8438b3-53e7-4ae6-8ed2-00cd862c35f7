// Schema utilities for IEPA 2025 Conference Registration
// Utilities for working with form schemas and date configurations

import {
  dateUtils,
  MEAL_SCHEDULE,
  CONFERENCE_DATES,
} from '@/lib/conference-config';
import {
  pricingUtils,
  PRICING_CONSTANTS,
  REGISTRATION_PRICING,
  SPONSORSHIP_PACKAGES,
} from '@/lib/pricing-config';

/**
 * Get meal options formatted for form schemas
 * Returns meal options with proper enum values and display names
 */
export const getMealSchemaOptions = () => {
  const mealOptions = dateUtils.getMealOptions();

  return {
    enum: mealOptions.map(meal => meal.id),
    enumNames: mealOptions.map(meal => meal.label),
    descriptions: mealOptions.map(meal => meal.description || ''),
  };
};

/**
 * Get meal options for UI components (checkboxes, etc.)
 */
export const getMealUIOptions = () => {
  return dateUtils.getMealOptions().map(meal => ({
    value: meal.id,
    label: meal.label,
    description: meal.description,
    date: meal.date,
    time: meal.time,
  }));
};

/**
 * Validate that selected meals are valid conference meals
 */
export const validateMealSelections = (selectedMeals: string[]): boolean => {
  const validMealIds = MEAL_SCHEDULE.map(meal => meal.id);
  return selectedMeals.every(mealId => validMealIds.includes(mealId));
};

/**
 * Get display names for selected meals
 */
export const getMealDisplayNames = (selectedMealIds: string[]): string[] => {
  const mealOptions = dateUtils.getMealOptions();
  return selectedMealIds
    .map(id => mealOptions.find(meal => meal.id === id)?.label)
    .filter(Boolean) as string[];
};

/**
 * Calculate total meal cost (if meals have individual pricing in the future)
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const calculateMealCost = (_selectedMealIds: string[]): number => {
  // Currently meals are included in registration
  // This function is prepared for future meal pricing
  return 0;
};

/**
 * Get conference date range as a formatted string
 */
export const getConferenceDateRange = (): string => {
  const { startDate, endDate } = CONFERENCE_DATES;
  return `${startDate.displayDate} - ${endDate.displayDate}`;
};

/**
 * Check if registration is still open based on conference dates
 */
export const isRegistrationOpen = (): boolean => {
  const { startDate } = CONFERENCE_DATES;
  const conferenceStart = new Date(startDate.date);
  const today = new Date();

  // Registration closes 7 days before conference starts
  const registrationDeadline = new Date(conferenceStart);
  registrationDeadline.setDate(registrationDeadline.getDate() - 7);

  return today <= registrationDeadline;
};

/**
 * Get days until registration closes
 */
export const getDaysUntilRegistrationCloses = (): number => {
  const { startDate } = CONFERENCE_DATES;
  const conferenceStart = new Date(startDate.date);
  const today = new Date();

  const registrationDeadline = new Date(conferenceStart);
  registrationDeadline.setDate(registrationDeadline.getDate() - 7);

  const diffTime = registrationDeadline.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays);
};

/**
 * Phone number formatting utilities
 */
export const phoneUtils = {
  /**
   * Format phone number as user types - US format (XXX) XXX-XXXX
   */
  formatPhoneNumber: (value: string): string => {
    // Remove all non-digit characters
    const digits = value.replace(/\D/g, '');

    // Handle different lengths
    if (digits.length === 0) return '';
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
    if (digits.length <= 10) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
    }

    // Handle 11 digits (with country code)
    if (digits.length === 11 && digits.startsWith('1')) {
      const phoneDigits = digits.slice(1);
      return `+1 (${phoneDigits.slice(0, 3)}) ${phoneDigits.slice(3, 6)}-${phoneDigits.slice(6)}`;
    }

    // For more than 10 digits, just format the first 10
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
  },

  /**
   * Extract raw digits from formatted phone number
   */
  extractDigits: (formattedPhone: string): string => {
    return formattedPhone.replace(/\D/g, '');
  },

  /**
   * Get the phone number for storage (removes +1 country code if present)
   */
  getStorageValue: (formattedPhone: string): string => {
    const digits = phoneUtils.extractDigits(formattedPhone);

    // If it's 11 digits and starts with 1, remove the 1
    if (digits.length === 11 && digits.startsWith('1')) {
      return digits.slice(1);
    }

    return digits;
  },

  /**
   * Parse various phone number formats and return formatted version
   */
  parseAndFormat: (input: string): string => {
    // Handle pasted phone numbers in various formats
    const digits = input.replace(/\D/g, '');

    if (digits.length === 0) return '';

    // Format using the standard formatter
    return phoneUtils.formatPhoneNumber(digits);
  },
};

/**
 * Schema validation helpers
 */
export const schemaValidation = {
  /**
   * Validate email format
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate phone number format - now works with both formatted and unformatted numbers
   */
  isValidPhoneNumber: (phone: string): boolean => {
    const digits = phoneUtils.extractDigits(phone);

    // Must have exactly 10 digits (US phone number)
    if (digits.length !== 10) return false;

    // First digit of area code cannot be 0 or 1
    if (digits[0] === '0' || digits[0] === '1') return false;

    // First digit of exchange code cannot be 0 or 1
    if (digits[3] === '0' || digits[3] === '1') return false;

    return true;
  },

  /**
   * Validate ZIP code format
   */
  isValidZipCode: (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  },

  /**
   * Validate URL format
   */
  isValidUrl: (url: string): boolean => {
    try {
      new URL(url);
      return url.startsWith('http://') || url.startsWith('https://');
    } catch {
      return false;
    }
  },
};

/**
 * Form field helpers
 */
export const formHelpers = {
  /**
   * Generate field ID for accessibility
   */
  generateFieldId: (fieldName: string, formType: string): string => {
    return `iepa-2025-${formType}-${fieldName}`;
  },

  /**
   * Generate aria-describedby IDs for form fields
   */
  generateDescriptionId: (fieldName: string, formType: string): string => {
    return `iepa-2025-${formType}-${fieldName}-desc`;
  },

  /**
   * Generate error message ID for form fields
   */
  generateErrorId: (fieldName: string, formType: string): string => {
    return `iepa-2025-${formType}-${fieldName}-error`;
  },
};

/**
 * Pricing utility functions for forms and schemas
 */
export const pricingHelpers = {
  /**
   * Get registration options formatted for form schemas
   */
  getRegistrationSchemaOptions: () => {
    const options = pricingUtils.getRegistrationOptions();
    return {
      enum: options.map(opt => opt.id),
      enumNames: options.map(opt => opt.label),
      descriptions: options.map(
        opt => `${opt.formattedPrice} - ${opt.description}`
      ),
    };
  },

  /**
   * Get sponsorship options formatted for form schemas
   */
  getSponsorshipSchemaOptions: () => {
    const options = pricingUtils.getSponsorshipOptions();
    return {
      enum: options.map(opt => opt.id),
      enumNames: options.map(opt => opt.label),
      descriptions: options.map(
        opt => `${opt.formattedPrice} - ${opt.description}`
      ),
    };
  },

  /**
   * Calculate registration total for form display
   */
  calculateFormTotal: (
    registrationType: string,
    includeGolf: boolean,
    additionalOptions: string[] = []
  ): {
    registrationTotal: number;
    golfTotal: number;
    grandTotal: number;
    formattedRegistrationTotal: string;
    formattedGolfTotal: string;
    formattedGrandTotal: string;
  } => {
    const registrationTotal =
      pricingUtils.getRegistrationPrice(registrationType);
    const golfTotal = includeGolf ? PRICING_CONSTANTS.GOLF_TOURNAMENT_FEE : 0;
    const additionalTotal = additionalOptions.reduce(
      (sum, optionId) => sum + pricingUtils.getAdditionalOptionPrice(optionId),
      0
    );
    const grandTotal = registrationTotal + golfTotal + additionalTotal;

    return {
      registrationTotal,
      golfTotal,
      grandTotal,
      formattedRegistrationTotal: pricingUtils.formatPrice(registrationTotal),
      formattedGolfTotal: pricingUtils.formatPrice(golfTotal),
      formattedGrandTotal: pricingUtils.formatPrice(grandTotal),
    };
  },

  /**
   * Validate pricing selections
   */
  validatePricingSelections: (data: {
    registrationType?: string;
    sponsorshipLevel?: string;
    includeGolf?: boolean;
  }): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (data.registrationType) {
      const validRegistrationTypes = REGISTRATION_PRICING.map(r => r.id);
      if (!validRegistrationTypes.includes(data.registrationType)) {
        errors.push('Invalid registration type selected');
      }
    }

    if (data.sponsorshipLevel) {
      const validSponsorshipLevels = SPONSORSHIP_PACKAGES.map(s => s.id);
      if (!validSponsorshipLevels.includes(data.sponsorshipLevel)) {
        errors.push('Invalid sponsorship level selected');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Get pricing summary for confirmation displays
   */
  getPricingSummary: (
    registrationType: string,
    includeGolf: boolean,
    additionalOptions: string[] = []
  ): Array<{ label: string; amount: string; description?: string }> => {
    const summary = [];

    // Registration fee
    const registrationPrice =
      pricingUtils.getRegistrationPrice(registrationType);
    const registration = REGISTRATION_PRICING.find(
      r => r.id === registrationType
    );
    if (registration) {
      summary.push({
        label: registration.displayName,
        amount: pricingUtils.formatPrice(registrationPrice),
        description: registration.description,
      });
    }

    // Golf tournament
    if (includeGolf) {
      summary.push({
        label: 'Golf Tournament',
        amount: pricingUtils.formatPrice(PRICING_CONSTANTS.GOLF_TOURNAMENT_FEE),
        description: 'Annual IEPA Golf Tournament at Lake Tahoe Golf Course',
      });
    }

    // Additional options
    additionalOptions.forEach(optionId => {
      const price = pricingUtils.getAdditionalOptionPrice(optionId);
      if (price > 0) {
        summary.push({
          label: optionId
            .replace('-', ' ')
            .replace(/\b\w/g, l => l.toUpperCase()),
          amount: pricingUtils.formatPrice(price),
        });
      }
    });

    return summary;
  },
};

/**
 * Export types for TypeScript support
 */
export interface MealOption {
  id: string;
  value: string;
  label: string;
  description?: string;
  date: string;
  time: string;
}

export interface SchemaValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface FormFieldIds {
  fieldId: string;
  descriptionId: string;
  errorId: string;
}

export interface PricingSummaryItem {
  label: string;
  amount: string;
  description?: string;
}

export interface FormTotalCalculation {
  registrationTotal: number;
  golfTotal: number;
  grandTotal: number;
  formattedRegistrationTotal: string;
  formattedGolfTotal: string;
  formattedGrandTotal: string;
}
