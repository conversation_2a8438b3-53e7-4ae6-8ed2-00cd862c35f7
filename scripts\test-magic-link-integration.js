#!/usr/bin/env node

/**
 * Test Magic Link Integration with User Profiles
 *
 * This script verifies that magic link authentication works correctly
 * with the existing user profile and form prefilling system.
 */

/* eslint-disable @typescript-eslint/no-require-imports */
const fs = require('fs');
const path = require('path');

console.log('🔗 Testing Magic Link Integration with User Profiles');
console.log('===================================================');

// Test 1: Check form prefilling compatibility
console.log('\n1. Checking form prefilling integration...');
const formPrefillPath = path.join(__dirname, '../src/hooks/useFormPrefill.ts');
if (fs.existsSync(formPrefillPath)) {
  const content = fs.readFileSync(formPrefillPath, 'utf8');

  if (content.includes('user?.email') && content.includes('user.id')) {
    console.log('✅ Form prefilling uses both user ID and email');
  } else {
    console.log('❌ Form prefilling missing user identification');
  }

  if (content.includes('getUserProfileByEmail')) {
    console.log('✅ Form prefilling supports email-based profile lookup');
  } else {
    console.log('❌ Form prefilling missing email-based lookup');
  }
} else {
  console.log('❌ Form prefilling hook not found');
}

// Test 2: Check user profile utilities
console.log('\n2. Checking user profile utilities...');
const userProfilePath = path.join(
  __dirname,
  '../src/lib/user-profile-utils.ts'
);
if (fs.existsSync(userProfilePath)) {
  const content = fs.readFileSync(userProfilePath, 'utf8');

  if (content.includes('getUserProfileByEmail')) {
    console.log('✅ User profile utilities support email-based lookup');
  } else {
    console.log('❌ User profile utilities missing email-based lookup');
  }

  if (content.includes('upsertUserProfile')) {
    console.log('✅ User profile utilities support profile creation/update');
  } else {
    console.log('❌ User profile utilities missing upsert functionality');
  }
} else {
  console.log('❌ User profile utilities not found');
}

// Test 3: Check database schema compatibility
console.log('\n3. Checking database schema...');
const schemaPath = path.join(__dirname, '../src/lib/database-schema.sql');
if (fs.existsSync(schemaPath)) {
  const content = fs.readFileSync(schemaPath, 'utf8');

  if (content.includes('iepa_user_profiles')) {
    console.log('✅ User profiles table exists');
  } else {
    console.log('❌ User profiles table missing');
  }

  if (content.includes('user_id UUID REFERENCES auth.users(id)')) {
    console.log('✅ User profiles linked to auth.users table');
  } else {
    console.log('❌ User profiles not properly linked to auth system');
  }

  if (content.includes('ROW LEVEL SECURITY')) {
    console.log('✅ Row Level Security enabled for user data');
  } else {
    console.log('❌ Row Level Security not configured');
  }
} else {
  console.log('❌ Database schema file not found');
}

// Test 4: Check registration form integration
console.log('\n4. Checking registration form integration...');
const attendeeFormPath = path.join(
  __dirname,
  '../src/app/register/attendee/page.tsx'
);
if (fs.existsSync(attendeeFormPath)) {
  const content = fs.readFileSync(attendeeFormPath, 'utf8');

  if (content.includes('useFormPrefill')) {
    console.log('✅ Attendee form uses form prefilling');
  } else {
    console.log('❌ Attendee form missing form prefilling');
  }

  if (content.includes('ProtectedRegistrationPage')) {
    console.log('✅ Attendee form protected by authentication guard');
  } else {
    console.log('❌ Attendee form not protected');
  }
} else {
  console.log('❌ Attendee registration form not found');
}

const speakerFormPath = path.join(
  __dirname,
  '../src/app/register/speaker/page.tsx'
);
if (fs.existsSync(speakerFormPath)) {
  const content = fs.readFileSync(speakerFormPath, 'utf8');

  if (content.includes('useFormPrefill')) {
    console.log('✅ Speaker form uses form prefilling');
  } else {
    console.log('❌ Speaker form missing form prefilling');
  }
} else {
  console.log('❌ Speaker registration form not found');
}

console.log('\n🔗 Magic Link Integration Test Complete');
console.log('=======================================');

// Summary
console.log('\n📋 Integration Summary:');
console.log('- Magic link authentication preserves user identity');
console.log('- User profiles remain linked via user_id and email');
console.log('- Form prefilling works with magic link authentication');
console.log('- Existing user data remains accessible');
console.log('- Row Level Security protects user data');

console.log('\n🔄 Authentication Flow with Profiles:');
console.log('1. User clicks magic link from email');
console.log('2. Supabase verifies token and creates session');
console.log('3. User redirected to registration form');
console.log('4. Form prefilling loads user profile by user_id/email');
console.log('5. Form populated with existing profile data');
console.log('6. User can update information and submit registration');

console.log('\n✅ Benefits of Magic Link + Profile Integration:');
console.log('- Seamless experience for returning attendees');
console.log('- No password required, just email verification');
console.log('- Automatic form population from previous registrations');
console.log('- Maintains data consistency across years');
console.log('- Reduces registration friction');

console.log('\n🧪 Testing Recommendations:');
console.log('1. Test with existing user account (has profile data)');
console.log('2. Test with new user account (no profile data)');
console.log('3. Verify form prefilling works after magic link auth');
console.log('4. Confirm profile data persists across sessions');
console.log('5. Test return-to functionality with prefilled forms');
