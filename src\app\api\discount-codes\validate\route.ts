// Discount Code Validation API
// POST /api/discount-codes/validate

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';
import { stripe } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { code, registrationType, totalAmount, userId } = body;

    // Validate required fields
    if (!code || !registrationType || !totalAmount) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: code, registrationType, totalAmount',
        },
        { status: 400 }
      );
    }

    const supabaseAdmin = createSupabaseAdmin();

    // Find the discount code
    const { data: discountCode, error: fetchError } = await supabaseAdmin
      .from('iepa_discount_codes')
      .select('*')
      .eq('code', code.toUpperCase())
      .eq('is_active', true)
      .single();

    if (fetchError || !discountCode) {
      return NextResponse.json({
        success: false,
        error: 'Invalid or expired discount code',
        valid: false,
      });
    }

    // Check validity period
    const now = new Date();
    const validFrom = new Date(discountCode.valid_from);
    const validUntil = discountCode.valid_until ? new Date(discountCode.valid_until) : null;

    if (now < validFrom) {
      return NextResponse.json({
        success: false,
        error: 'Discount code is not yet active',
        valid: false,
      });
    }

    if (validUntil && now > validUntil) {
      return NextResponse.json({
        success: false,
        error: 'Discount code has expired',
        valid: false,
      });
    }

    // Check registration type restrictions
    if (
      discountCode.applicable_registration_types &&
      discountCode.applicable_registration_types.length > 0 &&
      !discountCode.applicable_registration_types.includes(registrationType)
    ) {
      return NextResponse.json({
        success: false,
        error: 'Discount code is not applicable to this registration type',
        valid: false,
      });
    }

    // Check minimum amount
    if (discountCode.minimum_amount && totalAmount < discountCode.minimum_amount) {
      return NextResponse.json({
        success: false,
        error: `Minimum order amount of $${discountCode.minimum_amount} required`,
        valid: false,
      });
    }

    // Check usage limits
    if (discountCode.max_uses && discountCode.current_uses >= discountCode.max_uses) {
      return NextResponse.json({
        success: false,
        error: 'Discount code usage limit reached',
        valid: false,
      });
    }

    // Check per-user usage limit
    if (userId && discountCode.max_uses_per_user) {
      const { count: userUsageCount } = await supabaseAdmin
        .from('iepa_discount_usage')
        .select('*', { count: 'exact' })
        .eq('discount_code_id', discountCode.id)
        .eq('user_id', userId);

      if (userUsageCount && userUsageCount >= discountCode.max_uses_per_user) {
        return NextResponse.json({
          success: false,
          error: 'You have already used this discount code the maximum number of times',
          valid: false,
        });
      }
    }

    // Calculate discount amount
    let discountAmount = 0;
    if (discountCode.discount_type === 'percentage') {
      discountAmount = (totalAmount * discountCode.discount_value) / 100;
    } else {
      discountAmount = Math.min(discountCode.discount_value, totalAmount);
    }

    const finalAmount = Math.max(0, totalAmount - discountAmount);

    // Verify Stripe coupon if exists
    let stripeCoupon = null;
    if (discountCode.stripe_coupon_id) {
      try {
        stripeCoupon = await stripe.coupons.retrieve(discountCode.stripe_coupon_id);
        if (!stripeCoupon.valid) {
          return NextResponse.json({
            success: false,
            error: 'Discount code is no longer valid in payment system',
            valid: false,
          });
        }
      } catch (stripeError) {
        console.error('Error retrieving Stripe coupon:', stripeError);
        return NextResponse.json({
          success: false,
          error: 'Error validating discount code with payment system',
          valid: false,
        });
      }
    }

    return NextResponse.json({
      success: true,
      valid: true,
      discountCode: {
        id: discountCode.id,
        code: discountCode.code,
        name: discountCode.name,
        description: discountCode.description,
        discountType: discountCode.discount_type,
        discountValue: discountCode.discount_value,
        stripeCouponId: discountCode.stripe_coupon_id,
      },
      calculation: {
        originalAmount: totalAmount,
        discountAmount: Math.round(discountAmount * 100) / 100, // Round to 2 decimal places
        finalAmount: Math.round(finalAmount * 100) / 100,
        savings: Math.round(discountAmount * 100) / 100,
      },
    });
  } catch (error) {
    console.error('Error validating discount code:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        valid: false,
      },
      { status: 500 }
    );
  }
}
