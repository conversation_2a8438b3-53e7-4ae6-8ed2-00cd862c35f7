{"$schema": "http://json-schema.org/draft-07/schema#", "title": "IEPA 2025 Attendee Registration Schema", "description": "Form schema for IEPA 2025 Conference attendee registration", "type": "object", "properties": {"registrationType": {"type": "string", "title": "Registration Type", "description": "What type of registration?", "enum": ["iepa-member", "non-iepa-member", "day-use-iepa", "day-use-non-iepa", "fed-state-government", "cca", "spouse", "child"], "enumNames": ["IEPA Member ($2,369)", "Non-IEPA Member ($2,730)", "Day Use Only - IEPA Members ($1,803)", "Day Use Only - Non-IEPA Members ($2,163)", "Federal/State Government ($2,060)", "California Community Choice Association ($2,369)", "Spouse Registration ($500)", "Child Registration ($100)"]}, "linkedAttendeeEmail": {"type": "string", "title": "Primary Attendee <PERSON><PERSON>", "description": "Email address of the primary attendee (required for spouse and child registrations)", "format": "email"}, "personalInfo": {"type": "object", "title": "Personal Information", "properties": {"email": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "format": "email"}, "firstName": {"type": "string", "title": "Attendee First Name", "minLength": 1}, "lastName": {"type": "string", "title": "Attendee Last Name", "minLength": 1}, "nameOnBadge": {"type": "string", "title": "Name on Badge", "description": "Name as you'd like to see it on your badge", "minLength": 1}, "gender": {"type": "string", "title": "Attendee Gender", "enum": ["female", "male"], "enumNames": ["Female", "Male"]}}, "required": ["email", "firstName", "lastName", "nameOnBadge", "gender"]}, "contactInfo": {"type": "object", "title": "Contact Information", "properties": {"phoneNumber": {"type": "string", "title": "Phone Number", "description": "for Attendee List", "pattern": "^[\\d\\s\\-\\(\\)\\+\\.]+$"}, "streetAddress": {"type": "string", "title": "Street Address", "description": "for Attendee List"}, "city": {"type": "string", "title": "City", "description": "for Attendee List"}, "state": {"type": "string", "title": "State", "description": "for Attendee List"}, "zipCode": {"type": "string", "title": "Zip Code", "description": "for Attendee List", "pattern": "^\\d{5}(-\\d{4})?$"}}, "required": ["phoneNumber", "streetAddress", "city", "state", "zipCode"]}, "professionalInfo": {"type": "object", "title": "Professional Information", "properties": {"organization": {"type": "string", "title": "Attendee Organization"}, "jobTitle": {"type": "string", "title": "Attendee Job Title"}}, "required": ["organization", "jobTitle"]}, "eventOptions": {"type": "object", "title": "Event Options", "properties": {"nightOne": {"type": "boolean", "title": "Night One: Monday, September 15, 2025", "description": "Monday night lodging - Arrival day and welcome dinner", "default": true}, "nightTwo": {"type": "boolean", "title": "Night Two: Tuesday, September 16, 2025", "description": "Tuesday night lodging - Main conference day and networking dinner", "default": true}, "attendingGolf": {"type": "boolean", "title": "Are you attending the golf tournament?", "description": "$200 Fee", "default": false}, "meals": {"type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "Please indicate which meals you're planning on attending. Meal options are dynamically loaded from conference configuration.", "items": {"type": "string", "enum": ["dinner-day1", "breakfast-day2", "lunch-day2", "dinner-day2", "breakfast-day3", "lunch-day3"]}, "uniqueItems": true, "_note": "Meal options are populated dynamically from MEAL_SCHEDULE in conference-config.ts"}, "dietaryNeeds": {"type": "string", "title": "Special Dietary Needs", "description": "Put 'none' if not applicable.", "default": "none"}}, "required": ["nightOne", "nightTwo", "<PERSON><PERSON><PERSON><PERSON>", "meals", "dietaryNeeds"]}, "calculatedFields": {"type": "object", "title": "Calculated Fields", "description": "These fields are calculated automatically", "properties": {"registrationTotal": {"type": "number", "title": "Registration Total", "description": "Calculated based on registration type", "minimum": 0, "default": 0}, "golfTotal": {"type": "number", "title": "Golf Total", "description": "Golf tournament fee if selected", "minimum": 0, "default": 0}, "grandTotal": {"type": "number", "title": "Grand Order Total", "description": "Sum of registration and golf totals", "minimum": 0, "default": 0}}}}, "required": ["registrationType", "personalInfo", "contactInfo", "professionalInfo", "eventOptions"], "additionalProperties": false, "_metadata": {"version": "1.1.0", "lastUpdated": "2025-01-30", "conferenceYear": 2025, "notes": "Updated for Task 2.3: Integrated pricing display in registration types, enhanced meal configuration integration, updated for 2025 requirements", "dependencies": {"conferenceConfig": "src/lib/conference-config.ts", "pricingConfig": "src/lib/pricing-config.ts", "schemaUtils": "src/utils/schema-utils.ts"}, "validationRules": {"registrationType": "Must match REGISTRATION_PRICING enum values", "meals": "Must match MEAL_SCHEDULE IDs from conference config", "email": "Must be valid email format", "phoneNumber": "Must match phone pattern with minimum 10 digits", "zipCode": "Must be valid US ZIP code format"}}}