'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import '@/styles/admin-responsive.css';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Card, CardBody } from '@/components/ui';
import { FiAlertTriangle, FiLoader } from 'react-icons/fi';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { user, loading: authLoading } = useAuth();
  const {
    isAdmin,
    adminUser,
    isLoading: adminLoading,
    error: adminError,
  } = useAdminAccess();
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/login');
    }
  }, [user, authLoading, router]);

  // Show loading state
  if (authLoading || adminLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FiLoader
            className="w-8 h-8 animate-spin mx-auto mb-4 text-[var(--iepa-primary-blue)]"
            suppressHydrationWarning
          />
          <p className="text-gray-600">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  // Show access denied if user is not an admin
  // Allow <NAME_EMAIL> even if isAdmin is false (temporary fix)
  const skipAdminCheck = user?.email === '<EMAIL>';

  if (!isAdmin && !skipAdminCheck) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardBody className="text-center py-12">
            <div className="text-red-500 mb-4">
              <FiAlertTriangle
                className="w-16 h-16 mx-auto"
                suppressHydrationWarning
              />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Access Denied
            </h2>
            <p className="text-gray-600 mb-6">
              You don&apos;t have permission to access the IEPA Conference
              Administration Dashboard. This area is restricted to authorized
              administrators only.
            </p>
            <div className="space-y-3">
              <p className="text-sm text-gray-500">
                Signed in as: <span className="font-medium">{user?.email}</span>
              </p>
              {adminError && (
                <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  Error: {adminError}
                </p>
              )}
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  // Render admin dashboard layout
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Header */}
      <AdminHeader
        adminUser={adminUser}
        onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      />

      <div className="flex">
        {/* Admin Sidebar */}
        <AdminSidebar
          adminUser={adminUser}
          isOpen={isMobileMenuOpen}
          onClose={() => setIsMobileMenuOpen(false)}
        />

        {/* Main Content */}
        <main className="flex-1 p-4 sm:p-6">
          <div className="max-w-full mx-auto">{children}</div>
        </main>
      </div>
    </div>
  );
}
