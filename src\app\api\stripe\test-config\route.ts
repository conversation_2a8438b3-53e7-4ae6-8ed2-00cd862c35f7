// Stripe Configuration Test API
// GET /api/stripe/test-config

import { NextRequest, NextResponse } from 'next/server';
import {
  validateStripeConfig,
  testStripeConnection,
  STRIPE_CONFIG,
} from '@/lib/stripe';

export async function GET() {
  try {
    // Validate configuration
    const configValidation = validateStripeConfig();

    // Test connection (only if config is valid)
    let connectionTest: { success: boolean; error?: string } = {
      success: false,
      error: 'Configuration invalid',
    };
    if (configValidation.isValid) {
      connectionTest = await testStripeConnection();
    }

    // Determine environment
    const isTestMode =
      STRIPE_CONFIG.publishableKey?.startsWith('pk_test_') || false;
    const environment = isTestMode ? 'test' : 'production';

    // Get configuration status
    const configStatus = {
      hasPublishableKey: !!STRIPE_CONFIG.publishableKey,
      hasWebhookSecret: !!STRIPE_CONFIG.webhookSecret,
      environment,
      isTestMode,
      currency: STRIPE_CONFIG.currency,
      paymentMethodTypes: STRIPE_CONFIG.paymentMethodTypes,
    };

    // Prepare response
    const response = {
      success: configValidation.isValid && connectionTest.success,
      timestamp: new Date().toISOString(),
      configuration: {
        isValid: configValidation.isValid,
        errors: configValidation.errors,
        status: configStatus,
      },
      connection: connectionTest,
      recommendations: generateRecommendations(
        configValidation,
        connectionTest,
        isTestMode
      ),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error testing Stripe configuration:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to test Stripe configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Generate recommendations based on test results
function generateRecommendations(
  configValidation: { isValid: boolean; errors: string[] },
  connectionTest: { success: boolean; error?: string },
  isTestMode: boolean
): string[] {
  const recommendations: string[] = [];

  // Configuration recommendations
  if (!configValidation.isValid) {
    recommendations.push('Fix configuration errors before proceeding');
    configValidation.errors.forEach(error => {
      recommendations.push(`• ${error}`);
    });
  }

  // Connection recommendations
  if (!connectionTest.success) {
    recommendations.push('Stripe API connection failed');
    if (connectionTest.error) {
      recommendations.push(`• ${connectionTest.error}`);
    }
  }

  // Environment recommendations
  if (process.env.NODE_ENV === 'development' && !isTestMode) {
    recommendations.push(
      '⚠️ Using production keys in development - switch to test keys'
    );
  }

  if (process.env.NODE_ENV === 'production' && isTestMode) {
    recommendations.push(
      '⚠️ Using test keys in production - switch to production keys'
    );
  }

  // Webhook recommendations
  if (!STRIPE_CONFIG.webhookSecret) {
    recommendations.push('Configure webhook secret for payment event handling');
  }

  // Success recommendations
  if (configValidation.isValid && connectionTest.success) {
    recommendations.push('✅ Stripe configuration is valid and ready for use');
    if (isTestMode) {
      recommendations.push(
        '✅ Test mode active - safe for development and testing'
      );
    }
  }

  return recommendations;
}

// POST method for testing specific Stripe operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testType } = body;

    switch (testType) {
      case 'create-test-customer':
        return await testCreateCustomer();

      case 'create-test-product':
        return await testCreateProduct();

      default:
        return NextResponse.json(
          {
            success: false,
            error:
              'Invalid test type. Available: create-test-customer, create-test-product',
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error running Stripe test:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to run Stripe test',
      },
      { status: 500 }
    );
  }
}

// Test creating a customer
async function testCreateCustomer() {
  try {
    const { stripe } = await import('@/lib/stripe');

    const customer = await stripe.customers.create({
      email: '<EMAIL>',
      name: 'Test Customer',
      description: 'Test customer for IEPA conference registration',
      metadata: {
        test: 'true',
        created_by: 'stripe-config-test',
      },
    });

    // Clean up - delete the test customer
    await stripe.customers.del(customer.id);

    return NextResponse.json({
      success: true,
      message: 'Successfully created and deleted test customer',
      customerId: customer.id,
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Test creating a product
async function testCreateProduct() {
  try {
    const { stripe } = await import('@/lib/stripe');

    const product = await stripe.products.create({
      name: 'Test IEPA Conference Registration',
      description: 'Test product for IEPA conference registration',
      metadata: {
        test: 'true',
        created_by: 'stripe-config-test',
      },
    });

    // Clean up - delete the test product
    await stripe.products.del(product.id);

    return NextResponse.json({
      success: true,
      message: 'Successfully created and deleted test product',
      productId: product.id,
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
