'use client';

import { Suspense } from 'react';

interface SearchParamsWrapperProps {
  children: React.ReactNode;
}

function SearchParamsContent({ children }: SearchParamsWrapperProps) {
  return <>{children}</>;
}

export default function SearchParamsWrapper({ children }: SearchParamsWrapperProps) {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
    </div>}>
      <SearchParamsContent>{children}</SearchParamsContent>
    </Suspense>
  );
}
