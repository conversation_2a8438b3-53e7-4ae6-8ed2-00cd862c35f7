// Test Schema Validation System
// Run this to verify all schema updates are working correctly

import {
  validateAttendeeData,
  validateSpeakerData,
  validateSponsorData,
  validateSchemaIntegrity,
  getSchemaValidationSummary,
  type AttendeeFormData,
  type SpeakerFormData,
  type SponsorFormData,
} from './schema-validation';

/**
 * Test attendee validation
 */
export const testAttendeeValidation = () => {
  console.log('🧪 Testing Attendee Validation...');

  // Valid attendee data
  const validAttendee: AttendeeFormData = {
    registrationType: 'iepa-member',
    personalInfo: {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    },
    contactInfo: {
      phoneNumber: '************',
      zipCode: '12345',
    },
    eventOptions: {
      meals: ['dinner-day1', 'breakfast-day2'],
    },
  };

  // Invalid attendee data
  const invalidAttendee: AttendeeFormData = {
    registrationType: 'invalid-type',
    personalInfo: {
      email: 'invalid-email',
      firstName: '',
      lastName: '',
    },
    contactInfo: {
      phoneNumber: 'abc',
      zipCode: 'invalid',
    },
    eventOptions: {
      meals: ['invalid-meal'],
    },
  };

  const validResult = validateAttendeeData(validAttendee);
  const invalidResult = validateAttendeeData(invalidAttendee);

  console.log('✅ Valid attendee result:', validResult.isValid);
  console.log('❌ Invalid attendee result:', invalidResult.isValid);
  console.log('❌ Invalid attendee errors:', invalidResult.errors);

  return { validResult, invalidResult };
};

/**
 * Test speaker validation
 */
export const testSpeakerValidation = () => {
  console.log('🧪 Testing Speaker Validation...');

  // Valid speaker data
  const validSpeaker: SpeakerFormData = {
    personalInfo: {
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Smith',
    },
    contactInfo: {
      phoneNumber: '************',
    },
    presentationInfo: {
      presentationTitle: 'My Amazing Presentation',
      bio: 'This is a valid biography that is longer than 50 characters and provides meaningful information about the speaker.',
      presentationAbstract:
        'This is a comprehensive presentation abstract that exceeds the minimum 100 character requirement and provides detailed information about the presentation content, objectives, and expected outcomes for the audience.',
    },
  };

  // Invalid speaker data
  const invalidSpeaker: SpeakerFormData = {
    personalInfo: {
      email: 'invalid-email',
      firstName: '',
      lastName: '',
    },
    contactInfo: {
      phoneNumber: 'abc',
    },
    presentationInfo: {
      presentationTitle: 'Too',
      bio: 'Too short',
      presentationAbstract: 'Too short',
    },
  };

  const validResult = validateSpeakerData(validSpeaker);
  const invalidResult = validateSpeakerData(invalidSpeaker);

  console.log('✅ Valid speaker result:', validResult.isValid);
  console.log('❌ Invalid speaker result:', invalidResult.isValid);
  console.log('❌ Invalid speaker errors:', invalidResult.errors);

  return { validResult, invalidResult };
};

/**
 * Test sponsor validation
 */
export const testSponsorValidation = () => {
  console.log('🧪 Testing Sponsor Validation...');

  // Valid sponsor data
  const validSponsor: SponsorFormData = {
    sponsorshipLevel: 'gold-sponsor',
    sponsorInfo: {
      sponsorName: 'Acme Corporation',
      sponsorUrl: 'https://www.acme.com',
      sponsorDescription:
        'Acme Corporation is a leading provider of innovative solutions in the energy sector with over 20 years of experience.',
    },
    contactInfo: {
      contactEmail: '<EMAIL>',
      contactPhone: '************',
    },
  };

  // Invalid sponsor data
  const invalidSponsor: SponsorFormData = {
    sponsorshipLevel: 'invalid-level',
    sponsorInfo: {
      sponsorName: '',
      sponsorUrl: 'invalid-url',
      sponsorDescription: 'Too short',
    },
    contactInfo: {
      contactEmail: 'invalid-email',
      contactPhone: 'abc',
    },
  };

  const validResult = validateSponsorData(validSponsor);
  const invalidResult = validateSponsorData(invalidSponsor);

  console.log('✅ Valid sponsor result:', validResult.isValid);
  console.log('❌ Invalid sponsor result:', invalidResult.isValid);
  console.log('❌ Invalid sponsor errors:', invalidResult.errors);

  return { validResult, invalidResult };
};

/**
 * Test schema integrity
 */
export const testSchemaIntegrity = () => {
  console.log('🧪 Testing Schema Integrity...');

  const integrityResult = validateSchemaIntegrity();
  console.log('🔍 Schema integrity result:', integrityResult.isValid);

  if (!integrityResult.isValid) {
    console.log('❌ Schema integrity errors:', integrityResult.errors);
  }

  if (integrityResult.warnings.length > 0) {
    console.log('⚠️ Schema integrity warnings:', integrityResult.warnings);
  }

  return integrityResult;
};

/**
 * Get validation summary
 */
export const testValidationSummary = () => {
  console.log('🧪 Getting Validation Summary...');

  const summary = getSchemaValidationSummary();
  console.log('📊 Schema validation summary:', summary);

  return summary;
};

/**
 * Run all tests
 */
export const runAllSchemaTests = () => {
  console.log('🚀 Running All Schema Validation Tests...\n');

  const attendeeTests = testAttendeeValidation();
  console.log('');

  const speakerTests = testSpeakerValidation();
  console.log('');

  const sponsorTests = testSponsorValidation();
  console.log('');

  const integrityTest = testSchemaIntegrity();
  console.log('');

  const summary = testValidationSummary();
  console.log('');

  // Overall results
  const allValid =
    attendeeTests.validResult.isValid &&
    speakerTests.validResult.isValid &&
    sponsorTests.validResult.isValid &&
    integrityTest.isValid;

  const allInvalid =
    !attendeeTests.invalidResult.isValid &&
    !speakerTests.invalidResult.isValid &&
    !sponsorTests.invalidResult.isValid;

  console.log('🎯 Test Results Summary:');
  console.log(`✅ All valid data passes: ${allValid}`);
  console.log(`❌ All invalid data fails: ${allInvalid}`);
  console.log(`🔍 Schema integrity: ${integrityTest.isValid}`);
  console.log(
    `📊 Schema versions: ${JSON.stringify(summary.schemas, null, 2)}`
  );

  return {
    allTestsPass: allValid && allInvalid && integrityTest.isValid,
    attendeeTests,
    speakerTests,
    sponsorTests,
    integrityTest,
    summary,
  };
};

// Export for console testing
if (typeof window !== 'undefined') {
  // Browser environment - attach to window for console testing
  (window as unknown as Record<string, unknown>).schemaTests = {
    runAllSchemaTests,
    testAttendeeValidation,
    testSpeakerValidation,
    testSponsorValidation,
    testSchemaIntegrity,
    testValidationSummary,
  };
}
