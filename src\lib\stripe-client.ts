// Client-side Stripe utilities for IEPA Conference Registration
// Safe for use in browser/frontend components

import { loadStripe, Stripe } from '@stripe/stripe-js';

// Get publishable key from environment
const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

if (!stripePublishableKey) {
  throw new Error(
    'Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable. Please check your .env.local file.'
  );
}

// Validate that we're using test keys in development
if (process.env.NODE_ENV === 'development') {
  if (!stripePublishableKey.startsWith('pk_test_')) {
    console.warn(
      '⚠️  Warning: Using production Stripe publishable key in development environment'
    );
  }
}

// Initialize Stripe client (cached with error handling)
let stripePromise: Promise<Stripe | null> | null = null;

export const getStripe = (): Promise<Stripe | null> => {
  if (!stripePromise) {
    stripePromise = loadStripe(stripePublishableKey).catch(error => {
      console.error('Failed to load Stripe:', error);
      stripePromise = null; // Reset promise to allow retry
      return null;
    });
  }
  return stripePromise;
};

// Stripe client configuration
export const STRIPE_CLIENT_CONFIG = {
  publishableKey: stripePublishableKey,
  appearance: {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#1e40af', // IEPA blue
      colorBackground: '#ffffff',
      colorText: '#1f2937',
      colorDanger: '#dc2626',
      fontFamily: 'system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '6px',
    },
  },
} as const;

// Define session data type
type SessionData = {
  registrationId: string;
  registrationType: 'attendee' | 'sponsor' | 'speaker' | 'sponsor-attendee';
  customerEmail: string;
  customerName?: string;
  totalAmount: number;
  lineItems?: Array<{
    name: string;
    description?: string;
    price: number;
    quantity?: number;
  }>;
  discountCode?: string; // Stripe coupon ID for discount codes
  metadata?: Record<string, string>;
};

// Payment processing utilities
export const stripeUtils = {
  /**
   * Create a checkout session and redirect to Stripe
   */
  createCheckoutSession: async (sessionData: SessionData) => {
    try {
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sessionData),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      return {
        success: true,
        sessionId: data.sessionId,
        url: data.url,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },

  /**
   * Redirect to Stripe Checkout with fallback
   */
  redirectToCheckout: async (sessionId: string) => {
    try {
      console.log('🔄 Loading Stripe for redirect...');
      const stripe = await getStripe();

      if (!stripe) {
        console.error(
          '❌ Stripe failed to load, attempting direct URL redirect...'
        );
        // Fallback: try to get the checkout URL and redirect directly
        const sessionResult = await stripeUtils.getCheckoutSession(sessionId);
        if (sessionResult.success && sessionResult.session?.url) {
          console.log('🔄 Using direct URL redirect fallback...');
          window.location.href = sessionResult.session.url;
          return { success: true };
        }
        throw new Error('Stripe failed to load and no checkout URL available');
      }

      console.log('✅ Stripe loaded, redirecting to checkout...');
      const { error } = await stripe.redirectToCheckout({
        sessionId,
      });

      if (error) {
        console.error('❌ Stripe redirect error:', error);
        throw error;
      }

      console.log('✅ Redirect initiated successfully');
      return { success: true };
    } catch (error) {
      console.error('💥 Redirect error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },

  /**
   * Create checkout session and redirect in one step
   */
  processPayment: async (sessionData: SessionData) => {
    try {
      console.log('🔄 Starting payment process...', sessionData);

      // Create checkout session
      const sessionResult =
        await stripeUtils.createCheckoutSession(sessionData);

      console.log('📝 Checkout session result:', sessionResult);

      if (!sessionResult.success) {
        console.error(
          '❌ Checkout session creation failed:',
          sessionResult.error
        );
        return sessionResult;
      }

      console.log('✅ Checkout session created, attempting redirect...');

      // Redirect to checkout
      const redirectResult = await stripeUtils.redirectToCheckout(
        sessionResult.sessionId
      );

      console.log('🔄 Redirect result:', redirectResult);
      return redirectResult;
    } catch (error) {
      console.error('💥 Payment process error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },

  /**
   * Retrieve checkout session details
   */
  getCheckoutSession: async (sessionId: string) => {
    try {
      console.log('🔍 Retrieving checkout session:', sessionId);
      const response = await fetch(
        `/api/stripe/create-checkout-session?session_id=${sessionId}`
      );

      console.log('📡 Session retrieval response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📄 Session data:', data);

      if (!data.success) {
        throw new Error(data.error || 'Failed to retrieve session');
      }

      return {
        success: true,
        session: data.session,
      };
    } catch (error) {
      console.error('❌ Session retrieval error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },

  /**
   * Format amount for display
   */
  formatAmount: (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  },

  /**
   * Validate Stripe configuration on client side
   */
  validateConfig: () => {
    const errors: string[] = [];

    if (!stripePublishableKey) {
      errors.push('Missing Stripe publishable key');
    } else if (!stripePublishableKey.startsWith('pk_')) {
      errors.push('Invalid Stripe publishable key format');
    }

    return {
      isValid: errors.length === 0,
      errors,
      isTestMode: stripePublishableKey?.startsWith('pk_test_') || false,
    };
  },
};

// Test card numbers for development
export const STRIPE_TEST_CARDS = {
  visa: '****************',
  visaDebit: '****************',
  mastercard: '****************',
  amex: '***************',
  declined: '****************',
  insufficientFunds: '****************',
  expired: '****************',
  incorrectCvc: '****************',
} as const;

// Export types for use in components
export type StripeClientResult<T = Record<string, unknown>> = {
  success: boolean;
  error?: string;
} & T;
