import * as React from 'react';
import { cn } from '@/lib/utils';
import { Label } from './label';
import { phoneUtils } from '@/utils/schema-utils';

export interface PhoneInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  // Custom props for phone formatting
  value?: string;
  onChange?: (value: string) => void;
  onFormattedChange?: (formatted: string, raw: string) => void;
  
  // shadcn/ui specific props
  inputSize?: 'sm' | 'md' | 'lg';
  
  // HeroUI compatibility props (for gradual migration)
  variant?: 'flat' | 'bordered' | 'underlined' | 'faded';
  color?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger';
    
  // Label support for HeroUI compatibility
  label?: string;
  description?: string;
  isRequired?: boolean;
}

const PhoneInput = React.forwardRef<HTMLInputElement, PhoneInputProps>(
  (
    {
      className,
      inputSize = 'md',
      label,
      description,
      isRequired,
      id,
      value = '',
      onChange,
      onFormattedChange,
      onPaste,
      ...props
    },
    ref
  ) => {
    // Generate ID at the top level to avoid conditional hook calls
    const generatedId = React.useId();
    const inputId = id || generatedId;

    const [formattedValue, setFormattedValue] = React.useState(() =>
      phoneUtils.parseAndFormat(value)
    );

    // Update formatted value when prop value changes
    React.useEffect(() => {
      const newFormatted = phoneUtils.parseAndFormat(value);
      setFormattedValue(newFormatted);
    }, [value]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      const formatted = phoneUtils.formatPhoneNumber(inputValue);
      const rawDigits = phoneUtils.getStorageValue(formatted);
      
      setFormattedValue(formatted);
      
      // Call the onChange callback with the raw digits for storage
      if (onChange) {
        onChange(rawDigits);
      }
      
      // Call the formatted change callback if provided
      if (onFormattedChange) {
        onFormattedChange(formatted, rawDigits);
      }
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
      e.preventDefault();
      const pastedText = e.clipboardData.getData('text');
      const formatted = phoneUtils.parseAndFormat(pastedText);
      const rawDigits = phoneUtils.getStorageValue(formatted);
      
      setFormattedValue(formatted);
      
      if (onChange) {
        onChange(rawDigits);
      }
      
      if (onFormattedChange) {
        onFormattedChange(formatted, rawDigits);
      }
      
      // Call original onPaste if provided
      if (onPaste) {
        onPaste(e);
      }
    };

    const inputProps = { ...props };
    delete inputProps.variant;
    delete inputProps.color;

    if (label) {
      return (
        <div className="space-y-1">
          <Label htmlFor={inputId}>
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <input
            id={inputId}
            type="tel"
            ref={ref}
            value={formattedValue}
            onChange={handleInputChange}
            onPaste={handlePaste}
            placeholder="(*************"
            data-slot="input"
            className={cn(
              'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
              'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
              'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
              // IEPA enhanced styling and accessibility
              'border-[var(--iepa-gray-300)] focus-visible:ring-[var(--iepa-primary-blue)]/20 focus-visible:border-[var(--iepa-primary-blue)]',
              // Size variants with accessibility - Compact padding
              {
                'h-10 px-2 py-1 min-h-[44px]': inputSize === 'sm',
                'h-10 px-3 py-2 min-h-[44px]': inputSize === 'md',
                'h-11 px-3 py-2 min-h-[44px]': inputSize === 'lg',
              },
              className
            )}
            {...inputProps}
          />
          {description && (
            <p className="text-sm text-[var(--iepa-gray-600)]">{description}</p>
          )}
        </div>
      );
    }

    return (
      <input
        id={inputId}
        type="tel"
        ref={ref}
        value={formattedValue}
        onChange={handleInputChange}
        onPaste={handlePaste}
        placeholder="(*************"
        data-slot="input"
        className={cn(
          'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
          'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
          // IEPA enhanced styling and accessibility
          'border-[var(--iepa-gray-300)] focus-visible:ring-[var(--iepa-primary-blue)]/20 focus-visible:border-[var(--iepa-primary-blue)]',
          // Size variants with accessibility - Compact padding
          {
            'h-10 px-2 py-1 min-h-[44px]': inputSize === 'sm',
            'h-10 px-3 py-2 min-h-[44px]': inputSize === 'md',
            'h-11 px-3 py-2 min-h-[44px]': inputSize === 'lg',
          },
          className
        )}
        {...inputProps}
      />
    );
  }
);

PhoneInput.displayName = 'PhoneInput';

export { PhoneInput };
