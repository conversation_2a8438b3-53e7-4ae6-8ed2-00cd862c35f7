#!/bin/bash

# IEPA Production Data Import Script
# Generated: 2025-06-07T18:35:51.331Z

set -e

echo "🔄 Importing IEPA production data..."

# Check if local Supabase is running
if ! curl -s http://127.0.0.1:54321/rest/v1/ > /dev/null 2>&1; then
    echo "❌ Local Supabase is not running"
    echo "Please run: supabase start"
    exit 1
fi

echo "✅ Local Supabase is running"

# Import data
echo "📥 Importing 246 rows..."
if psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -f ".docs/production-dumps/production-dump-2025-06-07.sql"; then
    echo "✅ Data imported successfully!"
    echo ""
    echo "🔗 Access your data:"
    echo "   Studio: http://127.0.0.1:54323"
    echo "   API: http://127.0.0.1:54321"
else
    echo "❌ Import failed"
    exit 1
fi
