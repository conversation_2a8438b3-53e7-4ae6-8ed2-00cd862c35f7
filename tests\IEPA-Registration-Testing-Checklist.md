# IEPA Registration Testing Checklist

## Overview

This comprehensive checklist covers all registration types in the IEPA Annual Meeting system. Each registration type has specific requirements, pricing, and validation rules that need to be tested.

## 🎯 **Registration Types Summary**

| Registration Type | Price | Golf | Club Rental | Payment Required | Special Features |
|------------------|-------|------|-------------|------------------|------------------|
| IEPA Member | $2,369 | +$200 | +$75 | Yes | Standard member pricing |
| Non-IEPA Member | $2,730 | +$200 | +$75 | Yes | Higher non-member rate |
| Day Use IEPA | $1,803 | +$200 | +$75 | Yes | No lodging included |
| Day Use Non-IEPA | $2,163 | +$200 | +$75 | Yes | No lodging, non-member |
| Federal/State Gov | $2,060 | +$200 | +$75 | Yes | Government discount |
| CCA Member | $2,369 | +$200 | +$75 | Yes | Same as IEPA member |
| Spouse | $500 | +$200 | +$75 | Yes | Linked to primary attendee |
| Child | $100 | +$200 | +$75 | Yes | Linked to primary attendee |
| Speaker (Comped) | $0 | +$200 | +$75 | Golf only | 1 night lodging, 3 meals |
| Speaker (Full) | $1,500 | +$200 | +$75 | Yes | Full meeting access |
| Sponsor | Varies | N/A | N/A | By check | Sponsorship packages |
| Sponsor Attendee | $0 | +$200 | +$75 | Golf only | Linked to sponsor |
| Staff | $0 | +$200 | +$75 | Golf only | Admin created only |

---

## 📋 **1. IEPA Member Registration**

### Test Scenario: `iepa-member-registration-e2e.spec.js`

**✅ Required Fields:**
- [ ] First Name, Last Name, Name on Badge
- [ ] Email (auto-filled if logged in)
- [ ] Phone Number (formatted as (XXX) XXX-XXXX)
- [ ] Organization, Job Title
- [ ] Street Address, City, State, ZIP Code
- [ ] Emergency Contact Name, Phone, Relationship

**✅ Pricing Validation:**
- [ ] Base price: $2,369
- [ ] Golf tournament: +$200
- [ ] Golf club rental: +$75
- [ ] Total calculation correct

**✅ Form Features:**
- [ ] Multi-step form navigation
- [ ] Form persistence (localStorage)
- [ ] Meal selection (all meals free)
- [ ] Night selection (both nights default)
- [ ] Golf handedness selection (left/right)

**✅ Payment Flow:**
- [ ] Stripe checkout integration
- [ ] Promo code application ('TEST' for 100% discount)
- [ ] Payment completion and webhook firing
- [ ] Redirect to success page

**✅ Post-Registration:**
- [ ] Welcome email sent
- [ ] Registration appears in my-registrations
- [ ] PDF receipt generated
- [ ] Conference page access granted

---

## 📋 **2. Non-IEPA Member Registration**

### Test Scenario: `non-iepa-member-registration-e2e.spec.js`

**✅ Differences from IEPA Member:**
- [ ] Higher base price: $2,730 (+$361)
- [ ] Same form fields and validation
- [ ] Same golf and meal options
- [ ] Same payment and email flow

**✅ Specific Tests:**
- [ ] Price calculation shows $2,730 base
- [ ] Registration type correctly saved as 'non-iepa-member'
- [ ] PDF receipt shows correct pricing

---

## 📋 **3. Day Use IEPA Member Registration**

### Test Scenario: `day-use-iepa-registration-e2e.spec.js`

**✅ Unique Features:**
- [ ] Lower price: $1,803 (no lodging)
- [ ] Night selection disabled/hidden
- [ ] Meals still available (day meals only)
- [ ] Golf tournament still available

**✅ Validation:**
- [ ] Cannot select lodging nights
- [ ] Meal options limited to day meals
- [ ] Price reflects no lodging cost

---

## 📋 **4. Day Use Non-IEPA Member Registration**

### Test Scenario: `day-use-non-iepa-registration-e2e.spec.js`

**✅ Features:**
- [ ] Price: $2,163 (day use + non-member premium)
- [ ] Same restrictions as Day Use IEPA
- [ ] Higher pricing tier validation

---

## 📋 **5. Federal/State Government Registration**

### Test Scenario: `government-registration-e2e.spec.js`

**✅ Special Features:**
- [ ] Discounted price: $2,060
- [ ] Government organization validation
- [ ] Same form fields as standard attendee
- [ ] Special pricing tier in receipt

---

## 📋 **6. CCA Member Registration**

### Test Scenario: `cca-member-registration-e2e.spec.js`

**✅ Features:**
- [ ] Same price as IEPA member: $2,369
- [ ] CCA organization validation
- [ ] Standard attendee features
- [ ] Correct registration type saved

---

## 📋 **7. Spouse Registration**

### Test Scenario: `spouse-registration-e2e.spec.js`

**✅ Unique Requirements:**
- [ ] Linked attendee email field (required)
- [ ] Lower price: $500
- [ ] Primary attendee validation
- [ ] Linked registration in database

**✅ Special Validation:**
- [ ] Primary attendee must exist
- [ ] Primary attendee email validation
- [ ] Cannot register without valid primary
- [ ] Spouse relationship properly linked

**✅ Features:**
- [ ] Golf tournament available (+$200)
- [ ] Club rental available (+$75)
- [ ] Meal selections available
- [ ] Night selections available

---

## 📋 **8. Child Registration**

### Test Scenario: `child-registration-e2e.spec.js`

**✅ Unique Features:**
- [ ] Lowest price: $100
- [ ] Linked to primary attendee
- [ ] Age validation (if implemented)
- [ ] Simplified meal options

**✅ Special Cases:**
- [ ] Multiple children per primary attendee
- [ ] Child-specific meal options
- [ ] Golf availability for older children

---

## 📋 **9. Speaker Registration (Comped)**

### Test Scenario: `speaker-comped-registration-e2e.spec.js`

**✅ Speaker-Specific Fields:**
- [ ] Presentation title and abstract
- [ ] Professional bio (50-500 words)
- [ ] File uploads (presentation, headshot)
- [ ] Speaking experience
- [ ] Equipment needs

**✅ Pricing:**
- [ ] Base registration: $0
- [ ] Golf tournament: +$200 (if selected)
- [ ] Club rental: +$75 (if selected)
- [ ] Only golf charges apply

**✅ Lodging/Meals:**
- [ ] One night lodging (user choice)
- [ ] 3 meals included
- [ ] Limited meal selection vs full attendee

**✅ File Uploads:**
- [ ] Presentation file (PDF/PPT/PPTX, max 50MB)
- [ ] Professional headshot (JPG/PNG, max 5MB)
- [ ] File validation and storage

---

## 📋 **10. Speaker Registration (Full Meeting)**

### Test Scenario: `speaker-full-meeting-registration-e2e.spec.js`

**✅ Differences from Comped:**
- [ ] Base price: $1,500
- [ ] Two nights lodging
- [ ] All meals included
- [ ] Full attendee privileges

**✅ Payment Required:**
- [ ] Stripe payment for $1,500 base
- [ ] Additional golf charges
- [ ] Full payment flow testing

---

## 📋 **11. Sponsor Registration**

### Test Scenario: `sponsor-registration-e2e.spec.js`

**✅ Sponsor-Specific Fields:**
- [ ] Sponsor name and URL
- [ ] Sponsorship level selection
- [ ] Contact information
- [ ] Sponsor description
- [ ] Logo upload

**✅ Sponsorship Levels:**
- [ ] $5,000 (1 attendee)
- [ ] $10,000 (2 attendees)
- [ ] $15,000 (3 attendees)
- [ ] $20,000 (4 attendees)
- [ ] $25,000 (5 attendees)

**✅ Payment Method:**
- [ ] Check payment only (no Stripe)
- [ ] Payment instructions displayed
- [ ] Invoice generation
- [ ] Check tracking functionality

---

## 📋 **12. Sponsor Attendee Registration**

### Test Scenario: `sponsor-attendee-registration-e2e.spec.js`

**✅ Unique Features:**
- [ ] Free base registration ($0)
- [ ] Linked to sponsor organization
- [ ] Email domain validation or coupon code
- [ ] Golf charges still apply

**✅ Sponsor Linking:**
- [ ] Email domain matching
- [ ] Coupon code validation
- [ ] Automatic discount application
- [ ] Sponsor relationship tracking

**✅ Access Method:**
- [ ] Special registration URL
- [ ] Email invitation link
- [ ] Coupon code entry

---

## 📋 **13. Staff Registration (Admin Only)**

### Test Scenario: `staff-registration-e2e.spec.js`

**✅ Admin Creation:**
- [ ] Only accessible via admin panel
- [ ] Admin authentication required
- [ ] Staff member selection
- [ ] Free registration ($0)

**✅ Features:**
- [ ] All attendee features available
- [ ] Golf charges apply if selected
- [ ] Full meal and lodging options
- [ ] Admin-only creation process

---

## 🔧 **Cross-Registration Testing**

### Test Scenario: `registration-constraints-e2e.spec.js`

**✅ Constraint Validation:**
- [ ] One primary registration per user
- [ ] Multiple spouse/child registrations allowed
- [ ] Speaker + attendee registration conflict
- [ ] Sponsor + attendee registration conflict

**✅ Edge Cases:**
- [ ] Duplicate email prevention
- [ ] Registration type switching
- [ ] Incomplete registration handling
- [ ] Payment failure recovery

---

## 📧 **Email Testing for All Types**

### Test Scenario: `email-verification-all-types-e2e.spec.js`

**✅ Email Templates:**
- [ ] Attendee welcome email
- [ ] Speaker confirmation email
- [ ] Sponsor confirmation email
- [ ] Sponsor attendee welcome email
- [ ] Staff registration email

**✅ Email Content:**
- [ ] Correct registration type mentioned
- [ ] Accurate pricing information
- [ ] Proper contact information
- [ ] Conference details included

---

## 🎯 **Test Execution Strategy**

### Priority 1 (Core Flows):
1. IEPA Member Registration
2. Non-IEPA Member Registration
3. Speaker (Comped) Registration
4. Sponsor Attendee Registration

### Priority 2 (Special Cases):
1. Spouse Registration
2. Child Registration
3. Government Registration
4. Day Use Registrations

### Priority 3 (Admin/Complex):
1. Sponsor Registration
2. Staff Registration
3. Speaker (Full Meeting)
4. Cross-registration constraints

### Test Data Requirements:
- Unique email per test run
- Valid test credit cards
- Promo codes ('TEST' for 100% discount)
- Admin credentials for staff testing
- Sponsor organization data

### Environment Setup:
- Development server on port 6969
- Stripe test keys configured
- SendGrid email testing
- Supabase test database
- Admin user access
