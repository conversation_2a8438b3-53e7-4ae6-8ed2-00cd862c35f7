// Golf Add-On Stripe Checkout Session API
// Create Stripe checkout session for golf tournament add-on

import { NextRequest, NextResponse } from 'next/server';
import { stripe, STRIPE_CONFIG, formatAmountForStripe, getStripeUrls } from '@/lib/stripe';
import type { GolfAddOnSessionData } from '@/types/golfAddOn';

export async function POST(request: NextRequest) {
  try {
    console.log('🏌️ Creating golf add-on checkout session');

    const body: GolfAddOnSessionData = await request.json();
    const {
      registrationId,
      userId,
      customerName,
      customerEmail,
      golfTournament,
      golfClubRental,
      golfClubHandedness,
      lineItems,
      totalAmount,
      metadata,
    } = body;

    // Validate required fields
    if (!registrationId || !userId || !customerEmail || !lineItems || lineItems.length === 0) {
      console.error('❌ Missing required fields for golf add-on checkout session');
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (totalAmount <= 0) {
      console.error('❌ Invalid total amount for golf add-on');
      return NextResponse.json(
        { error: 'Total amount must be greater than 0' },
        { status: 400 }
      );
    }

    console.log('💰 Golf add-on checkout details:', {
      registrationId,
      customerEmail,
      totalAmount,
      lineItems: lineItems.length,
      golfTournament,
      golfClubRental,
    });

    // Get base URL for success/cancel URLs
    const baseUrl = request.headers.get('origin') || 'http://localhost:3000';
    const urls = getStripeUrls(baseUrl);

    // Prepare line items for Stripe
    const stripeLineItems = lineItems.map(item => ({
      price_data: {
        currency: STRIPE_CONFIG.currency,
        product_data: {
          name: item.name,
          description: item.description,
        },
        unit_amount: formatAmountForStripe(item.price),
      },
      quantity: item.quantity,
    }));

    console.log('🛒 Stripe line items:', stripeLineItems);

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      ...STRIPE_CONFIG.checkoutDefaults,
      payment_method_types: STRIPE_CONFIG.paymentMethodTypes,
      line_items: stripeLineItems,
      customer_email: customerEmail,
      success_url: `${urls.success}?session_id={CHECKOUT_SESSION_ID}&registration_id=${registrationId}&type=golf-addon`,
      cancel_url: `${urls.cancel}?registration_id=${registrationId}&type=golf-addon`,
      metadata: {
        registrationId,
        userId,
        registrationType: 'golf-addon',
        golfTournament: golfTournament.toString(),
        golfClubRental: golfClubRental.toString(),
        golfClubHandedness: golfClubHandedness || '',
        customerName: customerName || '',
        ...metadata,
      },
      billing_address_collection: 'required',
      automatic_tax: {
        enabled: false,
      },
      invoice_creation: {
        enabled: true,
        invoice_data: {
          description: `IEPA 2025 Conference - Golf Tournament Add-On`,
          metadata: {
            registrationId,
            userId,
            registrationType: 'golf-addon',
            golfTournament: golfTournament.toString(),
            golfClubRental: golfClubRental.toString(),
          },
          footer: 'Thank you for adding golf to your IEPA 2025 Conference registration!',
        },
      },
    });

    console.log('✅ Golf add-on checkout session created:', session.id);

    return NextResponse.json({
      success: true,
      sessionId: session.id,
      url: session.url,
    });

  } catch (error) {
    console.error('❌ Error creating golf add-on checkout session:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create checkout session',
      },
      { status: 500 }
    );
  }
}
