'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { FiCheck, FiX, <PERSON>Loader, Fi<PERSON>ser, FiShield } from 'react-icons/fi';
import Link from 'next/link';

interface AuthStatusIndicatorProps {
  variant?: 'badge' | 'banner' | 'card';
  showActions?: boolean;
  className?: string;
}

export function AuthStatusIndicator({
  variant = 'badge',
  showActions = false,
  className = '',
}: AuthStatusIndicatorProps) {
  const { user, loading, signOut } = useAuth();

  if (loading) {
    if (variant === 'badge') {
      return (
        <Badge variant="outline" className={`animate-pulse ${className}`}>
          <FiLoader className="w-3 h-3 mr-1 animate-spin" />
          Loading...
        </Badge>
      );
    }

    if (variant === 'banner') {
      return (
        <Alert className={`border-blue-200 bg-blue-50 ${className}`}>
          <FiLoader className="h-4 w-4 animate-spin text-blue-600" />
          <AlertDescription className="text-blue-800">
            Checking authentication status...
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <FiLoader className="w-4 h-4 animate-spin text-blue-600" />
        <span className="text-sm text-gray-600">
          Checking authentication...
        </span>
      </div>
    );
  }

  if (user) {
    const isEmailConfirmed = user.email_confirmed_at;

    if (variant === 'badge') {
      return (
        <Badge
          variant="default"
          className={`bg-green-100 text-green-800 border-green-200 ${className}`}
        >
          <FiCheck className="w-3 h-3 mr-1" />
          Authenticated
        </Badge>
      );
    }

    if (variant === 'banner') {
      return (
        <Alert className={`border-green-200 bg-green-50 ${className}`}>
          <FiShield className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <div className="flex items-center justify-between">
              <div>
                <span className="font-medium">Signed in as {user.email}</span>
                {!isEmailConfirmed && (
                  <span className="block text-sm text-amber-700 mt-1">
                    ⚠️ Email not confirmed - check your inbox
                  </span>
                )}
              </div>
              {showActions && (
                <div className="flex gap-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => signOut()}
                    className="text-green-700 border-green-300 hover:bg-green-100"
                  >
                    Sign Out
                  </Button>
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
          <FiCheck className="w-4 h-4" />
          <span>Authenticated</span>
        </div>
        {showActions && (
          <Button variant="outline" size="sm" onClick={() => signOut()}>
            Sign Out
          </Button>
        )}
      </div>
    );
  }

  // Not authenticated
  if (variant === 'badge') {
    return (
      <Badge
        variant="destructive"
        className={`bg-red-100 text-red-800 border-red-200 ${className}`}
      >
        <FiX className="w-3 h-3 mr-1" />
        Not Authenticated
      </Badge>
    );
  }

  if (variant === 'banner') {
    return (
      <Alert className={`border-red-200 bg-red-50 ${className}`}>
        <FiUser className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          <div className="flex items-center justify-between">
            <span>You need to sign in to access this content</span>
            {showActions && (
              <div className="flex gap-2 ml-4">
                <Button
                  asChild
                  size="sm"
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  <Link href="/auth/magic-link">Sign In</Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="sm"
                  className="text-red-700 border-red-300 hover:bg-red-100"
                >
                  <Link href="/auth/signup">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex items-center gap-2 px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">
        <FiX className="w-4 h-4" />
        <span>Not Authenticated</span>
      </div>
      {showActions && (
        <div className="flex gap-2">
          <Button asChild size="sm">
            <Link href="/auth/magic-link">Sign In</Link>
          </Button>
          <Button asChild variant="outline" size="sm">
            <Link href="/auth/signup">Sign Up</Link>
          </Button>
        </div>
      )}
    </div>
  );
}

// Specialized component for protected pages
export function ProtectedPageBanner({
  className = '',
}: {
  className?: string;
}) {
  return (
    <div className={`bg-green-50 border-b border-green-200 ${className}`}>
      <div className="container mx-auto px-4 py-2">
        <AuthStatusIndicator variant="banner" showActions={true} />
      </div>
    </div>
  );
}

// Specialized component for user info display
export function UserInfoDisplay({ className = '' }: { className?: string }) {
  const { user } = useAuth();

  if (!user) return null;

  return (
    <div
      className={`flex items-center gap-2 text-sm text-gray-600 ${className}`}
    >
      <FiUser className="w-4 h-4" />
      <span>Signed in as:</span>
      <span className="font-medium text-gray-900">{user.email}</span>
      {!user.email_confirmed_at && (
        <Badge
          variant="outline"
          className="text-xs bg-amber-50 text-amber-700 border-amber-200"
        >
          Unconfirmed
        </Badge>
      )}
    </div>
  );
}
