'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  debugResetPassword,
  getAuthDebugLogs,
  clearAuthDebugLogs,
  getAuthEnvironmentInfo,
  logAuthOperation,
  type AuthDebugInfo,
} from '@/lib/auth-debug';
import { useAuth } from '@/contexts/AuthContext';

export default function AuthDebugPage() {
  const { user, loading } = useAuth();
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [debugLogs, setDebugLogs] = useState<AuthDebugInfo[]>([]);
  const [envInfo, setEnvInfo] = useState<{
    appUrl: string;
    supabaseUrl?: string;
    hasAnonKey: boolean;
    nodeEnv?: string;
    timestamp: string;
    userAgent?: string;
    location?: string;
  } | null>(null);

  useEffect(() => {
    // Initialize environment info
    setEnvInfo(getAuthEnvironmentInfo());

    // Load existing debug logs
    setDebugLogs(getAuthDebugLogs());

    // Log page access
    logAuthOperation('authDebugPage', true, null, null, {
      step: 'page_loaded',
      userAuthenticated: !!user,
    });
  }, [user]);

  const handleTestResetPassword = async () => {
    if (!email) {
      setMessage('Please enter an email address');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const result = await debugResetPassword(email);

      if (result.error) {
        setMessage(
          `Error: ${result.error instanceof Error ? result.error.message : JSON.stringify(result.error)}`
        );
      } else {
        setMessage(
          'Password reset email sent successfully! Check the debug logs below for details.'
        );
      }

      // Refresh debug logs
      setDebugLogs(getAuthDebugLogs());
    } catch (error) {
      setMessage(
        `Exception: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearLogs = () => {
    clearAuthDebugLogs();
    setDebugLogs([]);
    setMessage('Debug logs cleared');
  };

  const handleRefreshLogs = () => {
    setDebugLogs(getAuthDebugLogs());
    setMessage('Debug logs refreshed');
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="iepa-heading-1 mb-4">🔧 Auth Debug Center</h1>
            <p className="iepa-body">
              Comprehensive debugging tools for authentication issues
            </p>
            <Badge variant="outline" className="mt-2">
              Development Mode Only
            </Badge>
          </div>

          {/* Environment Information */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Environment Information</CardTitle>
              <CardDescription>
                Current environment and configuration details
              </CardDescription>
            </CardHeader>
            <CardContent>
              {envInfo && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>App URL:</strong> {envInfo.appUrl}
                  </div>
                  <div>
                    <strong>Supabase URL:</strong> {envInfo.supabaseUrl}
                  </div>
                  <div>
                    <strong>Has Anon Key:</strong>{' '}
                    {envInfo.hasAnonKey ? '✅' : '❌'}
                  </div>
                  <div>
                    <strong>Node Environment:</strong> {envInfo.nodeEnv}
                  </div>
                  <div>
                    <strong>Current Location:</strong> {envInfo.location}
                  </div>
                  <div>
                    <strong>User Agent:</strong>{' '}
                    {envInfo.userAgent?.substring(0, 50)}...
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Authentication Status */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Authentication Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <strong>Loading:</strong> {loading ? '⏳ Yes' : '✅ No'}
                </div>
                <div>
                  <strong>User:</strong>{' '}
                  {user
                    ? `✅ Authenticated (${user.email})`
                    : '❌ Not authenticated'}
                </div>
                {user && (
                  <div className="text-xs text-gray-600">
                    <strong>User ID:</strong> {user.id}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Password Reset Testing */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Test Password Reset</CardTitle>
              <CardDescription>
                Test the password reset flow with comprehensive logging
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="test-email">Email Address</Label>
                  <Input
                    id="test-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                  />
                </div>

                <Button
                  onClick={handleTestResetPassword}
                  disabled={isLoading || !email}
                  className="w-full"
                >
                  {isLoading ? 'Sending Reset Email...' : 'Test Password Reset'}
                </Button>

                {message && (
                  <Alert>
                    <AlertDescription>{message}</AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Debug Logs */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Debug Logs</CardTitle>
                  <CardDescription>
                    Real-time authentication operation logs ({debugLogs.length}{' '}
                    entries)
                  </CardDescription>
                </div>
                <div className="space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefreshLogs}
                  >
                    Refresh
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleClearLogs}>
                    Clear
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {debugLogs.length === 0 ? (
                  <p className="text-gray-500 text-sm">
                    No debug logs yet. Perform some auth operations to see logs
                    here.
                  </p>
                ) : (
                  debugLogs
                    .slice()
                    .reverse()
                    .map((log, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded border text-xs ${
                          log.success
                            ? 'bg-green-50 border-green-200'
                            : 'bg-red-50 border-red-200'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-1">
                          <span className="font-semibold">
                            {log.success ? '✅' : '❌'} {log.operation}
                          </span>
                          <span className="text-gray-500">
                            {new Date(log.timestamp).toLocaleTimeString()}
                          </span>
                        </div>

                        {!!log.context && (
                          <div className="mb-1">
                            <strong>Context:</strong>{' '}
                            {JSON.stringify(log.context, null, 2)}
                          </div>
                        )}

                        {!!log.error && (
                          <div className="text-red-600">
                            <strong>Error:</strong>{' '}
                            <pre className="whitespace-pre-wrap">
                              {typeof log.error === 'string'
                                ? log.error
                                : JSON.stringify(log.error, null, 2)}
                            </pre>
                          </div>
                        )}

                        {!!log.data && (
                          <div className="text-green-600">
                            <strong>Data:</strong>{' '}
                            <pre className="whitespace-pre-wrap">
                              {JSON.stringify(log.data, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Links */}
          <div className="mt-8 text-center space-x-4">
            <Button variant="outline" asChild>
              <a href="/auth/forgot-password">Test Forgot Password Page</a>
            </Button>
            <Button variant="outline" asChild>
              <a href="/auth/login">Test Login Page</a>
            </Button>
            <Button variant="outline" asChild>
              <a href="/auth-test">Auth Test Page</a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
