import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { attendeeId, sponsorId, type } = await request.json();

    // Validate required fields based on type
    if (
      !type ||
      (type === 'attendee' && !attendeeId) ||
      (type === 'sponsor' && !sponsorId)
    ) {
      return NextResponse.json(
        { error: 'Registration ID and type are required' },
        { status: 400 }
      );
    }

    let registration;
    let tableName;
    let emailField;

    // Handle different registration types
    if (type === 'attendee') {
      tableName = 'iepa_attendee_registrations';
      emailField = 'email';

      // Fetch attendee details
      const { data: attendee, error: attendeeError } = await supabase
        .from(tableName)
        .select('*')
        .eq('id', attendeeId)
        .single();

      if (attendeeError || !attendee) {
        console.error('Error fetching attendee:', attendeeError);
        return NextResponse.json(
          { error: 'Attendee not found' },
          { status: 404 }
        );
      }

      // Check if registration is completed (not draft status)
      if (attendee.payment_status === 'draft') {
        return NextResponse.json(
          { error: 'Cannot send welcome email for draft registrations' },
          { status: 400 }
        );
      }

      registration = attendee;
    } else if (type === 'sponsor') {
      tableName = 'iepa_sponsor_registrations';
      emailField = 'contact_email';

      // Fetch sponsor details
      const { data: sponsor, error: sponsorError } = await supabase
        .from(tableName)
        .select('*')
        .eq('id', sponsorId)
        .single();

      if (sponsorError || !sponsor) {
        console.error('Error fetching sponsor:', sponsorError);
        return NextResponse.json(
          { error: 'Sponsor not found' },
          { status: 404 }
        );
      }

      registration = sponsor;
    } else {
      return NextResponse.json(
        { error: 'Invalid registration type. Must be "attendee" or "sponsor"' },
        { status: 400 }
      );
    }

    // Initialize email service
    const { emailService } = await import('@/services/email');

    let emailSent = false;
    const recipientEmail = registration[emailField];
    let recipientName = '';

    if (type === 'attendee') {
      recipientName =
        registration.full_name ||
        `${registration.first_name} ${registration.last_name}`.trim();

      // Send attendee welcome email
      emailSent = await emailService.sendWelcomeEmail(
        recipientEmail,
        recipientName,
        {
          type: 'attendee',
          confirmationNumber: registration.id,
          userId: registration.user_id,
          hasLodging: registration.night_one || registration.night_two || false,
          hasGolf: registration.attending_golf || false,
        }
      );
    } else if (type === 'sponsor') {
      recipientName = registration.contact_name || registration.sponsor_name;

      // Send sponsor confirmation email using the specific template
      emailSent = await emailService.sendSponsorConfirmationEmail(
        recipientEmail,
        recipientName,
        {
          organizationName: registration.sponsor_name,
          sponsorshipLevel: registration.sponsorship_level,
          confirmationNumber: registration.id,
          registrationDate: new Date(
            registration.created_at
          ).toLocaleDateString(),
          paymentAmount: registration.sponsorship_amount
            ? `$${registration.sponsorship_amount.toLocaleString()}`
            : undefined,
          userId: registration.user_id,
        }
      );
    }

    if (!emailSent) {
      return NextResponse.json(
        { error: 'Failed to send welcome email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Welcome email sent successfully to ${recipientEmail}`,
      recipient: {
        email: recipientEmail,
        name: recipientName,
        type: type,
        registrationId: registration.id,
      },
    });
  } catch (error) {
    console.error('Error in resend welcome email API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
