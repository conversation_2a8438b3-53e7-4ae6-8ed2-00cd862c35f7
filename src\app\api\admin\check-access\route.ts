import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    console.log('[ADMIN-CHECK] Processing admin access check...');

    let currentUser = null;

    // Try Bearer token auth (for API requests)
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const supabaseAdmin = createSupabaseAdmin();

      const {
        data: { user: tokenUser },
        error: tokenError,
      } = await supabaseAdmin.auth.getUser(token);
      if (!tokenError && tokenUser) {
        currentUser = tokenUser;
      }
    }

    // For browser requests, we'll rely on the client-side admin check
    // This API is primarily for token-based verification
    if (!currentUser?.email) {
      console.log('[ADMIN-CHECK] No authenticated user found via Bearer token');
      return NextResponse.json({
        success: true,
        isAdmin: false,
        adminUser: null,
        message: 'No authenticated user via Bearer token',
      });
    }

    console.log('[ADMIN-CHECK] Checking admin access for:', currentUser.email);

    // Hardcoded admin list for immediate verification
    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    // Check if user is in hardcoded admin list
    if (!adminEmails.includes(currentUser.email)) {
      console.log('[ADMIN-CHECK] User not in admin list');
      return NextResponse.json({
        success: true,
        isAdmin: false,
        adminUser: null,
        message: 'User not in admin list',
      });
    }

    // User is in admin list, now try to get database record using admin client
    const supabaseAdmin = createSupabaseAdmin();

    const { data: adminUser, error: dbError } = await supabaseAdmin
      .from('iepa_admin_users')
      .select('*')
      .eq('email', currentUser.email)
      .eq('is_active', true)
      .single();

    if (dbError && dbError.code !== 'PGRST116') {
      console.warn('[ADMIN-CHECK] Database query failed:', dbError);
    }

    // Create fallback admin user if database fails or user not found
    const fallbackAdminUser = {
      id: 'fallback-admin-id',
      user_id: currentUser.id,
      email: currentUser.email,
      role:
        currentUser.email === '<EMAIL>' ? 'super_admin' : 'admin',
      permissions: {
        dashboard: true,
        users: currentUser.email === '<EMAIL>',
        settings: currentUser.email === '<EMAIL>',
        reports: true,
        database: currentUser.email === '<EMAIL>',
        audit: currentUser.email === '<EMAIL>',
      },
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const finalAdminUser = adminUser || fallbackAdminUser;
    const source = adminUser ? 'database' : 'fallback';

    console.log(`[ADMIN-CHECK] Admin access granted via ${source}`);

    return NextResponse.json({
      success: true,
      user: {
        id: currentUser.id,
        email: currentUser.email,
      },
      isAdmin: true,
      adminUser: finalAdminUser,
      message: `Admin access granted via ${source}`,
      source,
    });
  } catch (error) {
    console.error('Error in check-access API:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
