# IEPA Conference Manual Registration Guide

## Overview

The IEPA Conference Registration System provides comprehensive admin capabilities for manually registering attendees. This guide explains how administrators can register multiple people like "<PERSON>, <PERSON> and myself" through the admin interface.

## Manual Registration Methods

### Method 1: Admin Attendee Management Interface

**Location**: https://reg.iepa.com/admin/attendees

**Capabilities**:
- View all existing registrations
- Edit existing attendee information
- Delete registrations if needed
- Generate invoices and receipts
- Export attendee data to CSV

**Current Limitations**:
- The admin interface currently focuses on managing existing registrations
- Direct creation of new attendees through admin interface needs to be implemented

### Method 2: Individual Registration Process

**Recommended Approach for Multiple Registrations**:

1. **Primary Attendee Registration**:
   - Go to https://reg.iepa.com/register/attendee
   - Complete registration for yourself as the primary attendee
   - This creates your main account and registration

2. **Spouse/Child Registrations**:
   - During the primary registration, you can add spouse and child registrations
   - These are linked to your primary account
   - Pricing is automatically calculated for additional attendees

3. **Separate Individual Registrations**:
   - For colleagues like "<PERSON>" and "<PERSON>", they should register individually
   - Each person gets their own account and registration
   - This ensures proper email confirmations and individual access

### Method 3: Bulk Import System (Advanced)

**For Large Groups**:
The system includes an attendee import feature for bulk registrations:

- **Location**: Admin interface (requires development team setup)
- **Format**: JSON data import
- **Features**: 
  - Bulk user account creation
  - Automatic password generation
  - Profile creation and linking
  - Batch processing capabilities

## Step-by-Step Manual Registration Process

### For Individual Registrations

1. **Access Registration**:
   - Go to https://reg.iepa.com/register/attendee
   - Choose appropriate registration type (Attendee, Spouse, Child)

2. **Complete Personal Information**:
   - Full name and contact details
   - Organization and job title
   - Badge name preferences

3. **Select Conference Options**:
   - Lodging nights (Night One: Monday, Night Two: Tuesday)
   - Golf tournament participation ($200)
   - Golf club rental if needed ($75)
   - Meal selections (most meals are complimentary)

4. **Payment Processing**:
   - Online payment via Stripe
   - Check payment option available for sponsors
   - Automatic invoice generation

5. **Confirmation**:
   - Email confirmation sent automatically
   - PDF receipt/invoice attached
   - Registration details saved to admin system

### For Family Members (Spouse/Child)

1. **During Primary Registration**:
   - Select "Spouse" or "Child" registration type
   - Link to primary attendee email
   - Reduced pricing automatically applied

2. **Separate Accounts Option**:
   - Each family member can have their own account
   - Individual email confirmations
   - Separate access to registration details

## Admin Management Capabilities

### Current Admin Features

1. **Attendee Dashboard** (`/admin/attendees`):
   - View all registrations in paginated table
   - Search by name, email, or organization
   - Filter by registration type, payment status
   - Sort by various criteria

2. **Individual Attendee Management**:
   - **View Details**: Complete registration information
   - **Edit Registration**: Modify attendee information
   - **Generate Invoices**: Create and download PDF invoices
   - **Delete Registration**: Remove registrations if needed

3. **Export Capabilities**:
   - CSV export of all attendee data
   - Filtered exports based on search criteria
   - Registration reports and analytics

### Recommended Admin Workflow

1. **Monitor Registrations**:
   - Check admin dashboard regularly
   - Verify payment status
   - Review registration details

2. **Assist with Manual Registrations**:
   - Help attendees complete online registration
   - Process check payments for sponsors
   - Update registration information as needed

3. **Manage Group Registrations**:
   - Coordinate multiple registrations from same organization
   - Apply sponsor discounts where applicable
   - Ensure proper linking of related registrations

## Payment Processing Options

### Online Payments
- **Stripe Integration**: Secure credit card processing
- **Automatic Invoicing**: PDF invoices generated and emailed
- **Real-time Processing**: Immediate confirmation

### Check Payments
- **Sponsor Option**: Sponsors can pay by check
- **Manual Processing**: Admin marks payment as received
- **Invoice Generation**: PDF invoices for record keeping

### Discount Codes
- **Sponsor Discounts**: Automatic 100% discounts for sponsor attendees
- **Custom Codes**: Admin-created discount codes
- **Domain-based Discounts**: Automatic discounts based on email domain

## Recommendations for Multiple Registrations

### For "Jan, Sara and Myself" Scenario:

1. **Option A - Individual Registrations** (Recommended):
   - Each person registers separately at https://reg.iepa.com/register/attendee
   - Individual accounts and confirmations
   - Separate payment processing
   - Full individual control over registration details

2. **Option B - Primary + Linked Registrations**:
   - You register as primary attendee
   - Add Jan and Sara as additional attendees during your registration
   - Single payment for all three
   - Linked registrations in the system

3. **Option C - Admin Assistance**:
   - Contact admin team for assistance
   - Provide details for all three attendees
   - Admin can help coordinate registrations
   - Ensure proper setup and payment processing

## Support and Assistance

### For Registration Help:
- **Admin Dashboard**: https://reg.iepa.com/admin
- **Registration Portal**: https://reg.iepa.com/register
- **Technical Support**: Contact development team
- **Documentation**: Reference this guide and user documentation

### Admin Contact Information:
- **Admin Email**: <EMAIL>
- **System Access**: Admin dashboard with full management capabilities
- **Support**: Technical team available for complex registration scenarios

---

**Note**: The manual registration process is designed to be flexible and accommodate various registration scenarios while maintaining data integrity and proper payment processing.
