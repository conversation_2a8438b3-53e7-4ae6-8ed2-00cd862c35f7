'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { SpeakerRegistration } from '@/types/database';
import { Button } from '@/components/ui';
import { supabase } from '@/lib/supabase';
import { SpeakerEditForm } from '@/components/admin/forms/SpeakerEditForm';
import { FiArrowLeft } from 'react-icons/fi';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { useAuth } from '@/contexts/AuthContext';

export default function EditSpeakerPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const speakerId = searchParams?.get('id');
  const { user } = useAuth();

  // Check for test mode bypass
  const isTestMode =
    process.env.NODE_ENV === 'development' &&
    searchParams?.get('testAdmin') === 'true';

  const { isAdmin, isLoading: adminLoading } = useAdminAccess();

  // Special <NAME_EMAIL>
  const isEnoteware = user?.email === '<EMAIL>';

  const [speaker, setSpeaker] = useState<SpeakerRegistration | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Allow <NAME_EMAIL>, admin users, or test mode
    const hasAccess = isAdmin || isTestMode || isEnoteware;

    // Only redirect if we're sure the user is not an admin (loading is complete) and not enoteware
    if (!adminLoading && !hasAccess && !isTestMode && !isEnoteware) {
      console.log('🚫 Access denied - redirecting to admin dashboard');
      router.push('/admin');
      return;
    }

    // Only proceed if we have admin access, are in test mode, or are enoteware
    if ((hasAccess || isTestMode || isEnoteware) && speakerId) {
      fetchSpeaker();
    } else if (!speakerId && !adminLoading) {
      router.push('/admin/speakers');
    }
  }, [speakerId, isAdmin, adminLoading, isTestMode, isEnoteware, router]);

  const fetchSpeaker = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('iepa_speaker_registrations')
        .select('*')
        .eq('id', speakerId)
        .single();

      if (error) throw error;

      setSpeaker(data);
    } catch (err) {
      console.error('Error fetching speaker:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch speaker');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (updatedData: any) => {
    try {
      setSaving(true);
      setError(null);

      const { error: updateError } = await supabase
        .from('iepa_speaker_registrations')
        .update(updatedData)
        .eq('id', speakerId);

      if (updateError) throw updateError;

      // Refresh speaker data
      await fetchSpeaker();
    } catch (err) {
      console.error('Error updating speaker:', err);
      setError(err instanceof Error ? err.message : 'Failed to update speaker');
      throw err; // Re-throw to let the form handle the error
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/admin/speakers${isTestMode ? '?testAdmin=true' : ''}`);
  };

  if ((adminLoading && !isTestMode) || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--iepa-primary-blue)] mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading speaker...</p>
        </div>
      </div>
    );
  }

  if (!speaker) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Speaker not found</p>
        <Button onClick={handleBack} className="mt-4">
          <FiArrowLeft className="w-4 h-4 mr-2" />
          Back to Speakers
        </Button>
      </div>
    );
  }

  return (
    <>
      <style jsx global>{`
        /* Fix for long file URLs causing page width issues */
        .speaker-edit-container p {
          word-break: break-all;
          overflow-wrap: break-word;
          max-width: 100%;
        }

        /* Specific fix for file display containers */
        .speaker-edit-container .file-display-container,
        .speaker-edit-container [data-slot="base"] {
          max-width: 100%;
          overflow: hidden;
        }

        /* Ensure text content doesn't overflow */
        .speaker-edit-container .text-sm,
        .speaker-edit-container .text-xs {
          word-break: break-all;
          overflow-wrap: break-word;
          max-width: 100%;
        }

        /* Force container width constraints */
        .speaker-edit-container {
          max-width: 100vw;
          overflow-x: hidden;
        }
      `}</style>
      <div className="space-y-6 speaker-edit-container">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={handleBack}>
              <FiArrowLeft className="w-4 h-4 mr-2" />
              Back to Speakers
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Edit Speaker</h1>
              <p className="text-gray-600">{speaker.full_name}</p>
            </div>
          </div>
        </div>

        {/* Error Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Speaker Edit Form */}
        <SpeakerEditForm
          speakerData={speaker}
          onSave={handleSave}
          saving={saving}
        />
      </div>
    </>
  );
}
