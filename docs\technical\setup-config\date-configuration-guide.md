# IEPA 2025 Annual Meeting Date Configuration Guide

## Overview

This guide explains how to use and maintain the centralized date configuration system for the IEPA 2025 Annual Meeting Registration Application.

## Configuration Files

### Primary Configuration: `src/lib/conference-config.ts`

This is the single source of truth for all annual meeting dates, meal schedules, and events.

#### Key Components:

1. **CONFERENCE_DATES**: Main annual meeting start/end dates and golf tournament date
2. **MEAL_SCHEDULE**: Complete meal schedule with dates, times, and descriptions
3. **CONFERENCE_EVENTS**: All annual meeting events with scheduling details
4. **dateUtils**: Utility functions for date formatting and manipulation

### Schema Files: `src/schemas/`

Form schemas that reference the centralized date configuration:

- `attendee-iepa-2025.json`: Attendee registration form schema
- `speaker-iepa-2025.json`: Speaker registration form schema
- `sponsor-iepa-2025.json`: Sponsor registration form schema

### Utility Functions: `src/utils/schema-utils.ts`

Helper functions for working with dates and schemas in forms and components.

## How to Update Conference Dates

### Step 1: Update Main Configuration

Edit `src/lib/conference-config.ts`:

```typescript
export const CONFERENCE_DATES = {
  startDate: {
    date: '2025-09-22', // Update this ISO date
    displayDate: 'September 22, 2025', // Update display format
    dayOfWeek: 'Monday', // Update day of week
  },
  endDate: {
    date: '2025-09-24', // Update this ISO date
    displayDate: 'September 24, 2025', // Update display format
    dayOfWeek: 'Wednesday', // Update day of week
  },
  // ... update golf tournament date similarly
};
```

### Step 2: Update Meal Schedule

Update the `MEAL_SCHEDULE` array with correct dates and times:

```typescript
export const MEAL_SCHEDULE: MealEvent[] = [
  {
    id: 'dinner-day1',
    name: 'Welcome Dinner',
    date: '2025-09-22', // Update ISO date
    time: '18:00', // Update time in 24-hour format
    displayName: 'Welcome Dinner - September 22, 2025', // Update display name
    description: 'Opening night welcome dinner for all attendees',
  },
  // ... update other meals
];
```

### Step 3: Update Events (if needed)

Update `CONFERENCE_EVENTS` array with any schedule changes.

### Step 4: Update Metadata

Update the `CONFIG_METADATA` object:

```typescript
export const CONFIG_METADATA = {
  lastUpdated: '2025-01-29', // Update to current date
  version: '1.1.0', // Increment version
  notes: 'Updated with confirmed 2025 conference dates.',
};
```

## Using Date Configuration in Components

### Import the Configuration

```typescript
import {
  dateUtils,
  MEAL_SCHEDULE,
  CONFERENCE_DATES,
} from '@/lib/conference-config';
```

### Get Meal Options for Forms

```typescript
const mealOptions = dateUtils.getMealOptions();
// Returns array of meal objects with id, label, date, time, description
```

### Format Dates for Display

```typescript
const displayDate = dateUtils.formatDisplayDate('2025-09-22');
// Returns: "Monday, September 22, 2025"

const shortDate = dateUtils.formatShortDate('2025-09-22');
// Returns: "Sep 22"

const time = dateUtils.formatTime('18:00');
// Returns: "6:00 PM"
```

### Check Conference Dates

```typescript
const isConferenceDate = dateUtils.isConferenceDate('2025-09-23');
// Returns: true if date is within annual meeting period

const duration = dateUtils.getConferenceDuration();
// Returns: number of annual meeting days
```

## Using Schema Utilities

### Import Schema Utilities

```typescript
import { getMealUIOptions, validateMealSelections } from '@/utils/schema-utils';
```

### Get Meal Options for UI Components

```typescript
const mealOptions = getMealUIOptions();
// Returns meal options formatted for checkboxes, selects, etc.
```

### Validate Form Data

```typescript
const isValid = validateMealSelections(['dinner-day1', 'lunch-day2']);
// Returns: true if all selected meals are valid
```

## Form Integration Examples

### Checkbox Group for Meals

```tsx
import { getMealUIOptions } from '@/utils/schema-utils';

const MealSelection = () => {
  const mealOptions = getMealUIOptions();

  return (
    <CheckboxGroupWithOptions
      label="Meal Selections"
      description="Select which meals you plan to attend"
      options={mealOptions}
      // ... other props
    />
  );
};
```

### Display Annual Meeting Information

```tsx
import { CONFERENCE_DATES, dateUtils } from '@/lib/conference-config';

const ConferenceInfo = () => {
  return (
    <div>
      <h2>IEPA 2025 Annual Meeting</h2>
      <p>
        {dateUtils.formatDisplayDate(CONFERENCE_DATES.startDate.date)} -
        {dateUtils.formatDisplayDate(CONFERENCE_DATES.endDate.date)}
      </p>
      <p>Duration: {dateUtils.getConferenceDuration()} days</p>
    </div>
  );
};
```

## Best Practices

### 1. Always Use Centralized Configuration

- Never hardcode dates in components
- Always import from `conference-config.ts`
- Use utility functions for consistent formatting

### 2. Update All Related Files

When changing dates, update:

- Main configuration file
- Documentation
- Test data (if applicable)
- Version metadata

### 3. Test After Updates

- Verify all components display correct dates
- Test form validation with new dates
- Check that meal options appear correctly

### 4. Maintain Consistency

- Use ISO date format (YYYY-MM-DD) for all date storage
- Use 24-hour time format (HH:MM) for time storage
- Use consistent display formatting via utility functions

## Troubleshooting

### Common Issues

1. **Dates not updating in components**

   - Check that components are importing from `conference-config.ts`
   - Verify the import path is correct
   - Restart development server after config changes

2. **Meal options not displaying**

   - Check that `MEAL_SCHEDULE` is properly formatted
   - Verify meal IDs are unique
   - Check that components are using `getMealOptions()` or `getMealUIOptions()`

3. **Form validation errors**
   - Ensure meal IDs in forms match those in `MEAL_SCHEDULE`
   - Check that date formats are consistent (ISO format)
   - Verify schema files reference correct meal options

### Getting Help

If you encounter issues with the date configuration system:

1. Check this documentation first
2. Review the configuration files for syntax errors
3. Check browser console for JavaScript errors
4. Verify all imports are correct

## Future Enhancements

Planned improvements to the date configuration system:

1. **Dynamic Date Loading**: Load dates from environment variables or API
2. **Time Zone Support**: Handle different time zones for attendees
3. **Meal Pricing**: Individual meal pricing configuration
4. **Event Capacity**: Track and limit event attendance
5. **Calendar Integration**: Export conference schedule to calendar apps
