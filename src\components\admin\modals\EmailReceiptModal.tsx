'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { FiMail, FiUser, FiFileText, FiCheck, FiX } from 'react-icons/fi';
import { formatCurrency } from '@/lib/pdf-generation/utils';

interface ReceiptRecord {
  id: string;
  registration_id: string;
  registration_type: 'attendee' | 'speaker' | 'sponsor';
  full_name: string;
  email: string;
  organization: string;
  grand_total: number;
  receipt_url: string | null;
  receipt_generated_at: string | null;
  payment_status: string;
  payment_id: string | null;
  created_at: string;
}

interface EmailReceiptModalProps {
  receipt: ReceiptRecord;
  open: boolean;
  onClose: () => void;
  onSent: () => void;
}

export default function EmailReceiptModal({
  receipt,
  open,
  onClose,
  onSent,
}: EmailReceiptModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [emailData, setEmailData] = useState({
    email: receipt.email,
    subject: `Your IEPA 2025 Annual Meeting Receipt - ${receipt.full_name}`,
    message: `Dear ${receipt.full_name},

Thank you for your registration for the IEPA 2025 Annual Meeting. Please find your receipt attached to this email.

If you have any questions about your registration or the conference, please don't hesitate to contact us.

Best regards,
IEPA Team`,
  });

  const handleSendReceipt = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      console.log('Sending receipt email:', {
        registrationId: receipt.registration_id,
        registrationType: receipt.registration_type,
        email: emailData.email,
        fullName: receipt.full_name,
      });

      const response = await fetch('/api/admin/send-receipt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          registrationId: receipt.registration_id,
          registrationType: receipt.registration_type,
          email: emailData.email,
          fullName: receipt.full_name,
          subject: emailData.subject,
          message: emailData.message,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send receipt');
      }

      console.log('Receipt sent successfully:', result);
      setSuccess(true);

      // Auto-close after 2 seconds
      setTimeout(() => {
        onSent();
      }, 2000);
    } catch (err) {
      console.error('Error sending receipt:', err);
      setError(err instanceof Error ? err.message : 'Failed to send receipt');
    } finally {
      setLoading(false);
    }
  };

  const getRegistrationTypeBadge = (type: string) => {
    switch (type) {
      case 'attendee':
        return <Badge className="bg-blue-100 text-blue-800">Attendee</Badge>;
      case 'speaker':
        return <Badge className="bg-green-100 text-green-800">Speaker</Badge>;
      default:
        return <Badge variant="secondary">{type}</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FiMail className="w-5 h-5" />
            Send Receipt via Email
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Registration Details */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <FiUser className="w-4 h-4" />
              Registration Details
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Name:</span>
                <p className="font-medium">{receipt.full_name}</p>
              </div>
              <div>
                <span className="text-gray-600">Type:</span>
                <div className="mt-1">
                  {getRegistrationTypeBadge(receipt.registration_type)}
                </div>
              </div>
              <div>
                <span className="text-gray-600">Organization:</span>
                <p className="font-medium">{receipt.organization}</p>
              </div>
              <div>
                <span className="text-gray-600">Amount:</span>
                <p className="font-medium">
                  {formatCurrency(receipt.grand_total)}
                </p>
              </div>
              <div>
                <span className="text-gray-600">Payment Status:</span>
                <Badge className="bg-green-100 text-green-800 mt-1">
                  {receipt.payment_status}
                </Badge>
              </div>
              <div>
                <span className="text-gray-600">Receipt Status:</span>
                <Badge
                  className={
                    receipt.receipt_url
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }
                >
                  {receipt.receipt_url ? 'Generated' : 'Not Generated'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Email Form */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Recipient Email</Label>
              <Input
                id="email"
                type="email"
                value={emailData.email}
                onChange={e =>
                  setEmailData(prev => ({ ...prev, email: e.target.value }))
                }
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <Label htmlFor="subject">Email Subject</Label>
              <Input
                id="subject"
                value={emailData.subject}
                onChange={e =>
                  setEmailData(prev => ({ ...prev, subject: e.target.value }))
                }
                placeholder="Email subject"
              />
            </div>

            <div>
              <Label htmlFor="message">Email Message</Label>
              <Textarea
                id="message"
                value={emailData.message}
                onChange={e =>
                  setEmailData(prev => ({ ...prev, message: e.target.value }))
                }
                placeholder="Email message"
                rows={6}
              />
            </div>
          </div>

          {/* Status Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-red-800">
                <FiX className="w-4 h-4" />
                <span className="font-medium">Error</span>
              </div>
              <p className="text-red-700 mt-1">{error}</p>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-green-800">
                <FiCheck className="w-4 h-4" />
                <span className="font-medium">Success</span>
              </div>
              <p className="text-green-700 mt-1">
                Receipt sent successfully! The modal will close automatically.
              </p>
            </div>
          )}

          {/* Receipt Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 text-blue-800 mb-2">
              <FiFileText className="w-4 h-4" />
              <span className="font-medium">Receipt Information</span>
            </div>
            <p className="text-blue-700 text-sm">
              {receipt.receipt_url
                ? 'A PDF receipt will be automatically attached to this email.'
                : 'A PDF receipt will be generated and attached to this email.'}
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSendReceipt}
            disabled={loading || !emailData.email || success}
          >
            {loading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Sending...
              </>
            ) : success ? (
              <>
                <FiCheck className="w-4 h-4 mr-2" />
                Sent!
              </>
            ) : (
              <>
                <FiMail className="w-4 h-4 mr-2" />
                Send Receipt
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
