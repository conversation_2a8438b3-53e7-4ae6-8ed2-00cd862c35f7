import { test, expect } from '@playwright/test';

// Test configuration
const ADMIN_EMAIL = '<EMAIL>';
const TEST_BASE_URL = 'http://localhost:6969';

test.describe('Email Center E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the email center page
    await page.goto(`${TEST_BASE_URL}/admin/emails`);
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('displays email center page with all components', async ({ page }) => {
    // Check page title
    await expect(page.locator('h1')).toContainText('Email Center');
    
    // Check main components are present
    await expect(page.locator('[data-testid="email-status-summary"]')).toBeVisible();
    await expect(page.locator('[data-testid="email-log-filters"]')).toBeVisible();
    await expect(page.locator('[data-testid="send-email-form"]')).toBeVisible();
    
    // Check navigation elements
    await expect(page.locator('button:has-text("Refresh")')).toBeVisible();
  });

  test('loads and displays email logs', async ({ page }) => {
    // Wait for email logs to load
    await page.waitForSelector('[data-testid="email-log-card"]', { timeout: 10000 });
    
    // Check that email logs are displayed
    const emailCards = page.locator('[data-testid="email-log-card"]');
    await expect(emailCards.first()).toBeVisible();
    
    // Check email card contains expected elements
    await expect(emailCards.first().locator('.subject')).toBeVisible();
    await expect(emailCards.first().locator('.recipient')).toBeVisible();
    await expect(emailCards.first().locator('.status-badge')).toBeVisible();
  });

  test('filters email logs by status', async ({ page }) => {
    // Wait for initial load
    await page.waitForSelector('[data-testid="email-log-card"]', { timeout: 10000 });
    
    // Get initial count of email logs
    const initialCards = await page.locator('[data-testid="email-log-card"]').count();
    
    // Apply status filter
    await page.locator('[data-testid="status-filter"]').click();
    await page.locator('text=Sent').click();
    
    // Wait for filtered results
    await page.waitForTimeout(1000);
    
    // Check that filtering worked (results should change or show only sent emails)
    const sentBadges = page.locator('.status-badge:has-text("Sent")');
    const failedBadges = page.locator('.status-badge:has-text("Failed")');
    
    // All visible status badges should be "Sent"
    const sentCount = await sentBadges.count();
    const failedCount = await failedBadges.count();
    
    expect(failedCount).toBe(0);
    if (sentCount > 0) {
      expect(sentCount).toBeGreaterThan(0);
    }
  });

  test('searches email logs', async ({ page }) => {
    // Wait for initial load
    await page.waitForSelector('[data-testid="email-log-card"]', { timeout: 10000 });
    
    // Perform search
    const searchInput = page.locator('input[placeholder*="Search"]');
    await searchInput.fill('test');
    await searchInput.press('Enter');
    
    // Wait for search results
    await page.waitForTimeout(1000);
    
    // Check that search results are displayed
    // (This would depend on having test data with "test" in the content)
    const searchResults = page.locator('[data-testid="email-log-card"]');
    const resultsCount = await searchResults.count();
    
    // Verify search functionality is working (results may be 0 if no test data matches)
    expect(resultsCount).toBeGreaterThanOrEqual(0);
  });

  test('clears filters', async ({ page }) => {
    // Wait for initial load
    await page.waitForSelector('[data-testid="email-log-card"]', { timeout: 10000 });
    
    // Apply a filter first
    await page.locator('[data-testid="status-filter"]').click();
    await page.locator('text=Sent').click();
    await page.waitForTimeout(500);
    
    // Clear filters
    const clearButton = page.locator('button:has-text("Clear Filters")');
    if (await clearButton.isVisible()) {
      await clearButton.click();
      await page.waitForTimeout(1000);
      
      // Check that filters are cleared
      await expect(page.locator('text=Filters active')).not.toBeVisible();
    }
  });

  test('refreshes email logs', async ({ page }) => {
    // Wait for initial load
    await page.waitForSelector('[data-testid="email-log-card"]', { timeout: 10000 });
    
    // Click refresh button
    await page.locator('button:has-text("Refresh")').click();
    
    // Wait for refresh to complete
    await page.waitForTimeout(2000);
    
    // Check that page is still functional after refresh
    await expect(page.locator('[data-testid="email-status-summary"]')).toBeVisible();
    await expect(page.locator('[data-testid="email-log-filters"]')).toBeVisible();
  });

  test('displays email status summary', async ({ page }) => {
    // Wait for status summary to load
    await page.waitForSelector('[data-testid="email-status-summary"]', { timeout: 10000 });
    
    // Check that all status items are present
    await expect(page.locator('text=Sent:')).toBeVisible();
    await expect(page.locator('text=Failed:')).toBeVisible();
    await expect(page.locator('text=Pending:')).toBeVisible();
    await expect(page.locator('text=Total:')).toBeVisible();
    
    // Check that numbers are displayed (should be numeric)
    const sentCount = page.locator('text=Sent:').locator('..').locator('.badge');
    await expect(sentCount).toBeVisible();
  });

  test('opens send email form', async ({ page }) => {
    // Check that send email form is visible
    await expect(page.locator('[data-testid="send-email-form"]')).toBeVisible();
    await expect(page.locator('text=Send Email')).toBeVisible();
    
    // Check form fields
    await expect(page.locator('text=Recipients')).toBeVisible();
    await expect(page.locator('text=Subject')).toBeVisible();
    await expect(page.locator('text=Message')).toBeVisible();
    await expect(page.locator('button:has-text("Send Email")')).toBeVisible();
  });

  test('validates send email form', async ({ page }) => {
    // Try to submit empty form
    const sendButton = page.locator('button:has-text("Send Email")');
    
    // Button should be disabled when form is empty
    await expect(sendButton).toBeDisabled();
    
    // Fill in required fields
    await page.locator('[data-testid="recipients-select"]').click();
    await page.locator('text=All Attendees').click();
    
    await page.locator('input[placeholder*="subject"]').fill('Test Subject');
    await page.locator('textarea[placeholder*="message"]').fill('Test message content');
    
    // Button should now be enabled
    await expect(sendButton).toBeEnabled();
  });

  test('copies SendGrid ID to clipboard', async ({ page }) => {
    // Wait for email logs with SendGrid IDs
    await page.waitForSelector('[data-testid="email-log-card"]', { timeout: 10000 });
    
    // Look for copy button (SendGrid ID copy)
    const copyButton = page.locator('[title*="Copy SendGrid ID"]').first();
    
    if (await copyButton.isVisible()) {
      // Grant clipboard permissions
      await page.context().grantPermissions(['clipboard-read', 'clipboard-write']);
      
      // Click copy button
      await copyButton.click();
      
      // Check for success feedback (this would depend on implementation)
      // Could check for toast notification or visual feedback
      await page.waitForTimeout(500);
    }
  });

  test('handles pagination', async ({ page }) => {
    // Wait for initial load
    await page.waitForSelector('[data-testid="email-log-card"]', { timeout: 10000 });
    
    // Check if pagination is present
    const nextButton = page.locator('button:has-text("Next")');
    const prevButton = page.locator('button:has-text("Previous")');
    
    if (await nextButton.isVisible()) {
      // Test pagination
      await nextButton.click();
      await page.waitForTimeout(1000);
      
      // Check that we're on a different page
      await expect(page.locator('text=Page 2')).toBeVisible();
      
      // Go back
      if (await prevButton.isEnabled()) {
        await prevButton.click();
        await page.waitForTimeout(1000);
        await expect(page.locator('text=Page 1')).toBeVisible();
      }
    }
  });

  test('responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that components are still visible and functional
    await expect(page.locator('[data-testid="email-status-summary"]')).toBeVisible();
    await expect(page.locator('[data-testid="email-log-filters"]')).toBeVisible();
    
    // Check that mobile layout is applied
    // This would depend on specific mobile styling classes
    const mainContainer = page.locator('.grid');
    await expect(mainContainer).toBeVisible();
  });

  test('keyboard navigation', async ({ page }) => {
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Check that focus is visible on interactive elements
    const focusedElement = page.locator(':focus');
    await expect(focusedElement).toBeVisible();
    
    // Test keyboard shortcuts (if implemented)
    await page.keyboard.press('r'); // Refresh shortcut
    await page.waitForTimeout(1000);
  });

  test('error handling', async ({ page }) => {
    // Test error scenarios by intercepting network requests
    await page.route('**/api/admin/email-logs*', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ success: false, error: 'Server error' })
      });
    });
    
    // Refresh to trigger the error
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Check that error is handled gracefully
    await expect(page.locator('text=Error loading email logs')).toBeVisible();
  });

  test('loading states', async ({ page }) => {
    // Intercept API calls to add delay
    await page.route('**/api/admin/email-logs*', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      route.continue();
    });
    
    // Reload page to see loading state
    await page.reload();
    
    // Check for loading indicators
    const loadingIndicator = page.locator('[data-testid="loading-skeleton"]');
    if (await loadingIndicator.isVisible()) {
      await expect(loadingIndicator).toBeVisible();
    }
    
    // Wait for loading to complete
    await page.waitForSelector('[data-testid="email-log-card"]', { timeout: 15000 });
  });
});
