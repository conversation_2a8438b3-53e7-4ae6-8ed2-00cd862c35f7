'use client';

import * as React from 'react';
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
import { CheckIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { RegistrationPrice } from '@/lib/pricing-config';
import {
  FaUserTie,
  FaUsers,
  FaHandshake,
  FaUniversity,
  FaClock,
  FaCalendarDay,
  FaHeart,
  FaChild,
} from 'react-icons/fa';

// Icon mapping for different registration types
const getRegistrationIcon = (registrationId: string) => {
  const iconMap = {
    'iepa-member': FaUserTie,
    'non-iepa-member': FaUsers,
    cca: FaHandshake,
    'fed-state-government': FaUniversity,
    'day-use-iepa': FaCalendarDay,
    'day-use-non-iepa': <PERSON><PERSON><PERSON><PERSON>,
    spouse: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    child: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  };

  return iconMap[registrationId as keyof typeof iconMap] || FaUserTie;
};

interface RegistrationCardRadioProps {
  options: RegistrationPrice[];
  value?: string;
  onValueChange?: (value: string) => void;
  name?: string;
  required?: boolean;
  className?: string;
  'aria-describedby'?: string;
}

interface RegistrationCardProps {
  option: RegistrationPrice;
  isSelected: boolean;
  onSelect: (value: string) => void;
}

function RegistrationCard({
  option,
  isSelected,
  onSelect,
}: RegistrationCardProps) {
  const IconComponent = getRegistrationIcon(option.id);

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onSelect(option.id);
    }
  };

  return (
    <div
      id={`registration-card-${option.id}`}
      className="relative"
      data-testid={`registration-card-${option.id}`}
    >
      <RadioGroupPrimitive.Item
        value={option.id}
        id={`registration-radio-${option.id}`}
        className="sr-only"
        onClick={() => onSelect(option.id)}
        data-testid={`registration-radio-${option.id}`}
      />
      <label
        htmlFor={`registration-radio-${option.id}`}
        className={cn(
          'registration-card-label block cursor-pointer transition-all duration-250 ease-out',
          // Enhanced hover effects
          'hover:-translate-y-1 hover:shadow-[0_8px_25px_rgba(0,0,0,0.15)]',
          // Enhanced focus states with visible indicators
          'focus-within:ring-2 focus-within:ring-[var(--iepa-primary-blue)]/30 focus-within:ring-offset-2',
          'focus-within:-translate-y-1 focus-within:shadow-[0_8px_25px_rgba(0,0,0,0.15)]',
          // Accessibility improvements
          'focus-within:outline-none'
        )}
        data-testid={`registration-card-label-${option.id}`}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="radio"
        aria-checked={isSelected}
        aria-describedby={`registration-card-description-${option.id}`}
      >
        <Card
          id={`registration-card-content-${option.id}`}
          className={cn(
            'h-full transition-all duration-250 ease-out',
            // IEPA balanced padding - responsive design
            'p-3 sm:p-4 lg:p-5',
            // Enhanced hover background color change
            'hover:bg-[var(--iepa-gray-50)]',
            // Selection state styling with enhanced shadows
            isSelected
              ? 'border-2 border-[var(--iepa-primary-blue)] bg-[var(--iepa-primary-blue)]/5 shadow-lg'
              : 'border border-[var(--iepa-gray-300)] hover:border-[var(--iepa-primary-blue)]/50',
            // Accessibility
            'focus-within:outline-none'
          )}
          data-testid={`registration-card-content-${option.id}`}
        >
          <CardContent
            id={`registration-card-body-${option.id}`}
            className="p-0 space-y-3"
            data-testid={`registration-card-body-${option.id}`}
          >
            {/* Header with icon, title and selection indicator */}
            <div
              id={`registration-card-header-${option.id}`}
              className="flex items-start justify-between"
              data-testid={`registration-card-header-${option.id}`}
            >
              <div className="flex items-start gap-3 flex-1">
                {/* Icon */}
                <div
                  id={`registration-card-icon-${option.id}`}
                  className={cn(
                    'flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center mt-1',
                    isSelected
                      ? 'bg-[var(--iepa-primary-blue)]/10 text-[var(--iepa-primary-blue)]'
                      : 'bg-[var(--iepa-gray-100)] text-[var(--iepa-gray-600)]'
                  )}
                  data-testid={`registration-card-icon-${option.id}`}
                >
                  <IconComponent className="w-5 h-5" />
                </div>

                {/* Title and description */}
                <div
                  id={`registration-card-info-${option.id}`}
                  className="flex-1"
                  data-testid={`registration-card-info-${option.id}`}
                >
                  <h3
                    id={`registration-card-title-${option.id}`}
                    className={cn(
                      'font-semibold text-lg leading-tight',
                      isSelected
                        ? 'text-[var(--iepa-primary-blue)]'
                        : 'text-[var(--iepa-gray-800)]'
                    )}
                    data-testid={`registration-card-title-${option.id}`}
                  >
                    {option.displayName}
                  </h3>
                  <p
                    id={`registration-card-description-${option.id}`}
                    className="text-sm text-[var(--iepa-gray-600)] mt-1"
                    data-testid={`registration-card-description-${option.id}`}
                  >
                    {option.description}
                  </p>
                </div>
              </div>

              {/* Selection indicator */}
              <div
                id={`registration-card-indicator-${option.id}`}
                className={cn(
                  'flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center ml-3',
                  'transition-all duration-250 ease-out',
                  isSelected
                    ? 'border-[var(--iepa-primary-blue)] bg-[var(--iepa-primary-blue)] scale-110'
                    : 'border-[var(--iepa-gray-300)] scale-100'
                )}
                data-testid={`registration-card-indicator-${option.id}`}
              >
                {isSelected && (
                  <CheckIcon className="w-4 h-4 text-white transition-all duration-250 ease-out" />
                )}
              </div>
            </div>

            {/* Price section */}
            <div
              id={`registration-card-pricing-${option.id}`}
              className="space-y-2"
              data-testid={`registration-card-pricing-${option.id}`}
            >
              <div className="flex items-baseline gap-2">
                <span
                  id={`registration-card-price-${option.id}`}
                  className={cn(
                    'text-2xl font-bold',
                    isSelected
                      ? 'text-[var(--iepa-primary-blue)]'
                      : 'text-[var(--iepa-gray-800)]'
                  )}
                  data-testid={`registration-card-price-${option.id}`}
                >
                  ${option.basePrice.toLocaleString()}
                </span>
                <span
                  id={`registration-card-price-unit-${option.id}`}
                  className="text-sm text-[var(--iepa-gray-500)]"
                  data-testid={`registration-card-price-unit-${option.id}`}
                >
                  per person
                </span>
              </div>

              {/* Group discount pricing - Hidden per user request */}
              {/* {option.groupDiscountPrice && (
                <div
                  id={`registration-card-group-price-${option.id}`}
                  className="text-sm text-[var(--iepa-secondary-green)]"
                  data-testid={`registration-card-group-price-${option.id}`}
                >
                  <span className="font-medium">Group rate:</span> $
                  {option.groupDiscountPrice.toLocaleString()}
                  <span className="text-[var(--iepa-gray-500)]">
                    {' '}
                    (3rd person+)
                  </span>
                </div>
              )} */}
            </div>
          </CardContent>
        </Card>
      </label>
    </div>
  );
}

export function RegistrationCardRadio({
  options,
  value,
  onValueChange,
  name,
  required,
  className,
  'aria-describedby': ariaDescribedBy,
}: RegistrationCardRadioProps) {
  return (
    <RadioGroupPrimitive.Root
      id="registration-type-selector"
      value={value}
      onValueChange={onValueChange}
      name={name}
      required={required}
      className={cn('registration-card-radio-group space-y-4', className)}
      aria-describedby={ariaDescribedBy}
      data-testid="registration-type-selector"
    >
      {/* Single column layout for all screen sizes */}
      <div
        id="registration-cards-container"
        className="grid gap-3 sm:gap-4 grid-cols-1"
        data-testid="registration-cards-container"
      >
        {options.map(option => (
          <RegistrationCard
            key={option.id}
            option={option}
            isSelected={value === option.id}
            onSelect={onValueChange || (() => {})}
          />
        ))}
      </div>
    </RadioGroupPrimitive.Root>
  );
}

export default RegistrationCardRadio;
