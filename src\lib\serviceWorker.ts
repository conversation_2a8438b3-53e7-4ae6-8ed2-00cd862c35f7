// Service Worker Registration and Management
// Provides progressive enhancement features for the IEPA Email Center

import React from 'react';

interface ServiceWorkerConfig {
  onUpdate?: (registration: ServiceWorkerRegistration) => void;
  onSuccess?: (registration: ServiceWorkerRegistration) => void;
  onError?: (error: Error) => void;
}

// Check if service workers are supported
export function isServiceWorkerSupported(): boolean {
  return 'serviceWorker' in navigator;
}

// Register service worker
export async function registerServiceWorker(config: ServiceWorkerConfig = {}): Promise<ServiceWorkerRegistration | null> {
  if (!isServiceWorkerSupported()) {
    console.log('[SW] Service workers not supported');
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
    });

    console.log('[SW] Service worker registered successfully');

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (!newWorker) return;

      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed') {
          if (navigator.serviceWorker.controller) {
            // New content is available
            console.log('[SW] New content available');
            config.onUpdate?.(registration);
          } else {
            // Content is cached for offline use
            console.log('[SW] Content cached for offline use');
            config.onSuccess?.(registration);
          }
        }
      });
    });

    return registration;
  } catch (error) {
    console.error('[SW] Service worker registration failed:', error);
    config.onError?.(error as Error);
    return null;
  }
}

// Unregister service worker
export async function unregisterServiceWorker(): Promise<boolean> {
  if (!isServiceWorkerSupported()) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      console.log('[SW] Service worker unregistered:', result);
      return result;
    }
    return false;
  } catch (error) {
    console.error('[SW] Service worker unregistration failed:', error);
    return false;
  }
}

// Check if app is running offline
export function isOffline(): boolean {
  return !navigator.onLine;
}

// Listen for online/offline events
export function addNetworkListeners(
  onOnline?: () => void,
  onOffline?: () => void
): () => void {
  const handleOnline = () => {
    console.log('[SW] App is online');
    onOnline?.();
  };

  const handleOffline = () => {
    console.log('[SW] App is offline');
    onOffline?.();
  };

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  // Return cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
}

// Request persistent storage
export async function requestPersistentStorage(): Promise<boolean> {
  if ('storage' in navigator && 'persist' in navigator.storage) {
    try {
      const persistent = await navigator.storage.persist();
      console.log('[SW] Persistent storage:', persistent);
      return persistent;
    } catch (error) {
      console.error('[SW] Failed to request persistent storage:', error);
      return false;
    }
  }
  return false;
}

// Get storage usage
export async function getStorageUsage(): Promise<{ used: number; quota: number } | null> {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    try {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        quota: estimate.quota || 0,
      };
    } catch (error) {
      console.error('[SW] Failed to get storage usage:', error);
      return null;
    }
  }
  return null;
}

// Background sync for failed operations
export async function scheduleBackgroundSync(tag: string): Promise<void> {
  if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
    try {
      const registration = await navigator.serviceWorker.ready;
      // @ts-ignore - sync API is experimental
      await registration.sync?.register(tag);
      console.log('[SW] Background sync scheduled:', tag);
    } catch (error) {
      console.error('[SW] Failed to schedule background sync:', error);
    }
  }
}

// Periodic background sync (experimental)
export async function schedulePeriodicSync(tag: string, minInterval: number): Promise<void> {
  if ('serviceWorker' in navigator && 'periodicSync' in window.ServiceWorkerRegistration.prototype) {
    try {
      const registration = await navigator.serviceWorker.ready;
      // @ts-ignore - periodicSync is experimental
      await registration.periodicSync.register(tag, {
        minInterval,
      });
      console.log('[SW] Periodic sync scheduled:', tag);
    } catch (error) {
      console.error('[SW] Failed to schedule periodic sync:', error);
    }
  }
}

// Show update available notification
export function showUpdateNotification(onUpdate: () => void): void {
  // This would typically show a toast or modal
  // For now, we'll use a simple confirm dialog
  const shouldUpdate = confirm(
    'A new version of the application is available. Would you like to update now?'
  );
  
  if (shouldUpdate) {
    onUpdate();
  }
}

// Update service worker
export async function updateServiceWorker(): Promise<void> {
  if (!isServiceWorkerSupported()) {
    return;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      await registration.update();
      console.log('[SW] Service worker update triggered');
      
      // Skip waiting and activate new service worker
      if (registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      }
    }
  } catch (error) {
    console.error('[SW] Failed to update service worker:', error);
  }
}

// Skip waiting and activate new service worker
export function skipWaiting(): void {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      window.location.reload();
    });

    navigator.serviceWorker.ready.then((registration) => {
      if (registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      }
    });
  }
}

// Check for updates manually
export async function checkForUpdates(): Promise<boolean> {
  if (!isServiceWorkerSupported()) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      await registration.update();
      return registration.waiting !== null;
    }
    return false;
  } catch (error) {
    console.error('[SW] Failed to check for updates:', error);
    return false;
  }
}

// React hook for service worker management
export function useServiceWorker(config: ServiceWorkerConfig = {}) {
  const [isSupported] = React.useState(isServiceWorkerSupported());
  const [isRegistered, setIsRegistered] = React.useState(false);
  const [isOfflineMode, setIsOfflineMode] = React.useState(isOffline());
  const [updateAvailable, setUpdateAvailable] = React.useState(false);

  React.useEffect(() => {
    if (isSupported) {
      registerServiceWorker({
        ...config,
        onUpdate: (registration) => {
          setUpdateAvailable(true);
          config.onUpdate?.(registration);
        },
        onSuccess: (registration) => {
          setIsRegistered(true);
          config.onSuccess?.(registration);
        },
      });
    }

    // Listen for network changes
    const cleanup = addNetworkListeners(
      () => setIsOfflineMode(false),
      () => setIsOfflineMode(true)
    );

    return cleanup;
  }, [isSupported]);

  return {
    isSupported,
    isRegistered,
    isOfflineMode,
    updateAvailable,
    updateServiceWorker,
    skipWaiting,
    checkForUpdates,
  };
}
