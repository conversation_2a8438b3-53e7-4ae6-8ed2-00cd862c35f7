#!/usr/bin/env node

/**
 * Webhook Processing Verification Script
 *
 * This script verifies that webhook events are properly processed and
 * database records are created correctly.
 */

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Supabase configuration not found in .env.local');
  process.exit(1);
}

// Initialize Supabase client with service role key for admin access
const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface PaymentRecord {
  id: string;
  registration_id: string;
  stripe_session_id: string;
  stripe_payment_intent_id?: string;
  amount: number;
  currency: string;
  status: string;
  created_at: string;
  updated_at: string;
  metadata?: any;
}

interface RegistrationRecord {
  id: string;
  user_id: string;
  registration_type: string;
  status: string;
  created_at: string;
  updated_at: string;
}

async function checkPaymentRecords(): Promise<void> {
  try {
    console.log('🔍 Checking payment records in database...');

    const { data: payments, error } = await supabase
      .from('iepa_payments')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      console.error('❌ Error fetching payment records:', error);
      return;
    }

    if (!payments || payments.length === 0) {
      console.log('📭 No payment records found in database');
      console.log(
        '   This is expected if no webhook events have been processed yet'
      );
      return;
    }

    console.log(`📊 Found ${payments.length} payment record(s):`);
    console.log('');

    payments.forEach((payment: PaymentRecord, index) => {
      console.log(`${index + 1}. Payment ID: ${payment.id}`);
      console.log(`   Registration ID: ${payment.registration_id}`);
      console.log(`   Stripe Session ID: ${payment.stripe_session_id}`);
      console.log(
        `   Amount: $${(payment.amount / 100).toFixed(2)} ${payment.currency.toUpperCase()}`
      );
      console.log(`   Status: ${payment.status}`);
      console.log(
        `   Created: ${new Date(payment.created_at).toLocaleString()}`
      );
      console.log(
        `   Updated: ${new Date(payment.updated_at).toLocaleString()}`
      );

      if (payment.metadata) {
        console.log(
          `   Metadata: ${JSON.stringify(payment.metadata, null, 2)}`
        );
      }
      console.log('');
    });
  } catch (error) {
    console.error('💥 Error checking payment records:', error);
  }
}

async function checkRegistrationRecords(): Promise<void> {
  try {
    console.log('🔍 Checking registration records...');

    // Check all three registration tables
    const tables = [
      'iepa_attendee_registrations',
      'iepa_speaker_registrations',
      'iepa_sponsor_registrations',
    ];

    for (const tableName of tables) {
      console.log(`\n📋 Checking ${tableName}:`);

      const { data: registrations, error } = await supabase
        .from(tableName)
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) {
        console.error(`❌ Error fetching ${tableName}:`, error.message);
        continue;
      }

      if (!registrations || registrations.length === 0) {
        console.log(`📭 No records found in ${tableName}`);
        continue;
      }

      console.log(
        `📊 Found ${registrations.length} record(s) in ${tableName}:`
      );

      registrations.forEach((registration: any, index) => {
        console.log(`  ${index + 1}. ID: ${registration.id}`);
        console.log(`     User ID: ${registration.user_id}`);
        console.log(
          `     Name: ${registration.full_name || registration.sponsor_name || 'N/A'}`
        );
        console.log(`     Email: ${registration.email || 'N/A'}`);
        console.log(
          `     Payment Status: ${registration.payment_status || 'N/A'}`
        );
        console.log(
          `     Created: ${new Date(registration.created_at).toLocaleString()}`
        );
        console.log('');
      });
    }
  } catch (error) {
    console.error('💥 Error checking registration records:', error);
  }
}

async function checkWebhookLogs(): Promise<void> {
  try {
    console.log('🔍 Checking webhook processing logs...');

    // Check if there's a webhook logs table
    const { data: webhookLogs, error } = await supabase
      .from('iepa_webhook_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (error && error.code === 'PGRST116') {
      console.log('📝 No webhook logs table found (this is optional)');
      return;
    }

    if (error) {
      console.error('❌ Error fetching webhook logs:', error);
      return;
    }

    if (!webhookLogs || webhookLogs.length === 0) {
      console.log('📭 No webhook logs found');
      return;
    }

    console.log(`📊 Found ${webhookLogs.length} webhook log(s):`);
    console.log('');

    webhookLogs.forEach((log: any, index) => {
      console.log(`${index + 1}. Event Type: ${log.event_type}`);
      console.log(`   Event ID: ${log.stripe_event_id}`);
      console.log(`   Status: ${log.processing_status}`);
      console.log(`   Created: ${new Date(log.created_at).toLocaleString()}`);
      console.log('');
    });
  } catch (error) {
    console.error('💥 Error checking webhook logs:', error);
  }
}

async function verifyDatabaseSchema(): Promise<void> {
  try {
    console.log('🔍 Verifying database schema...');

    // Check if required tables exist
    const tables = [
      'iepa_payments',
      'iepa_attendee_registrations',
      'iepa_speaker_registrations',
      'iepa_sponsor_registrations',
    ];

    for (const table of tables) {
      const { data, error } = await supabase.from(table).select('*').limit(1);

      if (error && error.code === 'PGRST116') {
        console.log(`❌ Table '${table}' does not exist`);
      } else if (error) {
        console.log(`⚠️  Error accessing table '${table}': ${error.message}`);
      } else {
        console.log(`✅ Table '${table}' exists and is accessible`);
      }
    }
  } catch (error) {
    console.error('💥 Error verifying database schema:', error);
  }
}

async function main() {
  console.log(
    '🔍 IEPA Conference Registration - Webhook Processing Verification'
  );
  console.log(
    '================================================================'
  );
  console.log('');

  // Verify database schema
  await verifyDatabaseSchema();
  console.log('');

  // Check payment records
  await checkPaymentRecords();

  // Check registration records
  await checkRegistrationRecords();

  // Check webhook logs (if available)
  await checkWebhookLogs();

  console.log('📋 Webhook Testing Instructions:');
  console.log('');
  console.log('1. **Start Development Server**:');
  console.log('   npm run dev');
  console.log('');
  console.log('2. **Start Webhook Forwarding** (in new terminal):');
  console.log('   ./scripts/setup-development-webhooks.sh');
  console.log('');
  console.log('3. **Create Test Payment**:');
  console.log('   - Visit: http://localhost:3000/test-stripe');
  console.log('   - Click "Test Payment Flow"');
  console.log('   - Complete payment with test card: ****************');
  console.log('');
  console.log('4. **Verify Processing**:');
  console.log('   - Run this script again to check database records');
  console.log('   - Check webhook forwarding terminal for events');
  console.log('   - Monitor server logs for webhook processing');
  console.log('');
  console.log('5. **Test Specific Events**:');
  console.log('   stripe trigger checkout.session.completed');
  console.log('   stripe trigger payment_intent.succeeded');
  console.log('   stripe trigger payment_intent.payment_failed');
  console.log('');
  console.log('🎯 Expected Results:');
  console.log('- Payment records created in iepa_payments table');
  console.log('- Registration status updated to "completed"');
  console.log('- Webhook events logged (if logging is implemented)');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { main as verifyWebhookProcessing };
