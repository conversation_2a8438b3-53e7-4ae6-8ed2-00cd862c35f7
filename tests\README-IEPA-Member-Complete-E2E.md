# IEPA Member Registration Complete E2E Test

## Overview

This comprehensive end-to-end test simulates a complete IEPA member registration flow, including:

1. **New User Registration** - Creates a unique test user
2. **Complete Attendee Registration Form** - Fills all required fields
3. **Golf Tournament Selection** - Adds golf tournament and club rental
4. **Promo Code Application** - Applies 'TEST' code for 100% discount
5. **Stripe Payment Completion** - Handles Stripe checkout (even for $0 payments)
6. **Email Verification** - Confirms welcome email was sent
7. **Registration Verification** - Checks my-registrations page

## Test Configuration

### Test User Data
- **Name**: <PERSON>ember
- **Email**: `john.testmember.{timestamp}@iepa-test.com` (unique per test run)
- **Organization**: Test Energy Solutions
- **Registration Type**: IEPA Member
- **Golf**: Tournament + Club Rental (right-handed)
- **Promo Code**: TEST (100% discount)

### Key Features
- Uses data-testid selectors where available
- Realistic delays for form interactions
- Comprehensive error handling and debugging
- Modular helper class for easy extension
- Full screenshot documentation
- Handles both Stripe checkout and $0 payment flows

## Running the Test

### Prerequisites
1. Development server running on port 6969:
   ```bash
   npm run dev
   ```

2. Stripe test keys configured in `.env.local`:
   ```
   STRIPE_PUBLISHABLE_KEY=pk_test_...
   STRIPE_SECRET_KEY=sk_test_...
   ```

3. SendGrid configured for email testing

### Run Commands

```bash
# Run the complete test
npx playwright test tests/iepa-member-registration-complete-e2e.spec.js

# Run with browser visible (headed mode)
npx playwright test tests/iepa-member-registration-complete-e2e.spec.js --headed

# Run with Playwright inspector for debugging
npx playwright test tests/iepa-member-registration-complete-e2e.spec.js --debug

# Run only the main registration test
npx playwright test tests/iepa-member-registration-complete-e2e.spec.js -g "should complete full IEPA member registration"

# Run only the configuration verification test
npx playwright test tests/iepa-member-registration-complete-e2e.spec.js -g "should verify test data and configuration"
```

## Test Steps Detail

### 1. Registration Page Navigation
- Navigates to `/register/attendee`
- Waits for page to load completely
- Takes screenshot: `01-registration-page.png`

### 2. Registration Type Selection
- Selects "IEPA Member" option
- Proceeds to next step
- Takes screenshot: `02-registration-type-selected.png`

### 3. Personal Information
- Fills: First Name, Last Name, Name on Badge
- Fills: Organization, Job Title
- Takes screenshot: `03-personal-information.png`

### 4. Contact Information
- Fills: Phone Number, Street Address, City
- Selects: State (California)
- Fills: ZIP Code
- Takes screenshot: `04-contact-information.png`

### 5. Event Options & Golf
- Selects golf tournament checkbox
- Selects golf club rental checkbox
- Chooses club handedness (right-handed)
- Takes screenshot: `05-event-options-golf.png`

### 6. Emergency Contact
- Fills emergency contact information
- Takes screenshot: `06-emergency-contact.png`

### 7. Promo Code Application
- Clicks "Have a discount code?"
- Enters 'TEST' promo code
- Applies code and verifies $0 total
- Takes screenshot: `07-promo-code-applied.png`

### 8. Stripe Payment
- Handles Stripe checkout form
- Uses test card: ****************
- Completes payment process
- Takes screenshot: `08-payment-completed.png`

### 9. Email Verification
- Waits for email processing
- Logs expected email recipient

### 10. My-Registrations Verification
- Navigates to `/my-registrations`
- Verifies registration appears
- Checks golf tournament and club rental
- Verifies payment status
- Takes screenshot: `09-my-registrations.png`

### 11. Conference Page Access
- Navigates to `/conference`
- Verifies page accessibility
- Takes screenshot: `10-conference-page.png`

## Error Handling

The test includes comprehensive error handling:

- **Alternative Selectors**: Multiple selector strategies for form elements
- **Graceful Degradation**: Continues test even if some steps fail
- **Debug Information**: Logs current page URL, title, and error states
- **Screenshot on Error**: Captures error state for debugging
- **Timeout Management**: Appropriate timeouts for different operations

## Extending the Test

### Adding New Steps
1. Add method to `IEPARegistrationHelpers` class
2. Call method in main test flow
3. Add screenshot capture
4. Update documentation

### Testing Different Scenarios
- Modify `TEST_CONFIG` object for different user data
- Change `promoCode` to test different discounts
- Adjust `golfClubHandedness` for left-handed testing
- Update `registration.type` for different member types

### Debugging Tips
- Use `--headed` flag to see browser actions
- Use `--debug` flag for step-by-step debugging
- Check `test-results/` folder for screenshots
- Review console output for detailed step information

## Expected Outcomes

### Success Indicators
- ✅ All form steps completed without errors
- ✅ Golf tournament and club rental selected
- ✅ Promo code applied successfully ($0 total)
- ✅ Stripe payment completed (even for $0)
- ✅ Registration appears in my-registrations
- ✅ All screenshots captured successfully

### Common Issues
- **Form Validation**: Ensure all required fields are filled
- **Stripe Integration**: Verify test keys are configured
- **Email Service**: Check SendGrid configuration
- **Database**: Ensure Supabase connection is working
- **Promo Code**: Verify 'TEST' code exists and provides 100% discount

## File Structure

```
tests/
├── iepa-member-registration-complete-e2e.spec.js  # Main test file
├── README-IEPA-Member-Complete-E2E.md             # This documentation
└── test-results/                                  # Generated screenshots
    ├── iepa-member-complete-01-registration-page.png
    ├── iepa-member-complete-02-registration-type-selected.png
    ├── iepa-member-complete-03-personal-information.png
    ├── iepa-member-complete-04-contact-information.png
    ├── iepa-member-complete-05-event-options-golf.png
    ├── iepa-member-complete-06-emergency-contact.png
    ├── iepa-member-complete-07-promo-code-applied.png
    ├── iepa-member-complete-08-payment-completed.png
    ├── iepa-member-complete-09-my-registrations.png
    └── iepa-member-complete-10-conference-page.png
```

## Support

For issues or questions about this test:
1. Check the console output for detailed error messages
2. Review the generated screenshots in `test-results/`
3. Verify all prerequisites are met
4. Check that the development server is running on port 6969
5. Ensure Stripe test keys and SendGrid are properly configured
