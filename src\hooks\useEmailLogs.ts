'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  EmailLog,
  EmailStats,
  EmailLogFilters,
  getDefaultFilters,
  buildEmailLogQuery,
} from '@/lib/email-log-utils';

interface EmailLogsResponse {
  success: boolean;
  logs: EmailLog[];
  statistics: EmailStats;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
  timestamp: string;
}

interface UseEmailLogsReturn {
  // Data
  emailLogs: EmailLog[];
  emailStats: EmailStats;
  pagination: EmailLogsResponse['pagination'];
  
  // State
  loading: boolean;
  error: string | null;
  
  // Filters
  filters: EmailLogFilters;
  setFilters: (filters: Partial<EmailLogFilters>) => void;
  resetFilters: () => void;
  
  // Actions
  fetchEmailLogs: () => Promise<void>;
  refreshEmailLogs: () => Promise<void>;
  deleteEmailLog: (logId: string) => Promise<boolean>;
  
  // Utilities
  hasActiveFilters: boolean;
  totalResults: number;
}

export function useEmailLogs(): UseEmailLogsReturn {
  const [emailLogs, setEmailLogs] = useState<EmailLog[]>([]);
  const [emailStats, setEmailStats] = useState<EmailStats>({
    totalSent: 0,
    totalFailed: 0,
    totalPending: 0,
    totalEmails: 0,
  });
  const [pagination, setPagination] = useState<EmailLogsResponse['pagination']>({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFiltersState] = useState<EmailLogFilters>(getDefaultFilters());

  const fetchEmailLogs = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = buildEmailLogQuery(filters);
      const response = await fetch(`/api/admin/email-logs?${queryParams}`);
      const data: EmailLogsResponse = await response.json();

      if (data.success) {
        setEmailLogs(data.logs);
        setEmailStats(data.statistics);
        setPagination(data.pagination);
      } else {
        setError(data.error || 'Failed to fetch email logs');
        setEmailLogs([]);
        setEmailStats({
          totalSent: 0,
          totalFailed: 0,
          totalPending: 0,
          totalEmails: 0,
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch email logs';
      setError(errorMessage);
      setEmailLogs([]);
      setEmailStats({
        totalSent: 0,
        totalFailed: 0,
        totalPending: 0,
        totalEmails: 0,
      });
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const refreshEmailLogs = useCallback(async () => {
    await fetchEmailLogs();
  }, [fetchEmailLogs]);

  const deleteEmailLog = useCallback(async (logId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/admin/email-logs?id=${logId}`, {
        method: 'DELETE',
      });
      const data = await response.json();

      if (data.success) {
        // Remove the deleted log from the current list
        setEmailLogs(prev => prev.filter(log => log.id !== logId));
        // Refresh to get updated statistics
        await refreshEmailLogs();
        return true;
      } else {
        setError(data.error || 'Failed to delete email log');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete email log';
      setError(errorMessage);
      return false;
    }
  }, [refreshEmailLogs]);

  const setFilters = useCallback((newFilters: Partial<EmailLogFilters>) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters,
      // Reset to page 1 when filters change (except when changing page)
      page: newFilters.page !== undefined ? newFilters.page : 1,
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState(getDefaultFilters());
  }, []);

  // Fetch email logs when filters change
  useEffect(() => {
    fetchEmailLogs();
  }, [fetchEmailLogs]);

  // Computed values
  const hasActiveFilters = 
    filters.status !== 'all' || 
    filters.emailType !== 'all' || 
    filters.search.trim() !== '';

  const totalResults = pagination.total;

  return {
    // Data
    emailLogs,
    emailStats,
    pagination,
    
    // State
    loading,
    error,
    
    // Filters
    filters,
    setFilters,
    resetFilters,
    
    // Actions
    fetchEmailLogs,
    refreshEmailLogs,
    deleteEmailLog,
    
    // Utilities
    hasActiveFilters,
    totalResults,
  };
}

// Hook for sending emails
interface UseSendEmailReturn {
  sendEmail: (emailData: any) => Promise<void>;
  sending: boolean;
  sendError: string | null;
}

export function useSendEmail(): UseSendEmailReturn {
  const [sending, setSending] = useState(false);
  const [sendError, setSendError] = useState<string | null>(null);

  const sendEmail = useCallback(async (emailData: any) => {
    setSending(true);
    setSendError(null);

    try {
      const response = await fetch('/api/admin/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to send email');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send email';
      setSendError(errorMessage);
      throw err;
    } finally {
      setSending(false);
    }
  }, []);

  return {
    sendEmail,
    sending,
    sendError,
  };
}

// Hook for clipboard operations
interface UseClipboardReturn {
  copyToClipboard: (text: string) => Promise<boolean>;
  copied: boolean;
  copyError: string | null;
}

export function useClipboard(): UseClipboardReturn {
  const [copied, setCopied] = useState(false);
  const [copyError, setCopyError] = useState<string | null>(null);

  const copyToClipboard = useCallback(async (text: string): Promise<boolean> => {
    setCopyError(null);
    
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to copy to clipboard';
      setCopyError(errorMessage);
      return false;
    }
  }, []);

  return {
    copyToClipboard,
    copied,
    copyError,
  };
}
