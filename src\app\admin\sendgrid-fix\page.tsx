'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ExternalLink,
  AlertTriangle,
  <PERSON><PERSON><PERSON>cle,
  Settings,
} from 'lucide-react';

export default function SendGridFixPage() {
  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="iepa-heading-1 mb-4">
              🔧 SendGrid Click Tracking Fix
            </h1>
            <p className="iepa-body text-gray-600">
              Resolve magic link authentication issues caused by SendGrid click
              tracking
            </p>
          </div>

          {/* Problem Alert */}
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>Issue:</strong> Magic links fail with &quot;Missing
              authentication code&quot; because SendGrid&apos;s click tracking
              wraps URLs with tracking redirects that strip authentication
              parameters.
            </AlertDescription>
          </Alert>

          {/* Quick Fix */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Immediate Fix Required
                <Badge variant="destructive">URGENT</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-900 mb-2">
                    Step 1: Access SendGrid Dashboard
                  </h3>
                  <p className="text-blue-800 mb-3">
                    Login to your SendGrid account and navigate to tracking
                    settings
                  </p>
                  <Button asChild>
                    <a
                      href="https://app.sendgrid.com/settings/tracking"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2"
                    >
                      Open SendGrid Tracking Settings
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-green-900 mb-2">
                    Step 2: Disable Click Tracking
                  </h3>
                  <div className="text-green-800 space-y-2">
                    <p>In the SendGrid Tracking settings:</p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>
                        <strong>Turn OFF</strong> &quot;Click Tracking&quot;
                        globally
                      </li>
                      <li>
                        <strong>Keep ON</strong> &quot;Open Tracking&quot; for
                        analytics
                      </li>
                      <li>
                        <strong>Save</strong> the settings
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-purple-900 mb-2">
                    Step 3: Test Magic Links
                  </h3>
                  <p className="text-purple-800 mb-3">
                    After disabling click tracking, test the magic link
                    authentication
                  </p>
                  <Button asChild variant="outline">
                    <a
                      href="/auth/login"
                      className="inline-flex items-center gap-2"
                    >
                      Test Magic Link Login
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Verification */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Verification Steps
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center text-sm font-semibold">
                    ✓
                  </div>
                  <div>
                    <p className="font-medium">
                      Magic link URLs should be direct
                    </p>
                    <p className="text-sm text-gray-600">
                      URLs should look like:{' '}
                      <code className="bg-gray-100 px-1 rounded">
                        https://iepa.vercel.app/auth/callback?code=abc123...
                      </code>
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-red-100 text-red-600 flex items-center justify-center text-sm font-semibold">
                    ✗
                  </div>
                  <div>
                    <p className="font-medium">NOT SendGrid tracking URLs</p>
                    <p className="text-sm text-gray-600">
                      Avoid URLs like:{' '}
                      <code className="bg-gray-100 px-1 rounded">
                        https://u11949574.ct.sendgrid.net/ls/click?upn=...
                      </code>
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Alternative Solutions */}
          <Card>
            <CardHeader>
              <CardTitle>Alternative Solutions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h3 className="font-semibold mb-2">
                    Option 1: Switch to Resend (Recommended)
                  </h3>
                  <p className="text-gray-600 mb-2">
                    Use Resend.com for authentication emails - no click tracking
                    interference
                  </p>
                  <Button asChild variant="outline" size="sm">
                    <a
                      href="https://resend.com"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Learn about Resend
                    </a>
                  </Button>
                </div>

                <div className="border-l-4 border-yellow-500 pl-4">
                  <h3 className="font-semibold mb-2">
                    Option 2: Configure Supabase SMTP Headers
                  </h3>
                  <p className="text-gray-600 mb-2">
                    Add custom SMTP headers to disable tracking per email
                  </p>
                  <code className="bg-gray-100 p-2 rounded block text-sm">
                    X-SMTPAPI:{' '}
                    {`{"filters":{"clicktrack":{"settings":{"enable":0}}}}`}
                  </code>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h3 className="font-semibold mb-2">
                    Option 3: Use Amazon SES
                  </h3>
                  <p className="text-gray-600">
                    Switch to Amazon SES which doesn&apos;t interfere with
                    authentication URLs
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Support */}
          <div className="mt-8 text-center">
            <p className="text-gray-600 mb-4">
              Need help? Check the configuration diagnostic tool:
            </p>
            <Button asChild variant="outline">
              <a href="/api/admin/check-supabase-config" target="_blank">
                Check Configuration
              </a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
