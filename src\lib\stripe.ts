// Stripe Configuration for IEPA Conference Registration
// Centralized Stripe client setup and configuration

import Strip<PERSON> from 'stripe';

// Environment variable validation
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

if (!stripeSecretKey) {
  throw new Error(
    'Missing STRIPE_SECRET_KEY environment variable. Please check your .env.local file.'
  );
}

if (!stripePublishableKey) {
  throw new Error(
    'Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable. Please check your .env.local file.'
  );
}

// Validate that we're using test keys in development
if (process.env.NODE_ENV === 'development') {
  if (!stripeSecretKey.startsWith('sk_test_')) {
    console.warn(
      '⚠️  Warning: Using production Stripe secret key in development environment'
    );
  }
  if (!stripePublishableKey.startsWith('pk_test_')) {
    console.warn(
      '⚠️  Warning: Using production Stripe publishable key in development environment'
    );
  }
}

// Initialize Stripe client (server-side)
export const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-05-28.basil', // Use latest API version
  typescript: true,
});

// Stripe configuration constants
export const STRIPE_CONFIG = {
  publishableKey: stripePublishableKey,
  webhookSecret: stripeWebhookSecret,
  currency: 'usd',

  // Payment method types to accept
  paymentMethodTypes: [
    'card',
  ] as Stripe.Checkout.SessionCreateParams.PaymentMethodType[],

  // Checkout session configuration
  checkoutDefaults: {
    mode: 'payment' as const,
    allow_promotion_codes: true,
    billing_address_collection: 'required' as const,
    // Phone number collection removed to avoid country validation issues
    // Phone numbers are collected in the registration forms instead
    customer_creation: 'always' as const,
  },

  // Success and cancel URLs (will be dynamically set)
  urls: {
    success: '/payment/success',
    cancel: '/payment/cancel',
  },
} as const;

// Stripe webhook event types we handle
export const STRIPE_WEBHOOK_EVENTS = {
  CHECKOUT_SESSION_COMPLETED: 'checkout.session.completed',
  PAYMENT_INTENT_SUCCEEDED: 'payment_intent.succeeded',
  PAYMENT_INTENT_PAYMENT_FAILED: 'payment_intent.payment_failed',
  INVOICE_PAYMENT_SUCCEEDED: 'invoice.payment_succeeded',
  INVOICE_PAYMENT_FAILED: 'invoice.payment_failed',
} as const;

// Helper function to get dynamic URLs
export const getStripeUrls = (baseUrl: string) => ({
  success: `${baseUrl}/payment/success`,
  cancel: `${baseUrl}/payment/cancel`,
});

// Utility function to format amount for Stripe (cents)
export const formatAmountForStripe = (amount: number): number => {
  return Math.round(amount * 100); // Convert dollars to cents
};

// Utility function to format amount from Stripe (dollars)
export const formatAmountFromStripe = (amount: number): number => {
  return amount / 100; // Convert cents to dollars
};

// Validate Stripe configuration
export const validateStripeConfig = (): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!stripeSecretKey) {
    errors.push('Missing STRIPE_SECRET_KEY');
  } else if (!stripeSecretKey.startsWith('sk_')) {
    errors.push('Invalid STRIPE_SECRET_KEY format');
  }

  if (!stripePublishableKey) {
    errors.push('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY');
  } else if (!stripePublishableKey.startsWith('pk_')) {
    errors.push('Invalid NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY format');
  }

  // Check if keys match (same account)
  // Note: For test keys, we'll skip this validation as the key format
  // can vary and both keys being test keys from the same account is sufficient
  if (stripeSecretKey && stripePublishableKey) {
    const isSecretTest = stripeSecretKey.startsWith('sk_test_');
    const isPublishableTest = stripePublishableKey.startsWith('pk_test_');
    const isSecretLive = stripeSecretKey.startsWith('sk_live_');
    const isPublishableLive = stripePublishableKey.startsWith('pk_live_');

    // Ensure both keys are of the same type (both test or both live)
    if (
      (isSecretTest && !isPublishableTest) ||
      (isSecretLive && !isPublishableLive)
    ) {
      errors.push(
        'Stripe secret and publishable keys must both be test keys or both be live keys'
      );
    }

    // For live keys, we can do more strict validation if needed
    // For test keys, having matching prefixes is sufficient for most use cases
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Test Stripe connection
export const testStripeConnection = async (): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Simple API call to test connection
    await stripe.balance.retrieve();
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Export types for use in other files
export type StripeCheckoutSession = Stripe.Checkout.Session;
export type StripePaymentIntent = Stripe.PaymentIntent;
export type StripeWebhookEvent = Stripe.Event;
