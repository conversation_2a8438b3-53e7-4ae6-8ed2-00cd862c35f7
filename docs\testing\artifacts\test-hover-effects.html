<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IEPA Navigation Hover Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --iepa-primary-blue: #396DA4;
            --iepa-primary-blue-light: #4d7fb5;
            --iepa-primary-blue-dark: #2d5a8a;
            --iepa-gray-100: #e9ecef;
        }

        /* Desktop Navigation Menu Items - High Contrast Hover Effects */
        .desktop-register-link:hover,
        .nav-main-link:hover {
            background-color: #396DA4 !important;
            color: white !important;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
            transform: scale(1.02) !important;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'iepa-primary': {
                            DEFAULT: 'var(--iepa-primary-blue)',
                            light: 'var(--iepa-primary-blue-light)',
                            dark: 'var(--iepa-primary-blue-dark)',
                        },
                        'iepa-gray': {
                            100: 'var(--iepa-gray-100)',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-iepa-primary">IEPA Navigation Hover Effects Test</h1>
        
        <!-- Main Navigation Items Test -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Main Navigation Items</h2>
            <div class="bg-gray-50 border border-gray-200 rounded-md p-4 w-80">
                <div class="flex gap-4 justify-center">
                    <a href="#" class="nav-main-link text-iepa-primary transition-all duration-200 font-medium bg-transparent inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm h-10 px-4 py-2">Annual Meeting Info</a>
                    <a href="#" class="nav-main-link text-iepa-primary transition-all duration-200 font-medium bg-transparent inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm h-10 px-4 py-2">Register</a>
                    <a href="#" class="nav-main-link text-iepa-primary transition-all duration-200 font-medium bg-transparent inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm h-10 px-4 py-2">Contact</a>
                </div>
            </div>
        </div>

        <!-- Desktop Dropdown Menu Items Test -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Register Dropdown Menu Items</h2>
            <div class="bg-white border border-gray-200 shadow-lg rounded-md p-4 w-80">
                <div class="grid gap-3">
                    <a href="#" class="desktop-register-link block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-all duration-200">
                        <div class="text-sm font-medium leading-none">Register - Attendee</div>
                    </a>
                    <a href="#" class="desktop-register-link block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-all duration-200">
                        <div class="text-sm font-medium leading-none">Register - Speaker</div>
                    </a>
                    <a href="#" class="desktop-register-link block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-all duration-200">
                        <div class="text-sm font-medium leading-none">Register - Sponsor</div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Mobile Menu Items Test -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Mobile Menu Items</h2>
            <div class="bg-iepa-primary p-4 rounded-md w-80">
                <div class="space-y-2">
                    <a href="#" class="block text-white hover:text-white hover:bg-white/20 hover:shadow-md text-sm py-2 px-3 rounded-md transition-all duration-200">Register - Attendee</a>
                    <a href="#" class="block text-white hover:text-white hover:bg-white/20 hover:shadow-md text-sm py-2 px-3 rounded-md transition-all duration-200">Register - Speaker</a>
                    <a href="#" class="block text-white hover:text-white hover:bg-white/20 hover:shadow-md text-sm py-2 px-3 rounded-md transition-all duration-200">Register - Sponsor</a>
                </div>
            </div>
        </div>

        <div class="text-sm text-gray-600">
            <p><strong>Instructions:</strong> Hover over the menu items above to test the hover effects.</p>
            <ul class="mt-2 space-y-1">
                <li>• <strong>Main Navigation:</strong> All nav items (Annual Meeting Info, Register, Contact) have consistent hover effects</li>
                <li>• <strong>Dropdown items:</strong> Register submenu items have the same strong hover feedback</li>
                <li>• <strong>Mobile items:</strong> Semi-transparent white background with shadow</li>
                <li>• <strong>Effects:</strong> Full IEPA blue background, white text, shadow, and 2% scale increase</li>
                <li>• <strong>Transitions:</strong> Smooth 200ms duration for professional feel</li>
                <li>• <strong>Accessibility:</strong> High contrast ratios (white on dark blue) for excellent visibility</li>
            </ul>
        </div>
    </div>
</body>
</html>
