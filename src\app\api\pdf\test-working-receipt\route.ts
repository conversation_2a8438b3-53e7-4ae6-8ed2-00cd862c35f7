// Working PDF Receipt Generation
// POST /api/pdf/test-working-receipt

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('Starting working receipt PDF generation...');

    const body = await request.json();
    const { registrationId, registrationType } = body;

    // Import React-PDF
    const { renderToBuffer, Document, Page, Text, View } = await import(
      '@react-pdf/renderer'
    );
    const React = await import('react');
    console.log('✅ React-PDF imported successfully');

    // IEPA Official Brand Colors
    const colors = {
      primaryBlue: '#396DA4',
      secondaryGreen: '#5EAE50',
      gray800: '#212529',
      gray600: '#495057',
      white: '#ffffff',
    };

    // Sample data
    const sampleData = {
      receiptNumber: 'IEPA-RCP-2025-12345678',
      issueDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      customerOrg: 'Sample Energy Company',
      customerAddress: '123 Main Street, Sacramento, CA 95814',
      items: [
        {
          description: 'Member Registration',
          quantity: 1,
          unitPrice: 450,
          total: 450,
        },
        {
          description: 'Golf Tournament',
          quantity: 1,
          unitPrice: 200,
          total: 200,
        },
        {
          description: 'Golf Club Rental (Right Handed)',
          quantity: 1,
          unitPrice: 75,
          total: 75,
        },
      ],
      subtotal: 725,
      tax: 0,
      total: 725,
      paymentMethod: 'Credit Card',
      transactionId: 'txn_test_123456',
    };

    // Create PDF Document using React.createElement
    const ReceiptDocument = React.createElement(
      Document,
      {},
      React.createElement(
        Page,
        {
          size: 'LETTER',
          style: {
            padding: 72,
            fontFamily: 'Helvetica',
            fontSize: 11,
            color: colors.gray800,
          },
        },
        // Header Section
        React.createElement(
          View,
          {
            style: {
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginBottom: 30,
              paddingBottom: 20,
              borderBottomWidth: 2,
              borderBottomColor: colors.primaryBlue,
            },
          },
          // Company Info
          React.createElement(
            View,
            { style: { flex: 1 } },
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 18,
                  fontFamily: 'Helvetica-Bold',
                  color: colors.primaryBlue,
                  marginBottom: 5,
                },
              },
              'IEPA'
            ),
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 12,
                  color: colors.gray600,
                  marginBottom: 10,
                },
              },
              'Independent Energy Producers Association'
            ),
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 10,
                  color: colors.gray600,
                  lineHeight: 1.4,
                },
              },
              '1215 K Street, Suite 900\nSacramento, CA 95814\n(916) 448-9499\<EMAIL>'
            )
          ),
          // Receipt Info
          React.createElement(
            View,
            { style: { flex: 1, alignItems: 'flex-end' } },
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 24,
                  fontFamily: 'Helvetica-Bold',
                  color: colors.primaryBlue,
                  marginBottom: 10,
                },
              },
              'RECEIPT'
            ),
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 14,
                  fontFamily: 'Helvetica-Bold',
                  marginBottom: 5,
                },
              },
              `Receipt #: ${sampleData.receiptNumber}`
            ),
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 11,
                  color: colors.gray600,
                },
              },
              `Date: ${sampleData.issueDate}`
            )
          )
        ),

        // Conference Info
        React.createElement(
          View,
          {
            style: {
              backgroundColor: '#f8f9fa',
              padding: 15,
              marginBottom: 20,
              borderRadius: 5,
            },
          },
          React.createElement(
            Text,
            {
              style: {
                fontSize: 16,
                fontFamily: 'Helvetica-Bold',
                color: colors.primaryBlue,
                marginBottom: 5,
              },
            },
            'IEPA 2025 Annual Meeting'
          ),
          React.createElement(
            Text,
            {
              style: {
                fontSize: 12,
                color: colors.gray800,
                marginBottom: 3,
              },
            },
            'September 15-17, 2025'
          ),
          React.createElement(
            Text,
            {
              style: {
                fontSize: 11,
                color: colors.gray600,
              },
            },
            'TBD Conference Center, TBD City, CA'
          )
        ),

        // Customer Info
        React.createElement(
          View,
          { style: { marginBottom: 25 } },
          React.createElement(
            Text,
            {
              style: {
                fontSize: 14,
                fontFamily: 'Helvetica-Bold',
                color: colors.primaryBlue,
                marginBottom: 10,
                paddingBottom: 5,
                borderBottomWidth: 1,
                borderBottomColor: '#dee2e6',
              },
            },
            'Registration Details'
          ),
          React.createElement(
            View,
            { style: { flexDirection: 'row' } },
            React.createElement(
              View,
              { style: { flex: 1, marginRight: 20 } },
              React.createElement(
                Text,
                {
                  style: {
                    fontSize: 13,
                    fontFamily: 'Helvetica-Bold',
                    marginBottom: 5,
                  },
                },
                sampleData.customerName
              ),
              React.createElement(
                Text,
                {
                  style: {
                    fontSize: 11,
                    color: colors.gray600,
                    marginBottom: 3,
                  },
                },
                `Email: ${sampleData.customerEmail}`
              ),
              React.createElement(
                Text,
                {
                  style: {
                    fontSize: 11,
                    color: colors.gray600,
                    marginBottom: 3,
                  },
                },
                `Organization: ${sampleData.customerOrg}`
              )
            ),
            React.createElement(
              View,
              { style: { flex: 1 } },
              React.createElement(
                Text,
                {
                  style: {
                    fontSize: 11,
                    color: colors.gray600,
                  },
                },
                `Address:\n${sampleData.customerAddress}`
              )
            )
          )
        ),

        // Items Table
        React.createElement(
          View,
          { style: { marginBottom: 25 } },
          React.createElement(
            Text,
            {
              style: {
                fontSize: 14,
                fontFamily: 'Helvetica-Bold',
                color: colors.primaryBlue,
                marginBottom: 10,
              },
            },
            'Items'
          ),

          // Table Header
          React.createElement(
            View,
            {
              style: {
                flexDirection: 'row',
                backgroundColor: colors.primaryBlue,
                padding: 10,
                marginBottom: 1,
              },
            },
            React.createElement(
              Text,
              {
                style: {
                  flex: 3,
                  fontSize: 11,
                  fontFamily: 'Helvetica-Bold',
                  color: colors.white,
                },
              },
              'Description'
            ),
            React.createElement(
              Text,
              {
                style: {
                  flex: 1,
                  fontSize: 11,
                  fontFamily: 'Helvetica-Bold',
                  color: colors.white,
                  textAlign: 'center',
                },
              },
              'Qty'
            ),
            React.createElement(
              Text,
              {
                style: {
                  flex: 1,
                  fontSize: 11,
                  fontFamily: 'Helvetica-Bold',
                  color: colors.white,
                  textAlign: 'right',
                },
              },
              'Unit Price'
            ),
            React.createElement(
              Text,
              {
                style: {
                  flex: 1,
                  fontSize: 11,
                  fontFamily: 'Helvetica-Bold',
                  color: colors.white,
                  textAlign: 'right',
                },
              },
              'Total'
            )
          ),

          // Table Rows
          ...sampleData.items.map((item, index) =>
            React.createElement(
              View,
              {
                key: index,
                style: {
                  flexDirection: 'row',
                  padding: 8,
                  backgroundColor: index % 2 === 0 ? colors.white : '#f8f9fa',
                  borderBottomWidth: 1,
                  borderBottomColor: '#dee2e6',
                },
              },
              React.createElement(
                Text,
                {
                  style: {
                    flex: 3,
                    fontSize: 10,
                    paddingRight: 10,
                  },
                },
                item.description
              ),
              React.createElement(
                Text,
                {
                  style: {
                    flex: 1,
                    fontSize: 10,
                    textAlign: 'center',
                  },
                },
                item.quantity.toString()
              ),
              React.createElement(
                Text,
                {
                  style: {
                    flex: 1,
                    fontSize: 10,
                    textAlign: 'right',
                  },
                },
                `$${item.unitPrice.toFixed(2)}`
              ),
              React.createElement(
                Text,
                {
                  style: {
                    flex: 1,
                    fontSize: 10,
                    fontFamily: 'Helvetica-Bold',
                    textAlign: 'right',
                  },
                },
                `$${item.total.toFixed(2)}`
              )
            )
          )
        ),

        // Totals
        React.createElement(
          View,
          {
            style: {
              flexDirection: 'row',
              justifyContent: 'flex-end',
              marginBottom: 25,
            },
          },
          React.createElement(
            View,
            { style: { width: 200 } },
            React.createElement(
              View,
              {
                style: {
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  paddingVertical: 3,
                },
              },
              React.createElement(
                Text,
                {
                  style: {
                    fontSize: 11,
                    color: colors.gray800,
                  },
                },
                'Subtotal:'
              ),
              React.createElement(
                Text,
                {
                  style: {
                    fontSize: 11,
                    fontFamily: 'Helvetica-Bold',
                  },
                },
                `$${sampleData.subtotal.toFixed(2)}`
              )
            ),
            React.createElement(
              View,
              {
                style: {
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  paddingVertical: 5,
                  paddingTop: 8,
                  borderTopWidth: 2,
                  borderTopColor: colors.primaryBlue,
                },
              },
              React.createElement(
                Text,
                {
                  style: {
                    fontSize: 13,
                    fontFamily: 'Helvetica-Bold',
                    color: colors.primaryBlue,
                  },
                },
                'Total Paid:'
              ),
              React.createElement(
                Text,
                {
                  style: {
                    fontSize: 13,
                    fontFamily: 'Helvetica-Bold',
                    color: colors.primaryBlue,
                  },
                },
                `$${sampleData.total.toFixed(2)}`
              )
            )
          )
        ),

        // Payment Info
        React.createElement(
          View,
          { style: { marginBottom: 20 } },
          React.createElement(
            Text,
            {
              style: {
                fontSize: 14,
                fontFamily: 'Helvetica-Bold',
                color: colors.primaryBlue,
                marginBottom: 10,
              },
            },
            'Payment Information'
          ),
          React.createElement(
            View,
            {
              style: {
                backgroundColor: '#f8f9fa',
                padding: 15,
                borderRadius: 5,
              },
            },
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 11,
                  marginBottom: 3,
                },
              },
              `Payment Method: ${sampleData.paymentMethod}`
            ),
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 11,
                  marginBottom: 3,
                },
              },
              `Transaction ID: ${sampleData.transactionId}`
            ),
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 11,
                  marginBottom: 3,
                },
              },
              'Payment Status: PAID'
            ),
            React.createElement(
              Text,
              {
                style: {
                  fontSize: 11,
                },
              },
              `Payment Date: ${sampleData.issueDate}`
            )
          )
        ),

        // Footer
        React.createElement(
          View,
          {
            style: {
              position: 'absolute',
              bottom: 30,
              left: 72,
              right: 72,
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingTop: 15,
              borderTopWidth: 1,
              borderTopColor: '#dee2e6',
            },
          },
          React.createElement(
            Text,
            {
              style: {
                fontSize: 9,
                color: colors.gray600,
              },
            },
            'Independent Energy Producers Association • www.iepa.com'
          ),
          React.createElement(
            Text,
            {
              style: {
                fontSize: 9,
                color: colors.gray600,
              },
            },
            `Receipt generated on ${new Date().toLocaleDateString()}`
          )
        )
      )
    );

    console.log('✅ Receipt document created');

    // Render to buffer
    const pdfBuffer = await renderToBuffer(ReceiptDocument);
    console.log('✅ PDF rendered to buffer, size:', pdfBuffer.length);

    // Return as response
    const headers = new Headers();
    headers.set('Content-Type', 'application/pdf');
    headers.set(
      'Content-Disposition',
      `attachment; filename="receipt-${registrationType}-${registrationId.slice(-8)}.pdf"`
    );
    headers.set('Content-Length', pdfBuffer.length.toString());

    return new NextResponse(pdfBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('❌ Working receipt generation error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Working receipt generation failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
