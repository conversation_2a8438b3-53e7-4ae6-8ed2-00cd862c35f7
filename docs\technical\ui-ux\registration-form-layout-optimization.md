# IEPA Registration Page Layout Optimization (Next.js)

This document outlines how to reduce vertical scroll on the IEPA registration form by restructuring the layout using CSS and React structure adjustments in a Next.js project.

## Sticky Step Indicators

**JSX:**

```jsx
<div className="sticky-steps">
  {/* Step indicators */}
</div>

CSS:

.sticky-steps {
  position: sticky;
  top: 0;
  background: white;
  z-index: 100;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #ccc;
}

Two-Column Layout for Review Section

JSX:

<div className="review-grid">
  <div className="summary">
    {/* Registration Summary */}
  </div>
  <div className="details">
    {/* Personal, Contact, and Event Info */}
  </div>
</div>

CSS:

.review-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .review-grid {
    grid-template-columns: 1fr;
  }
}

Compact Text and Spacing

CSS:

h1, h2, h3 {
  margin: 0.25em 0;
  font-size: 1.25rem;
}

p {
  margin: 0.25em 0;
  font-size: 0.95rem;
  line-height: 1.3;
}

Reduce Top Padding and Remove Redundant Headers

Conditionally in JSX:

{isRegistrationPage && (
  <div className="sticky-steps">
    {/* Step indicators */}
  </div>
)}

CSS to Hide Non-Essential UI:

.header,
.breadcrumbs {
  display: none;
}

Main Element Optimization

CSS:

main {
  min-height: 100vh;
  padding: 1rem;
}

If scroll needs to be constrained:

.form-wrapper {
  overflow-y: auto;
  max-height: calc(100vh - [header height]);
}

Outcome

These adjustments will eliminate unnecessary vertical padding, reduce scrolling, and anchor the step indicators persistently at the top for consistent navigation context.

