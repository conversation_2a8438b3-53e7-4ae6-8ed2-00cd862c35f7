#!/usr/bin/env node

/**
 * Create Test User Script
 * Creates a test user through the proper Supabase auth API
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  console.error('NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.error('SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

// Create admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTestUser() {
  console.log('🚀 Creating test user through Supabase Auth API...');
  
  const email = '<EMAIL>';
  const password = 'GolfTest123!';
  
  try {
    // First, delete any existing user with this email
    console.log('🗑️ Cleaning up existing user...');
    const { error: deleteError } = await supabase.auth.admin.deleteUser(
      '6d8afb02-1d30-48d4-9631-70f29bce02a3' // The ID from our previous query
    );
    
    if (deleteError && !deleteError.message.includes('User not found')) {
      console.warn('⚠️ Delete warning:', deleteError.message);
    }
    
    // Create new user through proper auth API
    console.log('👤 Creating new user...');
    const { data, error } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true,
      user_metadata: {
        full_name: 'Golf Test User',
        first_name: 'Golf',
        last_name: 'Tester'
      }
    });
    
    if (error) {
      console.error('❌ Error creating user:', error);
      return;
    }
    
    console.log('✅ User created successfully!');
    console.log('📧 Email:', data.user.email);
    console.log('🆔 ID:', data.user.id);
    console.log('✉️ Email Confirmed:', !!data.user.email_confirmed_at);
    
    // Test the login
    console.log('\n🔐 Testing login...');
    
    // Create a regular client for testing login
    const testClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    
    const { data: loginData, error: loginError } = await testClient.auth.signInWithPassword({
      email: email,
      password: password
    });
    
    if (loginError) {
      console.error('❌ Login test failed:', loginError);
    } else {
      console.log('✅ Login test successful!');
      console.log('👤 User ID:', loginData.user.id);
      console.log('📧 Email:', loginData.user.email);
      
      // Sign out
      await testClient.auth.signOut();
    }
    
  } catch (error) {
    console.error('💥 Script error:', error);
  }
}

// Run the script
createTestUser().then(() => {
  console.log('\n🎯 Test user creation complete!');
  console.log('📝 Credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: GolfTest123!');
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
