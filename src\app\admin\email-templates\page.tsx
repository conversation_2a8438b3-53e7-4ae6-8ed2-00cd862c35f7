'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  FiMail,
  FiEdit3,
  FiEye,
  FiTrash2,
  FiPlus,
  FiRefreshCw,
  FiSave,
  FiX,
  FiCode,
  FiType,
  FiSend,
  FiMonitor,
  FiExternalLink,
} from 'react-icons/fi';
import { useAuth } from '@/contexts/AuthContext';

interface EmailTemplate {
  id: string;
  template_key: string;
  template_name: string;
  description: string | null;
  subject_template: string;
  html_template: string;
  text_template: string | null;
  variables: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface Notification {
  type: 'success' | 'error' | 'info';
  message: string;
}

interface PreviewData {
  subject: string;
  html: string;
  text: string | null;
  template_key: string;
  template_name: string;
  raw: {
    subject: string;
    html: string;
    text: string | null;
  };
  sampleData: Record<string, unknown>;
  variables: string[];
}

export default function EmailTemplatesPage() {
  const { user } = useAuth();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [notification, setNotification] = useState<Notification | null>(null);
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(
    null
  );
  const [previewTemplate, setPreviewTemplate] = useState<EmailTemplate | null>(
    null
  );
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [viewMode, setViewMode] = useState<'html' | 'text'>('html');
  const [previewMode, setPreviewMode] = useState<'raw' | 'rendered'>(
    'rendered'
  );
  const [testEmailAddress, setTestEmailAddress] = useState('');
  const [sendingTestEmail, setSendingTestEmail] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state for editing/creating templates
  const [formData, setFormData] = useState({
    template_key: '',
    template_name: '',
    description: '',
    subject_template: '',
    html_template: '',
    text_template: '',
    variables: [] as string[],
    is_active: true,
  });

  useEffect(() => {
    loadTemplates();
  }, []);

  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => {
        setNotification(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [notification]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/email-templates');
      const result = await response.json();

      if (result.success) {
        setTemplates(result.templates);
      } else {
        setNotification({
          type: 'error',
          message: result.error || 'Failed to load email templates',
        });
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      setNotification({
        type: 'error',
        message: 'Failed to load email templates',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (template: EmailTemplate) => {
    setEditingTemplate(template);
    setFormData({
      template_key: template.template_key,
      template_name: template.template_name,
      description: template.description || '',
      subject_template: template.subject_template,
      html_template: template.html_template,
      text_template: template.text_template || '',
      variables: template.variables,
      is_active: template.is_active,
    });
    setShowCreateForm(false);
    setPreviewTemplate(null);
  };

  const handlePreview = async (template: EmailTemplate) => {
    setPreviewTemplate(template);
    setEditingTemplate(null);
    setShowCreateForm(false);

    // Load preview data
    try {
      const response = await fetch(
        `/api/admin/email-templates/${template.id}/preview`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sampleData: {},
            includeRendered: true,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setPreviewData(result.preview);
      } else {
        setNotification({
          type: 'error',
          message: result.error || 'Failed to load preview',
        });
      }
    } catch (error) {
      console.error('Error loading preview:', error);
      setNotification({
        type: 'error',
        message: 'Failed to load preview',
      });
    }
  };

  const handleCreate = () => {
    setShowCreateForm(true);
    setFormData({
      template_key: '',
      template_name: '',
      description: '',
      subject_template: '',
      html_template: '',
      text_template: '',
      variables: [],
      is_active: true,
    });
    setEditingTemplate(null);
    setPreviewTemplate(null);
  };

  const handleSave = async () => {
    try {
      setIsSubmitting(true);

      if (!user?.email) {
        throw new Error('User email not available');
      }

      if (editingTemplate) {
        // Update existing template
        const response = await fetch(
          `/api/admin/email-templates/${editingTemplate.id}`,
          {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              updates: {
                template_name: formData.template_name,
                description: formData.description || null,
                subject_template: formData.subject_template,
                html_template: formData.html_template,
                text_template: formData.text_template || null,
                variables: formData.variables,
                is_active: formData.is_active,
              },
              changedBy: user.email,
              changeReason: 'Updated via admin interface',
            }),
          }
        );

        const result = await response.json();

        if (result.success) {
          setNotification({
            type: 'success',
            message: 'Email template updated successfully',
          });
          await loadTemplates();
          setEditingTemplate(null);
        } else {
          throw new Error(result.error || 'Failed to update template');
        }
      } else {
        // Create new template
        const response = await fetch('/api/admin/email-templates', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            template: formData,
            changedBy: user.email,
          }),
        });

        const result = await response.json();

        if (result.success) {
          setNotification({
            type: 'success',
            message: 'Email template created successfully',
          });
          await loadTemplates();
          setShowCreateForm(false);
        } else {
          throw new Error(result.error || 'Failed to create template');
        }
      }
    } catch (error) {
      console.error('Error saving template:', error);
      setNotification({
        type: 'error',
        message:
          error instanceof Error ? error.message : 'Failed to save template',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (template: EmailTemplate) => {
    if (!user?.email) {
      setNotification({
        type: 'error',
        message: 'User email not available',
      });
      return;
    }

    if (
      !confirm(
        `Are you sure you want to delete the template "${template.template_name}"?`
      )
    ) {
      return;
    }

    try {
      const response = await fetch(
        `/api/admin/email-templates/${template.id}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            changedBy: user.email,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setNotification({
          type: 'success',
          message: 'Email template deleted successfully',
        });
        await loadTemplates();
      } else {
        setNotification({
          type: 'error',
          message: result.error || 'Failed to delete template',
        });
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      setNotification({
        type: 'error',
        message: 'Failed to delete template',
      });
    }
  };

  const handleCancel = () => {
    setEditingTemplate(null);
    setShowCreateForm(false);
    setPreviewTemplate(null);
    setPreviewData(null);
  };

  const handleSendTestEmail = async () => {
    if (!previewTemplate || !testEmailAddress || !user?.email) {
      setNotification({
        type: 'error',
        message: 'Template, test email address, and user email are required',
      });
      return;
    }

    try {
      setSendingTestEmail(true);

      const response = await fetch(
        `/api/admin/email-templates/${previewTemplate.id}/test-send`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            testEmail: testEmailAddress,
            adminEmail: user.email,
            sampleData: previewData?.sampleData || {},
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setNotification({
          type: 'success',
          message: `Test email sent successfully to ${testEmailAddress}`,
        });
        setTestEmailAddress('');
      } else {
        setNotification({
          type: 'error',
          message: result.error || 'Failed to send test email',
        });
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      setNotification({
        type: 'error',
        message: 'Failed to send test email',
      });
    } finally {
      setSendingTestEmail(false);
    }
  };

  const parseVariables = (text: string): string[] => {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables = new Set<string>();
    let match;

    while ((match = variableRegex.exec(text)) !== null) {
      const variable = match[1].trim();
      if (
        !variable.startsWith('#') &&
        !variable.startsWith('/') &&
        !variable.startsWith('^')
      ) {
        variables.add(variable);
      }
    }

    return Array.from(variables);
  };

  const updateVariables = () => {
    const subjectVars = parseVariables(formData.subject_template);
    const htmlVars = parseVariables(formData.html_template);
    const textVars = parseVariables(formData.text_template);

    const allVariables = Array.from(
      new Set([...subjectVars, ...htmlVars, ...textVars])
    );
    setFormData(prev => ({ ...prev, variables: allVariables }));
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? 'default' : 'secondary'}>
        {isActive ? 'Active' : 'Inactive'}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <Button
          onClick={loadTemplates}
          disabled={loading}
          variant="outline"
          size="sm"
        >
          <FiRefreshCw
            className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`}
          />
          Refresh
        </Button>
        <Button onClick={handleCreate} size="sm">
          <FiPlus className="w-4 h-4 mr-2" />
          New Template
        </Button>
      </div>

      {/* Notification */}
      {notification && (
        <Card
          className={`border-l-4 ${
            notification.type === 'success'
              ? 'border-l-green-500 bg-green-50'
              : notification.type === 'error'
                ? 'border-l-red-500 bg-red-50'
                : 'border-l-blue-500 bg-blue-50'
          }`}
        >
          <CardBody className="py-3">
            <p
              className={`text-sm ${
                notification.type === 'success'
                  ? 'text-green-700'
                  : notification.type === 'error'
                    ? 'text-red-700'
                    : 'text-blue-700'
              }`}
            >
              {notification.message}
            </p>
          </CardBody>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Templates List */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">
              Email Templates ({templates.length})
            </h2>
          </CardHeader>
          <CardBody className="space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <FiRefreshCw className="w-8 h-8 animate-spin mx-auto text-gray-400 mb-2" />
                <p className="text-gray-500">Loading templates...</p>
              </div>
            ) : templates.length === 0 ? (
              <div className="text-center py-8">
                <FiMail className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                <p className="text-gray-500">No email templates found</p>
              </div>
            ) : (
              templates.map(template => (
                <div
                  key={template.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-gray-900">
                          {template.template_name}
                        </h3>
                        {getStatusBadge(template.is_active)}
                      </div>
                      <p className="text-sm text-gray-600 mb-1">
                        Key:{' '}
                        <code className="bg-gray-100 px-1 rounded">
                          {template.template_key}
                        </code>
                      </p>
                      {template.description && (
                        <p className="text-sm text-gray-500 mb-2">
                          {template.description}
                        </p>
                      )}
                      <div className="flex flex-wrap gap-1">
                        {template.variables.map(variable => (
                          <Badge
                            key={variable}
                            variant="outline"
                            className="text-xs"
                          >
                            {variable}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex gap-1 ml-4">
                      <Button
                        onClick={() => handlePreview(template)}
                        size="sm"
                        variant="outline"
                        title="Preview Template"
                      >
                        <FiEye className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={() => handleEdit(template)}
                        size="sm"
                        variant="outline"
                      >
                        <FiEdit3 className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={() => handleDelete(template)}
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardBody>
        </Card>

        {/* Edit/Create/Preview Panel */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">
                {editingTemplate
                  ? 'Edit Template'
                  : showCreateForm
                    ? 'Create Template'
                    : previewTemplate
                      ? 'Preview Template'
                      : 'Select a Template'}
              </h2>
              {(editingTemplate || showCreateForm || previewTemplate) && (
                <Button onClick={handleCancel} size="sm" variant="outline">
                  <FiX className="w-4 h-4" />
                </Button>
              )}
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            {editingTemplate || showCreateForm ? (
              // Edit/Create Form
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Template Key *
                    </label>
                    <Input
                      value={formData.template_key}
                      onChange={e =>
                        setFormData(prev => ({
                          ...prev,
                          template_key: e.target.value,
                        }))
                      }
                      placeholder="e.g., registration_confirmation"
                      disabled={!!editingTemplate}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Template Name *
                    </label>
                    <Input
                      value={formData.template_name}
                      onChange={e =>
                        setFormData(prev => ({
                          ...prev,
                          template_name: e.target.value,
                        }))
                      }
                      placeholder="e.g., Registration Confirmation"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <Input
                    value={formData.description}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    placeholder="Brief description of this template"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject Template *
                  </label>
                  <Input
                    value={formData.subject_template}
                    onChange={e => {
                      setFormData(prev => ({
                        ...prev,
                        subject_template: e.target.value,
                      }));
                      updateVariables();
                    }}
                    placeholder="e.g., Welcome {{name}} to IEPA 2025!"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    HTML Template *
                  </label>
                  <Textarea
                    value={formData.html_template}
                    onChange={e => {
                      setFormData(prev => ({
                        ...prev,
                        html_template: e.target.value,
                      }));
                      updateVariables();
                    }}
                    placeholder="HTML email template with {{variable}} placeholders"
                    rows={8}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Text Template
                  </label>
                  <Textarea
                    value={formData.text_template}
                    onChange={e => {
                      setFormData(prev => ({
                        ...prev,
                        text_template: e.target.value,
                      }));
                      updateVariables();
                    }}
                    placeholder="Plain text version of the email"
                    rows={6}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Variables
                  </label>
                  <div className="flex flex-wrap gap-1 p-2 border rounded-md bg-gray-50 min-h-[40px]">
                    {formData.variables.length > 0 ? (
                      formData.variables.map(variable => (
                        <Badge key={variable} variant="outline">
                          {variable}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-sm text-gray-500">
                        No variables detected
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Variables are automatically detected from your templates
                    using {'{{'} {'}}'} syntax
                  </p>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        is_active: e.target.checked,
                      }))
                    }
                    className="rounded"
                  />
                  <label
                    htmlFor="is_active"
                    className="text-sm font-medium text-gray-700"
                  >
                    Template is active
                  </label>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={handleSave}
                    disabled={
                      isSubmitting ||
                      !formData.template_key ||
                      !formData.template_name ||
                      !formData.subject_template ||
                      !formData.html_template
                    }
                    data-testid="save-email-template-button"
                  >
                    <FiSave className="w-4 h-4 mr-2" />
                    {isSubmitting
                      ? editingTemplate
                        ? 'Updating Template...'
                        : 'Creating Template...'
                      : editingTemplate
                        ? 'Update Template'
                        : 'Create Template'}
                  </Button>
                  <Button onClick={handleCancel} variant="outline">
                    Cancel
                  </Button>
                </div>
              </>
            ) : previewTemplate ? (
              // Preview Mode
              <>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={() => setViewMode('html')}
                      size="sm"
                      variant={viewMode === 'html' ? 'default' : 'outline'}
                    >
                      <FiCode className="w-4 h-4 mr-1" />
                      HTML
                    </Button>
                    <Button
                      onClick={() => setViewMode('text')}
                      size="sm"
                      variant={viewMode === 'text' ? 'default' : 'outline'}
                    >
                      <FiType className="w-4 h-4 mr-1" />
                      Text
                    </Button>
                    <div className="h-4 w-px bg-gray-300 mx-2" />
                    <Button
                      onClick={() => setPreviewMode('rendered')}
                      size="sm"
                      variant={
                        previewMode === 'rendered' ? 'default' : 'outline'
                      }
                    >
                      <FiMonitor className="w-4 h-4 mr-1" />
                      Rendered
                    </Button>
                    <Button
                      onClick={() => setPreviewMode('raw')}
                      size="sm"
                      variant={previewMode === 'raw' ? 'default' : 'outline'}
                    >
                      <FiCode className="w-4 h-4 mr-1" />
                      Raw
                    </Button>
                  </div>
                  {previewData && (
                    <Button
                      onClick={() => {
                        const htmlContent =
                          viewMode === 'html'
                            ? previewMode === 'rendered'
                              ? previewData.html
                              : previewData.raw.html
                            : previewMode === 'rendered'
                              ? previewData.text
                              : previewData.raw.text;

                        if (htmlContent && viewMode === 'html') {
                          const newWindow = window.open('', '_blank');
                          if (newWindow) {
                            newWindow.document.write(htmlContent);
                            newWindow.document.close();
                          }
                        }
                      }}
                      size="sm"
                      variant="outline"
                      disabled={viewMode !== 'html'}
                    >
                      <FiExternalLink className="w-4 h-4 mr-1" />
                      Open in New Tab
                    </Button>
                  )}
                </div>

                {previewData ? (
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">
                        Subject
                      </h3>
                      <div className="p-3 bg-gray-50 rounded border">
                        <code className="text-sm">
                          {previewMode === 'rendered'
                            ? previewData.subject
                            : previewData.raw.subject}
                        </code>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">
                        {viewMode === 'html' ? 'HTML Content' : 'Text Content'}
                      </h3>
                      <div className="border rounded">
                        {viewMode === 'html' ? (
                          previewMode === 'rendered' ? (
                            <div className="p-3 bg-white border rounded max-h-96 overflow-y-auto">
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: previewData.html,
                                }}
                              />
                            </div>
                          ) : (
                            <div className="p-3 bg-gray-50 rounded max-h-96 overflow-y-auto">
                              <pre className="text-sm whitespace-pre-wrap">
                                {previewData.raw.html}
                              </pre>
                            </div>
                          )
                        ) : (
                          <div className="p-3 bg-gray-50 rounded max-h-96 overflow-y-auto">
                            <pre className="text-sm whitespace-pre-wrap">
                              {previewMode === 'rendered'
                                ? previewData.text || 'No text template defined'
                                : previewData.raw.text ||
                                  'No text template defined'}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">
                        Variables Used
                      </h3>
                      <div className="flex flex-wrap gap-1">
                        {previewData.variables.map(variable => (
                          <Badge key={variable} variant="outline">
                            {variable}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Test Email Section */}
                    <div className="border-t pt-4">
                      <h3 className="font-medium text-gray-900 mb-3">
                        Send Test Email
                      </h3>
                      <div className="flex gap-2">
                        <Input
                          type="email"
                          placeholder="Enter test email address"
                          value={testEmailAddress}
                          onChange={e => setTestEmailAddress(e.target.value)}
                          className="flex-1"
                        />
                        <Button
                          onClick={handleSendTestEmail}
                          disabled={!testEmailAddress || sendingTestEmail}
                          size="sm"
                        >
                          <FiSend className="w-4 h-4 mr-1" />
                          {sendingTestEmail ? 'Sending...' : 'Send Test'}
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Test email will include sample data and be marked as a
                        test message
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FiRefreshCw className="w-8 h-8 animate-spin mx-auto text-gray-400 mb-2" />
                    <p className="text-gray-500">Loading preview...</p>
                  </div>
                )}
              </>
            ) : (
              // Default state
              <div className="text-center py-8">
                <FiMail className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                <p className="text-gray-500">
                  Select a template to edit or preview
                </p>
              </div>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
