'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  Card,
  CardBody,
  CardHeader,
  CardTitle,
  Button,
  Input,
} from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import { AttendeeRegistration, SpeakerRegistration } from '@/types/database';
import {
  FiDownload,
  FiEdit,
  FiTrash2,
  FiFileText,
  FiEye,
  FiRefreshCw,
  FiMic,
  FiPlus,
  FiCoffee,
  FiCalendar,
} from 'react-icons/fi';
import { ActionButtons } from '@/components/ui/responsive-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency } from '@/lib/pdf-generation/utils';
// import Link from 'next/link'; // Removed - using window.open in ActionButtons
import DeleteConfirmationModal from '@/components/admin/modals/DeleteConfirmationModal';

// Import centralized meal utilities
import { getMealDisplayNames } from '@/lib/meal-utils';

interface AttendeeFilters {
  search: string;
  registrationType: string;
  paymentStatus: string;
  golfParticipation: string;
  meal: string;
}

export default function AttendeesPage() {
  const searchParams = useSearchParams();
  const isTestMode = searchParams?.get('testAdmin') === 'true';
  const mealFilter = searchParams?.get('meal') || '';

  const [attendees, setAttendees] = useState<
    (AttendeeRegistration & { speaker_data?: SpeakerRegistration })[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingAttendee, setDeletingAttendee] =
    useState<AttendeeRegistration | null>(null);
  const [showDelete, setShowDelete] = useState(false);
  const [mealDisplayNames, setMealDisplayNames] = useState<Record<string, string>>({});

  const [filters, setFilters] = useState<AttendeeFilters>({
    search: '',
    registrationType: 'all',
    paymentStatus: 'all',
    golfParticipation: 'all',
    meal: mealFilter || 'all',
  });

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0,
  });

  // Fetch attendees
  const fetchAttendees = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('iepa_attendee_registrations')
        .select('*', { count: 'exact' })
        .order('last_name', { ascending: true, nullsFirst: false })
        .order('first_name', { ascending: true, nullsFirst: false });

      // Apply filters
      if (filters.search) {
        query = query.or(
          `first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,organization.ilike.%${filters.search}%`
        );
      }

      if (filters.registrationType && filters.registrationType !== 'all') {
        query = query.eq('registration_type', filters.registrationType);
      }

      if (filters.paymentStatus && filters.paymentStatus !== 'all') {
        query = query.eq('payment_status', filters.paymentStatus);
      }

      if (filters.golfParticipation && filters.golfParticipation !== 'all') {
        query = query.eq('attending_golf', filters.golfParticipation === 'yes');
      }

      if (filters.meal && filters.meal !== 'all') {
        query = query.contains('meals', [filters.meal]);
      }

      // Apply pagination
      const from = (pagination.page - 1) * pagination.limit;
      const to = from + pagination.limit - 1;
      query = query.range(from, to);

      const { data, error: fetchError, count } = await query;

      if (fetchError) throw fetchError;

      // Manually fetch speaker data for attendees that might have speaker registrations
      // Look for potential speaker matches by email
      const attendeesWithSpeakerData = await Promise.all(
        (data || []).map(async attendee => {
          let speakerData = null;

          // First, try to get linked speaker data (if formal link exists)
          if (attendee.speaker_registration_id) {
            try {
              const { data: linkedSpeakerData, error: speakerError } =
                await supabase
                  .from('iepa_speaker_registrations')
                  .select('*')
                  .eq('id', attendee.speaker_registration_id)
                  .single();

              if (!speakerError && linkedSpeakerData) {
                speakerData = linkedSpeakerData;
              }
            } catch (error) {
              console.warn(
                `Failed to fetch linked speaker data for attendee ${attendee.id}:`,
                error
              );
            }
          }

          // If no linked speaker, look for potential match by email
          if (!speakerData) {
            try {
              const { data: potentialSpeakerData, error: potentialError } =
                await supabase
                  .from('iepa_speaker_registrations')
                  .select('*')
                  .eq('email', attendee.email)
                  .single();

              if (!potentialError && potentialSpeakerData) {
                speakerData = { ...potentialSpeakerData, _isUnlinked: true };
              }
            } catch {
              // No match found, which is fine
            }
          }

          return speakerData
            ? { ...attendee, speaker_data: speakerData }
            : attendee;
        })
      );

      setAttendees(attendeesWithSpeakerData);
      setPagination(prev => ({ ...prev, total: count || 0 }));
    } catch (err) {
      console.error('Error fetching attendees:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch attendees'
      );
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.page, pagination.limit]);

  // Delete attendee
  const handleDelete = async (attendee: AttendeeRegistration) => {
    try {
      const { error: deleteError } = await supabase
        .from('iepa_attendee_registrations')
        .delete()
        .eq('id', attendee.id);

      if (deleteError) throw deleteError;

      // Refresh the list
      await fetchAttendees();
      setShowDelete(false);
      setDeletingAttendee(null);
    } catch (err) {
      console.error('Error deleting attendee:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to delete attendee'
      );
    }
  };

  // Export attendees
  const handleExport = () => {
    // TODO: Implement CSV export
    console.log('Export attendees');
  };

  // Generate and download Stripe invoice using blob method
  const handleGenerateInvoice = async (attendee: AttendeeRegistration) => {
    try {
      setLoading(true);
      setError(null); // Clear any previous errors

      // Create direct download URL - always use test mode for development
      const downloadUrl = `/api/stripe/download-direct?registrationId=${attendee.id}&registrationType=attendee&documentType=invoice&testMode=true`;

      console.log('Starting PDF download for:', attendee.full_name);

      // Fetch the PDF as blob
      const response = await fetch(downloadUrl);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Download failed:', response.status, errorText);
        throw new Error(`Failed to download PDF: ${response.status}`);
      }

      const blob = await response.blob();
      console.log('PDF blob created, size:', blob.size);

      // Create blob URL and trigger download
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `invoice-attendee-${attendee.full_name.replace(/\s+/g, '-')}-${attendee.id.slice(-8)}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up blob URL
      URL.revokeObjectURL(blobUrl);

      console.log('PDF download completed for:', attendee.full_name);
    } catch (err) {
      console.error('Error downloading invoice:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to download invoice'
      );
    } finally {
      setLoading(false);
    }
  };

  // Load meal display names from database
  useEffect(() => {
    const loadMealDisplayNames = async () => {
      try {
        const allMealKeys = [
          'day1-reception', 'day1-breakfast', 'day1-lunch',
          'day2-breakfast', 'day2-lunch', 'day2-dinner',
          'day3-breakfast', 'day3-lunch',
          'sept15Dinner', 'sept16Breakfast', 'sept16Lunch',
          'sept16Dinner', 'sept17Breakfast', 'sept17Lunch'
        ];
        const displayNames = await getMealDisplayNames(allMealKeys);
        setMealDisplayNames(displayNames);
      } catch (error) {
        console.error('Error loading meal display names:', error);
      }
    };

    loadMealDisplayNames();
  }, []);

  useEffect(() => {
    fetchAttendees();
  }, [fetchAttendees]);

  // Function to get meal display name
  const getMealDisplayName = (mealKey: string): string => {
    return mealDisplayNames[mealKey] || mealKey;
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex items-center justify-end">
        <div className="flex items-center space-x-3">
          <Button onClick={handleExport} variant="outline" size="sm">
            <FiDownload className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button onClick={fetchAttendees} variant="outline" size="sm">
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Meal Filter Status */}
      {filters.meal && filters.meal !== 'all' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FiCoffee className="w-5 h-5 text-blue-600" />
                <span className="text-blue-800 font-medium">
                  Showing attendees registered for:{' '}
                  {getMealDisplayName(filters.meal)}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters(prev => ({ ...prev, meal: 'all' }))}
                className="text-blue-600 border-blue-300 hover:bg-blue-100"
              >
                Clear Filter
              </Button>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            <div>
              <Input
                placeholder="Search attendees..."
                value={filters.search}
                onChange={e =>
                  setFilters(prev => ({ ...prev, search: e.target.value }))
                }
                className="w-full"
              />
            </div>
            <Select
              value={filters.registrationType}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, registrationType: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Registration Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="iepa-member">IEPA Member</SelectItem>
                <SelectItem value="non-iepa-member">Non-IEPA Member</SelectItem>
                <SelectItem value="day-use-iepa">Day Use - IEPA</SelectItem>
                <SelectItem value="day-use-non-iepa">
                  Day Use - Non-IEPA
                </SelectItem>
                <SelectItem value="fed-state-government">
                  Federal/State Government
                </SelectItem>
                <SelectItem value="cca">
                  California Community Choice Association
                </SelectItem>
                <SelectItem value="comped-speaker">Comped Speaker</SelectItem>
                <SelectItem value="full-meeting-speaker">
                  Full Meeting Speaker
                </SelectItem>
                <SelectItem value="iepa-staff">IEPA Staff</SelectItem>
                <SelectItem value="sponsor-attendee">Sponsor Attendee</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.paymentStatus}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, paymentStatus: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Payment Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.golfParticipation}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, golfParticipation: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Golf Participation" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="yes">Golf Participants</SelectItem>
                <SelectItem value="no">Non-Golf</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.meal}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, meal: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Filter by Meal" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Meals</SelectItem>
                <SelectItem value="day1-reception">
                  {getMealDisplayName('day1-reception')}
                </SelectItem>
                <SelectItem value="day2-breakfast">
                  {getMealDisplayName('day2-breakfast')}
                </SelectItem>
                <SelectItem value="day2-lunch">
                  {getMealDisplayName('day2-lunch')}
                </SelectItem>
                <SelectItem value="day2-dinner">
                  {getMealDisplayName('day2-dinner')}
                </SelectItem>
                <SelectItem value="day3-breakfast">
                  {getMealDisplayName('day3-breakfast')}
                </SelectItem>
                <SelectItem value="day3-lunch">
                  {getMealDisplayName('day3-lunch')}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody>
            <p className="text-red-600">{error}</p>
          </CardBody>
        </Card>
      )}

      {/* Attendees Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Attendees ({pagination.total})</span>
            <div className="text-sm text-gray-500">
              Page {pagination.page} of {totalPages}
            </div>
          </CardTitle>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="text-center py-8">
              <FiRefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">Loading attendees...</p>
            </div>
          ) : attendees.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No attendees found</p>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden lg:block overflow-x-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="[&_tr]:border-b">
                    <tr>
                      <th className="h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs">
                        Name
                      </th>
                      <th className="h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs">
                        Email
                      </th>
                      <th className="h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs hidden xl:table-cell">
                        Organization
                      </th>
                      <th className="h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs hidden xl:table-cell">
                        Type
                      </th>
                      <th className="h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs hidden xl:table-cell">
                        Payment
                      </th>
                      <th className="h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs hidden 2xl:table-cell">
                        Total
                      </th>
                      <th className="h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs hidden 2xl:table-cell">
                        Golf
                      </th>
                      <th className="h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs hidden xl:table-cell">
                        Submitted
                      </th>
                      <th className="h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="[&_tr:last-child]:border-0">
                    {attendees.map(attendee => (
                      <tr
                        key={attendee.id}
                        className="border-b transition-colors hover:bg-muted/50"
                      >
                        <td className="py-2 px-2 align-middle font-medium text-sm">
                          {`${attendee.last_name}, ${attendee.first_name}`.trim()}
                        </td>
                        <td className="py-2 px-2 align-middle text-sm">
                          <div className="truncate max-w-40">
                            {attendee.email}
                          </div>
                        </td>
                        <td className="py-2 px-2 align-middle hidden xl:table-cell text-sm">
                          <div className="truncate max-w-28">
                            {attendee.organization}
                          </div>
                        </td>
                        <td className="py-2 px-2 align-middle hidden xl:table-cell">
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            {attendee.registration_type}
                          </Badge>
                        </td>
                        <td className="py-2 px-2 align-middle hidden xl:table-cell">
                          {getPaymentStatusBadge(attendee.payment_status)}
                        </td>
                        <td className="py-2 px-2 align-middle hidden 2xl:table-cell text-sm">
                          {formatCurrency(attendee.grand_total)}
                        </td>
                        <td className="py-2 px-2 align-middle hidden 2xl:table-cell">
                          {attendee.attending_golf ? (
                            <Badge className="bg-green-100 text-green-800 text-xs px-1 py-0">
                              Yes
                            </Badge>
                          ) : (
                            <Badge variant="secondary" className="text-xs px-1 py-0">No</Badge>
                          )}
                        </td>
                        <td className="py-2 px-2 align-middle hidden xl:table-cell text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <FiCalendar className="w-3 h-3" />
                            <div className="flex flex-col">
                              <span>{new Date(attendee.created_at).toLocaleDateString()}</span>
                              <span className="text-gray-400">{new Date(attendee.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                            </div>
                          </div>
                        </td>
                        <td className="py-2 px-2 align-middle">
                          <ActionButtons
                            actions={[
                              {
                                label: 'View Details',
                                icon: FiEye,
                                onClick: () =>
                                  window.open(
                                    `/admin/attendees/view?id=${attendee.id}${isTestMode ? '&testAdmin=true' : ''}`,
                                    '_blank'
                                  ),
                              },
                              {
                                label: 'Edit Attendee',
                                icon: FiEdit,
                                onClick: () =>
                                  window.open(
                                    `/admin/attendees/edit?id=${attendee.id}${isTestMode ? '&testAdmin=true' : ''}`,
                                    '_blank'
                                  ),
                              },
                              ...(attendee.speaker_data
                                ? [
                                    {
                                      label: 'View Speaker Profile',
                                      icon: FiMic,
                                      onClick: () =>
                                        window.open(
                                          `/admin/speakers/view?id=${attendee.speaker_data!.id}${isTestMode ? '&testAdmin=true' : ''}`,
                                          '_blank'
                                        ),
                                    },
                                  ]
                                : []),
                              {
                                label: 'Download Invoice',
                                icon: FiFileText,
                                onClick: () => handleGenerateInvoice(attendee),
                              },
                              {
                                label: 'Delete Attendee',
                                icon: FiTrash2,
                                onClick: () => {
                                  setDeletingAttendee(attendee);
                                  setShowDelete(true);
                                },
                                variant: 'destructive' as const,
                              },
                            ]}
                            compact={true}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Mobile Card View */}
              <div className="lg:hidden space-y-4">
                {attendees.map(attendee => (
                  <div
                    key={attendee.id}
                    className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm"
                  >
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-lg">
                          {`${attendee.last_name}, ${attendee.first_name}`.trim()}
                        </h3>
                        {getPaymentStatusBadge(attendee.payment_status)}
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Email:</span>
                          <span className="text-gray-900 truncate ml-2">
                            {attendee.email}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Organization:</span>
                          <span className="text-gray-900 truncate ml-2">
                            {attendee.organization}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Type:</span>
                          <Badge variant="outline" className="ml-2">
                            {attendee.registration_type}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Total:</span>
                          <span className="text-gray-900 font-medium">
                            {formatCurrency(attendee.grand_total)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Golf:</span>
                          {attendee.attending_golf ? (
                            <Badge className="bg-green-100 text-green-800 ml-2">
                              Yes
                            </Badge>
                          ) : (
                            <Badge variant="secondary" className="ml-2">
                              No
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="pt-3 border-t">
                        <ActionButtons
                          actions={[
                            {
                              label: 'View Details',
                              icon: FiEye,
                              onClick: () =>
                                window.open(
                                  `/admin/attendees/view?id=${attendee.id}${isTestMode ? '&testAdmin=true' : ''}`,
                                  '_blank'
                                ),
                            },
                            {
                              label: 'Edit Attendee',
                              icon: FiEdit,
                              onClick: () =>
                                window.open(
                                  `/admin/attendees/edit?id=${attendee.id}${isTestMode ? '&testAdmin=true' : ''}`,
                                  '_blank'
                                ),
                            },
                            ...(attendee.speaker_data
                              ? [
                                  {
                                    label: 'View Speaker Profile',
                                    icon: FiMic,
                                    onClick: () =>
                                      window.open(
                                        `/admin/speakers/view?id=${attendee.speaker_data!.id}${isTestMode ? '&testAdmin=true' : ''}`,
                                        '_blank'
                                      ),
                                  },
                                ]
                              : []),
                            {
                              label: 'Download Invoice',
                              icon: FiFileText,
                              onClick: () => handleGenerateInvoice(attendee),
                            },
                            {
                              label: 'Delete Attendee',
                              icon: FiTrash2,
                              onClick: () => {
                                setDeletingAttendee(attendee);
                                setShowDelete(true);
                              },
                              variant: 'destructive' as const,
                            },
                          ]}
                          compact={false}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-500">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{' '}
                of {pagination.total} results
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPagination(prev => ({ ...prev, page: prev.page - 1 }))
                  }
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPagination(prev => ({ ...prev, page: prev.page + 1 }))
                  }
                  disabled={pagination.page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Delete Confirmation Modal */}
      {deletingAttendee && (
        <DeleteConfirmationModal
          open={showDelete}
          onClose={() => {
            setShowDelete(false);
            setDeletingAttendee(null);
          }}
          onConfirm={() => handleDelete(deletingAttendee)}
          title="Delete Attendee"
          description={`Are you sure you want to delete ${deletingAttendee.full_name}? This action cannot be undone.`}
        />
      )}
    </div>
  );
}
