// IEPA Conference Meal Configuration
// Centralized configuration for all conference meals and dining options


import { supabase } from '@/lib/supabase';

export interface MealOption {
  key: string;
  name: string;
  time: string;
  description?: string;
  price: number; // All meals are complimentary for attendees
  day: string;
  date: string; // ISO date string for the meal
  displayDate: string; // Human-readable date
  dayOfWeek: string; // Day of the week
}

// Database meal type from Supabase
export interface DatabaseMeal {
  id: string;
  meal_key: string;
  meal_name: string;
  meal_description: string | null;
  meal_time: string | null;
  meal_date: string;
  day_of_week: string;
  display_date: string;
  price: number;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface DayMealOptions {
  day: string;
  date: string;
  displayDate: string; // Human-readable date
  dayOfWeek: string; // Day of the week
  meals: MealOption[];
}

// Static fallback meal schedule (used when database is unavailable)
export const fallbackMealOptions: DayMealOptions[] = [
  {
    day: 'Monday, September 15, 2025 - Conference Opening',
    date: '2025-09-15',
    displayDate: 'Monday, September 15, 2025',
    dayOfWeek: 'Monday',
    meals: [
      {
        key: 'day1-reception',
        name: 'Welcome Reception and Dinner',
        time: '6:00 PM - 10:00 PM',
        description: 'Cocktail reception with hors d\'oeuvres and plated dinner',
        price: 0,
        day: 'Monday, September 15, 2025',
        date: '2025-09-15',
        displayDate: 'Monday, September 15, 2025',
        dayOfWeek: 'Monday',
      },
    ],
  },
  {
    day: 'Tuesday, September 16, 2025 - Main Conference',
    date: '2025-09-16',
    displayDate: 'Tuesday, September 16, 2025',
    dayOfWeek: 'Tuesday',
    meals: [
      {
        key: 'day2-breakfast',
        name: 'Conference Breakfast',
        time: '7:30 AM - 8:30 AM',
        description: 'Hot breakfast buffet',
        price: 0,
        day: 'Tuesday, September 16, 2025',
        date: '2025-09-16',
        displayDate: 'Tuesday, September 16, 2025',
        dayOfWeek: 'Tuesday',
      },
      {
        key: 'day2-lunch',
        name: 'Conference Lunch',
        time: '12:00 PM - 1:00 PM',
        description: 'Lunch buffet',
        price: 0,
        day: 'Tuesday, September 16, 2025',
        date: '2025-09-16',
        displayDate: 'Tuesday, September 16, 2025',
        dayOfWeek: 'Tuesday',
      },
      {
        key: 'day2-dinner',
        name: 'Conference Reception and Dinner',
        time: '6:00 PM - 10:00 PM',
        description: 'Reception and plated dinner',
        price: 0,
        day: 'Tuesday, September 16, 2025',
        date: '2025-09-16',
        displayDate: 'Tuesday, September 16, 2025',
        dayOfWeek: 'Tuesday',
      },
    ],
  },
  {
    day: 'Wednesday, September 17, 2025 - Conference Closing',
    date: '2025-09-17',
    displayDate: 'Wednesday, September 17, 2025',
    dayOfWeek: 'Wednesday',
    meals: [
      {
        key: 'day3-breakfast',
        name: 'Conference Breakfast',
        time: '8:00 AM - 9:00 AM',
        description: 'Hot breakfast buffet',
        price: 0,
        day: 'Wednesday, September 17, 2025',
        date: '2025-09-17',
        displayDate: 'Wednesday, September 17, 2025',
        dayOfWeek: 'Wednesday',
      },
      {
        key: 'day3-lunch',
        name: 'Conference Lunch',
        time: '12:00 PM - 1:00 PM',
        description: 'Lunch buffet',
        price: 0,
        day: 'Wednesday, September 17, 2025',
        date: '2025-09-17',
        displayDate: 'Wednesday, September 17, 2025',
        dayOfWeek: 'Wednesday',
      },
    ],
  },
];

// Primary export - gets meals from database with fallback to static data
export const mealOptions = fallbackMealOptions; // For backward compatibility

// Function to get meals from database or fallback
export async function getMealOptions(): Promise<DayMealOptions[]> {
  try {
    return await mealDatabase.getMealsGroupedByDay();
  } catch (error) {
    console.error('Error fetching meals from database, using fallback:', error);
    return fallbackMealOptions;
  }
}

// Database functions for meal management
export const mealDatabase = {
  /**
   * Fetch all meals from Supabase
   */
  async fetchAllMeals(): Promise<DatabaseMeal[]> {
    const { data, error } = await supabase
      .from('iepa_meals')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      console.error('Error fetching meals:', error);
      return [];
    }

    return data || [];
  },

  /**
   * Get meal display name from database
   */
  async getMealDisplayName(mealKey: string): Promise<string> {
    const { data, error } = await supabase
      .from('iepa_meals')
      .select('display_date')
      .eq('meal_key', mealKey)
      .eq('is_active', true)
      .single();

    if (error || !data) {
      console.error('Error fetching meal display name:', error);
      return mealKey; // Fallback to the key itself
    }

    return data.display_date;
  },

  /**
   * Get multiple meal display names
   */
  async getMealDisplayNames(mealKeys: string[]): Promise<Record<string, string>> {
    if (mealKeys.length === 0) return {};

    const { data, error } = await supabase
      .from('iepa_meals')
      .select('meal_key, display_date')
      .in('meal_key', mealKeys)
      .eq('is_active', true);

    if (error || !data) {
      console.error('Error fetching meal display names:', error);
      return {};
    }

    return data.reduce((acc: Record<string, string>, meal: { meal_key: string; display_date: string }) => {
      acc[meal.meal_key] = meal.display_date;
      return acc;
    }, {} as Record<string, string>);
  },

  /**
   * Convert database meal to MealOption format
   */
  convertToMealOption(dbMeal: DatabaseMeal): MealOption {
    return {
      key: dbMeal.meal_key,
      name: dbMeal.meal_name,
      time: dbMeal.meal_time || '',
      description: dbMeal.meal_description || undefined,
      price: dbMeal.price,
      day: dbMeal.display_date,
      date: dbMeal.meal_date,
      displayDate: dbMeal.display_date,
      dayOfWeek: dbMeal.day_of_week,
    };
  },

  /**
   * Get meals grouped by day from database
   */
  async getMealsGroupedByDay(): Promise<DayMealOptions[]> {
    const meals = await this.fetchAllMeals();
    const mealsByDate = meals.reduce((acc, meal) => {
      const date = meal.meal_date;
      if (!acc[date]) {
        acc[date] = {
          day: meal.display_date,
          date: meal.meal_date,
          displayDate: meal.display_date,
          dayOfWeek: meal.day_of_week,
          meals: [],
        };
      }
      acc[date].meals.push(this.convertToMealOption(meal));
      return acc;
    }, {} as Record<string, DayMealOptions>);

    return Object.values(mealsByDate).sort((a, b) => a.date.localeCompare(b.date));
  },
};

// Utility functions for meal management
export const mealUtils = {
  /**
   * Get all available meals as a flat array
   */
  getAllMeals: (): MealOption[] => {
    return mealOptions.flatMap(day => day.meals);
  },

  /**
   * Get meals for a specific day
   */
  getMealsByDay: (day: string): MealOption[] => {
    const dayOption = mealOptions.find(d => d.day === day);
    return dayOption ? dayOption.meals : [];
  },

  /**
   * Get meal by key
   */
  getMealByKey: (key: string): MealOption | null => {
    const allMeals = mealUtils.getAllMeals();
    return allMeals.find(meal => meal.key === key) || null;
  },

  /**
   * Calculate total meal cost (currently all meals are complimentary)
   */
  calculateMealTotal: (selectedMealKeys: string[]): number => {
    return selectedMealKeys.reduce((total, key) => {
      const meal = mealUtils.getMealByKey(key);
      return total + (meal ? meal.price : 0);
    }, 0);
  },

  /**
   * Get meal names for display
   */
  getMealNames: (selectedMealKeys: string[]): string[] => {
    return selectedMealKeys
      .map(key => {
        const meal = mealUtils.getMealByKey(key);
        return meal ? meal.name : null;
      })
      .filter(Boolean) as string[];
  },

  /**
   * Validate meal selection based on registration type
   */
  validateMealSelection: (
    selectedMealKeys: string[],
    registrationType: string
  ): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    // For speaker registrations, validate based on pricing type
    if (registrationType === 'comped-speaker') {
      // Comped speakers get 3 meals - typically breakfast, lunch, dinner on one day
      const selectedMeals = selectedMealKeys.map(key => mealUtils.getMealByKey(key)).filter(Boolean);
      if (selectedMeals.length > 3) {
        errors.push('Complimentary speaker registration includes up to 3 meals');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  },
};

export default fallbackMealOptions;
