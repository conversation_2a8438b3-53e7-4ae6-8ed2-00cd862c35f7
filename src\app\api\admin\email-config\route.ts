// API Route: Admin Email Configuration Management
// Handle CRUD operations for email configuration

import { NextRequest, NextResponse } from 'next/server';
import { emailConfigService } from '@/services/email-config';

export async function GET(request: NextRequest) {
  try {
    console.log('[EMAIL-CONFIG-API] Getting all email configurations...');

    const configs = await emailConfigService.getAllConfigs();
    
    console.log('[EMAIL-CONFIG-API] Retrieved configurations:', configs.length);

    return NextResponse.json({
      success: true,
      configurations: configs,
      count: configs.length,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('[EMAIL-CONFIG-API] Failed to get configurations:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to retrieve email configurations',
      configurations: [],
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('[EMAIL-CONFIG-API] Updating email configurations...');

    const body = await request.json();
    const { configurations, changedBy, changeReason } = body;

    if (!configurations || typeof configurations !== 'object') {
      return NextResponse.json({
        success: false,
        error: 'Invalid configurations data'
      }, { status: 400 });
    }

    if (!changedBy) {
      return NextResponse.json({
        success: false,
        error: 'changedBy is required'
      }, { status: 400 });
    }

    const results = [];
    const errors = [];

    // Update each configuration
    for (const [key, value] of Object.entries(configurations)) {
      if (typeof value === 'string' && value.trim()) {
        try {
          console.log(`[EMAIL-CONFIG-API] Updating ${key}: ${value.trim()}`);
          
          await emailConfigService.updateConfig(
            key,
            value.trim(),
            changedBy,
            changeReason || 'Updated via admin interface'
          );
          
          results.push({ key, value: value.trim(), status: 'updated' });
        } catch (error: any) {
          console.error(`[EMAIL-CONFIG-API] Failed to update ${key}:`, error);
          errors.push({ key, error: error.message });
        }
      } else {
        console.log(`[EMAIL-CONFIG-API] Skipping ${key}: empty or invalid value`);
      }
    }

    // Refresh the configuration cache
    await emailConfigService.refreshConfig();

    console.log('[EMAIL-CONFIG-API] Update completed:', {
      updated: results.length,
      errors: errors.length
    });

    if (errors.length > 0) {
      return NextResponse.json({
        success: false,
        message: `Updated ${results.length} configurations, but ${errors.length} failed`,
        results,
        errors,
        timestamp: new Date().toISOString()
      }, { status: 207 }); // 207 Multi-Status
    }

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${results.length} email configurations`,
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('[EMAIL-CONFIG-API] Update failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to update email configurations',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    console.log('[EMAIL-CONFIG-API] Refreshing email configuration cache...');

    await emailConfigService.refreshConfig();
    
    console.log('[EMAIL-CONFIG-API] Cache refreshed successfully');

    return NextResponse.json({
      success: true,
      message: 'Email configuration cache refreshed successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('[EMAIL-CONFIG-API] Cache refresh failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to refresh configuration cache',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
