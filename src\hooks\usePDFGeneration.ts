// React Hook for PDF Generation
// Custom hook for generating and managing PDF receipts and invoices

import { useState, useCallback } from 'react';

export interface PDFGenerationState {
  generating: boolean;
  error: string | null;
  receiptUrl: string | null;
  invoiceUrl: string | null;
}

export interface PDFGenerationOptions {
  registrationId: string;
  registrationType: 'attendee' | 'speaker' | 'sponsor';
  paymentMethod?: string;
  transactionId?: string;
  dueDate?: string;
  paymentTerms?: string;
  notes?: string;
}

export function usePDFGeneration() {
  const [state, setState] = useState<PDFGenerationState>({
    generating: false,
    error: null,
    receiptUrl: null,
    invoiceUrl: null,
  });

  /**
   * Generate PDF receipt
   */
  const generateReceipt = useCallback(
    async (options: PDFGenerationOptions): Promise<boolean> => {
      setState(prev => ({
        ...prev,
        generating: true,
        error: null,
      }));

      try {
        const response = await fetch('/api/pdf/generate-receipt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            registrationId: options.registrationId,
            registrationType: options.registrationType,
            paymentMethod: options.paymentMethod,
            transactionId: options.transactionId,
          }),
        });

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || 'Failed to generate receipt');
        }

        setState(prev => ({
          ...prev,
          generating: false,
          receiptUrl: result.receiptUrl,
        }));

        return true;
      } catch (error) {
        setState(prev => ({
          ...prev,
          generating: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        }));
        return false;
      }
    },
    []
  );

  /**
   * Generate PDF invoice
   */
  const generateInvoice = useCallback(
    async (options: PDFGenerationOptions): Promise<boolean> => {
      setState(prev => ({
        ...prev,
        generating: true,
        error: null,
      }));

      try {
        const response = await fetch('/api/pdf/generate-invoice', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            registrationId: options.registrationId,
            registrationType: options.registrationType,
            dueDate: options.dueDate,
            paymentTerms: options.paymentTerms,
            notes: options.notes,
          }),
        });

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || 'Failed to generate invoice');
        }

        setState(prev => ({
          ...prev,
          generating: false,
          invoiceUrl: result.invoiceUrl,
        }));

        return true;
      } catch (error) {
        setState(prev => ({
          ...prev,
          generating: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        }));
        return false;
      }
    },
    []
  );

  /**
   * Get existing receipt URL
   */
  const getReceiptUrl = useCallback(
    async (
      registrationId: string,
      registrationType: 'attendee' | 'speaker' | 'sponsor'
    ): Promise<string | null> => {
      try {
        const response = await fetch(
          `/api/pdf/generate-receipt?registrationId=${registrationId}&registrationType=${registrationType}`
        );

        const result = await response.json();

        if (result.success) {
          setState(prev => ({
            ...prev,
            receiptUrl: result.receiptUrl,
          }));
          return result.receiptUrl;
        }

        return null;
      } catch (error) {
        console.error('Error getting receipt URL:', error);
        return null;
      }
    },
    []
  );

  /**
   * Get existing invoice URL
   */
  const getInvoiceUrl = useCallback(
    async (
      registrationId: string,
      registrationType: 'attendee' | 'speaker' | 'sponsor'
    ): Promise<string | null> => {
      try {
        const response = await fetch(
          `/api/pdf/generate-invoice?registrationId=${registrationId}&registrationType=${registrationType}`
        );

        const result = await response.json();

        if (result.success) {
          setState(prev => ({
            ...prev,
            invoiceUrl: result.invoiceUrl,
          }));
          return result.invoiceUrl;
        }

        return null;
      } catch (error) {
        console.error('Error getting invoice URL:', error);
        return null;
      }
    },
    []
  );

  /**
   * Download PDF file using secure download API
   */
  const downloadPDF = useCallback(
    async (
      registrationId: string,
      registrationType: 'attendee' | 'speaker' | 'sponsor',
      documentType: 'receipt' | 'invoice',
      fileName?: string
    ) => {
      try {
        const response = await fetch(
          `/api/pdf/download?registrationId=${registrationId}&registrationType=${registrationType}&documentType=${documentType}`
        );

        if (!response.ok) {
          throw new Error('Failed to get download URL');
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || 'Failed to get download URL');
        }

        // Create download link with signed URL
        const link = document.createElement('a');
        link.href = result.downloadUrl;
        link.download = fileName || result.fileName || `${documentType}.pdf`;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        console.error('Error downloading PDF:', error);
        throw error;
      }
    },
    []
  );

  /**
   * Reset state
   */
  const resetState = useCallback(() => {
    setState({
      generating: false,
      error: null,
      receiptUrl: null,
      invoiceUrl: null,
    });
  }, []);

  return {
    ...state,
    generateReceipt,
    generateInvoice,
    getReceiptUrl,
    getInvoiceUrl,
    downloadPDF,
    resetState,
  };
}

/**
 * Hook for PDF storage setup and testing
 */
export function usePDFStorageSetup() {
  const [state, setState] = useState<{
    loading: boolean;
    error: string | null;
    setupComplete: boolean;
  }>({
    loading: false,
    error: null,
    setupComplete: false,
  });

  /**
   * Initialize PDF storage
   */
  const initializeStorage = useCallback(async (): Promise<boolean> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/pdf/setup-storage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'initialize' }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to initialize storage');
      }

      setState(prev => ({
        ...prev,
        loading: false,
        setupComplete: true,
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
      return false;
    }
  }, []);

  /**
   * Test PDF storage setup
   */
  const testStorage = useCallback(async (): Promise<boolean> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/pdf/setup-storage?action=test');
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Storage test failed');
      }

      setState(prev => ({
        ...prev,
        loading: false,
        setupComplete:
          result.details?.bucketExists &&
          result.details?.canUpload &&
          result.details?.canDownload,
      }));

      return result.success;
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
      return false;
    }
  }, []);

  /**
   * Get storage information
   */
  const getStorageInfo = useCallback(async () => {
    try {
      const response = await fetch('/api/pdf/setup-storage?action=info');
      const result = await response.json();
      return result.success ? result.info : null;
    } catch (error) {
      console.error('Error getting storage info:', error);
      return null;
    }
  }, []);

  return {
    ...state,
    initializeStorage,
    testStorage,
    getStorageInfo,
  };
}
