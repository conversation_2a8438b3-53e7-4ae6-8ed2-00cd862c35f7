'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardBody, CardHeader } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { FileUpload } from '@/components/ui/FileUpload';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { updateSpeakerFiles } from '@/lib/fileManagement';
import { useSignedUrls } from '@/hooks/useSignedUrl';
import { showSuccess, showError } from '@/utils/notifications';
import {
  FiMic,
  FiSave,
  FiArrowLeft,
  FiUser,
  FiBriefcase,
  FiFile,
  FiImage,
} from 'react-icons/fi';

interface SpeakerData {
  id: string;
  full_name: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  organization_name: string;
  job_title: string;
  bio: string;
  presentation_title: string;
  presentation_description: string;
  presentation_duration: string;
  target_audience: string;
  learning_objectives: string;
  speaker_experience: string;
  previous_speaking: string;
  equipment_needs: string;
  special_requests: string;
  presentation_file_url: string | null;
  headshot_url: string | null;
}

export default function EditSpeakerProfilePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [speakerData, setSpeakerData] = useState<SpeakerData | null>(null);
  const [formData, setFormData] = useState<Partial<SpeakerData>>({});
  const [newFiles, setNewFiles] = useState<{
    presentationFile?: File;
    headshot?: File;
  }>({});

  // Memoize URLs array to prevent infinite loops
  const urlsToFetch = useMemo(() => {
    if (!speakerData) return [];
    return [
      {
        url: speakerData.presentation_file_url,
        bucket: 'iepa-presentations',
        key: 'presentation',
      },
      {
        url: speakerData.headshot_url,
        bucket: 'iepa-presentations',
        key: 'headshot',
      },
    ];
  }, [speakerData?.presentation_file_url, speakerData?.headshot_url]);

  // Get signed URLs for current files
  const signedUrls = useSignedUrls(urlsToFetch, { enabled: !!speakerData });

  // Fetch speaker data
  useEffect(() => {
    if (!user) return;

    const fetchSpeakerData = async () => {
      try {
        const { data, error } = await supabase
          .from('iepa_speaker_registrations')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            // No speaker registration found
            router.push('/my-speaker-profile');
            return;
          }
          throw error;
        }

        setSpeakerData(data);
        setFormData(data);
      } catch (error) {
        console.error('Error fetching speaker data:', error);
        showError('Error', 'Failed to load speaker data');
        router.push('/my-speaker-profile');
      } finally {
        setLoading(false);
      }
    };

    fetchSpeakerData();
  }, [user, router]);

  const handleInputChange = (field: keyof SpeakerData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFileUpload = (type: 'presentationFile' | 'headshot', file: File | null) => {
    if (file) {
      setNewFiles(prev => ({
        ...prev,
        [type]: file,
      }));
    }
  };

  const handleSave = async () => {
    if (!speakerData || !user) return;

    try {
      setSaving(true);

      // Update files if any were uploaded
      let fileUpdateResult;
      if (newFiles.presentationFile || newFiles.headshot) {
        fileUpdateResult = await updateSpeakerFiles(
          speakerData.id,
          newFiles,
          {
            presentationFileUrl: speakerData.presentation_file_url,
            headshotUrl: speakerData.headshot_url,
          }
        );

        if (!fileUpdateResult.success) {
          throw new Error(fileUpdateResult.error);
        }
      }

      // Prepare update data
      const updateData = {
        ...formData,
        updated_at: new Date().toISOString(),
      };

      // Include updated file URLs if files were uploaded
      if (fileUpdateResult?.updatedUrls) {
        if (fileUpdateResult.updatedUrls.presentationFileUrl) {
          updateData.presentation_file_url = fileUpdateResult.updatedUrls.presentationFileUrl;
        }
        if (fileUpdateResult.updatedUrls.headshotUrl) {
          updateData.headshot_url = fileUpdateResult.updatedUrls.headshotUrl;
        }
      }

      // Update database
      const { error } = await supabase
        .from('iepa_speaker_registrations')
        .update(updateData)
        .eq('id', speakerData.id);

      if (error) {
        throw error;
      }

      showSuccess('Profile Updated', 'Speaker profile updated successfully!');
      router.push('/my-speaker-profile');
    } catch (error) {
      console.error('Error updating speaker profile:', error);
      showError(
        'Update Failed',
        error instanceof Error ? error.message : 'Failed to update speaker profile'
      );
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    router.push('/my-speaker-profile');
  };

  if (loading) {
    return (
      <div className="iepa-container">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--iepa-primary-blue)] mx-auto mb-4"></div>
            <p className="iepa-body">Loading speaker profile...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!speakerData) {
    return null;
  }

  return (
    <div className="iepa-container">
      {/* Header */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center gap-4 mb-8">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBack}
              className="flex items-center gap-2"
            >
              <FiArrowLeft className="w-4 h-4" />
              Back to Profile
            </Button>
            <div>
              <h1 className="iepa-heading-1 flex items-center gap-3">
                <FiMic className="w-8 h-8 text-[var(--iepa-primary-blue)]" />
                Edit Speaker Profile
              </h1>
              <p className="iepa-body text-gray-600">
                Update your speaker information and files
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Edit Form */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Personal Information */}
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 flex items-center gap-2">
                <FiUser className="w-5 h-5" />
                Personal Information
              </h2>
            </CardHeader>
            <CardBody>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first_name">First Name</Label>
                  <Input
                    id="first_name"
                    value={formData.first_name || ''}
                    onChange={e => handleInputChange('first_name', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="last_name">Last Name</Label>
                  <Input
                    id="last_name"
                    value={formData.last_name || ''}
                    onChange={e => handleInputChange('last_name', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email || ''}
                    onChange={e => handleInputChange('email', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="phone_number">Phone Number</Label>
                  <Input
                    id="phone_number"
                    value={formData.phone_number || ''}
                    onChange={e => handleInputChange('phone_number', e.target.value)}
                  />
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Professional Information */}
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 flex items-center gap-2">
                <FiBriefcase className="w-5 h-5" />
                Professional Information
              </h2>
            </CardHeader>
            <CardBody>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="organization_name">Organization</Label>
                  <Input
                    id="organization_name"
                    value={formData.organization_name || ''}
                    onChange={e => handleInputChange('organization_name', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="job_title">Job Title</Label>
                  <Input
                    id="job_title"
                    value={formData.job_title || ''}
                    onChange={e => handleInputChange('job_title', e.target.value)}
                  />
                </div>
              </div>
              <div className="mt-4">
                <Label htmlFor="bio">Biography</Label>
                <Textarea
                  id="bio"
                  rows={4}
                  value={formData.bio || ''}
                  onChange={e => handleInputChange('bio', e.target.value)}
                  placeholder="Tell us about yourself and your expertise..."
                />
              </div>
            </CardBody>
          </Card>

          {/* Presentation Information */}
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 flex items-center gap-2">
                <FiMic className="w-5 h-5" />
                Presentation Information
              </h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="presentation_title">Presentation Title</Label>
                  <Input
                    id="presentation_title"
                    value={formData.presentation_title || ''}
                    onChange={e => handleInputChange('presentation_title', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="presentation_description">Presentation Description</Label>
                  <Textarea
                    id="presentation_description"
                    rows={4}
                    value={formData.presentation_description || ''}
                    onChange={e => handleInputChange('presentation_description', e.target.value)}
                    placeholder="Describe your presentation topic and key points..."
                  />
                </div>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="presentation_duration">Duration</Label>
                    <Input
                      id="presentation_duration"
                      value={formData.presentation_duration || ''}
                      onChange={e => handleInputChange('presentation_duration', e.target.value)}
                      placeholder="e.g., 45 minutes"
                    />
                  </div>
                  <div>
                    <Label htmlFor="target_audience">Target Audience</Label>
                    <Input
                      id="target_audience"
                      value={formData.target_audience || ''}
                      onChange={e => handleInputChange('target_audience', e.target.value)}
                      placeholder="e.g., Energy professionals, Developers"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="learning_objectives">Learning Objectives</Label>
                  <Textarea
                    id="learning_objectives"
                    rows={3}
                    value={formData.learning_objectives || ''}
                    onChange={e => handleInputChange('learning_objectives', e.target.value)}
                    placeholder="What will attendees learn from your presentation?"
                  />
                </div>
              </div>
            </CardBody>
          </Card>

          {/* File Uploads */}
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 flex items-center gap-2">
                <FiFile className="w-5 h-5" />
                Files & Media
              </h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-6">
                <div>
                  <FileUpload
                    label="Presentation File"
                    description="Upload your presentation file (PDF, PPT, PPTX, DOC, DOCX - max 50MB)"
                    bucket="iepa-presentations"
                    folder="speaker-presentations"
                    maxSize={52428800} // 50MB
                    allowedTypes={[
                      'application/pdf',
                      'application/vnd.ms-powerpoint',
                      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                      'application/msword',
                      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    ]}
                    accept=".pdf,.ppt,.pptx,.doc,.docx"
                    onFileUpload={(url, file) => handleFileUpload('presentationFile', file)}
                    placeholder={
                      speakerData.presentation_file_url
                        ? 'Replace current presentation file'
                        : 'Upload your presentation file'
                    }
                  />
                  {speakerData.presentation_file_url && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">
                        Current file:{' '}
                        {signedUrls.presentation?.loading ? (
                          <span className="text-gray-500">Loading...</span>
                        ) : signedUrls.presentation?.signedUrl ? (
                          <a
                            href={signedUrls.presentation.signedUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-[var(--iepa-primary-blue)] hover:underline"
                          >
                            View current presentation
                          </a>
                        ) : (
                          <span className="text-red-500">Error loading file</span>
                        )}
                      </p>
                    </div>
                  )}
                </div>

                <div>
                  <FileUpload
                    label="Professional Headshot"
                    description="Upload a professional headshot (JPG, PNG, WebP - max 5MB)"
                    bucket="iepa-presentations"
                    folder="speaker-headshots"
                    maxSize={5242880} // 5MB
                    allowedTypes={['image/jpeg', 'image/png', 'image/webp']}
                    accept=".jpg,.jpeg,.png,.webp"
                    onFileUpload={(url, file) => handleFileUpload('headshot', file)}
                    placeholder={
                      speakerData.headshot_url
                        ? 'Replace current headshot'
                        : 'Upload your professional headshot'
                    }
                  />

                  {/* Current Headshot Preview */}
                  {speakerData.headshot_url && (
                    <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                      <div className="flex items-start gap-4">
                        {/* Thumbnail */}
                        <div className="flex-shrink-0">
                          {signedUrls.headshot?.loading ? (
                            <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[var(--iepa-primary-blue)]"></div>
                            </div>
                          ) : signedUrls.headshot?.signedUrl ? (
                            <div className="relative group">
                              <img
                                src={signedUrls.headshot.signedUrl}
                                alt="Current headshot"
                                className="w-20 h-20 object-cover rounded-lg border-2 border-gray-200 shadow-sm hover:shadow-md transition-all cursor-pointer"
                                onClick={() => signedUrls.headshot?.signedUrl && window.open(signedUrls.headshot.signedUrl, '_blank')}
                              />
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-lg transition-all duration-200 flex items-center justify-center">
                                <FiImage className="w-4 h-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                              </div>
                            </div>
                          ) : (
                            <div className="w-20 h-20 bg-red-50 border-2 border-red-200 rounded-lg flex items-center justify-center">
                              <p className="text-xs text-red-600 text-center px-1">Error</p>
                            </div>
                          )}
                        </div>

                        {/* Info */}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 mb-1">Current Headshot</p>
                          <p className="text-sm text-gray-600 mb-2">
                            {signedUrls.headshot?.loading ? (
                              'Loading preview...'
                            ) : signedUrls.headshot?.signedUrl ? (
                              'Click thumbnail or link below to view full size'
                            ) : (
                              'Error loading preview'
                            )}
                          </p>
                          {signedUrls.headshot?.signedUrl && (
                            <a
                              href={signedUrls.headshot.signedUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-[var(--iepa-primary-blue)] hover:underline text-sm"
                            >
                              View full size →
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Speaking Experience */}
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2">Speaking Experience</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="speaker_experience">Speaking Experience</Label>
                  <Textarea
                    id="speaker_experience"
                    rows={3}
                    value={formData.speaker_experience || ''}
                    onChange={e => handleInputChange('speaker_experience', e.target.value)}
                    placeholder="Describe your speaking experience..."
                  />
                </div>
                <div>
                  <Label htmlFor="previous_speaking">Previous Speaking Engagements</Label>
                  <Textarea
                    id="previous_speaking"
                    rows={3}
                    value={formData.previous_speaking || ''}
                    onChange={e => handleInputChange('previous_speaking', e.target.value)}
                    placeholder="List previous conferences, events, or speaking engagements..."
                  />
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Technical Requirements */}
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2">Technical Requirements</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="equipment_needs">Equipment Needs</Label>
                  <Textarea
                    id="equipment_needs"
                    rows={3}
                    value={formData.equipment_needs || ''}
                    onChange={e => handleInputChange('equipment_needs', e.target.value)}
                    placeholder="List any special equipment or technical requirements..."
                  />
                </div>
                <div>
                  <Label htmlFor="special_requests">Special Requests</Label>
                  <Textarea
                    id="special_requests"
                    rows={3}
                    value={formData.special_requests || ''}
                    onChange={e => handleInputChange('special_requests', e.target.value)}
                    placeholder="Any special requests or accommodations needed..."
                  />
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4">
            <Button variant="outline" onClick={handleBack} disabled={saving}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="flex items-center gap-2"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Saving...
                </>
              ) : (
                <>
                  <FiSave className="w-4 h-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
