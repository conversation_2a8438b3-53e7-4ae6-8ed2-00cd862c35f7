'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui';
import Link from 'next/link';
import {
  generateBreadcrumbs,
  shouldShowBreadcrumbs,
  type BreadcrumbItem as BreadcrumbItemType,
} from '@/lib/breadcrumb-config';

interface BreadcrumbsProps {
  className?: string;
  showIcons?: boolean;
  maxItems?: number;
  compact?: boolean; // For navigation header usage
}

export function Breadcrumbs({
  className = '',
  showIcons = true,
  compact = false,
}: Omit<BreadcrumbsProps, 'maxItems'>) {
  const pathname = usePathname();

  // Don't render breadcrumbs if they shouldn't be shown for this path
  if (!pathname || !shouldShowBreadcrumbs(pathname)) {
    return null;
  }

  const breadcrumbItems = generateBreadcrumbs(pathname);

  // Don't render if there's only one item (just home)
  if (breadcrumbItems.length <= 1) {
    return null;
  }

  // For compact navigation usage, render without container
  if (compact) {
    return (
      <Breadcrumb className={`py-1 ${className}`}>
        <BreadcrumbList className="text-xs">
          {breadcrumbItems.map((item: BreadcrumbItemType, index) => (
            <React.Fragment key={item.href}>
              <BreadcrumbItem
                className={
                  item.isCurrentPage
                    ? 'text-iepa-primary font-medium cursor-default'
                    : 'hover:text-iepa-primary-dark'
                }
              >
                {item.isCurrentPage ? (
                  <BreadcrumbPage className="flex items-center gap-1">
                    {showIcons && item.icon && (
                      <span className="text-xs" aria-hidden="true">
                        {item.icon}
                      </span>
                    )}
                    <span>{item.label}</span>
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link
                      href={item.href}
                      className="flex items-center gap-1 hover:text-iepa-primary-dark transition-colors"
                    >
                      {showIcons && item.icon && (
                        <span className="text-xs" aria-hidden="true">
                          {item.icon}
                        </span>
                      )}
                      <span>{item.label}</span>
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {index < breadcrumbItems.length - 1 && (
                <BreadcrumbSeparator className="text-iepa-gray-400" />
              )}
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  // Standard breadcrumbs with container
  return (
    <div className={`iepa-breadcrumbs-container ${className}`}>
      <div className="iepa-container">
        <Breadcrumb className="py-3">
          <BreadcrumbList className="text-sm">
            {breadcrumbItems.map((item: BreadcrumbItemType, index) => (
              <React.Fragment key={item.href}>
                <BreadcrumbItem
                  className={
                    item.isCurrentPage
                      ? 'text-iepa-primary font-medium cursor-default'
                      : 'hover:text-iepa-primary-dark'
                  }
                >
                  {item.isCurrentPage ? (
                    <BreadcrumbPage className="flex items-center gap-2">
                      {showIcons && item.icon && (
                        <span className="text-xs" aria-hidden="true">
                          {item.icon}
                        </span>
                      )}
                      <span>{item.label}</span>
                    </BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <Link
                        href={item.href}
                        className="flex items-center gap-2 hover:text-iepa-primary-dark transition-colors"
                      >
                        {showIcons && item.icon && (
                          <span className="text-xs" aria-hidden="true">
                            {item.icon}
                          </span>
                        )}
                        <span>{item.label}</span>
                      </Link>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {index < breadcrumbItems.length - 1 && (
                  <BreadcrumbSeparator className="text-iepa-gray-400" />
                )}
              </React.Fragment>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    </div>
  );
}

// Utility component for custom breadcrumb items
interface CustomBreadcrumbsProps {
  items: Array<{
    label: string;
    href?: string;
    isCurrentPage?: boolean;
    icon?: string;
  }>;
  className?: string;
  showIcons?: boolean;
  maxItems?: number;
}

export function CustomBreadcrumbs({
  items,
  className = '',
  showIcons = true,
}: Omit<CustomBreadcrumbsProps, 'maxItems'>) {
  if (items.length === 0) {
    return null;
  }

  return (
    <div className={`iepa-breadcrumbs-container ${className}`}>
      <div className="iepa-container">
        <Breadcrumb className="py-3">
          <BreadcrumbList className="text-sm">
            {items.map((item, index) => (
              <React.Fragment key={item.href || index}>
                <BreadcrumbItem
                  className={
                    item.isCurrentPage
                      ? 'text-iepa-primary font-medium cursor-default'
                      : 'hover:text-iepa-primary-dark'
                  }
                >
                  {item.isCurrentPage || !item.href ? (
                    <BreadcrumbPage className="flex items-center gap-2">
                      {showIcons && item.icon && (
                        <span className="text-xs" aria-hidden="true">
                          {item.icon}
                        </span>
                      )}
                      <span>{item.label}</span>
                    </BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <Link
                        href={item.href}
                        className="flex items-center gap-2 hover:text-iepa-primary-dark transition-colors"
                      >
                        {showIcons && item.icon && (
                          <span className="text-xs" aria-hidden="true">
                            {item.icon}
                          </span>
                        )}
                        <span>{item.label}</span>
                      </Link>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {index < items.length - 1 && (
                  <BreadcrumbSeparator className="text-iepa-gray-400" />
                )}
              </React.Fragment>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    </div>
  );
}
