import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';
import crypto from 'crypto';

const supabaseAdmin = createSupabaseAdmin();

// SendGrid webhook events we want to track
const TRACKED_EVENTS = [
  'delivered',
  'bounce',
  'dropped',
  'deferred',
  'processed',
  'open',
  'click',
  'unsubscribe',
  'group_unsubscribe',
  'group_resubscribe',
  'spam_report'
];

interface SendGridEvent {
  email: string;
  timestamp: number;
  event: string;
  sg_message_id: string;
  sg_event_id: string;
  reason?: string;
  status?: string;
  response?: string;
  url?: string;
  useragent?: string;
  ip?: string;
  category?: string[];
  unique_args?: Record<string, string>;
  marketing_campaign_id?: string;
  marketing_campaign_name?: string;
}

export async function POST(request: NextRequest) {
  try {
    console.log('[SENDGRID-WEBHOOK] Received webhook request');

    // Get the raw body for signature verification
    const body = await request.text();
    const signature = request.headers.get('x-twilio-email-event-webhook-signature');
    const timestamp = request.headers.get('x-twilio-email-event-webhook-timestamp');

    // Verify webhook signature if configured
    if (process.env.SENDGRID_WEBHOOK_VERIFY_KEY && signature && timestamp) {
      const isValid = verifyWebhookSignature(body, signature, timestamp);
      if (!isValid) {
        console.error('[SENDGRID-WEBHOOK] Invalid webhook signature');
        return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
      }
    }

    // Parse the events
    let events: SendGridEvent[];
    try {
      events = JSON.parse(body);
    } catch (parseError) {
      console.error('[SENDGRID-WEBHOOK] Failed to parse webhook body:', parseError);
      return NextResponse.json({ error: 'Invalid JSON' }, { status: 400 });
    }

    if (!Array.isArray(events)) {
      console.error('[SENDGRID-WEBHOOK] Expected array of events');
      return NextResponse.json({ error: 'Expected array of events' }, { status: 400 });
    }

    console.log(`[SENDGRID-WEBHOOK] Processing ${events.length} events`);

    // Process each event
    const results = await Promise.allSettled(
      events.map(event => processEvent(event))
    );

    // Count successes and failures
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    console.log(`[SENDGRID-WEBHOOK] Processed ${successful} events successfully, ${failed} failed`);

    // Log any failures
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        console.error(`[SENDGRID-WEBHOOK] Event ${index} failed:`, result.reason);
      }
    });

    return NextResponse.json({
      success: true,
      processed: successful,
      failed: failed,
      total: events.length
    });

  } catch (error) {
    console.error('[SENDGRID-WEBHOOK] Webhook processing failed:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function processEvent(event: SendGridEvent): Promise<void> {
  try {
    console.log(`[SENDGRID-WEBHOOK] Processing ${event.event} event for ${event.email}`);

    if (!TRACKED_EVENTS.includes(event.event)) {
      console.log(`[SENDGRID-WEBHOOK] Ignoring untracked event: ${event.event}`);
      return;
    }

    // Find the email log entry by SendGrid message ID
    const { data: emailLogs, error: findError } = await supabaseAdmin
      .from('iepa_email_log')
      .select('*')
      .eq('sendgrid_message_id', event.sg_message_id)
      .limit(1);

    if (findError) {
      console.error('[SENDGRID-WEBHOOK] Error finding email log:', findError);
      return;
    }

    if (!emailLogs || emailLogs.length === 0) {
      console.warn(`[SENDGRID-WEBHOOK] No email log found for message ID: ${event.sg_message_id}`);
      return;
    }

    const emailLog = emailLogs[0];

    // Update the email log based on the event type
    const updates = await getUpdatesForEvent(event, emailLog);

    if (Object.keys(updates).length > 0) {
      const { error: updateError } = await supabaseAdmin
        .from('iepa_email_log')
        .update(updates)
        .eq('id', emailLog.id);

      if (updateError) {
        console.error('[SENDGRID-WEBHOOK] Error updating email log:', updateError);
        return;
      }

      console.log(`[SENDGRID-WEBHOOK] Updated email log ${emailLog.id} for ${event.event} event`);
    }

    // Log the event details in a separate events table (if we want detailed tracking)
    await logEventDetails(event, emailLog.id);

  } catch (error) {
    console.error(`[SENDGRID-WEBHOOK] Error processing event:`, error);
    throw error;
  }
}

async function getUpdatesForEvent(event: SendGridEvent, emailLog: Record<string, unknown>): Promise<Record<string, unknown>> {
  const updates: Record<string, unknown> = {
    updated_at: new Date().toISOString()
  };

  switch (event.event) {
    case 'delivered':
      updates.status = 'sent';
      if (!emailLog.sent_at) {
        updates.sent_at = new Date(event.timestamp * 1000).toISOString();
      }
      break;

    case 'bounce':
    case 'dropped':
      updates.status = 'failed';
      updates.error_message = event.reason || event.response || `Email ${event.event}`;
      break;

    case 'deferred':
      // Keep as pending but update error message
      updates.error_message = event.reason || 'Email delivery deferred';
      break;

    case 'processed':
      // Email was processed by SendGrid but not yet delivered
      if (emailLog.status === 'pending') {
        // Keep as pending, just update timestamp
      }
      break;

    case 'open':
    case 'click':
    case 'unsubscribe':
    case 'group_unsubscribe':
    case 'group_resubscribe':
    case 'spam_report':
      // These are engagement events - don't change delivery status
      // We could track these in a separate engagement table if needed
      break;

    default:
      console.log(`[SENDGRID-WEBHOOK] Unhandled event type: ${event.event}`);
  }

  return updates;
}

async function logEventDetails(event: SendGridEvent, emailLogId: string): Promise<void> {
  try {
    // We could create a separate table for detailed event tracking
    // For now, we'll just log to console for debugging
    console.log(`[SENDGRID-WEBHOOK] Event details for email log ${emailLogId}:`, {
      event: event.event,
      timestamp: new Date(event.timestamp * 1000).toISOString(),
      email: event.email,
      sg_message_id: event.sg_message_id,
      sg_event_id: event.sg_event_id,
      reason: event.reason,
      status: event.status,
      url: event.url,
      ip: event.ip,
      useragent: event.useragent
    });

    // TODO: If we want detailed event tracking, create iepa_email_events table
    // and insert event details there
    
  } catch (error) {
    console.error('[SENDGRID-WEBHOOK] Error logging event details:', error);
  }
}

function verifyWebhookSignature(
  payload: string,
  signature: string,
  timestamp: string
): boolean {
  try {
    const verifyKey = process.env.SENDGRID_WEBHOOK_VERIFY_KEY;
    if (!verifyKey) {
      console.warn('[SENDGRID-WEBHOOK] No verification key configured');
      return true; // Allow if not configured
    }

    // SendGrid signature verification
    const timestampedPayload = timestamp + payload;
    const expectedSignature = crypto
      .createHmac('sha256', verifyKey)
      .update(timestampedPayload, 'utf8')
      .digest('base64');

    return signature === expectedSignature;
  } catch (error) {
    console.error('[SENDGRID-WEBHOOK] Signature verification error:', error);
    return false;
  }
}

// Handle GET requests for webhook verification
export async function GET() {
  return NextResponse.json({
    message: 'SendGrid webhook endpoint is active',
    timestamp: new Date().toISOString(),
    events_tracked: TRACKED_EVENTS
  });
}
