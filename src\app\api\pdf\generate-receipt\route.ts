// API Route for PDF Receipt Generation
// POST /api/pdf/generate-receipt

import { NextRequest, NextResponse } from 'next/server';
import { generateAndStoreReceiptPDF } from '@/lib/pdf-generation/services/pdfGenerator';
import { createSupabaseAdmin } from '@/lib/supabase';
import { transformDatabaseDataForPDF } from '@/utils/pdf-data-transform';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      registrationId,
      registrationType,
      paymentMethod,
      transactionId,
      forceRegenerate,
    } = body;

    // Validate required fields
    if (!registrationId || !registrationType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Registration ID and type are required',
        },
        { status: 400 }
      );
    }

    // Validate registration type
    if (!['attendee', 'speaker', 'sponsor'].includes(registrationType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid registration type',
        },
        { status: 400 }
      );
    }

    // Get registration data from database
    const supabaseAdmin = createSupabaseAdmin();
    let tableName;

    switch (registrationType) {
      case 'attendee':
        tableName = 'iepa_attendee_registrations';
        break;
      case 'speaker':
        tableName = 'iepa_speaker_registrations';
        break;
      case 'sponsor':
        tableName = 'iepa_sponsor_registrations';
        break;
      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid registration type',
          },
          { status: 400 }
        );
    }

    console.log('[PDF-RECEIPT] Searching for registration:', {
      registrationId,
      registrationType,
      tableName,
    });

    const { data, error } = await supabaseAdmin
      .from(tableName)
      .select('*')
      .eq('id', registrationId)
      .single();

    if (error) {
      console.error('[PDF-RECEIPT] Database error:', error);
      return NextResponse.json(
        {
          success: false,
          error: `Database error: ${error.message}`,
          details: {
            code: error.code,
            hint: error.hint,
            registrationId,
            tableName,
          },
        },
        { status: 404 }
      );
    }

    if (!data) {
      console.error('[PDF-RECEIPT] Registration not found:', {
        registrationId,
        registrationType,
        tableName,
      });
      return NextResponse.json(
        {
          success: false,
          error: `Registration not found in ${tableName}`,
          details: {
            registrationId,
            registrationType,
            tableName,
          },
        },
        { status: 404 }
      );
    }

    console.log('[PDF-RECEIPT] Registration found:', {
      id: data.id,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      paymentStatus: data.payment_status,
    });

    // Transform database data to PDF-expected format
    const registrationData = transformDatabaseDataForPDF(
      registrationType,
      data
    );

    console.log('[PDF-RECEIPT] Transformed data for PDF:', {
      id: registrationData.id,
      fullName: registrationData.fullName,
      email: registrationData.email,
      registrationType: registrationData.registrationType,
      grandTotal: registrationData.grandTotal,
    });

    // Check if receipt already exists and handle force regeneration
    if (data.receipt_url && !forceRegenerate) {
      console.log(
        '[PDF-RECEIPT] Receipt already exists, skipping generation:',
        {
          receiptUrl: data.receipt_url,
          generatedAt: data.receipt_generated_at,
        }
      );

      return NextResponse.json({
        success: true,
        message: 'Receipt already exists',
        receiptUrl: data.receipt_url,
        fileName: data.receipt_url.split('/').pop(),
        filePath: data.receipt_url,
        alreadyExists: true,
      });
    }

    if (forceRegenerate && data.receipt_url) {
      console.log('[PDF-RECEIPT] Force regenerating existing receipt:', {
        existingReceiptUrl: data.receipt_url,
        registrationId,
      });
    }

    // Generate and store PDF receipt
    const result = await generateAndStoreReceiptPDF(
      registrationType as 'attendee' | 'speaker' | 'sponsor',
      registrationData,
      paymentMethod,
      transactionId
    );

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error,
        },
        { status: 500 }
      );
    }

    console.log('[PDF-RECEIPT] PDF generation result:', {
      success: result.success,
      fileName: result.fileName,
      filePath: result.filePath,
      publicUrl: result.publicUrl,
    });

    // Update registration record with receipt file path (for signed URL generation)
    const updateData: Record<string, unknown> = {
      receipt_url: result.filePath || result.publicUrl, // Store file path for signed URLs
      receipt_generated_at: new Date().toISOString(),
    };

    console.log('[PDF-RECEIPT] Updating registration record:', {
      registrationId,
      tableName,
      updateData,
    });

    const { error: updateError } = await supabaseAdmin
      .from(tableName)
      .update(updateData)
      .eq('id', registrationId);

    if (updateError) {
      console.error(
        '[PDF-RECEIPT] Error updating registration record:',
        updateError
      );
      // Don't fail the whole operation, just log the error
    } else {
      console.log('[PDF-RECEIPT] Registration record updated successfully');
    }

    return NextResponse.json({
      success: true,
      message: 'Receipt generated successfully',
      receiptUrl: result.filePath || result.publicUrl,
      fileName: result.fileName,
      filePath: result.filePath,
    });
  } catch (error) {
    console.error('Error in generate-receipt API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}

// GET method to retrieve existing receipt
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');
    const registrationType = searchParams.get('registrationType');

    if (!registrationId || !registrationType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Registration ID and type are required',
        },
        { status: 400 }
      );
    }

    // Validate registration type
    if (!['attendee', 'speaker', 'sponsor'].includes(registrationType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid registration type',
        },
        { status: 400 }
      );
    }

    // Get registration data from database
    const supabaseAdmin = createSupabaseAdmin();
    let tableName;

    switch (registrationType) {
      case 'attendee':
        tableName = 'iepa_attendee_registrations';
        break;
      case 'speaker':
        tableName = 'iepa_speaker_registrations';
        break;
      case 'sponsor':
        tableName = 'iepa_sponsor_registrations';
        break;
      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid registration type',
          },
          { status: 400 }
        );
    }

    const { data, error } = await supabaseAdmin
      .from(tableName)
      .select('receipt_url, receipt_generated_at')
      .eq('id', registrationId)
      .single();

    if (error || !data) {
      return NextResponse.json(
        {
          success: false,
          error: 'Registration not found',
        },
        { status: 404 }
      );
    }

    if (!data.receipt_url) {
      return NextResponse.json(
        {
          success: false,
          error: 'Receipt not found for this registration',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      receiptUrl: data.receipt_url,
      generatedAt: data.receipt_generated_at,
    });
  } catch (error) {
    console.error('Error in get receipt API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}
