// API Route for Secure PDF Download
// GET /api/pdf/download

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';
import { generateSignedPDFUrl } from '@/lib/pdf-generation/services/pdfGenerator';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');
    const registrationType = searchParams.get('registrationType');
    const documentType = searchParams.get('documentType'); // 'invoice' or 'receipt'

    // Validate required parameters
    if (!registrationId || !registrationType || !documentType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Registration ID, type, and document type are required',
        },
        { status: 400 }
      );
    }

    // Validate registration type
    if (!['attendee', 'speaker', 'sponsor'].includes(registrationType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid registration type',
        },
        { status: 400 }
      );
    }

    // Validate document type
    if (!['invoice', 'receipt'].includes(documentType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid document type',
        },
        { status: 400 }
      );
    }

    // Get registration data from database to verify it exists and get the file path
    const supabaseAdmin = createSupabaseAdmin();
    let tableName: string;

    switch (registrationType) {
      case 'attendee':
        tableName = 'iepa_attendee_registrations';
        break;
      case 'speaker':
        tableName = 'iepa_speaker_registrations';
        break;
      case 'sponsor':
        tableName = 'iepa_sponsor_registrations';
        break;
      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid registration type',
          },
          { status: 400 }
        );
    }

    const urlField = documentType === 'invoice' ? 'invoice_url' : 'receipt_url';

    const { data, error } = await supabaseAdmin
      .from(tableName)
      .select(`id, ${urlField}`)
      .eq('id', registrationId)
      .single();

    if (error || !data) {
      return NextResponse.json(
        {
          success: false,
          error: 'Registration not found',
        },
        { status: 404 }
      );
    }

    const fileUrl = (data as any)[urlField];
    if (!fileUrl) {
      return NextResponse.json(
        {
          success: false,
          error: `${documentType} not found for this registration`,
        },
        { status: 404 }
      );
    }

    // Extract file path from the stored URL or use as-is if it's already a path
    let filePath: string;

    console.log('Stored fileUrl:', fileUrl); // Debug log

    if (fileUrl.startsWith('http')) {
      // It's a full URL, extract the file path
      try {
        const url = new URL(fileUrl);
        // Extract path from URL like: /storage/v1/object/public/iepa-documents/invoices/filename.pdf
        // or /storage/v1/object/sign/iepa-documents/invoices/filename.pdf
        const pathParts = url.pathname.split('/');
        const bucketIndex = pathParts.indexOf('iepa-documents');
        if (bucketIndex === -1) {
          throw new Error('Invalid file URL format');
        }
        filePath = pathParts.slice(bucketIndex + 1).join('/');
      } catch {
        // If URL parsing fails, try to extract using regex
        const match = fileUrl.match(/iepa-documents\/(.+?)(?:\?|$)/);
        if (match) {
          filePath = match[1];
        } else {
          throw new Error('Could not extract file path from URL');
        }
      }
    } else {
      // It's already a file path (new format)
      filePath = fileUrl;
    }

    console.log('Extracted filePath:', filePath); // Debug log

    // Generate signed URL for secure download
    const signedUrlResult = await generateSignedPDFUrl(filePath, 300); // 5 minutes expiry for downloads

    if (!signedUrlResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: signedUrlResult.error,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      downloadUrl: signedUrlResult.signedUrl,
      expiresIn: 300, // 5 minutes
      fileName: filePath.split('/').pop(),
    });
  } catch (error) {
    console.error('Error in PDF download API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}
