# Docker & Supabase Automated Setup

This document explains the automated Docker and Supabase setup for the IEPA Conference Registration project.

## Overview

The project now includes automated setup scripts and configurations to ensure the correct Supabase environment starts when opening the project, eliminating the need to manually manage Docker containers and Supabase projects.

## Quick Start

### Option 1: Using NPM Scripts (Recommended)

```bash
# Start the complete development environment
npm run dev:full

# Or start components separately
npm run supabase:setup  # Setup and start Supabase
npm run dev             # Start Next.js on port 6969
```

### Option 2: Using the Setup Script Directly

```bash
# Make sure the script is executable (already done)
chmod +x scripts/setup-local-supabase.sh

# Run the setup script
./scripts/setup-local-supabase.sh

# Then start your dev server
npm run dev
```

### Option 3: Using VS Code Tasks

1. Open the Command Palette (`Cmd+Shift+P`)
2. Type "Tasks: Run Task"
3. Select "🎯 Full Development Setup"

This will automatically:
- Start Docker if needed
- Stop any conflicting Supabase projects
- Start the IEPA Supabase environment
- Start the Next.js development server

## What the Setup Does

The automated setup script (`scripts/setup-local-supabase.sh`) performs the following:

1. **Docker Check**: Verifies Docker is running, starts it if needed
2. **Supabase CLI Check**: Ensures Supabase CLI is installed
3. **Conflict Resolution**: Stops any existing Supabase projects (like propfirms)
4. **IEPA Startup**: Starts the IEPA-specific Supabase environment
5. **Status Display**: Shows connection information and next steps

## Configuration Files

### Supabase Configuration (`supabase/config.toml`)

Key configurations for IEPA:
- **Project ID**: `iepa-conf-reg`
- **Site URL**: `http://127.0.0.1:6969` (matches dev server port)
- **Database Port**: `54322`
- **Studio Port**: `54323`
- **API Port**: `54321`
- **Email Confirmations**: Disabled (as per requirements)

### Storage Buckets

Pre-configured buckets:
- `iepa-presentations`: For speaker presentation files
- `iepa-sponsor-assets`: For sponsor logos and images  
- `iepa-documents`: For PDF invoices and receipts

### Database Migrations

Automatically applied migrations:
1. `20240101000000_initial_schema_fixed.sql`: Core database schema
2. `20240101000001_rls_policies.sql`: Row Level Security policies
3. `20240101000002_storage_setup.sql`: Storage buckets and policies
4. `20240101000003_email_log_schema.sql`: Email logging system

## VS Code Integration

### Tasks Available

- **🚀 Setup IEPA Development Environment**: Complete setup
- **🛑 Stop Supabase**: Stop local Supabase
- **📊 Open Supabase Studio**: Open Studio in browser
- **🔄 Restart Supabase**: Restart Supabase environment
- **🏃‍♂️ Start Dev Server (Port 6969)**: Start Next.js
- **🎯 Full Development Setup**: Complete setup + dev server

### Launch Configuration

The project includes a launch configuration that:
- Automatically runs the setup script before starting
- Launches Next.js on port 6969
- Sets up proper environment variables

## Environment URLs

When running locally:

- **Next.js App**: http://localhost:6969
- **Supabase Studio**: http://127.0.0.1:54323
- **Supabase API**: http://127.0.0.1:54321
- **Email Testing**: http://127.0.0.1:54324
- **Database**: postgresql://postgres:postgres@127.0.0.1:54322/postgres

## Troubleshooting

### Docker Issues

```bash
# Check if Docker is running
docker info

# Start Docker manually
open -a Docker
```

### Port Conflicts

```bash
# Check what's using the ports
lsof -i :54321
lsof -i :54322
lsof -i :54323

# Stop conflicting Supabase projects
supabase stop --project-id propfirms
```

### Supabase CLI Issues

```bash
# Install/update Supabase CLI
brew install supabase/tap/supabase
brew upgrade supabase

# Check version
supabase --version
```

### Reset Everything

```bash
# Stop all Supabase containers
supabase stop

# Remove all Supabase containers (nuclear option)
docker container prune -f
docker volume prune -f

# Restart setup
npm run supabase:setup
```

## Manual Commands

If you need to run commands manually:

```bash
# Supabase commands
supabase start
supabase stop
supabase restart
supabase status
supabase studio

# Docker commands
docker ps                    # List running containers
docker stop $(docker ps -q) # Stop all containers
docker system prune -f      # Clean up Docker
```

## Development Workflow

1. **Open Project**: The setup should run automatically via VS Code tasks
2. **Daily Development**: Just run `npm run dev:full` or use VS Code tasks
3. **Switching Projects**: The script handles stopping other Supabase projects
4. **Debugging**: Use Supabase Studio at http://127.0.0.1:54323

## Benefits

- **Zero Manual Setup**: Everything starts automatically
- **Conflict Resolution**: Handles multiple Supabase projects
- **Consistent Environment**: Same setup across all developers
- **VS Code Integration**: Tasks and launch configurations
- **Port Management**: Uses correct ports (6969 for Next.js)
- **Database Migrations**: Automatically applied on startup
