// Test API to check existing auth users
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function GET() {
  try {
    const supabaseAdmin = createSupabaseAdmin();
    
    // Query auth.users table to see existing users
    const { data: users, error: usersError } = await supabaseAdmin.auth.admin.listUsers();
    
    if (usersError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch users',
        details: usersError.message
      });
    }

    // Also check iepa_admin_users table
    const { data: adminUsers, error: adminError } = await supabaseAdmin
      .from('iepa_admin_users')
      .select('*');

    return NextResponse.json({
      success: true,
      authUsers: users.users.map(user => ({
        id: user.id,
        email: user.email,
        email_confirmed_at: user.email_confirmed_at,
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at
      })),
      adminUsers: adminUsers || [],
      adminError: adminError?.message || null
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
