#!/bin/bash

# IEPA Conference Registration - Project Initialization Script
# Run this script when first setting up the project or after a fresh clone

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header "🚀 IEPA Conference Registration - Project Initialization"
echo "=========================================================="
echo ""

# Change to project directory
cd "$(dirname "$0")/.."

# Check if this is a fresh clone
if [ ! -d "node_modules" ]; then
    print_status "Installing Node.js dependencies..."
    npm install
    print_success "Dependencies installed"
else
    print_success "Dependencies already installed"
fi

# Check for environment file
if [ ! -f ".env.local" ]; then
    print_warning ".env.local not found"
    print_status "You may need to create .env.local with your Supabase credentials"
    print_status "For local development, the default local Supabase will be used"
else
    print_success ".env.local exists"
fi

# Make scripts executable
print_status "Making scripts executable..."
chmod +x scripts/*.sh
print_success "Scripts are now executable"

# Check Docker and Supabase CLI
print_status "Checking prerequisites..."

if ! command -v docker &> /dev/null; then
    print_error "Docker not found. Please install Docker Desktop"
    exit 1
fi

if ! command -v supabase &> /dev/null; then
    print_error "Supabase CLI not found"
    print_status "Install with: brew install supabase/tap/supabase"
    exit 1
fi

print_success "Prerequisites check passed"

# Initialize Supabase if needed
if [ ! -f "supabase/config.toml" ]; then
    print_error "Supabase not initialized. This should not happen in this project."
    exit 1
else
    print_success "Supabase configuration found"
fi

# Run environment check
print_status "Running environment check..."
./scripts/check-environment.sh

echo ""
print_header "🎉 Project Initialization Complete!"
echo ""
echo "Next steps:"
echo "1. Start the development environment:"
echo "   ${GREEN}npm run dev:full${NC}"
echo ""
echo "2. Or start components separately:"
echo "   ${GREEN}npm run supabase:setup${NC}  # Start Supabase"
echo "   ${GREEN}npm run dev${NC}             # Start Next.js"
echo ""
echo "3. Open your browser to:"
echo "   ${BLUE}http://localhost:6969${NC}        # Your app"
echo "   ${BLUE}http://127.0.0.1:54323${NC}       # Supabase Studio"
echo ""
echo "4. Available commands:"
echo "   ${GREEN}npm run env:check${NC}        # Check environment status"
echo "   ${GREEN}npm run supabase:studio${NC}  # Open Supabase Studio"
echo "   ${GREEN}npm run supabase:status${NC}  # Check Supabase status"
echo ""
print_success "Happy coding! 🎯"
