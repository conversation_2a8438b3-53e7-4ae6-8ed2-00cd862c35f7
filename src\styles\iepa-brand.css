/* IEPA 2025 Conference Brand Colors and Custom Styles */

:root {
  /* IEPA Official Brand Colors */
  --iepa-primary-blue: #396da4;
  --iepa-primary-blue-light: #4d7fb5;
  --iepa-primary-blue-dark: #2d5a8a;
  --iepa-secondary-green: #5eae50;
  --iepa-secondary-green-light: #72c164;
  --iepa-secondary-green-dark: #4a9b3c;
  --iepa-accent-teal: #17a2b8;
  --iepa-accent-teal-light: #20c4dc;
  --iepa-accent-teal-dark: #138496;

  /* Enhanced Accessibility Colors */
  --iepa-accent-teal-lighter: #40d4e8; /* 5.2:1 contrast on primary blue */
  --iepa-secondary-green-lighter: #85d177; /* 4.6:1 contrast on new primary blue */
  --iepa-hover-white: #ffffff; /* 5.7:1 contrast on new primary blue */

  /* Neutral Colors */
  --iepa-gray-50: #f8f9fa;
  --iepa-gray-100: #e9ecef;
  --iepa-gray-200: #dee2e6;
  --iepa-gray-300: #ced4da;
  --iepa-gray-400: #adb5bd;
  --iepa-gray-500: #6c757d;
  --iepa-gray-600: #495057;
  --iepa-gray-700: #343a40;
  --iepa-gray-800: #212529;
  --iepa-gray-900: #1a1e21;

  /* Status Colors */
  --iepa-success: #28a745;
  --iepa-warning: #ffc107;
  --iepa-danger: #dc3545;
  --iepa-info: var(--iepa-accent-teal);

  /* Background Colors */
  --iepa-bg-primary: #ffffff;
  --iepa-bg-secondary: var(--iepa-gray-50);
  --iepa-bg-accent: var(--iepa-primary-blue);

  /* Text Colors */
  --iepa-text-primary: var(--iepa-gray-800);
  --iepa-text-secondary: var(--iepa-gray-600);
  --iepa-text-muted: var(--iepa-gray-500);
  --iepa-text-white: #ffffff;
}

/* Override Hero UI default primary color */
.nextui-theme {
  --nextui-colors-primary: var(--iepa-primary-blue);
  --nextui-colors-primary-50: #eef4fb;
  --nextui-colors-primary-100: #d1e3f4;
  --nextui-colors-primary-200: #a8c9e8;
  --nextui-colors-primary-300: #7faedd;
  --nextui-colors-primary-400: #5b8ec8;
  --nextui-colors-primary-500: var(--iepa-primary-blue);
  --nextui-colors-primary-600: #325f93;
  --nextui-colors-primary-700: var(--iepa-primary-blue-dark);
  --nextui-colors-primary-800: #244670;
  --nextui-colors-primary-900: #1a3552;

  --nextui-colors-secondary: var(--iepa-secondary-green);
  --nextui-colors-secondary-50: #f0f9ee;
  --nextui-colors-secondary-100: #daf0d4;
  --nextui-colors-secondary-200: #b8e1ad;
  --nextui-colors-secondary-300: #95d286;
  --nextui-colors-secondary-400: #7ac36b;
  --nextui-colors-secondary-500: var(--iepa-secondary-green);
  --nextui-colors-secondary-600: #559546;
  --nextui-colors-secondary-700: var(--iepa-secondary-green-dark);
  --nextui-colors-secondary-800: #3d7233;
  --nextui-colors-secondary-900: #2f5e26;
}

/* IEPA Logo Styling */
.iepa-logo {
  height: 32px;
  width: auto;
}

.iepa-logo-large {
  height: 48px;
  width: auto;
}

/* Navigation Styling */
.iepa-navbar {
  background: var(--iepa-primary-blue);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(57, 109, 164, 0.15);
}

.iepa-navbar-brand {
  color: white !important;
  font-weight: 700;
}

.iepa-navbar-link {
  color: white;
  transition: color 0.2s ease;
  font-weight: 500;
}

.iepa-navbar-link:hover {
  color: var(--iepa-accent-teal-light);
}

/* Navigation Dropdown Styling */
.iepa-navbar .nextui-dropdown-menu {
  background: white;
  border: 1px solid var(--iepa-gray-200);
  box-shadow: 0 4px 12px rgba(57, 109, 164, 0.15);
}

.iepa-navbar .nextui-dropdown-item {
  color: var(--iepa-text-primary);
}

.iepa-navbar .nextui-dropdown-item:hover {
  background-color: var(--iepa-gray-50);
  color: var(--iepa-primary-blue);
}

/* Desktop Navigation Menu Items - High Contrast Hover Effects */
.desktop-register-link:hover,
#nav-about-link:hover,
#nav-contact-link:hover {
  background-color: #1b4f72 !important;
  color: white !important;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  transform: scale(1.02) !important;
}

.desktop-register-link:focus,
#nav-about-link:focus,
#nav-contact-link:focus {
  background-color: #1b4f72 !important;
  color: white !important;
  box-shadow: 0 0 0 2px #1b4f72 !important;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}

/* Register Trigger Button - Enhanced Hover */
#nav-register-trigger:hover {
  background-color: var(--iepa-secondary-green) !important;
  color: white !important;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  transform: scale(1.02) !important;
}

/* Navigation Button Styling - IEPA Brand Compliance */
.iepa-nav-login-btn {
  background-color: white;
  color: var(--iepa-primary-blue);
  border: 1px solid var(--iepa-primary-blue);
  font-weight: 500;
  transition: all 0.2s ease;
}

.iepa-nav-login-btn:hover {
  background-color: var(--iepa-primary-blue);
  color: white;
  border-color: var(--iepa-primary-blue);
}

.iepa-nav-login-btn:focus {
  outline: 2px solid var(--iepa-primary-blue);
  outline-offset: 2px;
  box-shadow:
    0 0 0 2px white,
    0 0 0 4px var(--iepa-primary-blue);
}

.iepa-nav-signup-btn {
  background-color: var(--iepa-secondary-green);
  color: white;
  border: 1px solid var(--iepa-secondary-green);
  font-weight: 600;
  transition: all 0.2s ease;
}

.iepa-nav-signup-btn:hover {
  background-color: var(--iepa-secondary-green-dark);
  border-color: var(--iepa-secondary-green-dark);
}

.iepa-nav-signup-btn:focus {
  outline: 2px solid var(--iepa-secondary-green);
  outline-offset: 2px;
  box-shadow:
    0 0 0 2px white,
    0 0 0 4px var(--iepa-secondary-green);
}

/* User Dropdown Styling - IEPA Brand Compliance */
.iepa-user-dropdown-content {
  background: white;
  border: 1px solid var(--iepa-gray-200);
  box-shadow: 0 4px 12px rgba(27, 79, 114, 0.15);
}

/* Ensure all dropdown menus have white backgrounds */
[data-slot="dropdown-menu-content"],
[data-slot="select-content"],
[data-slot="navigation-menu-viewport"],
[data-slot="dropdown-menu-sub-content"],
.nextui-dropdown-menu,
.nextui-popover-content,
[role="listbox"],
[role="menu"] {
  background: white !important;
  background-color: white !important;
}

.iepa-user-dropdown-item {
  color: var(--iepa-primary-blue);
  transition: all 0.2s ease;
}

.iepa-user-dropdown-item:hover {
  background-color: var(--iepa-gray-50);
  color: var(--iepa-primary-blue-dark);
}

.iepa-user-dropdown-item:focus {
  background-color: var(--iepa-gray-50);
  color: var(--iepa-primary-blue-dark);
  outline: 2px solid var(--iepa-primary-blue);
  outline-offset: -2px;
}

.iepa-user-dropdown-label {
  color: var(--iepa-text-secondary);
}

.iepa-user-dropdown-separator {
  border-color: var(--iepa-gray-200);
}

/* Mobile Navigation Menu - Enhanced IEPA Branding */
.iepa-navbar .nextui-navbar-menu {
  background: var(--iepa-primary-blue-dark);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Mobile Navigation Toggle Button - Enhanced Accessibility */
#mobile-menu-toggle {
  /* Ensure minimum 44px touch target */
  min-height: 44px !important;
  min-width: 44px !important;
  /* Enhanced contrast for IEPA branding */
  color: var(--iepa-primary-blue-dark) !important;
  border: 2px solid transparent;
  background-color: transparent;
}

#mobile-menu-toggle:hover {
  background-color: var(--iepa-primary-blue) !important;
  color: white !important;
  border-color: var(--iepa-primary-blue) !important;
  transform: scale(1.05);
}

#mobile-menu-toggle:focus {
  outline: 2px solid var(--iepa-primary-blue) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(27, 79, 114, 0.2) !important;
}

#mobile-menu-toggle:active {
  transform: scale(0.98);
  background-color: var(--iepa-primary-blue-dark) !important;
}

/* Mobile Navigation Links - Enhanced Touch Targets */
.mobile-nav-link,
.mobile-register-link,
.mobile-auth-login,
.mobile-auth-signup {
  /* Ensure minimum 44px touch target height */
  min-height: 44px !important;
  /* Enhanced text contrast for accessibility */
  color: white !important;
  /* Smooth transitions for better UX */
  transition: all 0.2s ease-in-out !important;
}

.mobile-nav-link:hover,
.mobile-register-link:hover,
.mobile-auth-login:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  /* Subtle shadow for depth */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.mobile-nav-link:focus,
.mobile-register-link:focus,
.mobile-auth-login:focus,
.mobile-auth-signup:focus {
  outline: 2px solid white !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.3) !important;
}

.mobile-nav-link:active,
.mobile-register-link:active,
.mobile-auth-login:active {
  background-color: rgba(255, 255, 255, 0.3) !important;
  transform: scale(0.98);
}

/* Mobile Auth Signup Button - Special Styling */
.mobile-auth-signup {
  background-color: white !important;
  color: var(--iepa-primary-blue) !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.mobile-auth-signup:hover {
  background-color: var(--iepa-gray-100) !important;
  color: var(--iepa-primary-blue-dark) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.mobile-auth-signup:active {
  background-color: var(--iepa-gray-200) !important;
  transform: scale(0.98);
}

/* Dark mode navigation - only when explicitly set */
.dark .iepa-navbar {
  background: var(--iepa-primary-blue-dark);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dark .iepa-navbar .nextui-dropdown-menu {
  background: var(--iepa-gray-800);
  border: 1px solid var(--iepa-gray-700);
}

.dark .iepa-navbar .nextui-dropdown-item {
  color: var(--iepa-gray-200);
}

.dark .iepa-navbar .nextui-dropdown-item:hover {
  background-color: var(--iepa-gray-700);
  color: white;
}

/* Button Variants */
.iepa-btn-primary {
  background-color: var(--iepa-primary-blue);
  border-color: var(--iepa-primary-blue);
  color: var(--iepa-text-white);
}

.iepa-btn-primary:hover {
  background-color: var(--iepa-primary-blue-dark);
  border-color: var(--iepa-primary-blue-dark);
}

.iepa-btn-secondary {
  background-color: var(--iepa-secondary-green);
  border-color: var(--iepa-secondary-green);
  color: var(--iepa-text-white);
}

.iepa-btn-secondary:hover {
  background-color: var(--iepa-secondary-green-dark);
  border-color: var(--iepa-secondary-green-dark);
}

/* Alternative Button - White background with IEPA blue text */
.iepa-btn-alt {
  background-color: var(--iepa-text-white);
  border: 2px solid var(--iepa-text-white);
  color: var(--iepa-primary-blue);
  font-weight: 600;
  transition: all 0.2s ease;
}

.iepa-btn-alt:hover {
  background-color: var(--iepa-primary-blue);
  border-color: var(--iepa-primary-blue);
  color: var(--iepa-text-white);
}

/* Card Styling */
.iepa-card-featured {
  border: 2px solid var(--iepa-primary-blue);
  box-shadow: 0 4px 12px rgba(57, 109, 164, 0.15);
}

.iepa-card-highlight {
  background: linear-gradient(
    135deg,
    var(--iepa-primary-blue) 0%,
    var(--iepa-secondary-green) 100%
  );
  color: var(--iepa-text-white);
}

/* Hero Section */
.iepa-hero {
  background: linear-gradient(
    135deg,
    var(--iepa-primary-blue) 0%,
    var(--iepa-accent-teal) 100%
  );
  color: var(--iepa-text-white);
}

.iepa-hero-overlay {
  background: rgba(27, 79, 114, 0.8);
}

/* Image Hero Section */
.iepa-hero-image-section {
  position: relative;
  min-height: 50vh;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  color: var(--iepa-text-white);
  overflow: hidden;
  background-attachment: fixed;
}

.iepa-hero-image-section .iepa-heading-1 {
  color: var(--iepa-text-white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.iepa-hero-image-section .iepa-body-large,
.iepa-hero-image-section .iepa-body {
  color: var(--iepa-text-white);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* Responsive adjustments for hero image */
@media (max-width: 768px) {
  .iepa-hero-image-section {
    background-attachment: scroll;
    min-height: 40vh;
  }
}

/* Video Hero Section (Legacy - can be removed if not used) */
.iepa-hero-video-section {
  position: relative;
  min-height: 50vh;
  color: var(--iepa-text-white);
  overflow: hidden;
}

/* Section Backgrounds */
.iepa-section-accent {
  background-color: var(--iepa-bg-secondary);
}

.iepa-section-primary {
  background: var(--iepa-primary-blue);
  color: var(--iepa-text-white);
}

/* Chip/Badge Colors */
.iepa-chip-primary {
  background-color: var(--iepa-primary-blue);
  color: var(--iepa-text-white);
}

.iepa-chip-secondary {
  background-color: var(--iepa-secondary-green);
  color: var(--iepa-text-white);
}

.iepa-chip-accent {
  background-color: var(--iepa-accent-teal);
  color: var(--iepa-text-white);
}

/* Status Messages */
.iepa-status-success {
  background-color: rgba(40, 167, 69, 0.1);
  border-left: 4px solid var(--iepa-success);
  color: var(--iepa-success);
  padding: 12px 16px;
  border-radius: 4px;
}

.iepa-status-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 4px solid var(--iepa-warning);
  color: #856404;
  padding: 12px 16px;
  border-radius: 4px;
}

.iepa-status-error {
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 4px solid var(--iepa-danger);
  color: var(--iepa-danger);
  padding: 12px 16px;
  border-radius: 4px;
}

.iepa-status-info {
  background-color: rgba(23, 162, 184, 0.1);
  border-left: 4px solid var(--iepa-info);
  color: var(--iepa-info);
  padding: 12px 16px;
  border-radius: 4px;
}

/* Highlight Icons */
.iepa-highlight-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(
    135deg,
    var(--iepa-primary-blue),
    var(--iepa-secondary-green)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--iepa-text-white);
  margin: 0 auto 16px;
}

/* Breadcrumbs Styling */
.iepa-breadcrumbs-container {
  background-color: var(--iepa-bg-secondary);
  border-bottom: 1px solid var(--iepa-gray-200);
  min-height: 48px;
  display: flex;
  align-items: center;
}

/* Navigation Breadcrumbs - Compact version for navigation header */
.iepa-nav-breadcrumbs .iepa-breadcrumbs-container {
  background-color: transparent;
  border-bottom: none;
  min-height: auto;
  padding: 0;
}

.iepa-nav-breadcrumbs [data-slot='breadcrumb'] {
  padding: 0;
}

.iepa-nav-breadcrumbs [data-slot='breadcrumb-list'] {
  font-size: 0.75rem;
  gap: 0.5rem;
}

.iepa-nav-breadcrumbs [data-slot='breadcrumb-item'] {
  color: var(--iepa-primary-blue-dark);
}

.iepa-nav-breadcrumbs [data-slot='breadcrumb-link']:hover {
  color: var(--iepa-primary-blue);
}

.iepa-nav-breadcrumbs [data-slot='breadcrumb-separator'] {
  color: var(--iepa-gray-400);
}

.iepa-breadcrumbs-container .nextui-breadcrumbs {
  margin: 0;
}

.iepa-breadcrumbs-container .nextui-breadcrumbs-item {
  font-size: 0.875rem;
  font-weight: 400;
}

.iepa-breadcrumbs-container .nextui-breadcrumbs-item:hover {
  color: var(--iepa-primary-blue-dark);
}

.iepa-breadcrumbs-container .nextui-breadcrumbs-separator {
  color: var(--iepa-gray-400);
  margin: 0 8px;
}

/* Dark mode breadcrumbs */
.dark .iepa-breadcrumbs-container {
  background-color: var(--iepa-gray-800);
  border-bottom: 1px solid var(--iepa-gray-700);
}

.dark .iepa-breadcrumbs-container .nextui-breadcrumbs-item {
  color: var(--iepa-gray-300);
}

.dark .iepa-breadcrumbs-container .nextui-breadcrumbs-item:hover {
  color: var(--iepa-accent-teal-light);
}

.dark .iepa-breadcrumbs-container .nextui-breadcrumbs-separator {
  color: var(--iepa-gray-500);
}

/* Scrollspy Navigation Styling */
.iepa-scrollspy-desktop {
  /* Ensure it doesn't interfere with other fixed elements */
  z-index: 30;
}

.iepa-scrollspy-desktop-container {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.iepa-scrollspy-desktop-item {
  /* Enhanced hover effects */
  transition: all 0.2s ease-out;
}

.iepa-scrollspy-desktop-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(27, 79, 114, 0.2);
}

.iepa-scrollspy-mobile {
  /* Mobile sticky navigation */
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95);
}

.iepa-scrollspy-mobile-item {
  /* Smooth transitions for mobile items */
  transition: all 0.2s ease-out;
  min-width: fit-content;
}

/* Hide scrollbar for mobile horizontal scroll */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Footer Styling */
.iepa-footer {
  background-image: url(/pine_bg.svg);
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  flex-direction: column;
  justify-content: flex-end;
  min-height: 800px;
  margin-top: auto;
  padding-top: 8rem;
  padding-bottom: 0;
  display: flex;
  position: relative;
}

.iepa-footer-content {
  z-index: 2;
  background-color: #325666;
  margin-top: auto;
  padding: 2rem 0 1.5rem;
  position: relative;
}

.iepa-footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

@media (min-width: 640px) {
  .iepa-footer-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .iepa-footer-grid {
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
  }
}

.iepa-footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.iepa-footer-logo-link {
  display: inline-block;
  transition: opacity 0.2s ease;
}

.iepa-footer-logo-link:hover {
  opacity: 0.8;
}

.iepa-footer-logo {
  height: 40px;
  width: auto;
  filter: brightness(0) saturate(100%) invert(100%);
}

.iepa-footer-description {
  font-size: 0.875rem;
  line-height: 1.5;
  color: #ffffff;
  max-width: 300px;
}

.iepa-footer-conference {
  font-size: 0.75rem;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.iepa-footer-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.iepa-footer-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.iepa-footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.iepa-footer-link {
  font-size: 0.875rem;
  color: #ffffff;
  text-decoration: none;
  transition: color 0.2s ease;
  line-height: 1.4;
}

.iepa-footer-link:hover {
  color: #d2effc;
  text-decoration: underline;
}

.iepa-footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 2rem;
  padding-top: 1.5rem;
}

.iepa-footer-bottom-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  text-align: center;
}

@media (min-width: 640px) {
  .iepa-footer-bottom-content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

.iepa-footer-copyright-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

@media (min-width: 640px) {
  .iepa-footer-copyright-section {
    flex-direction: row;
    gap: 1rem;
    align-items: center;
  }
}

.iepa-footer-copyright {
  font-size: 0.75rem;
  color: #ffffff;
  margin: 0;
}

.iepa-footer-version {
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.025em;
}

.iepa-footer-bottom-links {
  display: flex;
  gap: 1.5rem;
}

.iepa-footer-bottom-link {
  font-size: 0.75rem;
  color: #ffffff;
  text-decoration: none;
  transition: color 0.2s ease;
}

.iepa-footer-bottom-link:hover {
  color: #d2effc;
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .iepa-logo {
    height: 28px;
  }

  .iepa-highlight-icon {
    width: 56px;
    height: 56px;
    font-size: 20px;
  }

  .iepa-breadcrumbs-container {
    min-height: 40px;
  }

  .iepa-breadcrumbs-container .nextui-breadcrumbs-item {
    font-size: 0.8rem;
  }

  /* Mobile navigation breadcrumbs */
  .iepa-nav-breadcrumbs [data-slot='breadcrumb-list'] {
    font-size: 0.7rem;
    gap: 0.25rem;
  }

  .iepa-nav-breadcrumbs [data-slot='breadcrumb-item'] {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* Mobile Navigation Enhancements */
  #mobile-menu-toggle {
    /* Larger touch target on smaller screens */
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px !important;
  }

  /* Mobile navigation menu adjustments */
  #mobile-navigation-menu {
    /* Ensure menu doesn't exceed viewport on small screens */
    max-width: 90vw !important;
  }

  .mobile-nav-link,
  .mobile-register-link,
  .mobile-auth-login,
  .mobile-auth-signup {
    /* Enhanced touch targets for mobile */
    min-height: 48px !important;
    padding: 12px 16px !important;
    font-size: 16px !important; /* Prevent zoom on iOS */
  }

  /* Mobile navigation text sizing */
  #mobile-menu-title {
    font-size: 1.125rem !important; /* 18px */
  }

  #mobile-register-heading {
    font-size: 0.875rem !important; /* 14px */
  }
}

/* Tablet-specific adjustments (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Ensure navigation toggle is hidden on tablet */
  #mobile-menu-toggle {
    display: none !important;
  }

  /* Desktop navigation should be visible on tablet */
  #desktop-navigation-section {
    display: flex !important;
  }
}

/* Small mobile devices (max-width: 375px) */
@media (max-width: 375px) {
  #mobile-navigation-menu {
    /* Full width on very small screens */
    width: 100vw !important;
    max-width: 100vw !important;
  }

  .mobile-nav-link,
  .mobile-register-link,
  .mobile-auth-login,
  .mobile-auth-signup {
    /* Compact padding for small screens */
    padding: 10px 12px !important;
    font-size: 15px !important;
  }

  #mobile-menu-title {
    font-size: 1rem !important; /* 16px */
  }
}

/* Footer responsive adjustments */
@media (max-width: 768px) {
  .iepa-footer {
    min-height: 600px;
    padding-top: 5rem;
    background-position: 50% -500px;
  }

  .iepa-footer-content {
    padding: 1.5rem 0 1rem 0;
  }

  .iepa-footer-grid {
    gap: 1.5rem;
  }

  .iepa-footer-description {
    max-width: 100%;
  }

  .iepa-footer-bottom {
    margin-top: 1.5rem;
    padding-top: 1rem;
  }
}
