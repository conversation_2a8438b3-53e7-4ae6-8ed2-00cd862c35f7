'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { FiUpload, FiCheck, FiX } from 'react-icons/fi';

export default function TestUploadPage() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      addTestResult(`✅ File selected: ${file.name} (${Math.round(file.size / 1024)} KB, ${file.type})`);
      console.log('File selected:', file.name, file.size, file.type);
    } else {
      addTestResult('❌ No file selected');
    }
  };

  const handleButtonClick = () => {
    addTestResult('🔄 Button clicked, attempting to trigger file dialog...');
    console.log('Button clicked, triggering file input');

    if (!fileInputRef.current) {
      addTestResult('❌ File input ref is null');
      return;
    }

    try {
      fileInputRef.current.click();
      addTestResult('✅ File input click() called successfully');
    } catch (error) {
      addTestResult(`❌ Error calling click(): ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    setSelectedFile(null);
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">File Dialog Test Page</h1>

      <div className="space-y-6">
        {/* Simple File Test */}
        <div className="p-6 bg-blue-50 rounded-lg border">
          <h2 className="text-xl font-semibold mb-4">🧪 File Dialog Test</h2>
          <p className="text-gray-700 mb-4">
            Click the button below to test if the file dialog opens. This tests the basic file input functionality.
          </p>

          <div className="space-y-4">
            <div className="flex gap-4 items-center">
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                accept=".pdf,.jpg,.jpeg,.png,.webp,.ppt,.pptx,.doc,.docx"
                onChange={handleFileSelect}
              />
              <Button
                onClick={handleButtonClick}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <FiUpload className="w-4 h-4 mr-2" />
                Open File Dialog
              </Button>

              <Button
                onClick={clearResults}
                variant="outline"
              >
                Clear Results
              </Button>
            </div>

            {selectedFile && (
              <div className="p-4 bg-green-100 border border-green-300 rounded">
                <div className="flex items-center gap-2 mb-2">
                  <FiCheck className="w-5 h-5 text-green-600" />
                  <span className="font-semibold text-green-800">File Selected Successfully!</span>
                </div>
                <div className="text-sm text-green-700 space-y-1">
                  <p><strong>Name:</strong> {selectedFile.name}</p>
                  <p><strong>Size:</strong> {Math.round(selectedFile.size / 1024)} KB</p>
                  <p><strong>Type:</strong> {selectedFile.type}</p>
                  <p><strong>Last Modified:</strong> {new Date(selectedFile.lastModified).toLocaleString()}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Alternative Methods */}
        <div className="p-6 bg-yellow-50 rounded-lg border">
          <h2 className="text-xl font-semibold mb-4">🔧 Alternative File Input Methods</h2>

          <div className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Method 1: Direct File Input (Visible)</h3>
              <input
                type="file"
                accept=".pdf,.jpg,.jpeg,.png,.webp,.ppt,.pptx,.doc,.docx"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    addTestResult(`✅ Direct input - File selected: ${file.name}`);
                  }
                }}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>

            <div>
              <h3 className="font-semibold mb-2">Method 2: Label-wrapped Input</h3>
              <label className="inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md cursor-pointer hover:bg-green-700 transition-colors">
                <FiUpload className="w-4 h-4 mr-2" />
                Choose File (Label Method)
                <input
                  type="file"
                  className="hidden"
                  accept=".pdf,.jpg,.jpeg,.png,.webp,.ppt,.pptx,.doc,.docx"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      addTestResult(`✅ Label method - File selected: ${file.name}`);
                    }
                  }}
                />
              </label>
            </div>
          </div>
        </div>

        {/* Test Results */}
        <div className="p-6 bg-gray-50 rounded-lg border">
          <h2 className="text-xl font-semibold mb-4">📋 Test Results</h2>
          {testResults.length === 0 ? (
            <p className="text-gray-500 italic">No test results yet. Try clicking the buttons above.</p>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono bg-white p-2 rounded border">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Browser Info */}
        <div className="p-6 bg-purple-50 rounded-lg border">
          <h2 className="text-xl font-semibold mb-4">🌐 Browser Information</h2>
          <div className="text-sm space-y-1">
            <p><strong>User Agent:</strong> {navigator.userAgent}</p>
            <p><strong>Platform:</strong> {navigator.platform}</p>
            <p><strong>Language:</strong> {navigator.language}</p>
            <p><strong>Cookies Enabled:</strong> {navigator.cookieEnabled ? 'Yes' : 'No'}</p>
            <p><strong>Online:</strong> {navigator.onLine ? 'Yes' : 'No'}</p>
          </div>
        </div>

        {/* Instructions */}
        <div className="p-6 bg-orange-50 rounded-lg border">
          <h2 className="text-xl font-semibold mb-4">📝 Testing Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>Click the "Open File Dialog" button above</li>
            <li>If the file dialog opens, select any file (PDF, image, or document)</li>
            <li>Check if the file information appears in the green box</li>
            <li>Try the alternative methods if the main button doesn't work</li>
            <li>Check the test results section for detailed logs</li>
            <li>If none of the methods work, check browser console for errors</li>
          </ol>

          <div className="mt-4 p-3 bg-orange-100 rounded">
            <p className="text-sm"><strong>Note:</strong> If file dialogs don't open, this could be due to:</p>
            <ul className="list-disc list-inside text-xs mt-2 space-y-1">
              <li>Browser security restrictions</li>
              <li>Ad blockers or browser extensions</li>
              <li>JavaScript errors in the console</li>
              <li>Browser popup blockers</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
