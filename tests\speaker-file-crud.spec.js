const { test, expect } = require('@playwright/test');
const path = require('path');
const fs = require('fs');

// Test configuration
const BASE_URL = 'http://localhost:6969';
const TEST_SPEAKER = {
  firstName: 'Dr. File',
  lastName: 'Tester',
  email: '<EMAIL>',
  phone: '555-FILE-TEST',
  organization: 'File Testing Institute',
  jobTitle: 'Senior File Engineer',
  bio: 'Dr. File Tester specializes in comprehensive file management testing for conference registration systems.',
  nameOnBadge: 'Dr. File Tester',
  streetAddress: '123 File Test Lane',
  city: 'Sacramento',
  state: 'CA',
  zipCode: '95814'
};

// Test file paths (relative to test directory)
const TEST_FILES = {
  validPdf: path.join(__dirname, 'test-files', 'sample-presentation.pdf'),
  validImage: path.join(__dirname, 'test-files', 'sample-headshot.jpg'),
  largePdf: path.join(__dirname, 'test-files', 'large-presentation.pdf'),
  largeImage: path.join(__dirname, 'test-files', 'large-headshot.jpg'),
  invalidFile: path.join(__dirname, 'test-files', 'invalid-file.txt'),
  oversizedPdf: path.join(__dirname, 'test-files', 'oversized-presentation.pdf'),
  oversizedImage: path.join(__dirname, 'test-files', 'oversized-headshot.jpg')
};

test.describe('Speaker File CRUD Operations', () => {
  let speakerId;
  let userEmail;

  test.beforeAll(async () => {
    // Create test files if they don't exist
    await createTestFiles();
  });

  test.beforeEach(async ({ page }) => {
    // Navigate to speaker registration and create a test speaker
    await page.goto(`${BASE_URL}/register/speaker`);
    await page.waitForLoadState('networkidle');
    
    // Generate unique email for this test run
    userEmail = `file-test-${Date.now()}@iepa-test.com`;
    
    console.log(`🧪 Creating test speaker with email: ${userEmail}`);
  });

  test.afterEach(async ({ page }) => {
    // Clean up: delete test speaker and files if created
    if (speakerId) {
      console.log(`🧹 Cleaning up test speaker: ${speakerId}`);
      // Note: In a real test, you'd call cleanup API endpoints here
    }
  });

  test('CREATE: Should upload valid presentation file', async ({ page }) => {
    console.log('🧪 Testing CREATE operation for presentation file');
    
    // First complete speaker registration
    await completeBasicSpeakerRegistration(page, userEmail);
    
    // Navigate to speaker profile or file management page
    await navigateToSpeakerFileManagement(page);
    
    // Test presentation file upload
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(TEST_FILES.validPdf);
    
    // Wait for upload to complete
    await expect(page.locator('text=Upload successful')).toBeVisible({ timeout: 30000 });
    
    // Verify file appears in UI
    await expect(page.locator('text=sample-presentation.pdf')).toBeVisible();
    
    console.log('✅ Presentation file upload successful');
  });

  test('CREATE: Should upload valid headshot image', async ({ page }) => {
    console.log('🧪 Testing CREATE operation for headshot image');
    
    await completeBasicSpeakerRegistration(page, userEmail);
    await navigateToSpeakerFileManagement(page);
    
    // Test headshot upload
    const headshotInput = page.locator('input[type="file"]').nth(1);
    await headshotInput.setInputFiles(TEST_FILES.validImage);
    
    await expect(page.locator('text=Upload successful')).toBeVisible({ timeout: 30000 });
    await expect(page.locator('text=sample-headshot.jpg')).toBeVisible();
    
    console.log('✅ Headshot image upload successful');
  });

  test('CREATE: Should reject oversized presentation file', async ({ page }) => {
    console.log('🧪 Testing file size validation for presentation');
    
    await completeBasicSpeakerRegistration(page, userEmail);
    await navigateToSpeakerFileManagement(page);
    
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(TEST_FILES.oversizedPdf);
    
    // Should show error message
    await expect(page.locator('text=File size exceeds maximum')).toBeVisible({ timeout: 10000 });
    
    console.log('✅ File size validation working correctly');
  });

  test('CREATE: Should reject invalid file types', async ({ page }) => {
    console.log('🧪 Testing file type validation');
    
    await completeBasicSpeakerRegistration(page, userEmail);
    await navigateToSpeakerFileManagement(page);
    
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(TEST_FILES.invalidFile);
    
    await expect(page.locator('text=Invalid file type')).toBeVisible({ timeout: 10000 });
    
    console.log('✅ File type validation working correctly');
  });

  test('READ: Should display uploaded files correctly', async ({ page }) => {
    console.log('🧪 Testing READ operation for uploaded files');
    
    await completeBasicSpeakerRegistration(page, userEmail);
    await navigateToSpeakerFileManagement(page);
    
    // Upload a file first
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(TEST_FILES.validPdf);
    await expect(page.locator('text=Upload successful')).toBeVisible({ timeout: 30000 });
    
    // Verify file is displayed
    await expect(page.locator('text=sample-presentation.pdf')).toBeVisible();
    
    // Test download link
    const downloadLink = page.locator('a[href*="sample-presentation.pdf"]');
    await expect(downloadLink).toBeVisible();
    
    console.log('✅ File display and download link working correctly');
  });

  test('UPDATE: Should replace existing file', async ({ page }) => {
    console.log('🧪 Testing UPDATE operation - file replacement');
    
    await completeBasicSpeakerRegistration(page, userEmail);
    await navigateToSpeakerFileManagement(page);
    
    // Upload initial file
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(TEST_FILES.validPdf);
    await expect(page.locator('text=Upload successful')).toBeVisible({ timeout: 30000 });
    
    // Replace with new file
    await fileInput.setInputFiles(TEST_FILES.largePdf);
    await expect(page.locator('text=Upload successful')).toBeVisible({ timeout: 30000 });
    
    // Verify new file is displayed
    await expect(page.locator('text=large-presentation.pdf')).toBeVisible();
    
    console.log('✅ File replacement working correctly');
  });

  test('DELETE: Should remove uploaded file', async ({ page }) => {
    console.log('🧪 Testing DELETE operation');
    
    await completeBasicSpeakerRegistration(page, userEmail);
    await navigateToSpeakerFileManagement(page);
    
    // Upload a file first
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(TEST_FILES.validPdf);
    await expect(page.locator('text=Upload successful')).toBeVisible({ timeout: 30000 });
    
    // Find and click delete/remove button
    const removeButton = page.locator('button:has-text("Remove"), button:has-text("Delete")').first();
    await removeButton.click();
    
    // Confirm deletion if modal appears
    const confirmButton = page.locator('button:has-text("Confirm"), button:has-text("Yes")');
    if (await confirmButton.isVisible()) {
      await confirmButton.click();
    }
    
    // Verify file is removed
    await expect(page.locator('text=sample-presentation.pdf')).not.toBeVisible();
    
    console.log('✅ File deletion working correctly');
  });

  test('SECURITY: Should prevent unauthorized file access', async ({ page }) => {
    console.log('🧪 Testing security - unauthorized access prevention');
    
    // This test would require creating two different users
    // and verifying that one cannot access the other's files
    // Implementation depends on your authentication system
    
    console.log('⚠️ Security test requires multi-user setup - placeholder test');
  });
});

// Helper functions
async function createTestFiles() {
  const testFilesDir = path.join(__dirname, 'test-files');
  
  // Create test files directory if it doesn't exist
  if (!fs.existsSync(testFilesDir)) {
    fs.mkdirSync(testFilesDir, { recursive: true });
  }
  
  // Create sample PDF content (minimal PDF structure)
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
>>
endobj
xref
0 4
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
trailer
<<
/Size 4
/Root 1 0 R
>>
startxref
190
%%EOF`;
  
  // Create test files
  const files = {
    'sample-presentation.pdf': pdfContent,
    'large-presentation.pdf': pdfContent + '\n'.repeat(1000),
    'sample-headshot.jpg': 'fake-jpg-content-for-testing',
    'large-headshot.jpg': 'fake-jpg-content-for-testing' + 'x'.repeat(1000),
    'invalid-file.txt': 'This is not a valid presentation file',
    'oversized-presentation.pdf': pdfContent + 'x'.repeat(52428800), // > 50MB
    'oversized-headshot.jpg': 'fake-jpg-content' + 'x'.repeat(5242880) // > 5MB
  };
  
  for (const [filename, content] of Object.entries(files)) {
    const filePath = path.join(testFilesDir, filename);
    if (!fs.existsSync(filePath)) {
      fs.writeFileSync(filePath, content);
    }
  }
  
  console.log('✅ Test files created successfully');
}

async function completeBasicSpeakerRegistration(page, email) {
  // Fill out basic speaker registration form
  await page.fill('input[placeholder="Enter your first name"]', TEST_SPEAKER.firstName);
  await page.fill('input[placeholder="Enter your last name"]', TEST_SPEAKER.lastName);
  await page.fill('input[placeholder="<EMAIL>"]', email);
  await page.fill('input[placeholder="Enter your phone number"]', TEST_SPEAKER.phone);
  await page.fill('input[placeholder="Enter your organization name"]', TEST_SPEAKER.organization);
  await page.fill('input[placeholder="Enter your job title"]', TEST_SPEAKER.jobTitle);
  await page.fill('textarea[placeholder*="bio"]', TEST_SPEAKER.bio);
  
  // Submit form
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  
  console.log('✅ Basic speaker registration completed');
}

async function navigateToSpeakerFileManagement(page) {
  // Navigate to file management section
  // This depends on your UI structure - adjust selectors as needed
  const fileSection = page.locator('h2:has-text("File"), h3:has-text("Upload"), text=Presentation');
  if (await fileSection.isVisible()) {
    await fileSection.scrollIntoViewIfNeeded();
  }
  
  console.log('✅ Navigated to file management section');
}
