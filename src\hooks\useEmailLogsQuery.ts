'use client';

import React, { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  EmailLog,
  EmailStats,
  EmailLogFilters,
  getDefaultFilters,
  buildEmailLogQuery,
} from '@/lib/email-log-utils';

interface EmailLogsResponse {
  success: boolean;
  logs: EmailLog[];
  statistics: EmailStats;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
  timestamp: string;
}

// Query keys for React Query
const emailLogsKeys = {
  all: ['emailLogs'] as const,
  lists: () => [...emailLogsKeys.all, 'list'] as const,
  list: (filters: EmailLogFilters) => [...emailLogsKeys.lists(), filters] as const,
  stats: () => [...emailLogsKeys.all, 'stats'] as const,
};

// Fetch function for email logs
async function fetchEmailLogs(filters: EmailLogFilters): Promise<EmailLogsResponse> {
  const queryParams = buildEmailLogQuery(filters);
  const response = await fetch(`/api/admin/email-logs?${queryParams}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch email logs');
  }
  
  return response.json();
}

// Send email function
async function sendEmail(emailData: any): Promise<any> {
  const response = await fetch('/api/admin/send-email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(emailData),
  });

  if (!response.ok) {
    throw new Error('Failed to send email');
  }

  return response.json();
}

// Delete email log function
async function deleteEmailLog(logId: string): Promise<any> {
  const response = await fetch(`/api/admin/email-logs?id=${logId}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    throw new Error('Failed to delete email log');
  }

  return response.json();
}

interface UseEmailLogsQueryReturn {
  // Data
  emailLogs: EmailLog[];
  emailStats: EmailStats;
  pagination: EmailLogsResponse['pagination'];
  
  // State
  loading: boolean;
  error: string | null;
  
  // Filters
  filters: EmailLogFilters;
  setFilters: (filters: Partial<EmailLogFilters>) => void;
  resetFilters: () => void;
  
  // Actions
  refetch: () => void;
  sendEmailMutation: any;
  deleteEmailMutation: any;
  
  // Utilities
  hasActiveFilters: boolean;
  totalResults: number;
  isStale: boolean;
}

export function useEmailLogsQuery(): UseEmailLogsQueryReturn {
  const [filters, setFiltersState] = useState<EmailLogFilters>(getDefaultFilters());
  const queryClient = useQueryClient();

  // Query for email logs
  const {
    data,
    isLoading,
    error,
    refetch,
    isStale,
  } = useQuery({
    queryKey: emailLogsKeys.list(filters),
    queryFn: () => fetchEmailLogs(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes for email logs
    gcTime: 5 * 60 * 1000, // 5 minutes
  });

  // Send email mutation
  const sendEmailMutation = useMutation({
    mutationFn: sendEmail,
    onSuccess: () => {
      // Invalidate and refetch email logs
      queryClient.invalidateQueries({ queryKey: emailLogsKeys.lists() });
      queryClient.invalidateQueries({ queryKey: emailLogsKeys.stats() });
    },
  });

  // Delete email mutation
  const deleteEmailMutation = useMutation({
    mutationFn: deleteEmailLog,
    onSuccess: () => {
      // Invalidate and refetch email logs
      queryClient.invalidateQueries({ queryKey: emailLogsKeys.lists() });
      queryClient.invalidateQueries({ queryKey: emailLogsKeys.stats() });
    },
  });

  const setFilters = useCallback((newFilters: Partial<EmailLogFilters>) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters,
      // Reset to page 1 when filters change (except when changing page)
      page: newFilters.page !== undefined ? newFilters.page : 1,
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState(getDefaultFilters());
  }, []);

  // Computed values
  const hasActiveFilters = 
    filters.status !== 'all' || 
    filters.emailType !== 'all' || 
    filters.search.trim() !== '';

  const emailLogs = data?.logs || [];
  const emailStats = data?.statistics || {
    totalSent: 0,
    totalFailed: 0,
    totalPending: 0,
    totalEmails: 0,
  };
  const pagination = data?.pagination || {
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
  };
  const totalResults = pagination.total;

  return {
    // Data
    emailLogs,
    emailStats,
    pagination,
    
    // State
    loading: isLoading,
    error: error?.message || null,
    
    // Filters
    filters,
    setFilters,
    resetFilters,
    
    // Actions
    refetch,
    sendEmailMutation,
    deleteEmailMutation,
    
    // Utilities
    hasActiveFilters,
    totalResults,
    isStale,
  };
}

// Hook for prefetching next page
export function usePrefetchEmailLogs() {
  const queryClient = useQueryClient();

  const prefetchNextPage = useCallback((currentFilters: EmailLogFilters) => {
    const nextPageFilters = {
      ...currentFilters,
      page: currentFilters.page + 1,
    };

    queryClient.prefetchQuery({
      queryKey: emailLogsKeys.list(nextPageFilters),
      queryFn: () => fetchEmailLogs(nextPageFilters),
      staleTime: 2 * 60 * 1000,
    });
  }, [queryClient]);

  return { prefetchNextPage };
}

// Hook for background refresh
export function useEmailLogsBackgroundRefresh(intervalMs: number = 30000) {
  const queryClient = useQueryClient();

  React.useEffect(() => {
    const interval = setInterval(() => {
      // Only refetch if the user is active and the page is visible
      if (document.visibilityState === 'visible') {
        queryClient.invalidateQueries({ queryKey: emailLogsKeys.lists() });
      }
    }, intervalMs);

    return () => clearInterval(interval);
  }, [queryClient, intervalMs]);
}
