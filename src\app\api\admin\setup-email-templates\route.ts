import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Create Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function POST() {
  try {
    console.log('[EMAIL-TEMPLATES-SETUP] Setting up email templates...');

    // First, try to check if the table exists by attempting to query it
    const { error: queryError } = await supabase
      .from('iepa_email_templates')
      .select('template_key')
      .limit(1);

    if (queryError) {
      console.log(
        '[EMAIL-TEMPLATES-SETUP] Table does not exist, please create it manually in Supabase SQL Editor'
      );
      return NextResponse.json(
        {
          success: false,
          error:
            'Email templates table does not exist. Please run the SQL script in Supabase SQL Editor first.',
          sqlScript: `
-- Run this in Supabase SQL Editor:

CREATE TABLE IF NOT EXISTS iepa_email_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_key VARCHAR(100) NOT NULL UNIQUE,
    template_name VARCHAR(255) NOT NULL,
    description TEXT,
    subject_template TEXT NOT NULL,
    html_template TEXT NOT NULL,
    text_template TEXT,
    variables JSONB DEFAULT '[]'::jsonb,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_iepa_email_templates_key ON iepa_email_templates(template_key);
CREATE INDEX IF NOT EXISTS idx_iepa_email_templates_active ON iepa_email_templates(is_active);

CREATE TABLE IF NOT EXISTS iepa_email_template_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_id UUID REFERENCES iepa_email_templates(id) ON DELETE CASCADE,
    template_key VARCHAR(100) NOT NULL,
    field_changed VARCHAR(50) NOT NULL,
    old_value TEXT,
    new_value TEXT NOT NULL,
    changed_by VARCHAR(255),
    change_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_iepa_email_template_log_template_id ON iepa_email_template_log(template_id);
CREATE INDEX IF NOT EXISTS idx_iepa_email_template_log_created_at ON iepa_email_template_log(created_at);
        `,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    console.log(
      '[EMAIL-TEMPLATES-SETUP] Tables exist, inserting default templates...'
    );

    // Insert default email templates
    const defaultTemplates = [
      {
        template_key: 'registration_confirmation',
        template_name: 'Registration Confirmation',
        description:
          'Email sent to confirm successful registration for the IEPA conference',
        subject_template:
          "Welcome to IEPA's 2025 Annual Meeting - Registration Confirmed!",
        html_template: `<div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
          <div style="background: #3A6CA5; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Independent Energy Producers Association</h1>
            <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">2025 Annual Meeting Registration Confirmed</p>
          </div>
          <hr style="color: #3A6CA5; border: 2px solid #3A6CA5; margin: 0;" />
          <div style="padding: 30px;">
            <h2 style="color: #3A6CA5; margin-top: 0;">Dear {{name}},</h2>
            <p>Thank you for registering for IEPA's 2025 Annual Meeting as a <strong>{{registrationType}}</strong>. We are putting together another outstanding program and look forward to your participation.</p>
            {{#confirmationNumber}}
            <div style="background: #f8f9fa; border-left: 4px solid #3A6CA5; padding: 15px; margin: 20px 0;">
              <p style="margin: 0;"><strong>Confirmation Number:</strong> {{confirmationNumber}}</p>
            </div>
            {{/confirmationNumber}}
            {{roleSpecificContent}}
          </div>
          <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
              Independent Energy Producers Association<br>
              California's oldest nonprofit trade association representing independent energy facilities
            </p>
          </div>
        </div>`,
        text_template: `Dear {{name}},

Thank you for registering for IEPA's 2025 Annual Meeting as a {{registrationType}}. We are putting together another outstanding program and look forward to your participation.

{{#confirmationNumber}}
Confirmation Number: {{confirmationNumber}}
{{/confirmationNumber}}

Best regards,
The IEPA Conference Team

Independent Energy Producers Association
California's oldest nonprofit trade association representing independent energy facilities`,
        variables: [
          'name',
          'registrationType',
          'confirmationNumber',
          'roleSpecificContent',
        ],
      },
      {
        template_key: 'password_reset',
        template_name: 'Password Reset',
        description: 'Email sent when user requests password reset',
        subject_template: 'Reset Your Password - IEPA Conference 2025',
        html_template: `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Password Reset Request</h2>
          <p>You requested to reset your password for your IEPA Conference account.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{resetUrl}}" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p>This link will expire in 24 hours for security reasons.</p>
          <p>If you didn't request this reset, you can safely ignore this email.</p>
          
          <p>Best regards,<br>
          The IEPA Conference Team</p>
        </div>`,
        text_template: `Password Reset Request

You requested to reset your password for your IEPA Conference account.

Reset your password by visiting: {{resetUrl}}

This link will expire in 24 hours for security reasons.
If you didn't request this reset, you can safely ignore this email.

Best regards,
The IEPA Conference Team`,
        variables: ['resetUrl'],
      },
      {
        template_key: 'sponsor_confirmation',
        template_name: 'Sponsor Registration Confirmation',
        description:
          'Email sent to confirm successful sponsor registration for the IEPA conference',
        subject_template:
          'IEPA 2025 Annual Meeting - {{sponsorshipLevel}} Sponsorship Confirmed',
        html_template: `<div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
          <div style="background: #3A6CA5; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Independent Energy Producers Association</h1>
            <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">2025 Annual Meeting - Sponsorship Confirmation</p>
          </div>
          <hr style="color: #3A6CA5; border: 2px solid #3A6CA5; margin: 0;" />
          <div style="padding: 30px;">
            <h2 style="color: #3A6CA5; margin-top: 0;">Dear {{name}},</h2>
            <p>Thank you for your {{sponsorshipLevel}} sponsorship of IEPA's 2025 Annual Meeting! We are thrilled to have {{organizationName}} as a valued partner for this important industry event.</p>
            <div style="background: #f8f9fa; padding: 20px; border-left: 4px solid #3A6CA5; margin: 20px 0;">
              <h3 style="color: #3A6CA5; margin-top: 0;">Sponsorship Details</h3>
              <p><strong>Organization:</strong> {{organizationName}}</p>
              <p><strong>Sponsorship Level:</strong> {{sponsorshipLevel}}</p>
              <p><strong>Confirmation Number:</strong> {{confirmationNumber}}</p>
              <p><strong>Registration Date:</strong> {{registrationDate}}</p>
              {{#paymentAmount}}<p><strong>Sponsorship Amount:</strong> {{paymentAmount}}</p>{{/paymentAmount}}
            </div>
            {{#sponsorshipBenefits}}
            <h3 style="color: #3A6CA5;">Your Sponsorship Benefits Include:</h3>
            <div style="margin: 15px 0;">{{sponsorshipBenefits}}</div>
            {{/sponsorshipBenefits}}
            <h3 style="color: #3A6CA5;">Conference Information</h3>
            <p><strong>Event Dates:</strong> {{eventDates}}</p>
            <p><strong>Venue:</strong> {{venueInfo}}</p>
            <div style="background: #f0f8f0; padding: 20px; border-left: 4px solid #5EAE50; margin: 20px 0;">
              <h3 style="color: #5EAE50; margin-top: 0;">Payment Instructions</h3>
              <p style="margin: 0 0 15px 0;"><strong>Sponsors pay by check.</strong> Please make your check payable to:</p>
              <div style="background: #ffffff; padding: 15px; border: 2px solid #5EAE50; border-radius: 8px; margin: 15px 0;">
                <p style="margin: 0 0 10px 0; font-weight: bold; font-size: 16px;">Pay to: IEPA</p>
                <p style="margin: 0 0 5px 0; font-weight: bold;">Mail to:</p>
                <div style="margin-left: 10px; line-height: 1.4;">
                  <p style="margin: 0;">Independent Energy Producers Association</p>
                  <p style="margin: 0;">P.O. Box 1287</p>
                  <p style="margin: 0;">Sloughhouse, CA 95683-9998</p>
                  <p style="margin: 0;">United States</p>
                </div>
              </div>
            </div>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h4 style="color: #1976d2; margin-top: 0;">Next Steps</h4>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Our sponsorship coordinator will contact you within 2 business days</li>
                <li>You will receive detailed information about booth setup and logistics</li>
                <li>Marketing materials and logo placement guidelines will be provided</li>
                <li>Attendee networking opportunities will be coordinated</li>
              </ul>
            </div>
            <p>If you have any questions about your sponsorship or need assistance, please don't hesitate to contact us at {{contactEmail}} or reply to this email.</p>
            <p>We look forward to a successful partnership and seeing you at the conference!</p>
            <p>Best regards,<br><strong>The IEPA Conference Team</strong></p>
          </div>
          <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
              Independent Energy Producers Association<br>
              California's oldest nonprofit trade association representing independent energy facilities
            </p>
          </div>
        </div>`,
        text_template: `Dear {{name}},

Thank you for your {{sponsorshipLevel}} sponsorship of IEPA's 2025 Annual Meeting! We are thrilled to have {{organizationName}} as a valued partner for this important industry event.

SPONSORSHIP DETAILS
Organization: {{organizationName}}
Sponsorship Level: {{sponsorshipLevel}}
Confirmation Number: {{confirmationNumber}}
Registration Date: {{registrationDate}}
{{#paymentAmount}}Sponsorship Amount: {{paymentAmount}}{{/paymentAmount}}

{{#sponsorshipBenefitsText}}
YOUR SPONSORSHIP BENEFITS INCLUDE:
{{sponsorshipBenefitsText}}
{{/sponsorshipBenefitsText}}

CONFERENCE INFORMATION
Event Dates: {{eventDates}}
Venue: {{venueInfo}}

PAYMENT INSTRUCTIONS
Sponsors pay by check. Please make your check payable to:

Pay to: IEPA

Mail to:
Independent Energy Producers Association
P.O. Box 1287
Sloughhouse, CA 95683-9998
United States

NEXT STEPS
- Our sponsorship coordinator will contact you within 2 business days
- You will receive detailed information about booth setup and logistics
- Marketing materials and logo placement guidelines will be provided
- Attendee networking opportunities will be coordinated

If you have any questions about your sponsorship or need assistance, please don't hesitate to contact us at {{contactEmail}} or reply to this email.

We look forward to a successful partnership and seeing you at the conference!

Best regards,
The IEPA Conference Team

Independent Energy Producers Association
California's oldest nonprofit trade association representing independent energy facilities`,
        variables: [
          'name',
          'organizationName',
          'sponsorshipLevel',
          'confirmationNumber',
          'registrationDate',
          'paymentAmount',
          'sponsorshipBenefits',
          'sponsorshipBenefitsText',
          'contactEmail',
          'eventDates',
          'venueInfo',
        ],
      },
      {
        template_key: 'speaker_confirmation',
        template_name: 'Speaker Registration Confirmation',
        description:
          'Email sent to confirm successful speaker registration for the IEPA conference',
        subject_template:
          'IEPA 2025 Annual Meeting - Speaker Confirmation: {{presentationTitle}}',
        html_template: `<div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
          <div style="background: #3A6CA5; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Independent Energy Producers Association</h1>
            <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">2025 Annual Meeting - Speaker Confirmation</p>
          </div>
          <hr style="color: #3A6CA5; border: 2px solid #3A6CA5; margin: 0;" />
          <div style="padding: 30px;">
            <h2 style="color: #3A6CA5; margin-top: 0;">Dear {{name}},</h2>
            <p>Thank you for agreeing to speak at IEPA's 2025 Annual Meeting! We are honored to have you share your expertise with our industry colleagues.</p>
            <div style="background: #f8f9fa; padding: 20px; border-left: 4px solid #3A6CA5; margin: 20px 0;">
              <h3 style="color: #3A6CA5; margin-top: 0;">Speaker Details</h3>
              <p><strong>Speaker:</strong> {{name}}</p>
              <p><strong>Organization:</strong> {{organizationName}}</p>
              <p><strong>Presentation Title:</strong> {{presentationTitle}}</p>
              {{#sessionType}}<p><strong>Session Type:</strong> {{sessionType}}</p>{{/sessionType}}
              <p><strong>Confirmation Number:</strong> {{confirmationNumber}}</p>
              <p><strong>Registration Date:</strong> {{registrationDate}}</p>
            </div>
            {{#sessionDate}}
            <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #2e7d32; margin-top: 0;">Session Information</h3>
              <p><strong>Date & Time:</strong> {{sessionDate}}</p>
              {{#sessionDuration}}<p><strong>Duration:</strong> {{sessionDuration}}</p>{{/sessionDuration}}
              {{#roomAssignment}}<p><strong>Room:</strong> {{roomAssignment}}</p>{{/roomAssignment}}
            </div>
            {{/sessionDate}}
            <h3 style="color: #3A6CA5;">Conference Information</h3>
            <p><strong>Event Dates:</strong> {{eventDates}}</p>
            <p><strong>Venue:</strong> {{venueInfo}}</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h4 style="color: #1976d2; margin-top: 0;">Next Steps & Important Information</h4>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Our program coordinator will contact you within 3 business days</li>
                <li>You will receive detailed session guidelines and technical requirements</li>
                <li>Presentation materials should be submitted 1 week before the conference</li>
                <li>A/V setup and rehearsal time will be scheduled</li>
                <li>Speaker bio and photo may be requested for conference materials</li>
                {{#speakerFee}}<li>Speaker fee processing information will be provided separately</li>{{/speakerFee}}
              </ul>
            </div>
            <p>If you have any questions about your presentation, travel arrangements, or conference details, please don't hesitate to contact us at {{contactEmail}} or reply to this email.</p>
            <p>We're excited to have you as part of our speaker lineup and look forward to your valuable contribution to the conference!</p>
            <p>Best regards,<br><strong>The IEPA Conference Team</strong></p>
          </div>
          <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
              Independent Energy Producers Association<br>
              California's oldest nonprofit trade association representing independent energy facilities
            </p>
          </div>
        </div>`,
        text_template: `Dear {{name}},

Thank you for agreeing to speak at IEPA's 2025 Annual Meeting! We are honored to have you share your expertise with our industry colleagues.

SPEAKER DETAILS
Speaker: {{name}}
Organization: {{organizationName}}
Presentation Title: {{presentationTitle}}
{{#sessionType}}Session Type: {{sessionType}}{{/sessionType}}
Confirmation Number: {{confirmationNumber}}
Registration Date: {{registrationDate}}

{{#sessionDate}}
SESSION INFORMATION
Date & Time: {{sessionDate}}
{{#sessionDuration}}Duration: {{sessionDuration}}{{/sessionDuration}}
{{#roomAssignment}}Room: {{roomAssignment}}{{/roomAssignment}}
{{/sessionDate}}

CONFERENCE INFORMATION
Event Dates: {{eventDates}}
Venue: {{venueInfo}}

NEXT STEPS & IMPORTANT INFORMATION
- Our program coordinator will contact you within 3 business days
- You will receive detailed session guidelines and technical requirements
- Presentation materials should be submitted 1 week before the conference
- A/V setup and rehearsal time will be scheduled
- Speaker bio and photo may be requested for conference materials
{{#speakerFee}}- Speaker fee processing information will be provided separately{{/speakerFee}}

If you have any questions about your presentation, travel arrangements, or conference details, please don't hesitate to contact us at {{contactEmail}} or reply to this email.

We're excited to have you as part of our speaker lineup and look forward to your valuable contribution to the conference!

Best regards,
The IEPA Conference Team

Independent Energy Producers Association
California's oldest nonprofit trade association representing independent energy facilities`,
        variables: [
          'name',
          'email',
          'organizationName',
          'presentationTitle',
          'sessionType',
          'confirmationNumber',
          'registrationDate',
          'sessionDate',
          'sessionDuration',
          'roomAssignment',
          'speakerFee',
          'accommodationInfo',
          'travelInfo',
          'contactEmail',
          'eventDates',
          'venueInfo',
          'avRequirements',
          'speakerBio',
        ],
      },
    ];

    // Insert default templates
    const { error: insertError } = await supabase
      .from('iepa_email_templates')
      .upsert(defaultTemplates, {
        onConflict: 'template_key',
        ignoreDuplicates: false,
      });

    if (insertError) {
      console.error(
        '[EMAIL-TEMPLATES-SETUP] Error inserting default templates:',
        insertError
      );
      throw insertError;
    }

    console.log(
      '[EMAIL-TEMPLATES-SETUP] Default templates inserted successfully'
    );

    return NextResponse.json({
      success: true,
      message: 'Email templates tables and default data created successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    console.error('[EMAIL-TEMPLATES-SETUP] Setup failed:', error);

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to setup email templates tables',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
