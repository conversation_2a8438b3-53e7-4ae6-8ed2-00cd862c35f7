// API endpoint for checking sponsor discount eligibility
// Automatically detects if an email domain qualifies for sponsor discounts

import { NextRequest, NextResponse } from 'next/server';
import { checkSponsorDiscount, linkAttendeeToSponsor } from '@/lib/sponsor-attendee-utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, attendeeId } = body;

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email is required',
        },
        { status: 400 }
      );
    }

    // Check if email is eligible for sponsor discount
    const discountResult = await checkSponsorDiscount(email);

    if (!discountResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: discountResult.error || 'Failed to check sponsor discount',
        },
        { status: 500 }
      );
    }

    // If eligible and attendeeId provided, link the attendee to sponsor
    if (discountResult.isEligible && attendeeId && discountResult.sponsorId) {
      const linkResult = await linkAttendeeToSponsor(
        attendeeId,
        discountResult.sponsorId,
        discountResult.discountCode
      );

      if (!linkResult.success) {
        console.error('Failed to link attendee to sponsor:', linkResult.error);
        // Don't fail the entire request, just log the error
      }
    }

    return NextResponse.json({
      success: true,
      isEligible: discountResult.isEligible,
      sponsorName: discountResult.sponsorName,
      discountCode: discountResult.discountCode,
      discountAmount: discountResult.discountAmount,
      message: discountResult.isEligible
        ? `Congratulations! You qualify for a sponsor discount from ${discountResult.sponsorName}.`
        : 'No sponsor discount available for this email domain.',
    });
  } catch (error) {
    console.error('Error checking sponsor discount:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}
