#!/usr/bin/env node

/**
 * Email Testing Setup and Verification Script
 * Tests email configuration and connectivity for IEPA Conference Registration
 */

const { testEmailConfiguration } = require('../tests/config/email-test-config');
const { waitForEmail } = require('../tests/utils/email-testing');

async function testEmailSetup() {
  console.log('🧪 IEPA Email Testing Setup Verification');
  console.log('=====================================\n');

  // Test 1: Check Supabase Inbucket (Local Development)
  console.log('1. Testing Supabase Inbucket (Local Development)...');
  try {
    const response = await fetch('http://127.0.0.1:54324/api/v1/mailbox/test');
    const isInbucketWorking =
      response.status === 200 || response.status === 404;

    if (isInbucketWorking) {
      console.log(
        '✅ Supabase Inbucket is accessible at http://127.0.0.1:54324'
      );
      console.log('   You can view test emails in the web interface');
    } else {
      console.log('❌ Supabase Inbucket is not accessible');
      console.log('   Run: npm run supabase:start');
    }
  } catch (error) {
    console.log('❌ Supabase Inbucket connection failed');
    console.log('   Make sure Supabase is running: npm run supabase:start');
  }
  console.log('');

  // Test 2: Check SendGrid Configuration
  console.log('2. Testing SendGrid Configuration...');
  try {
    const response = await fetch('http://localhost:6969/api/test-email', {
      method: 'GET',
    });

    if (response.ok) {
      const config = await response.json();
      console.log('✅ SendGrid configuration loaded');
      console.log(`   From Email: ${config.config?.fromEmail}`);
      console.log(`   From Name: ${config.config?.fromName}`);
    } else {
      console.log('❌ SendGrid configuration check failed');
      console.log(
        '   Make sure your development server is running: npm run dev'
      );
    }
  } catch (error) {
    console.log('❌ Could not check SendGrid configuration');
    console.log('   Make sure your development server is running: npm run dev');
  }
  console.log('');

  // Test 3: Check Email Test Account Configuration
  console.log('3. Testing Email Test Account Configuration...');

  const primaryConfigured = await testEmailConfiguration('primary');
  const mailtrapConfigured = await testEmailConfiguration('mailtrap');

  if (!primaryConfigured && !mailtrapConfigured) {
    console.log('⚠️  No external email accounts configured');
    console.log(
      '   For production-like testing, set up email credentials in .env.email-testing.local'
    );
  }
  console.log('');

  // Test 4: Send Test Email
  console.log('4. Sending Test Email...');
  const testEmail = `test.${Date.now()}@iepa-test.com`;

  try {
    const response = await fetch('http://localhost:6969/api/test-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        testEmail: testEmail,
        testType: 'basic',
      }),
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Test email sent successfully');
      console.log(`   Recipient: ${testEmail}`);

      // If using Inbucket, provide viewing instructions
      console.log('\n📧 To view the email:');
      console.log('   - Inbucket: http://127.0.0.1:54324');
      console.log(`   - Look for mailbox: ${testEmail.split('@')[0]}`);
    } else {
      console.log('❌ Test email sending failed');
      const error = await response.text();
      console.log(`   Error: ${error}`);
    }
  } catch (error) {
    console.log('❌ Test email sending failed');
    console.log(`   Error: ${error.message}`);
  }
  console.log('');

  // Summary and Recommendations
  console.log('📋 Setup Summary and Recommendations');
  console.log('====================================');
  console.log('');

  console.log('For Local Development:');
  console.log('✓ Use Supabase Inbucket (no additional setup required)');
  console.log('✓ View emails at: http://127.0.0.1:54324');
  console.log('✓ Emails are captured automatically during testing');
  console.log('');

  console.log('For Production-like Testing:');
  console.log('1. Copy .env.email-testing.example to .env.email-testing.local');
  console.log('2. Set up a Gmail account with app-specific password');
  console.log('3. Or sign up for Mailtrap.io for dedicated email testing');
  console.log('4. Configure credentials in .env.email-testing.local');
  console.log('');

  console.log('Running Email Tests:');
  console.log('• npm run test:e2e:local -- tests/email-deliverability.spec.js');
  console.log(
    '• npm run test:e2e:local -- tests/attendee-registration-e2e.spec.js'
  );
  console.log('');

  console.log('Troubleshooting:');
  console.log('• Check spam/junk folders for test emails');
  console.log('• Verify SendGrid API key in .env.local');
  console.log('• Ensure Supabase is running for local testing');
  console.log('• Use app-specific passwords for Gmail (not regular password)');
}

// Run the test if this script is executed directly
if (require.main === module) {
  testEmailSetup().catch(error => {
    console.error('❌ Email setup test failed:', error);
    process.exit(1);
  });
}

module.exports = { testEmailSetup };
