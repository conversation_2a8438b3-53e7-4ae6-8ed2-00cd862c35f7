'use client';

import { Suspense } from 'react';
// import { useSearchParams } from 'next/navigation';

interface AttendeeRegistrationClientProps {
  children: React.ReactNode;
}

function AttendeeRegistrationContent({
  children,
}: AttendeeRegistrationClientProps) {
  // This component can safely use useSearchParams since it's client-side
  return <>{children}</>;
}

export default function AttendeeRegistrationClient({
  children,
}: AttendeeRegistrationClientProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AttendeeRegistrationContent>{children}</AttendeeRegistrationContent>
    </Suspense>
  );
}
