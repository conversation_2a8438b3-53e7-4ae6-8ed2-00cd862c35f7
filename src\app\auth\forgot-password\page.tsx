'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, Input } from '@/components/ui';
import Link from 'next/link';
import { debugResetPassword, logAuthOperation } from '@/lib/auth-debug';

export default function ForgotPasswordPage() {
  // Note: We're using debugResetPassword instead of the context resetPassword for enhanced logging
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    logAuthOperation('forgotPassword', true, null, null, {
      step: 'form_submitted',
      email: email.replace(/(.{2}).*(@.*)/, '$1***$2'), // Partially mask email for logging
    });

    try {
      // Use debug reset password for comprehensive logging
      const result = await debugResetPassword(email);

      if (result.error) {
        throw result.error;
      }

      setSuccess(true);
      setMessage(
        'Password reset instructions have been sent to your email address.'
      );

      logAuthOperation('forgotPassword', true, null, null, {
        step: 'email_sent_successfully',
      });
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'An error occurred. Please try again.';

      setMessage(errorMessage);

      logAuthOperation('forgotPassword', false, null, err, {
        step: 'email_send_failed',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto text-center">
            <div className="iepa-status-success mb-8">
              <h1 className="iepa-heading-1 mb-4">Check Your Email</h1>
              <p className="iepa-body mb-4">
                We&apos;ve sent password reset instructions to{' '}
                <strong>{email}</strong>
              </p>
              <p className="iepa-body-small">
                If you don&apos;t see the email in your inbox, please check your
                spam folder.
              </p>
            </div>

            <div className="space-y-4">
              <Button
                as={Link}
                href="/auth/login"
                color="primary"
                size="lg"
                className="w-full"
              >
                Back to Sign In
              </Button>

              <Button
                onClick={() => {
                  setSuccess(false);
                  setEmail('');
                  setMessage('');
                }}
                variant="bordered"
                size="lg"
                className="w-full"
              >
                Try Different Email
              </Button>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="iepa-heading-1 mb-4">Reset Password</h1>
            <p className="iepa-body">
              Enter your email address and we&apos;ll send you instructions to
              reset your password.
            </p>
          </div>

          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 text-center">
                Forgot Your Password?
              </h2>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmit} className="space-y-6">
                {message && (
                  <div
                    className={`${message.includes('error') || message.includes('Error') ? 'iepa-status-error' : 'iepa-status-info'}`}
                  >
                    <p className="iepa-body-small">{message}</p>
                  </div>
                )}

                <Input
                  label="Email Address *"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  isRequired
                  autoComplete="email"
                  description="Enter the email address associated with your account"
                />

                <Button
                  type="submit"
                  color="primary"
                  size="lg"
                  className="w-full"
                  disabled={isLoading || !email}
                >
                  {isLoading
                    ? 'Sending Instructions...'
                    : 'Send Reset Instructions'}
                </Button>
              </form>
            </CardBody>
          </Card>

          <div className="text-center mt-6">
            <p className="iepa-body-small">
              Remember your password?{' '}
              <Link
                href="/auth/login"
                className="font-semibold hover:underline"
                style={{ color: 'var(--iepa-primary-blue)' }}
              >
                Sign In
              </Link>
            </p>
          </div>

          <div
            className="mt-8 p-4 rounded-lg"
            style={{ backgroundColor: 'var(--iepa-gray-50)' }}
          >
            <h3 className="iepa-heading-3 mb-2">Need Additional Help?</h3>
            <p className="iepa-body-small mb-3">
              If you&apos;re having trouble accessing your account or don&apos;t
              receive the reset email:
            </p>
            <ul className="iepa-body-small space-y-1 mb-4">
              <li>• Check your spam or junk email folder</li>
              <li>• Make sure you&apos;re using the correct email address</li>
              <li>• Wait a few minutes for the email to arrive</li>
              <li>• Contact our support team if you continue to have issues</li>
            </ul>
            <Button as={Link} href="/contact" variant="bordered" size="sm">
              Contact Support
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
