'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

// Force dynamic rendering to avoid SSG issues with useSearchParams
export const dynamic = 'force-dynamic';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Radio,
  LegacyRadioGroup as RadioGroup,
  Checkbox,
  RegistrationCardRadio,
  CancellationPolicy,
  HydrationSafeInput,
  HydrationSafePhoneInput,
  // IEPASubmitButton, // TODO: Create this component
} from '@/components/ui';
// import { PhoneInput } from '@/components/ui/phone-input';
import OrganizationCombobox from '@/components/ui/OrganizationCombobox';

import SponsorDiscountDetector from '@/components/sponsor/SponsorDiscountDetector';
import ScrollspyNavigation from '@/components/ui/scrollspy-navigation';
import { CONFERENCE_YEAR, dateUtils } from '@/lib/conference-config';
import { REGISTRATION_PRICING } from '@/lib/pricing-config';
import { STATE_PROVINCE_OPTIONS } from '@/lib/address-constants';
import { FORM_STORAGE_KEYS } from '@/lib/form-persistence';
import { useFormPersistence } from '@/hooks/useFormPersistence';
import { useScrollspy } from '@/hooks/useScrollspy';
import {
  RestoreDataPrompt,
  FloatingSaveStatus,
  DataManagementPanel,
} from '@/components/ui/form-persistence-ui';
import { cn } from '@/lib/utils';
import {
  FaUser,
  FaIdCard,
  FaPhone,
  FaCalendarAlt,
  FaExclamationTriangle,
  FaCheckCircle,
} from 'react-icons/fa';
import { useAuth } from '@/contexts/AuthContext';
import { useFormPrefill } from '@/hooks/useFormPrefill';
import { ProtectedRegistrationPage } from '@/components/auth/ProtectedRoute';
import { showSuccess, showError, showInfo } from '@/utils/notifications';
import { useRegistrationConstraints } from '@/hooks/useRegistrationConstraints';
import { ExistingRegistrationNotice } from '@/components/registration/ExistingRegistrationNotice';
// import { useTestDataFill } from '@/hooks/useTestDataFill';

// Form sections for single-page layout with scrollspy
const FORM_SECTIONS = [
  {
    id: 'registration-type',
    title: 'Registration Type',
    description: 'Choose your registration type',
    icon: FaUser,
  },
  {
    id: 'personal-information',
    title: 'Personal Information',
    description: 'Your personal details',
    icon: FaIdCard,
  },
  {
    id: 'contact-information',
    title: 'Contact Information',
    description: 'Contact and professional details',
    icon: FaPhone,
  },
  {
    id: 'event-options',
    title: 'Event Options',
    description: 'Optional add-ons and preferences',
    icon: FaCalendarAlt,
  },
  {
    id: 'emergency-contact',
    title: 'Emergency Contact',
    description: 'Emergency contact information',
    icon: FaExclamationTriangle,
  },
  {
    id: 'review-payment',
    title: 'Review & Payment',
    description: 'Review your registration',
    icon: FaCheckCircle,
  },
];

export default function AttendeeRegistrationPage() {
  const { user } = useAuth();
  const prefillAttemptedRef = useRef(false);

  // Check registration constraints
  const {
    constraintCheck,
    loading: constraintLoading,
    error: constraintError,
  } = useRegistrationConstraints({
    registrationType: 'attendee',
    attendeeType: 'attendee', // Check for primary attendee registration
    autoCheck: true,
  });
  const [formData, setFormData] = useState({
    registrationType: '',
    linkedAttendeeEmail: '', // For spouse/child registrations
    firstName: '',
    lastName: '',
    nameOnBadge: '',
    email: user?.email || '',
    gender: '',
    phoneNumber: '',
    organization: '',
    jobTitle: '',
    streetAddress: '', // Fixed: matches schema field name
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    golfTournament: false,
    golfClubRental: false,
    golfClubHandedness: '',
    // Night selections for annual meeting lodging
    nightOne: true, // Default to both nights selected
    nightTwo: true,
    // Meal selections for September 15-17, 2025
    meals: {
      sept15Dinner: false,
      sept16Breakfast: false,
      sept16Lunch: false,
      sept16Dinner: false,
      sept17Breakfast: false,
      sept17Lunch: false,
    },
    dietaryRestrictions: '',
    emergencyContact: '',
    emergencyPhone: '',
  });

  // Sponsor discount state
  const [sponsorDiscount, setSponsorDiscount] = useState<{
    isEligible: boolean;
    sponsorName?: string;
    discountCode?: string;
    discountAmount?: number;
  } | null>(null);

  // Form persistence - Updated for single-page form
  const persistence = useFormPersistence(formData, setFormData, {
    formKey: FORM_STORAGE_KEYS.ATTENDEE,
    debounceMs: 1000,
    expirationDays: 7,
    excludeFields: ['presentationFile', 'headshot'],
    onDataRestored: data => {
      console.log('Attendee form data restored:', data);
      showSuccess(
        'Form Data Restored',
        'Your previous progress has been restored.'
      );
    },
    onDataSaved: () => {
      console.log('Attendee form data auto-saved');
    },
    onDataCleared: () => {
      console.log('Attendee form data cleared');
    },
  });

  // Scrollspy for navigation
  const { activeSection, sectionRefs } = useScrollspy(
    FORM_SECTIONS.map(section => section.id),
    { offset: 100 }
  );

  // Form prefilling from user profile
  const formPrefill = useFormPrefill({
    formType: 'attendee',
    onPrefillComplete: (fieldsPopulated, summary) => {
      console.log('Form prefilled:', { fieldsPopulated, summary });
      showInfo('Form Populated', summary);
    },
    onPrefillError: error => {
      console.error('Form prefill error:', error);
      showError('Prefill Error', 'Could not load your profile information.');
    },
    enabled: true,
  });

  // Test data auto-fill functionality (disabled in production)
  // const { fillTestData, isTestMode } = useTestDataFill(
  //   'attendee',
  //   setFormData,
  //   {
  //     onTestDataFilled: (summary) => {
  //       console.log('Test data filled:', summary);
  //       showInfo('Test Data Loaded', summary);
  //     },
  //     onError: (error) => {
  //       console.error('Test data fill error:', error);
  //       showError('Test Data Error', error);
  //     },
  //     enabled: true,
  //   }
  // );

  // Submit button state management
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for form errors and notifications
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | null;
    title: string;
    message: string;
    registrationId?: string;
    totalAmount?: number;
  }>({
    type: null,
    title: '',
    message: '',
  });

  // Form prefilling effect
  const attemptPrefill = useCallback(async () => {
    // Prevent multiple prefill attempts
    if (prefillAttemptedRef.current) {
      return;
    }

    if (!formPrefill.canPrefill) {
      console.log('Cannot prefill form:', {
        user: !!user,
        profile: !!formPrefill.profile,
        hasPrefillData: formPrefill.hasPrefillData,
        isLoading: formPrefill.isLoading,
      });
      return;
    }

    // Check if form already has significant data (from localStorage or user input)
    const hasExistingData =
      formData.firstName || formData.lastName || formData.organization;

    if (hasExistingData) {
      console.log('Form already has data, skipping prefill');
      prefillAttemptedRef.current = true;
      return;
    }

    try {
      console.log('🔄 Starting form prefill process');
      prefillAttemptedRef.current = true;
      const result = await formPrefill.prefillForm(formData);
      if (result && result.fieldsPopulated.length > 0) {
        console.log('Applying prefilled data to form');
        setFormData(result.formData);
      }
    } catch (error) {
      console.error('Error during form prefill:', error);
    }
  }, [formData, formPrefill, user]);

  useEffect(() => {
    // Only attempt prefill after a short delay to allow localStorage restoration to complete
    const timeoutId = setTimeout(attemptPrefill, 500);

    return () => clearTimeout(timeoutId);
  }, [attemptPrefill]); // Re-run when attemptPrefill changes

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle meal selection changes
  const handleMealChange = (mealKey: string, value: boolean | string) => {
    setFormData(prev => ({
      ...prev,
      meals: {
        ...prev.meals,
        [mealKey]: Boolean(value),
      },
    }));
  };

  // Handle sponsor discount detection
  const handleSponsorDiscountDetected = (discount: {
    isEligible: boolean;
    sponsorName?: string;
    discountCode?: string;
    discountAmount?: number;
  }) => {
    setSponsorDiscount(discount);
  };

  const handleSponsorDiscountCleared = () => {
    setSponsorDiscount(null);
  };

  // Single-page form validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Registration Type validation
    if (!formData.registrationType) {
      newErrors.registrationType = 'Please select a registration type';
    }
    // Validate linked attendee email for spouse/child registrations
    if (
      formData.registrationType === 'spouse' ||
      formData.registrationType === 'child'
    ) {
      if (!formData.linkedAttendeeEmail) {
        newErrors.linkedAttendeeEmail =
          'Primary attendee email is required for spouse and child registrations';
      } else if (
        !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.linkedAttendeeEmail)
      ) {
        newErrors.linkedAttendeeEmail = 'Please enter a valid email address';
      }
    }

    // Personal Information validation
    if (!formData.firstName) newErrors.firstName = 'First name is required';
    if (!formData.lastName) newErrors.lastName = 'Last name is required';
    if (!formData.nameOnBadge)
      newErrors.nameOnBadge = 'Name on badge is required';
    if (!formData.email) newErrors.email = 'Email is required';
    if (!formData.gender) newErrors.gender = 'Gender selection is required';

    // Contact Information validation
    if (!formData.phoneNumber)
      newErrors.phoneNumber = 'Phone number is required';
    if (!formData.organization)
      newErrors.organization = 'Organization is required';
    if (!formData.jobTitle) newErrors.jobTitle = 'Job title is required';
    if (!formData.streetAddress)
      newErrors.streetAddress = 'Street address is required';
    if (!formData.city) newErrors.city = 'City is required';
    if (!formData.state) newErrors.state = 'State is required';
    if (!formData.zipCode) newErrors.zipCode = 'ZIP code is required';

    // Emergency Contact validation
    if (!formData.emergencyContact)
      newErrors.emergencyContact = 'Emergency contact name is required';
    if (!formData.emergencyPhone)
      newErrors.emergencyPhone = 'Emergency contact phone is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Check if form is valid for button state (separate from submission validation)
  const isFormValid = (): boolean => {
    return !!(
      formData.registrationType &&
      formData.firstName &&
      formData.lastName &&
      formData.nameOnBadge &&
      formData.email &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
      formData.gender &&
      formData.phoneNumber &&
      formData.organization &&
      formData.jobTitle &&
      formData.streetAddress &&
      formData.city &&
      formData.state &&
      formData.zipCode &&
      formData.emergencyContact &&
      formData.emergencyPhone
    );
  };

  // Handle scrollspy navigation clicks
  const handleScrollspyClick = (sectionId: string) => {
    // Smooth scroll to section is handled by the ScrollspyNavigation component
    console.log('Navigating to section:', sectionId);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return; // Prevent double submission

    try {
      setIsSubmitting(true);
      // Validate entire form before submission
      if (!validateForm()) {
        throw new Error(
          'Please correct the errors in the form before submitting.'
        );
      }

      // Check registration constraints before submission
      if (!user?.id) {
        throw new Error('Please log in to submit your registration.');
      }

      // Check registration constraints
      const { checkRegistrationConstraints } = await import(
        '@/services/registrationConstraints'
      );
      const attendeeType = ['spouse', 'child'].includes(
        formData.registrationType
      )
        ? (formData.registrationType as 'spouse' | 'child')
        : 'attendee';

      const constraintResult = await checkRegistrationConstraints(
        user.id,
        'attendee',
        attendeeType
      );

      if (!constraintResult.canRegister) {
        throw new Error(constraintResult.message);
      }

      // Submit to database
      console.log('Form submitted:', formData);

      // Import Supabase client
      const { supabase } = await import('@/lib/supabase');

      // Get authenticated user from current session
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      console.log('Session check:', {
        session: !!session,
        user: !!session?.user,
        sessionError,
      });

      if (sessionError) {
        console.error('Session error:', sessionError);
        throw new Error(
          `Authentication error: ${sessionError.message || 'Unknown session error'}`
        );
      }

      if (!session?.user) {
        console.error('No user session found');
        throw new Error('User not authenticated. Please log in and try again.');
      }

      // Validate that sponsor-attendee registration is not allowed on this form
      if (formData.registrationType === 'sponsor-attendee') {
        throw new Error(
          'Sponsor attendee registration must be completed through the dedicated sponsor attendee form.'
        );
      }

      // Calculate pricing based on registration type and options
      const registrationPricing = REGISTRATION_PRICING.find(
        p => p.id === formData.registrationType
      );

      if (!registrationPricing) {
        throw new Error('Invalid registration type selected.');
      }

      const registrationTotal = registrationPricing.basePrice;
      const golfTotal = formData.golfTournament ? 200 : 0; // Golf tournament fee
      const golfClubRentalTotal = formData.golfClubRental ? 70 : 0; // Golf club rental fee
      const mealTotal = 0; // All meals are complimentary
      const originalTotal =
        registrationTotal + golfTotal + golfClubRentalTotal + mealTotal;

      // Apply sponsor discount if available (100% off for sponsor attendees)
      let grandTotal = originalTotal;
      let discountAmount = 0;
      let discountCode = null;

      if (
        sponsorDiscount?.isEligible &&
        sponsorDiscount.discountAmount === 100
      ) {
        // Apply 100% sponsor discount
        grandTotal = 0;
        discountAmount = originalTotal;
        discountCode = sponsorDiscount.discountCode;
      }

      // Convert meals object to array format for database
      const selectedMeals = Object.entries(formData.meals)
        .filter(([, selected]) => selected)
        .map(([mealKey]) => mealKey);

      // Prepare data for database insertion
      const submissionData = {
        user_id: user.id,
        registration_type: formData.registrationType,
        attendee_type: attendeeType,
        linked_attendee_email:
          attendeeType !== 'attendee' ? formData.linkedAttendeeEmail : null,
        email: formData.email,
        first_name: formData.firstName,
        last_name: formData.lastName,
        name_on_badge: formData.nameOnBadge,
        gender: formData.gender,
        phone_number: formData.phoneNumber,
        street_address: formData.streetAddress,
        city: formData.city,
        state: formData.state,
        zip_code: formData.zipCode,
        organization: formData.organization,
        job_title: formData.jobTitle,
        attending_golf: formData.golfTournament,
        golf_club_rental: formData.golfClubRental,
        golf_club_handedness: formData.golfClubHandedness,
        night_one: formData.nightOne,
        night_two: formData.nightTwo,
        meals: selectedMeals,
        dietary_needs: formData.dietaryRestrictions,
        registration_total: registrationTotal,
        golf_total: golfTotal,
        golf_club_rental_total: golfClubRentalTotal,
        meal_total: mealTotal,
        grand_total: grandTotal,
        discount_code: discountCode,
        discount_amount: discountAmount,
        original_total: originalTotal,
        payment_status: grandTotal === 0 ? 'completed' : 'pending', // Mark as completed if free
        // Sponsor linking fields
        sponsor_discount_code: sponsorDiscount?.isEligible
          ? sponsorDiscount.discountCode
          : null,
        is_sponsor_attendee: sponsorDiscount?.isEligible || false,
      };

      console.log('Submitting to database:', submissionData);

      // Insert into database
      const { data, error } = await supabase
        .from('iepa_attendee_registrations')
        .insert([submissionData])
        .select();

      if (error) {
        console.error('Database insertion error:', error);
        throw new Error(
          `Database error: ${error.message || 'Failed to save registration'}`
        );
      }

      if (!data || data.length === 0) {
        console.error('No data returned from database insertion');
        throw new Error('Registration was not saved properly');
      }

      console.log('Database submission successful:', data);

      // 📧 SEND REGISTRATION CONFIRMATION EMAIL
      try {
        const emailResponse = await fetch('/api/send-registration-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: formData.email,
            fullName: `${formData.firstName} ${formData.lastName}`.trim(),
            type: 'attendee',
            confirmationNumber: data[0]?.id,
            userId: user.id,
          }),
        });

        if (emailResponse.ok) {
          console.log(
            '[EMAIL-DEBUG] Registration confirmation email sent to:',
            formData.email
          );
        } else {
          console.warn(
            '[EMAIL-WARNING] Email API returned non-OK status:',
            emailResponse.status
          );
        }
      } catch (emailError) {
        console.error(
          '[EMAIL-ERROR] Failed to send registration confirmation email:',
          emailError
        );
        // Don't fail the registration if email fails
      }

      // Show success notification
      setNotification({
        type: 'success',
        title: 'Registration Submitted Successfully!',
        message:
          'You will receive a confirmation email shortly. Please proceed to payment to complete your registration.',
        registrationId: data[0]?.id,
        totalAmount: grandTotal,
      });

      // Clear persisted form data after successful submission
      persistence.clearFormData();

      // Redirect to payment if amount > 0
      if (grandTotal > 0) {
        // Import Stripe utilities
        const { stripeUtils } = await import('@/lib/stripe-client');

        // Prepare payment data
        const paymentData = {
          registrationId: data[0]?.id,
          registrationType: 'attendee' as const,
          customerEmail: formData.email,
          customerName: `${formData.firstName} ${formData.lastName}`,
          totalAmount: grandTotal,
          lineItems: [
            {
              name: `IEPA ${CONFERENCE_YEAR} Conference Registration`,
              description: `${REGISTRATION_PRICING.find(p => p.id === formData.registrationType)?.displayName || 'Conference Registration'}`,
              price: grandTotal,
              quantity: 1,
            },
          ],
          discountCode: discountCode || undefined,
          metadata: {
            registrationType: formData.registrationType,
            firstName: formData.firstName,
            lastName: formData.lastName,
            email: formData.email,
            organization: formData.organization,
            golfTournament: formData.golfTournament.toString(),
            nightOne: formData.nightOne.toString(),
            nightTwo: formData.nightTwo.toString(),
          },
        };

        console.log('Initiating payment process...', paymentData);

        // Process payment (creates checkout session and redirects)
        const paymentResult = await stripeUtils.processPayment(paymentData);

        if (!paymentResult.success) {
          console.error('Payment initiation failed:', paymentResult.error);
          setNotification({
            type: 'error',
            title: 'Payment Error',
            message: `Failed to initiate payment: ${paymentResult.error}. Please try again or contact support.`,
          });
        }
        // If successful, user will be redirected to Stripe checkout
      }

      // Reset form after successful submission
      setFormData({
        registrationType: '',
        linkedAttendeeEmail: '',
        firstName: '',
        lastName: '',
        nameOnBadge: '',
        email: '',
        gender: '',
        phoneNumber: '',
        organization: '',
        jobTitle: '',
        streetAddress: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'United States',
        golfTournament: false,
        golfClubRental: false,
        golfClubHandedness: '',
        nightOne: true,
        nightTwo: true,
        meals: {
          sept15Dinner: false,
          sept16Breakfast: false,
          sept16Lunch: false,
          sept16Dinner: false,
          sept17Breakfast: false,
          sept17Lunch: false,
        },
        dietaryRestrictions: '',
        emergencyContact: '',
        emergencyPhone: '',
      });

      // Reset form state
      setErrors({});

      console.log('Registration submitted successfully');
      setIsSubmitting(false);
    } catch (error) {
      console.error('Form submission error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'An error occurred';
      showError('Registration Failed', errorMessage);
      setIsSubmitting(false);
    }
  };

  const genderOptions = [
    { value: 'female', label: 'Female' },
    { value: 'male', label: 'Male' },
  ];

  // Conference meal options for September 15-17, 2025 (All meals are complimentary)
  const mealOptions = [
    {
      date: 'September 15, 2025',
      day: 'Monday',
      meals: [
        {
          key: 'sept15Dinner',
          name: 'Dinner',
          time: '6:00 PM',
          price: 0,
          description: 'Welcome dinner with networking reception',
        },
      ],
    },
    {
      date: 'September 16, 2025',
      day: 'Tuesday',
      meals: [
        {
          key: 'sept16Breakfast',
          name: 'Breakfast',
          time: '7:00 AM',
          price: 0,
          description: 'Continental breakfast before morning sessions',
        },
        {
          key: 'sept16Lunch',
          name: 'Lunch',
          time: '12:00 PM',
          price: 0,
          description: 'Networking lunch with keynote presentation',
        },
        {
          key: 'sept16Dinner',
          name: 'Dinner',
          time: '6:30 PM',
          price: 0,
          description: 'Reception and plated dinner',
        },
      ],
    },
    {
      date: 'September 17, 2025',
      day: 'Wednesday',
      meals: [
        {
          key: 'sept17Breakfast',
          name: 'Breakfast',
          time: '7:00 AM',
          price: 0,
          description: 'Continental breakfast before final sessions',
        },
        {
          key: 'sept17Lunch',
          name: 'Lunch',
          time: '12:00 PM',
          price: 0,
          description: 'Closing lunch and farewell',
        },
      ],
    },
  ];

  // Use shared state/province options for consistency and Stripe compliance

  return (
    <ProtectedRegistrationPage>
      <div
        id="attendee-registration-page"
        className="iepa-container relative"
        data-testid="attendee-registration-page"
      >
        {/* Scrollspy Navigation */}
        <ScrollspyNavigation
          sections={FORM_SECTIONS}
          activeSection={activeSection}
          onSectionClick={handleScrollspyClick}
          variant="desktop"
        />

        <ScrollspyNavigation
          sections={FORM_SECTIONS}
          activeSection={activeSection}
          onSectionClick={handleScrollspyClick}
          variant="mobile"
        />

        {/* Header */}
        <section
          id="registration-header"
          className="iepa-compact-section"
          data-testid="registration-header"
        >
          <div className="max-w-4xl mx-auto text-center">
            <h1
              id="registration-page-title"
              className="iepa-compact-heading-1"
              data-testid="registration-page-title"
            >
              Attendee Registration - IEPA {CONFERENCE_YEAR}
            </h1>
            <p
              id="registration-page-description"
              className="iepa-compact-body"
              data-testid="registration-page-description"
            >
              Complete the form below to register for the annual meeting. All
              fields marked with * are required.
            </p>
          </div>
        </section>

        {/* Registration Constraint Check */}
        {constraintLoading && (
          <section className="iepa-compact-section">
            <div className="max-w-4xl mx-auto text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="iepa-body">Checking registration eligibility...</p>
            </div>
          </section>
        )}

        {constraintError && (
          <section className="iepa-compact-section">
            <div className="max-w-4xl mx-auto">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-800">
                  Error checking registration eligibility: {constraintError}
                </p>
              </div>
            </div>
          </section>
        )}

        {constraintCheck &&
          !constraintCheck.canRegister &&
          constraintCheck.existingRegistration && (
            <ExistingRegistrationNotice
              registration={constraintCheck.existingRegistration}
              message={constraintCheck.message}
              showEditOption={true}
              showViewOption={true}
            />
          )}

        {/* Only show the form if user can register or if we're still loading */}
        {(constraintLoading ||
          constraintError ||
          (constraintCheck && constraintCheck.canRegister)) && (
          <>
            {/* Form Persistence UI */}
            <section className="iepa-compact-section">
              <div className="max-w-4xl mx-auto">
                <RestoreDataPrompt
                  show={persistence.showRestorePrompt}
                  dataAge={persistence.dataAge}
                  onRestore={persistence.restoreData}
                  onStartFresh={persistence.startFresh}
                  onDismiss={persistence.dismissPrompt}
                  formType="attendee registration"
                />

                {/* Data Management Panel */}
                {persistence.hasPersistedData &&
                  !persistence.showRestorePrompt && (
                    <DataManagementPanel
                      hasPersistedData={persistence.hasPersistedData}
                      dataAge={persistence.dataAge}
                      isDataExpired={persistence.isDataExpired}
                      onClearData={persistence.clearFormData}
                      formType="attendee registration"
                      className="mb-6"
                    />
                  )}
              </div>
            </section>

            {/* Registration Form */}
            <section
              id="registration-form-section"
              className="iepa-compact-section"
              data-testid="registration-form-section"
            >
              <div className="max-w-4xl mx-auto lg:ml-80">
                {' '}
                {/* Add left margin for desktop scrollspy */}
                <form
                  id="attendee-registration-form"
                  onSubmit={handleSubmit}
                  className="space-y-8"
                  data-testid="attendee-registration-form"
                >
                  {/* Section 1: Registration Type */}
                  <section
                    id="registration-type"
                    ref={el => {
                      if (el && sectionRefs[0]) {
                        sectionRefs[0].current = el;
                      }
                    }}
                  >
                    <Card
                      className="iepa-form-card"
                      data-testid="registration-type-step"
                    >
                      <CardHeader>
                        <h2
                          className="iepa-compact-heading-2"
                          data-testid="registration-type-heading"
                        >
                          Registration Type
                        </h2>
                        <p
                          className="iepa-compact-body text-[var(--iepa-gray-600)]"
                          data-testid="registration-type-description"
                        >
                          Choose the registration type that applies to you. Each
                          option includes different benefits and pricing.
                        </p>
                      </CardHeader>
                      <CardBody>
                        <div
                          id="registration-type-field"
                          className="iepa-form-field"
                          data-testid="registration-type-field"
                        >
                          <RegistrationCardRadio
                            options={REGISTRATION_PRICING.filter(
                              option => option.id !== 'sponsor-attendee'
                            )}
                            value={formData.registrationType}
                            onValueChange={value =>
                              handleInputChange('registrationType', value)
                            }
                            name="registrationType"
                            required
                            className={
                              errors.registrationType
                                ? 'iepa-form-field-error'
                                : ''
                            }
                            aria-describedby={
                              errors.registrationType
                                ? 'registrationType-error'
                                : undefined
                            }
                          />
                          {errors.registrationType && (
                            <div
                              id="registrationType-error"
                              className="iepa-error-message mt-4"
                              role="alert"
                              data-testid="registrationType-error"
                            >
                              {errors.registrationType}
                            </div>
                          )}
                        </div>

                        {/* Linked Attendee Email Field - Only show for spouse/child registrations */}
                        {(formData.registrationType === 'spouse' ||
                          formData.registrationType === 'child') && (
                          <div
                            id="linked-attendee-email-field"
                            className="iepa-form-field mt-6"
                            data-testid="linked-attendee-email-field"
                          >
                            <HydrationSafeInput
                              id="linked-attendee-email-input"
                              label="Primary Attendee Email"
                              type="email"
                              placeholder="Enter the email address of the primary attendee"
                              value={formData.linkedAttendeeEmail}
                              onChange={e =>
                                handleInputChange(
                                  'linkedAttendeeEmail',
                                  e.target.value
                                )
                              }
                              isRequired
                              description="This should be the email address of the main attendee you are registering with"
                              className={
                                errors.linkedAttendeeEmail
                                  ? 'iepa-form-field-error'
                                  : ''
                              }
                              aria-describedby={
                                errors.linkedAttendeeEmail
                                  ? 'linkedAttendeeEmail-error'
                                  : undefined
                              }
                              data-testid="linked-attendee-email-input"
                            />
                            {errors.linkedAttendeeEmail && (
                              <div
                                id="linkedAttendeeEmail-error"
                                className="iepa-error-message"
                                role="alert"
                                data-testid="linkedAttendeeEmail-error"
                              >
                                {errors.linkedAttendeeEmail}
                              </div>
                            )}
                          </div>
                        )}
                      </CardBody>
                    </Card>
                  </section>

                  {/* Section 2: Personal Information */}
                  <section
                    id="personal-information"
                    ref={el => {
                      if (el && sectionRefs[1]) {
                        sectionRefs[1].current = el;
                      }
                    }}
                  >
                    <Card
                      className="iepa-form-card"
                      data-testid="personal-information-step"
                    >
                      <CardHeader>
                        <h2
                          className="iepa-compact-heading-2"
                          data-testid="personal-information-heading"
                        >
                          Personal Information
                        </h2>
                      </CardHeader>
                      <CardBody>
                        <div
                          id="personal-information-grid"
                          className="iepa-form-grid"
                          data-testid="personal-information-grid"
                        >
                          <div
                            id="first-name-field"
                            className="iepa-form-field"
                            data-testid="first-name-field"
                          >
                            <HydrationSafeInput
                              id="first-name-input"
                              label="First Name"
                              placeholder="Enter your first name"
                              value={formData.firstName}
                              onChange={e =>
                                handleInputChange('firstName', e.target.value)
                              }
                              isRequired
                              className={
                                errors.firstName ? 'iepa-form-field-error' : ''
                              }
                              aria-describedby={
                                errors.firstName ? 'firstName-error' : undefined
                              }
                              data-testid="first-name-input"
                            />
                            {errors.firstName && (
                              <div
                                id="firstName-error"
                                className="iepa-error-message"
                                role="alert"
                                data-testid="firstName-error"
                              >
                                {errors.firstName}
                              </div>
                            )}
                          </div>

                          <div
                            id="last-name-field"
                            className="iepa-form-field"
                            data-testid="last-name-field"
                          >
                            <HydrationSafeInput
                              id="last-name-input"
                              label="Last Name"
                              placeholder="Enter your last name"
                              value={formData.lastName}
                              onChange={e =>
                                handleInputChange('lastName', e.target.value)
                              }
                              isRequired
                              className={
                                errors.lastName ? 'iepa-form-field-error' : ''
                              }
                              aria-describedby={
                                errors.lastName ? 'lastName-error' : undefined
                              }
                              data-testid="last-name-input"
                            />
                            {errors.lastName && (
                              <div
                                id="lastName-error"
                                className="iepa-error-message"
                                role="alert"
                                data-testid="lastName-error"
                              >
                                {errors.lastName}
                              </div>
                            )}
                          </div>

                          <div className="iepa-form-field">
                            <HydrationSafeInput
                              label="Name on Badge"
                              placeholder="Name as it should appear on your badge"
                              value={formData.nameOnBadge}
                              onChange={e =>
                                handleInputChange('nameOnBadge', e.target.value)
                              }
                              isRequired
                              description="This is how your name will appear on your conference badge"
                              className={
                                errors.nameOnBadge
                                  ? 'iepa-form-field-error'
                                  : ''
                              }
                              aria-describedby={
                                errors.nameOnBadge
                                  ? 'nameOnBadge-error'
                                  : undefined
                              }
                            />
                            {errors.nameOnBadge && (
                              <div
                                id="nameOnBadge-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.nameOnBadge}
                              </div>
                            )}
                          </div>

                          <div className="iepa-form-field">
                            <HydrationSafeInput
                              label="Email Address"
                              type="email"
                              placeholder="<EMAIL>"
                              value={formData.email}
                              onChange={e =>
                                handleInputChange('email', e.target.value)
                              }
                              isRequired
                              className={
                                errors.email ? 'iepa-form-field-error' : ''
                              }
                              aria-describedby={
                                errors.email ? 'email-error' : undefined
                              }
                            />
                            {errors.email && (
                              <div
                                id="email-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.email}
                              </div>
                            )}
                          </div>

                          {/* Sponsor Discount Detection */}
                          <SponsorDiscountDetector
                            email={formData.email}
                            onDiscountDetected={handleSponsorDiscountDetected}
                            onDiscountCleared={handleSponsorDiscountCleared}
                            className="mt-4"
                          />

                          <div className="iepa-form-field">
                            <RadioGroup
                              label="Gender"
                              value={formData.gender}
                              onValueChange={value =>
                                handleInputChange('gender', value)
                              }
                              orientation="vertical"
                              isRequired
                              className={
                                errors.gender ? 'iepa-form-field-error' : ''
                              }
                              aria-describedby={
                                errors.gender ? 'gender-error' : undefined
                              }
                            >
                              {genderOptions.map(option => (
                                <Radio key={option.value} value={option.value}>
                                  {option.label}
                                </Radio>
                              ))}
                            </RadioGroup>
                            {errors.gender && (
                              <div
                                id="gender-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.gender}
                              </div>
                            )}
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  </section>

                  {/* Section 3: Contact Information */}
                  <section
                    id="contact-information"
                    ref={el => {
                      if (el && sectionRefs[2]) {
                        sectionRefs[2].current = el;
                      }
                    }}
                  >
                    <Card className="iepa-form-card">
                      <CardHeader>
                        <h2 className="iepa-compact-heading-2">
                          Contact Information
                        </h2>
                      </CardHeader>
                      <CardBody>
                        <div className="iepa-form-grid">
                          <div className="iepa-form-field">
                            <HydrationSafePhoneInput
                              label="Phone Number"
                              value={formData.phoneNumber}
                              onChange={value =>
                                handleInputChange('phoneNumber', value)
                              }
                              isRequired
                              className={
                                errors.phoneNumber
                                  ? 'iepa-form-field-error'
                                  : ''
                              }
                              aria-describedby={
                                errors.phoneNumber
                                  ? 'phoneNumber-error'
                                  : undefined
                              }
                            />
                            {errors.phoneNumber && (
                              <div
                                id="phoneNumber-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.phoneNumber}
                              </div>
                            )}
                          </div>

                          <div className="iepa-form-field">
                            <OrganizationCombobox
                              label="Organization"
                              placeholder="Select or enter your organization name"
                              value={formData.organization}
                              onChange={value =>
                                handleInputChange('organization', value)
                              }
                              isRequired
                              error={errors.organization}
                            />
                          </div>

                          <div className="iepa-form-field">
                            <HydrationSafeInput
                              label="Job Title"
                              placeholder="Your job title"
                              value={formData.jobTitle}
                              onChange={e =>
                                handleInputChange('jobTitle', e.target.value)
                              }
                              isRequired
                              className={
                                errors.jobTitle ? 'iepa-form-field-error' : ''
                              }
                              aria-describedby={
                                errors.jobTitle ? 'jobTitle-error' : undefined
                              }
                            />
                            {errors.jobTitle && (
                              <div
                                id="jobTitle-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.jobTitle}
                              </div>
                            )}
                          </div>

                          <div className="iepa-form-field col-span-full">
                            <HydrationSafeInput
                              label="Street Address"
                              placeholder="Street address"
                              value={formData.streetAddress}
                              onChange={e =>
                                handleInputChange(
                                  'streetAddress',
                                  e.target.value
                                )
                              }
                              isRequired
                              className={
                                errors.streetAddress
                                  ? 'iepa-form-field-error'
                                  : ''
                              }
                              aria-describedby={
                                errors.streetAddress
                                  ? 'streetAddress-error'
                                  : undefined
                              }
                            />
                            {errors.streetAddress && (
                              <div
                                id="streetAddress-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.streetAddress}
                              </div>
                            )}
                          </div>

                          <div className="iepa-form-field">
                            <HydrationSafeInput
                              label="City"
                              placeholder="City"
                              value={formData.city}
                              onChange={e =>
                                handleInputChange('city', e.target.value)
                              }
                              isRequired
                              className={
                                errors.city ? 'iepa-form-field-error' : ''
                              }
                              aria-describedby={
                                errors.city ? 'city-error' : undefined
                              }
                            />
                            {errors.city && (
                              <div
                                id="city-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.city}
                              </div>
                            )}
                          </div>

                          <div className="iepa-form-field">
                            <Label htmlFor="state-select">
                              State/Province{' '}
                              <span className="text-red-500 ml-1">*</span>
                            </Label>
                            <Select
                              value={formData.state}
                              onValueChange={value =>
                                handleInputChange('state', value)
                              }
                            >
                              <SelectTrigger
                                id="state-select"
                                className={cn(
                                  'w-full',
                                  errors.state ? 'iepa-form-field-error' : ''
                                )}
                                aria-describedby={
                                  errors.state ? 'state-error' : undefined
                                }
                              >
                                <SelectValue placeholder="Select state or province" />
                              </SelectTrigger>
                              <SelectContent>
                                {STATE_PROVINCE_OPTIONS.map(option => (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            {errors.state && (
                              <div
                                id="state-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.state}
                              </div>
                            )}
                          </div>

                          <div className="iepa-form-field">
                            <HydrationSafeInput
                              label="ZIP/Postal Code"
                              placeholder="12345 or A1A 1A1"
                              value={formData.zipCode}
                              onChange={e =>
                                handleInputChange('zipCode', e.target.value)
                              }
                              isRequired
                              className={
                                errors.zipCode ? 'iepa-form-field-error' : ''
                              }
                              aria-describedby={
                                errors.zipCode ? 'zipCode-error' : undefined
                              }
                            />
                            {errors.zipCode && (
                              <div
                                id="zipCode-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.zipCode}
                              </div>
                            )}
                          </div>

                          <div className="iepa-form-field">
                            <HydrationSafeInput
                              label="Country"
                              placeholder="Country"
                              value={formData.country}
                              onChange={e =>
                                handleInputChange('country', e.target.value)
                              }
                              description="Default: United States"
                            />
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  </section>

                  {/* Section 4: Event Options */}
                  <section
                    id="event-options"
                    ref={el => {
                      if (el && sectionRefs[3]) {
                        sectionRefs[3].current = el;
                      }
                    }}
                  >
                    <Card className="iepa-form-card">
                      <CardHeader>
                        <h2 className="iepa-compact-heading-2">
                          Event Options
                        </h2>
                        <p className="iepa-compact-body text-[var(--iepa-gray-600)]">
                          Select optional add-ons and meal preferences for the
                          conference.
                        </p>
                      </CardHeader>
                      <CardBody>
                        <div className="space-y-3">
                          {/* Golf Tournament Option */}
                          <div className="iepa-form-field">
                            <div className="p-3 border border-gray-200 rounded-lg">
                              <div className="flex items-start space-x-3">
                                <Checkbox
                                  id="golf-tournament"
                                  checked={formData.golfTournament}
                                  onCheckedChange={checked => {
                                    handleInputChange(
                                      'golfTournament',
                                      checked
                                    );
                                    // Reset club rental options if golf tournament is unchecked
                                    if (!checked) {
                                      handleInputChange(
                                        'golfClubRental',
                                        false
                                      );
                                      handleInputChange(
                                        'golfClubHandedness',
                                        ''
                                      );
                                    }
                                  }}
                                />
                                <div className="flex-1">
                                  <label
                                    htmlFor="golf-tournament"
                                    className="font-semibold text-lg cursor-pointer"
                                  >
                                    Golf Tournament (+$200)
                                  </label>
                                  <p className="iepa-body-small text-gray-600 mt-1">
                                    Join us for the annual golf tournament on
                                    the day before the conference
                                  </p>
                                </div>
                              </div>

                              {/* Golf Club Rental Options - Only show if golf tournament is selected */}
                              {formData.golfTournament && (
                                <div className="mt-4 ml-8 p-3 bg-gray-50 rounded-lg border border-gray-100">
                                  <div className="space-y-3">
                                    <div className="flex items-start space-x-3">
                                      <Checkbox
                                        id="golf-club-rental"
                                        checked={formData.golfClubRental}
                                        onCheckedChange={checked => {
                                          handleInputChange(
                                            'golfClubRental',
                                            checked
                                          );
                                          // Reset handedness if club rental is unchecked
                                          if (!checked) {
                                            handleInputChange(
                                              'golfClubHandedness',
                                              ''
                                            );
                                          }
                                        }}
                                      />
                                      <div className="flex-1">
                                        <label
                                          htmlFor="golf-club-rental"
                                          className="font-medium cursor-pointer"
                                        >
                                          Golf Club Rental (+$75)
                                        </label>
                                        <p className="text-sm text-gray-600 mt-1">
                                          Callaway Rogues, includes 6 Callaway
                                          Golf Balls
                                        </p>
                                      </div>
                                    </div>

                                    {/* Handedness Selection - Only show if club rental is selected */}
                                    {formData.golfClubRental && (
                                      <div className="ml-6">
                                        <Label className="text-sm font-medium mb-2 block">
                                          Club Handedness{' '}
                                          <span className="text-red-500 ml-1">
                                            *
                                          </span>
                                        </Label>
                                        <RadioGroup
                                          value={formData.golfClubHandedness}
                                          onValueChange={value =>
                                            handleInputChange(
                                              'golfClubHandedness',
                                              value
                                            )
                                          }
                                          orientation="horizontal"
                                          className="flex space-x-4"
                                        >
                                          <Radio value="right">
                                            Right Handed
                                          </Radio>
                                          <Radio value="left">
                                            Left Handed
                                          </Radio>
                                        </RadioGroup>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Night Selection */}
                          <div className="iepa-form-field">
                            <h3 className="font-semibold text-lg mb-3 text-[var(--iepa-primary-blue)]">
                              Conference Lodging
                            </h3>
                            <p className="iepa-body-small text-gray-600 mb-4">
                              Select which nights you plan to stay at the
                              conference center. Night One: Monday, September
                              15, 2025. Night Two: Tuesday, September 16, 2025.
                            </p>

                            <div className="space-y-3">
                              {(() => {
                                const nightOneInfo =
                                  dateUtils.getLodgingNightInfo('nightOne');
                                const nightTwoInfo =
                                  dateUtils.getLodgingNightInfo('nightTwo');

                                return (
                                  <>
                                    <div className="p-3 border border-gray-200 rounded-lg">
                                      <div className="flex items-start space-x-3">
                                        <Checkbox
                                          id="night-one"
                                          checked={formData.nightOne}
                                          onCheckedChange={checked =>
                                            handleInputChange(
                                              'nightOne',
                                              checked
                                            )
                                          }
                                        />
                                        <div className="flex-1">
                                          <label
                                            htmlFor="night-one"
                                            className="font-medium cursor-pointer"
                                          >
                                            <span className="hidden sm:inline">
                                              Night One (
                                              {nightOneInfo?.displayDate})
                                            </span>
                                            <span className="sm:hidden">
                                              Night One (
                                              {nightOneInfo?.mobileDate})
                                            </span>
                                          </label>
                                          <p className="text-sm text-gray-600 mt-1">
                                            {nightOneInfo?.description}
                                          </p>
                                        </div>
                                      </div>
                                    </div>

                                    <div className="p-3 border border-gray-200 rounded-lg">
                                      <div className="flex items-start space-x-3">
                                        <Checkbox
                                          id="night-two"
                                          checked={formData.nightTwo}
                                          onCheckedChange={checked =>
                                            handleInputChange(
                                              'nightTwo',
                                              checked
                                            )
                                          }
                                        />
                                        <div className="flex-1">
                                          <label
                                            htmlFor="night-two"
                                            className="font-medium cursor-pointer"
                                          >
                                            <span className="hidden sm:inline">
                                              Night Two (
                                              {nightTwoInfo?.displayDate})
                                            </span>
                                            <span className="sm:hidden">
                                              Night Two (
                                              {nightTwoInfo?.mobileDate})
                                            </span>
                                          </label>
                                          <p className="text-sm text-gray-600 mt-1">
                                            {nightTwoInfo?.description}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  </>
                                );
                              })()}
                            </div>
                          </div>

                          {/* Conference Meal Selections */}
                          <div className="iepa-form-field">
                            <h3 className="font-semibold text-lg mb-3 text-[var(--iepa-primary-blue)]">
                              Conference Meal Options
                            </h3>
                            <p className="iepa-body-small text-gray-600 mb-4">
                              Select the meals you would like to attend during
                              the conference. All meals are complimentary with
                              registration.
                            </p>

                            <div className="space-y-4">
                              {mealOptions.map(dayOption => (
                                <div
                                  key={dayOption.date}
                                  className="border border-gray-200 rounded-lg p-3"
                                >
                                  <h4 className="font-semibold text-base mb-2 text-[var(--iepa-primary-blue)]">
                                    <span className="hidden sm:inline">
                                      {dayOption.day}, {dayOption.date}
                                    </span>
                                    <span className="sm:hidden">
                                      {dateUtils.formatMobileDate(
                                        dayOption.date
                                      )}
                                    </span>
                                  </h4>
                                  <div className="space-y-2">
                                    {dayOption.meals.map(meal => (
                                      <div
                                        key={meal.key}
                                        className="p-2 border border-gray-100 rounded"
                                      >
                                        <div className="flex items-start space-x-3">
                                          <Checkbox
                                            id={`meal-${meal.key}`}
                                            checked={
                                              formData.meals[
                                                meal.key as keyof typeof formData.meals
                                              ]
                                            }
                                            onCheckedChange={checked =>
                                              handleMealChange(
                                                meal.key,
                                                checked
                                              )
                                            }
                                          />
                                          <div className="flex-1">
                                            <div className="flex justify-between items-start">
                                              <div>
                                                <label
                                                  htmlFor={`meal-${meal.key}`}
                                                  className="font-medium cursor-pointer"
                                                >
                                                  {meal.name} - {meal.time}
                                                </label>
                                                <p className="text-sm text-gray-600 mt-1">
                                                  {meal.description}
                                                </p>
                                              </div>
                                              <div className="font-semibold text-[var(--iepa-primary-blue)]">
                                                Complimentary
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Dietary Restrictions */}
                          <div className="iepa-form-field">
                            <HydrationSafeInput
                              label="Dietary Restrictions"
                              placeholder="Please list any dietary restrictions or allergies"
                              value={formData.dietaryRestrictions}
                              onChange={e =>
                                handleInputChange(
                                  'dietaryRestrictions',
                                  e.target.value
                                )
                              }
                              description="Help us accommodate your dietary needs during conference meals"
                            />
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  </section>

                  {/* Section 5: Emergency Contact */}
                  <section
                    id="emergency-contact"
                    ref={el => {
                      if (el && sectionRefs[4]) {
                        sectionRefs[4].current = el;
                      }
                    }}
                  >
                    <Card className="iepa-form-card">
                      <CardHeader>
                        <h2 className="iepa-compact-heading-2">
                          Emergency Contact
                        </h2>
                      </CardHeader>
                      <CardBody>
                        <div className="iepa-form-grid">
                          <div className="iepa-form-field">
                            <HydrationSafeInput
                              label="Emergency Contact Name"
                              placeholder="Contact person name"
                              value={formData.emergencyContact}
                              onChange={e =>
                                handleInputChange(
                                  'emergencyContact',
                                  e.target.value
                                )
                              }
                              isRequired
                              className={
                                errors.emergencyContact
                                  ? 'iepa-form-field-error'
                                  : ''
                              }
                              aria-describedby={
                                errors.emergencyContact
                                  ? 'emergencyContact-error'
                                  : undefined
                              }
                            />
                            {errors.emergencyContact && (
                              <div
                                id="emergencyContact-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.emergencyContact}
                              </div>
                            )}
                          </div>

                          <div className="iepa-form-field">
                            <HydrationSafePhoneInput
                              label="Emergency Contact Phone"
                              value={formData.emergencyPhone}
                              onChange={value =>
                                handleInputChange('emergencyPhone', value)
                              }
                              isRequired
                              className={
                                errors.emergencyPhone
                                  ? 'iepa-form-field-error'
                                  : ''
                              }
                              aria-describedby={
                                errors.emergencyPhone
                                  ? 'emergencyPhone-error'
                                  : undefined
                              }
                            />
                            {errors.emergencyPhone && (
                              <div
                                id="emergencyPhone-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.emergencyPhone}
                              </div>
                            )}
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  </section>

                  {/* Section 6: Review & Payment */}
                  <section
                    id="review-payment"
                    ref={el => {
                      if (el && sectionRefs[5]) {
                        sectionRefs[5].current = el;
                      }
                    }}
                  >
                    <Card className="iepa-form-card">
                      <CardHeader>
                        <h2 className="iepa-compact-heading-2">
                          Review & Payment
                        </h2>
                      </CardHeader>
                      <CardBody>
                        <div className="iepa-review-grid">
                          {/* Registration Summary - Left Column */}
                          <div className="iepa-review-summary">
                            {(() => {
                              const selectedPricing = REGISTRATION_PRICING.find(
                                p => p.id === formData.registrationType
                              );

                              // Calculate meal costs
                              const selectedMeals = Object.entries(
                                formData.meals
                              )
                                .filter(([, selected]) => selected)
                                .map(([mealKey]) => {
                                  // Find the meal in mealOptions
                                  for (const dayOption of mealOptions) {
                                    const meal = dayOption.meals.find(
                                      m => m.key === mealKey
                                    );
                                    if (meal) return meal;
                                  }
                                  return null;
                                })
                                .filter(Boolean);

                              const mealTotal = selectedMeals.reduce(
                                (sum, meal) => sum + (meal?.price || 0),
                                0
                              );
                              const golfCost = formData.golfTournament
                                ? 200
                                : 0;
                              const golfClubRentalCost = formData.golfClubRental
                                ? 70
                                : 0;
                              const originalTotal =
                                (selectedPricing?.basePrice || 0) +
                                golfCost +
                                golfClubRentalCost +
                                mealTotal;

                              // Apply sponsor discount if available
                              const finalTotal =
                                sponsorDiscount?.isEligible &&
                                sponsorDiscount.discountAmount === 100
                                  ? 0
                                  : originalTotal;

                              return selectedPricing ? (
                                <div className="p-3 rounded-lg bg-[var(--iepa-primary-blue)] text-white">
                                  <h3 className="text-lg font-bold mb-2">
                                    Registration Summary
                                  </h3>
                                  <div className="space-y-1">
                                    <div className="flex justify-between">
                                      <span>
                                        {selectedPricing.displayName}:
                                      </span>
                                      <span>
                                        $
                                        {selectedPricing.basePrice.toLocaleString()}
                                      </span>
                                    </div>
                                    {formData.golfTournament && (
                                      <div className="flex justify-between">
                                        <span>Golf Tournament:</span>
                                        <span>$200</span>
                                      </div>
                                    )}
                                    {formData.golfClubRental && (
                                      <div className="flex justify-between">
                                        <span>
                                          Golf Club Rental (
                                          {formData.golfClubHandedness} handed):
                                        </span>
                                        <span>$75</span>
                                      </div>
                                    )}
                                    {selectedMeals.map(meal => (
                                      <div
                                        key={meal?.key}
                                        className="flex justify-between"
                                      >
                                        <span>
                                          {meal?.name} (
                                          {meal?.key?.includes('sept15')
                                            ? 'Sep 15'
                                            : meal?.key?.includes('sept16')
                                              ? 'Sep 16'
                                              : 'Sep 17'}
                                          ):
                                        </span>
                                        <span>${meal?.price}</span>
                                      </div>
                                    ))}
                                    {(golfCost > 0 ||
                                      golfClubRentalCost > 0 ||
                                      mealTotal > 0) && (
                                      <hr className="my-2 opacity-30" />
                                    )}

                                    {/* Show sponsor discount if applied */}
                                    {sponsorDiscount?.isEligible &&
                                      sponsorDiscount.discountAmount ===
                                        100 && (
                                        <>
                                          <div className="flex justify-between">
                                            <span>Subtotal:</span>
                                            <span>
                                              ${originalTotal.toLocaleString()}
                                            </span>
                                          </div>
                                          <div className="flex justify-between text-green-200">
                                            <span>
                                              Sponsor Discount (
                                              {sponsorDiscount.sponsorName}):
                                            </span>
                                            <span>
                                              -${originalTotal.toLocaleString()}
                                            </span>
                                          </div>
                                        </>
                                      )}

                                    <div className="flex justify-between text-lg font-bold">
                                      <span>Total:</span>
                                      <span>
                                        ${finalTotal.toLocaleString()}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              ) : null;
                            })()}
                          </div>

                          {/* Registration Details - Right Column */}
                          <div className="iepa-review-details space-y-3">
                            <div>
                              <h4 className="font-semibold mb-1">
                                Personal Information
                              </h4>
                              <div className="text-sm space-y-0.5">
                                <p>
                                  <strong>Name:</strong>{' '}
                                  {`${formData.firstName} ${formData.lastName}`.trim()}
                                </p>
                                <p>
                                  <strong>Email:</strong> {formData.email}
                                </p>
                                <p>
                                  <strong>Badge Name:</strong>{' '}
                                  {formData.nameOnBadge}
                                </p>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-semibold mb-1">
                                Contact Information
                              </h4>
                              <div className="text-sm space-y-0.5">
                                <p>
                                  <strong>Phone:</strong> {formData.phoneNumber}
                                </p>
                                <p>
                                  <strong>Organization:</strong>{' '}
                                  {formData.organization}
                                </p>
                                <p>
                                  <strong>Job Title:</strong>{' '}
                                  {formData.jobTitle}
                                </p>
                              </div>
                            </div>

                            {/* Lodging Summary */}
                            {(formData.nightOne || formData.nightTwo) && (
                              <div>
                                <h4 className="font-semibold mb-1">
                                  Conference Lodging
                                </h4>
                                <div className="text-sm space-y-0.5">
                                  {formData.nightOne && (
                                    <p>
                                      <strong>
                                        <span className="hidden sm:inline">
                                          Night One (
                                          {
                                            dateUtils.getLodgingNightInfo(
                                              'nightOne'
                                            )?.shortDate
                                          }
                                          ):
                                        </span>
                                        <span className="sm:hidden">
                                          Night One (
                                          {
                                            dateUtils.getLodgingNightInfo(
                                              'nightOne'
                                            )?.mobileDate
                                          }
                                          ):
                                        </span>
                                      </strong>{' '}
                                      Yes
                                    </p>
                                  )}
                                  {formData.nightTwo && (
                                    <p>
                                      <strong>
                                        <span className="hidden sm:inline">
                                          Night Two (
                                          {
                                            dateUtils.getLodgingNightInfo(
                                              'nightTwo'
                                            )?.shortDate
                                          }
                                          ):
                                        </span>
                                        <span className="sm:hidden">
                                          Night Two (
                                          {
                                            dateUtils.getLodgingNightInfo(
                                              'nightTwo'
                                            )?.mobileDate
                                          }
                                          ):
                                        </span>
                                      </strong>{' '}
                                      Yes
                                    </p>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Event Options and Meals Summary */}
                            {(formData.golfTournament ||
                              Object.values(formData.meals).some(Boolean) ||
                              formData.dietaryRestrictions) && (
                              <div>
                                <h4 className="font-semibold mb-1">
                                  Event Options
                                </h4>
                                <div className="text-sm space-y-0.5">
                                  {formData.golfTournament && (
                                    <p>
                                      <strong>Golf Tournament:</strong> Yes
                                      (+$200)
                                    </p>
                                  )}
                                  {formData.golfClubRental && (
                                    <p>
                                      <strong>Golf Club Rental:</strong> Yes -{' '}
                                      {formData.golfClubHandedness} handed
                                      (+$70)
                                    </p>
                                  )}
                                  {Object.entries(formData.meals)
                                    .filter(([, selected]) => selected)
                                    .map(([mealKey]) => {
                                      // Find the meal details
                                      for (const dayOption of mealOptions) {
                                        const meal = dayOption.meals.find(
                                          m => m.key === mealKey
                                        );
                                        if (meal) {
                                          const dateLabel = mealKey.includes(
                                            'sept15'
                                          )
                                            ? 'Sep 15'
                                            : mealKey.includes('sept16')
                                              ? 'Sep 16'
                                              : 'Sep 17';
                                          return (
                                            <p key={mealKey}>
                                              <strong>
                                                {meal.name} ({dateLabel}):
                                              </strong>{' '}
                                              Yes (Complimentary)
                                            </p>
                                          );
                                        }
                                      }
                                      return null;
                                    })}
                                  {formData.dietaryRestrictions && (
                                    <p>
                                      <strong>Dietary Restrictions:</strong>{' '}
                                      {formData.dietaryRestrictions}
                                    </p>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Cancellation Policy - Displayed before payment */}
                        <div className="mt-6">
                          <CancellationPolicy />
                        </div>
                      </CardBody>
                    </Card>
                  </section>

                  {/* Submit Button */}
                  <div className="iepa-form-navigation">
                    <div className="flex justify-center">
                      <Button
                        type="submit"
                        disabled={!isFormValid() || isSubmitting}
                        className={`px-8 py-3 text-lg font-semibold transition-colors ${
                          !isFormValid() || isSubmitting
                            ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700 text-white cursor-pointer'
                        }`}
                        data-testid="submit-registration-button"
                      >
                        {isSubmitting
                          ? 'Submitting Registration...'
                          : 'Complete Registration'}
                      </Button>
                    </div>
                  </div>
                </form>
              </div>
            </section>

            {/* Toast Notification */}
            {notification.type && (
              <div
                id="registration-notification"
                className={`fixed top-4 right-4 z-50 max-w-md p-6 rounded-lg shadow-lg border-l-4 ${
                  notification.type === 'success'
                    ? 'bg-green-50 border-green-500 text-green-800'
                    : 'bg-red-50 border-red-500 text-red-800'
                }`}
                role="alert"
                aria-live="polite"
                data-testid={`registration-notification-${notification.type}`}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {notification.type === 'success' ? (
                      <FaCheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <FaExclamationTriangle className="w-5 h-5 text-red-600" />
                    )}
                  </div>
                  <div className="ml-3 flex-1">
                    <h3
                      id="notification-title"
                      className="text-sm font-medium"
                      data-testid="notification-title"
                    >
                      {notification.title}
                    </h3>
                    <p
                      id="notification-message"
                      className="mt-1 text-sm"
                      data-testid="notification-message"
                    >
                      {notification.message}
                    </p>
                    {notification.registrationId && (
                      <div
                        id="notification-details"
                        className="mt-2 text-sm"
                        data-testid="notification-details"
                      >
                        <p>
                          <strong>Registration ID:</strong>{' '}
                          {notification.registrationId}
                        </p>
                        {notification.totalAmount && (
                          <p>
                            <strong>Total Amount:</strong> $
                            {notification.totalAmount.toLocaleString()}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="ml-4 flex-shrink-0">
                    <button
                      id="notification-close-button"
                      type="button"
                      className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      onClick={() =>
                        setNotification({ type: null, title: '', message: '' })
                      }
                      data-testid="notification-close-button"
                    >
                      <span className="sr-only">Close notification</span>
                      <svg
                        className="w-5 h-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Floating Save Status */}
            <FloatingSaveStatus
              isAutoSaving={persistence.isAutoSaving}
              lastSavedAt={persistence.lastSavedAt}
              show={true}
              position="bottom-right"
            />
          </>
        )}
      </div>
    </ProtectedRegistrationPage>
  );
}
