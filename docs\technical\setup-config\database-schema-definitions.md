speaker
create table public.iepa_speaker_registrations (
  id uuid not null default gen_random_uuid (),
  user_id uuid null,
  full_name text not null,
  first_name text not null,
  last_name text not null,
  organization_name text not null,
  job_title text not null,
  presentation_file_url text null,
  bio text not null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  email text not null default ''::text,
  phone_number text null,
  preferred_contact_method text null default 'email'::text,
  presentation_title text null,
  presentation_description text null,
  presentation_duration text null,
  target_audience text null,
  learning_objectives text null,
  speaker_experience text null,
  previous_speaking text null,
  equipment_needs text null,
  special_requests text null,
  headshot_url text null,
  invoice_url text null,
  receipt_url text null,
  invoice_generated_at timestamp with time zone null,
  receipt_generated_at timestamp with time zone null,
  constraint iepa_speaker_registrations_pkey primary key (id),
  constraint iepa_speaker_registrations_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create trigger update_iepa_speaker_registrations_updated_at BEFORE
update on iepa_speaker_registrations for EACH row
execute FUNCTION update_updated_at_column ();


create table public.iepa_attendee_registrations (
  id uuid not null default gen_random_uuid (),
  user_id uuid null,
  registration_type text not null,
  full_name text not null,
  email text not null,
  first_name text not null,
  last_name text not null,
  name_on_badge text not null,
  gender text not null,
  phone_number text not null,
  street_address text not null,
  city text not null,
  state text not null,
  zip_code text not null,
  organization text not null,
  job_title text not null,
  attending_golf boolean null default false,
  meals text[] null default '{}'::text[],
  dietary_needs text null default ''::text,
  registration_total numeric(10, 2) null default 0,
  golf_total numeric(10, 2) null default 0,
  grand_total numeric(10, 2) null default 0,
  payment_status text null default 'pending'::text,
  payment_id text null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  golf_club_handedness text null default ''::text,
  golf_club_rental_total numeric null default 0,
  golf_club_rental boolean null default false,
  invoice_url text null,
  receipt_url text null,
  invoice_generated_at timestamp with time zone null,
  receipt_generated_at timestamp with time zone null,
  constraint iepa_attendee_registrations_pkey primary key (id),
  constraint iepa_attendee_registrations_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create trigger update_iepa_attendee_registrations_updated_at BEFORE
update on iepa_attendee_registrations for EACH row
execute FUNCTION update_updated_at_column ();