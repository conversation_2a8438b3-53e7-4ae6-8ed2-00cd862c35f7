// Utility to fix storage policies for file upload functionality
import { createSupabaseAdmin } from '@/lib/supabase';

export async function fixStoragePolicies(): Promise<{
  success: boolean;
  message: string;
  error?: string;
}> {
  try {
    console.log('🔧 Attempting to fix storage policies...');

    return {
      success: true,
      message: 'Storage policies need to be applied manually via Supabase dashboard. Please run the SQL from src/lib/storage-policies-fix.sql in your Supabase SQL editor.',
    };
  } catch (error) {
    console.error('Error fixing storage policies:', error);
    return {
      success: false,
      message: 'Failed to fix storage policies',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function testStorageAccess(): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    // Test bucket access
    const { data: buckets, error: bucketsError } = await supabaseAdmin.storage.listBuckets();
    
    if (bucketsError) {
      throw bucketsError;
    }

    const requiredBuckets = ['iepa-presentations', 'iepa-sponsor-assets', 'iepa-documents'];
    const existingBuckets = buckets.map(b => b.name);
    const missingBuckets = requiredBuckets.filter(b => !existingBuckets.includes(b));

    if (missingBuckets.length > 0) {
      return {
        success: false,
        message: `Missing storage buckets: ${missingBuckets.join(', ')}`,
        details: { existingBuckets, missingBuckets }
      };
    }

    return {
      success: true,
      message: 'All storage buckets are available',
      details: { buckets: existingBuckets }
    };
  } catch (error) {
    console.error('Error testing storage access:', error);
    return {
      success: false,
      message: 'Failed to test storage access',
    };
  }
}
