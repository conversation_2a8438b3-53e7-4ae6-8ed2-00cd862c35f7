'use client';

import { AuthGuard, RegistrationAuthGuard } from './AuthGuard';

interface ProtectedRouteProps {
  children: React.ReactNode;
  type?: 'default' | 'registration' | 'admin';
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export function ProtectedRoute({
  children,
  type = 'default',
  fallback,
  redirectTo,
}: ProtectedRouteProps) {
  if (type === 'registration') {
    return <RegistrationAuthGuard>{children}</RegistrationAuthGuard>;
  }

  if (type === 'admin') {
    return (
      <AuthGuard redirectTo="/auth/login" fallback={fallback}>
        {children}
      </AuthGuard>
    );
  }

  return (
    <AuthGuard redirectTo={redirectTo || '/auth/login'} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

// Convenience components for common use cases
export function ProtectedRegistrationPage({
  children,
}: {
  children: React.ReactNode;
}) {
  return <ProtectedRoute type="registration">{children}</ProtectedRoute>;
}

export function ProtectedAdminPage({
  children,
}: {
  children: React.ReactNode;
}) {
  return <ProtectedRoute type="admin">{children}</ProtectedRoute>;
}

export function ProtectedUserPage({ children }: { children: React.ReactNode }) {
  return <ProtectedRoute type="default">{children}</ProtectedRoute>;
}
