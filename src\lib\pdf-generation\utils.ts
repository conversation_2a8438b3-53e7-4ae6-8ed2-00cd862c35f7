// PDF Generation Utilities for IEPA Conference Registration
// Helper functions for PDF data formatting and processing

import {
  AttendeeRegistrationData,
  SpeakerRegistrationData,
  SponsorRegistrationData,
  PDFLineItem,
  PDFReceiptData,
} from './types';
import { DOCUMENT_NUMBERING } from './config';
import {
  REGISTRATION_PRICING,
  SPEAKER_PRICING,
  SPONSORSHIP_PACKAGES,
} from '@/lib/pricing-config';

/**
 * Format currency for PDF display
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Format date for PDF display
 */
export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

/**
 * Format date and time for PDF display
 */
export const formatDateTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Generate line items for attendee registration
 */
export const generateAttendeeLineItems = (
  data: AttendeeRegistrationData
): PDFLineItem[] => {
  const lineItems: PDFLineItem[] = [];

  // Registration fee with inclusions breakdown
  // Check both regular registration pricing and speaker pricing
  let registrationPricing = REGISTRATION_PRICING.find(
    p => p.id === data.registrationType
  );

  // If not found in regular pricing, check speaker pricing
  if (!registrationPricing) {
    registrationPricing = SPEAKER_PRICING.find(
      p => p.id === data.registrationType
    );
  }

  if (registrationPricing) {
    // For speaker registrations, use simplified naming
    let registrationDescription = registrationPricing.displayName;
    if (
      data.registrationType === 'comped-speaker' ||
      data.registrationType === 'full-meeting-speaker'
    ) {
      registrationDescription = 'Speaker Registration';
    }

    lineItems.push({
      description: registrationDescription,
      quantity: 1,
      unitPrice: data.registrationTotal || registrationPricing.basePrice,
      total: data.registrationTotal || registrationPricing.basePrice,
      category: 'registration',
    });

    // Add registration inclusions as separate line items (complimentary) - but not for speakers
    if (
      registrationPricing.inclusions &&
      registrationPricing.inclusions.length > 0 &&
      data.registrationType !== 'comped-speaker' &&
      data.registrationType !== 'full-meeting-speaker'
    ) {
      registrationPricing.inclusions.forEach(inclusion => {
        lineItems.push({
          description: `  • ${inclusion}`,
          quantity: 1,
          unitPrice: 0,
          total: 0,
          category: 'inclusion',
        });
      });
    }
  }

  // Lodging nights breakdown (if available in data)
  if (data.nightOne || data.nightTwo) {
    const nights: string[] = [];
    if (data.nightOne) nights.push('September 15-16, 2025 (Monday night)');
    if (data.nightTwo) nights.push('September 16-17, 2025 (Tuesday night)');

    nights.forEach(night => {
      lineItems.push({
        description: `Lodging: ${night}`,
        quantity: 1,
        unitPrice: 0,
        total: 0,
        category: 'lodging',
      });
    });
  }

  // Individual meal selections - handle both array and object formats
  if (data.meals) {
    const mealMapping = {
      sept15Dinner: 'Sept 15 Welcome Reception & Dinner',
      sept16Breakfast: 'Sept 16 Breakfast',
      sept16Lunch: 'Sept 16 Lunch',
      sept16Dinner: 'Sept 16 Reception & Dinner',
      sept17Breakfast: 'Sept 17 Breakfast',
      sept17Lunch: 'Sept 17 Closing Lunch',
      // Legacy meal keys for backward compatibility
      'day1-reception': 'Sept 15 Welcome Reception & Dinner',
      'day2-breakfast': 'Sept 16 Breakfast',
      'day2-lunch': 'Sept 16 Lunch',
      'day2-dinner': 'Sept 16 Reception & Dinner',
      'day3-breakfast': 'Sept 17 Breakfast',
      'day3-lunch': 'Sept 17 Closing Lunch',
    };

    let mealKeys: string[] = [];

    // Handle both array format (from database) and object format (from forms)
    if (Array.isArray(data.meals)) {
      mealKeys = data.meals;
    } else if (typeof data.meals === 'object') {
      mealKeys = Object.entries(data.meals)
        .filter(([, selected]) => selected)
        .map(([mealKey]) => mealKey);
    }

    mealKeys.forEach(mealKey => {
      const mealName =
        mealMapping[mealKey as keyof typeof mealMapping] || mealKey;
      lineItems.push({
        description: mealName,
        quantity: 1,
        unitPrice: 0,
        total: 0,
        category: 'meals',
      });
    });
  }

  // Golf tournament
  if (data.golfTournament) {
    lineItems.push({
      description: 'Golf Tournament - September 15, 2025',
      quantity: 1,
      unitPrice: data.golfTotal || 200,
      total: data.golfTotal || 200,
      category: 'golf',
    });
  }

  // Golf club rental
  if (data.golfClubRental) {
    const handedness = data.golfClubHandedness
      ? ` (${data.golfClubHandedness} handed)`
      : '';
    lineItems.push({
      description: `Golf Club Rental${handedness}`,
      quantity: 1,
      unitPrice: data.golfClubRentalTotal || 75,
      total: data.golfClubRentalTotal || 75,
      category: 'golf',
    });
  }

  // Dietary restrictions (if specified)
  if (data.dietaryNeeds && data.dietaryNeeds.trim()) {
    lineItems.push({
      description: `Special Dietary Requirements: ${data.dietaryNeeds}`,
      quantity: 1,
      unitPrice: 0,
      total: 0,
      category: 'special',
    });
  }

  return lineItems;
};

/**
 * Generate line items for speaker registration
 */
export const generateSpeakerLineItems = (): PDFLineItem[] => {
  const lineItems: PDFLineItem[] = [];

  // Speaker registration is typically complimentary
  lineItems.push({
    description: 'Speaker Registration (Complimentary)',
    quantity: 1,
    unitPrice: 0,
    total: 0,
    category: 'registration',
  });

  return lineItems;
};

/**
 * Generate line items for sponsor registration
 */
export const generateSponsorLineItems = (
  data: SponsorRegistrationData
): PDFLineItem[] => {
  const lineItems: PDFLineItem[] = [];

  // Sponsorship package
  const sponsorshipPackage = SPONSORSHIP_PACKAGES.find(
    p => p.id === data.sponsorshipLevel
  );
  if (sponsorshipPackage && data.sponsorshipTotal > 0) {
    lineItems.push({
      description: `${sponsorshipPackage.name} Sponsorship Package`,
      quantity: 1,
      unitPrice: data.sponsorshipTotal,
      total: data.sponsorshipTotal,
      category: 'sponsorship',
    });
  }

  return lineItems;
};

/**
 * Create receipt data from registration data
 */
export const createReceiptData = (
  type: 'attendee' | 'speaker' | 'sponsor',
  registrationData:
    | AttendeeRegistrationData
    | SpeakerRegistrationData
    | SponsorRegistrationData,
  paymentMethod?: string,
  transactionId?: string
): PDFReceiptData => {
  let lineItems: PDFLineItem[] = [];
  let total = 0;

  switch (type) {
    case 'attendee':
      lineItems = generateAttendeeLineItems(
        registrationData as AttendeeRegistrationData
      );
      total = (registrationData as AttendeeRegistrationData).grandTotal;
      break;
    case 'speaker':
      lineItems = generateSpeakerLineItems();
      total = 0; // Speakers typically have complimentary registration
      break;
    case 'sponsor':
      lineItems = generateSponsorLineItems(
        registrationData as SponsorRegistrationData
      );
      total = (registrationData as SponsorRegistrationData).sponsorshipTotal;
      break;
  }

  const subtotal = lineItems.reduce((sum, item) => sum + item.total, 0);
  const tax = 0; // No tax currently applied

  return {
    type,
    registrationData,
    receiptNumber: DOCUMENT_NUMBERING.generateReceiptNumber(
      registrationData.id
    ),
    issueDate: formatDate(new Date()),
    paymentMethod,
    transactionId,
    lineItems,
    subtotal,
    tax,
    total: Math.max(total, subtotal + tax),
  };
};

/**
 * Get customer information for PDF
 */
export const getCustomerInfo = (
  type: 'attendee' | 'speaker' | 'sponsor',
  registrationData:
    | AttendeeRegistrationData
    | SpeakerRegistrationData
    | SponsorRegistrationData
) => {
  switch (type) {
    case 'attendee': {
      const data = registrationData as AttendeeRegistrationData;
      return {
        name: `${data.firstName} ${data.lastName}`.trim(),
        email: data.email,
        phone: data.phoneNumber,
        organization: data.organization,
        jobTitle: data.jobTitle,
        address: {
          street: data.streetAddress,
          city: data.city,
          state: data.state,
          zipCode: data.zipCode,
          country: data.country,
        },
      };
    }
    case 'speaker': {
      const data = registrationData as SpeakerRegistrationData;
      return {
        name:
          data.fullName ||
          `${data.firstName || ''} ${data.lastName || ''}`.trim(),
        email: data.email,
        phone: data.phoneNumber,
        organization: data.organizationName,
        jobTitle: data.jobTitle,
        address: null, // Speakers don't provide address
      };
    }
    case 'sponsor': {
      const data = registrationData as SponsorRegistrationData;
      return {
        name: data.contactName,
        email: data.contactEmail,
        phone: data.contactPhone,
        organization: data.sponsorName,
        jobTitle: data.contactTitle,
        address: {
          street: data.billingAddress,
          city: data.billingCity,
          state: data.billingState,
          zipCode: data.billingZip,
          country: data.billingCountry,
        },
      };
    }
  }
};

/**
 * Validate registration data for PDF generation
 */
export const validateRegistrationData = (
  type: 'attendee' | 'speaker' | 'sponsor',
  data: Record<string, unknown>
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Common validations
  if (!data.id) errors.push('Registration ID is required');

  // Check for name fields based on type
  if (type === 'sponsor') {
    if (!data.sponsorName) errors.push('Sponsor name is required');
  } else {
    // For attendee and speaker, check for firstName/lastName or fullName
    const hasName = data.fullName || (data.firstName && data.lastName);
    if (!hasName) errors.push('Name is required (firstName and lastName)');
  }

  // Check for email based on type
  if (type === 'sponsor') {
    if (!data.contactEmail) errors.push('Contact email is required');
  } else {
    if (!data.email) errors.push('Email is required');
  }

  // Type-specific validations
  switch (type) {
    case 'attendee':
      if (!data.registrationType) errors.push('Registration type is required');
      if (
        data.grandTotal === undefined ||
        data.grandTotal === null ||
        (typeof data.grandTotal === 'number' && data.grandTotal < 0)
      )
        errors.push('Valid total amount is required');
      break;
    case 'speaker':
      if (!data.presentationTitle)
        errors.push('Presentation title is required');
      break;
    case 'sponsor':
      if (!data.sponsorshipLevel) errors.push('Sponsorship level is required');
      if (!data.sponsorName) errors.push('Sponsor name is required');
      break;
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Generate file name for PDF
 */
export const generatePDFFileName = (
  documentType: 'receipt' | 'invoice',
  registrationType: 'attendee' | 'speaker' | 'sponsor',
  registrationId: string
): string => {
  const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
  const shortId = registrationId.slice(-8).toUpperCase();
  return `${documentType}-${registrationType}-${shortId}-${timestamp}.pdf`;
};
