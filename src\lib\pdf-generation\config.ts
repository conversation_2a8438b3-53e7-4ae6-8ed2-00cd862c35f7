// PDF Generation Configuration for IEPA Annual Meeting Registration
// Configuration constants and settings for PDF generation

import {
  PDFCompanyInfo,
  PDFConferenceInfo,
  PDFGenerationOptions,
} from './types';
import { CONFERENCE_DATES, CONFERENCE_YEAR } from '@/lib/conference-config';

// IEPA Company Information for PDFs
export const IEPA_COMPANY_INFO: PDFCompanyInfo = {
  name: 'IEPA',
  fullName: 'Independent Energy Producers Association',
  address: {
    street: 'P.O. Box 1287',
    city: 'Sloughhouse',
    state: 'CA',
    zipCode: '95683-9998',
    country: 'United States',
  },
  phone: '(*************',
  email: '<EMAIL>',
  supportEmail: '<EMAIL>', // Support contact for receipt questions
  website: 'www.iepa.com',
  taxId: 'XX-XXXXXXX', // Update with actual tax ID if needed
};

// Conference Information for PDFs
export const CONFERENCE_INFO: PDFConferenceInfo = {
  name: `IEPA ${CONFERENCE_YEAR} Annual Meeting`,
  year: CONFERENCE_YEAR,
  dates: {
    start: CONFERENCE_DATES.startDate.displayDate,
    end: CONFERENCE_DATES.endDate.displayDate,
  },
  venue: {
    name: 'Stanford Sierra Conference Center',
    address: '130 Fallen Leaf Rd',
    city: 'South Lake Tahoe',
    state: 'CA',
  },
};

// Default PDF Generation Options
export const DEFAULT_PDF_OPTIONS: PDFGenerationOptions = {
  includeWatermark: false,
  includeLogo: true,
  format: 'Letter',
  orientation: 'portrait',
  margins: {
    top: 72, // 1 inch
    right: 72, // 1 inch
    bottom: 72, // 1 inch
    left: 72, // 1 inch
  },
};

// IEPA Official Brand Colors for PDFs (matching CSS variables)
export const IEPA_PDF_COLORS = {
  primaryBlue: '#396DA4',
  primaryBlueLight: '#4d7fb5',
  primaryBlueDark: '#2d5a8a',
  secondaryGreen: '#5EAE50',
  secondaryGreenLight: '#72c164',
  secondaryGreenDark: '#4a9b3c',
  accentTeal: '#17a2b8',
  accentTealLight: '#20c4dc',
  accentTealDark: '#138496',
  gray50: '#f8f9fa',
  gray100: '#e9ecef',
  gray200: '#dee2e6',
  gray300: '#ced4da',
  gray400: '#adb5bd',
  gray500: '#6c757d',
  gray600: '#495057',
  gray700: '#343a40',
  gray800: '#212529',
  gray900: '#1a1e21',
  white: '#ffffff',
  black: '#000000',
};

// PDF Font Configuration
export const PDF_FONTS = {
  primary: 'Helvetica',
  secondary: 'Helvetica-Bold',
  mono: 'Courier',
};

// PDF Layout Constants
export const PDF_LAYOUT = {
  pageWidth: 612, // Letter size width in points
  pageHeight: 792, // Letter size height in points
  headerHeight: 120,
  footerHeight: 60,
  logoWidth: 120,
  logoHeight: 40,
  lineHeight: 1.4,
  sectionSpacing: 20,
  itemSpacing: 10,
};

// PDF Text Styles
export const PDF_STYLES = {
  title: {
    fontSize: 24,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 18,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
    marginBottom: 15,
  },
  heading: {
    fontSize: 14,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.gray800,
    marginBottom: 10,
  },
  body: {
    fontSize: 11,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray800,
    lineHeight: PDF_LAYOUT.lineHeight,
  },
  small: {
    fontSize: 9,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray600,
  },
  bold: {
    fontSize: 11,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.gray800,
  },
  price: {
    fontSize: 12,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
  },
  total: {
    fontSize: 14,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
  },
};

// Supabase Storage Configuration
export const PDF_STORAGE_CONFIG = {
  bucket: 'iepa-documents',
  folders: {
    receipts: 'receipts',
    temp: 'temp',
  },
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedMimeTypes: ['application/pdf'],
};

// PDF File Naming Conventions
export const PDF_NAMING = {
  receipt: (registrationId: string, type: string) =>
    `receipt-${type}-${registrationId}-${Date.now()}.pdf`,
  temp: (prefix: string) => `temp-${prefix}-${Date.now()}.pdf`,
};

// Receipt Numbers
export const DOCUMENT_NUMBERING = {
  receiptPrefix: 'IEPA-RCP',
  yearPrefix: CONFERENCE_YEAR.toString(),
  generateReceiptNumber: (registrationId: string) =>
    `${DOCUMENT_NUMBERING.receiptPrefix}-${DOCUMENT_NUMBERING.yearPrefix}-${registrationId.slice(-8).toUpperCase()}`,
};

// PDF Generation Error Messages
export const PDF_ERROR_MESSAGES = {
  GENERATION_FAILED: 'Failed to generate PDF document',
  STORAGE_FAILED: 'Failed to store PDF document',
  INVALID_DATA: 'Invalid registration data provided',
  TEMPLATE_ERROR: 'PDF template rendering error',
  UPLOAD_FAILED: 'Failed to upload PDF to storage',
  DOWNLOAD_FAILED: 'Failed to download PDF from storage',
};

// PDF Quality Settings
export const PDF_QUALITY = {
  dpi: 300,
  compression: 'flate',
  imageQuality: 0.9,
};

// Email Integration Settings
export const PDF_EMAIL_CONFIG = {
  maxAttachmentSize: 25 * 1024 * 1024, // 25MB (most email providers limit)
  defaultSubject: {
    receipt: (conferenceYear: number) =>
      `IEPA ${conferenceYear} Annual Meeting Registration Receipt`,
  },
  templates: {
    receipt: 'receipt-email-template',
  },
};

// Validation Rules
export const PDF_VALIDATION = {
  requiredFields: {
    attendee: [
      'firstName',
      'lastName',
      'email',
      'registrationType',
      'grandTotal',
    ],
    speaker: ['firstName', 'lastName', 'email', 'presentationTitle'],
    sponsor: ['sponsorName', 'contactEmail', 'sponsorshipLevel'],
  },
  maxFieldLengths: {
    name: 100,
    email: 255,
    address: 200,
    description: 1000,
    notes: 500,
  },
};

// Development and Testing
export const PDF_DEV_CONFIG = {
  enableWatermark: process.env.NODE_ENV === 'development',
  debugMode: process.env.NODE_ENV === 'development',
  testDataEnabled: process.env.NODE_ENV === 'development',
  logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'error',
};
