const { test, expect } = require('@playwright/test');

// Test users from documentation
const TEST_USERS = {
  attendee: {
    email: '<EMAIL>',
    password: 'TestPass123!'
  },
  speaker: {
    email: '<EMAIL>',
    password: 'TestPass123!'
  },
  sponsor: {
    email: '<EMAIL>',
    password: 'TestPass123!'
  },
  e2e: {
    email: '<EMAIL>',
    password: 'TestPass123!'
  }
};

test.describe('Authentication Flow - Production E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set viewport for consistent testing
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should display login page with proper form elements', async ({ page }) => {
    console.log('🧪 Testing login page display...');
    
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');
    
    // Check page title
    await expect(page).toHaveTitle(/IEPA.*Login/);
    
    // Check form elements
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // Check for password show/hide toggle
    const passwordToggle = page.locator('button:has-text("Show"), button:has-text("Hide"), [class*="eye"]');
    const toggleCount = await passwordToggle.count();
    
    if (toggleCount > 0) {
      console.log('✅ Found password show/hide toggle');
    }
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/auth-login-page.png', fullPage: true });
    
    console.log('✅ Login page display verified');
  });

  test('should display signup page with proper form elements', async ({ page }) => {
    console.log('🧪 Testing signup page display...');
    
    await page.goto('/auth/signup');
    await page.waitForLoadState('networkidle');
    
    // Check form elements
    await expect(page.locator('input[name="firstName"], input[placeholder*="first name"]')).toBeVisible();
    await expect(page.locator('input[name="lastName"], input[placeholder*="last name"]')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // Check for terms and conditions
    const termsCheckbox = page.locator('input[type="checkbox"]');
    const termsCount = await termsCheckbox.count();
    
    if (termsCount > 0) {
      console.log('✅ Found terms and conditions checkbox');
    }
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/auth-signup-page.png', fullPage: true });
    
    console.log('✅ Signup page display verified');
  });

  test('should test login with existing test user', async ({ page }) => {
    console.log('🧪 Testing login with test user...');
    
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');
    
    // Fill login form
    await page.fill('input[type="email"]', TEST_USERS.e2e.email);
    await page.fill('input[type="password"]', TEST_USERS.e2e.password);
    
    // Take screenshot before submit
    await page.screenshot({ path: 'test-results/auth-login-filled.png', fullPage: true });
    
    // Submit form
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    // Check for successful login indicators
    const successIndicators = page.locator('text=Welcome, text=Dashboard, text=Logout, text=My Account');
    const successCount = await successIndicators.count();
    
    if (successCount > 0) {
      console.log('✅ Login appears successful');
      await page.screenshot({ path: 'test-results/auth-login-success.png', fullPage: true });
    } else {
      console.log('ℹ️ Login may require additional verification or different flow');
      await page.screenshot({ path: 'test-results/auth-login-attempt.png', fullPage: true });
    }
    
    console.log('✅ Login test completed');
  });

  test('should test authentication state persistence', async ({ page }) => {
    console.log('🧪 Testing authentication state persistence...');
    
    // First, try to login
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');
    
    await page.fill('input[type="email"]', TEST_USERS.e2e.email);
    await page.fill('input[type="password"]', TEST_USERS.e2e.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    // Navigate to different pages and check if auth persists
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check for authenticated user indicators
    const authIndicators = page.locator('text=Welcome, text=My Account, text=Logout');
    const authCount = await authIndicators.count();
    
    if (authCount > 0) {
      console.log('✅ Authentication state persisted on homepage');
    }
    
    // Test page refresh
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const authAfterRefresh = page.locator('text=Welcome, text=My Account, text=Logout');
    const authRefreshCount = await authAfterRefresh.count();
    
    if (authRefreshCount > 0) {
      console.log('✅ Authentication state persisted after page refresh');
    }
    
    await page.screenshot({ path: 'test-results/auth-persistence-test.png', fullPage: true });
    
    console.log('✅ Authentication persistence test completed');
  });

  test('should test form validation on login page', async ({ page }) => {
    console.log('🧪 Testing login form validation...');
    
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');
    
    // Try to submit empty form
    await page.click('button[type="submit"]');
    await page.waitForTimeout(1000);
    
    // Check for validation errors
    const errorElements = page.locator('[class*="error"], [class*="invalid"], .text-red-500, text=required, text=Required');
    const errorCount = await errorElements.count();
    
    if (errorCount > 0) {
      console.log(`✅ Found ${errorCount} validation error(s)`);
      await page.screenshot({ path: 'test-results/auth-login-validation.png', fullPage: true });
    }
    
    // Test invalid email format
    await page.fill('input[type="email"]', 'invalid-email');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(1000);
    
    const emailErrors = page.locator('text=invalid email, text=valid email, text=email format');
    const emailErrorCount = await emailErrors.count();
    
    if (emailErrorCount > 0) {
      console.log('✅ Email format validation working');
    }
    
    console.log('✅ Login form validation tested');
  });

  test('should test password show/hide functionality', async ({ page }) => {
    console.log('🧪 Testing password show/hide functionality...');
    
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');
    
    const passwordInput = page.locator('input[type="password"]');
    const showHideButton = page.locator('button:has-text("Show"), button:has-text("Hide"), [class*="eye"]');
    
    if (await showHideButton.isVisible()) {
      // Fill password field
      await passwordInput.fill('TestPassword123');
      
      // Click show/hide button
      await showHideButton.click();
      await page.waitForTimeout(500);
      
      // Check if input type changed
      const inputType = await passwordInput.getAttribute('type');
      
      if (inputType === 'text') {
        console.log('✅ Password visibility toggle working (showing)');
        
        // Click again to hide
        await showHideButton.click();
        await page.waitForTimeout(500);
        
        const hiddenType = await passwordInput.getAttribute('type');
        if (hiddenType === 'password') {
          console.log('✅ Password visibility toggle working (hiding)');
        }
      }
      
      await page.screenshot({ path: 'test-results/auth-password-toggle.png', fullPage: true });
    }
    
    console.log('✅ Password show/hide functionality tested');
  });
});

test.describe('Navigation and Branding - Production E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should verify IEPA branding throughout application', async ({ page }) => {
    console.log('🧪 Testing IEPA branding consistency...');
    
    const pages = ['/', '/register', '/register/attendee', '/register/speaker', '/register/sponsor'];
    
    for (const pagePath of pages) {
      await page.goto(pagePath);
      await page.waitForLoadState('networkidle');
      
      // Check for IEPA branding
      const iepaElements = page.locator('text=IEPA, [alt*="IEPA"], [src*="iepa"], [class*="iepa"]');
      const iepaCount = await iepaElements.count();
      
      if (iepaCount > 0) {
        console.log(`✅ Found IEPA branding on ${pagePath}`);
      }
      
      // Check for navigation bar
      const navElements = page.locator('nav, [role="navigation"], header');
      const navCount = await navElements.count();
      
      if (navCount > 0) {
        console.log(`✅ Found navigation on ${pagePath}`);
      }
      
      await page.screenshot({ path: `test-results/branding-${pagePath.replace(/\//g, '-') || 'home'}.png`, fullPage: true });
    }
    
    console.log('✅ IEPA branding verification completed');
  });

  test('should test navigation menu functionality', async ({ page }) => {
    console.log('🧪 Testing navigation menu functionality...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Look for navigation links
    const navLinks = page.locator('a[href*="/register"], a:has-text("Register"), a:has-text("Home"), a:has-text("About")');
    const linkCount = await navLinks.count();
    
    console.log(`✅ Found ${linkCount} navigation link(s)`);
    
    // Test navigation to registration page
    const registerLink = page.locator('a[href*="/register"]').first();
    if (await registerLink.isVisible()) {
      await registerLink.click();
      await page.waitForLoadState('networkidle');
      
      // Verify navigation worked
      const currentUrl = page.url();
      if (currentUrl.includes('/register')) {
        console.log('✅ Navigation to registration page successful');
      }
    }
    
    await page.screenshot({ path: 'test-results/navigation-test.png', fullPage: true });
    
    console.log('✅ Navigation menu functionality tested');
  });

  test('should test responsive navigation across breakpoints', async ({ page }) => {
    console.log('🧪 Testing responsive navigation...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    // Look for mobile menu toggle
    const mobileToggle = page.locator('button[aria-label*="menu"], button:has-text("Menu"), [class*="hamburger"]');
    const toggleCount = await mobileToggle.count();
    
    if (toggleCount > 0) {
      console.log('✅ Found mobile menu toggle');
      await mobileToggle.first().click();
      await page.waitForTimeout(500);
    }
    
    await page.screenshot({ path: 'test-results/navigation-mobile.png', fullPage: true });
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/navigation-tablet.png', fullPage: true });
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/navigation-desktop.png', fullPage: true });
    
    console.log('✅ Responsive navigation testing completed');
  });
});

test.describe('Error Handling and Edge Cases', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should handle 404 pages gracefully', async ({ page }) => {
    console.log('🧪 Testing 404 error handling...');
    
    await page.goto('/non-existent-page');
    await page.waitForLoadState('networkidle');
    
    // Check for 404 indicators
    const errorIndicators = page.locator('text=404, text=Not Found, text=Page Not Found');
    const errorCount = await errorIndicators.count();
    
    if (errorCount > 0) {
      console.log('✅ 404 page displayed correctly');
    }
    
    // Check if IEPA branding is still present
    const iepaElements = page.locator('text=IEPA');
    const iepaCount = await iepaElements.count();
    
    if (iepaCount > 0) {
      console.log('✅ IEPA branding maintained on 404 page');
    }
    
    await page.screenshot({ path: 'test-results/error-404-page.png', fullPage: true });
    
    console.log('✅ 404 error handling tested');
  });

  test('should test form error handling', async ({ page }) => {
    console.log('🧪 Testing form error handling...');
    
    await page.goto('/register/attendee');
    await page.waitForLoadState('networkidle');
    
    // Test network error simulation (if possible)
    await page.route('**/api/**', route => route.abort());
    
    // Try to submit form
    const submitButton = page.locator('button[type="submit"], button:has-text("Submit")');
    if (await submitButton.isVisible()) {
      await submitButton.click();
      await page.waitForTimeout(2000);
      
      // Look for error messages
      const errorMessages = page.locator('text=Error, text=Failed, text=Try again, [class*="error"]');
      const errorCount = await errorMessages.count();
      
      if (errorCount > 0) {
        console.log(`✅ Found ${errorCount} error message(s)`);
        await page.screenshot({ path: 'test-results/form-error-handling.png', fullPage: true });
      }
    }
    
    console.log('✅ Form error handling tested');
  });
});
