'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, CardTitle, Button } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  FiSettings,
  FiSave,
  FiRefreshCw,
  FiMail,
  FiDollarSign,
  FiCalendar,
  FiGlobe,
  FiShield,
  FiDatabase,
  FiAlertTriangle,
} from 'react-icons/fi';
import { createSupabaseAdmin } from '@/lib/supabase';
import { showSuccess, showError } from '@/utils/notifications';

interface SystemSettings {
  // Conference Settings
  conference_name: string;
  conference_year: string;
  conference_start_date: string;
  conference_end_date: string;
  conference_location: string;
  conference_description: string;
  
  // Registration Settings
  registration_open: boolean;
  early_bird_deadline: string;
  late_registration_fee: number;
  max_attendees: number;
  allow_profile_edits: boolean;
  
  // Payment Settings
  stripe_public_key: string;
  stripe_webhook_secret: string;
  payment_currency: string;
  tax_rate: number;
  
  // Email Settings
  smtp_host: string;
  smtp_port: number;
  smtp_username: string;
  smtp_password: string;
  from_email: string;
  from_name: string;
  
  // System Settings
  maintenance_mode: boolean;
  debug_mode: boolean;
  backup_frequency: string;
  session_timeout: number;
}

export default function AdminSettingsPage() {
  const [settings, setSettings] = useState<Partial<SystemSettings>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('conference');

  const supabase = createSupabaseAdmin();

  // Load settings
  const loadSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // For now, we'll use default values since the settings table might not exist
      const defaultSettings: Partial<SystemSettings> = {
        conference_name: 'IEPA 2025 Annual Meeting',
        conference_year: '2025',
        conference_start_date: '2025-09-15',
        conference_end_date: '2025-09-17',
        conference_location: 'California',
        conference_description: 'Independent Energy Producers Association Annual Meeting',
        registration_open: true,
        early_bird_deadline: '2025-08-15',
        late_registration_fee: 100,
        max_attendees: 500,
        allow_profile_edits: true,
        payment_currency: 'USD',
        tax_rate: 8.75,
        from_email: '<EMAIL>',
        from_name: 'IEPA Annual Meeting',
        maintenance_mode: false,
        debug_mode: false,
        backup_frequency: 'daily',
        session_timeout: 30,
      };

      setSettings(defaultSettings);
    } catch (err) {
      console.error('Error loading settings:', err);
      setError('Failed to load system settings');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // For now, just show a success message since we don't have a settings table yet
      showSuccess('Settings Saved', 'Settings saved successfully (demo mode)');
      
      // In a real implementation, you would save to a settings table:
      // const { error } = await supabase
      //   .from('iepa_system_settings')
      //   .upsert(settings);
      
    } catch (err) {
      console.error('Error saving settings:', err);
      showError('Save Failed', 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (key: keyof SystemSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading system settings...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <FiSettings className="w-8 h-8 mr-3 text-[var(--iepa-primary-blue)]" />
            System Settings
          </h1>
          <p className="text-gray-600 mt-1">
            Configure system-wide settings and preferences
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button onClick={loadSettings} variant="outline" size="sm" disabled={loading}>
            <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <FiSave className={`w-4 h-4 mr-2 ${saving ? 'animate-spin' : ''}`} />
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>

      {/* Warning Banner */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardBody className="p-4">
          <div className="flex items-center">
            <FiAlertTriangle className="w-5 h-5 text-yellow-600 mr-3" />
            <div>
              <h3 className="font-medium text-yellow-800">Development Mode</h3>
              <p className="text-sm text-yellow-700">
                Settings are currently in demo mode. Changes will not be persisted until the settings database table is implemented.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="conference" className="flex items-center">
            <FiCalendar className="w-4 h-4 mr-2" />
            Conference
          </TabsTrigger>
          <TabsTrigger value="payment" className="flex items-center">
            <FiDollarSign className="w-4 h-4 mr-2" />
            Payment
          </TabsTrigger>
          <TabsTrigger value="email" className="flex items-center">
            <FiMail className="w-4 h-4 mr-2" />
            Email
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center">
            <FiShield className="w-4 h-4 mr-2" />
            Security
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center">
            <FiDatabase className="w-4 h-4 mr-2" />
            System
          </TabsTrigger>
        </TabsList>

        {/* Conference Settings */}
        <TabsContent value="conference" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FiCalendar className="w-5 h-5 mr-2" />
                Conference Information
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Conference Name
                  </label>
                  <Input
                    value={settings.conference_name || ''}
                    onChange={(e) => updateSetting('conference_name', e.target.value)}
                    placeholder="Conference name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Year
                  </label>
                  <Input
                    value={settings.conference_year || ''}
                    onChange={(e) => updateSetting('conference_year', e.target.value)}
                    placeholder="2025"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Date
                  </label>
                  <Input
                    type="date"
                    value={settings.conference_start_date || ''}
                    onChange={(e) => updateSetting('conference_start_date', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date
                  </label>
                  <Input
                    type="date"
                    value={settings.conference_end_date || ''}
                    onChange={(e) => updateSetting('conference_end_date', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <Input
                    value={settings.conference_location || ''}
                    onChange={(e) => updateSetting('conference_location', e.target.value)}
                    placeholder="Conference location"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Attendees
                  </label>
                  <Input
                    type="number"
                    value={settings.max_attendees || ''}
                    onChange={(e) => updateSetting('max_attendees', parseInt(e.target.value))}
                    placeholder="500"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <Textarea
                  value={settings.conference_description || ''}
                  onChange={(e) => updateSetting('conference_description', e.target.value)}
                  placeholder="Conference description"
                  rows={3}
                />
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Registration Settings</CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Registration Open</h3>
                  <p className="text-sm text-gray-500">Allow new registrations</p>
                </div>
                <Switch
                  checked={settings.registration_open || false}
                  onCheckedChange={(checked) => updateSetting('registration_open', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Allow Profile Edits</h3>
                  <p className="text-sm text-gray-500">Allow attendees to edit their registration details</p>
                </div>
                <Switch
                  checked={settings.allow_profile_edits !== false}
                  onCheckedChange={(checked) => updateSetting('allow_profile_edits', checked)}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Early Bird Deadline
                  </label>
                  <Input
                    type="date"
                    value={settings.early_bird_deadline || ''}
                    onChange={(e) => updateSetting('early_bird_deadline', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Late Registration Fee ($)
                  </label>
                  <Input
                    type="number"
                    value={settings.late_registration_fee || ''}
                    onChange={(e) => updateSetting('late_registration_fee', parseFloat(e.target.value))}
                    placeholder="100"
                  />
                </div>
              </div>
            </CardBody>
          </Card>
        </TabsContent>

        {/* Payment Settings */}
        <TabsContent value="payment" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FiDollarSign className="w-5 h-5 mr-2" />
                Payment Configuration
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Currency
                  </label>
                  <Select
                    value={settings.payment_currency || 'USD'}
                    onValueChange={(value) => updateSetting('payment_currency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD - US Dollar</SelectItem>
                      <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                      <SelectItem value="EUR">EUR - Euro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tax Rate (%)
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={settings.tax_rate || ''}
                    onChange={(e) => updateSetting('tax_rate', parseFloat(e.target.value))}
                    placeholder="8.75"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stripe Public Key
                  </label>
                  <Input
                    value={settings.stripe_public_key || ''}
                    onChange={(e) => updateSetting('stripe_public_key', e.target.value)}
                    placeholder="pk_test_..."
                    type="password"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stripe Webhook Secret
                  </label>
                  <Input
                    value={settings.stripe_webhook_secret || ''}
                    onChange={(e) => updateSetting('stripe_webhook_secret', e.target.value)}
                    placeholder="whsec_..."
                    type="password"
                  />
                </div>
              </div>
            </CardBody>
          </Card>
        </TabsContent>

        {/* Email Settings */}
        <TabsContent value="email" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FiMail className="w-5 h-5 mr-2" />
                Email Configuration
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    From Email
                  </label>
                  <Input
                    type="email"
                    value={settings.from_email || ''}
                    onChange={(e) => updateSetting('from_email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    From Name
                  </label>
                  <Input
                    value={settings.from_name || ''}
                    onChange={(e) => updateSetting('from_name', e.target.value)}
                    placeholder="IEPA Conference"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Host
                  </label>
                  <Input
                    value={settings.smtp_host || ''}
                    onChange={(e) => updateSetting('smtp_host', e.target.value)}
                    placeholder="smtp.gmail.com"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Port
                  </label>
                  <Input
                    type="number"
                    value={settings.smtp_port || ''}
                    onChange={(e) => updateSetting('smtp_port', parseInt(e.target.value))}
                    placeholder="587"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Username
                  </label>
                  <Input
                    value={settings.smtp_username || ''}
                    onChange={(e) => updateSetting('smtp_username', e.target.value)}
                    placeholder="username"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Password
                  </label>
                  <Input
                    type="password"
                    value={settings.smtp_password || ''}
                    onChange={(e) => updateSetting('smtp_password', e.target.value)}
                    placeholder="password"
                  />
                </div>
              </div>
            </CardBody>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FiShield className="w-5 h-5 mr-2" />
                Security Configuration
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Session Timeout (minutes)
                </label>
                <Input
                  type="number"
                  value={settings.session_timeout || ''}
                  onChange={(e) => updateSetting('session_timeout', parseInt(e.target.value))}
                  placeholder="30"
                />
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Debug Mode</h3>
                    <p className="text-sm text-gray-500">Enable detailed error logging</p>
                  </div>
                  <Switch
                    checked={settings.debug_mode || false}
                    onCheckedChange={(checked) => updateSetting('debug_mode', checked)}
                  />
                </div>
              </div>
            </CardBody>
          </Card>
        </TabsContent>

        {/* System Settings */}
        <TabsContent value="system" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FiDatabase className="w-5 h-5 mr-2" />
                System Configuration
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Maintenance Mode</h3>
                  <p className="text-sm text-gray-500">Temporarily disable public access</p>
                </div>
                <Switch
                  checked={settings.maintenance_mode || false}
                  onCheckedChange={(checked) => updateSetting('maintenance_mode', checked)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Backup Frequency
                </label>
                <Select
                  value={settings.backup_frequency || 'daily'}
                  onValueChange={(value) => updateSetting('backup_frequency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">Hourly</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardBody>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
