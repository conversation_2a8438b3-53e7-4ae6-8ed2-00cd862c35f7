'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  AuthStatusIndicator,
  UserInfoDisplay,
} from '@/components/auth/AuthStatusIndicator';
import { auth } from '@/lib/auth';
import {
  getDynamicAppUrl,
  getCurrentPort,
  getCurrentHostname,
  getCurrentProtocol,
  isLocalhost,
  logEnvironmentInfo,
} from '@/lib/port-utils';
import { useAuth } from '@/contexts/AuthContext';

interface EnvInfo {
  appUrl: string;
  currentPort: number | null;
  hostname: string;
  protocol: string;
  isLocalhost: boolean;
  envAppUrl: string | undefined;
}

export default function AuthTestPage() {
  const { user, loading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [envInfo, setEnvInfo] = useState<EnvInfo>({
    appUrl: '',
    currentPort: null,
    hostname: '',
    protocol: '',
    isLocalhost: false,
    envAppUrl: undefined,
  });

  useEffect(() => {
    // Log environment info for debugging
    logEnvironmentInfo();

    // Set environment info for display
    setEnvInfo({
      appUrl: getDynamicAppUrl(),
      currentPort: getCurrentPort(),
      hostname: getCurrentHostname(),
      protocol: getCurrentProtocol(),
      isLocalhost: isLocalhost(),
      envAppUrl: process.env.NEXT_PUBLIC_APP_URL,
    });
  }, []);

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const { error } = await auth.signUp(email, password);
      if (error) {
        setMessage(`Sign up error: ${error.message}`);
      } else {
        setMessage('Sign up successful! Check your email for confirmation.');
      }
    } catch (error) {
      setMessage(`Sign up failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const { error } = await auth.signIn(email, password);
      if (error) {
        setMessage(`Sign in error: ${error.message}`);
      } else {
        setMessage('Sign in successful!');
      }
    } catch (error) {
      setMessage(`Sign in failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async () => {
    if (!email) {
      setMessage('Please enter an email address first');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const { error } = await auth.resetPassword(email);
      if (error) {
        setMessage(`Reset password error: ${error.message}`);
      } else {
        setMessage('Password reset email sent! Check your inbox.');
      }
    } catch (error) {
      setMessage(`Reset password failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Auth Configuration Test</h1>
          <p className="text-muted-foreground mt-2">
            Test Supabase authentication on any localhost port
          </p>
        </div>

        {/* Environment Info */}
        <Card>
          <CardHeader>
            <CardTitle>Environment Information</CardTitle>
            <CardDescription>
              Current environment and URL configuration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Dynamic App URL</Label>
                <Badge variant="outline" className="ml-2">
                  {envInfo.appUrl}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium">Current Port</Label>
                <Badge variant="outline" className="ml-2">
                  {envInfo.currentPort || 'default'}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium">Hostname</Label>
                <Badge variant="outline" className="ml-2">
                  {envInfo.hostname}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium">Protocol</Label>
                <Badge variant="outline" className="ml-2">
                  {envInfo.protocol}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium">Is Localhost</Label>
                <Badge
                  variant={envInfo.isLocalhost ? 'default' : 'destructive'}
                  className="ml-2"
                >
                  {envInfo.isLocalhost ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium">Env App URL</Label>
                <Badge variant="outline" className="ml-2">
                  {envInfo.envAppUrl || 'Not set'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Auth Status */}
        <Card>
          <CardHeader>
            <CardTitle>Authentication Status</CardTitle>
            <CardDescription>
              Real-time authentication state with visual indicators
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Status Banner */}
            <AuthStatusIndicator variant="banner" showActions={true} />

            {/* Detailed User Info */}
            {user && (
              <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
                <UserInfoDisplay />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <Label className="text-xs font-medium text-gray-600">
                      User ID
                    </Label>
                    <p className="font-mono text-xs bg-white p-2 rounded border">
                      {user.id}
                    </p>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-gray-600">
                      Created At
                    </Label>
                    <p className="text-xs text-gray-700">
                      {new Date(user.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-gray-600">
                      Last Sign In
                    </Label>
                    <p className="text-xs text-gray-700">
                      {user.last_sign_in_at
                        ? new Date(user.last_sign_in_at).toLocaleDateString()
                        : 'Never'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-gray-600">
                      Auth Provider
                    </Label>
                    <p className="text-xs text-gray-700">
                      {user.app_metadata?.provider || 'email'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Status Indicators */}
            <div className="flex flex-wrap gap-2">
              <AuthStatusIndicator variant="badge" />
              {user && (
                <>
                  <Badge
                    variant={user.email_confirmed_at ? 'default' : 'outline'}
                    className="text-xs"
                  >
                    {user.email_confirmed_at
                      ? 'Email Verified'
                      : 'Email Pending'}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {user.app_metadata?.provider || 'Email'} Auth
                  </Badge>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Auth Form */}
        {!user && (
          <Card>
            <CardHeader>
              <CardTitle>Test Authentication</CardTitle>
              <CardDescription>
                Sign up or sign in to test the auth configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    placeholder="Password (min 6 characters)"
                    required
                  />
                </div>
                <div className="flex gap-2 flex-wrap">
                  <Button
                    type="button"
                    onClick={handleSignUp}
                    disabled={isLoading}
                    variant="default"
                  >
                    Sign Up
                  </Button>
                  <Button
                    type="button"
                    onClick={handleSignIn}
                    disabled={isLoading}
                    variant="outline"
                  >
                    Sign In
                  </Button>
                  <Button
                    type="button"
                    onClick={handleResetPassword}
                    disabled={isLoading}
                    variant="secondary"
                  >
                    Reset Password
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Messages */}
        {message && (
          <Alert>
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p>
              1. Try running the app on different ports (e.g.,{' '}
              <code>npm run dev -- -p 3001</code>)
            </p>
            <p>
              2. The environment info above should automatically update to show
              the new port
            </p>
            <p>3. Test sign up, sign in, and password reset functionality</p>
            <p>
              4. Check that email confirmations and password reset links work
              correctly
            </p>
            <p>5. Verify that redirects work properly regardless of the port</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
