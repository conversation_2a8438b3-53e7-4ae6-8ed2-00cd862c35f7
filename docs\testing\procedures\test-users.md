# IEPA Conference Registration - Test User Accounts

## Overview

This document contains test user credentials for comprehensive testing of each registration type in the IEPA 2025 Conference Registration Application. These accounts are specifically created for development and testing purposes.

## Authentication Configuration

**Updated**: January 5, 2025
- ✅ **Email Confirmation**: **DISABLED** (`mailer_autoconfirm: true`)
- ✅ **Immediate Login**: Users can sign up and login immediately
- ✅ **Development Mode**: Optimized for testing

## Test User Credentials

### 1. Golf Test User (NEW - Recommended for Golf Add-On Testing)

- **Email**: `<EMAIL>`
- **Password**: `GolfTest123!`
- **User Type**: Attendee Registration
- **Purpose**: **SPECIFICALLY FOR GOLF ADD-ON TESTING** - Testing golf tournament registration, club rental, and payment processing
- **Status**: ✅ **VERIFIED WORKING** (Recreated: Jan 5, 2025)
- **User ID**: `4ea60bd3-28ae-4e23-916d-46a6310a9c24`
- **Notes**: Created through proper Supabase Auth API with correct password hashing

### 2. Attendee Test User

- **Email**: `<EMAIL>`
- **Password**: `TestPass123!`
- **User Type**: Attendee Registration
- **Purpose**: Testing attendee registration flow, pricing options, meal selections, and payment processing

### 3. E2E Test User

- **Email**: `<EMAIL>`
- **Password**: `TestPass123!`
- **User Type**: End-to-End Testing
- **Purpose**: Automated testing, Puppeteer testing, comprehensive flow testing
- **Status**: ✅ **VERIFIED WORKING**

### 4. Speaker Test User

- **Email**: `<EMAIL>`
- **Password**: `TestPass123!`
- **User Type**: Speaker Registration
- **Purpose**: Testing speaker proposal submission, presentation uploads, and speaker-specific features

### 5. Sponsor Test User

- **Email**: `<EMAIL>`
- **Password**: `TestPass123!`
- **User Type**: Sponsor Registration
- **Purpose**: Testing sponsor registration flow, sponsorship levels, and sponsor-specific features

### 6. IEPA Staff Accounts (Production-like)

- **Jamie**: `<EMAIL>` (Password: Contact admin)
- **Sara**: `<EMAIL>` (Password: Contact admin)
- **Smutny**: `<EMAIL>` (Password: Contact admin)
- **Purpose**: Production-like testing with real IEPA domain emails

## Authentication Configuration

### Email Verification Status

- **Email Confirmation**: ❌ Disabled (`mailer_autoconfirm: false`)
- **Immediate Login**: ✅ Enabled (users can log in immediately after signup)
- **Development Mode**: ✅ Active (no email verification required)

### Supabase Project Settings

- **Project ID**: `uffhyhpcuedjsisczocy`
- **Region**: `us-west-1`
- **Site URL**: `http://localhost:3001` (configured for local development)
- **URI Allow List**: Includes localhost on all ports for testing

## Usage Instructions

### Testing Registration Flows

1. **Access Registration Pages**:

   - Main registration: `/register`
   - Attendee registration: `/register/attendee`
   - Speaker registration: `/register/speaker`
   - Sponsor registration: `/register/sponsor`

2. **Authentication Testing**:

   - Use any of the test accounts to log in
   - Test authentication state persistence
   - Verify welcome bar displays correctly
   - Test logout functionality

3. **Form Testing**:
   - Test each registration type with appropriate test user
   - Verify form validation and submission
   - Test file uploads (speaker presentations)
   - Test payment flow integration

### Testing Scenarios

#### Attendee Registration Testing

- **User**: `<EMAIL>`
- **Test Cases**:
  - Registration type selection
  - Personal information form
  - Meal preferences and special dietary needs
  - Golf tournament registration
  - Payment processing
  - Confirmation email/PDF generation

#### Speaker Registration Testing

- **User**: `<EMAIL>`
- **Test Cases**:
  - Speaker proposal submission
  - Presentation file uploads (PDF/PPT/PPTX/Word)
  - Speaker bio and photo upload
  - Session preferences
  - A/V requirements
  - Confirmation and approval workflow

#### Sponsor Registration Testing

- **User**: `<EMAIL>`
- **Test Cases**:
  - Sponsorship level selection
  - Company information forms
  - Logo and marketing material uploads
  - Booth preferences
  - Additional services selection
  - Contract generation and approval

## Security Notes

### Development vs Production

- **Current State**: Development mode with simplified authentication
- **Production Requirements**:
  - Re-enable email confirmation
  - Implement proper user verification
  - Add additional security measures
  - Update test credentials or remove test accounts

### Test Account Management

- **Scope**: These accounts are for testing only
- **Data**: All test data should be clearly marked and removable
- **Access**: Test accounts should not have admin privileges
- **Cleanup**: Remove or deactivate test accounts before production deployment

## Verification Checklist

### ✅ Completed Setup

- [x] Test user accounts created in Supabase
- [x] Email verification disabled for development
- [x] Authentication context properly configured
- [x] Test credentials documented
- [x] Usage instructions provided

### ✅ Testing Requirements - COMPLETED

- [x] **Test User Creation**: All three test users successfully created via signup flow
- [x] **Login Verification**: All three test users (attendee, speaker, sponsor) login confirmed working
- [x] **Authentication State**: User authentication persists correctly
- [x] **Registration Access**: All authenticated users can access their registration pages
- [x] **Email Confirmation**: Disabled as expected (users can login immediately)
- [x] **Session Persistence**: Authentication state persists across page refreshes
- [x] **Welcome Bar Display**: Welcome bar displays correctly for authenticated users
- [x] **Registration Flow Access**: Each user type can access their appropriate registration forms
- [ ] Test logout functionality for all accounts
- [ ] Test form submission and validation for each registration type

## Troubleshooting

### Common Issues

1. **Login Failures**:

   - Verify Supabase connection
   - Check environment variables
   - Confirm user exists in auth.users table

2. **Authentication State Issues**:

   - Clear browser localStorage
   - Check auth context initialization
   - Verify session persistence settings

3. **Form Access Issues**:
   - Confirm authentication is properly disabled for testing
   - Check route protection settings
   - Verify user permissions

### Support Resources

- **Supabase Dashboard**: <https://supabase.com/dashboard/project/uffhyhpcuedjsisczocy>
- **Auth Settings**: Authentication > Settings
- **User Management**: Authentication > Users
- **Logs**: Logs > Auth logs for debugging

## Verification Results

### ✅ Test User Creation - COMPLETED

**Date**: January 2025
**Method**: Created via application signup flow at `/auth/signup`

All three test users were successfully created:

1. **Attendee Test User**: `<EMAIL>` ✅ Created
2. **Speaker Test User**: `<EMAIL>` ✅ Created
3. **Sponsor Test User**: `<EMAIL>` ✅ Created

### ✅ Authentication Testing - FULLY VERIFIED

#### All Test Users Login Verification

1. **Attendee User**: `<EMAIL>` ✅ Login successful
2. **Speaker User**: `<EMAIL>` ✅ Login successful
3. **Sponsor User**: `<EMAIL>` ✅ Login successful

#### Registration Flow Access Testing

- **Attendee**: ✅ Can access `/register` and `/register/attendee`
- **Speaker**: ✅ Can access `/register/speaker` with form functionality
- **Sponsor**: ✅ Can access `/register/sponsor` with form functionality

#### Authentication Features Verified

- **Session Persistence**: ✅ Auth state persists across page refreshes
- **Welcome Bar**: ✅ Displays correctly for authenticated users
- **Navigation**: ✅ User authentication status properly reflected
- **Email Confirmation**: ✅ Disabled as expected (immediate login)

### 📋 Next Testing Steps

1. **Logout Functionality Testing**: Test logout functionality for all accounts
2. **Form Submission Testing**: Test complete form submission and validation for each registration type
3. **File Upload Testing**: Test speaker presentation file uploads
4. **Payment Flow Testing**: Test payment integration with registration forms
5. **Admin Dashboard Testing**: Test admin access and dashboard functionality
6. **Production Preparation**: Plan for re-enabling authentication security features

---

**Last Updated**: January 2025
**Status**: ✅ Test users fully created and authentication verified
**Next Action**: Test logout functionality and complete form submission testing

## Summary

### ✅ Successfully Completed

- **3 Test User Accounts Created**: All registration types covered (Attendee, Speaker, Sponsor)
- **Authentication System Verified**: All users can login and access appropriate registration flows
- **Email Verification Disabled**: Confirmed working for development/testing environment
- **Session Management**: Authentication state persists correctly across page refreshes
- **Welcome Bar Integration**: Displays correctly for authenticated users
- **Registration Flow Access**: Each user type can access their specific registration forms

### 🎯 Ready for Use

The test user accounts are now fully functional and ready for comprehensive testing of:

- Complete registration form workflows
- File upload functionality (speaker presentations)
- Payment processing integration
- Admin dashboard access and management
- Form validation and error handling

### 🔐 Security Configuration

- Email confirmation disabled for testing (as intended for small customer base)
- Supabase authentication properly configured for localhost development
- Test accounts clearly marked and documented for easy cleanup before production
