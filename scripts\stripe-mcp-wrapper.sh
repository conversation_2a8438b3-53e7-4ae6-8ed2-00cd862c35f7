#!/bin/bash

# Stripe MCP Wrapper Script
# This script loads environment variables and starts the Stripe MCP server

# Change to the project directory
cd "$(dirname "$0")/.."

# Load environment variables from .env.local
if [ -f ".env.local" ]; then
    export $(grep -v '^#' .env.local | xargs)
fi

# Check if STRIPE_SECRET_KEY is set
if [ -z "$STRIPE_SECRET_KEY" ]; then
    echo "Error: STRIPE_SECRET_KEY not found in .env.local"
    exit 1
fi

# Start the official Stripe MCP server with the API key
npx -y @stripe/mcp --tools=all --api-key="$STRIPE_SECRET_KEY"
