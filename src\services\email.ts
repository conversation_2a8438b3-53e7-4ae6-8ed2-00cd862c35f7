import sgMail from '@sendgrid/mail';
import { createClient } from '@supabase/supabase-js';
import { emailConfigService, type EmailConfig } from './email-config';
import { emailTemplateService } from './email-templates';
import { renderTemplate } from '@/utils/template-renderer';
import {
  CONFERENCE_DATES,
  CONFERENCE_EVENTS,
  formatConferenceDateRange,
  formatEventTime,
} from '@/lib/conference-config';
import type {
  AttendeeRegistrationData,
  SpeakerRegistrationData,
  SponsorRegistrationData,
} from '@/lib/pdf-generation/types';

// Initialize SendGrid
const apiKey = process.env.SENDGRID_API_KEY;
if (!apiKey) {
  console.error(
    '[EMAIL-ERROR] SendGrid API key not found in environment variables'
  );
} else {
  sgMail.setApiKey(apiKey);
}

// Initialize Supabase client for logging (server-side with service role)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

let supabaseAdmin: ReturnType<typeof createClient> | null = null;

if (supabaseUrl && supabaseServiceKey) {
  supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}

// Email configuration interface for internal use
interface InternalEmailConfig {
  fromEmail: string;
  fromName: string;
  supportEmail: string;
  noreplyEmail: string;
  testBcc: string | null;
  isConfigured: boolean;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface EmailOptions {
  to: string | string[];
  from?: {
    email: string;
    name?: string;
  };
  subject: string;
  html: string;
  text?: string;
  attachments?: Array<{
    content: string;
    filename: string;
    type?: string;
    disposition?: string;
  }>;
}

export interface EmailLogEntry {
  recipientEmail: string;
  recipientName?: string;
  senderEmail: string;
  senderName?: string;
  subject: string;
  emailType: string;
  templateUsed?: string;
  contentPreview?: string;
  hasAttachments?: boolean;
  status: 'pending' | 'sent' | 'failed';
  sendgridMessageId?: string;
  errorMessage?: string;
  userId?: string;
  registrationId?: string;
  registrationType?: 'attendee' | 'speaker' | 'sponsor';
  paymentId?: string;
  sent_at?: string;
}

class EmailService {
  private isConfigured(): boolean {
    if (!apiKey) {
      console.error('[EMAIL-ERROR] SendGrid not configured - missing API key');
      return false;
    }
    return true;
  }

  /**
   * Get email configuration from database with fallback to environment variables
   */
  async getConfig(): Promise<InternalEmailConfig> {
    try {
      const dbConfig: EmailConfig = await emailConfigService.getEmailConfig();
      return {
        fromEmail: dbConfig.sender_email,
        fromName: dbConfig.from_name,
        supportEmail: dbConfig.support_email,
        noreplyEmail: dbConfig.noreply_email,
        testBcc:
          process.env.NODE_ENV === 'development'
            ? dbConfig.test_bcc_email || null
            : null,
        isConfigured: true,
      };
    } catch (error) {
      console.warn(
        '[EMAIL-CONFIG] Failed to load database config, using fallback:',
        error
      );
      return {
        fromEmail: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
        fromName: process.env.SENDGRID_FROM_NAME || 'IEPA Conference 2025',
        supportEmail: process.env.SENDGRID_SUPPORT_EMAIL || '<EMAIL>',
        noreplyEmail: process.env.SENDGRID_NOREPLY_EMAIL || '<EMAIL>',
        testBcc:
          process.env.NODE_ENV === 'development' ? '<EMAIL>' : null,
        isConfigured: true,
      };
    }
  }

  /**
   * Extract SendGrid message ID from response
   */
  private extractMessageId(
    response: Record<string, unknown>
  ): string | undefined {
    if (!response) return undefined;

    // Try multiple possible locations for the message ID
    const headers = response.headers as Record<string, unknown> | undefined;
    const messageId =
      headers?.['x-message-id'] ||
      headers?.['X-Message-Id'] ||
      headers?.['x-twilio-email-event-webhook-signature'] ||
      response.messageId ||
      response.message_id;

    if (messageId && typeof messageId === 'string') {
      console.log('[EMAIL-DEBUG] Extracted SendGrid message ID:', messageId);
      return messageId;
    }

    // If no message ID found, log the response structure for debugging
    console.warn('[EMAIL-DEBUG] No message ID found in response:', {
      headers: response.headers,
      statusCode: response.statusCode,
      body: response.body,
    });

    return undefined;
  }

  /**
   * Log email attempt to database
   */
  private async logEmail(logEntry: EmailLogEntry): Promise<void> {
    if (!supabaseAdmin) {
      console.warn(
        '[EMAIL-LOG] Database logging not available - missing Supabase configuration'
      );
      return;
    }

    try {
      const { error } = await supabaseAdmin.from('iepa_email_log').insert([
        {
          recipient_email: logEntry.recipientEmail,
          recipient_name: logEntry.recipientName,
          sender_email: logEntry.senderEmail,
          sender_name: logEntry.senderName,
          subject: logEntry.subject,
          email_type: logEntry.emailType,
          template_used: logEntry.templateUsed,
          content_preview: logEntry.contentPreview,
          has_attachments: logEntry.hasAttachments || false,
          status: logEntry.status,
          sendgrid_message_id: logEntry.sendgridMessageId,
          error_message: logEntry.errorMessage,
          user_id: logEntry.userId,
          registration_id: logEntry.registrationId,
          registration_type: logEntry.registrationType,
          payment_id: logEntry.paymentId,
          sent_at: logEntry.status === 'sent' ? new Date().toISOString() : null,
        },
      ]);

      if (error) {
        console.error('[EMAIL-LOG] Failed to log email to database:', error);
      } else {
        console.log('[EMAIL-LOG] Email logged successfully:', {
          to: logEntry.recipientEmail,
          type: logEntry.emailType,
          status: logEntry.status,
        });
      }
    } catch (error) {
      console.error('[EMAIL-LOG] Error logging email:', error);
    }
  }

  /**
   * Send a single email
   */
  async sendEmail(
    options: EmailOptions,
    metadata?: {
      emailType?: string;
      userId?: string;
      registrationId?: string;
      registrationType?: 'attendee' | 'speaker' | 'sponsor';
      paymentId?: string;
    }
  ): Promise<boolean> {
    if (!this.isConfigured()) {
      return false;
    }

    // Get current email configuration
    const config = await this.getConfig();

    // Prepare log entry
    const recipientEmail = Array.isArray(options.to)
      ? options.to[0]
      : options.to;
    const fromInfo = options.from || {
      email: config.fromEmail,
      name: config.fromName,
    };

    // Validate UUID format for database fields (only log if it's a valid UUID)
    const isValidUUID = (str: string) => {
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      return uuidRegex.test(str);
    };

    const logEntry: EmailLogEntry = {
      recipientEmail,
      senderEmail: fromInfo.email,
      senderName: fromInfo.name,
      subject: options.subject,
      emailType: metadata?.emailType || 'custom',
      contentPreview: options.html.replace(/<[^>]*>/g, '').substring(0, 500),
      hasAttachments: !!(options.attachments && options.attachments.length > 0),
      status: 'pending',
      userId:
        metadata?.userId && isValidUUID(metadata.userId)
          ? metadata.userId
          : undefined,
      registrationId:
        metadata?.registrationId && isValidUUID(metadata.registrationId)
          ? metadata.registrationId
          : undefined,
      registrationType: metadata?.registrationType || undefined,
      paymentId: metadata?.paymentId || undefined,
    };

    // Log the attempt
    await this.logEmail(logEntry);

    try {
      console.log('[EMAIL-DEBUG] Preparing to send email', {
        to: Array.isArray(options.to)
          ? options.to.length + ' recipients'
          : options.to,
        subject: options.subject,
        from: fromInfo.email,
        type: metadata?.emailType || 'custom',
      });

      const msg = {
        to: options.to,
        from: fromInfo,
        subject: options.subject,
        html: options.html,
        text: options.text,
        attachments: options.attachments,
        // TESTING ONLY: Add BCC for email verification during development
        // Only add BCC if it's different from the 'to' address to avoid SendGrid duplicate error
        ...(config.testBcc &&
          config.testBcc !== options.to && { bcc: config.testBcc }),
        // Disable click tracking for authentication emails to prevent URL mangling
        trackingSettings: {
          clickTracking: {
            enable: false,
            enableText: false,
          },
          openTracking: {
            enable: true,
          },
          subscriptionTracking: {
            enable: false,
          },
        },
      };

      const result = await sgMail.send(msg);

      // Extract SendGrid message ID from multiple possible locations
      const messageId = this.extractMessageId(
        result[0] as unknown as Record<string, unknown>
      );

      console.log('[EMAIL-DEBUG] Email sent successfully', {
        messageId,
        statusCode: result[0]?.statusCode,
        type: metadata?.emailType || 'custom',
        recipient: recipientEmail,
        subject: options.subject,
        fullHeaders: result[0]?.headers,
      });

      // Update log with success and SendGrid message ID
      logEntry.status = 'sent';
      logEntry.sendgridMessageId = messageId;
      logEntry.sent_at = new Date().toISOString();
      await this.logEmail(logEntry);

      return true;
    } catch (error: unknown) {
      console.error('[EMAIL-ERROR] Failed to send email:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        code: (error as Record<string, unknown>)?.code,
        response: (error as Record<string, unknown>)?.response,
        responseText: (error as Record<string, unknown>)?.responseText,
        responseData: JSON.stringify(
          (error as Record<string, unknown>)?.response,
          null,
          2
        ),
        fullError: JSON.stringify(error, null, 2),
        to: options.to,
        subject: options.subject,
        type: metadata?.emailType || 'custom',
      });

      // Update log with failure
      logEntry.status = 'failed';
      logEntry.errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      await this.logEmail(logEntry);

      return false;
    }
  }

  /**
   * Send multiple emails (bulk)
   */
  async sendBulkEmails(emails: EmailOptions[]): Promise<boolean[]> {
    if (!this.isConfigured()) {
      return emails.map(() => false);
    }

    console.log('[EMAIL-DEBUG] Sending bulk emails', { count: emails.length });

    const results = await Promise.allSettled(
      emails.map(email => this.sendEmail(email))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        console.error('[EMAIL-ERROR] Bulk email failed:', {
          index,
          error: result.reason,
          to: emails[index].to,
        });
        return false;
      }
    });
  }

  /**
   * Send email using database template
   */
  async sendTemplatedEmail(
    templateKey: string,
    recipientEmail: string,
    templateData: Record<string, unknown>,
    metadata?: {
      emailType?: string;
      userId?: string;
      registrationId?: string;
      registrationType?: 'attendee' | 'speaker' | 'sponsor';
      paymentId?: string;
    }
  ): Promise<boolean> {
    try {
      // Get template from database
      const template = await emailTemplateService.getTemplateByKey(templateKey);

      if (!template) {
        console.error(`[EMAIL-TEMPLATE] Template not found: ${templateKey}`);
        return false;
      }

      // Render template with data
      const subject = renderTemplate(template.subject_template, templateData);
      const html = renderTemplate(template.html_template, templateData);
      const text = template.text_template
        ? renderTemplate(template.text_template, templateData)
        : undefined;

      console.log(
        `[EMAIL-TEMPLATE] Sending templated email: ${templateKey} to ${recipientEmail}`
      );

      return this.sendEmail(
        {
          to: recipientEmail,
          subject,
          html,
          text,
        },
        {
          ...metadata,
          emailType: metadata?.emailType || templateKey,
        }
      );
    } catch (error) {
      console.error(
        `[EMAIL-TEMPLATE] Error sending templated email: ${templateKey}`,
        error
      );
      return false;
    }
  }

  /**
   * Send registration confirmation email with role-specific content and PDF invoice attachment
   */
  async sendRegistrationConfirmation(
    email: string,
    name: string,
    registrationDetails: {
      type: 'attendee' | 'speaker' | 'sponsor';
      confirmationNumber?: string;
      eventDate?: string;
      location?: string;
      userId?: string;
      speakerPricingType?: string;
      sponsorshipLevel?: string;
      registrationId?: string; // Added for PDF generation
    }
  ): Promise<boolean> {
    // Try to use database template first
    try {
      const template = await emailTemplateService.getTemplateByKey(
        'registration_confirmation'
      );

      if (template) {
        console.log(
          '[EMAIL-TEMPLATE] Using database template for registration confirmation'
        );

        // Generate role-specific content
        const roleSpecificContent = this.generateRoleSpecificContent(
          registrationDetails.type,
          {
            speakerPricingType: registrationDetails.speakerPricingType,
            sponsorshipLevel: registrationDetails.sponsorshipLevel,
          }
        );

        // Generate PDF receipt if registration ID is provided
        let receiptAttachment = null;
        if (registrationDetails.registrationId) {
          try {
            console.log(
              '[EMAIL-RECEIPT] Generating PDF receipt for registration:',
              registrationDetails.registrationId
            );
            receiptAttachment = await this.generateReceiptAttachment(
              registrationDetails.type,
              registrationDetails.registrationId
            );
            console.log('[EMAIL-RECEIPT] PDF receipt generated successfully');
          } catch (error) {
            console.error(
              '[EMAIL-RECEIPT] Failed to generate PDF receipt:',
              error
            );
            // Continue without attachment - don't fail the email
          }
        }

        const templateData = {
          name,
          registrationType:
            registrationDetails.type.charAt(0).toUpperCase() +
            registrationDetails.type.slice(1),
          confirmationNumber: registrationDetails.confirmationNumber,
          roleSpecificContent,
        };

        const subject = renderTemplate(template.subject_template, templateData);
        const html = renderTemplate(template.html_template, templateData);
        const text = template.text_template
          ? renderTemplate(template.text_template, templateData)
          : undefined;

        return this.sendEmail(
          {
            to: email,
            subject,
            html,
            text,
            attachments: receiptAttachment ? [receiptAttachment] : undefined,
          },
          {
            emailType: 'registration_confirmation',
            userId: registrationDetails.userId,
            registrationId: registrationDetails.confirmationNumber,
            registrationType: registrationDetails.type,
          }
        );
      }
    } catch (error) {
      console.warn(
        '[EMAIL-TEMPLATE] Failed to use database template, falling back to hardcoded template:',
        error
      );
    }

    // Fallback to hardcoded template
    console.log(
      '[EMAIL-TEMPLATE] Using hardcoded template for registration confirmation'
    );

    // Get current email configuration
    const config = await this.getConfig();
    const subject = `Welcome to IEPA's 2025 Annual Meeting - Registration Confirmed!`;

    // Get conference documents for email links
    const conferenceDocuments = await this.getConferenceDocuments();

    // Generate PDF receipt if registration ID is provided
    let receiptAttachment = null;
    if (registrationDetails.registrationId) {
      try {
        console.log(
          '[EMAIL-RECEIPT] Generating PDF receipt for registration:',
          registrationDetails.registrationId
        );
        receiptAttachment = await this.generateReceiptAttachment(
          registrationDetails.type,
          registrationDetails.registrationId
        );
        console.log('[EMAIL-RECEIPT] PDF receipt generated successfully');
      } catch (error) {
        console.error('[EMAIL-RECEIPT] Failed to generate PDF receipt:', error);
        // Continue without attachment - don't fail the email
      }
    }

    // Generate role-specific content
    const roleSpecificContent = this.generateRoleSpecificContent(
      registrationDetails.type,
      {
        speakerPricingType: registrationDetails.speakerPricingType,
        sponsorshipLevel: registrationDetails.sponsorshipLevel,
      }
    );

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
        <!-- IEPA Header -->
        <div style="background: #3A6CA5; padding: 20px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Independent Energy Producers Association</h1>
          <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">2025 Annual Meeting Registration Confirmed</p>
        </div>

        <hr style="color: #3A6CA5; border: 2px solid #3A6CA5; margin: 0;" />

        <div style="padding: 30px;">
          <h2 style="color: #3A6CA5; margin-top: 0;">Dear ${name},</h2>

          <p>Thank you for registering for IEPA's 2025 Annual Meeting as a <strong>${registrationDetails.type.charAt(0).toUpperCase() + registrationDetails.type.slice(1)}</strong>. We are putting together another outstanding program and look forward to your participation.</p>

          ${
            registrationDetails.confirmationNumber
              ? `
            <div style="background: #f8f9fa; border-left: 4px solid #3A6CA5; padding: 15px; margin: 20px 0;">
              <p style="margin: 0;"><strong>Confirmation Number:</strong> ${registrationDetails.confirmationNumber}</p>
            </div>
          `
              : ''
          }

          ${roleSpecificContent}

          <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #3A6CA5; margin-top: 0;">Important Conference Information</h3>
            <p><strong>Event Dates:</strong> Monday, September 15 through Wednesday, September 17, 2025</p>
            <p><strong>Location:</strong> Stanford Sierra Conference Center at Fallen Leaf Lake</p>
            <p><strong>Physical Address:</strong> 130 Fallen Leaf Road, Fallen Leaf, CA 96151</p>
            <p><strong>Phone:</strong> (*************</p>
            <p><strong>Website:</strong> <a href="http://stanfordsierra.com/" style="color: #3A6CA5;">stanfordsierra.com</a></p>
          </div>

          ${
            receiptAttachment
              ? `
            <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #155724; margin-top: 0;">📄 Receipt Attached</h3>
              <p style="color: #155724; margin: 0;">Your registration receipt is attached to this email as a PDF document. Please save this for your records and accounting purposes.</p>
            </div>
          `
              : ''
          }

          <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0;"><strong>📋 Next Steps:</strong> You will receive detailed conference information, including directions, maps, and agenda updates as we get closer to the event date.</p>
          </div>

          ${
            conferenceDocuments.length > 0
              ? `
          <div style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0;"><strong>📎 Conference Documents:</strong> Important documents are now available for download:</p>
            <ul style="margin: 10px 0;">
              ${conferenceDocuments
                .map(
                  doc => `
                <li><a href="${doc.file_url}" target="_blank" style="color: #3a6ca5; text-decoration: none;">${doc.name}</a>${doc.description ? ` - ${doc.description}` : ''}</li>
              `
                )
                .join('')}
            </ul>
            <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
              <em>All documents are also available on our website at <a href="https://reg.iepa.com/about#resources" target="_blank" style="color: #3a6ca5;">reg.iepa.com/about#resources</a></em>
            </p>
          </div>
          `
              : ''
          }

          <p>If you have any questions, please don't hesitate to contact us at <a href="mailto:${config.supportEmail}" style="color: #3A6CA5;">${config.supportEmail}</a> or call ************.</p>

          <p style="margin-top: 30px;">Best regards,<br>
          <strong>Jamie Parker</strong><br>
          Administrator/Annual Meeting Coordinator<br>
          Independent Energy Producers Association</p>
        </div>

        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
          <p style="margin: 0; color: #6c757d; font-size: 14px;">
            Independent Energy Producers Association<br>
            California's oldest nonprofit trade association representing independent energy facilities
          </p>
        </div>
      </div>
    `;

    const text = `
      IEPA 2025 Annual Meeting - Registration Confirmed!

      Dear ${name},

      Thank you for registering for IEPA's 2025 Annual Meeting as a ${registrationDetails.type}.

      ${registrationDetails.confirmationNumber ? `Confirmation Number: ${registrationDetails.confirmationNumber}` : ''}

      Event Dates: Monday, September 15 through Wednesday, September 17, 2025
      Location: Stanford Sierra Conference Center at Fallen Leaf Lake
      Address: 130 Fallen Leaf Road, Fallen Leaf, CA 96151
      Phone: (*************

      You will receive detailed conference information as we get closer to the event date.

      If you have any questions, please contact us at ${config.supportEmail} or call ************.

      Best regards,
      Jamie Parker
      Administrator/Annual Meeting Coordinator
      Independent Energy Producers Association
    `;

    return this.sendEmail(
      {
        to: email,
        subject,
        html,
        text,
        attachments: receiptAttachment ? [receiptAttachment] : undefined,
      },
      {
        emailType: 'registration_confirmation',
        userId: registrationDetails.userId,
        registrationId: registrationDetails.confirmationNumber,
        registrationType: registrationDetails.type,
      }
    );
  }

  /**
   * Send payment confirmation email with enhanced styling and details
   */
  async sendPaymentConfirmation(
    email: string,
    name: string,
    paymentDetails: {
      amount: number;
      paymentId: string;
      registrationType: string;
      receiptUrl?: string;
      userId?: string;
      registrationId?: string;
      isGolfAddon?: boolean;
      golfDetails?: {
        clubRental?: boolean;
        clubType?: string;
      };
    }
  ): Promise<boolean> {
    const isGolf = paymentDetails.isGolfAddon;
    const subject = isGolf
      ? `Golf Tournament Registration Confirmed - IEPA 2025`
      : `Payment Confirmation - IEPA 2025 Annual Meeting`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
        <!-- IEPA Header -->
        <div style="background: ${isGolf ? '#059669' : '#3A6CA5'}; padding: 20px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 24px;">
            ${isGolf ? '⛳ Golf Tournament Registration' : '💳 Payment Confirmation'}
          </h1>
          <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">IEPA 2025 Annual Meeting</p>
        </div>

        <hr style="color: ${isGolf ? '#059669' : '#3A6CA5'}; border: 2px solid ${isGolf ? '#059669' : '#3A6CA5'}; margin: 0;" />

        <div style="padding: 30px;">
          <h2 style="color: ${isGolf ? '#059669' : '#3A6CA5'}; margin-top: 0;">
            ${isGolf ? 'Golf Registration Confirmed!' : 'Payment Successfully Processed!'}
          </h2>

          <p>Dear ${name},</p>

          ${
            isGolf
              ? `
            <p>Your registration for the IEPA 2025 Golf Tournament has been confirmed! We're excited to have you join us on the course.</p>

            <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #155724; margin-top: 0;">⛳ Golf Tournament Details</h3>
              <p style="color: #155724; margin: 5px 0;"><strong>Date:</strong> Monday, September 15, 2025</p>
              <p style="color: #155724; margin: 5px 0;"><strong>Tee-off:</strong> Beginning at 11:00 AM</p>
              <p style="color: #155724; margin: 5px 0;"><strong>Location:</strong> South Lake Tahoe Golf Course</p>
              <p style="color: #155724; margin: 5px 0;"><strong>Address:</strong> 2500 Emerald Bay Road, South Lake Tahoe</p>
              <p style="color: #155724; margin: 5px 0;"><strong>Phone:</strong> (*************</p>
              ${
                paymentDetails.golfDetails?.clubRental
                  ? `
                <div style="background: #ffffff; padding: 10px; border-radius: 5px; margin: 10px 0;">
                  <p style="margin: 0; color: #155724;"><strong>🏌️ Club Rental Confirmed:</strong> ${paymentDetails.golfDetails.clubType || 'Callaway Rogue Golf Clubs'} (includes 6 Callaway balls)</p>
                </div>
              `
                  : ''
              }
            </div>
          `
              : `
            <p>Your payment for the IEPA 2025 Annual Meeting has been successfully processed.</p>
          `
          }

          <div style="background: #f8f9fa; border-left: 4px solid ${isGolf ? '#059669' : '#3A6CA5'}; padding: 20px; margin: 25px 0;">
            <h3 style="color: ${isGolf ? '#059669' : '#3A6CA5'}; margin-top: 0;">💰 Payment Details</h3>
            <p style="margin: 5px 0;"><strong>Amount Paid:</strong> $${paymentDetails.amount.toFixed(2)}</p>
            <p style="margin: 5px 0;"><strong>Payment ID:</strong> ${paymentDetails.paymentId}</p>
            <p style="margin: 5px 0;"><strong>Registration Type:</strong> ${paymentDetails.registrationType}</p>
            ${paymentDetails.registrationId ? `<p style="margin: 5px 0;"><strong>Registration ID:</strong> ${paymentDetails.registrationId}</p>` : ''}
          </div>

          ${
            paymentDetails.receiptUrl
              ? `
            <div style="text-align: center; margin: 25px 0;">
              <a href="${paymentDetails.receiptUrl}"
                 style="background: ${isGolf ? '#059669' : '#3A6CA5'}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                📄 Download Receipt
              </a>
            </div>
          `
              : ''
          }

          ${
            isGolf
              ? `
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <p style="margin: 0;"><strong>📋 Important Golf Information:</strong></p>
              <ul style="margin: 10px 0;">
                <li>You'll receive an email with foursomes and game rules a few days before the tournament</li>
                <li>If flying into Reno-Tahoe International Airport, allow 1 hour 20 minutes travel time</li>
                <li>Check your flight times to ensure you arrive in time for your tee time</li>
              </ul>
            </div>
          `
              : `
            <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #3A6CA5; margin-top: 0;">📅 Conference Information</h3>
              <p><strong>Event Dates:</strong> Monday, September 15 through Wednesday, September 17, 2025</p>
              <p><strong>Location:</strong> Stanford Sierra Conference Center at Fallen Leaf Lake</p>
              <p>You will receive detailed conference information as we get closer to the event date.</p>
            </div>
          `
          }

          <p>Thank you for your registration! We look forward to seeing you at the ${isGolf ? 'golf tournament and ' : ''}conference.</p>

          <p style="margin-top: 30px;">Best regards,<br>
          <strong>Jamie Parker</strong><br>
          Administrator/Annual Meeting Coordinator<br>
          Independent Energy Producers Association</p>
        </div>

        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
          <p style="margin: 0; color: #6c757d; font-size: 14px;">
            Independent Energy Producers Association<br>
            California's oldest nonprofit trade association representing independent energy facilities
          </p>
        </div>
      </div>
    `;

    const emailType = isGolf ? 'golf_addon' : 'payment_confirmation';

    return this.sendEmail(
      {
        to: email,
        subject,
        html,
      },
      {
        emailType,
        userId: paymentDetails.userId,
        registrationId: paymentDetails.registrationId,
        registrationType: paymentDetails.registrationType.toLowerCase() as
          | 'attendee'
          | 'speaker'
          | 'sponsor',
        paymentId: paymentDetails.paymentId,
      }
    );
  }

  /**
   * Generate role-specific content for registration emails
   */
  private generateRoleSpecificContent(
    type: 'attendee' | 'speaker' | 'sponsor',
    details: {
      speakerPricingType?: string;
      sponsorshipLevel?: string;
    }
  ): string {
    switch (type) {
      case 'speaker':
        return `
          <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #155724; margin-top: 0;">🎤 Speaker Benefits & Information</h3>
            ${
              details.speakerPricingType === 'comped-speaker'
                ? `
              <div style="background: #ffffff; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <p style="margin: 0; color: #155724;"><strong>✅ Complimentary Speaker Package ($0):</strong></p>
                <ul style="margin: 10px 0; color: #155724;">
                  <li>One night lodging included</li>
                  <li>Three meals included</li>
                  <li>All conference sessions and networking events</li>
                </ul>
              </div>
            `
                : `
              <div style="background: #ffffff; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <p style="margin: 0; color: #155724;"><strong>✅ Full Meeting Speaker Package ($1,500):</strong></p>
                <ul style="margin: 10px 0; color: #155724;">
                  <li>Two nights lodging included</li>
                  <li>All meals and events included</li>
                  <li>All conference sessions and networking events</li>
                </ul>
              </div>
            `
            }
            <p style="margin: 10px 0 0 0; color: #155724;"><strong>📋 Next Steps for Speakers:</strong></p>
            <ul style="margin: 5px 0; color: #155724;">
              <li>Upload your presentation materials (PDF, PPT, PPTX, or Word documents)</li>
              <li>Review speaker guidelines and technical requirements</li>
              <li>Submit any special equipment needs or requests</li>
              <li>Provide your professional headshot for conference materials</li>
            </ul>
          </div>
        `;

      case 'sponsor':
        const sponsorBenefits = this.getSponsorBenefits(
          details.sponsorshipLevel
        );
        return `
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #856404; margin-top: 0;">🏆 Sponsor Benefits & Information</h3>
            <div style="background: #ffffff; padding: 15px; border-radius: 5px; margin: 10px 0;">
              <p style="margin: 0; color: #856404;"><strong>✅ ${sponsorBenefits.levelName} Sponsorship Benefits:</strong></p>
              <ul style="margin: 10px 0; color: #856404;">
                ${sponsorBenefits.benefits.map(benefit => `<li>${benefit}</li>`).join('')}
              </ul>
            </div>
            <p style="margin: 10px 0 0 0; color: #856404;"><strong>📋 Next Steps for Sponsors:</strong></p>
            <ul style="margin: 5px 0; color: #856404;">
              <li>Submit your company logo and marketing materials</li>
              <li>Coordinate booth setup and display requirements</li>
              <li>Provide attendee information for included registrations</li>
              <li>Review sponsor guidelines and networking opportunities</li>
            </ul>
          </div>
        `;

      case 'attendee':
      default:
        return `
          <div style="background: #e8f4f8; border: 1px solid #bee5eb; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #0c5460; margin-top: 0;">🎯 Attendee Information</h3>
            <p style="color: #0c5460; margin: 0;">As a conference attendee, you'll have access to:</p>
            <ul style="margin: 10px 0; color: #0c5460;">
              <li>All conference sessions and presentations</li>
              <li>Networking opportunities with industry leaders</li>
              <li>Access to conference materials and resources</li>
              <li>Meals and refreshments (included in registration)</li>
              <li>Optional golf tournament participation</li>
            </ul>
          </div>
        `;
    }
  }

  /**
   * Get sponsor benefits based on sponsorship level
   */
  private getSponsorBenefits(sponsorshipLevel?: string): {
    levelName: string;
    benefits: string[];
  } {
    switch (sponsorshipLevel) {
      case 'bronze-sponsor':
        return {
          levelName: 'Bronze Level ($5,150)',
          benefits: [
            '1 complimentary registration',
            'Two nights lodging',
            'All meals and hosted receptions',
            'Company logo in conference materials',
            'Networking opportunities',
          ],
        };
      case 'silver-sponsor':
        return {
          levelName: 'Silver Level ($10,300)',
          benefits: [
            '2 complimentary registrations',
            'Two nights lodging for attendees',
            'All meals and hosted receptions',
            'Enhanced logo placement',
            'Priority networking opportunities',
          ],
        };
      case 'gold-sponsor':
        return {
          levelName: 'Gold Level ($15,450)',
          benefits: [
            '3 complimentary registrations',
            'Two nights lodging for attendees',
            'All meals and hosted receptions',
            'Premium logo placement',
            'Dedicated networking time',
          ],
        };
      case 'platinum-sponsor':
        return {
          levelName: 'Platinum Level ($20,600)',
          benefits: [
            '4 complimentary registrations',
            'Two nights lodging for attendees',
            'All meals and hosted receptions',
            'Top-tier logo placement',
            'Speaking opportunity consideration',
          ],
        };
      case 'diamond-sponsor':
        return {
          levelName: 'Diamond Level ($25,750)',
          benefits: [
            '5 complimentary registrations',
            'Two nights lodging for attendees',
            'All meals and hosted receptions',
            'Exclusive logo placement',
            'Guaranteed speaking opportunity',
          ],
        };
      default:
        return {
          levelName: 'Sponsor',
          benefits: [
            'Complimentary registrations included',
            'Conference materials and networking',
            'Logo placement in conference materials',
          ],
        };
    }
  }

  /**
   * Get conference documents for email inclusion
   */
  private async getConferenceDocuments(): Promise<
    Array<{
      name: string;
      file_url: string;
      description?: string;
    }>
  > {
    try {
      const { createSupabaseAdmin } = await import('@/lib/supabase');
      const supabaseAdmin = createSupabaseAdmin();

      const { data, error } = await supabaseAdmin
        .from('iepa_conference_documents')
        .select('name, file_url, description')
        .eq('is_active', true)
        .eq('is_public', true)
        .order('display_order', { ascending: true })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('[EMAIL] Error fetching conference documents:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('[EMAIL] Error in getConferenceDocuments:', error);
      return [];
    }
  }

  /**
   * Send sponsor confirmation email using the sponsor_confirmation template
   */
  async sendSponsorConfirmationEmail(
    email: string,
    name: string,
    sponsorData: {
      organizationName: string;
      sponsorshipLevel: string;
      confirmationNumber: string;
      registrationDate?: string;
      paymentAmount?: string;
      userId?: string;
    }
  ): Promise<boolean> {
    try {
      // Get the sponsor confirmation template
      const template = await emailTemplateService.getTemplateByKey(
        'sponsor_confirmation'
      );

      if (!template) {
        console.error(
          '[EMAIL-TEMPLATE] Sponsor confirmation template not found'
        );
        return false;
      }

      // Prepare template data
      const templateData = {
        name,
        organizationName: sponsorData.organizationName,
        sponsorshipLevel: sponsorData.sponsorshipLevel,
        confirmationNumber: sponsorData.confirmationNumber,
        registrationDate:
          sponsorData.registrationDate || new Date().toLocaleDateString(),
        paymentAmount: sponsorData.paymentAmount,
        contactEmail: '<EMAIL>',
        eventDates: formatConferenceDateRange(
          CONFERENCE_DATES.startDate,
          CONFERENCE_DATES.endDate
        ),
        venueInfo:
          'Stanford Sierra Conference Center, 130 Fallen Leaf Rd, South Lake Tahoe, CA 96150',
        // Add sponsorship benefits if needed
        sponsorshipBenefits: this.getSponsorshipBenefits(
          sponsorData.sponsorshipLevel
        ),
        sponsorshipBenefitsText: this.getSponsorshipBenefitsText(
          sponsorData.sponsorshipLevel
        ),
      };

      return this.sendTemplatedEmail(
        'sponsor_confirmation',
        email,
        templateData,
        {
          emailType: 'sponsor_confirmation',
          userId: sponsorData.userId,
          registrationId: sponsorData.confirmationNumber,
          registrationType: 'sponsor',
        }
      );
    } catch (error) {
      console.error(
        '[EMAIL-TEMPLATE] Error sending sponsor confirmation email:',
        error
      );
      return false;
    }
  }

  /**
   * Get sponsorship benefits HTML for a given level
   */
  private getSponsorshipBenefits(level: string): string {
    // This could be enhanced to pull from a configuration or database
    const benefits = {
      platinum:
        '<ul><li>Premium booth location</li><li>Logo on all materials</li><li>Speaking opportunity</li><li>VIP reception access</li><li>Attendee list access</li></ul>',
      gold: '<ul><li>Premium booth location</li><li>Logo on conference materials</li><li>Speaking opportunity</li><li>VIP reception access</li></ul>',
      silver:
        '<ul><li>Standard booth location</li><li>Logo on select materials</li><li>Networking reception access</li></ul>',
      bronze:
        '<ul><li>Standard booth location</li><li>Logo recognition</li><li>Conference attendance</li></ul>',
    };
    return (
      benefits[level.toLowerCase()] ||
      '<ul><li>Conference sponsorship benefits</li></ul>'
    );
  }

  /**
   * Get sponsorship benefits text for a given level
   */
  private getSponsorshipBenefitsText(level: string): string {
    const benefits = {
      platinum:
        '- Premium booth location\n- Logo on all materials\n- Speaking opportunity\n- VIP reception access\n- Attendee list access',
      gold: '- Premium booth location\n- Logo on conference materials\n- Speaking opportunity\n- VIP reception access',
      silver:
        '- Standard booth location\n- Logo on select materials\n- Networking reception access',
      bronze:
        '- Standard booth location\n- Logo recognition\n- Conference attendance',
    };
    return benefits[level.toLowerCase()] || '- Conference sponsorship benefits';
  }

  /**
   * Send comprehensive welcome email with annual meeting details and PDF invoice attachment
   */
  async sendWelcomeEmail(
    email: string,
    name: string,
    registrationDetails: {
      type: 'attendee' | 'speaker' | 'sponsor';
      confirmationNumber?: string;
      userId?: string;
      hasLodging?: boolean;
      hasGolf?: boolean;
    }
  ): Promise<boolean> {
    // Get dynamic configuration values
    const conferenceDateRange = formatConferenceDateRange(
      CONFERENCE_DATES.startDate,
      CONFERENCE_DATES.endDate
    );
    const golfEvent = CONFERENCE_EVENTS.find(
      event => event.id === 'golf-tournament'
    );
    const golfDate = CONFERENCE_DATES.golfTournament.displayDate;
    const golfTime = golfEvent
      ? formatEventTime(golfEvent.startTime)
      : '11:00 AM';

    // Get annual meeting documents for email links
    const conferenceDocuments = await this.getConferenceDocuments();

    // Generate PDF receipt if registration ID is provided
    let receiptAttachment = null;
    if (registrationDetails.confirmationNumber) {
      try {
        console.log(
          '[EMAIL-RECEIPT] Generating PDF receipt for welcome email:',
          registrationDetails.confirmationNumber
        );
        receiptAttachment = await this.generateReceiptAttachment(
          registrationDetails.type,
          registrationDetails.confirmationNumber
        );
        console.log(
          '[EMAIL-RECEIPT] PDF receipt generated successfully for welcome email'
        );
      } catch (error) {
        console.error(
          '[EMAIL-RECEIPT] Failed to generate PDF receipt for welcome email:',
          error
        );
        // Continue without attachment - don't fail the email
      }
    }

    const subject = `Welcome to IEPA's 2025 Annual Meeting - Important Annual Meeting Information`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
        <!-- IEPA Header -->
        <div style="background: #3A6CA5; padding: 20px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Welcome to IEPA's 2025 Annual Meeting!</h1>
        </div>

        <hr style="color: #3A6CA5; border: 2px solid #3A6CA5; margin: 0;" />

        <div style="padding: 30px;">
          <p><strong>Dear ${name},</strong></p>

          <p>Thank you for registering for IEPA's 2025 Annual Meeting to be held <strong>${conferenceDateRange}</strong>. We are putting together another outstanding program and look forward to your participation.</p>

          <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0;"><strong>📋 In preparation of your stay, please review the information below and the important documents that include directions and maps to Stanford Sierra Conference Center at Fallen Leaf Lake.</strong></p>
          </div>

          <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #3A6CA5; margin-top: 0;">📍 Stanford Sierra Conference Center</h3>
            <p><strong>Physical address:</strong> 130 Fallen Leaf Road, Fallen Leaf, CA 96151</p>
            <p><strong>Mailing address:</strong> PO Box 10618, South Lake Tahoe, CA 96158-1959</p>
            <p><strong>Phone number:</strong> (*************</p>
            <p><strong>Fax number:</strong> (*************</p>
            <p><strong>Website:</strong> <a href="http://stanfordsierra.com/" style="color: #3A6CA5;">stanfordsierra.com</a></p>
            <p><strong>Closest Airport:</strong> Reno-Tahoe International Airport</p>
          </div>

          <h3 style="color: #3A6CA5;">🏞️ About Stanford Sierra Conference Center</h3>
          <p>Stanford Sierra Conference Center (SSCC) is located on twenty acres of lakefront property on the south shore of beautiful Fallen Leaf Lake. It was established in 1907 as a summer family camp by William Wrightman Price, a Stanford professor and nature enthusiast. The Old Lodge, built in 1932, with hardwood floors, vaulted ceiling and stone fireplace is now a favorite spot for visiting groups to hold social hours.</p>

          <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #155724; margin-top: 0;">👔 Dress Code</h3>
            <p style="color: #155724; margin: 0;"><strong><em>Please dress casually</em></strong>, whether you are a speaker or a participant. This location is rustic with dusty, wooded paths and comfortable clothing and sturdy shoes are a must. Don't forget to bring a light jacket in case the weather is cool. And a flashlight is necessary for walking to your cabin after dark as there are no lights along the paths.</p>
          </div>

          ${
            registrationDetails.hasLodging
              ? `
            <div style="background: #f8f9fa; border-left: 4px solid #3A6CA5; padding: 20px; margin: 25px 0;">
              <h3 style="color: #3A6CA5; margin-top: 0;">🏨 On-Site Lodging Information</h3>
              <p><strong>Important:</strong> Stanford Sierra Conference Center is NOT a hotel. Lodging is available for the nights of <strong>${CONFERENCE_DATES.startDate.dayOfWeek}, ${CONFERENCE_DATES.startDate.displayDate} and ${CONFERENCE_DATES.endDate.dayOfWeek === 'Wednesday' ? 'Tuesday' : CONFERENCE_DATES.endDate.dayOfWeek}, ${new Date(new Date(CONFERENCE_DATES.endDate.date).getTime() - 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })} ONLY</strong>.</p>
              <p><strong>Check-in:</strong> Begins at 3 p.m. on ${CONFERENCE_DATES.startDate.dayOfWeek}, ${CONFERENCE_DATES.startDate.displayDate}</p>
              <p><strong>Check-out:</strong> No later than 10 a.m. on departure day</p>
              <p><strong>Departure:</strong> Our group must depart by 1:00 p.m. on ${CONFERENCE_DATES.endDate.dayOfWeek}, ${CONFERENCE_DATES.endDate.displayDate}</p>
              <p>Your cabin assignment and key will be provided upon check-in. If you have a favorite cabin from previous years, please email your request to <a href="mailto:<EMAIL>" style="color: #3A6CA5;"><EMAIL></a>.</p>
            </div>
          `
              : ''
          }

          ${
            registrationDetails.hasGolf
              ? `
            <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #155724; margin-top: 0;">⛳ Golf Tournament</h3>
              <p style="color: #155724;"><strong>Date:</strong> ${golfDate}</p>
              <p style="color: #155724;"><strong>Tee-off:</strong> Beginning at ${golfTime}</p>
              <p style="color: #155724;"><strong>Location:</strong> South Lake Tahoe Golf Course, 2500 Emerald Bay Road, South Lake Tahoe</p>
              <p style="color: #155724;"><strong>Phone:</strong> (*************</p>
              <p style="color: #155724;">A few days prior to the tournament, registered golfers will receive an email containing the list of foursomes and game rules.</p>
            </div>
          `
              : ''
          }

          <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #3A6CA5; margin-top: 0;">🚗 Transportation & Parking</h3>
            <p><strong>Parking:</strong> Very limited at Stanford Sierra Camp. Attendees with lodging will receive a parking pass at registration.</p>
            <p><strong>Shuttle Service:</strong> Available from Reno-Tahoe International Airport. Contact SSCC at (************* to schedule (at least one week prior).</p>
            <p><strong>Shuttle Info:</strong> <a href="https://stanfordsierra.com/shuttle-info/" style="color: #3A6CA5;">stanfordsierra.com/shuttle-info</a></p>
          </div>

          <div style="background: #f8f9fa; border-left: 4px solid #3A6CA5; padding: 20px; margin: 25px 0;">
            <h3 style="color: #3A6CA5; margin-top: 0;">📅 Schedule Overview</h3>
            <p><strong>Registration Packets:</strong> Available in the main lodge lobby</p>
            <ul>
              <li>${CONFERENCE_DATES.startDate.dayOfWeek}, ${CONFERENCE_DATES.startDate.displayDate}: 3:00 - 6:00 PM</li>
              <li>${new Date(new Date(CONFERENCE_DATES.startDate.date).getTime() + 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}: 8:00 - 9:00 AM</li>
            </ul>
            <p><strong>${CONFERENCE_DATES.startDate.dayOfWeek} Reception and Dinner:</strong> Reception at 6:00 PM, followed by dinner at 7:00 PM</p>
            <p><strong>Special dietary needs</strong> should be sent to <a href="mailto:<EMAIL>" style="color: #3A6CA5;"><EMAIL></a></p>
          </div>

          <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #3A6CA5; margin-top: 0;">💻 Business Services</h3>
            <p><strong>Internet:</strong> Free high-speed wireless access available in the main lodge and all cabins</p>
            <p><strong>Cell Phone:</strong> Reception is inconsistent. Three pay phones available in the main lodge</p>
            <p><strong>Business Center:</strong> Four computers available for guest use</p>
            <p><strong>Fax and Copy:</strong> Services available in the main office</p>
          </div>

          <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0; text-align: center;">
            <h3 style="color: #3A6CA5; margin-top: 0;">📅 Conference Agenda</h3>
            <p>View the complete conference agenda with detailed session information, timing, and event descriptions.</p>
            <div style="margin: 20px 0;">
              <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'https://reg.iepa.com'}/agenda"
                 style="background: #3A6CA5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                📋 View Conference Agenda
              </a>
            </div>
            <p style="font-size: 14px; color: #666; margin: 0;">Agenda updates will be posted as they become available.</p>
          </div>

          <p>If you have any questions, please don't hesitate to contact me at ************ or <a href="mailto:<EMAIL>" style="color: #3A6CA5;"><EMAIL></a>. We look forward to seeing you at IEPA's 2025 Annual Meeting.</p>

          <p style="margin-top: 30px;">Sincerely,<br>
          <strong>Jamie Parker</strong><br>
          <strong>Administrator/Annual Meeting Coordinator</strong></p>

          <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0;"><strong>📎 Important Documents:</strong> Please review the conference documents prior to your departure:</p>
            ${
              conferenceDocuments.length > 0
                ? `
            <ul style="margin: 10px 0;">
              ${conferenceDocuments
                .map(
                  doc => `
                <li><a href="${doc.file_url}" target="_blank" style="color: #3a6ca5; text-decoration: none;">${doc.name}</a>${doc.description ? ` - ${doc.description}` : ''}</li>
              `
                )
                .join('')}
            </ul>
            <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
              <em>All documents are also available on our website at <a href="https://reg.iepa.com/about#resources" target="_blank" style="color: #3a6ca5;">reg.iepa.com/about#resources</a></em>
            </p>
            `
                : `
            <p style="margin: 10px 0;">Conference documents will be available soon. Please check our website at <a href="https://reg.iepa.com/about#resources" target="_blank" style="color: #3a6ca5;">reg.iepa.com/about#resources</a> for updates.</p>
            `
            }
          </div>
        </div>

        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
          <p style="margin: 0; color: #6c757d; font-size: 14px;">
            Independent Energy Producers Association<br>
            California's oldest nonprofit trade association representing independent energy facilities
          </p>
        </div>
      </div>
    `;

    return this.sendEmail(
      {
        to: email,
        subject,
        html,
        attachments: receiptAttachment ? [receiptAttachment] : undefined,
      },
      {
        emailType: 'welcome_email',
        userId: registrationDetails.userId,
        registrationId: registrationDetails.confirmationNumber,
        registrationType: registrationDetails.type,
      }
    );
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(
    email: string,
    resetUrl: string,
    userId?: string
  ): Promise<boolean> {
    const subject = `Reset Your Password - IEPA Conference 2025`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Password Reset Request</h2>
        <p>You requested to reset your password for your IEPA Conference account.</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" 
             style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Reset Password
          </a>
        </div>
        
        <p>This link will expire in 24 hours for security reasons.</p>
        <p>If you didn't request this reset, you can safely ignore this email.</p>
        
        <p>Best regards,<br>
        The IEPA Conference Team</p>
      </div>
    `;

    return this.sendEmail(
      {
        to: email,
        subject,
        html,
      },
      {
        emailType: 'password_reset',
        userId,
      }
    );
  }

  /**
   * Generate PDF receipt attachment for registration confirmation email
   * Generates custom PDF receipt for all registrations
   */
  private async generateReceiptAttachment(
    registrationType: 'attendee' | 'speaker' | 'sponsor',
    registrationId: string
  ): Promise<{
    filename: string;
    content: string;
    type: string;
    disposition: string;
  } | null> {
    try {
      // Import PDF generation functions
      const { generateReceiptPDF } = await import(
        '@/lib/pdf-generation/server'
      );
      const { createSupabaseAdmin } = await import('@/lib/supabase');

      // Get registration data from database
      const supabaseAdmin = createSupabaseAdmin();
      let tableName: string;

      switch (registrationType) {
        case 'attendee':
          tableName = 'iepa_attendee_registrations';
          break;
        case 'speaker':
          tableName = 'iepa_speaker_registrations';
          break;
        case 'sponsor':
          tableName = 'iepa_sponsor_registrations';
          break;
        default:
          throw new Error(`Invalid registration type: ${registrationType}`);
      }

      const { data, error } = await supabaseAdmin
        .from(tableName)
        .select('*')
        .eq('id', registrationId)
        .single();

      if (error || !data) {
        throw new Error(`Registration not found: ${registrationId}`);
      }

      // Generate PDF receipt for all registrations
      console.log(`[EMAIL-RECEIPT] Generating receipt for ${registrationType}`);

      // Transform database data for PDF generation
      const { transformDatabaseDataForPDF } = await import(
        '@/utils/pdf-data-transform'
      );
      const registrationData = transformDatabaseDataForPDF(
        registrationType,
        data
      );

      // For attendees, ensure fullName is available for PDF validation
      if (
        registrationType === 'attendee' &&
        data.first_name &&
        data.last_name
      ) {
        registrationData.fullName = `${data.first_name} ${data.last_name}`;
      }

      // Determine payment method and transaction ID
      let paymentMethod = 'Credit Card';
      let transactionId = data.stripe_payment_intent_id || data.payment_id;

      if (registrationType === 'speaker' || data.payment_status === 'comped') {
        paymentMethod = 'Comped Registration';
        transactionId = 'COMPED';
      } else if (registrationType === 'sponsor') {
        paymentMethod = 'Check Payment';
        transactionId = data.payment_id || 'PENDING';
      }

      const pdfResult = await generateReceiptPDF(
        registrationType,
        registrationData as unknown as
          | AttendeeRegistrationData
          | SpeakerRegistrationData
          | SponsorRegistrationData,
        paymentMethod,
        transactionId
      );

      if (!pdfResult.success || !pdfResult.pdfBuffer || !pdfResult.fileName) {
        throw new Error(pdfResult.error || 'Failed to generate PDF receipt');
      }

      // Return receipt attachment object for SendGrid
      return {
        filename: pdfResult.fileName!,
        content: pdfResult.pdfBuffer!.toString('base64'),
        type: 'application/pdf',
        disposition: 'attachment',
      };
    } catch (error) {
      console.error(
        '[EMAIL-INVOICE] Error generating invoice attachment:',
        error
      );
      throw error;
    }
  }

  /**
   * Get Stripe invoice PDF attachment
   */
  private async getStripeInvoiceAttachment(
    _paymentId: string,
    registrationId: string,
    registrationType: string
  ): Promise<{
    filename: string;
    content: string;
    type: string;
    disposition: string;
  } | null> {
    try {
      // Import Stripe client
      const { stripe } = await import('@/lib/stripe');
      const { createSupabaseAdmin } = await import('@/lib/supabase');

      // Get payment record to find Stripe payment intent
      const supabaseAdmin = createSupabaseAdmin();
      const { data: paymentData, error: paymentError } = await supabaseAdmin
        .from('iepa_payments')
        .select('stripe_payment_intent_id, stripe_invoice_id')
        .eq('registration_id', registrationId)
        .eq('registration_type', registrationType)
        .single();

      if (paymentError || !paymentData) {
        console.log('[EMAIL-INVOICE] No payment record found in database');
        return null;
      }

      let invoiceId = paymentData.stripe_invoice_id;

      // If no invoice ID in payment record, try to find it via payment intent
      if (!invoiceId && paymentData.stripe_payment_intent_id) {
        const paymentIntent = await stripe.paymentIntents.retrieve(
          paymentData.stripe_payment_intent_id
        );
        if (paymentIntent.invoice) {
          invoiceId = paymentIntent.invoice as string;
        }
      }

      // If still no invoice, try to create one
      if (!invoiceId) {
        console.log(
          '[EMAIL-INVOICE] No existing Stripe invoice found, creating one...'
        );

        // Create detailed invoice via our existing API
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:6969'}/api/stripe/create-detailed-invoice`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              registrationId,
              registrationType,
            }),
          }
        );

        const result = await response.json();
        if (result.success && result.invoice?.id) {
          invoiceId = result.invoice.id;
        } else {
          console.warn(
            '[EMAIL-INVOICE] Failed to create Stripe invoice:',
            result.error
          );
          return null;
        }
      }

      if (!invoiceId) {
        console.log('[EMAIL-INVOICE] No Stripe invoice available');
        return null;
      }

      // Download the invoice PDF from Stripe
      const invoice = await stripe.invoices.retrieve(invoiceId);
      if (!invoice.invoice_pdf) {
        console.log('[EMAIL-INVOICE] Stripe invoice has no PDF URL');
        return null;
      }

      // Fetch the PDF content
      const pdfResponse = await fetch(invoice.invoice_pdf);
      if (!pdfResponse.ok) {
        throw new Error(
          `Failed to download Stripe invoice PDF: ${pdfResponse.statusText}`
        );
      }

      const pdfBuffer = Buffer.from(await pdfResponse.arrayBuffer());

      // Generate filename
      const invoiceNumber = invoice.number || invoiceId;
      const filename = `IEPA-Invoice-${invoiceNumber}.pdf`;

      console.log('[EMAIL-INVOICE] Successfully retrieved Stripe invoice PDF');

      return {
        filename,
        content: pdfBuffer.toString('base64'),
        type: 'application/pdf',
        disposition: 'attachment',
      };
    } catch (error) {
      console.error('[EMAIL-INVOICE] Error getting Stripe invoice:', error);
      throw error;
    }
  }

  /**
   * Get email configuration (for debugging) - synchronous version
   */
  getConfigSync() {
    // Return fallback config for synchronous access
    return {
      fromEmail: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      fromName: process.env.SENDGRID_FROM_NAME || 'IEPA Conference 2025',
      supportEmail: process.env.SENDGRID_SUPPORT_EMAIL || '<EMAIL>',
      noreplyEmail: process.env.SENDGRID_NOREPLY_EMAIL || '<EMAIL>',
      testBcc:
        process.env.NODE_ENV === 'development' ? '<EMAIL>' : null,
      isConfigured: this.isConfigured(),
    };
  }
}

// Export singleton instance
export const emailService = new EmailService();

// Export types for external use
export { EmailService };
