'use client';

// Golf Add-On Button Component
// Button to trigger golf add-on modal for existing registrations

import { useState } from 'react';
import { Button } from '@/components/ui';
import { Target } from 'lucide-react';
import { GolfAddOnModal } from './GolfAddOnModal';
import type { AttendeeRegistration } from '@/types/golfAddOn';

interface GolfAddOnButtonProps {
  registration: AttendeeRegistration;
  onSuccess?: () => void;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  disabled?: boolean;
}

export function GolfAddOnButton({
  registration,
  onSuccess,
  variant = 'outline',
  size = 'sm',
  className = '',
  disabled = false,
}: GolfAddOnButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSuccess = () => {
    setIsModalOpen(false);
    onSuccess?.();
  };

  // Don't show button if user already has golf tournament
  if (registration.attending_golf) {
    return null;
  }

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={handleOpenModal}
        disabled={disabled}
        className={`${className}`}
      >
        <Target className="mr-2 h-4 w-4" />
        Add Golf
      </Button>

      <GolfAddOnModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        registration={registration}
        onSuccess={handleSuccess}
      />
    </>
  );
}
