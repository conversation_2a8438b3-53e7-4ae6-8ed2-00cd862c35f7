# Webhook Testing Procedures

**Date**: June 24, 2025  
**Purpose**: Testing procedures for Stripe webhook integration  
**Environment**: Production & Development

## Overview

This document provides step-by-step procedures for testing the IEPA conference registration system's Stripe webhook integration in both development and production environments.

## Prerequisites

### Required Tools

- Stripe CLI installed and configured
- Access to Stripe dashboard (live and test modes)
- Local development environment running
- curl or similar HTTP client

### Required Access

- Stripe account with webhook configuration permissions
- Production environment access (reg.iepa.com)
- Local development server capability

## Testing Procedures

### 1. Development Environment Testing

#### Setup Local Environment

```bash
# Start development server
npm run dev
# Server should start on http://localhost:6969

# In separate terminal, start Stripe webhook forwarding
stripe listen --forward-to localhost:6969/api/stripe/webhook --live
```

#### Test Webhook Reception

```bash
# Trigger test webhook event
stripe trigger checkout.session.completed

# Expected result: Event forwarded to local server
# Check terminal output for webhook processing logs
```

#### Verify Event Processing

- Check development server logs for webhook event reception
- Verify database updates (if applicable)
- Confirm proper error handling for invalid events

### 2. Production Environment Testing

#### Test Webhook Endpoint Availability

```bash
# Test endpoint accessibility
curl -v https://reg.iepa.com/api/stripe/webhook/live

# Expected: Connection successful, endpoint responds
```

#### Test Signature Verification

```bash
# Test with invalid signature (should return 400)
curl -X POST https://reg.iepa.com/api/stripe/webhook/live \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: invalid_signature" \
  -d '{"type": "test"}'

# Expected response: {"error":"Invalid signature"}
```

#### Test Live Event Processing

```bash
# Forward live events to production (for testing only)
stripe listen --forward-to https://reg.iepa.com/api/stripe/webhook/live --live

# Trigger test event
stripe trigger checkout.session.completed --live
```

### 3. Safe Testing with Coupons

#### Create Test Coupon

```bash
# Create 100% off coupon for safe testing
stripe coupons create \
  -d "percent_off=100" \
  -d "duration=forever" \
  -d "name=Test Coupon - 100% Off" \
  -d "id=TEST100" \
  --live
```

#### Test Payment Flow

1. Create test registration with 100% off coupon
2. Complete checkout process
3. Verify webhook events are triggered
4. Confirm registration status updates

### 4. Error Handling Testing

#### Test Invalid Webhook Data

```bash
# Test malformed JSON
curl -X POST http://localhost:6969/api/stripe/webhook \
  -H "Content-Type: application/json" \
  -d 'invalid json'

# Test missing required fields
curl -X POST http://localhost:6969/api/stripe/webhook \
  -H "Content-Type: application/json" \
  -d '{}'
```

#### Test Network Issues

- Simulate timeout scenarios
- Test with large payload sizes
- Verify retry mechanisms

## Validation Checklist

### ✅ Development Environment

- [ ] Local server starts successfully
- [ ] Stripe CLI connects and forwards events
- [ ] Webhook events are received and logged
- [ ] Database connections work properly
- [ ] Error handling functions correctly

### ✅ Production Environment

- [ ] Webhook endpoint is accessible via HTTPS
- [ ] SSL certificate is valid
- [ ] Signature verification works correctly
- [ ] Invalid requests return appropriate errors
- [ ] Live events are processed successfully

### ✅ Security Verification

- [ ] Webhook signature verification is enforced
- [ ] Invalid signatures are rejected
- [ ] No sensitive data is exposed in error responses
- [ ] HTTPS is enforced for all webhook communications

### ✅ Performance Testing

- [ ] Webhook responses are under 30 seconds
- [ ] High volume events are handled properly
- [ ] Memory usage remains stable
- [ ] No resource leaks detected

## Common Issues and Solutions

### Issue: Webhook Events Not Received

**Symptoms**: No events appearing in logs
**Solutions**:

1. Check Stripe CLI connection status
2. Verify webhook endpoint URL configuration
3. Confirm firewall/network settings
4. Check Stripe dashboard for webhook delivery attempts

### Issue: Signature Verification Failures

**Symptoms**: All webhooks return "Invalid signature"
**Solutions**:

1. Verify webhook secret configuration
2. Check environment variable setup
3. Confirm Stripe CLI is using correct endpoint
4. Validate webhook endpoint implementation

### Issue: Database Connection Errors

**Symptoms**: Webhook received but database updates fail
**Solutions**:

1. Check Supabase connection configuration
2. Verify database credentials
3. Confirm table permissions and RLS policies
4. Check network connectivity to database

### Issue: Timeout Errors

**Symptoms**: Webhook processing takes too long
**Solutions**:

1. Optimize database queries
2. Implement async processing for heavy operations
3. Add proper error handling and retries
4. Monitor resource usage

## Monitoring and Logging

### Key Metrics to Monitor

- Webhook response times
- Success/failure rates
- Database connection health
- Memory and CPU usage

### Log Analysis

```bash
# Check webhook processing logs
grep "webhook" /var/log/application.log

# Monitor error rates
grep "ERROR" /var/log/application.log | grep "webhook"

# Check database connection logs
grep "supabase" /var/log/application.log
```

### Stripe Dashboard Monitoring

- Monitor webhook delivery attempts
- Check for failed deliveries
- Review event processing times
- Verify endpoint health status

## Best Practices

### Development

1. Always test with non-production data
2. Use test mode for initial development
3. Implement comprehensive error logging
4. Test edge cases and error scenarios

### Production

1. Monitor webhook delivery success rates
2. Implement proper retry mechanisms
3. Set up alerting for webhook failures
4. Regularly review and update webhook configurations

### Security

1. Always verify webhook signatures
2. Use HTTPS for all webhook endpoints
3. Implement rate limiting if necessary
4. Regularly rotate webhook secrets

## Emergency Procedures

### Webhook Endpoint Down

1. Check server status and logs
2. Verify DNS and SSL configuration
3. Contact hosting provider if necessary
4. Implement temporary workarounds if possible

### Mass Webhook Failures

1. Check Stripe dashboard for system-wide issues
2. Review recent code deployments
3. Verify database connectivity
4. Implement manual processing if necessary

## Documentation References

- [Stripe Webhook Documentation](https://stripe.com/docs/webhooks)
- [IEPA Technical Troubleshooting Guide](../technical/IEPA_Technical_Troubleshooting_Guide.md)
- [Webhook System Verification](../technical/webhook-system-verification.md)

---

_Last Updated: June 24, 2025_  
_Next Review: September 2025_
