import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const {
      email = '<EMAIL>',
      fullName = 'Test User',
      type = 'attendee',
      confirmationNumber = 'TEST-12345',
      userId = 'test-user-id',
      hasLodging = false,
      hasGolf = false,
    } = await request.json();

    // Validate required fields
    if (!email || !fullName || !type) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!confirmationNumber) {
      return NextResponse.json(
        { error: 'Confirmation number is required for PDF invoice generation' },
        { status: 400 }
      );
    }

    console.log('[EMAIL-TEST] Testing welcome email with PDF invoice:', {
      email,
      fullName,
      type,
      confirmationNumber,
      userId,
      hasLodging,
      hasGolf,
    });

    // Try to send email using the email service
    try {
      const { emailService } = await import('@/services/email');
      await emailService.sendWelcomeEmail(email, fullName, {
        type,
        confirmationNumber,
        userId,
        hasLodging,
        hasGolf,
      });

      console.log(
        '[EMAIL-TEST] Welcome email with PDF invoice sent successfully'
      );
      return NextResponse.json({
        success: true,
        message: 'Welcome email with PDF invoice sent successfully',
        details: {
          email,
          fullName,
          type,
          confirmationNumber,
          hasLodging,
          hasGolf,
        },
      });
    } catch (emailError) {
      console.error('[EMAIL-TEST] Failed to send welcome email:', emailError);
      return NextResponse.json(
        {
          error: 'Failed to send welcome email',
          details:
            emailError instanceof Error ? emailError.message : 'Unknown error',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('[EMAIL-TEST] API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
