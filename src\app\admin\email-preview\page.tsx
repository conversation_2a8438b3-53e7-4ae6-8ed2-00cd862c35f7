'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/Checkbox';
import { 
  FiMail, 
  FiEye, 
  FiExternalLink, 
  FiSend, 
  FiCode, 
  FiMonitor,
  FiType,
  FiRefreshCw
} from 'react-icons/fi';

interface EmailPreviewData {
  subject: string;
  html: string;
  text?: string;
  variables: Record<string, unknown>;
}

export default function EmailPreviewPage() {
  const [emailType, setEmailType] = useState<string>('welcome');
  const [previewData, setPreviewData] = useState<EmailPreviewData | null>(null);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'html' | 'text'>('html');
  const [previewMode, setPreviewMode] = useState<'rendered' | 'raw'>('rendered');
  const [testEmailAddress, setTestEmailAddress] = useState('');
  const [sendingTestEmail, setSendingTestEmail] = useState(false);
  const [notification, setNotification] = useState<{type: 'success' | 'error', message: string} | null>(null);

  // Form data for customizing preview
  const [formData, setFormData] = useState({
    name: 'John Doe',
    email: '<EMAIL>',
    type: 'attendee' as 'attendee' | 'speaker' | 'sponsor',
    confirmationNumber: 'CONF-12345',
    hasLodging: true,
    hasGolf: true,
    userId: 'user-123',
    speakerPricingType: 'comped-speaker',
    sponsorshipLevel: 'bronze-sponsor'
  });

  const emailTypes = [
    { value: 'welcome', label: 'Welcome Email', description: 'Comprehensive welcome email with conference details' },
    { value: 'registration_confirmation', label: 'Registration Confirmation', description: 'Email sent after successful registration' },
    { value: 'payment_confirmation', label: 'Payment Confirmation', description: 'Email sent after payment completion' }
  ];

  const loadPreview = useCallback(async () => {
    setLoading(true);
    setNotification(null);

    try {
      const response = await fetch('/api/admin/email-preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailType,
          sampleData: formData
        }),
      });

      const result = await response.json();

      if (result.success) {
        setPreviewData(result.preview);
      } else {
        setNotification({
          type: 'error',
          message: result.error || 'Failed to load preview'
        });
      }
    } catch (error) {
      console.error('Error loading preview:', error);
      setNotification({
        type: 'error',
        message: 'Failed to load preview'
      });
    } finally {
      setLoading(false);
    }
  }, [emailType, formData]);

  const sendTestEmail = async () => {
    if (!testEmailAddress || !previewData) return;

    setSendingTestEmail(true);
    setNotification(null);

    try {
      const response = await fetch('/api/admin/email-preview/test-send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailType,
          testEmail: testEmailAddress,
          sampleData: formData
        }),
      });

      const result = await response.json();

      if (result.success) {
        setNotification({
          type: 'success',
          message: `Test email sent successfully to ${testEmailAddress}`
        });
        setTestEmailAddress('');
      } else {
        setNotification({
          type: 'error',
          message: result.error || 'Failed to send test email'
        });
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      setNotification({
        type: 'error',
        message: 'Failed to send test email'
      });
    } finally {
      setSendingTestEmail(false);
    }
  };

  // Load preview on component mount and when email type changes
  useEffect(() => {
    loadPreview();
  }, [loadPreview]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <FiMail className="mr-3 text-blue-600" />
            Email Content Preview
          </h1>
          <p className="mt-2 text-gray-600">
            Preview and test email templates with dynamic configuration values
          </p>
        </div>

        {/* Notification */}
        {notification && (
          <div className={`mb-6 p-4 rounded-md ${
            notification.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800' 
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            {notification.message}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Configuration Panel */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Email Configuration</h2>
              
              {/* Email Type Selection */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="emailType">Email Type</Label>
                  <Select value={emailType} onValueChange={setEmailType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select email type" />
                    </SelectTrigger>
                    <SelectContent>
                      {emailTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div>
                            <div className="font-medium">{type.label}</div>
                            <div className="text-sm text-gray-500">{type.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Sample Data Configuration */}
                <div className="space-y-3">
                  <h3 className="font-medium text-gray-900">Sample Data</h3>
                  
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                    />
                  </div>

                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                    />
                  </div>

                  <div>
                    <Label htmlFor="type">Registration Type</Label>
                    <Select 
                      value={formData.type} 
                      onValueChange={(value: 'attendee' | 'speaker' | 'sponsor') => 
                        setFormData({...formData, type: value})
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="attendee">Attendee</SelectItem>
                        <SelectItem value="speaker">Speaker</SelectItem>
                        <SelectItem value="sponsor">Sponsor</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="confirmationNumber">Confirmation Number</Label>
                    <Input
                      id="confirmationNumber"
                      value={formData.confirmationNumber}
                      onChange={(e) => setFormData({...formData, confirmationNumber: e.target.value})}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasLodging"
                      checked={formData.hasLodging}
                      onCheckedChange={(checked) => setFormData({...formData, hasLodging: !!checked})}
                    />
                    <Label htmlFor="hasLodging">Has Lodging</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasGolf"
                      checked={formData.hasGolf}
                      onCheckedChange={(checked) => setFormData({...formData, hasGolf: !!checked})}
                    />
                    <Label htmlFor="hasGolf">Has Golf</Label>
                  </div>
                </div>

                <Button 
                  onClick={loadPreview} 
                  disabled={loading}
                  className="w-full"
                >
                  <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  {loading ? 'Loading...' : 'Refresh Preview'}
                </Button>
              </div>
            </Card>

            {/* Test Email Section */}
            <Card className="p-6 mt-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Send Test Email</h2>
              <div className="space-y-3">
                <div>
                  <Label htmlFor="testEmail">Test Email Address</Label>
                  <Input
                    id="testEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    value={testEmailAddress}
                    onChange={(e) => setTestEmailAddress(e.target.value)}
                  />
                </div>
                <Button 
                  onClick={sendTestEmail}
                  disabled={!testEmailAddress || !previewData || sendingTestEmail}
                  className="w-full"
                >
                  <FiSend className={`w-4 h-4 mr-2 ${sendingTestEmail ? 'animate-pulse' : ''}`} />
                  {sendingTestEmail ? 'Sending...' : 'Send Test Email'}
                </Button>
              </div>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-900">Email Preview</h2>
                
                {/* View Controls */}
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={() => setViewMode('html')}
                    size="sm"
                    variant={viewMode === 'html' ? 'default' : 'outline'}
                  >
                    <FiEye className="w-4 h-4 mr-1" />
                    HTML
                  </Button>
                  <Button
                    onClick={() => setViewMode('text')}
                    size="sm"
                    variant={viewMode === 'text' ? 'default' : 'outline'}
                  >
                    <FiType className="w-4 h-4 mr-1" />
                    Text
                  </Button>
                  <div className="h-4 w-px bg-gray-300 mx-2" />
                  <Button
                    onClick={() => setPreviewMode('rendered')}
                    size="sm"
                    variant={previewMode === 'rendered' ? 'default' : 'outline'}
                  >
                    <FiMonitor className="w-4 h-4 mr-1" />
                    Rendered
                  </Button>
                  <Button
                    onClick={() => setPreviewMode('raw')}
                    size="sm"
                    variant={previewMode === 'raw' ? 'default' : 'outline'}
                  >
                    <FiCode className="w-4 h-4 mr-1" />
                    Raw
                  </Button>
                  {previewData && viewMode === 'html' && previewMode === 'rendered' && (
                    <Button
                      onClick={() => {
                        const newWindow = window.open('', '_blank');
                        if (newWindow) {
                          newWindow.document.write(previewData.html);
                          newWindow.document.close();
                        }
                      }}
                      size="sm"
                      variant="outline"
                    >
                      <FiExternalLink className="w-4 h-4 mr-1" />
                      Open in New Tab
                    </Button>
                  )}
                </div>
              </div>

              {previewData ? (
                <div className="space-y-6">
                  {/* Subject */}
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Subject</h3>
                    <div className="p-3 bg-gray-50 rounded border">
                      <code className="text-sm">{previewData.subject}</code>
                    </div>
                  </div>

                  {/* Content */}
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">
                      {viewMode === 'html' ? 'HTML Content' : 'Text Content'}
                    </h3>
                    <div className="border rounded">
                      {viewMode === 'html' ? (
                        previewMode === 'rendered' ? (
                          <div className="p-4 bg-white border rounded max-h-96 overflow-y-auto">
                            <div dangerouslySetInnerHTML={{ __html: previewData.html }} />
                          </div>
                        ) : (
                          <div className="p-4 bg-gray-50 rounded max-h-96 overflow-y-auto">
                            <pre className="text-sm whitespace-pre-wrap">{previewData.html}</pre>
                          </div>
                        )
                      ) : (
                        <div className="p-4 bg-gray-50 rounded max-h-96 overflow-y-auto">
                          <pre className="text-sm whitespace-pre-wrap">
                            {previewData.text || 'No text version available'}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Variables Used */}
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Dynamic Values Used</h3>
                    <div className="space-y-2">
                      {Object.entries(previewData.variables).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <Badge variant="outline">{key}</Badge>
                          <code className="text-sm text-gray-600">{String(value)}</code>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  {loading ? 'Loading preview...' : 'Select an email type to preview'}
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
