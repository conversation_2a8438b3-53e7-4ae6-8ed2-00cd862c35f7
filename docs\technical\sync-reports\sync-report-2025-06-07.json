{"timestamp": "2025-06-07T18:33:31.123Z", "options": {}, "results": [{"table": "iepa_user_profiles", "productionCount": 110, "localCount": 0, "synced": 0, "errors": ["Failed to insert batch: Could not find the 'gender' column of 'iepa_user_profiles' in the schema cache"]}, {"table": "iepa_organizations", "productionCount": 10, "localCount": 0, "synced": 0, "errors": ["Failed to insert batch: Could not find the 'last_used_at' column of 'iepa_organizations' in the schema cache"]}, {"table": "iepa_historical_registrations", "productionCount": 109, "localCount": 0, "synced": 0, "errors": ["Failed to insert batch: Could not find the 'golf_total' column of 'iepa_historical_registrations' in the schema cache"]}, {"table": "iepa_attendee_registrations", "productionCount": 6, "localCount": 0, "synced": 0, "errors": ["Failed to insert batch: insert or update on table \"iepa_attendee_registrations\" violates foreign key constraint \"iepa_attendee_registrations_user_id_fkey\""]}, {"table": "iepa_speaker_registrations", "productionCount": 1, "localCount": 0, "synced": 0, "errors": ["Failed to insert batch: insert or update on table \"iepa_speaker_registrations\" violates foreign key constraint \"iepa_speaker_registrations_user_id_fkey\""]}, {"table": "iepa_sponsor_registrations", "productionCount": 4, "localCount": 0, "synced": 0, "errors": ["Failed to insert batch: insert or update on table \"iepa_sponsor_registrations\" violates foreign key constraint \"iepa_sponsor_registrations_user_id_fkey\""]}, {"table": "iepa_golf_registrations", "productionCount": 0, "localCount": 0, "synced": 0, "errors": []}, {"table": "iepa_payments", "productionCount": 6, "localCount": 0, "synced": 0, "errors": ["Failed to insert batch: insert or update on table \"iepa_payments\" violates foreign key constraint \"iepa_payments_user_id_fkey\""]}, {"table": "iepa_email_log", "productionCount": 0, "localCount": 0, "synced": 0, "errors": []}], "summary": {"totalProductionRows": 246, "totalSyncedRows": 0, "totalErrors": 7}}