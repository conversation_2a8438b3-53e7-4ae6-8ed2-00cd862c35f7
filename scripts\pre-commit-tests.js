#!/usr/bin/env node

/**
 * Comprehensive pre-commit testing script
 * Runs all necessary tests to catch issues before committing
 */

const { execSync } = require('child_process');
const fs = require('fs');

const TESTS = [
  {
    name: 'Build Test',
    command: 'npm run build',
    description: 'Testing production build (includes TypeScript check)...',
    required: true,
  },
  {
    name: 'Client-side Error Check',
    command: 'npm run test:client',
    description: 'Testing for client-side runtime errors...',
    required: true,
    requiresServer: true,
  },
];

function checkServerRunning() {
  try {
    // Just check if anything responds on port 6969
    execSync('curl -s http://localhost:6969 > /dev/null 2>&1', {
      stdio: 'ignore',
    });
    return true;
  } catch {
    return false;
  }
}

function runCommand(command, description) {
  console.log(`🔍 ${description}`);
  try {
    const output = execSync(command, {
      encoding: 'utf8',
      stdio: 'pipe',
    });
    return { success: true, output };
  } catch (error) {
    return {
      success: false,
      output: error.stdout || error.stderr || error.message,
    };
  }
}

async function runTests() {
  console.log('🧪 Running pre-commit tests...\n');

  let allPassed = true;
  const results = [];

  // Check if server is needed and running
  const needsServer = TESTS.some(test => test.requiresServer);
  const serverRunning = checkServerRunning();

  if (needsServer && !serverRunning) {
    console.log('⚠️  Development server not running on port 6969');
    console.log('   Client-side tests will be skipped');
    console.log('   To run full tests, start server with: npm run dev\n');
  }

  for (const test of TESTS) {
    // Skip server-dependent tests if server not running
    if (test.requiresServer && !serverRunning) {
      console.log(`⏭️  Skipping ${test.name} (server not running)`);
      results.push({
        ...test,
        success: true,
        output: 'Skipped - server not running',
      });
      continue;
    }

    const result = runCommand(test.command, test.description);
    results.push({ ...test, ...result });

    if (result.success) {
      console.log(`✅ ${test.name} passed\n`);
    } else {
      console.log(`❌ ${test.name} failed`);
      if (test.required) {
        allPassed = false;
      }

      // Show error output for failed tests
      if (result.output) {
        console.log('Error output:');
        console.log(result.output.slice(0, 1000)); // Limit output length
        if (result.output.length > 1000) {
          console.log('... (output truncated)');
        }
      }
      console.log('');
    }
  }

  // Summary
  console.log('📊 Test Summary:');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`   ${status} ${result.name}`);
  });

  if (allPassed) {
    console.log('\n🎉 All tests passed! Ready to commit.');
    process.exit(0);
  } else {
    console.log('\n💥 Some tests failed. Please fix issues before committing.');
    process.exit(1);
  }
}

if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}
