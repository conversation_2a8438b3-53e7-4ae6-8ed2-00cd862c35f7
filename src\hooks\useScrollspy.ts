import { useState, useEffect, useRef, RefObject, createRef } from 'react';

export interface UseScrollspyOptions {
  offset?: number;
  threshold?: number;
}

export interface UseScrollspyReturn {
  activeSection: string;
  sectionRefs: RefObject<HTMLElement | null>[];
}

/**
 * Custom hook for implementing scrollspy functionality
 * Tracks which section is currently visible in the viewport
 */
export function useScrollspy(
  sectionIds: string[],
  options: UseScrollspyOptions = {}
): UseScrollspyReturn {
  const { offset = 100, threshold = 0.5 } = options;
  const [activeSection, setActiveSection] = useState<string>(sectionIds[0] || '');
  const sectionRefs = useRef<RefObject<HTMLElement | null>[]>(
    sectionIds.map(() => createRef<HTMLElement>())
  );

  useEffect(() => {
    // Initialize refs for each section
    sectionRefs.current = sectionIds.map((id) => ({
      current: document.getElementById(id)
    }));

    const observerOptions: IntersectionObserverInit = {
      root: null,
      rootMargin: `-${offset}px 0px -${100 - (threshold * 100)}% 0px`,
      threshold: [0, threshold, 1]
    };

    const observerCallback: IntersectionObserverCallback = (entries) => {
      // Find the entry with the highest intersection ratio that's intersecting
      let maxRatio = 0;
      let activeId = '';

      entries.forEach((entry) => {
        if (entry.isIntersecting && entry.intersectionRatio > maxRatio) {
          maxRatio = entry.intersectionRatio;
          activeId = entry.target.id;
        }
      });

      // If we found an intersecting section, update active section
      if (activeId) {
        setActiveSection(activeId);
      } else {
        // Fallback: find the section closest to the top of the viewport
        let closestSection = '';
        let closestDistance = Infinity;

        sectionIds.forEach((id) => {
          const element = document.getElementById(id);
          if (element) {
            const rect = element.getBoundingClientRect();
            const distance = Math.abs(rect.top - offset);
            
            if (distance < closestDistance) {
              closestDistance = distance;
              closestSection = id;
            }
          }
        });

        if (closestSection) {
          setActiveSection(closestSection);
        }
      }
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    // Observe all sections
    sectionIds.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        observer.observe(element);
      }
    });

    // Cleanup function
    return () => {
      observer.disconnect();
    };
  }, [sectionIds, offset, threshold]);

  // Handle scroll events for more responsive updates
  useEffect(() => {
    const handleScroll = () => {
      // Throttle scroll events
      const now = Date.now();
      if (handleScroll.lastCall && now - handleScroll.lastCall < 100) {
        return;
      }
      handleScroll.lastCall = now;

      // Find the section that's most visible
      let activeId = '';
      let maxVisibleHeight = 0;

      sectionIds.forEach((id) => {
        const element = document.getElementById(id);
        if (element) {
          const rect = element.getBoundingClientRect();
          const viewportHeight = window.innerHeight;
          
          // Calculate visible height of the section
          const visibleTop = Math.max(0, rect.top);
          const visibleBottom = Math.min(viewportHeight, rect.bottom);
          const visibleHeight = Math.max(0, visibleBottom - visibleTop);
          
          // Consider sections that are at least partially visible and above the offset
          if (visibleHeight > 0 && rect.top <= offset && visibleHeight > maxVisibleHeight) {
            maxVisibleHeight = visibleHeight;
            activeId = id;
          }
        }
      });

      if (activeId && activeId !== activeSection) {
        setActiveSection(activeId);
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Initial check
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [sectionIds, activeSection, offset]);

  return {
    activeSection,
    sectionRefs: sectionRefs.current
  };
}

// Add type augmentation for the lastCall property
declare global {
  interface Function {
    lastCall?: number;
  }
}
