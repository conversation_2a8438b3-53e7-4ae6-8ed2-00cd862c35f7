# 🔧 Fix Supabase Authentication Redirect URLs

## 🚨 **Problem**

Password reset emails from Supabase are redirecting to `localhost:3001` instead of the production domain `reg.iepa.com`.

## 🎯 **Root Cause**

The Supabase project authentication settings are configured with localhost URLs instead of the production domain.

## ✅ **Solution: Update Supabase Authentication Settings**

### **Method 1: Via Supabase Dashboard (Recommended)**

1. **Open Supabase Dashboard**:

   - Go to: https://supabase.com/dashboard/project/uffhyhpcuedjsisczocy

2. **Navigate to Authentication Settings**:

   - Click **Authentication** in the left sidebar
   - Click **Settings** tab
   - Click **URL Configuration** section

3. **Update Site URL**:

   ```
   Current: http://localhost:6969
   Change to: https://reg.iepa.com
   ```

4. **Update Redirect URLs**:
   Add these URLs to the **Redirect URLs** list:

   ```
   https://reg.iepa.com/**
   https://reg.iepa.com/auth/callback
   https://reg.iepa.com/auth/reset-password
   http://localhost:*  (keep for development)
   ```

5. **Save Changes**:
   - Click **Save** to apply the changes

### **Method 2: Via Supabase CLI (Alternative)**

If you have Supabase CLI installed:

```bash
# Update site URL
supabase projects update --project-ref uffhyhpcuedjsisczocy --site-url https://reg.iepa.com

# Update redirect URLs (requires manual dashboard configuration)
```

### **Method 3: Environment Variables for Deployment**

For your deployment platform (Vercel, Netlify, etc.), ensure these environment variables are set:

```env
NEXT_PUBLIC_APP_URL=https://reg.iepa.com
NEXTAUTH_URL=https://reg.iepa.com
NEXT_PUBLIC_SUPABASE_URL=https://uffhyhpcuedjsisczocy.supabase.co
```

## 🧪 **Testing the Fix**

After updating the settings:

1. **Test Password Reset**:

   - Go to your production site: https://reg.iepa.com
   - Try the "Forgot Password" feature
   - Check that the email link redirects to `reg.iepa.com` instead of localhost

2. **Test Authentication Flow**:

   - Sign up for a new account
   - Verify email confirmation links work correctly
   - Test login/logout functionality

3. **Check Email Links**:
   - All authentication emails should now contain `reg.iepa.com` links
   - No more localhost URLs in production emails

## 📋 **Current Configuration Status**

### ❌ **Before Fix**:

- Site URL: `http://localhost:6969`
- Redirect URLs: `localhost:*` only
- Password reset emails: redirect to localhost

### ✅ **After Fix**:

- Site URL: `https://reg.iepa.com`
- Redirect URLs: `reg.iepa.com` + localhost for development
- Password reset emails: redirect to production domain

## 🔍 **Verification Steps**

1. **Check Supabase Dashboard**:

   - Verify Site URL shows `https://reg.iepa.com`
   - Verify Redirect URLs include production domain

2. **Test Email Flow**:

   - Request password reset from production site
   - Check email contains correct production URL
   - Click link and verify it goes to production site

3. **Monitor Logs**:
   - Check for any authentication errors
   - Verify successful redirects in browser network tab

## 🚨 **Important Notes**

- **Development**: Keep localhost URLs for local development
- **Production**: Only use `reg.iepa.com` for production
- **Security**: Ensure HTTPS is used for production URLs
- **Testing**: Test thoroughly before going live

## 🔄 **Rollback Plan**

If issues occur, you can quickly revert:

1. Go back to Supabase Dashboard
2. Change Site URL back to previous value
3. Remove production URLs from Redirect URLs
4. Save changes

## 📞 **Support**

If you encounter issues:

- Check Supabase Dashboard for error messages
- Verify environment variables in deployment platform
- Test authentication flow step by step
- Check browser developer tools for redirect errors

---

**Document Information**
**Document Type**: Implementation Guide
**Last Updated**: December 2024
**Document Version**: 1.0
**System Version**: v0.1.0
**Implementation Status**: ✅ Complete
**Prepared By**: Technical Team

**🎯 This fix will ensure all Supabase authentication emails redirect to your production domain instead of localhost.**
