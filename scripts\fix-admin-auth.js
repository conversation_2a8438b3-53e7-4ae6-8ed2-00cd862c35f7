#!/usr/bin/env node

/**
 * Fix Admin Authentication Script
 * Attempts to fix the existing admin user authentication
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

// Create admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixAdminAuth() {
  console.log('🔧 Attempting to fix admin authentication...');
  
  const email = '<EMAIL>';
  const newPassword = 'AdminPass123!';
  
  try {
    // First, let's check the admin_users table
    console.log('🔍 Checking admin_users table...');
    const { data: adminUser, error: adminError } = await supabase
      .from('iepa_admin_users')
      .select('*')
      .eq('email', email)
      .single();
      
    if (adminError) {
      console.error('❌ Error checking admin user:', adminError);
      return;
    }
    
    console.log('✅ Admin user found in database:');
    console.log('   Email:', adminUser.email);
    console.log('   User ID:', adminUser.user_id);
    console.log('   Role:', adminUser.role);
    console.log('   Active:', adminUser.is_active);
    
    // Try to get the auth user by ID
    if (adminUser.user_id) {
      console.log('\n🔍 Checking auth user by ID...');
      const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(adminUser.user_id);
      
      if (authError) {
        console.error('❌ Auth user not found:', authError.message);
        console.log('🔧 The user_id in admin table points to non-existent auth user');
        
        // Try to create a new auth user and link it
        console.log('👤 Creating new auth user...');
        const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
          email: email,
          password: newPassword,
          email_confirm: true,
          user_metadata: {
            full_name: 'Admin User',
            first_name: 'Admin',
            last_name: 'User'
          }
        });
        
        if (createError) {
          if (createError.message.includes('already been registered')) {
            console.log('⚠️ User exists but with different ID, trying password reset...');
            
            // Try to reset password for existing user
            const { error: resetError } = await supabase.auth.admin.updateUserById(
              adminUser.user_id, // Try with the stored ID first
              { password: newPassword }
            );
            
            if (resetError) {
              console.error('❌ Password reset failed:', resetError.message);
            } else {
              console.log('✅ Password reset successful');
            }
          } else {
            console.error('❌ Error creating new user:', createError);
          }
        } else {
          console.log('✅ New auth user created:', newUser.user.id);
          
          // Update admin table with new user ID
          const { error: updateError } = await supabase
            .from('iepa_admin_users')
            .update({ user_id: newUser.user.id })
            .eq('email', email);
            
          if (updateError) {
            console.error('❌ Error updating admin table:', updateError);
          } else {
            console.log('✅ Admin table updated with new user ID');
          }
        }
      } else {
        console.log('✅ Auth user found:', authUser.user.email);
        console.log('🔧 Updating password for existing user...');
        
        const { error: updateError } = await supabase.auth.admin.updateUserById(
          authUser.user.id,
          { password: newPassword }
        );
        
        if (updateError) {
          console.error('❌ Password update failed:', updateError);
        } else {
          console.log('✅ Password updated successfully');
        }
      }
    }
    
    // Test the login
    console.log('\n🔐 Testing login...');
    const testClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    
    const { data: loginData, error: loginError } = await testClient.auth.signInWithPassword({
      email: email,
      password: newPassword
    });
    
    if (loginError) {
      console.error('❌ Login test failed:', loginError.message);
      
      if (loginError.message.includes('Database error querying schema')) {
        console.log('🔍 The schema error persists - this may be a deeper Supabase configuration issue');
        console.log('💡 Recommendation: Check Supabase project health and RLS policies');
      }
    } else {
      console.log('✅ Login test successful!');
      console.log('👤 User ID:', loginData.user.id);
      console.log('📧 Email:', loginData.user.email);
      console.log('🎉 Authentication is now working!');
      
      // Sign out
      await testClient.auth.signOut();
    }
    
  } catch (error) {
    console.error('💥 Script error:', error);
  }
}

// Run the script
fixAdminAuth().then(() => {
  console.log('\n🎯 Admin authentication fix complete!');
  console.log('📝 Try logging in with:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: AdminPass123!');
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
