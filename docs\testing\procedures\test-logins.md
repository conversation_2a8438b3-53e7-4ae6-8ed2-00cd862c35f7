# IEPA Conference Registration - Test Logins & Credentials

**Last Updated:** January 30, 2025  
**Environment:** Development (http://localhost:6969)  
**Database:** Supabase NDS Project (uffhyhpcuedjsisczocy)  
**Authentication:** Email confirmation disabled for testing

## 🔐 Primary Test Accounts

### 1. Admin Account

- **Email:** `<EMAIL>`
- **Password:** [Contact admin]
- **Role:** Administrator
- **Purpose:** Admin dashboard testing, user management, system configuration
- **Access:** Full admin privileges, all dashboard features

### 2. Golf Test User (Recommended for Golf Add-On Testing)

- **Email:** `<EMAIL>`
- **Password:** `GolfTest123!`
- **User ID:** `4ea60bd3-28ae-4e23-916d-46a6310a9c24`
- **Purpose:** Golf tournament registration, club rental, payment processing
- **Status:** ✅ Verified Working (Created: Jan 5, 2025)
- **Notes:** Created through proper Supabase Auth API

### 3. Attendee Test User

- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Purpose:** Regular attendee registration flow, pricing options, meal selections
- **Registration Types:** IEPA Member, Non-Member, Day Use, Government, CCA
- **Status:** ✅ Verified Working

### 4. Speaker Test User

- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Purpose:** Speaker registration, presentation uploads, speaker-specific features
- **Registration Types:** Comped Speaker ($0), Paid Speaker ($1,500)
- **Status:** ✅ Verified Working

### 5. Sponsor Test User

- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Purpose:** Sponsor registration, sponsorship levels, sponsor-specific features
- **Registration Types:** All sponsorship tiers with automatic discounts
- **Status:** ✅ Verified Working

### 6. E2E Test User

- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Purpose:** Automated testing, Playwright testing, comprehensive flow testing
- **Status:** ✅ Verified Working

## 🧪 Testing-Specific Email Accounts

### Registration Flow Testing

- **IEPA Member Test:** `<EMAIL>`
- **Non-Member Test:** `<EMAIL>`
- **Comped Speaker Test:** `<EMAIL>`
- **Paid Speaker Test:** `<EMAIL>`
- **Spouse Registration:** `<EMAIL>`
- **Child Registration:** `<EMAIL>`
- **Sponsor Attendee:** `<EMAIL>`
- **Payment Failure:** `<EMAIL>`

### Email System Testing

- **Email Confirmation:** `<EMAIL>`
- **PDF Generation:** `<EMAIL>`
- **Welcome Email:** `<EMAIL>`

## 🏢 IEPA Staff Accounts (Production-like)

### Real IEPA Domain Accounts

- **Jamie:** `<EMAIL>` (Password: Contact admin)
- **Sara:** `<EMAIL>` (Password: Contact admin)
- **Smutny:** `<EMAIL>` (Password: Contact admin)
- **Purpose:** Production-like testing with real IEPA domain emails

## 💳 Payment Testing Credentials

### Stripe Test Cards

- **Success Card:** `****************`
- **Decline Card:** `****************`
- **Insufficient Funds:** `****************`
- **Expired Card:** `****************`
- **CVC Check Fail:** `****************`

### Test Payment Details

- **Expiry:** Any future date (e.g., 12/25)
- **CVC:** Any 3-digit number (e.g., 123)
- **ZIP:** Any 5-digit number (e.g., 12345)

## 🔧 Authentication Configuration

### Current Settings

- **Email Confirmation:** ❌ Disabled (`mailer_autoconfirm: true`)
- **Immediate Login:** ✅ Enabled (users can log in immediately after signup)
- **Development Mode:** ✅ Active (no email verification required)
- **Site URL:** `http://localhost:6969` (configured for local development)
- **URI Allow List:** Includes localhost on all ports for testing

### Supabase Project Details

- **Project ID:** `uffhyhpcuedjsisczocy`
- **Region:** `us-west-1`
- **Database:** NDS Project (use iepa\_ prefixed tables)

## 📧 Email Testing Configuration

### Email Service

- **Provider:** SendGrid (test configuration)
- **From Address:** `<EMAIL>` (temporary testing domain)
- **BCC for Testing:** `<EMAIL>` (temporarily added for verification)
- **Email Logging:** All emails logged to `iepa_email_logs` table

### Email Templates

- **Confirmation Email:** Registration confirmation with PDF attachment
- **Welcome Email:** Longer welcome template for attendees
- **Speaker Confirmation:** Speaker-specific confirmation template
- **Sponsor Instructions:** Payment by check instructions

## 🎯 Test Account Usage Guidelines

### Registration Type Testing

1. **Regular Attendee:** Use `<EMAIL>`
2. **Speaker Registration:** Use `<EMAIL>`
3. **Sponsor Registration:** Use `<EMAIL>`
4. **Golf Add-ons:** Use `<EMAIL>`
5. **E2E Testing:** Use `<EMAIL>`

### Linked Registration Testing

- **Primary Attendee:** Use any main test account
- **Spouse Registration:** Link to primary attendee email
- **Child Registration:** Link to primary attendee email

### Payment Flow Testing

- **Successful Payment:** Use success test card
- **Failed Payment:** Use decline test card
- **Free Registration:** Comped speakers, sponsor attendees

## 🔍 Verification Checklist

### Account Access

- [ ] All test accounts can log in successfully
- [ ] Authentication state persists across page refreshes
- [ ] Welcome bar displays correctly for authenticated users
- [ ] Navigation reflects user authentication status

### Registration Access

- [ ] Attendee account can access `/register` and `/register/attendee`
- [ ] Speaker account can access `/register/speaker`
- [ ] Sponsor account can access `/register/sponsor`
- [ ] Admin account can access `/admin` dashboard

### Email System

- [ ] Confirmation emails sent to test accounts
- [ ] BCC emails received at `<EMAIL>`
- [ ] Email logs recorded in `iepa_email_logs` table
- [ ] PDF attachments generated and accessible

## 🚨 Security Notes

### Test Account Management

- **Scope:** These accounts are for testing only
- **Data:** All test data should be clearly marked and removable
- **Access:** Test accounts should not have admin privileges (except designated admin)
- **Cleanup:** Remove or deactivate test accounts before production deployment

### Production Deployment

Before going live:

- [ ] Remove BCC test email configuration
- [ ] Switch to production Stripe keys
- [ ] Update email configuration to use `<EMAIL>`
- [ ] Verify production Supabase instance
- [ ] Remove or deactivate all test accounts
- [ ] Clear test data from database

## 📞 Support Resources

- **Supabase Dashboard:** https://supabase.com/dashboard/project/uffhyhpcuedjsisczocy
- **Auth Settings:** Authentication > Settings
- **User Management:** Authentication > Users
- **Logs:** Logs > Auth logs for debugging
- **Stripe Dashboard:** https://dashboard.stripe.com/test (test mode)

---

**Note:** This document contains sensitive testing credentials. Keep secure and do not commit to public repositories.
