// PDF Receipt Template for IEPA Conference Registration
// React-PDF template for generating professional receipts

import React from 'react';
import { Document, Page, Text, View } from '@react-pdf/renderer';
import { PDFReceiptData } from '../types';
import { IEPA_COMPANY_INFO, CONFERENCE_INFO } from '../config';
import { formatCurrency, getCustomerInfo } from '../utils';
import { pdfStyles } from './PDFStyles';

interface ReceiptTemplateProps {
  data: PDFReceiptData;
}

export const ReceiptTemplate: React.FC<ReceiptTemplateProps> = ({ data }) => {
  console.log('[RECEIPT-TEMPLATE] Rendering template with data:', {
    type: data?.type,
    receiptNumber: data?.receiptNumber,
    hasRegistrationData: !!data?.registrationData,
    lineItemsCount: data?.lineItems?.length,
  });

  try {
    const customerInfo = getCustomerInfo(data.type, data.registrationData);

    console.log('[RECEIPT-TEMPLATE] Customer info extracted:', {
      name: customerInfo?.name,
      email: customerInfo?.email,
    });

    return (
      <Document>
        <Page size="LETTER" style={pdfStyles.page}>
          {/* Header Section */}
          <View style={pdfStyles.header}>
            <View style={pdfStyles.headerLeft}>
              {/* IEPA Logo would go here */}
              <Text style={pdfStyles.companyName}>
                {IEPA_COMPANY_INFO.name}
              </Text>
              <Text style={pdfStyles.companyFullName}>
                {IEPA_COMPANY_INFO.fullName}
              </Text>
              <Text style={pdfStyles.companyAddress}>
                {IEPA_COMPANY_INFO.address.street}
                {'\n'}
                {IEPA_COMPANY_INFO.address.city},{' '}
                {IEPA_COMPANY_INFO.address.state}{' '}
                {IEPA_COMPANY_INFO.address.zipCode}
                {'\n'}
                {IEPA_COMPANY_INFO.phone}
                {'\n'}
                {IEPA_COMPANY_INFO.email}
              </Text>
            </View>

            <View style={pdfStyles.headerRight}>
              <Text style={pdfStyles.documentTitle}>RECEIPT</Text>
              <Text style={pdfStyles.documentNumber}>
                Receipt #: {data.receiptNumber}
              </Text>
              <Text style={pdfStyles.documentDate}>Date: {data.issueDate}</Text>
              {data.paymentMethod && (
                <Text style={pdfStyles.documentDate}>
                  Payment Method: {data.paymentMethod}
                </Text>
              )}
              {data.transactionId && (
                <Text style={pdfStyles.documentDate}>
                  Transaction ID: {data.transactionId}
                </Text>
              )}
            </View>
          </View>

          {/* Conference Information */}
          <View style={pdfStyles.conferenceInfo}>
            <Text style={pdfStyles.conferenceName}>{CONFERENCE_INFO.name}</Text>
            <Text style={pdfStyles.conferenceDates}>
              {CONFERENCE_INFO.dates.start} - {CONFERENCE_INFO.dates.end}
            </Text>
            <Text style={pdfStyles.conferenceVenue}>
              {CONFERENCE_INFO.venue.name}, {CONFERENCE_INFO.venue.city},{' '}
              {CONFERENCE_INFO.venue.state}
            </Text>
          </View>

          {/* Customer Information */}
          <View style={pdfStyles.customerSection}>
            <Text style={pdfStyles.sectionTitle}>Registration Details</Text>
            <View style={pdfStyles.customerInfo}>
              <View style={pdfStyles.customerLeft}>
                <Text style={pdfStyles.customerName}>{customerInfo.name}</Text>
                <Text style={pdfStyles.customerDetail}>
                  Email: {customerInfo.email}
                </Text>
                {customerInfo.phone && (
                  <Text style={pdfStyles.customerDetail}>
                    Phone: {customerInfo.phone}
                  </Text>
                )}
                {customerInfo.organization && (
                  <Text style={pdfStyles.customerDetail}>
                    Organization: {customerInfo.organization}
                  </Text>
                )}
                {customerInfo.jobTitle && (
                  <Text style={pdfStyles.customerDetail}>
                    Title: {customerInfo.jobTitle}
                  </Text>
                )}
              </View>

              {customerInfo.address && (
                <View style={pdfStyles.customerRight}>
                  <Text style={pdfStyles.customerDetail}>Address:</Text>
                  <Text style={pdfStyles.customerDetail}>
                    {customerInfo.address.street}
                  </Text>
                  <Text style={pdfStyles.customerDetail}>
                    {customerInfo.address.city}, {customerInfo.address.state}{' '}
                    {customerInfo.address.zipCode}
                  </Text>
                  <Text style={pdfStyles.customerDetail}>
                    {customerInfo.address.country}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Line Items Table */}
          <View style={pdfStyles.tableContainer}>
            <Text style={pdfStyles.sectionTitle}>Items</Text>

            {/* Table Header */}
            <View style={pdfStyles.tableHeader}>
              <Text
                style={[pdfStyles.tableHeaderText, pdfStyles.colDescription]}
              >
                Description
              </Text>
              <Text style={[pdfStyles.tableHeaderText, pdfStyles.colQuantity]}>
                Qty
              </Text>
              <Text style={[pdfStyles.tableHeaderText, pdfStyles.colPrice]}>
                Unit Price
              </Text>
              <Text style={[pdfStyles.tableHeaderText, pdfStyles.colTotal]}>
                Total
              </Text>
            </View>

            {/* Table Rows */}
            {data.lineItems.map((item, index) => (
              <View
                key={index}
                style={
                  index % 2 === 0 ? pdfStyles.tableRow : pdfStyles.tableRowAlt
                }
              >
                <Text style={[pdfStyles.tableCell, pdfStyles.colDescription]}>
                  {item.description}
                </Text>
                <Text style={[pdfStyles.tableCell, pdfStyles.colQuantity]}>
                  {item.quantity}
                </Text>
                <Text style={[pdfStyles.tableCell, pdfStyles.colPrice]}>
                  {formatCurrency(item.unitPrice)}
                </Text>
                <Text style={[pdfStyles.tableCellBold, pdfStyles.colTotal]}>
                  {formatCurrency(item.total)}
                </Text>
              </View>
            ))}
          </View>

          {/* Totals Section */}
          <View style={pdfStyles.totalsContainer}>
            <View style={pdfStyles.totalsSection}>
              <View style={pdfStyles.totalRow}>
                <Text style={pdfStyles.totalLabel}>Subtotal:</Text>
                <Text style={pdfStyles.totalValue}>
                  {formatCurrency(data.subtotal)}
                </Text>
              </View>

              {data.tax > 0 && (
                <View style={pdfStyles.totalRow}>
                  <Text style={pdfStyles.totalLabel}>Tax:</Text>
                  <Text style={pdfStyles.totalValue}>
                    {formatCurrency(data.tax)}
                  </Text>
                </View>
              )}

              <View style={pdfStyles.grandTotalRow}>
                <Text style={pdfStyles.grandTotalLabel}>Total Paid:</Text>
                <Text style={pdfStyles.grandTotalValue}>
                  {formatCurrency(data.total)}
                </Text>
              </View>
            </View>
          </View>

          {/* Payment Information */}
          {(data.paymentMethod || data.transactionId) && (
            <View style={pdfStyles.paymentSection}>
              <Text style={pdfStyles.sectionTitle}>Payment Information</Text>
              <View style={pdfStyles.paymentInfo}>
                {data.paymentMethod && (
                  <Text style={pdfStyles.paymentDetail}>
                    Payment Method: {data.paymentMethod}
                  </Text>
                )}
                {data.transactionId && (
                  <Text style={pdfStyles.paymentDetail}>
                    Transaction ID: {data.transactionId}
                  </Text>
                )}
                <Text style={pdfStyles.paymentDetail}>
                  Payment Status: PAID
                </Text>
                <Text style={pdfStyles.paymentDetail}>
                  Payment Date: {data.issueDate}
                </Text>
              </View>
            </View>
          )}

          {/* Notes Section */}
          <View style={pdfStyles.notesSection}>
            <Text style={pdfStyles.notesText}>
              Thank you for your registration for the {CONFERENCE_INFO.name}.
              This receipt confirms your payment and registration. Please keep
              this receipt for your records. If you have any questions, please
              contact us at{' '}
              {IEPA_COMPANY_INFO.supportEmail || IEPA_COMPANY_INFO.email} or{' '}
              {IEPA_COMPANY_INFO.phone}.
              {IEPA_COMPANY_INFO.supportEmail && (
                <>
                  {'\n\n'}
                  Technical support: {IEPA_COMPANY_INFO.supportEmail}
                </>
              )}
            </Text>
          </View>

          {/* Footer */}
          <View style={pdfStyles.footer}>
            <Text style={pdfStyles.footerText}>
              {IEPA_COMPANY_INFO.fullName} • {IEPA_COMPANY_INFO.website}
            </Text>
            <Text style={pdfStyles.footerText}>
              Receipt generated on {new Date().toLocaleDateString()}
            </Text>
          </View>
        </Page>
      </Document>
    );
  } catch (templateError) {
    console.error(
      '[RECEIPT-TEMPLATE] Error rendering template:',
      templateError
    );
    console.error(
      '[RECEIPT-TEMPLATE] Data that caused error:',
      JSON.stringify(data, null, 2)
    );
    throw templateError;
  }
};
