#!/bin/bash

# IEPA Conference Registration - Local Supabase Setup Script
# This script ensures the correct Supabase environment is running for development

set -e

echo "🚀 Setting up IEPA Conference Registration local development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    print_status "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        print_warning "Docker is not running. Starting Docker..."
        open -a Docker
        print_status "Waiting for Docker to start..."
        
        # Wait for Docker to be ready (max 60 seconds)
        for i in {1..60}; do
            if docker info > /dev/null 2>&1; then
                print_success "Docker is now running"
                break
            fi
            sleep 1
            if [ $i -eq 60 ]; then
                print_error "Docker failed to start within 60 seconds"
                exit 1
            fi
        done
    else
        print_success "Docker is already running"
    fi
}

# Check if Supabase CLI is installed
check_supabase_cli() {
    print_status "Checking Supabase CLI..."
    if ! command -v supabase &> /dev/null; then
        print_error "Supabase CLI is not installed"
        print_status "Please install it with: brew install supabase/tap/supabase"
        exit 1
    else
        print_success "Supabase CLI is installed"
    fi
}

# Stop any existing Supabase projects
stop_existing_projects() {
    print_status "Checking for existing Supabase projects..."
    
    # Check if any Supabase containers are running
    if docker ps --format "table {{.Names}}" | grep -q "supabase_"; then
        print_warning "Found running Supabase containers. Stopping them..."
        
        # Try to stop propfirms project specifically (common conflict)
        if docker ps --format "table {{.Names}}" | grep -q "propfirms"; then
            print_status "Stopping propfirms project..."
            supabase stop --project-id propfirms || true
        fi
        
        # Stop any other Supabase projects
        docker ps --format "table {{.Names}}" | grep "supabase_" | while read container; do
            if [[ $container != *"iepa-conf-reg"* ]]; then
                print_status "Stopping container: $container"
                docker stop "$container" || true
            fi
        done
    else
        print_success "No conflicting Supabase projects found"
    fi
}

# Start IEPA Supabase project
start_iepa_supabase() {
    print_status "Starting IEPA Supabase project..."
    
    # Change to project directory
    cd "$(dirname "$0")/.."
    
    # Check if supabase is already initialized
    if [ ! -f "supabase/config.toml" ]; then
        print_error "Supabase not initialized. Please run 'supabase init' first."
        exit 1
    fi
    
    # Start Supabase
    if supabase start; then
        print_success "IEPA Supabase project started successfully!"
    else
        print_error "Failed to start IEPA Supabase project"
        exit 1
    fi
}

# Display connection information
show_connection_info() {
    print_success "🎉 IEPA Conference Registration development environment is ready!"
    echo ""
    echo "📊 Supabase Studio: http://127.0.0.1:54323"
    echo "🔗 API URL: http://127.0.0.1:54321"
    echo "📧 Email Testing: http://127.0.0.1:54324"
    echo "🗄️  Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres"
    echo ""
    echo "🚀 You can now start your Next.js development server:"
    echo "   npm run dev"
    echo ""
    echo "💡 The app will be available at: http://localhost:6969"
}

# Main execution
main() {
    echo "=================================================="
    echo "  IEPA Conference Registration Setup"
    echo "=================================================="
    echo ""
    
    check_docker
    check_supabase_cli
    stop_existing_projects
    start_iepa_supabase
    show_connection_info
    
    echo ""
    print_success "Setup completed successfully! 🎉"
}

# Run main function
main "$@"
