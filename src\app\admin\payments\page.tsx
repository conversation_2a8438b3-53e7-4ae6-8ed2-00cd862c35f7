'use client';

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  FiCreditCard,
  FiRefreshCw,
  FiDownload,
  FiEye,
  FiDollarSign,
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiCalendar,
} from 'react-icons/fi';
import { formatCurrency, formatDate } from '@/lib/pdf-generation/utils';

interface PaymentRecord {
  id: string;
  user_id: string;
  registration_id: string;
  registration_type: 'attendee' | 'speaker' | 'sponsor';
  stripe_payment_intent_id?: string;
  stripe_session_id?: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  payment_method?: string;
  customer_name: string;
  customer_email: string;
  description?: string;
  metadata?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

interface PaymentFilters {
  search: string;
  registrationType: string;
  status: string;
  dateRange: string;
}

export default function PaymentsPage() {
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Modal states for future implementation
  // const [selectedPayment, setSelectedPayment] =
  //   useState<PaymentRecord | null>(null);
  // const [showDetails, setShowDetails] = useState(false);

  const [filters, setFilters] = useState<PaymentFilters>({
    search: '',
    registrationType: 'all',
    status: 'all',
    dateRange: 'all',
  });

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0,
  });

  const [stats, setStats] = useState({
    totalRevenue: 0,
    completedPayments: 0,
    pendingPayments: 0,
    failedPayments: 0,
    refundedPayments: 0,
  });

  // Fetch payments
  const fetchPayments = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('iepa_payments')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.search) {
        query = query.or(
          `customer_name.ilike.%${filters.search}%,customer_email.ilike.%${filters.search}%,stripe_payment_intent_id.ilike.%${filters.search}%`
        );
      }

      if (filters.registrationType && filters.registrationType !== 'all') {
        query = query.eq('registration_type', filters.registrationType);
      }

      if (filters.status && filters.status !== 'all') {
        query = query.eq('status', filters.status);
      }

      if (filters.dateRange && filters.dateRange !== 'all') {
        const now = new Date();
        let startDate: Date;

        switch (filters.dateRange) {
          case 'today':
            startDate = new Date(
              now.getFullYear(),
              now.getMonth(),
              now.getDate()
            );
            break;
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          default:
            startDate = new Date(0);
        }

        query = query.gte('created_at', startDate.toISOString());
      }

      // Apply pagination
      const from = (pagination.page - 1) * pagination.limit;
      const to = from + pagination.limit - 1;
      query = query.range(from, to);

      const { data, error: fetchError, count } = await query;

      if (fetchError) throw fetchError;

      setPayments(data || []);
      setPagination(prev => ({ ...prev, total: count || 0 }));

      // Calculate stats
      await calculateStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch payments');
    } finally {
      setLoading(false);
    }
  };

  // Calculate payment statistics
  const calculateStats = async () => {
    try {
      const { data: allPayments, error } = await supabase
        .from('iepa_payments')
        .select('amount, status');

      if (error) throw error;

      const stats = allPayments?.reduce(
        (acc, payment) => {
          acc.totalRevenue += payment.amount;
          switch (payment.status) {
            case 'completed':
              acc.completedPayments++;
              break;
            case 'pending':
              acc.pendingPayments++;
              break;
            case 'failed':
              acc.failedPayments++;
              break;
            case 'refunded':
              acc.refundedPayments++;
              break;
          }
          return acc;
        },
        {
          totalRevenue: 0,
          completedPayments: 0,
          pendingPayments: 0,
          failedPayments: 0,
          refundedPayments: 0,
        }
      ) || {
        totalRevenue: 0,
        completedPayments: 0,
        pendingPayments: 0,
        failedPayments: 0,
        refundedPayments: 0,
      };

      setStats(stats);
    } catch (err) {
      console.error('Failed to calculate stats:', err);
    }
  };

  useEffect(() => {
    fetchPayments();
  }, [filters, pagination.page, pagination.limit]);

  // Export payments
  const handleExport = async () => {
    try {
      const { data, error: exportError } = await supabase
        .from('iepa_payments')
        .select('*')
        .csv();

      if (exportError) throw exportError;

      const blob = new Blob([data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `payments-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to export payments'
      );
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800">
            <FiCheckCircle className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      case 'pending':
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <FiClock className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        );
      case 'failed':
        return (
          <Badge className="bg-red-100 text-red-800">
            <FiXCircle className="w-3 h-3 mr-1" />
            Failed
          </Badge>
        );
      case 'refunded':
        return (
          <Badge className="bg-gray-100 text-gray-800">
            <FiXCircle className="w-3 h-3 mr-1" />
            Refunded
          </Badge>
        );
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const getRegistrationTypeBadge = (type: string) => {
    switch (type) {
      case 'attendee':
        return <Badge className="bg-blue-100 text-blue-800">Attendee</Badge>;
      case 'speaker':
        return <Badge className="bg-purple-100 text-purple-800">Speaker</Badge>;
      case 'sponsor':
        return <Badge className="bg-yellow-100 text-yellow-800">Sponsor</Badge>;
      case 'sponsor-attendee':
        return <Badge className="bg-green-100 text-green-800">Sponsor Attendee</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <FiCreditCard className="w-6 h-6 mr-3 text-[var(--iepa-primary-blue)]" />
            Payment Management
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Track and manage payment records and transactions
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <Button
            onClick={handleExport}
            variant="outline"
            className="flex items-center"
          >
            <FiDownload className="w-4 h-4 mr-2" />
            Export CSV
          </Button>
          <Button
            onClick={fetchPayments}
            variant="outline"
            className="flex items-center"
          >
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <FiDollarSign className="w-8 h-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">
                  Total Revenue
                </p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatCurrency(stats.totalRevenue)}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <FiCheckCircle className="w-8 h-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Completed</p>
                <p className="text-lg font-semibold text-gray-900">
                  {stats.completedPayments}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <FiClock className="w-8 h-8 text-yellow-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Pending</p>
                <p className="text-lg font-semibold text-gray-900">
                  {stats.pendingPayments}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <FiXCircle className="w-8 h-8 text-red-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Failed</p>
                <p className="text-lg font-semibold text-gray-900">
                  {stats.failedPayments}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <FiXCircle className="w-8 h-8 text-gray-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Refunded</p>
                <p className="text-lg font-semibold text-gray-900">
                  {stats.refundedPayments}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Input
                placeholder="Search payments..."
                value={filters.search}
                onChange={e =>
                  setFilters(prev => ({ ...prev, search: e.target.value }))
                }
                className="w-full"
              />
            </div>
            <Select
              value={filters.registrationType}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, registrationType: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Registration Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="attendee">Attendee</SelectItem>
                <SelectItem value="speaker">Speaker</SelectItem>
                <SelectItem value="sponsor">Sponsor</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.status}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, status: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Payment Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="refunded">Refunded</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.dateRange}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, dateRange: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">Last 7 Days</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Error Display */}
      {error && (
        <Card>
          <CardBody className="p-4">
            <div className="text-red-600 text-sm">{error}</div>
          </CardBody>
        </Card>
      )}

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Payment Records ({pagination.total})</span>
            {loading && (
              <FiRefreshCw className="w-4 h-4 animate-spin text-gray-400" />
            )}
          </CardTitle>
        </CardHeader>
        <CardBody className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Customer</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment ID</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payments.map(payment => (
                  <TableRow key={payment.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {payment.customer_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {payment.customer_email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getRegistrationTypeBadge(payment.registration_type)}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(payment.amount)}
                    </TableCell>
                    <TableCell>{getStatusBadge(payment.status)}</TableCell>
                    <TableCell>
                      <div className="text-sm text-gray-500 font-mono">
                        {payment.stripe_payment_intent_id ? (
                          <span title={payment.stripe_payment_intent_id}>
                            {payment.stripe_payment_intent_id.substring(0, 20)}
                            ...
                          </span>
                        ) : (
                          'N/A'
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <FiCalendar className="w-3 h-3" />
                        <div className="flex flex-col">
                          <span>{new Date(payment.created_at).toLocaleDateString()}</span>
                          <span className="text-gray-400">{new Date(payment.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          // TODO: Implement payment details modal
                          console.log('View payment:', payment);
                        }}
                      >
                        <FiEye className="w-4 h-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-6 py-4 border-t">
              <div className="text-sm text-gray-500">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{' '}
                of {pagination.total} payments
              </div>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    setPagination(prev => ({
                      ...prev,
                      page: Math.max(1, prev.page - 1),
                    }))
                  }
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    setPagination(prev => ({
                      ...prev,
                      page: Math.min(totalPages, prev.page + 1),
                    }))
                  }
                  disabled={pagination.page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
