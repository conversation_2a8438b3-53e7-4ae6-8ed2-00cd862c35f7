# IEPA Annual Meeting Registration System - User Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Getting Started](#getting-started)
3. [User Journey Documentation](#user-journey-documentation)
4. [Registration Types](#registration-types)
5. [Payment Processing](#payment-processing)
6. [User Dashboard](#user-dashboard)
7. [Administrative Features](#administrative-features)
8. [Technical Architecture](#technical-architecture)
9. [Troubleshooting](#troubleshooting)

---

## System Overview

The IEPA Annual Meeting Registration System (reg.iepa.com) is a comprehensive web application designed for the Independent Energy Producers Association's Annual Meeting. The system provides:

- **Multi-type Registration**: Attendee, Speaker, and Sponsor registration workflows
- **Secure Authentication**: Magic link and password-based authentication options
- **Payment Processing**: Integrated Stripe payment system with invoice generation
- **User Management**: Personal dashboards and registration tracking
- **Administrative Tools**: Complete backend management for conference organizers

### Key Features
- ✅ **Magic Link Authentication** - Password-free login experience
- ✅ **Multi-step Registration Forms** - Guided registration process
- ✅ **Automatic Form Persistence** - Save progress automatically
- ✅ **Stripe Payment Integration** - Secure online payments
- ✅ **PDF Invoice Generation** - Automatic receipt and invoice creation
- ✅ **Email Notifications** - Confirmation and welcome emails
- ✅ **Responsive Design** - Mobile-friendly interface
- ✅ **Admin Dashboard** - Complete management interface

---

## Getting Started

### Creating an Account

![Homepage](../screenshots/homepage-landing.png)
*Homepage showing registration options and annual meeting information*

1. **Visit the Homepage**: Navigate to [reg.iepa.com](https://reg.iepa.com)
2. **Click "Create Account"**: Located in the top navigation or main call-to-action areas
3. **Fill Registration Form**: Provide required information:
   - First Name and Last Name
   - Email Address (will be your login)
   - Organization (optional)
   - Password and confirmation
   - Accept Terms and Conditions

![Signup Form](../screenshots/signup-page.png)
*Account creation form with required fields*

### Authentication Options

The system offers two authentication methods:

#### Magic Link Authentication (Recommended)
![Magic Link Auth](../screenshots/magic-link-auth.png)
*Magic link authentication - password-free login*

- Enter your email address
- Receive secure login link via email
- Click link to automatically sign in
- Redirected to your intended destination

#### Password Authentication
![Password Login](../screenshots/password-login.png)
*Traditional password-based login with magic link option*

- Enter email and password
- Optional "Show Password" toggle
- "Forgot Password" recovery option
- Alternative magic link option available

---

## User Journey Documentation

### Step 1: Landing and Navigation
The homepage provides:
- Annual meeting overview and dates (September 15-17, 2025)
- Registration type comparison
- Pricing information
- Sponsorship details
- Speaker information

### Step 2: Authentication
- **New Users**: Create account with basic information
- **Returning Users**: Sign in via magic link or password
- **Authentication Guards**: Protected pages redirect to login with return functionality

### Step 3: Registration Selection
Choose from three registration types:
- **Attendee Registration**: Standard annual meeting participation
- **Speaker Registration**: For presenters and session leaders
- **Sponsor Registration**: Corporate sponsorship packages

### Step 4: Form Completion
- **Multi-step Forms**: Guided process with progress indicators
- **Auto-save**: Form data automatically saved to localStorage
- **Validation**: Real-time field validation and error handling
- **Prefilling**: Returning users get forms prefilled from profile data

### Step 5: Payment Processing
- **Stripe Integration**: Secure payment processing
- **Multiple Payment Methods**: Credit cards, digital wallets
- **Invoice Generation**: Automatic PDF invoices via Stripe
- **Confirmation**: Email confirmations with receipt attachments

### Step 6: Post-Registration
- **Dashboard Access**: View all registrations and payments
- **PDF Downloads**: Access invoices and receipts
- **Golf Add-ons**: Additional tournament registration
- **Profile Management**: Update personal information

---

## Registration Types

### Attendee Registration
**Pricing Structure:**
- IEPA Member: $2,300 (3rd person discount: $2,050)
- Non-IEPA Member: $2,650 (3rd person discount: $2,400)
- Federal/State Government: $2,000
- California Community Choice Association: $2,300
- Day Use - Member: $1,750
- Day Use - Non-Member: $2,100
- Spouse: $500
- Child: $100

**Includes:**
- Two nights lodging
- All annual meeting meals
- Golf tournament option ($200)
- Club rental available ($75)

### Speaker Registration
**Two Options:**
1. **Complimentary Speaker** ($0)
   - One night lodging
   - 3 meals (breakfast, lunch, dinner)
   - One hosted reception
   - Meeting materials

2. **Full Meeting Speaker** ($1,500)
   - Two nights lodging
   - All meals and events
   - Complete conference access

### Sponsor Registration
**Sponsorship Levels:**
- **Bronze**: $5,000 (1 registration included)
- **Silver**: $10,000 (2 registrations included)
- **Gold**: $15,000 (3 registrations included)
- **Platinum**: $20,000 (4 registrations included)
- **Diamond**: $25,000 (5 registrations included)

**Benefits Include:**
- Complimentary registrations
- Website listing and company video
- Printed agenda acknowledgment
- Tent cards at meals
- Meeting materials inclusion

---

## Payment Processing

### Stripe Integration
- **Secure Checkout**: Hosted payment pages
- **Multiple Payment Methods**: Cards, Apple Pay, Google Pay
- **Real-time Processing**: Immediate payment confirmation
- **Webhook Handling**: Automatic status updates

### Invoice and Receipt Generation
- **Automatic PDF Creation**: Generated via Stripe invoices
- **Email Delivery**: Attached to confirmation emails
- **Download Access**: Available in user dashboard
- **Detailed Breakdown**: Itemized costs and taxes

### Payment Status Tracking
- **Pending**: Registration submitted, payment required
- **Completed**: Payment processed successfully
- **Failed**: Payment processing error
- **Cancelled**: User cancelled payment process

---

## User Dashboard

### My Registrations Page
- **Registration Overview**: All current and past registrations
- **Status Tracking**: Payment and confirmation status
- **PDF Downloads**: Access to invoices and receipts
- **Golf Add-ons**: Additional tournament registration
- **Edit Capabilities**: Modify registration details (where applicable)

### Profile Management
- **Personal Information**: Update contact details
- **Organization Data**: Company and job title information
- **Preferences**: Communication and notification settings
- **Historical Data**: Past annual meeting participation

---

## Administrative Features

![Admin Access](../screenshots/admin-access-denied.png)
*Admin area requires proper authentication and permissions*

### Admin Dashboard Access
- **Role-based Access**: Super admin and admin roles
- **Email Verification**: Admin access tied to specific email addresses
- **Secure Authentication**: Protected by Row Level Security (RLS)

### Management Capabilities
- **Attendee Management**: View, edit, and manage all registrations
- **Speaker Administration**: Review applications and manage presentations
- **Sponsor Oversight**: Handle sponsorship packages and benefits
- **Payment Tracking**: Monitor all transactions and payment status
- **Email Management**: Send notifications and manage templates
- **Invoice Generation**: Create and manage billing documents

### Reporting and Analytics
- **Registration Statistics**: Real-time dashboard metrics
- **Revenue Tracking**: Payment and financial reporting
- **Attendee Analytics**: Participation and demographic data
- **Export Capabilities**: Data export for external analysis

---

## Technical Architecture

### Frontend Technology Stack
- **Framework**: Next.js 14 with App Router
- **UI Components**: shadcn/ui component library
- **Styling**: Tailwind CSS with custom IEPA branding
- **State Management**: React Context API
- **Form Handling**: React Hook Form with validation
- **File Uploads**: Integrated file upload system

### Backend Infrastructure
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with magic links
- **API**: Next.js API routes
- **Payment Processing**: Stripe integration
- **Email Service**: SendGrid for transactional emails
- **File Storage**: Supabase Storage for uploads

### Database Schema
**Core Tables:**
- `iepa_user_profiles`: User account information
- `iepa_attendee_registrations`: Annual meeting attendee data
- `iepa_speaker_registrations`: Speaker applications and details
- `iepa_sponsor_registrations`: Sponsorship information
- `iepa_payments`: Payment transaction records
- `iepa_admin_users`: Administrative access control
- `iepa_email_logs`: Email delivery tracking

### Security Features
- **Row Level Security (RLS)**: Database-level access control
- **JWT Authentication**: Secure token-based authentication
- **HTTPS Encryption**: All data transmission encrypted
- **Input Validation**: Comprehensive form and API validation
- **CSRF Protection**: Cross-site request forgery prevention

### Deployment and Hosting
- **Platform**: Vercel for frontend hosting
- **Database**: Supabase cloud infrastructure
- **CDN**: Global content delivery network
- **SSL**: Automatic HTTPS certificate management
- **Monitoring**: Real-time error tracking and performance monitoring

---

## Troubleshooting

### Common Authentication Issues

**Problem**: Magic link not received
- **Solution**: Check spam/junk folder, verify email address spelling
- **Alternative**: Use password login option

**Problem**: Password reset not working
- **Solution**: Ensure email is registered, check spam folder
- **Contact**: Use support contact form for assistance

### Registration Form Issues

**Problem**: Form data lost during completion
- **Solution**: Data is auto-saved to localStorage, refresh page to restore
- **Prevention**: Complete registration in single session when possible

**Problem**: Payment processing errors
- **Solution**: Verify card information, try different payment method
- **Support**: Contact support with error message details

### Technical Support

**Contact Methods:**
- **Support Email**: Available through contact form
- **Phone Support**: Listed on contact page
- **Live Chat**: Available during business hours

**Information to Provide:**
- Registration ID (if available)
- Error messages (exact text)
- Browser and device information
- Steps taken before issue occurred

### Browser Compatibility
- **Recommended**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile**: iOS Safari, Android Chrome
- **JavaScript**: Required for full functionality
- **Cookies**: Must be enabled for authentication

---

## Feature Documentation

### Form Persistence and Auto-Save
The system automatically saves form progress to prevent data loss:

- **Auto-save Frequency**: Every 30 seconds and on field changes
- **Storage Duration**: 7 days in browser localStorage
- **Visual Indicators**: Save status shown with timestamps
- **Data Restoration**: Automatic prompt to restore saved data
- **Manual Clear**: Option to clear saved data and start fresh

### Multi-step Form Navigation
Registration forms use a guided multi-step approach:

- **Progress Indicators**: Visual progress bar showing completion status
- **Step Validation**: Each step validated before proceeding
- **Skip Navigation**: Jump between completed steps
- **Warning Icons**: Incomplete steps marked with warning indicators
- **Scrollspy Navigation**: Desktop sidebar navigation for easy access

### Email System Integration
Comprehensive email notifications throughout the process:

**Registration Confirmation Emails:**
- Sent immediately upon form submission
- Include registration details and next steps
- PDF invoice attached when payment required
- Unique confirmation number provided

**Welcome Emails:**
- Sent after successful payment completion
- Conference details and logistics information
- Important dates and deadlines
- Contact information for questions

**Payment Confirmation:**
- Stripe-generated receipts
- Detailed payment breakdown
- Invoice download links
- Payment method information

### Golf Tournament Add-on
Special golf tournament registration available:

- **Cost**: $200 per participant
- **Club Rental**: Optional $70 for equipment rental
- **Handedness Selection**: Left or right-handed clubs
- **Separate Payment**: Can be added after initial registration
- **Confirmation**: Separate confirmation email sent

### Discount Code System
Automated discount application:

- **Sponsor Discounts**: Automatic 100% discount for sponsor attendees
- **Email Domain Matching**: Automatic application based on email domain
- **Manual Codes**: Support for manual discount code entry
- **Stripe Integration**: Discounts processed through Stripe coupons
- **Usage Tracking**: Complete audit trail of discount usage

---

## Advanced User Features

### Registration Linking
The system supports linked registrations for families and organizations:

**Spouse and Child Registrations:**
- Linked to primary attendee email
- Reduced pricing structure
- Shared accommodation options
- Family meal planning coordination

**Sponsor Attendee Linking:**
- Automatic discount application
- Company-wide registration management
- Bulk registration capabilities
- Centralized billing options

### PDF Generation and Management
Comprehensive document generation system:

**Invoice Generation:**
- Stripe-native PDF invoices preferred
- Custom PDF generation as fallback
- Detailed cost breakdown
- Tax calculations included
- Professional IEPA branding

**Receipt Management:**
- Automatic generation upon payment
- Email delivery with confirmations
- Dashboard download access
- Historical receipt archive

### Mobile Responsiveness
Optimized mobile experience:

- **Touch Targets**: Minimum 44px for accessibility
- **Responsive Design**: Adapts to all screen sizes
- **Mobile Navigation**: Collapsible menu system
- **Form Optimization**: Mobile-friendly input fields
- **Payment Flow**: Mobile-optimized Stripe checkout

---

## System Integrations

### Supabase Database Integration
- **Real-time Updates**: Live data synchronization
- **Row Level Security**: User-specific data access
- **Automatic Backups**: Regular data protection
- **Scalable Infrastructure**: Handles high registration volumes

### Stripe Payment Processing
- **PCI Compliance**: Secure payment handling
- **International Support**: Multiple currencies and payment methods
- **Webhook Processing**: Real-time payment status updates
- **Subscription Management**: Recurring payment capabilities
- **Fraud Protection**: Built-in fraud detection

### Email Service Integration
- **SendGrid Integration**: Reliable email delivery
- **Template Management**: Dynamic email templates
- **Delivery Tracking**: Email open and click tracking
- **Bounce Handling**: Automatic bounce management
- **Unsubscribe Management**: Compliance with email regulations

---

## Data Privacy and Security

### Data Protection Measures
- **Encryption**: All data encrypted in transit and at rest
- **Access Controls**: Role-based access permissions
- **Audit Logging**: Complete activity tracking
- **Data Retention**: Configurable retention policies
- **GDPR Compliance**: European data protection compliance

### User Privacy Rights
- **Data Access**: Users can view all stored personal data
- **Data Correction**: Ability to update personal information
- **Data Deletion**: Account deletion removes personal data
- **Data Portability**: Export personal data on request
- **Consent Management**: Clear consent for data processing

---

## Performance and Reliability

### System Performance
- **Page Load Times**: Optimized for fast loading
- **Database Queries**: Efficient query optimization
- **CDN Integration**: Global content delivery
- **Caching Strategy**: Strategic caching for performance
- **Image Optimization**: Automatic image compression

### Reliability Features
- **Uptime Monitoring**: 24/7 system monitoring
- **Error Tracking**: Real-time error detection
- **Backup Systems**: Multiple backup strategies
- **Failover Protection**: Automatic failover capabilities
- **Load Balancing**: Traffic distribution for stability

---

## Future Enhancements

### Planned Features
- **Multi-language Support**: International attendee support
- **Calendar Integration**: Automatic calendar event creation
- **QR Code Check-in**: Mobile check-in capabilities
- **Networking Features**: Attendee connection tools
- **Mobile App**: Dedicated conference mobile application

### Integration Roadmap
- **CRM Integration**: Customer relationship management
- **Marketing Automation**: Automated marketing campaigns
- **Analytics Dashboard**: Advanced reporting capabilities
- **API Development**: Third-party integration support
- **Accessibility Improvements**: Enhanced accessibility features

---

**Document Information**
**Document Type**: User Documentation
**Last Updated**: December 2024
**Document Version**: 1.0
**System Version**: v0.1.0
**Next Review**: March 2025
**Prepared By**: Technical Team

*This comprehensive documentation covers all aspects of the IEPA Annual Meeting Registration System. For technical support, feature requests, or additional information, please contact the IEPA technical team through the support channels provided on the website.*
