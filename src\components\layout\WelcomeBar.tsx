'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { FiX, FiUser, FiMail, FiCalendar } from 'react-icons/fi';
import { cn } from '@/lib/utils';
import { CONFERENCE_YEAR, CONFERENCE_DATES } from '@/lib/conference-config';

interface WelcomeBarProps {
  className?: string;
  showDismiss?: boolean;
  onDismiss?: () => void;
}

export function WelcomeBar({
  className,
  showDismiss = true,
  onDismiss,
}: WelcomeBarProps) {
  const { user, loading } = useAuth();
  const [isDismissed, setIsDismissed] = useState(false);

  // Don't render if loading or dismissed
  if (loading || isDismissed) {
    return null;
  }

  // Extract user information if authenticated
  const isAuthenticated = !!user;
  const displayName = isAuthenticated
    ? user.user_metadata?.full_name ||
      user.user_metadata?.name ||
      user.email?.split('@')[0] ||
      'User'
    : null;

  const userEmail = isAuthenticated ? user.email : null;

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  return (
    <div
      id="welcome-bar"
      className={cn(
        'w-full bg-iepa-primary border-b border-iepa-primary shadow-sm',
        className
      )}
      style={{
        backgroundColor: 'var(--iepa-primary-blue, #396DA4)',
        borderBottomColor: 'var(--iepa-primary-blue, #396DA4)',
      }}
      role="banner"
      aria-label="Welcome message"
      data-testid="welcome-bar"
    >
      <div className="container mx-auto px-3 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between min-h-[2.5rem] sm:h-12 py-2">
          {/* Welcome Message */}
          <div
            id="welcome-message-section"
            className="flex items-center gap-2 sm:gap-3 text-white min-w-0 flex-1 mr-2"
            data-testid="welcome-message-section"
          >
            {isAuthenticated ? (
              <FiUser
                className="h-4 w-4 sm:h-5 sm:w-5 text-iepa-accent-lighter flex-shrink-0"
                aria-hidden="true"
              />
            ) : (
              <FiCalendar
                className="h-4 w-4 sm:h-5 sm:w-5 text-iepa-accent-lighter flex-shrink-0"
                aria-hidden="true"
              />
            )}
            <div className="flex flex-col sm:flex-row sm:items-center sm:gap-2 min-w-0 flex-1">
              <span
                id="welcome-greeting"
                className="text-sm sm:text-base font-medium leading-tight"
                data-testid="welcome-greeting"
              >
                {isAuthenticated ? (
                  `Welcome back, ${displayName}!`
                ) : (
                  <>
                    {/* Mobile: Shorter text */}
                    <span className="sm:hidden">
                      Welcome to IEPA {CONFERENCE_YEAR} Registration
                    </span>
                    {/* Desktop: Full text */}
                    <span className="hidden sm:inline">
                      Welcome to the IEPA {CONFERENCE_YEAR} Annual Meeting
                      Registration
                    </span>
                  </>
                )}
              </span>
              {isAuthenticated && userEmail ? (
                <div
                  id="user-email-display"
                  className="flex items-center gap-1 text-xs sm:text-sm text-iepa-accent-lighter"
                  data-testid="user-email-display"
                >
                  <FiMail
                    className="h-3 w-3 sm:h-4 sm:w-4"
                    aria-hidden="true"
                  />
                  <span
                    id="user-email-text"
                    className="truncate max-w-[200px] sm:max-w-none"
                    data-testid="user-email-text"
                  >
                    {userEmail}
                  </span>
                </div>
              ) : (
                !isAuthenticated && (
                  <div
                    id="conference-dates-display"
                    className="flex items-center gap-1 text-xs sm:text-sm text-iepa-accent-lighter"
                    data-testid="conference-dates-display"
                  >
                    <span className="truncate">
                      {CONFERENCE_DATES.startDate.displayDate} -{' '}
                      {CONFERENCE_DATES.endDate.displayDate}
                    </span>
                  </div>
                )
              )}
            </div>
          </div>

          {/* Dismiss Button */}
          {showDismiss && (
            <Button
              id="welcome-bar-dismiss"
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="text-white hover:text-iepa-accent-lighter hover:bg-white/10 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-iepa-primary transition-all p-1 sm:p-2 h-8 w-8 sm:h-9 sm:w-9 flex-shrink-0"
              aria-label="Dismiss welcome message"
              data-testid="welcome-bar-dismiss"
            >
              <FiX className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

// Optional: Enhanced version with additional features
interface EnhancedWelcomeBarProps extends WelcomeBarProps {
  showLastLogin?: boolean;
  showQuickActions?: boolean;
}

export function EnhancedWelcomeBar({
  className,
  showDismiss = true,
  showLastLogin = false,
  showQuickActions = false,
  onDismiss,
}: EnhancedWelcomeBarProps) {
  const { user, loading } = useAuth();
  const [isDismissed, setIsDismissed] = useState(false);

  // Don't render if loading or dismissed
  if (loading || isDismissed) {
    return null;
  }

  // Extract user information if authenticated
  const isAuthenticated = !!user;
  const displayName = isAuthenticated
    ? user.user_metadata?.full_name ||
      user.user_metadata?.name ||
      user.email?.split('@')[0] ||
      'User'
    : null;

  const userEmail = isAuthenticated ? user.email : null;
  const lastSignIn =
    isAuthenticated && user.last_sign_in_at
      ? new Date(user.last_sign_in_at)
      : null;

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  return (
    <div
      className={cn(
        'w-full bg-iepa-primary border-b border-iepa-primary shadow-sm',
        className
      )}
      style={{
        backgroundColor: 'var(--iepa-primary-blue, #396DA4)',
        borderBottomColor: 'var(--iepa-primary-blue, #396DA4)',
      }}
      role="banner"
      aria-label="Welcome message with user information"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-2 sm:py-3">
          {/* Welcome Content */}
          <div className="flex items-center gap-3 sm:gap-4 text-white min-w-0 flex-1">
            {isAuthenticated ? (
              <FiUser
                className="h-5 w-5 sm:h-6 sm:w-6 text-iepa-accent-lighter flex-shrink-0"
                aria-hidden="true"
              />
            ) : (
              <FiCalendar
                className="h-5 w-5 sm:h-6 sm:w-6 text-iepa-accent-lighter flex-shrink-0"
                aria-hidden="true"
              />
            )}

            <div className="flex flex-col gap-1 min-w-0 flex-1">
              {/* Main welcome message */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:gap-2">
                <span className="text-sm sm:text-base font-semibold leading-tight">
                  {isAuthenticated ? (
                    `Welcome back, ${displayName}!`
                  ) : (
                    <>
                      {/* Mobile: Shorter text */}
                      <span className="sm:hidden">
                        Welcome to IEPA {CONFERENCE_YEAR} Registration
                      </span>
                      {/* Desktop: Full text */}
                      <span className="hidden sm:inline">
                        Welcome to the IEPA {CONFERENCE_YEAR} Annual Meeting
                        Registration
                      </span>
                    </>
                  )}
                </span>
                {isAuthenticated && userEmail ? (
                  <div className="flex items-center gap-1 text-xs sm:text-sm text-iepa-accent-lighter">
                    <FiMail
                      className="h-3 w-3 sm:h-4 sm:w-4"
                      aria-hidden="true"
                    />
                    <span className="truncate">{userEmail}</span>
                  </div>
                ) : (
                  !isAuthenticated && (
                    <div className="flex items-center gap-1 text-xs sm:text-sm text-iepa-accent-lighter">
                      <span className="truncate">
                        {CONFERENCE_DATES.startDate.displayDate} -{' '}
                        {CONFERENCE_DATES.endDate.displayDate}
                      </span>
                    </div>
                  )
                )}
              </div>

              {/* Additional info */}
              {isAuthenticated && showLastLogin && lastSignIn && (
                <div className="text-xs text-iepa-accent-lighter">
                  Last login: {lastSignIn.toLocaleDateString()} at{' '}
                  {lastSignIn.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          {isAuthenticated && showQuickActions && (
            <div className="hidden sm:flex items-center gap-2 mr-2">
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:text-iepa-accent-lighter hover:bg-white/10 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-iepa-primary transition-all text-xs px-3 py-1"
              >
                Dashboard
              </Button>
            </div>
          )}

          {/* Dismiss Button */}
          {showDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="text-white hover:text-iepa-accent-lighter hover:bg-white/10 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-iepa-primary transition-all p-2 h-9 w-9 flex-shrink-0"
              aria-label="Dismiss welcome message"
            >
              <FiX className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
