#!/usr/bin/env node

/**
 * Webhook Testing Script for IEPA Conference Registration
 *
 * This script tests webhook delivery and processing for both development
 * and production environments.
 */

import Stripe from 'stripe';
import * as dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config({ path: '.env.local' });

const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

if (!stripeSecretKey) {
  console.error('❌ Error: STRIPE_SECRET_KEY not found in .env.local');
  process.exit(1);
}

if (!webhookSecret) {
  console.error('❌ Error: STRIPE_WEBHOOK_SECRET not found in .env.local');
  process.exit(1);
}

// Initialize Stripe client
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-05-28.basil',
  typescript: true,
});

interface TestResult {
  test: string;
  success: boolean;
  message: string;
  details?: any;
}

async function testWebhookEndpoint(url: string): Promise<TestResult> {
  try {
    console.log(`🧪 Testing webhook endpoint: ${url}`);

    // Create a simple test payload
    const testPayload = {
      id: 'evt_test_webhook',
      object: 'event',
      api_version: '2025-05-28.basil',
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: 'cs_test_webhook',
          object: 'checkout.session',
          metadata: {
            registrationId: 'test-registration-' + Date.now(),
            registrationType: 'attendee',
          },
        },
      },
      livemode: false,
      pending_webhooks: 1,
      request: {
        id: 'req_test_webhook',
        idempotency_key: null,
      },
      type: 'checkout.session.completed',
    };

    // Create a test signature (this won't be valid, but tests endpoint accessibility)
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': 'test-signature',
      },
      body: JSON.stringify(testPayload),
    });

    if (response.status === 400) {
      // 400 is expected for invalid signature, which means endpoint is accessible
      return {
        test: 'Webhook Endpoint Accessibility',
        success: true,
        message: 'Endpoint is accessible (signature validation working)',
        details: { status: response.status, statusText: response.statusText },
      };
    } else if (response.status === 200) {
      return {
        test: 'Webhook Endpoint Accessibility',
        success: true,
        message: 'Endpoint is accessible and responding',
        details: { status: response.status, statusText: response.statusText },
      };
    } else {
      return {
        test: 'Webhook Endpoint Accessibility',
        success: false,
        message: `Unexpected response: ${response.status} ${response.statusText}`,
        details: { status: response.status, statusText: response.statusText },
      };
    }
  } catch (error) {
    return {
      test: 'Webhook Endpoint Accessibility',
      success: false,
      message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
    };
  }
}

async function testStripeWebhookConfiguration(): Promise<TestResult[]> {
  const results: TestResult[] = [];

  try {
    console.log('🔍 Testing Stripe webhook configuration...');

    // List webhooks
    const webhooks = await stripe.webhookEndpoints.list({ limit: 10 });

    const iepaWebhooks = webhooks.data.filter(
      webhook =>
        webhook.url.includes('iepa') || webhook.description?.includes('IEPA')
    );

    if (iepaWebhooks.length === 0) {
      results.push({
        test: 'Stripe Webhook Configuration',
        success: false,
        message: 'No IEPA webhooks found in Stripe dashboard',
        details: { totalWebhooks: webhooks.data.length },
      });
    } else {
      results.push({
        test: 'Stripe Webhook Configuration',
        success: true,
        message: `Found ${iepaWebhooks.length} IEPA webhook(s)`,
        details: iepaWebhooks.map(w => ({
          id: w.id,
          url: w.url,
          status: w.status,
        })),
      });

      // Test each webhook endpoint
      for (const webhook of iepaWebhooks) {
        const endpointTest = await testWebhookEndpoint(webhook.url);
        results.push(endpointTest);
      }
    }
  } catch (error) {
    results.push({
      test: 'Stripe Webhook Configuration',
      success: false,
      message: `Error accessing Stripe API: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
    });
  }

  return results;
}

async function testLocalWebhookEndpoint(): Promise<TestResult> {
  const localUrl = 'http://localhost:3000/api/stripe/webhook';
  return await testWebhookEndpoint(localUrl);
}

async function testWebhookSecretConfiguration(): Promise<TestResult> {
  try {
    if (!webhookSecret || !webhookSecret.startsWith('whsec_')) {
      return {
        test: 'Webhook Secret Configuration',
        success: false,
        message:
          'Webhook secret does not have correct format (should start with whsec_)',
        details: { secretPrefix: webhookSecret ? webhookSecret.substring(0, 10) + '...' : 'undefined' },
      };
    }

    return {
      test: 'Webhook Secret Configuration',
      success: true,
      message: 'Webhook secret is properly configured',
      details: { secretPrefix: webhookSecret.substring(0, 10) + '...' },
    };
  } catch (error) {
    return {
      test: 'Webhook Secret Configuration',
      success: false,
      message: `Error validating webhook secret: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

function printResults(results: TestResult[]): void {
  console.log('\n📊 Test Results Summary');
  console.log('========================');

  let passed = 0;
  let failed = 0;

  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    const number = (index + 1).toString().padStart(2, '0');

    console.log(`${number}. ${status} ${result.test}`);
    console.log(`    ${result.message}`);

    if (result.details) {
      console.log(
        `    Details: ${JSON.stringify(result.details, null, 2).replace(/\n/g, '\n    ')}`
      );
    }
    console.log('');

    if (result.success) {
      passed++;
    } else {
      failed++;
    }
  });

  console.log(`📈 Summary: ${passed} passed, ${failed} failed`);

  if (failed === 0) {
    console.log('🎉 All webhook tests passed!');
  } else {
    console.log('⚠️  Some webhook tests failed. Check the details above.');
  }
}

async function main() {
  console.log('🧪 IEPA Conference Registration - Webhook Testing');
  console.log('=================================================');
  console.log('');

  const allResults: TestResult[] = [];

  // Test webhook secret configuration
  console.log('1️⃣ Testing webhook secret configuration...');
  const secretTest = await testWebhookSecretConfiguration();
  allResults.push(secretTest);

  // Test Stripe webhook configuration
  console.log('2️⃣ Testing Stripe webhook configuration...');
  const stripeTests = await testStripeWebhookConfiguration();
  allResults.push(...stripeTests);

  // Test local webhook endpoint
  console.log('3️⃣ Testing local webhook endpoint...');
  const localTest = await testLocalWebhookEndpoint();
  allResults.push(localTest);

  // Print results
  printResults(allResults);

  console.log('\n📋 Next Steps:');
  console.log(
    '1. If local endpoint test failed, make sure your development server is running'
  );
  console.log(
    '2. For development, use: ./scripts/setup-development-webhooks.sh'
  );
  console.log(
    '3. Test webhook events with: stripe trigger checkout.session.completed'
  );
  console.log('4. Monitor webhook logs in Stripe dashboard');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { main as testWebhooks };
