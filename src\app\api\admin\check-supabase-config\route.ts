import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { origin } = new URL(request.url);

    // Get current environment info
    const envInfo = {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
      VERCEL_URL: process.env.VERCEL_URL,
      VERCEL_ENV: process.env.VERCEL_ENV,
      currentOrigin: origin,
      userAgent: request.headers.get('user-agent'),
      host: request.headers.get('host'),
      forwardedHost: request.headers.get('x-forwarded-host'),
      forwardedProto: request.headers.get('x-forwarded-proto'),
    };

    // Determine the correct site URL for Supabase
    let expectedSiteUrl = '';
    if (process.env.NODE_ENV === 'development') {
      expectedSiteUrl = 'http://localhost:6969';
    } else if (process.env.VERCEL_URL) {
      expectedSiteUrl = `https://${process.env.VERCEL_URL}`;
    } else {
      expectedSiteUrl = 'https://iepa.vercel.app';
    }

    // Check if we can determine the correct callback URL
    // const callbackUrl = `${expectedSiteUrl}/auth/callback`;

    const recommendations = [];

    // Check for common issues
    if (process.env.NODE_ENV === 'production' && !process.env.VERCEL_URL) {
      recommendations.push(
        'VERCEL_URL environment variable is missing in production'
      );
    }

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
      recommendations.push('NEXT_PUBLIC_SUPABASE_URL is missing');
    }

    // Provide configuration guidance
    const supabaseConfig = {
      siteUrl: expectedSiteUrl,
      additionalRedirectUrls: [
        'http://localhost:6969/auth/callback',
        'https://iepa.vercel.app/auth/callback',
        ...(process.env.VERCEL_URL
          ? [`https://${process.env.VERCEL_URL}/auth/callback`]
          : []),
      ].filter((url, index, arr) => arr.indexOf(url) === index), // Remove duplicates
    };

    return NextResponse.json({
      success: true,
      environment: envInfo,
      recommendations,
      supabaseConfig,
      instructions: {
        step1:
          'Go to your Supabase Dashboard > Authentication > URL Configuration',
        step2: `Set Site URL to: ${expectedSiteUrl}`,
        step3: 'Add all redirect URLs from the additionalRedirectUrls array',
        step4:
          'CRITICAL: Disable SendGrid click tracking (see sendgridFix below)',
        step5: 'Test magic link authentication after configuration',
      },
      sendgridFix: {
        problem:
          'SendGrid click tracking is wrapping magic link URLs with tracking redirects',
        solution: 'Disable click tracking in SendGrid account settings',
        steps: [
          '1. Login to SendGrid Dashboard (https://app.sendgrid.com/)',
          '2. Navigate to Settings → Tracking',
          '3. DISABLE "Click Tracking" globally',
          '4. Keep "Open Tracking" enabled for analytics',
          '5. Save settings and test magic links',
        ],
        alternative:
          'Switch to Resend.com for authentication emails (no click tracking)',
        verification: 'Magic link URLs should be direct (not ct.sendgrid.net)',
      },
    });
  } catch (error) {
    console.error('Error checking Supabase config:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check configuration',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
