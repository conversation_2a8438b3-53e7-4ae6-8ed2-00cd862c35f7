/**
 * Integration tests for email logs API endpoints
 * Tests the complete flow from API request to database interaction
 */

import { NextRequest } from 'next/server';
import { GET, DELETE } from '../email-logs/route';

// Mock Supabase client
const mockSupabaseClient = {
  from: jest.fn(),
};

const mockQuery = {
  select: jest.fn(),
  eq: jest.fn(),
  or: jest.fn(),
  gte: jest.fn(),
  lt: jest.fn(),
  order: jest.fn(),
  range: jest.fn(),
  delete: jest.fn(),
};

// Chain all query methods to return the mock query object
Object.keys(mockQuery).forEach(method => {
  mockQuery[method as keyof typeof mockQuery] = jest.fn().mockReturnValue(mockQuery);
});

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseClient),
}));

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';

const mockEmailLogs = [
  {
    id: '1',
    recipient_email: '<EMAIL>',
    recipient_name: 'Test User 1',
    sender_email: '<EMAIL>',
    subject: 'Test Email 1',
    email_type: 'registration_confirmation',
    status: 'sent',
    created_at: '2024-01-15T10:00:00Z',
    sent_at: '2024-01-15T10:05:00Z',
  },
  {
    id: '2',
    recipient_email: '<EMAIL>',
    recipient_name: 'Test User 2',
    sender_email: '<EMAIL>',
    subject: 'Test Email 2',
    email_type: 'welcome',
    status: 'failed',
    created_at: '2024-01-15T11:00:00Z',
    error_message: 'SMTP connection failed',
  },
];

describe('Email Logs API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabaseClient.from.mockReturnValue(mockQuery);
  });

  describe('GET /api/admin/email-logs', () => {
    it('fetches email logs with default pagination', async () => {
      // Mock successful database response
      mockQuery.range.mockResolvedValueOnce({
        data: mockEmailLogs,
        error: null,
        count: 2,
      });

      // Mock statistics queries
      const mockStatsResponses = [
        { count: 1 }, // sent
        { count: 1 }, // failed
        { count: 0 }, // pending
        { count: 2 }, // total
      ];

      Promise.all = jest.fn().mockResolvedValue(mockStatsResponses);

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.logs).toHaveLength(2);
      expect(data.statistics.totalSent).toBe(1);
      expect(data.statistics.totalFailed).toBe(1);
      expect(data.pagination.page).toBe(1);
      expect(data.pagination.limit).toBe(50);
    });

    it('applies status filter correctly', async () => {
      mockQuery.range.mockResolvedValueOnce({
        data: [mockEmailLogs[0]], // Only sent emails
        error: null,
        count: 1,
      });

      Promise.all = jest.fn().mockResolvedValue([
        { count: 1 }, { count: 0 }, { count: 0 }, { count: 1 }
      ]);

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs?status=sent');
      const response = await GET(request);
      const data = await response.json();

      expect(mockQuery.eq).toHaveBeenCalledWith('status', 'sent');
      expect(data.logs).toHaveLength(1);
      expect(data.logs[0].status).toBe('sent');
    });

    it('applies email type filter correctly', async () => {
      mockQuery.range.mockResolvedValueOnce({
        data: [mockEmailLogs[0]], // Only registration emails
        error: null,
        count: 1,
      });

      Promise.all = jest.fn().mockResolvedValue([
        { count: 1 }, { count: 0 }, { count: 0 }, { count: 1 }
      ]);

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs?emailType=registration_confirmation');
      const response = await GET(request);

      expect(mockQuery.eq).toHaveBeenCalledWith('email_type', 'registration_confirmation');
    });

    it('applies search filter correctly', async () => {
      mockQuery.range.mockResolvedValueOnce({
        data: mockEmailLogs,
        error: null,
        count: 2,
      });

      Promise.all = jest.fn().mockResolvedValue([
        { count: 2 }, { count: 0 }, { count: 0 }, { count: 2 }
      ]);

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs?search=test');
      const response = await GET(request);

      expect(mockQuery.or).toHaveBeenCalledWith(expect.stringContaining('test'));
    });

    it('applies date range filters correctly', async () => {
      mockQuery.range.mockResolvedValueOnce({
        data: mockEmailLogs,
        error: null,
        count: 2,
      });

      Promise.all = jest.fn().mockResolvedValue([
        { count: 2 }, { count: 0 }, { count: 0 }, { count: 2 }
      ]);

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs?dateFrom=2024-01-01&dateTo=2024-01-31');
      const response = await GET(request);

      expect(mockQuery.gte).toHaveBeenCalledWith('created_at', '2024-01-01');
      expect(mockQuery.lt).toHaveBeenCalledWith('created_at', expect.any(String));
    });

    it('handles pagination correctly', async () => {
      mockQuery.range.mockResolvedValueOnce({
        data: mockEmailLogs,
        error: null,
        count: 100,
      });

      Promise.all = jest.fn().mockResolvedValue([
        { count: 80 }, { count: 20 }, { count: 0 }, { count: 100 }
      ]);

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs?page=2&limit=25');
      const response = await GET(request);
      const data = await response.json();

      expect(mockQuery.range).toHaveBeenCalledWith(25, 49); // (page-1)*limit to page*limit-1
      expect(data.pagination.page).toBe(2);
      expect(data.pagination.limit).toBe(25);
      expect(data.pagination.totalPages).toBe(4);
      expect(data.pagination.hasNextPage).toBe(true);
      expect(data.pagination.hasPreviousPage).toBe(true);
    });

    it('limits page size to maximum allowed', async () => {
      mockQuery.range.mockResolvedValueOnce({
        data: [],
        error: null,
        count: 0,
      });

      Promise.all = jest.fn().mockResolvedValue([
        { count: 0 }, { count: 0 }, { count: 0 }, { count: 0 }
      ]);

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs?limit=200');
      const response = await GET(request);
      const data = await response.json();

      expect(data.pagination.limit).toBe(100); // Should be capped at 100
    });

    it('handles database errors gracefully', async () => {
      mockQuery.range.mockResolvedValueOnce({
        data: null,
        error: { message: 'Database connection failed' },
        count: null,
      });

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Database connection failed');
      expect(data.logs).toEqual([]);
    });

    it('includes proper cache headers', async () => {
      mockQuery.range.mockResolvedValueOnce({
        data: mockEmailLogs,
        error: null,
        count: 2,
      });

      Promise.all = jest.fn().mockResolvedValue([
        { count: 1 }, { count: 1 }, { count: 0 }, { count: 2 }
      ]);

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs');
      const response = await GET(request);

      expect(response.headers.get('Cache-Control')).toBe('private, max-age=60');
      expect(response.headers.get('ETag')).toBeTruthy();
    });
  });

  describe('DELETE /api/admin/email-logs', () => {
    it('deletes email log successfully', async () => {
      mockQuery.delete.mockResolvedValueOnce({
        error: null,
      });

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs?id=test-id');
      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Email log deleted successfully');
      expect(mockQuery.delete).toHaveBeenCalled();
      expect(mockQuery.eq).toHaveBeenCalledWith('id', 'test-id');
    });

    it('returns error when ID is missing', async () => {
      const request = new NextRequest('http://localhost:3000/api/admin/email-logs');
      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Log ID is required');
    });

    it('handles database deletion errors', async () => {
      mockQuery.delete.mockResolvedValueOnce({
        error: { message: 'Record not found' },
      });

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs?id=nonexistent-id');
      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Record not found');
    });
  });

  describe('Error Handling', () => {
    it('handles missing environment variables', async () => {
      // Temporarily remove environment variables
      const originalUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const originalKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
      
      delete process.env.NEXT_PUBLIC_SUPABASE_URL;
      delete process.env.SUPABASE_SERVICE_ROLE_KEY;

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toContain('configuration');

      // Restore environment variables
      process.env.NEXT_PUBLIC_SUPABASE_URL = originalUrl;
      process.env.SUPABASE_SERVICE_ROLE_KEY = originalKey;
    });

    it('handles unexpected errors gracefully', async () => {
      // Mock an unexpected error
      mockSupabaseClient.from.mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      const request = new NextRequest('http://localhost:3000/api/admin/email-logs');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unexpected error');
    });
  });
});
