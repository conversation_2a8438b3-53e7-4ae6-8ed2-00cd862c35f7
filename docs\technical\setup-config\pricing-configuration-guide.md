# IEPA 2025 Conference Pricing Configuration Guide

## Overview

This guide explains how to use and maintain the centralized pricing configuration system for the IEPA 2025 Conference Registration Application.

## Configuration Files

### Primary Configuration: `src/lib/pricing-config.ts`

This is the single source of truth for all conference pricing, registration fees, and sponsorship packages.

#### Key Components:

1. **REGISTRATION_PRICING**: All registration types with base prices and group discounts
2. **ADDITIONAL_OPTIONS**: Golf tournament, spouse/child registrations, and add-ons
3. **SPONSORSHIP_PACKAGES**: All sponsorship levels with pricing and benefits
4. **SPEAKER_PRICING**: Speaker registration options
5. **pricingUtils**: Utility functions for price calculations and formatting

### Utility Functions: `src/utils/schema-utils.ts`

Extended with pricing helper functions for forms and validation.

## Current 2025 Pricing Structure

### Registration Fees (2025 Rates)

| Registration Type             | Base Price | Group Discount | Notes                      |
| ----------------------------- | ---------- | -------------- | -------------------------- |
| **IEPA Member**               | $2,369     | $2,112         | 3rd person saves $250      |
| **Non-IEPA Member**           | $2,730     | $2,472         | 3rd person saves $250      |
| **CCA Member**                | $2,369     | N/A            | Same as IEPA member rate   |
| **Fed/State Government**      | $2,060     | N/A            | Discounted government rate |
| **Day Use - IEPA Member**     | $1,803     | N/A            | No lodging included        |
| **Day Use - Non-IEPA Member** | $2,163     | N/A            | No lodging included        |

### Additional Options

| Option                  | Price | Notes                   |
| ----------------------- | ----- | ----------------------- |
| **Golf Tournament**     | $200  | Consistent across years |
| **Spouse Registration** | $515  | Additional attendee     |
| **Child Registration**  | $103  | Child attendee          |

### Sponsorship Packages

| Level        | Price   | Included Registrations | Benefits                                 |
| ------------ | ------- | ---------------------- | ---------------------------------------- |
| **Bronze**   | $5,150  | 1                      | Basic sponsorship benefits               |
| **Silver**   | $10,300 | 2                      | Enhanced logo placement                  |
| **Gold**     | $15,450 | 3                      | Premium placement + ceremony recognition |
| **Platinum** | $20,600 | 4                      | Speaking opportunity                     |
| **Diamond**  | $25,750 | 5                      | Keynote opportunity + booth space        |

### Speaker Options

| Type              | Price  | Inclusions                       |
| ----------------- | ------ | -------------------------------- |
| **Complimentary** | $0     | 1 night, 3 meals, limited access |
| **Full Meeting**  | $1,500 | 2 nights, all meals, full access |

## How to Update Pricing

### Step 1: Update Base Pricing

Edit `src/lib/pricing-config.ts`:

```typescript
export const REGISTRATION_PRICING: RegistrationPrice[] = [
  {
    id: 'iepa-member',
    name: 'IEPA Member',
    displayName: 'IEPA Member Registration',
    basePrice: 2369, // Update this amount
    groupDiscountPrice: 2112, // Update group discount
    description: 'Full conference registration for IEPA members',
    inclusions: [...],
  },
  // ... update other registration types
];
```

### Step 2: Update Additional Options

```typescript
export const ADDITIONAL_OPTIONS: AdditionalOption[] = [
  {
    id: 'golf-tournament',
    name: 'Golf Tournament',
    price: 200, // Update golf price if needed
    description: 'Annual IEPA Golf Tournament at Lake Tahoe Golf Course',
    category: 'golf',
  },
  // ... update other options
];
```

### Step 3: Update Sponsorship Packages

```typescript
export const SPONSORSHIP_PACKAGES: SponsorshipPackage[] = [
  {
    id: 'bronze-sponsor',
    name: 'Bronze Sponsor',
    price: 5150, // Update sponsorship price
    includedRegistrations: 1,
    level: 'bronze',
    benefits: [...],
    marketingBenefits: [...],
  },
  // ... update other packages
];
```

### Step 4: Update Metadata

```typescript
export const PRICING_METADATA = {
  lastUpdated: '2025-01-29', // Update to current date
  version: '1.1.0', // Increment version
  baseYear: 2024,
  inflationAdjustment: '3%', // Update adjustment rate
  source: 'https://iepa.com/annual-meeting/',
  notes: 'Updated pricing for 2025 conference',
};
```

## Using Pricing Configuration in Components

### Import the Configuration

```typescript
import { pricingUtils, REGISTRATION_PRICING } from '@/lib/pricing-config';
```

### Get Registration Options for Forms

```typescript
const registrationOptions = pricingUtils.getRegistrationOptions();
// Returns array of registration objects with formatted prices
```

### Format Prices for Display

```typescript
const formattedPrice = pricingUtils.formatPrice(2369);
// Returns: "$2,369"
```

### Calculate Registration Totals

```typescript
const total = pricingUtils.calculateRegistrationTotal(
  'iepa-member',
  true, // include golf
  ['spouse-registration'], // additional options
  false // not group discount
);
// Returns: total amount
```

### Get Sponsorship Options

```typescript
const sponsorshipOptions = pricingUtils.getSponsorshipOptions();
// Returns formatted sponsorship packages for forms
```

## Using Pricing Helpers in Forms

### Import Pricing Helpers

```typescript
import { pricingHelpers } from '@/utils/schema-utils';
```

### Get Schema Options

```typescript
const registrationSchemaOptions = pricingHelpers.getRegistrationSchemaOptions();
// Returns: { enum: [...], enumNames: [...], descriptions: [...] }
```

### Calculate Form Totals

```typescript
const totals = pricingHelpers.calculateFormTotal(
  'iepa-member',
  true, // include golf
  ['spouse-registration']
);
// Returns: { registrationTotal, golfTotal, grandTotal, formatted versions }
```

### Validate Pricing Selections

```typescript
const validation = pricingHelpers.validatePricingSelections({
  registrationType: 'iepa-member',
  includeGolf: true,
});
// Returns: { isValid: boolean, errors: string[] }
```

## Form Integration Examples

### Registration Type Selection

```tsx
import { pricingUtils } from '@/lib/pricing-config';

const RegistrationTypeSelect = () => {
  const options = pricingUtils.getRegistrationOptions();

  return (
    <Select>
      {options.map(option => (
        <SelectItem key={option.id} value={option.id}>
          {option.label} - {option.formattedPrice}
        </SelectItem>
      ))}
    </Select>
  );
};
```

### Price Calculation Display

```tsx
import { pricingHelpers } from '@/utils/schema-utils';

const PriceCalculator = ({ registrationType, includeGolf }) => {
  const totals = pricingHelpers.calculateFormTotal(
    registrationType,
    includeGolf
  );

  return (
    <div>
      <p>Registration: {totals.formattedRegistrationTotal}</p>
      <p>Golf: {totals.formattedGolfTotal}</p>
      <p>Total: {totals.formattedGrandTotal}</p>
    </div>
  );
};
```

### Sponsorship Package Display

```tsx
import { pricingUtils } from '@/lib/pricing-config';

const SponsorshipOptions = () => {
  const packages = pricingUtils.getSponsorshipOptions();

  return (
    <div>
      {packages.map(pkg => (
        <div key={pkg.id}>
          <h3>{pkg.label}</h3>
          <p>{pkg.formattedPrice}</p>
          <p>{pkg.description}</p>
        </div>
      ))}
    </div>
  );
};
```

## Pricing Logic and Rules

### Group Discounts

- **Eligibility**: 3rd person and beyond in same registration type
- **Discount Amount**: $250 off regular price
- **Applicable To**: IEPA Member and Non-IEPA Member only

### Government Pricing

- **Fed/State Rate**: Special discounted rate for government employees
- **Verification**: May require proof of employment
- **No Group Discount**: Government rate is already discounted

### Day Use Pricing

- **Percentage**: Approximately 75-80% of full registration
- **Inclusions**: Meals and sessions for selected days only
- **No Lodging**: Attendees arrange their own accommodation

### Golf Tournament

- **Consistent Pricing**: $200 across all years
- **Advance Registration**: Required through registration form
- **Separate Fee**: Added to registration total

### Sponsorship Value

- **Cost Per Registration**: Sponsorship price ÷ included registrations
- **Additional Benefits**: Marketing exposure, networking opportunities
- **ROI Calculation**: Consider marketing value beyond registration savings

## Validation and Testing

### Pricing Validation

```typescript
import { pricingUtils } from '@/lib/pricing-config';

// Validate pricing configuration
const validation = pricingUtils.validatePricing();
if (!validation.isValid) {
  console.error('Pricing errors:', validation.errors);
}
```

### Test Calculations

```typescript
// Test registration calculation
const testTotal = pricingUtils.calculateRegistrationTotal('iepa-member', true, [
  'spouse-registration',
]);
console.log('Test total:', pricingUtils.formatPrice(testTotal));
```

## Best Practices

### 1. Always Use Centralized Configuration

- Never hardcode prices in components
- Always import from `pricing-config.ts`
- Use utility functions for consistent formatting

### 2. Update All Related Files

When changing prices, update:

- Main pricing configuration file
- Documentation
- Test data (if applicable)
- Version metadata

### 3. Test After Updates

- Verify all components display correct prices
- Test form calculations
- Check that totals calculate properly
- Validate sponsorship package logic

### 4. Maintain Consistency

- Use consistent currency formatting
- Apply inflation adjustments uniformly
- Keep group discount logic consistent
- Document all pricing rules

## Troubleshooting

### Common Issues

1. **Prices not updating in components**

   - Check that components are importing from `pricing-config.ts`
   - Verify the import path is correct
   - Restart development server after config changes

2. **Calculation errors**

   - Check that registration type IDs match exactly
   - Verify additional option IDs are correct
   - Ensure pricing validation passes

3. **Form display issues**
   - Check that pricing helpers are being used correctly
   - Verify schema options are properly formatted
   - Test with different registration types

### Getting Help

If you encounter issues with the pricing configuration system:

1. Check this documentation first
2. Review the configuration files for syntax errors
3. Check browser console for JavaScript errors
4. Verify all imports are correct
5. Test pricing validation function

## Future Enhancements

Planned improvements to the pricing configuration system:

1. **Early Bird Pricing**: Time-based pricing discounts
2. **Dynamic Pricing**: Load prices from API or database
3. **Multi-Currency**: Support for international attendees
4. **Volume Discounts**: Additional group pricing tiers
5. **Promotional Codes**: Discount code system
