'use client';

import React from 'react';
import { <PERSON>, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { cn } from '@/lib/utils';
import {
  GraduationCap,
  Users,
  Target,
} from 'lucide-react';

interface HighlightItem {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const highlights: HighlightItem[] = [
  {
    id: 'educational-sessions',
    title: 'Educational Sessions',
    description:
      'Attend technical sessions, workshops, and presentations covering the latest developments in environmental science and policy.',
    icon: GraduationCap,
  },
  {
    id: 'networking',
    title: 'Networking',
    description:
      'Connect with peers, build professional relationships, and expand your network through structured networking events.',
    icon: Users,
  },
  {
    id: 'golf-tournament',
    title: 'Golf Tournament',
    description:
      'Enjoy a relaxing round of golf with colleagues and new connections at our annual golf tournament.',
    icon: Target,
  },
];

interface ConferenceHighlightsProps {
  className?: string;
  showTitle?: boolean;
  title?: string;
}

export function ConferenceHighlights({
  className,
  showTitle = true,
  title = 'Annual Meeting Highlights',
}: ConferenceHighlightsProps) {
  return (
    <section
      id="conference-highlights"
      className={cn(
        'iepa-section',
        className
      )}
      style={{ backgroundColor: 'var(--iepa-gray-50)' }}
      data-testid="conference-highlights"
    >
      <div className="max-w-6xl mx-auto">
        {showTitle && (
          <h2 className="iepa-heading-2 text-center mb-12">
            {title}
          </h2>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {highlights.map((highlight) => (
            <HighlightCard
              key={highlight.id}
              highlight={highlight}
            />
          ))}
        </div>
      </div>
    </section>
  );
}

interface HighlightCardProps {
  highlight: HighlightItem;
  className?: string;
}

function HighlightCard({ highlight, className }: HighlightCardProps) {
  const IconComponent = highlight.icon;

  return (
    <Card
      id={`highlight-${highlight.id}`}
      className={cn(
        'text-center transition-all duration-300 hover:shadow-lg hover:scale-[1.02]',
        'border-gray-200 hover:border-[var(--iepa-primary-blue)]/20',
        'bg-white/80 backdrop-blur-sm',
        className
      )}
      data-testid={`highlight-card-${highlight.id}`}
    >
      <CardHeader className="pb-4">
        <div className="flex justify-center mb-4">
          <div
            className={cn(
              'w-16 h-16 rounded-full flex items-center justify-center',
              'bg-gradient-to-br from-[var(--iepa-primary-blue)] to-[var(--iepa-secondary-green)]',
              'shadow-lg'
            )}
          >
            <IconComponent
              className="w-8 h-8 text-white"
              aria-hidden="true"
            />
          </div>
        </div>
        <CardTitle className="iepa-heading-3 text-center">
          {highlight.title}
        </CardTitle>
      </CardHeader>
      <CardBody className="pt-0">
        <p className="iepa-body text-center text-[var(--iepa-gray-600)]">
          {highlight.description}
        </p>
      </CardBody>
    </Card>
  );
}

// Export individual components for flexibility
export { HighlightCard };
export type { HighlightItem, ConferenceHighlightsProps };
