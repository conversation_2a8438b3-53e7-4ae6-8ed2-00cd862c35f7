'use client';

import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { FiStar, FiCheck, FiInfo } from 'react-icons/fi';

interface SponsorDiscountDetectorProps {
  email: string;
  attendeeId?: string;
  onDiscountDetected?: (discount: SponsorDiscountInfo) => void;
  onDiscountCleared?: () => void;
  className?: string;
}

interface SponsorDiscountInfo {
  isEligible: boolean;
  sponsorName?: string;
  discountCode?: string;
  discountAmount?: number;
  message?: string;
}

export default function SponsorDiscountDetector({
  email,
  attendeeId,
  onDiscountDetected,
  onDiscountCleared,
  className = '',
}: SponsorDiscountDetectorProps) {
  const [discountInfo, setDiscountInfo] = useState<SponsorDiscountInfo | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheckedEmail, setLastCheckedEmail] = useState('');

  // Check for sponsor discount when email changes
  useEffect(() => {
    const checkSponsorDiscount = async () => {
      // Only check if email is valid and different from last checked
      if (!email || !email.includes('@') || email === lastCheckedEmail) {
        return;
      }

      setIsChecking(true);
      setLastCheckedEmail(email);

      try {
        const response = await fetch('/api/sponsor-discount/check', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            attendeeId,
          }),
        });

        const result = await response.json();

        if (result.success) {
          const discountData: SponsorDiscountInfo = {
            isEligible: result.isEligible,
            sponsorName: result.sponsorName,
            discountCode: result.discountCode,
            discountAmount: result.discountAmount,
            message: result.message,
          };

          setDiscountInfo(discountData);

          // Notify parent component
          if (result.isEligible && onDiscountDetected) {
            onDiscountDetected(discountData);
          } else if (!result.isEligible && onDiscountCleared) {
            onDiscountCleared();
          }
        } else {
          console.error('Error checking sponsor discount:', result.error);
          setDiscountInfo(null);
          if (onDiscountCleared) {
            onDiscountCleared();
          }
        }
      } catch (error) {
        console.error('Error checking sponsor discount:', error);
        setDiscountInfo(null);
        if (onDiscountCleared) {
          onDiscountCleared();
        }
      } finally {
        setIsChecking(false);
      }
    };

    // Debounce the check to avoid too many API calls
    const timeoutId = setTimeout(checkSponsorDiscount, 500);

    return () => clearTimeout(timeoutId);
  }, [email, attendeeId, lastCheckedEmail, onDiscountDetected, onDiscountCleared]);

  // Clear discount info when email is cleared
  useEffect(() => {
    if (!email) {
      setDiscountInfo(null);
      setLastCheckedEmail('');
      if (onDiscountCleared) {
        onDiscountCleared();
      }
    }
  }, [email, onDiscountCleared]);

  // Don't render anything if no email or still checking
  if (!email || isChecking) {
    return null;
  }

  // Don't render if no discount info
  if (!discountInfo) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {discountInfo.isEligible ? (
        <Alert className="border-green-200 bg-green-50">
          <FiStar className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium mb-1">
                  🎉 Sponsor Discount Available!
                </div>
                <div className="text-sm">
                  {discountInfo.message}
                </div>
                {discountInfo.discountAmount === 100 && (
                  <div className="text-sm font-medium mt-1">
                    Your registration is complimentary as a sponsor attendee.
                  </div>
                )}
              </div>
              <div className="flex flex-col items-end space-y-1">
                <Badge className="bg-green-100 text-green-800 border-green-300">
                  <FiCheck className="w-3 h-3 mr-1" />
                  Sponsor Attendee
                </Badge>
                {discountInfo.discountAmount && (
                  <Badge variant="outline" className="text-green-700 border-green-300">
                    {discountInfo.discountAmount}% Off
                  </Badge>
                )}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      ) : (
        <Alert className="border-blue-200 bg-blue-50">
          <FiInfo className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            <div className="text-sm">
              {discountInfo.message || 'No sponsor discount available for this email domain.'}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Sponsor Information Display */}
      {discountInfo.isEligible && discountInfo.sponsorName && (
        <div className="bg-white border border-green-200 rounded-lg p-3">
          <div className="text-sm text-gray-600 mb-1">Sponsored by:</div>
          <div className="font-medium text-gray-900">{discountInfo.sponsorName}</div>
          {discountInfo.discountCode && (
            <div className="text-xs text-gray-500 mt-1">
              Discount Code: <code className="bg-gray-100 px-1 rounded">{discountInfo.discountCode}</code>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// Export types for use in other components
export type { SponsorDiscountInfo };
