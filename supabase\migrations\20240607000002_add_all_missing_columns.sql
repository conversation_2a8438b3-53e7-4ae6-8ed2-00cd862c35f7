-- IEPA Annual Meeting Registration - Add All Missing Columns
-- This migration adds all columns found in production that are missing in local

-- Add missing columns to iepa_user_profiles
ALTER TABLE iepa_user_profiles 
ADD COLUMN IF NOT EXISTS preferred_name_on_badge TEXT;

-- Add missing columns to iepa_organizations
ALTER TABLE iepa_organizations 
ADD COLUMN IF NOT EXISTS normalized_name TEXT;

-- Add missing columns to iepa_historical_registrations
ALTER TABLE iepa_historical_registrations 
ADD COLUMN IF NOT EXISTS golf_tournament BOOLEAN DEFAULT FALSE;

-- Update the full_name columns to be regular columns instead of generated
-- This allows us to import data with explicit full_name values

-- For iepa_attendee_registrations
ALTER TABLE iepa_attendee_registrations 
DROP COLUMN IF EXISTS full_name CASCADE;

ALTER TABLE iepa_attendee_registrations 
ADD COLUMN full_name TEXT;

-- For iepa_speaker_registrations  
ALTER TABLE iepa_speaker_registrations 
DROP COLUMN IF EXISTS full_name CASCADE;

ALTER TABLE iepa_speaker_registrations 
ADD COLUMN full_name TEXT;

-- <PERSON><PERSON> triggers to auto-populate full_name when not provided
CREATE OR REPLACE FUNCTION auto_populate_full_name()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update full_name if it's null or empty
    IF NEW.full_name IS NULL OR NEW.full_name = '' THEN
        NEW.full_name = COALESCE(NEW.first_name, '') || ' ' || COALESCE(NEW.last_name, '');
        NEW.full_name = TRIM(NEW.full_name);
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
DROP TRIGGER IF EXISTS auto_populate_attendee_full_name ON iepa_attendee_registrations;
CREATE TRIGGER auto_populate_attendee_full_name
    BEFORE INSERT OR UPDATE ON iepa_attendee_registrations
    FOR EACH ROW EXECUTE FUNCTION auto_populate_full_name();

DROP TRIGGER IF EXISTS auto_populate_speaker_full_name ON iepa_speaker_registrations;
CREATE TRIGGER auto_populate_speaker_full_name
    BEFORE INSERT OR UPDATE ON iepa_speaker_registrations
    FOR EACH ROW EXECUTE FUNCTION auto_populate_full_name();
