import { chromium } from '@playwright/test';

async function globalSetup() {
  console.log('🚀 Starting global setup for IEPA Email Center E2E tests...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto('http://localhost:6969/', {
      waitUntil: 'networkidle',
      timeout: 60000,
    });

    // Check if the application is responding
    await page.waitForSelector('h1, h2, [data-testid="main-heading"]', {
      timeout: 30000,
    });
    console.log('✅ Application is ready');

    // Setup test data if needed
    await setupTestData();

    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupTestData() {
  try {
    console.log('📝 Setting up test data...');

    // Basic test data setup - just verify the app is working

    console.log('✅ Test data setup completed');
  } catch (error) {
    console.log(
      '⚠️ Test data setup failed, continuing with existing data:',
      (error as Error).message
    );
  }
}

export default globalSetup;
