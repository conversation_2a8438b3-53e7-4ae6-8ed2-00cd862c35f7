# Materialize Components Reference for IEPA Registration Forms

## Priority Components for IEPA Conference Registration

### 1. Form Elements - High Priority

#### Radio Button Components

**Use Case**: Registration type selection (At<PERSON><PERSON>, Speaker, Sponsor)

**Features**:

- RadioGroup wrapper for proper accessibility
- Controlled and uncontrolled variants
- Custom colors (Primary, Secondary, Success, Error, Warning, Info)
- Multiple sizes (Small, Medium)
- Custom styling with styled hook
- Label placement options (Top, Bottom, Start, End)
- Error state handling
- Standalone radio buttons without wrapper

**Implementation Pattern**:

```tsx
<RadioGroup value={value} onChange={handleChange}>
  <FormControlLabel value="attendee" control={<Radio />} label="Attendee" />
  <FormControlLabel value="speaker" control={<Radio />} label="Speaker" />
  <FormControlLabel value="sponsor" control={<Radio />} label="Sponsor" />
</RadioGroup>
```

**IEPA Application**: Perfect for registration type cards with custom styling

#### File Uploader (React Dropzone)

**Use Case**: Speaker presentation uploads

**Features**:

- Multiple file upload support
- Single file upload mode
- File type restrictions (_.pdf, _.ppt, _.pptx, _.doc, \*.docx)
- File size limitations
- Drag and drop interface
- Browse button fallback
- Custom styling with AppReactDropzone wrapper

**Implementation Requirements**:

- Must wrap with `AppReactDropzone` component
- Single instance: wrap individual component
- Multiple instances: wrap entire page

**IEPA Application**: Speaker presentation file uploads with PDF/PPT restrictions

#### Text Field Components

**Use Case**: Personal information forms

**Features**:

- Multiple variants (outlined, filled, standard)
- Input validation states
- Helper text support
- Required field indicators
- Custom styling options
- Responsive design

**IEPA Application**: Name, email, organization, phone number fields

#### Checkbox Components

**Use Case**: Meal selections, add-on services

**Features**:

- Individual checkboxes
- Checkbox groups
- Custom colors and sizes
- Indeterminate state
- Label placement options
- Error state handling

**IEPA Application**: Conference meals, golf tournament, networking events

#### Select Components

**Use Case**: Dropdown selections

**Features**:

- Single and multiple selection
- Search functionality
- Custom options rendering
- Placeholder support
- Error states

**IEPA Application**: Dietary restrictions, t-shirt sizes, accessibility needs

### 2. Layout Components - High Priority

#### Card Components

**Use Case**: Registration option displays

**Features**:

- Elevation and shadow system
- Custom content areas
- Action buttons integration
- Responsive design
- Hover states

**IEPA Application**: Registration type selection cards with pricing

#### Progress Components

**Use Case**: Multi-step form progress

**Features**:

- Linear progress bars
- Circular progress indicators
- Determinate and indeterminate states
- Custom colors
- Step indicators

**IEPA Application**: Form completion progress, step navigation

#### Button Components

**Use Case**: Form navigation and actions

**Features**:

- Multiple variants (contained, outlined, text)
- Icon integration (startIcon, endIcon)
- Custom colors and sizes
- Loading states
- Disabled states
- Floating Action Buttons (FAB)

**IEPA Application**: Next/Previous navigation, Submit buttons, Add to cart

### 3. Feedback Components - Medium Priority

#### Alert Components

**Use Case**: Form validation messages

**Features**:

- Multiple severity levels (success, error, warning, info)
- Dismissible alerts
- Action buttons
- Custom icons
- Responsive design

**IEPA Application**: Form validation feedback, success confirmations

#### Snackbar/Toast Components

**Use Case**: User feedback

**Features**:

- Temporary message display
- Position control
- Auto-hide functionality
- Action buttons
- Queue management

**IEPA Application**: Registration success, error notifications

#### Dialog Components

**Use Case**: Confirmation modals

**Features**:

- Modal dialogs
- Confirmation dialogs
- Custom content
- Backdrop control
- Responsive design

**IEPA Application**: Registration confirmation, cancellation warnings

### 4. Navigation Components - Medium Priority

#### Tabs Components

**Use Case**: Form section organization

**Features**:

- Horizontal and vertical tabs
- Scrollable tabs
- Icon integration
- Custom styling
- Controlled navigation

**IEPA Application**: Registration sections (Personal Info, Preferences, Payment)

#### Breadcrumb Components

**Use Case**: Form step navigation

**Features**:

- Step-by-step navigation
- Custom separators
- Active state indicators
- Click navigation
- Responsive design

**IEPA Application**: Multi-step form navigation

### 5. Data Display Components - Lower Priority

#### Timeline Components

**Use Case**: Conference schedule display

**Features**:

- Chronological data display
- Custom content areas
- Icon integration
- Responsive design

**IEPA Application**: Conference agenda, speaker schedule

#### Badge Components

**Use Case**: Status indicators

**Features**:

- Notification badges
- Status indicators
- Custom colors
- Positioning options

**IEPA Application**: Pricing indicators, status badges

#### Chip Components

**Use Case**: Selected options display

**Features**:

- Tag-like elements
- Deletable chips
- Custom colors
- Icon integration

**IEPA Application**: Selected meal options, chosen add-ons

## Integration Strategy for IEPA Project

### Phase 1: Core Form Components

1. **Radio Button Cards**: Enhance registration type selection
2. **File Uploader**: Implement speaker presentation uploads
3. **Enhanced Text Fields**: Improve form input styling
4. **Progress Indicators**: Add form completion feedback

### Phase 2: Layout Enhancements

1. **Card Components**: Upgrade registration option displays
2. **Button Styling**: Enhance form navigation buttons
3. **Alert System**: Improve validation feedback

### Phase 3: Advanced Features

1. **Dialog Modals**: Add confirmation dialogs
2. **Toast Notifications**: Implement user feedback system
3. **Tab Navigation**: Organize complex forms

## Customization for IEPA Branding

### Theme Integration

- Adapt Material-UI theme to IEPA colors
- Customize component variants for brand consistency
- Implement responsive design patterns
- Maintain accessibility standards

### Component Mapping

- Map Materialize components to current shadcn/ui setup
- Create hybrid component library
- Maintain existing functionality while enhancing UI

---

_Component reference compiled for IEPA Conference Registration enhancement_
_Focus on form-centric components with high applicability to current project needs_
