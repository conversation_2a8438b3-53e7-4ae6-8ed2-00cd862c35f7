-- RLS Policies for IEPA Annual Meeting Registration System

-- RLS Policies for iepa_user_profiles
CREATE POLICY "Users can view their own profile"
    ON iepa_user_profiles FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile"
    ON iepa_user_profiles FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile"
    ON iepa_user_profiles FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_historical_registrations
CREATE POLICY "Users can view their own historical registrations"
    ON iepa_historical_registrations FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own historical registrations"
    ON iepa_historical_registrations FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own historical registrations"
    ON iepa_historical_registrations FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_attendee_registrations
CREATE POLICY "Users can view their own attendee registrations"
    ON iepa_attendee_registrations FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own attendee registrations"
    ON iepa_attendee_registrations FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own attendee registrations"
    ON iepa_attendee_registrations FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_speaker_registrations
CREATE POLICY "Users can view their own speaker registrations"
    ON iepa_speaker_registrations FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own speaker registrations"
    ON iepa_speaker_registrations FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own speaker registrations"
    ON iepa_speaker_registrations FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_sponsor_registrations
CREATE POLICY "Users can view their own sponsor registrations"
    ON iepa_sponsor_registrations FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sponsor registrations"
    ON iepa_sponsor_registrations FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sponsor registrations"
    ON iepa_sponsor_registrations FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_payments
CREATE POLICY "Users can view their own payments"
    ON iepa_payments FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own payments"
    ON iepa_payments FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- RLS Policies for iepa_admin_users
CREATE POLICY "Admin users can view all admin records"
    ON iepa_admin_users FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

CREATE POLICY "Super admins can manage admin users"
    ON iepa_admin_users FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.role = 'super_admin'
            AND admin.is_active = true
        )
    );

-- Admin policies (for service role)
CREATE POLICY "Service role can manage all user profiles"
    ON iepa_user_profiles FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all historical registrations"
    ON iepa_historical_registrations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all attendee registrations"
    ON iepa_attendee_registrations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all speaker registrations"
    ON iepa_speaker_registrations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all sponsor registrations"
    ON iepa_sponsor_registrations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all payments"
    ON iepa_payments FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all admin users"
    ON iepa_admin_users FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

-- RLS Policies for iepa_organizations (all authenticated users can read, only admins can write)
CREATE POLICY "Anyone can view active organizations"
    ON iepa_organizations FOR SELECT
    USING (is_active = true);

CREATE POLICY "Only admins can manage organizations"
    ON iepa_organizations FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

CREATE POLICY "Service role can manage all organizations"
    ON iepa_organizations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

-- RLS Policies for iepa_sponsor_domains
CREATE POLICY "Anyone can view active sponsor domains"
    ON iepa_sponsor_domains FOR SELECT
    USING (is_active = true);

CREATE POLICY "Only admins can manage sponsor domains"
    ON iepa_sponsor_domains FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

CREATE POLICY "Service role can manage all sponsor domains"
    ON iepa_sponsor_domains FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');
