import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

/**
 * Accessibility tests for IEPA Email Center
 * Tests WCAG compliance, keyboard navigation, and screen reader compatibility
 */

test.describe('Email Center Accessibility Tests', () => {
  test('page meets WCAG 2.1 AA standards', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-status-summary"]');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
    
    console.log(`✅ Accessibility scan passed with ${accessibilityScanResults.passes.length} checks`);
  });

  test('keyboard navigation works correctly', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-status-summary"]');
    
    // Test tab navigation
    await page.keyboard.press('Tab');
    let focusedElement = await page.locator(':focus').first();
    await expect(focusedElement).toBeVisible();
    
    // Continue tabbing through interactive elements
    const interactiveElements = [];
    for (let i = 0; i < 20; i++) {
      await page.keyboard.press('Tab');
      const focused = page.locator(':focus').first();
      if (await focused.isVisible()) {
        const tagName = await focused.evaluate(el => el.tagName.toLowerCase());
        const role = await focused.getAttribute('role');
        interactiveElements.push({ tagName, role });
      }
    }
    
    // Should have navigated through multiple interactive elements
    expect(interactiveElements.length).toBeGreaterThan(5);
    console.log(`Navigated through ${interactiveElements.length} interactive elements`);
  });

  test('focus indicators are visible', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-status-summary"]');
    
    // Tab to first interactive element
    await page.keyboard.press('Tab');
    
    const focusedElement = page.locator(':focus').first();
    await expect(focusedElement).toBeVisible();
    
    // Check that focus indicator is visible
    const focusStyles = await focusedElement.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        outline: styles.outline,
        outlineWidth: styles.outlineWidth,
        outlineStyle: styles.outlineStyle,
        outlineColor: styles.outlineColor,
        boxShadow: styles.boxShadow,
      };
    });
    
    // Should have visible focus indicator (outline or box-shadow)
    const hasFocusIndicator = 
      focusStyles.outline !== 'none' ||
      focusStyles.outlineWidth !== '0px' ||
      focusStyles.boxShadow !== 'none';
    
    expect(hasFocusIndicator).toBe(true);
    console.log('✅ Focus indicators are visible');
  });

  test('proper heading hierarchy', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('h1');
    
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    const headingLevels = [];
    
    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
      const text = await heading.textContent();
      headingLevels.push({ level: parseInt(tagName.charAt(1)), text });
    }
    
    // Should start with h1
    expect(headingLevels[0]?.level).toBe(1);
    
    // Check for proper hierarchy (no skipping levels)
    for (let i = 1; i < headingLevels.length; i++) {
      const currentLevel = headingLevels[i].level;
      const previousLevel = headingLevels[i - 1].level;
      
      // Should not skip more than one level
      expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
    }
    
    console.log(`✅ Heading hierarchy is correct: ${headingLevels.map(h => `h${h.level}`).join(', ')}`);
  });

  test('images have alt text', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForLoadState('networkidle');
    
    const images = await page.locator('img').all();
    
    for (const image of images) {
      const alt = await image.getAttribute('alt');
      const ariaLabel = await image.getAttribute('aria-label');
      const ariaLabelledby = await image.getAttribute('aria-labelledby');
      const role = await image.getAttribute('role');
      
      // Images should have alt text, aria-label, or be decorative
      const hasAccessibleName = alt !== null || ariaLabel || ariaLabelledby || role === 'presentation';
      expect(hasAccessibleName).toBe(true);
    }
    
    console.log(`✅ All ${images.length} images have proper accessibility attributes`);
  });

  test('form labels are properly associated', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="send-email-form"]');
    
    const inputs = await page.locator('input, textarea, select').all();
    
    for (const input of inputs) {
      const id = await input.getAttribute('id');
      const ariaLabel = await input.getAttribute('aria-label');
      const ariaLabelledby = await input.getAttribute('aria-labelledby');
      const placeholder = await input.getAttribute('placeholder');
      
      // Check for associated label
      let hasLabel = false;
      if (id) {
        const label = page.locator(`label[for="${id}"]`);
        hasLabel = await label.count() > 0;
      }
      
      // Input should have label, aria-label, or aria-labelledby
      const hasAccessibleName = hasLabel || ariaLabel || ariaLabelledby;
      
      if (!hasAccessibleName && !placeholder) {
        console.warn(`Input without accessible name found: ${await input.getAttribute('name') || 'unnamed'}`);
      }
      
      // Most inputs should have accessible names (some exceptions for hidden inputs)
      const inputType = await input.getAttribute('type');
      if (inputType !== 'hidden') {
        expect(hasAccessibleName || placeholder).toBe(true);
      }
    }
    
    console.log(`✅ Form inputs have proper labels`);
  });

  test('buttons have accessible names', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="send-email-form"]');
    
    const buttons = await page.locator('button').all();
    
    for (const button of buttons) {
      const textContent = await button.textContent();
      const ariaLabel = await button.getAttribute('aria-label');
      const ariaLabelledby = await button.getAttribute('aria-labelledby');
      const title = await button.getAttribute('title');
      
      // Button should have accessible name
      const hasAccessibleName = 
        (textContent && textContent.trim()) ||
        ariaLabel ||
        ariaLabelledby ||
        title;
      
      expect(hasAccessibleName).toBeTruthy();
    }
    
    console.log(`✅ All ${buttons.length} buttons have accessible names`);
  });

  test('color contrast meets standards', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-status-summary"]');
    
    // This would typically use a color contrast analyzer
    // For now, we'll check that text is visible and readable
    const textElements = await page.locator('p, span, div, h1, h2, h3, h4, h5, h6, button, a').all();
    
    let contrastIssues = 0;
    
    for (const element of textElements.slice(0, 20)) { // Check first 20 elements
      const styles = await element.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor,
          fontSize: computed.fontSize,
        };
      });
      
      // Basic check - ensure text color is not the same as background
      if (styles.color === styles.backgroundColor) {
        contrastIssues++;
      }
    }
    
    expect(contrastIssues).toBe(0);
    console.log('✅ No obvious color contrast issues found');
  });

  test('skip links are present', async ({ page }) => {
    await page.goto('/admin/emails');
    
    // Check for skip links (usually hidden until focused)
    await page.keyboard.press('Tab');
    
    const skipLink = page.locator('a[href*="#main"], a[href*="#content"], a:has-text("Skip to")').first();
    
    if (await skipLink.count() > 0) {
      await expect(skipLink).toBeVisible();
      console.log('✅ Skip link found and visible on focus');
    } else {
      console.log('ℹ️ No skip links found (consider adding for better accessibility)');
    }
  });

  test('ARIA landmarks are present', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-status-summary"]');
    
    // Check for ARIA landmarks
    const landmarks = await page.locator('[role="main"], [role="navigation"], [role="banner"], [role="contentinfo"], main, nav, header, footer').all();
    
    expect(landmarks.length).toBeGreaterThan(0);
    
    const landmarkTypes = [];
    for (const landmark of landmarks) {
      const role = await landmark.getAttribute('role');
      const tagName = await landmark.evaluate(el => el.tagName.toLowerCase());
      landmarkTypes.push(role || tagName);
    }
    
    console.log(`✅ Found landmarks: ${landmarkTypes.join(', ')}`);
  });

  test('screen reader compatibility', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-status-summary"]');
    
    // Check for screen reader specific attributes
    const ariaElements = await page.locator('[aria-label], [aria-labelledby], [aria-describedby], [aria-expanded], [aria-hidden]').all();
    
    expect(ariaElements.length).toBeGreaterThan(0);
    
    // Check for proper use of aria-hidden
    const ariaHiddenElements = await page.locator('[aria-hidden="true"]').all();
    
    for (const element of ariaHiddenElements) {
      // Elements with aria-hidden="true" should not be focusable
      const tabIndex = await element.getAttribute('tabindex');
      const isFocusable = tabIndex !== '-1' && tabIndex !== null;
      
      if (isFocusable) {
        const tagName = await element.evaluate(el => el.tagName.toLowerCase());
        console.warn(`Focusable element with aria-hidden="true" found: ${tagName}`);
      }
    }
    
    console.log(`✅ Screen reader attributes properly configured`);
  });

  test('mobile accessibility', async ({ browser }) => {
    const context = await browser.newContext({
      ...require('@playwright/test').devices['iPhone 12'],
    });
    
    const page = await context.newPage();
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-status-summary"]');
    
    // Check touch target sizes
    const buttons = await page.locator('button, a, input[type="button"], input[type="submit"]').all();
    
    for (const button of buttons.slice(0, 10)) { // Check first 10 buttons
      const boundingBox = await button.boundingBox();
      
      if (boundingBox) {
        // Touch targets should be at least 44x44 pixels
        const minSize = 44;
        expect(boundingBox.width).toBeGreaterThanOrEqual(minSize - 5); // Allow 5px tolerance
        expect(boundingBox.height).toBeGreaterThanOrEqual(minSize - 5);
      }
    }
    
    console.log('✅ Mobile touch targets meet minimum size requirements');
    
    await context.close();
  });

  test('error messages are accessible', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="send-email-form"]');
    
    // Try to trigger form validation errors
    const submitButton = page.locator('button:has-text("Send Email")');
    
    if (await submitButton.isVisible()) {
      // Try to submit empty form
      await submitButton.click();
      
      // Check for error messages
      const errorMessages = await page.locator('[role="alert"], .error, [aria-invalid="true"]').all();
      
      for (const error of errorMessages) {
        // Error messages should be visible and have proper ARIA attributes
        await expect(error).toBeVisible();
        
        const ariaLive = await error.getAttribute('aria-live');
        const role = await error.getAttribute('role');
        
        // Should have aria-live or role="alert"
        const isAccessible = ariaLive || role === 'alert';
        expect(isAccessible).toBe(true);
      }
      
      if (errorMessages.length > 0) {
        console.log(`✅ ${errorMessages.length} error messages are properly accessible`);
      }
    }
  });
});
