-- IEPA Production Data Dump
-- Generated: 2025-06-07T18:35:49.921Z
-- 
-- This file contains production data that can be imported into local development
-- 
-- Usage:
--   1. Make sure local Supabase is running: supabase start
--   2. Import: psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -f .docs/production-dumps/production-dump-2025-06-07.sql

-- Disable foreign key checks and triggers for faster import
SET session_replication_role = replica;

-- Clear existing data (uncomment if needed)
-- TRUNCATE iepa_email_log, iepa_payments, iepa_golf_registrations, 
--          iepa_sponsor_registrations, iepa_speaker_registrations, 
--          iepa_attendee_registrations, iepa_historical_registrations, 
--          iepa_organizations, iepa_user_profiles CASCADE;


-- iepa_user_profiles (110 rows)
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('70f7d3a8-5e07-43e3-a772-2333fea615cf', '14445446-8d3f-4a51-9119-3e0030c9389c', '<PERSON>', 'Miller', '<PERSON> <PERSON>', '<EMAIL>', '9253232435', 'E3', 'Director', '1331 <PERSON> Ave', 'San <PERSON>', 'California', '94117', 'United <PERSON>', 'Male', 'Nate', TRUE, '2025-06-05T07:26:59.779+00:00', '2025-06-05T07:26:59.86408+00:00', '2025-06-05T07:26:59.86408+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('5117f6b7-a841-44c7-94e1-a8566a47e370', '9b537cd2-f875-447b-96b3-fa8f95f2f54b', 'Maia', 'Leroy', 'Maia Leroy', '<EMAIL>', '9169174712', 'CEERT', 'Policy Director', '1100 11th Street Suite 311', 'Sacramento', 'California', '95814', 'United States', 'Female', 'Maia Leroy', TRUE, '2025-06-05T07:27:00.274+00:00', '2025-06-05T07:27:00.358676+00:00', '2025-06-05T07:27:00.358676+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('8438d39d-3ab2-47cd-904f-aea8de13b4fa', 'c6b963a2-148a-4e5a-81ec-e6017e52cba5', 'Carol', 'Denning', 'Carol Denning', '<EMAIL>', '5305132799', 'Energy Pathways', 'President', '789 Downing Avenue', 'Chico', 'California', '95926', 'United States', 'Female', 'Carol Denning', TRUE, '2025-06-05T07:27:00.661+00:00', '2025-06-05T07:27:00.749569+00:00', '2025-06-05T07:27:00.749569+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('33ca3f44-784a-40a2-846b-0e1cbbb05da4', 'aadb84e0-46b1-448a-9f79-102f36abd36b', 'Fanny', 'Kidwell Langlois', 'Fanny Kidwell Langlois', '<EMAIL>', '7752333325', 'Viridon', 'VP Finance & Corporate Development', '435 Gooseberry Drive', 'Reno', 'Nevada', '89523', 'United States', 'Female', 'Fanny Kidwell', TRUE, '2025-06-05T07:27:01.071+00:00', '2025-06-05T07:27:01.15798+00:00', '2025-06-05T07:27:01.15798+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('987fe873-3bfb-4dc7-b014-ca19ad9439b0', 'a439431a-af56-434a-be47-a676dab84483', 'Alex', 'Jackson', 'Alex Jackson', '<EMAIL>', '5104214075', 'American Clean Power - California', 'Executive Director', '2733 6th Avenue', 'Sacramento', 'California', '95818', 'United States', 'Male', 'Alex Jackson', TRUE, '2025-06-05T07:27:01.518+00:00', '2025-06-05T07:27:01.609838+00:00', '2025-06-05T07:27:01.609838+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('35febe6f-5dd7-4743-9a54-3bfcff1e9676', 'c443989a-5dc9-4161-acba-694505a95dc5', 'Alex', 'Morris', 'Alex Morris', '<EMAIL>', '9162055682', 'California Community Power (CC Power)', 'General Manager', '901 H Street, Suite 120 PMB 157', 'Sacramento', 'California', '95814', 'United States', 'Male', 'Alex Morris', TRUE, '2025-06-05T07:27:01.931+00:00', '2025-06-05T07:27:02.018615+00:00', '2025-06-05T07:27:02.018615+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a6043ada-e249-453b-a8df-66671a58d17f', 'b10e9855-de9a-41cc-a49b-6f6338725a01', 'Dean', 'Tuel', 'Dean Tuel', '<EMAIL>', '5126323009', 'Hydrostor', 'VP, Origination (North America)', '1125 17th Street', 'Denver', 'Alabama', '80202', 'United States', 'Male', 'Dean', TRUE, '2025-06-05T07:27:02.325+00:00', '2025-06-05T07:27:02.404466+00:00', '2025-06-05T07:27:02.404466+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('f0c7385c-93a1-4cb3-af41-a98b8a6fba2d', 'ee18122c-99b8-4adf-87a2-ccaf2ff52572', 'Raja', 'Ramesh', 'Raja Ramesh', '<EMAIL>', '9166644603', 'California Energy Commission', 'Senior Policy Advisor to Vice Chair Gunda', '715 P Street', 'Sacramento', 'California', '95814', 'United States', 'Male', 'Raja Ramesh', TRUE, '2025-06-05T07:27:02.709+00:00', '2025-06-05T07:27:02.790605+00:00', '2025-06-05T07:27:02.790605+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('b384f8d1-465e-4f15-b91a-8f87248d59ec', '9e1871bc-541c-42c8-9ae1-241cfe0cdee7', 'Daniel', 'Handal', 'Daniel Handal', '<EMAIL>', '5612897115', 'NextEra Energy Resources', 'Senior Director', 'One California Street, Suite 1600', 'San Francisco', 'California', '94111', 'United States', 'Male', 'Dan Handal', TRUE, '2025-06-05T07:27:04.129+00:00', '2025-06-05T07:27:04.212644+00:00', '2025-06-05T07:27:04.212644+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('94c70dda-be19-4640-92c5-c95fca34e30e', '4b70de52-9c59-47b8-b20e-cf71bdff59b4', 'Julie', 'Thompson', 'Julie Thompson', '<EMAIL>', '4057778438', 'Hydrostor', 'VP Origination, Commercial & Industrial', '1125 17th Street', 'Denver', 'Colorado', '80202', 'United States', 'Female', 'Julie', TRUE, '2025-06-05T07:27:04.516+00:00', '2025-06-05T07:27:04.597349+00:00', '2025-06-05T07:27:04.597349+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('5cc35024-a2dc-461b-abd4-e7909f67bc61', '8fa66175-a4d8-41ce-af11-2a553ca0d55d', 'Greg', 'Contreras', 'Greg Contreras', '<EMAIL>', '9162818440', 'Wellhead Electric Company', 'Counsel', '650 Bercut Drive, Suite C', 'Sacramento', 'California', '95811', 'United States', 'Male', 'Greg Contreras', TRUE, '2025-06-05T07:27:04.949+00:00', '2025-06-05T07:27:05.031752+00:00', '2025-06-05T07:27:05.031752+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('58982323-8074-49e1-8cbf-fea4cf3dd689', '65de69c0-485f-4744-9c5d-3d668330d2cf', 'Jason', 'Armenta', 'Jason Armenta', '<EMAIL>', '7134165039', 'Calpine Corporation', 'VP of Power Trading', '717 Texas Avenue', 'Houston', 'Texas', '77002', 'United States', 'Male', 'Jason Armenta', TRUE, '2025-06-05T07:27:05.328+00:00', '2025-06-05T07:27:05.410936+00:00', '2025-06-05T07:27:05.410936+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('40ee26bd-940b-4f57-8b7b-893e1be4878d', '0d96e55d-0b98-49d6-b346-500c9aa9488d', 'Monica', 'Garcia', 'Monica Garcia', '<EMAIL>', '5625097881', 'The AES Corporation', 'External Affairs / Stakeholder Relations', '690 N. Studebaker Road', 'Long Beach', 'California', '90803', 'United States', 'Female', 'Monica Garcia', TRUE, '2025-06-05T07:27:05.776+00:00', '2025-06-05T07:27:05.865131+00:00', '2025-06-05T07:27:05.865131+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('aa75dfe6-5970-42b7-9cf4-8923d5827278', 'ace9a1a6-ab5d-423f-bfa1-e0a40a61940f', 'Beth', 'Vaughan', 'Beth Vaughan', '<EMAIL>', '9254085142', 'CalCCA', 'CEO', '4391 N Marsh Elder Court', 'Concord', 'California', '94521', 'United States', 'Female', 'Beth Vaughan', TRUE, '2025-06-05T07:27:06.185+00:00', '2025-06-05T07:27:06.264836+00:00', '2025-06-05T07:27:06.264836+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('4f0d0ad6-6129-486c-9984-eb1c06e13452', '449daea0-50f4-47c6-84a4-aca41aa13063', 'Dai', 'Owen', 'Dai Owen', '<EMAIL>', '4157200284', 'EDF Renewables', 'SVP Origination & Power Marketing', '1999 Harrison Street, Suite 675', 'Oakland', 'California', '94612', 'United States', 'Male', 'Dai Owen', TRUE, '2025-06-05T07:27:06.568+00:00', '2025-06-05T07:27:06.652115+00:00', '2025-06-05T07:27:06.652115+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('4cd0c9e9-6fb0-46ee-beb7-d0672ecc83ef', '7311130c-e448-409c-a878-59833784deef', 'Byron', 'Vosburg', 'Byron Vosburg', '<EMAIL>', '6198806545', 'San Diego Community Power', 'Chief Commercial Officer', '2305 Historic Decatur Road', 'San Diego', 'California', '92106', 'United States', 'Male', 'Byron Vosburg', TRUE, '2025-06-05T07:27:06.949+00:00', '2025-06-05T07:27:07.030917+00:00', '2025-06-05T07:27:07.030917+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('6ab707a7-0455-4f8a-9871-1ee58624792b', '6eaaa76e-e894-4128-8e54-2795ed5ee40d', 'Bob', 'Mitchell', 'Bob Mitchell', '<EMAIL>', '2403406326', 'TRED', 'Co-Founding Partner', '307 3rd Street, SE', 'Washington', 'District of Columbia', '20003', 'United States', 'Male', 'Bob Mitchell', TRUE, '2025-06-05T07:27:07.314+00:00', '2025-06-05T07:27:07.391107+00:00', '2025-06-05T07:27:07.391107+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('6535ed3c-8ed3-4112-8ba5-e3813ce68eaa', '2db8e029-cdab-431b-aa07-063ec1905ce6', 'Rachel', 'McMahon', 'Rachel McMahon', '<EMAIL>', '4154127587', 'California Energy Storage Alliance', 'Vice President, Policy', '808 R Street, #209', 'Sacramento', 'California', '95814', 'United States', 'Female', 'Rachel McMahon', TRUE, '2025-06-05T07:27:07.688+00:00', '2025-06-05T07:27:07.770123+00:00', '2025-06-05T07:27:07.770123+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('b146ab2c-4fd8-4097-ae80-3f2c1b23e228', 'be0012dd-b2c2-4e2c-9c8f-47cc0de2f1d6', 'Adam', 'Hatefi', 'Adam Hatefi', '<EMAIL>', '9168468379', 'Gridwell Consulting', 'Senior Associate', '8304 Cranford Way', 'Citrus Heights', 'California', '95610', 'United States', 'Male', 'Adam Hatefi', TRUE, '2025-06-05T07:27:09.104+00:00', '2025-06-05T07:27:09.195322+00:00', '2025-06-05T07:27:09.195322+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('55feec3e-13d8-4aaa-8334-19f71c956cd5', '612d3355-a997-4403-b03c-281de540138a', 'Emily', 'Singer', 'Emily Singer', '<EMAIL>', '5153001751', 'BHE Renewables, LLC', 'Vice President of Regulatory Affairs', '4020 Ingersoll Avenue', 'Des Moines', 'Iowa', '50312', 'United States', 'Female', 'Emily Singer', TRUE, '2025-06-05T07:27:09.498+00:00', '2025-06-05T07:27:09.577977+00:00', '2025-06-05T07:27:09.577977+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a5d3863b-a63b-4332-ad2a-7869f0d77087', 'ab661b9c-0d31-475b-a252-ea0365c1b27d', 'Mark', 'Wiranowski', 'Mark Wiranowski', '<EMAIL>', '3039317139', 'Wilkinson Barker Knauer LLP', 'Partner', '2138 W 32nd Avenue', 'Denver', 'Colorado', '80211', 'United States', 'Male', 'Mark Wiranowski', TRUE, '2025-06-05T07:27:09.884+00:00', '2025-06-05T07:27:09.964637+00:00', '2025-06-05T07:27:09.964637+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('4879176d-8bd8-4170-b69a-9e575ab4a554', '04989d07-0451-43e4-b583-b00c9520c732', 'Varner', 'Seaman', 'Varner Seaman', '<EMAIL>', '5032010021', 'Pattern Energy', 'Director of Legislative & Regulatory Affairs', '5616 SE Tolman Street', 'Portland', 'Oregon', '97206', 'United States', 'Male', 'Varner', TRUE, '2025-06-05T07:27:10.28+00:00', '2025-06-05T07:27:10.361188+00:00', '2025-06-05T07:27:10.361188+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('054898cf-06d4-468b-ab31-e96abd89ef0a', '638bbe89-6bbd-4426-aecb-a6d8b0017f1c', 'Daniel', 'Runyan', 'Daniel Runyan', '<EMAIL>', '7085230043', 'Invenergy LLC', 'SVP, Offshore Wind', '1 South Wacker Drive', 'Chicago', 'Illinois', '60606', 'United States', 'Male', 'Dan', TRUE, '2025-06-05T07:27:10.649+00:00', '2025-06-05T07:27:10.729367+00:00', '2025-06-05T07:27:10.729367+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('1c54ecfd-629f-4fa9-b7e4-e23a67317ba7', 'd94ae139-de4d-4bd4-a87d-57105bf8e07e', 'Robin', 'Smutny-Jones', 'Robin Smutny-Jones', '<EMAIL>', '9168025298', 'ZGlobal', 'Consultant', '604 Sutter Street, Suite 250', 'Folsom', 'California', '95630', 'United States', 'Female', 'Robin Smutny-Jones', TRUE, '2025-06-05T07:27:11.016+00:00', '2025-06-05T07:27:11.107569+00:00', '2025-06-05T07:27:11.107569+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a4b06e0c-2395-4298-aa81-433d39c0cfb5', '49a33f52-81ee-494b-b574-d7ab0a8abda7', 'Kyra', 'Millich', 'Kyra Millich', '<EMAIL>', '4152973046', 'National Multiple Sclerosis Society', 'Major Gifts Officer', '633 Coventry Road', 'Kensington', 'California', '94707', 'United States', 'Female', 'Kyra Millich', TRUE, '2025-06-05T07:27:11.446+00:00', '2025-06-05T07:27:11.527365+00:00', '2025-06-05T07:27:11.527365+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('448ddcc8-2d5f-4180-b081-eb5fa9e95ed9', '10e1caf3-f74d-4bcc-8ddc-e809d722fe89', 'Sara', 'Fitzsimon', 'Sara Fitzsimon', '<EMAIL>', '9166063234', 'Independent Energy Producers Association', 'Policy Director', 'PO Box 1287', 'Sloughhouse', 'California', '95683', 'United States', 'Female', 'Sara Fitzsimon', TRUE, '2025-06-05T07:27:11.828+00:00', '2025-06-05T07:27:11.910111+00:00', '2025-06-05T07:27:11.910111+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('dec8d6f9-3aa0-4cdf-b66a-dba1e03ae926', 'f57c55d1-4c36-451f-a0b4-8795fc95c2cb', 'Jan', 'Smutny-Jones', 'Jan Smutny-Jones', '<EMAIL>', '9164489499', 'Independent Energy Producers Association', 'CEO', 'PO Box 1287', 'Sloughhouse', 'California', '95683', 'United States', 'Male', 'Jan Smutny-Jones', TRUE, '2025-06-05T07:27:12.239+00:00', '2025-06-05T07:27:12.319678+00:00', '2025-06-05T07:27:12.319678+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a976c390-21be-4549-b50c-37db4cd6a873', '2e827e40-1bf4-45e6-b4c5-1c480d17333f', 'Jamie', 'Parker', 'Jamie Parker', '<EMAIL>', '9164489499', 'Independent Energy Producers Association', 'Administrator', 'PO Box 1287', 'Sloughhouse', 'California', '95683', 'United States', 'Female', 'Jamie Parker', TRUE, '2025-06-05T07:27:12.694+00:00', '2025-06-05T07:27:12.777014+00:00', '2025-06-05T07:27:12.777014+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('30377521-6ca0-49a7-95ef-30ef30bf727a', '09f90adc-bac3-42ff-bb31-c0fab9ad549b', 'Adam', 'Weber', 'Adam Weber', '<EMAIL>', '5129631859', 'Metropolitan Partners Group', 'Principal', '850 3rd Avenue', 'New York', 'New York', '10022', 'United States', 'Male', 'Adam R. Weber', TRUE, '2025-06-05T07:27:14.093+00:00', '2025-06-05T07:27:14.177094+00:00', '2025-06-05T07:27:14.177094+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('61644a27-b80b-49c4-b492-4832972515a3', '3d9577c8-5128-467d-ac92-c90a73087587', 'Markian', 'Melnyk', 'Markian Melnyk', '<EMAIL>', '3012564423', 'Three Rivers Energy Development LLC', 'Principal', '9011 Gettysburg Lane', 'College Park', 'Maryland', '20740', 'United States', 'Male', 'Markian Melnyk', TRUE, '2025-06-05T07:27:14.465+00:00', '2025-06-05T07:27:14.544904+00:00', '2025-06-05T07:27:14.544904+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('b7e2a204-f91c-459c-bcfd-8f24272e2cab', 'd68bc4bb-5588-4066-a0ba-d8639f94eb68', 'Matthew', 'Haley', 'Matthew Haley', '<EMAIL>', '5126329669', 'RWE', 'Sr Manager, Commercial', '1401 E 6th Street, Suite 400', 'Austin', 'Texas', '78702', 'United States', 'Male', 'Matt Haley', TRUE, '2025-06-05T07:27:14.865+00:00', '2025-06-05T07:27:14.958488+00:00', '2025-06-05T07:27:14.958488+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('9110f24a-6684-4b4c-b1a1-303c76ad2733', '94cd472e-5b0e-4f11-8984-d6e6f5907659', 'Nick', 'Dominguez', 'Nick Dominguez', '<EMAIL>', '8312643127', 'Central Coast Community Energy', 'Director of Power Supply Resources', '70 Garden Court, #300', 'Monterey', 'California', '93940', 'United States', 'Male', 'Nick Dominguez', TRUE, '2025-06-05T07:27:15.442+00:00', '2025-06-05T07:27:15.527579+00:00', '2025-06-05T07:27:15.527579+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('3081dd39-ab29-41a8-94bd-2456ec45c7e6', '2f3a7e7c-dcc6-4b6f-835d-2437230e288e', 'Virinder', 'Singh', 'Virinder Singh', '<EMAIL>', '5033472851', 'EDF Renewables', 'Vice President - Regulatory & Legislative Affairs', '15445 Innovation Drive', 'San Diego', 'California', '92128', 'United States', 'Male', 'Virinder Singh', TRUE, '2025-06-05T07:27:15.865+00:00', '2025-06-05T07:27:15.945979+00:00', '2025-06-05T07:27:15.945979+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('1045279b-dc7d-408d-8fe5-2d83039036cb', '50602e99-3763-49e4-a58b-f78b6f2f1a25', 'Jason', 'Burwen', 'Jason Burwen', '<EMAIL>', '5033130645', 'GridStor', 'VP Policy & Strategy', '7 SE Stark', 'Portland', 'Oregon', '97214', 'United States', 'Male', 'Jason Burwen', TRUE, '2025-06-05T07:27:16.267+00:00', '2025-06-05T07:27:16.348302+00:00', '2025-06-05T07:27:16.348302+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('fa97249e-d673-4baf-92ac-90d765510a30', '427bd81e-3d8f-4742-aaa8-4712516c0170', 'Mackenzie', 'Sheehan', 'Mackenzie Sheehan', '<EMAIL>', '9735255949', 'Hull Street Energy', 'Associate', '4747 Bethesda Avenue', 'Bethesda', 'Maryland', '20814', 'United States', 'Female', 'Mackenzie Sheehan', TRUE, '2025-06-05T07:27:16.733+00:00', '2025-06-05T07:27:16.815892+00:00', '2025-06-05T07:27:16.815892+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('43112d9e-677a-42a8-a356-dc00f22ebe9d', 'e37c7ad1-86c2-4f7c-af84-371f81b85214', 'Rebecca', 'Covarrubias', 'Rebecca Covarrubias', '<EMAIL>', '2132384556', 'Diamond Generating Corporation', 'Director Portfolio & Asset Management', '633 W. 5th Street 27th Floor', 'Los Angeles', 'California', '90071', 'United States', 'Female', 'Rebecca Covarrubias', TRUE, '2025-06-05T07:27:17.122+00:00', '2025-06-05T07:27:17.207017+00:00', '2025-06-05T07:27:17.207017+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('7c9f8ece-ea29-41f6-b0fe-f745a1195ed2', 'ccc5d0a9-25fe-493d-b8a1-9d046521cb83', 'Claudia', 'Becerril', 'Claudia Becerril', '<EMAIL>', '5036883633', 'Avangrid Renewables', 'Asset Manager', '735 SE 72nd Avenue', 'Portland', 'Oregon', '97215', 'United States', 'Female', 'Claudia Becerril', TRUE, '2025-06-05T07:27:17.514+00:00', '2025-06-05T07:27:17.598629+00:00', '2025-06-05T07:27:17.598629+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('33d410ba-f157-40a9-ba5f-43fb49562979', 'dd70a82d-e7d2-447b-a43a-a01d77dc37b3', 'Debrea', 'Terwilliger', 'Debrea Terwilliger', '<EMAIL>', '7758488271', 'Wilkinson Barker Knauer LLP', 'Partner', '2138 W. 32nd Avenue, Suite 300', 'Denver', 'Colorado', '80211', 'United States', 'Female', 'Debrea Terwilliger', TRUE, '2025-06-05T07:27:17.908+00:00', '2025-06-05T07:27:18.010041+00:00', '2025-06-05T07:27:18.010041+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('dc41136b-9a1b-4615-930a-8b544022639b', 'aac11c4a-9f3c-4333-87fa-d3ac53d5fb92', 'Jordan', 'Pinjuv', 'Jordan Pinjuv', '<EMAIL>', '9166710912', 'Wilkinson Barker Knauer LLP', 'Partner', '5001 Owls Nest Road', 'Shingle Springs', 'California', '95682', 'United States', 'Male', 'Jordan Pinjuv', TRUE, '2025-06-05T07:27:19.301+00:00', '2025-06-05T07:27:19.381727+00:00', '2025-06-05T07:27:19.381727+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('59f7284b-f41f-48e7-a946-b6eb100b20f6', 'b1e86d61-e7bf-4aa4-b72e-c52f8aabd81c', 'Joe', 'Kruger', 'Joe Kruger', '<EMAIL>', '6232883189', 'Capital Power', 'Consultant', '2398 E Camelback Road', 'Phoenix', 'Arizona', '85016', 'United States', 'Male', 'Joe Kruger', TRUE, '2025-06-05T07:27:19.73+00:00', '2025-06-05T07:27:19.809709+00:00', '2025-06-05T07:27:19.809709+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('8ebd1f7d-2b46-472b-8c52-dafb23b3044d', '53f8210a-fa1d-44c8-958a-1be5396868ac', 'Matthew', 'Freedman', 'Matthew Freedman', '<EMAIL>', '4159548084', 'The Utility Reform Network', 'Staff Attorney', '360 Grand Avenue, #150', 'Oakland', 'California', '94610', 'United States', 'Male', 'Matthew Freedman', TRUE, '2025-06-05T07:27:20.163+00:00', '2025-06-05T07:27:20.27127+00:00', '2025-06-05T07:27:20.27127+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('d0614d32-a3c9-4db6-a1af-a4473841f54e', 'b8383649-fd46-4972-840c-0605af38602c', 'Danielle', 'Mills', 'Danielle Mills', '<EMAIL>', '9169063325', 'CAISO', 'Principal, Infrastructure Policy Development', '250 Outcropping Way', 'Folsom', 'California', '95630', 'United States', 'Female', 'Danielle', TRUE, '2025-06-05T07:27:20.576+00:00', '2025-06-05T07:27:20.657628+00:00', '2025-06-05T07:27:20.657628+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a147e0a3-502e-4487-985a-75c8f86abc31', '5ce63f6c-86c1-4067-93d1-32d57429f587', 'Jim', 'Shandalov', 'Jim Shandalov', '<EMAIL>', '4153185910', 'NextEra Energy Resources', 'Vice President', '1 California Street', 'San Francisco', 'California', '94111', 'United States', 'Male', 'Jim Shandalov', TRUE, '2025-06-05T07:27:20.957+00:00', '2025-06-05T07:27:21.040317+00:00', '2025-06-05T07:27:21.040317+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('7d965a32-49d6-4a40-a283-8a6a4330a041', 'a76ec29f-42b0-43cf-b757-779ad8169ea4', 'Delphine', 'Hou', 'Delphine Hou', '<EMAIL>', '9167492214', 'Statewide Energy Office, Department of Water Resources', 'Deputy Director, Statewide Energy', '715 P Street', 'Sacramento', 'California', '95814', 'United States', 'Female', 'Delphine Hou', TRUE, '2025-06-05T07:27:21.336+00:00', '2025-06-05T07:27:21.414721+00:00', '2025-06-05T07:27:21.414721+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('7dd94ffb-e007-4223-8451-fcdb0d09dbcb', 'e030628b-ccd1-4f87-aaef-745f98b3629a', 'Arne', 'Olson', 'Arne Olson', '<EMAIL>', '4157229708', 'Energy and Environmental Economics, Inc.', 'Senior Partner', '44 Montgomery Street, Suite 1500', 'San Francisco', 'California', '94104', 'United States', 'Male', 'Arne Olson, E3', TRUE, '2025-06-05T07:27:21.706+00:00', '2025-06-05T07:27:21.793587+00:00', '2025-06-05T07:27:21.793587+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('f01ded78-2884-4e6a-bfef-5b0a70c05bba', '8279b11c-8486-4f8e-a03d-13349bace525', 'Willie', 'Calvin', 'Willie Calvin', '<EMAIL>', '7652286619', 'California Community Choice Association', 'Regulatory Case Manager', '1121 L Street, Suite 400', 'Sacramento', 'California', '95814', 'United States', 'Male', 'Willie Calvin', TRUE, '2025-06-05T07:27:22.098+00:00', '2025-06-05T07:27:22.176035+00:00', '2025-06-05T07:27:22.176035+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('89971de1-b2ce-4cf0-b496-4f46fb1107c5', '819fd8a0-498d-472d-b997-5c35cbbe6294', 'Scott', 'Olson', 'Scott Olson', '<EMAIL>', '5103343743', 'Avangrid Renewables', 'Director, Western Policy, Regulatory, and Markets', '2701 NW Vaughn', 'Portland', 'Oregon', '97210', 'United States', 'Male', 'Scott Olson', TRUE, '2025-06-05T07:27:22.476+00:00', '2025-06-05T07:27:22.560033+00:00', '2025-06-05T07:27:22.560033+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('e72d6a7f-b22b-4f3b-a995-c1d50f1bc591', '745b9410-074b-44d3-b168-ddf25827e86e', 'Alice', 'Reynolds', 'Alice Reynolds', '<EMAIL>', '9166019825', 'California Public Utilities Commission', 'President', '505 Van Ness Avenue', 'San Francisco', 'California', '94102', 'United States', 'Female', 'Alice Reynolds', TRUE, '2025-06-05T07:27:22.856+00:00', '2025-06-05T07:27:22.93881+00:00', '2025-06-05T07:27:22.93881+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('6a87f5bb-e0de-4d4f-b4b9-50ec106879c9', '6c61f5f2-9924-4353-bcc6-1172ad83414c', 'Christopher', 'Devon', 'Christopher Devon', '<EMAIL>', '5172945473', 'Terra-Gen, LLC', 'Director of Energy Market Policy', '11455 El Camino Real', 'San Diego', 'California', '92130', 'United States', 'Male', 'Chris Devon', TRUE, '2025-06-05T07:27:24.242+00:00', '2025-06-05T07:27:24.330583+00:00', '2025-06-05T07:27:24.330583+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('370c5702-7fd9-491a-8e48-eb28166b3a90', 'd080a141-95bf-4999-91d8-a53d04353b3f', 'Amanda', 'Hosch', 'Amanda Hosch', '<EMAIL>', '5152424279', 'BHE Renewables, LLC', 'Originator', '3299 NW Urbandale Drive', 'Urbandale', 'Iowa', '50322', 'United States', 'Female', 'Amanda Hosch', TRUE, '2025-06-05T07:27:24.639+00:00', '2025-06-05T07:27:24.727146+00:00', '2025-06-05T07:27:24.727146+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('6a465d79-703b-4f01-8ae7-8fbc44c6c7a0', 'dbe251ca-ed80-4537-81d5-863702cfa8f5', 'Seth', 'Hilton', 'Seth Hilton', '<EMAIL>', '4156178943', 'Stoel Rives LLP', 'Attorney', '1 Montgomery Street', 'San Francisco', 'California', '94104', 'United States', 'Male', 'Seth Hilton', TRUE, '2025-06-05T07:27:25.036+00:00', '2025-06-05T07:27:25.124702+00:00', '2025-06-05T07:27:25.124702+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('1bdfc2a9-ba27-43f1-813a-adc2044fd45a', '236490fb-a26e-40cc-b602-30f7db1f1e24', 'Heejin', 'Ryu', 'Heejin Ryu', '<EMAIL>', '4438008868', 'Hull Street Energy', 'Portfolio Manager', '4747 Bethesda Avenue', 'Bethesda', 'Maryland', '20814', 'United States', 'Female', 'Heejin Ryu', TRUE, '2025-06-05T07:27:25.458+00:00', '2025-06-05T07:27:25.550701+00:00', '2025-06-05T07:27:25.550701+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('9899f2b7-048c-4ace-a2e9-55c810eb19b2', '4a9f67c3-6135-4f6a-8cca-cd0051ff8bfb', 'Avis', 'Kowalewski', 'Avis Kowalewski', '<EMAIL>', '9257859418', 'Calpine Corporation', 'VP Governmental & Reg Affairs', '3003 Oak Road, Suite 400', 'Walnut Creek', 'California', '94597', 'United States', 'Female', 'Avis Kowalewski', TRUE, '2025-06-05T07:27:25.874+00:00', '2025-06-05T07:27:25.962268+00:00', '2025-06-05T07:27:25.962268+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('6bb95917-b15f-44cc-aa11-82ab573daea7', 'e91567bc-e43b-4be8-b731-c4df436b621d', 'Ed', 'Callaghan', 'Ed Callaghan', '<EMAIL>', '9253917020', 'Calpine Corporation', 'Managing Counsel', '3003 Oak Road, Suite 400', 'Walnut Creek', 'California', '94597', 'United States', 'Male', 'Ed Callaghan', TRUE, '2025-06-05T07:27:26.272+00:00', '2025-06-05T07:27:26.362762+00:00', '2025-06-05T07:27:26.362762+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('33976340-50f0-4101-8efd-9c7cd7f60ce1', '9505a809-aa37-4881-80c5-b0186b68ae98', 'Ashley', 'Bernstein', 'Ashley Bernstein', '<EMAIL>', '9255572233', 'Calpine Corporation', 'Strategic Origination Director', '3003 Oak Road, Suite 400', 'Walnut Creek', 'California', '94597', 'United States', 'Female', 'Ashley Bernstein', TRUE, '2025-06-05T07:27:26.668+00:00', '2025-06-05T07:27:26.754174+00:00', '2025-06-05T07:27:26.754174+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('8083eb9d-1f8f-48bb-b3e2-0758b4a32afd', '007e45f1-e38d-4021-b9e0-a39a7111c3d9', 'Jill', 'Van Dalen', 'Jill Van Dalen', '<EMAIL>', '9255572296', 'Calpine Corporation', 'VP and Associate General Counsel', '3003 Walnut Road, Suite 400', 'Walnut Creek', 'California', '94597', 'United States', 'Female', 'Jill Van Dalen', TRUE, '2025-06-05T07:27:27.062+00:00', '2025-06-05T07:27:27.156694+00:00', '2025-06-05T07:27:27.156694+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('eedb1817-24de-4ee9-bef7-c0044b3ac9b5', 'abba0a4a-d732-46c7-9993-7465f60cc1c1', 'Alexandre', 'Makler', 'Alexandre Makler', '<EMAIL>', '9255572282', 'Calpine Corporation', 'SVP West Region', '3003 Oak Road, Suite 400', 'Walnut Creek', 'California', '94597', 'United States', 'Male', 'Alex Makler', TRUE, '2025-06-05T07:27:27.475+00:00', '2025-06-05T07:27:27.556631+00:00', '2025-06-05T07:27:27.556631+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('3a0288ea-df13-4332-a0c3-5462c4e0daca', 'ef0ffa01-2c87-4f8b-a8d9-735d2f495bde', 'Kassandra', 'Gough', 'Kassandra Gough', '<EMAIL>', '9164913366', 'Calpine Corporation', 'VP Governmental & Reg Affairs', '3003 Oak Road, Suite 400', 'Walnut Creek', 'California', '94597', 'United States', 'Female', 'Kassandra Gough', TRUE, '2025-06-05T07:27:27.867+00:00', '2025-06-05T07:27:27.960034+00:00', '2025-06-05T07:27:27.960034+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('4e72c199-ee61-4ff9-956b-8ffd0440926a', 'be6ae3f3-6695-420d-be81-49f84199fd1e', 'Anthony', 'Brunello', 'Anthony Brunello', '<EMAIL>', '9167188292', 'California Strategies', 'Partner', '255 10th Avenue', 'San Francisco', 'California', '94118', 'United States', 'Male', 'Anthony Brunello', TRUE, '2025-06-05T07:27:29.287+00:00', '2025-06-05T07:27:29.376664+00:00', '2025-06-05T07:27:29.376664+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('42deffeb-8e3a-4773-bd82-f674a72a0631', 'b9702dc1-6eb5-4360-a14e-8677f60a9b07', 'Walter', 'Clemence', 'Walter Clemence', '<EMAIL>', '8503219492', 'Capital Power', 'Sr. Advisor- US Regulatory', '141 Seabreeze Circle', 'JUPITER', 'Florida', '33477', 'United States', 'Male', 'Walter Clemence', TRUE, '2025-06-05T07:27:29.739+00:00', '2025-06-05T07:27:29.845078+00:00', '2025-06-05T07:27:29.845078+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('afd5363a-2fa7-472b-a028-7a71f1be03c4', '9843631f-2bf2-4f67-b0b3-acb85f5ffd9a', 'Nuo', 'Tang', 'Nuo Tang', '<EMAIL>', '6195658864', 'Middle River Power', 'Director - Asset Management', '4350 Executive Drive, Suite 320', 'San Diego', 'California', '92121', 'United States', 'Male', 'Nuo Tang', TRUE, '2025-06-05T07:27:30.181+00:00', '2025-06-05T07:27:30.278928+00:00', '2025-06-05T07:27:30.278928+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('f6cff907-6762-4444-a7e9-fa8cd4435a11', '7d3c335a-9bc9-4d95-97d1-1ecd2c9cb692', 'Grant', 'Glazer', 'Grant Glazer', '<EMAIL>', '4028509381', 'MN8 Energy', 'Manager, Regulatory & Market Affairs', '1155 Avenue of the Americas', 'New York', 'New York', '10036', 'United States', 'Male', 'Grant Glazer', TRUE, '2025-06-05T07:27:30.631+00:00', '2025-06-05T07:27:30.719132+00:00', '2025-06-05T07:27:30.719132+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('0da43696-e4a4-44e6-948c-6a69ef616c77', '5ad9b809-21c2-4590-9cdf-af8b4cdf783c', 'Kathleen', 'Staks', 'Kathleen Staks', '<EMAIL>', '7209899745', 'Western Freedom', 'Executive Director', 'PO Box 150666', 'Lakewood', 'Colorado', '80215', 'United States', 'Female', 'Kathleen Staks', TRUE, '2025-06-05T07:27:31.048+00:00', '2025-06-05T07:27:31.13939+00:00', '2025-06-05T07:27:31.13939+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('6aa3220f-8348-44e0-875c-52f656eb82c0', '94fa2d1a-7d43-42f8-9842-d0d73e019923', 'Bryan', 'Long', 'Bryan Long', '<EMAIL>', '2122572023', 'Goldman Sachs & Co.', 'Vice President, Power Origination', '200 West Street', 'New York', 'New York', '10282', 'United States', 'Male', 'Bryan Long', TRUE, '2025-06-05T07:27:31.484+00:00', '2025-06-05T07:27:31.574552+00:00', '2025-06-05T07:27:31.574552+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a4cbcd86-6e6a-48a0-8396-00ab0c954f34', 'f406d858-b26e-41c3-aff2-e9bcedb66093', 'Vincent', 'Wiraatmadja', 'Vincent Wiraatmadja', '<EMAIL>', '8184453145', 'MCE (Marin Clean Energy)', 'Senior Legislative Manager', '813 Purple Sage Drive', 'Vacaville', 'California', '95687', 'United States', 'Male', 'Vince Wiraatmadja', TRUE, '2025-06-05T07:27:31.894+00:00', '2025-06-05T07:27:31.981139+00:00', '2025-06-05T07:27:31.981139+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('45f36d4a-4062-43d6-af29-dae5edbd620b', '1df8a19e-c75c-4dc8-91aa-1c0c9c8a8dc4', 'William', 'Paxton', 'William Paxton', '<EMAIL>', '9012186736', 'Hull Street Energy', 'Director - Origination', '4747 Bethesda Avenue Suite 1220', 'Bethesda', 'Maryland', '20814', 'United States', 'Male', 'William Paxton', TRUE, '2025-06-05T07:27:32.295+00:00', '2025-06-05T07:27:32.386049+00:00', '2025-06-05T07:27:32.386049+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('04c1ebac-f14b-4b12-afb7-ab49837053c1', 'b1e58237-826e-417a-aef4-16e837dd29da', 'Poonum', 'Agrawal', 'Poonum Agrawal', '<EMAIL>', '7035826082', 'Invenergy LLC', 'Senior Manager, Regulatory Affairs - West', 'One South Wacker Drive, Suite 1800', 'Chicago', 'Illinois', '94087', 'United States', 'Female', 'Poonum Agrawal', TRUE, '2025-06-05T07:27:32.692+00:00', '2025-06-05T07:27:32.7759+00:00', '2025-06-05T07:27:32.7759+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('638f7d95-96cd-4de3-b75b-f0acbdd5cd5c', '540c2c9e-95e8-401c-89c0-af9762d65aae', 'Brian', 'Theaker', 'Brian Theaker', '<EMAIL>', '5303203596', 'Middle River Power', 'VP Western Market and Regulatory Affairs', '3161 Ken Derek Lane', 'Placerville', 'California', '95667', 'United States', 'Male', 'Brian Theaker', TRUE, '2025-06-05T07:27:33.11+00:00', '2025-06-05T07:27:33.199606+00:00', '2025-06-05T07:27:33.199606+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('2f893ced-51d0-496d-a8b4-d5e43f145f7a', '184bcf42-0977-437f-902c-361635715dc8', 'Joanne', 'Bradley', 'Joanne Bradley', '<EMAIL>', '9169072771', 'LS Power', 'Transmission Regulatory Policy Manager', '101 Parkshore Drive', 'Folsom', 'California', '95630', 'United States', 'Female', 'Joanne Bradley', TRUE, '2025-06-05T07:27:34.513+00:00', '2025-06-05T07:27:34.599919+00:00', '2025-06-05T07:27:34.599919+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a24e5597-eb1f-4313-bc8b-6b57c5cb8483', 'e6576ded-6f0d-4f1a-9e5d-66b7d4394cff', 'Renae', 'Steichen', 'Renae Steichen', '<EMAIL>', '9259183295', 'REV Renewables', 'Senior Director of Regulatory Affairs', '2121 N California Blvd Suite 1000', 'Walnut Creek', 'California', '94596', 'United States', 'Female', 'Renae Steichen', TRUE, '2025-06-05T07:27:34.94+00:00', '2025-06-05T07:27:35.028106+00:00', '2025-06-05T07:27:35.028106+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('2c2b5ae9-e598-4ca6-ae73-031d65370fe0', '147eb542-c32f-4ac9-bbb1-639afab3ac5a', 'Keith', 'Martin', 'Keith Martin', '<EMAIL>', '2024150794', 'Norton Rose Fulbright US LLP', 'Partner', '799 Ninth Street, NW, Suite 1000', 'Washington', 'District of Columbia', '20001', 'United States', 'Male', 'Keith Martin', TRUE, '2025-06-05T07:27:35.34+00:00', '2025-06-05T07:27:35.450085+00:00', '2025-06-05T07:27:35.450085+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('08e74908-756a-4459-bdde-7f555991c7f9', 'c8303114-e100-42aa-a8d3-057ab33393c9', 'Tristan', 'Grimbert', 'Tristan Grimbert', '<EMAIL>', '8585213303', 'EDF Renewables', 'President & CEO', '15445 Innovation Drive', 'San Diego', 'California', '92128', 'United States', 'Male', 'Tristan Grimbert', TRUE, '2025-06-05T07:27:35.788+00:00', '2025-06-05T07:27:35.878649+00:00', '2025-06-05T07:27:35.878649+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('622a95eb-cd5b-47eb-8746-ea4d0bab0e7f', 'c293afc7-a59b-450a-9130-5acab1f979ef', 'Matthew', 'Langer', 'Matthew Langer', '<EMAIL>', '2137137012', 'Clean Power Alliance of Southern CA', 'Chief Operating Officer', '801 S Grand Avenue, Suite 400', 'Los Angeles', 'California', '90017', 'United States', 'Male', 'Matthew Langer', TRUE, '2025-06-05T07:27:36.198+00:00', '2025-06-05T07:27:36.286304+00:00', '2025-06-05T07:27:36.286304+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('fa2b61a2-0ef0-4b88-9e94-be09c13475c9', 'da3fdfe9-108d-44d8-a01c-df67146a4aff', 'Ravi', 'Sankaran', 'Ravi Sankaran', '<EMAIL>', '3105071294', 'Southwestern Power Group', 'Director of Business Development', '21818 S. Wilmington Avenue Suite 414', 'Long Beach', 'California', '90810', 'United States', 'Male', 'Ravi Sankaran', TRUE, '2025-06-05T07:27:36.619+00:00', '2025-06-05T07:27:36.713488+00:00', '2025-06-05T07:27:36.713488+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('06400780-fc17-4f4c-85b3-09725eb82b8b', '331bf4ab-b647-4b02-91fc-b6aa64d6c4ee', 'Christopher', 'Marelich', 'Christopher Marelich', '<EMAIL>', '9165205246', 'Downey Brand', 'Attorney', '621 Capitol Mall, 18th Floor', 'Sacramento', 'California', '95814', 'United States', 'Male', 'Christopher Marelich', TRUE, '2025-06-05T07:27:37.072+00:00', '2025-06-05T07:27:37.15619+00:00', '2025-06-05T07:27:37.15619+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('95fc292e-99ec-4967-8c4b-745398b40208', 'abcc7bfb-542f-4fe1-b518-039a312f7a2e', 'Matt', 'Fleiner', 'Matt Fleiner', '<EMAIL>', '7758577220', 'Onward Energy', 'Director, Asset Management', '1015 Crown Drive', 'Reno', 'Nevada', '89503', 'United States', 'Male', 'Matt Fleiner', TRUE, '2025-06-05T07:27:37.472+00:00', '2025-06-05T07:27:37.558348+00:00', '2025-06-05T07:27:37.558348+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('49c13af3-675c-435e-9601-c7bd494f891a', '7f75b7df-b539-4429-b4f1-2ca7088c4ad3', 'Adam', 'Pierce', 'Adam Pierce', '<EMAIL>', '8582867732', 'SDG&E', 'Vice President, Energy Procurement & Rates', '8330 Century Park Court', 'San Diego', 'California', '92123', 'United States', 'Male', 'Adam Pierce', TRUE, '2025-06-05T07:27:37.913+00:00', '2025-06-05T07:27:38.004547+00:00', '2025-06-05T07:27:38.004547+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a4ea102f-157c-430b-bcd3-ba635474c5f5', 'bc4babc5-b25e-4237-abcd-71b8d9e8b06c', 'Eric', 'Little', 'Eric Little', '<EMAIL>', '5109825620', 'CalCCA', 'Director of Regulatory Affairs', '1121 L Street, Suite 400', 'sacramento', 'California', '95814', 'United States', 'Male', 'Eric Little', TRUE, '2025-06-05T07:27:38.335+00:00', '2025-06-05T07:27:38.422163+00:00', '2025-06-05T07:27:38.422163+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('33d6a40a-dba7-4ca2-b8c2-d7a4f2ef41e9', 'bfc05c9f-a3f9-4e15-9b59-03b6d29d141c', 'Megan', 'Somogyi', 'Megan Somogyi', '<EMAIL>', '4158484800', 'Downey Brand', 'Partner', '455 Market Street, Suite 1500', 'San Francisco', 'California', '94105', 'United States', 'Female', 'Megan Somogyi', TRUE, '2025-06-05T07:27:39.77+00:00', '2025-06-05T07:27:39.861801+00:00', '2025-06-05T07:27:39.861801+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('24e2c2af-a454-4b06-8f0e-23f0a75ff3fa', '2a28fb8d-2f94-4b08-8fca-f8c7d808d910', 'Erin', 'Grizard', 'Erin Grizard', '<EMAIL>', '9162017944', 'Invenergy LLC', 'VP, Government Affairs', 'One South Wacker Drive', 'Chicago', 'Illinois', '60606', 'United States', 'Female', 'Erin Grizard', TRUE, '2025-06-05T07:27:40.179+00:00', '2025-06-05T07:27:40.271007+00:00', '2025-06-05T07:27:40.271007+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('010e9e53-3af6-4d10-8d5b-03f0c64b2eed', '19da7c13-78ae-4cc6-a11d-3db4e5f70d35', 'Siva', 'Gunda', 'Siva Gunda', '<EMAIL>', '9166281727', 'California Energy Commission', 'Vice Chair/Commissioner', '715 P Street', 'Sacramento', 'California', '95814', 'United States', 'Male', 'Siva Gunda', TRUE, '2025-06-05T07:27:40.595+00:00', '2025-06-05T07:27:40.684531+00:00', '2025-06-05T07:27:40.684531+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('77776510-594c-429e-a9aa-97e028a2cdbc', '6491830b-16be-420e-b473-6dc6237d981e', 'Lauren', 'Carr', 'Lauren Carr', '<EMAIL>', '9169908701', 'CalCCA', 'Senior Market Policy Analyst', '240 Natoma Station Drive Apt 285', 'Folsom', 'California', '95630', 'United States', 'Female', 'Lauren Carr', TRUE, '2025-06-05T07:27:41.02+00:00', '2025-06-05T07:27:41.109901+00:00', '2025-06-05T07:27:41.109901+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('aeb0d8ce-07a7-4014-8aec-ea7a7852401b', 'f48bbc75-64a3-4247-ad45-f0fbe3f3bdf6', 'Don', 'Howerton', 'Don Howerton', '<EMAIL>', '4156329015', 'Pacific Gas and Electric Company', 'Senior Director, Commercial Procurement', '300 Lakeside Drive', 'Oakland', 'California', '94512', 'United States', 'Male', 'Don Howerton', TRUE, '2025-06-05T07:27:41.427+00:00', '2025-06-05T07:27:41.516871+00:00', '2025-06-05T07:27:41.516871+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('71d545f1-88f1-4ec3-94ee-28638434b634', '475d31b1-f846-44b9-becb-d34bc9d59962', 'Caitlin', 'Liotiris', 'Caitlin Liotiris', '<EMAIL>', '8018913081', 'Western Power Trading Forum (WPTF)', 'Principal', '111 E Broadway', 'Salt Lake City', 'Utah', '84111', 'United States', 'Female', 'Caitlin Liotiris', TRUE, '2025-06-05T07:27:41.821+00:00', '2025-06-05T07:27:41.931481+00:00', '2025-06-05T07:27:41.931481+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('5b88cdb8-885e-415b-b9d1-283e915ae1bb', '65eb29e0-b62e-40ea-b76b-838abe0cd718', 'Lewis', 'Bichkoff', 'Lewis Bichkoff', '<EMAIL>', '4152352094', 'esVolta', 'Director of Development', '100 Bayview Circle, Suite 340', 'Newport Beach', 'California', '92660', 'United States', 'Male', 'Lewis Bichkoff', TRUE, '2025-06-05T07:27:42.252+00:00', '2025-06-05T07:27:42.337219+00:00', '2025-06-05T07:27:42.337219+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('27ce2e1a-2395-4614-98eb-6b1bf8f99d33', 'cc106401-7c91-45ab-b554-039cecdf33cf', 'Juan', 'Builes', 'Juan Builes', '<EMAIL>', '5108334106', 'esVolta', 'Sr Manager, Business Development', '828 Baker Street', 'San Francisco', 'California', '94115', 'United States', 'Male', 'Juan Felipe Builes', TRUE, '2025-06-05T07:27:42.651+00:00', '2025-06-05T07:27:42.73722+00:00', '2025-06-05T07:27:42.73722+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('7a45c2df-32a9-4554-8ab8-de5bf16cdac0', 'eadd314e-2c39-4d5a-948c-5a8d4fda853f', 'Kayla', 'Baum', 'Kayla Baum', '<EMAIL>', '6692301261', 'San Jose Clean Energy', 'Regulatory Policy Specialist', '200 E Santa Clara Street', 'San Jose', 'California', '95113', 'United States', 'Female', 'Kayla Baum', TRUE, '2025-06-05T07:27:43.05+00:00', '2025-06-05T07:27:43.13561+00:00', '2025-06-05T07:27:43.13561+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('08ee0811-e31c-43b0-995a-081564e21931', 'baef950f-abb3-4bbf-9cd5-538ff886e826', 'Brian', 'Cragg', 'Brian Cragg', '<EMAIL>', '4158484813', 'Downey Brand', 'Partner', '455 Market Street, Suite 1500', 'San Francisco', 'California', '94105', 'United States', 'Male', 'Brian Cragg', TRUE, '2025-06-05T07:27:43.51+00:00', '2025-06-05T07:27:43.602855+00:00', '2025-06-05T07:27:43.602855+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('f9141b43-03d1-4905-987c-82b7c1935073', 'a09d3585-437d-41f4-982e-5ff3b0952504', 'Carl', 'Steen', 'Carl Steen', '<EMAIL>', '2138925143', 'Dentons US LLP', 'Partner', '601 S. Figueroa Street, Suite 2500', 'Los Angeles', 'California', '90017', 'United States', 'Male', 'Carl R. Steen', TRUE, '2025-06-05T07:27:44.926+00:00', '2025-06-05T07:27:45.017909+00:00', '2025-06-05T07:27:45.017909+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('3368b13d-2afc-4f63-8243-79473941d473', '337788be-c991-4bcf-8786-56a6530b911c', 'Brian', 'Biering', 'Brian Biering', '<EMAIL>', '9164472166', 'Ellison Schneider Harris & Donlan LLP', 'Attorney', '2600 Capitol Avenue, Suite 400', 'Sacramento', 'California', '95816', 'United States', 'Male', 'Brian Biering', TRUE, '2025-06-05T07:27:45.351+00:00', '2025-06-05T07:27:45.443345+00:00', '2025-06-05T07:27:45.443345+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('311db1d9-25fe-4316-bd0c-133c8e604ef0', 'ccb0db19-d063-41ef-9428-fde975390f20', 'Jeff', 'Ghilardi', 'Jeff Ghilardi', '<EMAIL>', '6193061956', 'Linea Energy', 'Managing Director', '848 Inspiration Lane', 'Escondido', 'California', '92025', 'United States', 'Male', 'Jeff', TRUE, '2025-06-05T07:27:45.837+00:00', '2025-06-05T07:27:45.92149+00:00', '2025-06-05T07:27:45.92149+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('ddc727fd-190c-455f-8e77-03f75700c394', '94ddc256-9cc6-41d4-b89d-d7678521a392', 'Robert', 'Zdebski', 'Robert Zdebski', '<EMAIL>', '2134071468', 'Diamond Generating Corporation', 'Vice President Development', '633 W. 5th Street', 'Los Angeles', 'California', '90071', 'United States', 'Male', 'Bob Zdebski', TRUE, '2025-06-05T07:27:46.223+00:00', '2025-06-05T07:27:46.308539+00:00', '2025-06-05T07:27:46.308539+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('cb808d5c-2f0b-42f4-88bc-ccac80420b09', '80dbe29d-9bec-4ee1-9576-ed4fe9840f71', 'Sarah', 'Keane', 'Sarah Keane', '<EMAIL>', '3038257000', 'Kaplan Kirsch & Rockwell LLP', 'Lawyer', '1675 Broadway, Suite 2300', 'Denver', 'Colorado', '80202', 'United States', 'Female', 'Sarah Keane', TRUE, '2025-06-05T07:27:46.618+00:00', '2025-06-05T07:27:46.708627+00:00', '2025-06-05T07:27:46.708627+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('c945feba-5f4a-4eb2-808c-4853ad8b72df', 'c62f5aba-d419-4ba3-b905-2624d65cff89', 'Garet', 'Emmerson', 'Garet Emmerson', '<EMAIL>', '5305745502', 'Sierra Pacific Industries', 'Director of Technical Services', '19794 Riverside Avenue', 'Anderson', 'California', '96007', 'United States', 'Male', 'GARET R EMMERSON', TRUE, '2025-06-05T07:27:47.03+00:00', '2025-06-05T07:27:47.131306+00:00', '2025-06-05T07:27:47.131306+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('47ba3250-9125-4522-be12-9b4cbfa0093a', '5584d096-a523-4f4a-8e0c-fbebab101a69', 'Adam', 'Smith', 'Adam Smith', '<EMAIL>', '6264842117', 'Southern California Edison', 'Director', '2017-36TH Street', 'Sacramento', 'California', '95817', 'United States', 'Male', 'Adam Smith', TRUE, '2025-06-05T07:27:47.456+00:00', '2025-06-05T07:27:47.547968+00:00', '2025-06-05T07:27:47.547968+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('02ad1317-284a-4a8c-b835-a4d19f16f423', '1427084f-7596-42b6-b162-9952b7e8f95b', 'Kevin', 'Telford', 'Kevin Telford', '<EMAIL>', '7039012529', 'Hull Street Energy', 'Principal', '4747 Bethesda Avenue', 'Bethesda', 'Maryland', '20814', 'United States', 'Male', 'Kevin Telford', TRUE, '2025-06-05T07:27:47.875+00:00', '2025-06-05T07:27:47.964035+00:00', '2025-06-05T07:27:47.964035+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('272cdefe-e46a-406c-98e7-0894da023f52', '93b771d9-769c-4010-96b6-12371e898698', 'Frederick', 'Redell', 'Frederick Redell', '<EMAIL>', '4806193361', 'Atlantica Sustainable Infrastructure', 'Managing Director, North America', '1553 W. Todd Drive, Suite 204', 'Tempe', 'Arizona', '85283', 'United States', 'Male', 'Frederick Redell', TRUE, '2025-06-05T07:27:48.375+00:00', '2025-06-05T07:27:48.474129+00:00', '2025-06-05T07:27:48.474129+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('1fe2b5d8-92d0-436d-9f91-4142c413b9b4', '76f475a9-f171-4350-a360-65b09a926cad', 'Ravneet', 'Singh', 'Ravneet Singh', '<EMAIL>', '4806193361', 'Atlantica Sustainable Infrastructure', 'Director, U.S. Business Development', '1553 W. Todd Drive, Suite 204', 'Tempe', 'Arizona', '85283', 'United States', 'Male', 'Ravneet Singh', TRUE, '2025-06-05T07:27:48.788+00:00', '2025-06-05T07:27:48.879182+00:00', '2025-06-05T07:27:48.879182+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('9d70d236-455a-4182-ae32-78abb57d738a', 'f4d23e4b-0fc6-46f5-912a-c14e8abcd615', 'Christopher', 'Morris', 'Christopher Morris', '<EMAIL>', '4806193361', 'Atlantica Sustainable Infrastructure', 'U.S. General Counsel', '1553 W. Todd Drive, Suite 204', 'Tempe', 'Arizona', '85283', 'United States', 'Male', 'Christopher Morris', TRUE, '2025-06-05T07:27:50.216+00:00', '2025-06-05T07:27:50.307682+00:00', '2025-06-05T07:27:50.307682+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('72c960c5-74f0-4237-994a-9b755cf8c682', 'c4cdc931-812a-44e3-bd97-9258cd49c5e6', 'Treshia', 'Sewell', 'Treshia Sewell', '<EMAIL>', '4806193361', 'Atlantica Sustainable Infrastructure', 'Senior Policy & Regulatory Specialist', '1553 W. Todd Drive, Suite 204', 'Tempe', 'Alabama', '85283', 'United States', 'Female', 'Treshia Sewell', TRUE, '2025-06-05T07:27:50.635+00:00', '2025-06-05T07:27:50.72784+00:00', '2025-06-05T07:27:50.72784+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('522c3603-54ce-474c-ba1b-10ee496d455a', '5856dcc5-0ce2-49a9-ae60-4745cb9f47cc', 'Erin', 'Pulgar', 'Erin Pulgar', '<EMAIL>', '6263022509', 'Southern California Edison', 'Director, Portfolio, Planning, & Analysis', '2244 Walnut Grove Avenue, Q-1d', 'Rosemead', 'California', '91770', 'United States', 'Female', 'Erin P', TRUE, '2025-06-05T07:27:51.079+00:00', '2025-06-05T07:27:51.166469+00:00', '2025-06-05T07:27:51.166469+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('e2beb736-7caf-4c1a-92bb-4fcd8c57721a', 'e9bd3744-e43a-4a05-be6b-c5c69803f986', 'Marina', 'Pantchenko', 'Marina Pantchenko', '<EMAIL>', '8316417222', 'Central Coast Community Energy', 'Deputy General Counsel', '70 Garden Court, Suite 300', 'Monterey', 'California', '93940', 'United States', 'Female', 'Marina Pantchenko', TRUE, '2025-06-05T07:27:51.514+00:00', '2025-06-05T07:27:51.607962+00:00', '2025-06-05T07:27:51.607962+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('60ff898d-2f64-4074-9b5c-233577d089ed', '6e75d4e3-06af-4804-a22a-dedda4eb8d60', 'Catherine', 'Stedman', 'Catherine Stedman', '<EMAIL>', '8317370411', 'Central Coast Community Energy', 'Chief Communications Officer', '70 Garden Court, Suite 300', 'Monterey', 'California', '93940', 'United States', 'Female', 'Catherine Stedman', TRUE, '2025-06-05T07:27:51.984+00:00', '2025-06-05T07:27:52.072288+00:00', '2025-06-05T07:27:52.072288+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a89dfbc0-c263-49a7-a757-15615caecc68', '051b20da-e183-4f2e-ada5-4f5b11e5d5f8', 'Lindsay', 'Descagnia', 'Lindsay Descagnia', '<EMAIL>', '2132804011', 'Clean Power Alliance of Southern CA', 'VP, Power Supply', '801 S Grand Avenue, Suite 400', 'Los Angeles', 'California', '90017', 'United States', 'Female', 'Lindsay Descagnia', TRUE, '2025-06-05T07:27:52.376+00:00', '2025-06-05T07:27:52.479161+00:00', '2025-06-05T07:27:52.479161+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('edbecd39-c350-44af-8f6b-d535dc01e410', '9a43bdf0-ffb8-40d4-a9ea-0d96a5b44719', 'William', 'Walsh', 'William Walsh', '<EMAIL>', '5625470093', 'Southern California Edison', 'Vice President, Energy Procurement & Mnagement', '2244 Walnut Grove Avenue', 'Rosemead', 'California', '91770', 'United States', 'Male', 'Bill Walsh', TRUE, '2025-06-05T07:27:52.77+00:00', '2025-06-05T07:27:52.855245+00:00', '2025-06-05T07:27:52.855245+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('88aed8f9-af5f-4eab-82bc-bdd22e591e1f', '0908c6a4-d1db-4184-89bb-5c9ef40df25d', 'Saeed', 'Farrokhpay', 'Saeed Farrokhpay', '<EMAIL>', '9162940322', 'FERC', 'Energy Industry Analyst', '888 First Street NE', 'Washington', 'District of Columbia', '20426', 'United States', 'Male', 'Saeed Farrokhpay', TRUE, '2025-06-05T07:27:53.187+00:00', '2025-06-05T07:27:53.274546+00:00', '2025-06-05T07:27:53.274546+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('6bbf9468-7efd-4486-bc2a-8189620b6d3a', '3e3153ed-de5f-4440-bde2-bcdbc98488dd', 'Jeffery', 'Harris', 'Jeffery Harris', '<EMAIL>', '9164472166', 'Ellison Schneider Harris & Donlan LLP', 'Partner', '2600 Capitol Avenue, Suite 400', 'Sacramento', 'California', '95816', 'United States', 'Male', 'Jeff', TRUE, '2025-06-05T07:27:53.623+00:00', '2025-06-05T07:27:53.710364+00:00', '2025-06-05T07:27:53.710364+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('286bca12-bc1a-4df9-9624-6a20a6430ba3', 'd25df4cf-3caf-4693-8455-7d6f5632ed1b', 'Courtney', 'Krause', 'Courtney Krause', '<EMAIL>', '3039814352', 'Onward Energy', 'Assistant General Counsel, Regulatory', '600 Seventeenth Street, Suite 2400S', 'Denver', 'Colorado', '80202', 'United States', 'Female', 'Courtney Krause', TRUE, '2025-06-05T07:27:54.041+00:00', '2025-06-05T07:27:54.141043+00:00', '2025-06-05T07:27:54.141043+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a1dacad9-264d-48e3-abba-2c98e02b42ea', '57c3c738-7651-4028-9475-886a13232e60', 'Monica', 'Padilla', 'Monica Padilla', '<EMAIL>', '4087215301', 'Silicon Valley Clean Energy', 'Chief Executive Officer', '333 W. El Camino Real, Suite 330', 'Sunnyvale', 'California', '94087', 'United States', 'Female', 'Monica Padilla', TRUE, '2025-06-05T07:27:55.461+00:00', '2025-06-05T07:27:55.553624+00:00', '2025-06-05T07:27:55.553624+00:00');
INSERT INTO iepa_user_profiles (id, user_id, first_name, last_name, full_name, email, phone_number, organization, job_title, street_address, city, state, zip_code, country, gender, preferred_name_on_badge, imported_from_2024, import_date, created_at, updated_at) VALUES ('a1e5f162-8010-4611-a099-f1608756a527', '595398af-4d45-4845-9546-6772c0e06163', 'John', 'Tester', 'John Tester', '<EMAIL>', '5551234567', 'Clean Energy Solutions Inc.', 'Senior Environmental Engineer', '123 Green Energy Blvd', 'Sacramento', 'CA', '95814', 'United States', 'male', 'John T.', FALSE, NULL, '2025-06-06T16:56:37.689634+00:00', '2025-06-06T16:56:37.689634+00:00');

-- iepa_organizations (10 rows)
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('a2c1a9ad-e0ea-465a-892c-a77223107e99', 'Clean Energy Solutions Inc.', 'clean energy solutions inc.', 'Renewable Energy', NULL, NULL, TRUE, 5, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('123b6c03-dbae-4a9d-938a-d73cd3060cc7', 'Pacific Power Partners', 'pacific power partners', 'Energy Trading', NULL, NULL, TRUE, 3, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('e8d45973-2e02-4de8-834b-893cf46f045b', 'California Independent Energy', 'california independent energy', 'Power Generation', NULL, NULL, TRUE, 8, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('215558ef-4d45-4162-a6e9-cbbb26793b34', 'Green Valley Energy', 'green valley energy', 'Solar Power', NULL, NULL, TRUE, 2, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('27f7050f-6cfd-4865-a6c6-f4fa338c23f6', 'West Coast Wind Power', 'west coast wind power', 'Wind Energy', NULL, NULL, TRUE, 4, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('5dee5b37-9ccb-4100-abc5-68882561bde4', 'Independent Energy Producers Association', 'independent energy producers association', 'Trade Association', NULL, NULL, TRUE, 10, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('7a4e31d8-e64d-4eee-8c44-8b0de7942175', 'Solar Solutions LLC', 'solar solutions llc', 'Solar Power', NULL, NULL, TRUE, 6, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('5e3dab94-54a8-4dc6-854a-2ad77cb785e6', 'Energy Storage Systems', 'energy storage systems', 'Battery Storage', NULL, NULL, TRUE, 1, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('2e90062a-6ac8-461a-afc1-9d9d62486bbb', 'Renewable Resources Corp', 'renewable resources corp', 'Renewable Energy', NULL, NULL, TRUE, 7, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');
INSERT INTO iepa_organizations (id, name, normalized_name, industry, description, website_url, is_active, usage_count, last_used_at, created_at, updated_at) VALUES ('b738e3d3-4a47-4ea5-b71f-a74c8b8cada8', 'California Energy Commission', 'california energy commission', 'Government', NULL, NULL, TRUE, 3, NULL, '2025-06-06T19:09:13.308105+00:00', '2025-06-06T19:09:13.308105+00:00');

-- iepa_historical_registrations (109 rows)
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('1edef54c-b86a-4aa7-8663-e21183a2582e', '14445446-8d3f-4a51-9119-3e0030c9389c', '70f7d3a8-5e07-43e3-a772-2333fea615cf', 2024, 'IEPA Annual Meeting 2024', '2024-09-22T17:47:21+00:00', 'Non IEPA Member', NULL, 'draft', 'Nate', NULL, ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'None', FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":94117,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"draft","Street":"1331 Masonic Ave","Country":null,"Date Added":"2024-09-22 17:47:21","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Nate","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Director","Attendee Last Name":"Miller","Attendee First Name":"Nathan","Attendee Type (IEPA)":null,"Attendee Organization":"E3","Special Dietary Needs":"None","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":null,"Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:26:59.988937+00:00', '2025-06-05T07:26:59.988937+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('2e6666b1-1ada-4f98-91cc-164a59f42814', '9b537cd2-f875-447b-96b3-fa8f95f2f54b', '5117f6b7-a841-44c7-94e1-a8566a47e370', 2024, 'IEPA Annual Meeting 2024', '2024-09-19T10:35:39+00:00', 'Non IEPA Member', NULL, 'publish', 'Maia Leroy', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'None', FALSE, 'No rental needed', NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":95814,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"1100 11th Street Suite 311","Country":null,"Date Added":"2024-09-19 10:35:39","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Maia Leroy","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Policy Director","Attendee Last Name":"Leroy","Attendee First Name":"Maia","Attendee Type (IEPA)":null,"Attendee Organization":"CEERT","Special Dietary Needs":"None","Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:00.473145+00:00', '2025-06-05T07:27:00.473145+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('776bfed0-0e43-4fa3-b070-985324a0ae8a', 'c6b963a2-148a-4e5a-81ec-e6017e52cba5', '8438d39d-3ab2-47cd-904f-aea8de13b4fa', 2024, 'IEPA Annual Meeting 2024', '2024-09-17T16:39:48+00:00', 'Non IEPA Member', NULL, 'publish', 'Carol Denning', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'None', FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":95926,"City":"Chico","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"789 Downing Avenue","Country":null,"Date Added":"2024-09-17 16:39:48","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Carol Denning","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"President","Attendee Last Name":"Denning","Attendee First Name":"Carol","Attendee Type (IEPA)":null,"Attendee Organization":"Energy Pathways","Special Dietary Needs":"None","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:00.873599+00:00', '2025-06-05T07:27:00.873599+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('e315fb9a-8aca-49f5-a19b-3f4a8dbbe527', 'aadb84e0-46b1-448a-9f79-102f36abd36b', '33ca3f44-784a-40a2-846b-0e1cbbb05da4', 2024, 'IEPA Annual Meeting 2024', '2024-09-17T11:47:31+00:00', 'Non IEPA Member', NULL, 'draft', 'Fanny Kidwell', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":89523,"City":"Reno","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Nevada","Gender":"Female","Status":"draft","Street":"435 Gooseberry Drive","Country":null,"Date Added":"2024-09-17 11:47:31","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Fanny Kidwell","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"VP Finance & Corporate Development","Attendee Last Name":"Kidwell Langlois","Attendee First Name":"Fanny","Attendee Type (IEPA)":null,"Attendee Organization":"Viridon","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:01.273075+00:00', '2025-06-05T07:27:01.273075+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('4f1f95b0-1e63-4bd5-a68f-6dccd40f16e5', 'a439431a-af56-434a-be47-a676dab84483', '987fe873-3bfb-4dc7-b014-ca19ad9439b0', 2024, 'IEPA Annual Meeting 2024', '2024-09-17T11:34:29+00:00', 'Non IEPA Member', NULL, 'publish', 'Alex Jackson', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, TRUE, 'No rental needed', NULL, 0, 2850, 'completed', '2024-attendee-export', '{"Zip":95818,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"2733 6th Avenue","Country":null,"Date Added":"2024-09-17 11:34:29","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Alex Jackson","Golf Tournament?":"Yes","Grand order total":2850,"Attendee Job Title":"Executive Director","Attendee Last Name":"Jackson","Attendee First Name":"Alex","Attendee Type (IEPA)":null,"Attendee Organization":"American Clean Power - California","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:01.744328+00:00', '2025-06-05T07:27:01.744328+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('7ee04938-5c64-4a2b-8904-1771d780932f', 'c443989a-5dc9-4161-acba-694505a95dc5', '35febe6f-5dd7-4743-9a54-3bfcff1e9676', 2024, 'IEPA Annual Meeting 2024', '2024-09-17T09:43:47+00:00', 'Non IEPA Member', NULL, 'publish', 'Alex Morris', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":95814,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24","State":"California","Gender":"Male","Status":"publish","Street":"901 H Street, Suite 120 PMB 157","Country":null,"Date Added":"2024-09-17 09:43:47","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Alex Morris","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"General Manager","Attendee Last Name":"Morris","Attendee First Name":"Alex","Attendee Type (IEPA)":null,"Attendee Organization":"California Community Power (CC Power)","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:02.145832+00:00', '2025-06-05T07:27:02.145832+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('0f462143-a2a6-4e86-83ff-cae6ed5b19f3', 'b10e9855-de9a-41cc-a49b-6f6338725a01', 'a6043ada-e249-453b-a8df-66671a58d17f', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T22:12:22+00:00', 'Non IEPA Member', NULL, 'publish', 'Dean', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, TRUE, 'Right', NULL, 0, 2920, 'completed', '2024-attendee-export', '{"Zip":80202,"City":"Denver","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Alabama","Gender":"Male","Status":"publish","Street":"1125 17th Street","Country":null,"Date Added":"2024-09-16 22:12:22","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Dean","Golf Tournament?":"Yes","Grand order total":2920,"Attendee Job Title":"VP, Origination (North America)","Attendee Last Name":"Tuel","Attendee First Name":"Dean","Attendee Type (IEPA)":null,"Attendee Organization":"Hydrostor","Special Dietary Needs":null,"Are you renting clubs?":"Right","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:02.52533+00:00', '2025-06-05T07:27:02.52533+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('8eea2df3-2295-4055-a6d6-51592e383cd5', 'ee18122c-99b8-4adf-87a2-ccaf2ff52572', 'f0c7385c-93a1-4cb3-af41-a98b8a6fba2d', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T21:08:02+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Raja Ramesh', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24'], 'Does not eat beef/red meat, but otherwise okay with other meat (chicken/fish)', FALSE, NULL, NULL, 0, 2000, 'completed', '2024-attendee-export', '{"Zip":95814,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24","State":"California","Gender":"Male","Status":"publish","Street":"715 P Street","Country":null,"Date Added":"2024-09-16 21:08:02","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Raja Ramesh","Golf Tournament?":"No","Grand order total":2000,"Attendee Job Title":"Senior Policy Advisor to Vice Chair Gunda","Attendee Last Name":"Ramesh","Attendee First Name":"Raja","Attendee Type (IEPA)":null,"Attendee Organization":"California Energy Commission","Special Dietary Needs":"Does not eat beef/red meat, but otherwise okay with other meat (chicken/fish)","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:02.920891+00:00', '2025-06-05T07:27:02.920891+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('17292959-070f-4708-b68e-15c35fb7f006', '9e1871bc-541c-42c8-9ae1-241cfe0cdee7', 'b384f8d1-465e-4f15-b91a-8f87248d59ec', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T21:06:27+00:00', 'Non IEPA Member', NULL, 'publish', 'Dan Handal', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":94111,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"One California Street, Suite 1600","Country":null,"Date Added":"2024-09-16 21:06:27","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Dan Handal","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Senior Director","Attendee Last Name":"Handal","Attendee First Name":"Daniel","Attendee Type (IEPA)":null,"Attendee Organization":"NextEra Energy Resources","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:04.330813+00:00', '2025-06-05T07:27:04.330813+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('d448efba-b8e1-4ab6-8837-1677a14bd474', '4b70de52-9c59-47b8-b20e-cf71bdff59b4', '94c70dda-be19-4640-92c5-c95fca34e30e', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T15:26:17+00:00', 'Sponsor Attendee', NULL, 'publish', 'Julie', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, TRUE, 'Right', NULL, 270, 270, 'completed', '2024-attendee-export', '{"Zip":80202,"City":"Denver","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Colorado","Gender":"Female","Status":"publish","Street":"1125 17th Street","Country":null,"Date Added":"2024-09-16 15:26:17","Golf Total":270,"Attendee Type":"Sponsor Attendee","Name on Badge":"Julie","Golf Tournament?":"Yes","Grand order total":270,"Attendee Job Title":"VP Origination, Commercial & Industrial","Attendee Last Name":"Thompson","Attendee First Name":"Julie","Attendee Type (IEPA)":null,"Attendee Organization":"Hydrostor","Special Dietary Needs":null,"Are you renting clubs?":"Right","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:04.732611+00:00', '2025-06-05T07:27:04.732611+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('2157130d-3011-4af0-bdeb-9ac4eaf8540a', '8fa66175-a4d8-41ce-af11-2a553ca0d55d', '5cc35024-a2dc-461b-abd4-e7909f67bc61', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T13:24:57+00:00', 'IEPA Member', 'IEPA-Board-Member', 'publish', 'Greg Contreras', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":95811,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"650 Bercut Drive, Suite C","Country":null,"Date Added":"2024-09-16 13:24:57","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Greg Contreras","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Counsel","Attendee Last Name":"Contreras","Attendee First Name":"Greg","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"Wellhead Electric Company","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:05.152134+00:00', '2025-06-05T07:27:05.152134+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('85487758-e350-411c-b3f7-95e16e4e3cd6', '65de69c0-485f-4744-9c5d-3d668330d2cf', '58982323-8074-49e1-8cbf-fea4cf3dd689', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T11:41:00+00:00', 'Sponsor Attendee', NULL, 'publish', 'Jason Armenta', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25'], NULL, FALSE, 'No rental needed', NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":77002,"City":"Houston","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25","State":"Texas","Gender":"Male","Status":"publish","Street":"717 Texas Avenue","Country":null,"Date Added":"2024-09-16 11:41:00","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Jason Armenta","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"VP of Power Trading","Attendee Last Name":"Armenta","Attendee First Name":"Jason","Attendee Type (IEPA)":null,"Attendee Organization":"Calpine Corporation","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:05.555638+00:00', '2025-06-05T07:27:05.555638+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('9432c6c9-56d9-44db-8688-dd757d7d5ad5', '0d96e55d-0b98-49d6-b346-500c9aa9488d', '40ee26bd-940b-4f57-8b7b-893e1be4878d', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T11:40:29+00:00', 'Sponsor Attendee', NULL, 'publish', 'Monica Garcia', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":90803,"City":"Long Beach","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25","State":"California","Gender":"Female","Status":"publish","Street":"690 N. Studebaker Road","Country":null,"Date Added":"2024-09-16 11:40:29","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Monica Garcia","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"External Affairs / Stakeholder Relations","Attendee Last Name":"Garcia","Attendee First Name":"Monica","Attendee Type (IEPA)":null,"Attendee Organization":"The AES Corporation","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:06.002249+00:00', '2025-06-05T07:27:06.002249+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('71b9f9dc-ea1a-452d-80ad-7ebbbb4c2331', 'ace9a1a6-ab5d-423f-bfa1-e0a40a61940f', 'aa75dfe6-5970-42b7-9cf4-8923d5827278', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T11:39:18+00:00', 'Speaker', NULL, 'publish', 'Beth Vaughan', 'Not staying any nights.', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94521,"City":"Concord","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24","State":"California","Gender":"Female","Status":"publish","Street":"4391 N Marsh Elder Court","Country":null,"Date Added":"2024-09-16 11:39:18","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Beth Vaughan","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"CEO","Attendee Last Name":"Vaughan","Attendee First Name":"Beth","Attendee Type (IEPA)":null,"Attendee Organization":"CalCCA","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Not staying any nights.","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:06.375412+00:00', '2025-06-05T07:27:06.375412+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('b7f604cd-bc36-4e2c-ab7e-e43612c52d42', '449daea0-50f4-47c6-84a4-aca41aa13063', '4f0d0ad6-6129-486c-9984-eb1c06e13452', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T11:38:26+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Dai Owen', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":94612,"City":"Oakland","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"1999 Harrison Street, Suite 675","Country":null,"Date Added":"2024-09-16 11:38:26","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Dai Owen","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"SVP Origination & Power Marketing","Attendee Last Name":"Owen","Attendee First Name":"Dai","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"EDF Renewables","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:06.776672+00:00', '2025-06-05T07:27:06.776672+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('82cbdf14-f4ce-4c36-b7a1-7a59e6cc16c1', '7311130c-e448-409c-a878-59833784deef', '4cd0c9e9-6fb0-46ee-beb7-d0672ecc83ef', 2024, 'IEPA Annual Meeting 2024', '2024-09-16T11:38:11+00:00', 'Speaker', NULL, 'publish', 'Byron Vosburg', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 1200, 'completed', '2024-attendee-export', '{"Zip":92106,"City":"San Diego","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"2305 Historic Decatur Road","Country":null,"Date Added":"2024-09-16 11:38:11","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Byron Vosburg","Golf Tournament?":"No","Grand order total":1200,"Attendee Job Title":"Chief Commercial Officer","Attendee Last Name":"Vosburg","Attendee First Name":"Byron","Attendee Type (IEPA)":null,"Attendee Organization":"San Diego Community Power","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:07.143038+00:00', '2025-06-05T07:27:07.143038+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('f4d81657-b2d5-4178-988a-9122ae1027cc', '6eaaa76e-e894-4128-8e54-2795ed5ee40d', '6ab707a7-0455-4f8a-9871-1ee58624792b', 2024, 'IEPA Annual Meeting 2024', '2024-09-12T16:15:21+00:00', 'IEPA Member', NULL, 'publish', 'Bob Mitchell', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, TRUE, 'Right', NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":20003,"City":"Washington","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"District of Columbia","Gender":"Male","Status":"publish","Street":"307 3rd Street, SE","Country":null,"Date Added":"2024-09-12 16:15:21","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Bob Mitchell","Golf Tournament?":"Yes","Grand order total":2300,"Attendee Job Title":"Co-Founding Partner","Attendee Last Name":"Mitchell","Attendee First Name":"Bob","Attendee Type (IEPA)":null,"Attendee Organization":"TRED","Special Dietary Needs":null,"Are you renting clubs?":"Right","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:07.506014+00:00', '2025-06-05T07:27:07.506014+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('8a976829-2f68-420a-acfb-c6426e882e2c', '2db8e029-cdab-431b-aa07-063ec1905ce6', '6535ed3c-8ed3-4112-8ba5-e3813ce68eaa', 2024, 'IEPA Annual Meeting 2024', '2024-09-12T13:20:32+00:00', 'Non IEPA Member', NULL, 'publish', 'Rachel McMahon', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25'], NULL, FALSE, 'No rental needed', NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":95814,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25","State":"California","Gender":"Female","Status":"publish","Street":"808 R Street, #209","Country":null,"Date Added":"2024-09-12 13:20:32","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Rachel McMahon","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Vice President, Policy","Attendee Last Name":"McMahon","Attendee First Name":"Rachel","Attendee Type (IEPA)":null,"Attendee Organization":"California Energy Storage Alliance","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:07.902655+00:00', '2025-06-05T07:27:07.902655+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('f4821810-d47a-4d6a-bf00-0ad7997f9f9b', 'be0012dd-b2c2-4e2c-9c8f-47cc0de2f1d6', 'b146ab2c-4fd8-4097-ae80-3f2c1b23e228', 2024, 'IEPA Annual Meeting 2024', '2024-09-11T21:25:12+00:00', 'Non IEPA Member', NULL, 'publish', 'Adam Hatefi', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":95610,"City":"Citrus Heights","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"8304 Cranford Way","Country":null,"Date Added":"2024-09-11 21:25:12","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Adam Hatefi","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Senior Associate","Attendee Last Name":"Hatefi ","Attendee First Name":"Adam","Attendee Type (IEPA)":null,"Attendee Organization":"Gridwell Consulting","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:09.312755+00:00', '2025-06-05T07:27:09.312755+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('8a1eac10-07ce-4963-8dcf-46c32b4e9785', '612d3355-a997-4403-b03c-281de540138a', '55feec3e-13d8-4aaa-8334-19f71c956cd5', 2024, 'IEPA Annual Meeting 2024', '2024-09-11T16:19:13+00:00', 'IEPA Member', NULL, 'publish', 'Emily Singer', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":50312,"City":"Des Moines","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Iowa","Gender":"Female","Status":"publish","Street":"4020 Ingersoll Avenue","Country":null,"Date Added":"2024-09-11 16:19:13","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Emily Singer","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Vice President of Regulatory Affairs","Attendee Last Name":"Singer","Attendee First Name":"Emily","Attendee Type (IEPA)":null,"Attendee Organization":"BHE Renewables, LLC","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:09.697405+00:00', '2025-06-05T07:27:09.697405+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('ee6dab28-58a8-43d4-a16b-ca9608bd605a', 'ab661b9c-0d31-475b-a252-ea0365c1b27d', 'a5d3863b-a63b-4332-ad2a-7869f0d77087', 2024, 'IEPA Annual Meeting 2024', '2024-09-11T16:19:13+00:00', 'Non IEPA Member', NULL, 'publish', 'Mark Wiranowski', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":80211,"City":"Denver","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Colorado","Gender":"Male","Status":"publish","Street":"2138 W 32nd Avenue","Country":null,"Date Added":"2024-09-11 16:19:13","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Mark Wiranowski","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Partner","Attendee Last Name":"Wiranowski","Attendee First Name":"Mark","Attendee Type (IEPA)":null,"Attendee Organization":"Wilkinson Barker Knauer LLP","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:10.087752+00:00', '2025-06-05T07:27:10.087752+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('9d40c3dd-3337-4b93-a82f-9056229c59e7', '04989d07-0451-43e4-b583-b00c9520c732', '4879176d-8bd8-4170-b69a-9e575ab4a554', 2024, 'IEPA Annual Meeting 2024', '2024-09-11T16:11:07+00:00', 'Speaker', NULL, 'publish', 'Varner', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'No Pork', FALSE, NULL, NULL, 0, 1200, 'completed', '2024-attendee-export', '{"Zip":97206,"City":"Portland","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Oregon","Gender":"Male","Status":"publish","Street":"5616 SE Tolman Street","Country":null,"Date Added":"2024-09-11 16:11:07","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Varner","Golf Tournament?":"No","Grand order total":1200,"Attendee Job Title":"Director of Legislative & Regulatory Affairs","Attendee Last Name":"Seaman","Attendee First Name":"Varner","Attendee Type (IEPA)":null,"Attendee Organization":"Pattern Energy","Special Dietary Needs":"No Pork","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:10.475211+00:00', '2025-06-05T07:27:10.475211+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('4dbbe6c6-75ee-4abe-852c-afad586f491f', '638bbe89-6bbd-4426-aecb-a6d8b0017f1c', '054898cf-06d4-468b-ab31-e96abd89ef0a', 2024, 'IEPA Annual Meeting 2024', '2024-09-11T15:44:38+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Dan', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, TRUE, 'No rental needed', NULL, 0, 2500, 'completed', '2024-attendee-export', '{"Zip":60606,"City":"Chicago","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Illinois","Gender":"Male","Status":"publish","Street":"1 South Wacker Drive","Country":null,"Date Added":"2024-09-11 15:44:38","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Dan","Golf Tournament?":"Yes","Grand order total":2500,"Attendee Job Title":"SVP, Offshore Wind","Attendee Last Name":"Runyan","Attendee First Name":"Daniel","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Invenergy LLC","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:10.845993+00:00', '2025-06-05T07:27:10.845993+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('db1fe119-77b2-4664-b98c-57d044fa3db2', 'd94ae139-de4d-4bd4-a87d-57105bf8e07e', '1c54ecfd-629f-4fa9-b7e4-e23a67317ba7', 2024, 'IEPA Annual Meeting 2024', '2024-09-11T13:09:38+00:00', 'IEPA Member', NULL, 'publish', 'Robin Smutny-Jones', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, 'No rental needed', NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":95630,"City":"Folsom","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"604 Sutter Street, Suite 250","Country":"United States","Date Added":"2024-09-11 13:09:38","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Robin Smutny-Jones","Golf Tournament?":"No","Grand order total":null,"Attendee Job Title":"Consultant","Attendee Last Name":"Smutny-Jones","Attendee First Name":"Robin","Attendee Type (IEPA)":null,"Attendee Organization":"ZGlobal","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:11.254386+00:00', '2025-06-05T07:27:11.254386+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('7eec4365-7157-4a87-ac1e-01560af3e229', '49a33f52-81ee-494b-b574-d7ab0a8abda7', 'a4b06e0c-2395-4298-aa81-433d39c0cfb5', 2024, 'IEPA Annual Meeting 2024', '2024-09-11T12:38:56+00:00', 'Adult Guest', NULL, 'publish', 'Kyra Millich', 'Tuesday 9/24', ARRAY['3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Prefers vegetarian/pescatarian', FALSE, 'No rental needed', NULL, 0, 500, 'completed', '2024-attendee-export', '{"Zip":94707,"City":"Kensington","Meals":"3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"633 Coventry Road","Country":null,"Date Added":"2024-09-11 12:38:56","Golf Total":null,"Attendee Type":"Adult Guest","Name on Badge":"Kyra Millich","Golf Tournament?":"No","Grand order total":500,"Attendee Job Title":"Major Gifts Officer","Attendee Last Name":"Millich","Attendee First Name":"Kyra","Attendee Type (IEPA)":null,"Attendee Organization":"National Multiple Sclerosis Society","Special Dietary Needs":"Prefers vegetarian/pescatarian","Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Tuesday 9/24","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:11.65459+00:00', '2025-06-05T07:27:11.65459+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('d3b484e7-3e36-4e35-b978-1d77eb19366a', '10e1caf3-f74d-4bcc-8ddc-e809d722fe89', '448ddcc8-2d5f-4180-b081-eb5fa9e95ed9', 2024, 'IEPA Annual Meeting 2024', '2024-09-10T16:36:47+00:00', 'Staff', 'IEPA-Board-Member', 'publish', 'Sara Fitzsimon', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegetarian', TRUE, 'No rental needed', NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":95683,"City":"Sloughhouse","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"PO Box 1287","Country":"United States","Date Added":"2024-09-10 16:36:47","Golf Total":null,"Attendee Type":"Staff","Name on Badge":"Sara Fitzsimon","Golf Tournament?":"Yes","Grand order total":null,"Attendee Job Title":"Policy Director","Attendee Last Name":"Fitzsimon","Attendee First Name":"Sara","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"Independent Energy Producers Association","Special Dietary Needs":"Vegetarian","Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:12.026233+00:00', '2025-06-05T07:27:12.026233+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('fd6788ad-1285-46fb-97ca-ed36ce5d8b57', 'f57c55d1-4c36-451f-a0b4-8795fc95c2cb', 'dec8d6f9-3aa0-4cdf-b66a-dba1e03ae926', 2024, 'IEPA Annual Meeting 2024', '2024-09-10T16:31:25+00:00', 'Staff', 'IEPA-Board-Member', 'publish', 'Jan Smutny-Jones', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, TRUE, 'Right', NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":95683,"City":"Sloughhouse","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"PO Box 1287","Country":"United States","Date Added":"2024-09-10 16:31:25","Golf Total":null,"Attendee Type":"Staff","Name on Badge":"Jan Smutny-Jones","Golf Tournament?":"Yes","Grand order total":null,"Attendee Job Title":"CEO","Attendee Last Name":"Smutny-Jones","Attendee First Name":"Jan","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"Independent Energy Producers Association","Special Dietary Needs":null,"Are you renting clubs?":"Right","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:12.499842+00:00', '2025-06-05T07:27:12.499842+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('9dd76de8-3551-47fc-91d7-68283abe834c', '2e827e40-1bf4-45e6-b4c5-1c480d17333f', 'a976c390-21be-4549-b50c-37db4cd6a873', 2024, 'IEPA Annual Meeting 2024', '2024-09-10T16:27:35+00:00', 'Staff', 'IEPA-Board-Member', 'publish', 'Jamie Parker', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, 'No rental needed', NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":95683,"City":"Sloughhouse","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"PO Box 1287","Country":"United States","Date Added":"2024-09-10 16:27:35","Golf Total":null,"Attendee Type":"Staff","Name on Badge":"Jamie Parker","Golf Tournament?":"No","Grand order total":null,"Attendee Job Title":"Administrator","Attendee Last Name":"Parker","Attendee First Name":"Jamie","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"Independent Energy Producers Association","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:12.90161+00:00', '2025-06-05T07:27:12.90161+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('0d404e97-581c-4cd5-92a7-e448f8fd1dff', '09f90adc-bac3-42ff-bb31-c0fab9ad549b', '30377521-6ca0-49a7-95ef-30ef30bf727a', 2024, 'IEPA Annual Meeting 2024', '2024-09-10T13:20:27+00:00', 'Non IEPA Member', NULL, 'publish', 'Adam R. Weber', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":10022,"City":"New York","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"New York","Gender":"Male","Status":"publish","Street":"850 3rd Avenue","Country":null,"Date Added":"2024-09-10 13:20:27","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Adam R. Weber","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Principal","Attendee Last Name":"Weber","Attendee First Name":"Adam","Attendee Type (IEPA)":null,"Attendee Organization":"Metropolitan Partners Group","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:14.290701+00:00', '2025-06-05T07:27:14.290701+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('80cf2d4b-ecb9-4ea0-a6d3-0d48ec90b5a3', '3d9577c8-5128-467d-ac92-c90a73087587', '61644a27-b80b-49c4-b492-4832972515a3', 2024, 'IEPA Annual Meeting 2024', '2024-09-10T12:12:19+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Markian Melnyk', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":20740,"City":"College Park","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Maryland","Gender":"Male","Status":"publish","Street":"9011 Gettysburg Lane","Country":null,"Date Added":"2024-09-10 12:12:19","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Markian Melnyk","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Principal","Attendee Last Name":"Melnyk","Attendee First Name":"Markian","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Three Rivers Energy Development LLC","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:14.670172+00:00', '2025-06-05T07:27:14.670172+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('5afaeae4-4aa7-41e2-af26-31a748163ea2', 'd68bc4bb-5588-4066-a0ba-d8639f94eb68', 'b7e2a204-f91c-459c-bcfd-8f24272e2cab', 2024, 'IEPA Annual Meeting 2024', '2024-09-10T12:11:37+00:00', 'Non IEPA Member', NULL, 'publish', 'Matt Haley', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":78702,"City":"Austin","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Texas","Gender":"Male","Status":"publish","Street":"1401 E 6th Street, Suite 400","Country":null,"Date Added":"2024-09-10 12:11:37","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Matt Haley","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Sr Manager, Commercial","Attendee Last Name":"Haley","Attendee First Name":"Matthew","Attendee Type (IEPA)":null,"Attendee Organization":"RWE","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:15.172583+00:00', '2025-06-05T07:27:15.172583+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('2c8d993c-bcfa-43e0-a5a6-2f78ae5bafc3', '94cd472e-5b0e-4f11-8984-d6e6f5907659', '9110f24a-6684-4b4c-b1a1-303c76ad2733', 2024, 'IEPA Annual Meeting 2024', '2024-09-10T10:48:21+00:00', 'Speaker', NULL, 'publish', 'Nick Dominguez', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 1200, 'completed', '2024-attendee-export', '{"Zip":93940,"City":"Monterey","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"70 Garden Court, #300","Country":null,"Date Added":"2024-09-10 10:48:21","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Nick Dominguez","Golf Tournament?":"No","Grand order total":1200,"Attendee Job Title":"Director of Power Supply Resources","Attendee Last Name":"Dominguez","Attendee First Name":"Nick","Attendee Type (IEPA)":null,"Attendee Organization":"Central Coast Community Energy","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:15.670063+00:00', '2025-06-05T07:27:15.670063+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('fafd4074-f2c6-4888-99cd-da057f080c24', '2f3a7e7c-dcc6-4b6f-835d-2437230e288e', '3081dd39-ab29-41a8-94bd-2456ec45c7e6', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T15:58:13+00:00', 'Sponsor Attendee', 'IEPA-Board-Member', 'publish', 'Virinder Singh', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":92128,"City":"San Diego","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"15445 Innovation Drive","Country":null,"Date Added":"2024-09-09 15:58:13","Golf Total":null,"Attendee Type":"Sponsor Attendee","Name on Badge":"Virinder Singh","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Vice President - Regulatory & Legislative Affairs","Attendee Last Name":"Singh","Attendee First Name":"Virinder","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"EDF Renewables","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:16.066474+00:00', '2025-06-05T07:27:16.066474+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('d22bb2d0-2598-4604-96bf-223fedc30d34', '50602e99-3763-49e4-a58b-f78b6f2f1a25', '1045279b-dc7d-408d-8fe5-2d83039036cb', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T15:57:44+00:00', 'Non IEPA Member', NULL, 'publish', 'Jason Burwen', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25'], 'Celiac - gluten free', FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":97214,"City":"Portland","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25","State":"Oregon","Gender":"Male","Status":"publish","Street":"7 SE Stark","Country":null,"Date Added":"2024-09-09 15:57:44","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Jason Burwen","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"VP Policy & Strategy","Attendee Last Name":"Burwen","Attendee First Name":"Jason","Attendee Type (IEPA)":null,"Attendee Organization":"GridStor","Special Dietary Needs":"Celiac - gluten free","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:16.48445+00:00', '2025-06-05T07:27:16.48445+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('f4e69c68-b4dc-436a-8b20-ae1a947099c5', '427bd81e-3d8f-4742-aaa8-4712516c0170', 'fa97249e-d673-4baf-92ac-90d765510a30', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T15:57:14+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Mackenzie Sheehan', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":20814,"City":"Bethesda","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Maryland","Gender":"Female","Status":"publish","Street":"4747 Bethesda Avenue","Country":null,"Date Added":"2024-09-09 15:57:14","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Mackenzie Sheehan","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Associate","Attendee Last Name":"Sheehan","Attendee First Name":"Mackenzie","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Hull Street Energy","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:16.936725+00:00', '2025-06-05T07:27:16.936725+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('dd48b270-cb20-41f8-8337-fa4a017eacaa', 'e37c7ad1-86c2-4f7c-af84-371f81b85214', '43112d9e-677a-42a8-a356-dc00f22ebe9d', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T15:48:37+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Rebecca Covarrubias', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":90071,"City":"Los Angeles","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"633 W. 5th Street 27th Floor","Country":null,"Date Added":"2024-09-09 15:48:37","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Rebecca Covarrubias","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Director Portfolio & Asset Management","Attendee Last Name":"Covarrubias","Attendee First Name":"Rebecca","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Diamond Generating Corporation","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:17.328851+00:00', '2025-06-05T07:27:17.328851+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('745153b4-3d2f-4cbe-a500-89c15d04edcc', 'ccc5d0a9-25fe-493d-b8a1-9d046521cb83', '7c9f8ece-ea29-41f6-b0fe-f745a1195ed2', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T15:48:33+00:00', 'Sponsor Attendee', NULL, 'publish', 'Claudia Becerril', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":97215,"City":"Portland","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Oregon","Gender":"Female","Status":"publish","Street":"735 SE 72nd Avenue","Country":null,"Date Added":"2024-09-09 15:48:33","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Claudia Becerril","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Asset Manager","Attendee Last Name":"Becerril","Attendee First Name":"Claudia","Attendee Type (IEPA)":null,"Attendee Organization":"Avangrid Renewables","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:17.722516+00:00', '2025-06-05T07:27:17.722516+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('356d5e72-ffb2-45d8-a9a9-88f270f53064', 'dd70a82d-e7d2-447b-a43a-a01d77dc37b3', '33d410ba-f157-40a9-ba5f-43fb49562979', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T14:18:56+00:00', 'Non IEPA Member', NULL, 'publish', 'Debrea Terwilliger', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegan', FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":80211,"City":"Denver","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Colorado","Gender":"Female","Status":"publish","Street":"2138 W. 32nd Avenue, Suite 300","Country":null,"Date Added":"2024-09-09 14:18:56","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Debrea Terwilliger","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Partner","Attendee Last Name":"Terwilliger","Attendee First Name":"Debrea","Attendee Type (IEPA)":null,"Attendee Organization":"Wilkinson Barker Knauer LLP","Special Dietary Needs":"Vegan","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:18.131218+00:00', '2025-06-05T07:27:18.131218+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('2bdc464b-96b6-4283-820a-68fd770fdf82', 'aac11c4a-9f3c-4333-87fa-d3ac53d5fb92', 'dc41136b-9a1b-4615-930a-8b544022639b', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:14:42+00:00', 'Non IEPA Member', NULL, 'publish', 'Jordan Pinjuv', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":95682,"City":"Shingle Springs","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25","State":"California","Gender":"Male","Status":"publish","Street":"5001 Owls Nest Road","Country":null,"Date Added":"2024-09-09 13:14:42","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Jordan Pinjuv","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Partner","Attendee Last Name":"Pinjuv","Attendee First Name":"Jordan","Attendee Type (IEPA)":null,"Attendee Organization":"Wilkinson Barker Knauer LLP","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:19.536958+00:00', '2025-06-05T07:27:19.536958+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('fd8911b7-302b-4eb5-8ee6-820f94f2c35f', 'b1e86d61-e7bf-4aa4-b72e-c52f8aabd81c', '59f7284b-f41f-48e7-a946-b6eb100b20f6', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Joe Kruger', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":85016,"City":"Phoenix","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Arizona","Gender":"Male","Status":"publish","Street":"2398 E Camelback Road","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Joe Kruger","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Consultant","Attendee Last Name":"Kruger","Attendee First Name":"Joe","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Capital Power","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:19.955773+00:00', '2025-06-05T07:27:19.955773+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('5d9e877f-ce9f-4296-9e4c-1c2a1e610f37', '53f8210a-fa1d-44c8-958a-1be5396868ac', '8ebd1f7d-2b46-472b-8c52-dafb23b3044d', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'Speaker', NULL, 'publish', 'Matthew Freedman', 'Tuesday 9/24', ARRAY['3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94610,"City":"Oakland","Meals":"3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"360 Grand Avenue, #150","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Matthew Freedman","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Staff Attorney","Attendee Last Name":"Freedman","Attendee First Name":"Matthew","Attendee Type (IEPA)":null,"Attendee Organization":"The Utility Reform Network","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Tuesday 9/24","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:20.405249+00:00', '2025-06-05T07:27:20.405249+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('5d6afbe6-563a-43ed-8626-a913820a0ec8', 'b8383649-fd46-4972-840c-0605af38602c', 'd0614d32-a3c9-4db6-a1af-a4473841f54e', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'Speaker', NULL, 'publish', 'Danielle', 'Tuesday 9/24', ARRAY['3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25'], 'Vegetarian preferred', FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":95630,"City":"Folsom","Meals":"3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25","State":"California","Gender":"Female","Status":"publish","Street":"250 Outcropping Way","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Danielle","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Principal, Infrastructure Policy Development","Attendee Last Name":"Mills","Attendee First Name":"Danielle","Attendee Type (IEPA)":null,"Attendee Organization":"CAISO","Special Dietary Needs":"Vegetarian preferred","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Tuesday 9/24","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:20.776346+00:00', '2025-06-05T07:27:20.776346+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('72a4c10a-dc2a-4778-aaa2-daf1d5448da5', '5ce63f6c-86c1-4067-93d1-32d57429f587', 'a147e0a3-502e-4487-985a-75c8f86abc31', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'Non IEPA Member', NULL, 'publish', 'Jim Shandalov', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":94111,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"1 California Street","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Jim Shandalov","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Vice President","Attendee Last Name":"Shandalov","Attendee First Name":"Jim","Attendee Type (IEPA)":null,"Attendee Organization":"NextEra Energy Resources","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:21.15026+00:00', '2025-06-05T07:27:21.15026+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('49a327c6-7731-46fa-b096-abbec7f4cc16', 'a76ec29f-42b0-43cf-b757-779ad8169ea4', '7d965a32-49d6-4a40-a283-8a6a4330a041', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'Speaker', NULL, 'publish', 'Delphine Hou', 'Tuesday 9/24', ARRAY['3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25'], 'Vegan if possible.  Otherwise vegetarian', FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":95814,"City":"Sacramento","Meals":"3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25","State":"California","Gender":"Female","Status":"publish","Street":"715 P Street","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Delphine Hou","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Deputy Director, Statewide Energy","Attendee Last Name":"Hou","Attendee First Name":"Delphine","Attendee Type (IEPA)":null,"Attendee Organization":"Statewide Energy Office, Department of Water Resources","Special Dietary Needs":"Vegan if possible.  Otherwise vegetarian","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Tuesday 9/24","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:21.530877+00:00', '2025-06-05T07:27:21.530877+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('d0202d46-6545-4074-9099-50364484a344', 'e030628b-ccd1-4f87-aaef-745f98b3629a', '7dd94ffb-e007-4223-8451-fcdb0d09dbcb', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'Speaker', NULL, 'publish', 'Arne Olson, E3', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 1200, 'completed', '2024-attendee-export', '{"Zip":94104,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"44 Montgomery Street, Suite 1500","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Arne Olson, E3","Golf Tournament?":"No","Grand order total":1200,"Attendee Job Title":"Senior Partner","Attendee Last Name":"Olson","Attendee First Name":"Arne","Attendee Type (IEPA)":null,"Attendee Organization":"Energy and Environmental Economics, Inc.","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:21.923611+00:00', '2025-06-05T07:27:21.923611+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('bbd9e08c-781a-421c-a630-0942fa2d661d', '8279b11c-8486-4f8e-a03d-13349bace525', 'f01ded78-2884-4e6a-bfef-5b0a70c05bba', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Willie Calvin', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":95814,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"1121 L Street, Suite 400","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Willie Calvin","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Regulatory Case Manager","Attendee Last Name":"Calvin","Attendee First Name":"Willie","Attendee Type (IEPA)":null,"Attendee Organization":"California Community Choice Association","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:22.287459+00:00', '2025-06-05T07:27:22.287459+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('2691b3d9-3536-40ad-968c-a44a8af6cf3e', '819fd8a0-498d-472d-b997-5c35cbbe6294', '89971de1-b2ce-4cf0-b496-4f46fb1107c5', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'Sponsor Attendee', NULL, 'publish', 'Scott Olson', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":97210,"City":"Portland","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24","State":"Oregon","Gender":"Male","Status":"publish","Street":"2701 NW Vaughn","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Scott Olson","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Director, Western Policy, Regulatory, and Markets","Attendee Last Name":"Olson","Attendee First Name":"Scott","Attendee Type (IEPA)":null,"Attendee Organization":"Avangrid Renewables","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:22.674301+00:00', '2025-06-05T07:27:22.674301+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('07350629-a82a-43bb-8ea7-821cb29f3ffe', '745b9410-074b-44d3-b168-ddf25827e86e', 'e72d6a7f-b22b-4f3b-a995-c1d50f1bc591', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'Speaker', NULL, 'publish', 'Alice Reynolds', 'Monday 9/23', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94102,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24","State":"California","Gender":"Female","Status":"publish","Street":"505 Van Ness Avenue","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Alice Reynolds","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"President","Attendee Last Name":"Reynolds","Attendee First Name":"Alice","Attendee Type (IEPA)":null,"Attendee Organization":"California Public Utilities Commission","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Monday 9/23","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:23.059496+00:00', '2025-06-05T07:27:23.059496+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('a401cdf4-aba8-4670-8752-1dbf53752078', '6c61f5f2-9924-4353-bcc6-1172ad83414c', '6a87f5bb-e0de-4d4f-b4b9-50ec106879c9', 2024, 'IEPA Annual Meeting 2024', '2024-09-09T13:13:56+00:00', 'Non IEPA Member', NULL, 'publish', 'Chris Devon', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Gluten Free - Allergy', TRUE, 'Right', NULL, 0, 2920, 'completed', '2024-attendee-export', '{"Zip":92130,"City":"San Diego","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"11455 El Camino Real","Country":null,"Date Added":"2024-09-09 13:13:56","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Chris Devon","Golf Tournament?":"Yes","Grand order total":2920,"Attendee Job Title":"Director of Energy Market Policy","Attendee Last Name":"Devon","Attendee First Name":"Christopher","Attendee Type (IEPA)":null,"Attendee Organization":"Terra-Gen, LLC","Special Dietary Needs":"Gluten Free - Allergy","Are you renting clubs?":"Right","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:24.447989+00:00', '2025-06-05T07:27:24.447989+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('7562b999-c7bd-43a3-9c8c-aa30d08ab9be', 'd080a141-95bf-4999-91d8-a53d04353b3f', '370c5702-7fd9-491a-8e48-eb28166b3a90', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T16:31:14+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Amanda Hosch', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":50322,"City":"Urbandale","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Iowa","Gender":"Female","Status":"publish","Street":"3299 NW Urbandale Drive","Country":null,"Date Added":"2024-09-06 16:31:14","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Amanda Hosch","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Originator","Attendee Last Name":"Hosch","Attendee First Name":"Amanda","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"BHE Renewables, LLC","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:24.85096+00:00', '2025-06-05T07:27:24.85096+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('4602a070-95fc-4e1a-9f64-32992ac05476', 'dbe251ca-ed80-4537-81d5-863702cfa8f5', '6a465d79-703b-4f01-8ae7-8fbc44c6c7a0', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:28:08+00:00', 'Non IEPA Member', NULL, 'publish', 'Seth Hilton', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":94104,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"1 Montgomery Street","Country":null,"Date Added":"2024-09-06 13:28:08","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Seth Hilton","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Attorney","Attendee Last Name":"Hilton","Attendee First Name":"Seth","Attendee Type (IEPA)":null,"Attendee Organization":"Stoel Rives LLP","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:25.264818+00:00', '2025-06-05T07:27:25.264818+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('fcb09dbe-8df6-4afd-a4bf-3b9eb71e0543', 'c8303114-e100-42aa-a8d3-057ab33393c9', '08e74908-756a-4459-bdde-7f555991c7f9', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Speaker', NULL, 'publish', 'Tristan Grimbert', 'Tuesday 9/24', ARRAY['3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":92128,"City":"San Diego","Meals":"3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"15445 Innovation Drive","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Tristan Grimbert","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"President & CEO","Attendee Last Name":"Grimbert","Attendee First Name":"Tristan","Attendee Type (IEPA)":null,"Attendee Organization":"EDF Renewables","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Tuesday 9/24","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:35.997054+00:00', '2025-06-05T07:27:35.997054+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('6225dc15-6c64-48bd-b3a9-50de88324137', '236490fb-a26e-40cc-b602-30f7db1f1e24', '1bdfc2a9-ba27-43f1-813a-adc2044fd45a', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:27:35+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Heejin Ryu', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":20814,"City":"Bethesda","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Maryland","Gender":"Female","Status":"publish","Street":"4747 Bethesda Avenue","Country":null,"Date Added":"2024-09-06 13:27:35","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Heejin Ryu","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Portfolio Manager","Attendee Last Name":"Ryu","Attendee First Name":"Heejin","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Hull Street Energy","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:25.680943+00:00', '2025-06-05T07:27:25.680943+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('1d99a178-37b7-4bd7-98a3-44e1de13bdf5', '4a9f67c3-6135-4f6a-8cca-cd0051ff8bfb', '9899f2b7-048c-4ace-a2e9-55c810eb19b2', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:27:35+00:00', 'Sponsor Attendee', NULL, 'publish', 'Avis Kowalewski', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94597,"City":"Walnut Creek","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"3003 Oak Road, Suite 400","Country":null,"Date Added":"2024-09-06 13:27:35","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Avis Kowalewski","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"VP Governmental & Reg Affairs","Attendee Last Name":"Kowalewski","Attendee First Name":"Avis","Attendee Type (IEPA)":null,"Attendee Organization":"Calpine Corporation","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:26.08534+00:00', '2025-06-05T07:27:26.08534+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('3c9993b1-ff12-4633-b12f-56740a77ea87', 'e91567bc-e43b-4be8-b731-c4df436b621d', '6bb95917-b15f-44cc-aa11-82ab573daea7', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:27:35+00:00', 'Sponsor Attendee', NULL, 'publish', 'Ed Callaghan', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94597,"City":"Walnut Creek","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"3003 Oak Road, Suite 400","Country":null,"Date Added":"2024-09-06 13:27:35","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Ed Callaghan","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Managing Counsel","Attendee Last Name":"Callaghan","Attendee First Name":"Ed","Attendee Type (IEPA)":null,"Attendee Organization":"Calpine Corporation","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:26.491761+00:00', '2025-06-05T07:27:26.491761+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('74f351be-80ed-4ea5-b288-706fba115dca', '9505a809-aa37-4881-80c5-b0186b68ae98', '33976340-50f0-4101-8efd-9c7cd7f60ce1', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:27:35+00:00', 'Sponsor Attendee', NULL, 'publish', 'Ashley Bernstein', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94597,"City":"Walnut Creek","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"3003 Oak Road, Suite 400","Country":null,"Date Added":"2024-09-06 13:27:35","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Ashley Bernstein","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Strategic Origination Director","Attendee Last Name":"Bernstein","Attendee First Name":"Ashley","Attendee Type (IEPA)":null,"Attendee Organization":"Calpine Corporation","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:26.8774+00:00', '2025-06-05T07:27:26.8774+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('0493efe7-3ac2-41c4-8d5b-ecc3e300048a', '007e45f1-e38d-4021-b9e0-a39a7111c3d9', '8083eb9d-1f8f-48bb-b3e2-0758b4a32afd', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:27:35+00:00', 'Sponsor Attendee', NULL, 'publish', 'Jill Van Dalen', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94597,"City":"Walnut Creek","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"3003 Walnut Road, Suite 400","Country":null,"Date Added":"2024-09-06 13:27:35","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Jill Van Dalen","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"VP and Associate General Counsel","Attendee Last Name":"Van Dalen","Attendee First Name":"Jill","Attendee Type (IEPA)":null,"Attendee Organization":"Calpine Corporation","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:27.276554+00:00', '2025-06-05T07:27:27.276554+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('7eb35813-1dff-412f-82d1-8bbf2f7a361b', 'abba0a4a-d732-46c7-9993-7465f60cc1c1', 'eedb1817-24de-4ee9-bef7-c0044b3ac9b5', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:27:35+00:00', 'Sponsor Attendee', NULL, 'publish', 'Alex Makler', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94597,"City":"Walnut Creek","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"3003 Oak Road, Suite 400","Country":null,"Date Added":"2024-09-06 13:27:35","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Alex Makler","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"SVP West Region","Attendee Last Name":"Makler","Attendee First Name":"Alexandre","Attendee Type (IEPA)":null,"Attendee Organization":"Calpine Corporation","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:27.678549+00:00', '2025-06-05T07:27:27.678549+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('823ecf22-8aac-46f7-aaa7-01854d92fc5a', 'ef0ffa01-2c87-4f8b-a8d9-735d2f495bde', '3a0288ea-df13-4332-a0c3-5462c4e0daca', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:27:35+00:00', 'Sponsor Attendee', NULL, 'draft', 'Kassandra Gough', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94597,"City":"Walnut Creek","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"draft","Street":"3003 Oak Road, Suite 400","Country":null,"Date Added":"2024-09-06 13:27:35","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Kassandra Gough","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"VP Governmental & Reg Affairs","Attendee Last Name":"Gough","Attendee First Name":"Kassandra","Attendee Type (IEPA)":null,"Attendee Organization":"Calpine Corporation","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:28.094571+00:00', '2025-06-05T07:27:28.094571+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('9871f721-7e28-432a-971a-aa38d32c4b0a', 'be6ae3f3-6695-420d-be81-49f84199fd1e', '4e72c199-ee61-4ff9-956b-8ffd0440926a', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:27:35+00:00', 'Day Use Only (Non-IEPA Members)', NULL, 'publish', 'Anthony Brunello', NULL, ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'vegetarian', FALSE, NULL, NULL, 0, 2100, 'completed', '2024-attendee-export', '{"Zip":94118,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"255 10th Avenue","Country":null,"Date Added":"2024-09-06 13:27:35","Golf Total":null,"Attendee Type":"Day Use Only (Non-IEPA Members)","Name on Badge":"Anthony Brunello","Golf Tournament?":"No","Grand order total":2100,"Attendee Job Title":"Partner","Attendee Last Name":"Brunello","Attendee First Name":"Anthony","Attendee Type (IEPA)":null,"Attendee Organization":"California Strategies","Special Dietary Needs":"vegetarian","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":null,"Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:29.523017+00:00', '2025-06-05T07:27:29.523017+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('9eb5c528-2ff4-472a-b785-fcafc3b19c29', 'b9702dc1-6eb5-4360-a14e-8677f60a9b07', '42deffeb-8e3a-4773-bd82-f674a72a0631', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:27:35+00:00', 'IEPA Member', NULL, 'publish', 'Walter Clemence', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegan', FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":33477,"City":"JUPITER","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Florida","Gender":"Male","Status":"publish","Street":"141 Seabreeze Circle","Country":null,"Date Added":"2024-09-06 13:27:35","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Walter Clemence","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Sr. Advisor- US Regulatory","Attendee Last Name":"Clemence","Attendee First Name":"Walter","Attendee Type (IEPA)":null,"Attendee Organization":"Capital Power","Special Dietary Needs":"Vegan","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:29.974673+00:00', '2025-06-05T07:27:29.974673+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('bb2ee6cb-4484-49a9-8d9a-72e1f4e34d29', '9843631f-2bf2-4f67-b0b3-acb85f5ffd9a', 'afd5363a-2fa7-472b-a028-7a71f1be03c4', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:24:26+00:00', 'Sponsor Attendee', NULL, 'publish', 'Nuo Tang', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":92121,"City":"San Diego","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"4350 Executive Drive, Suite 320","Country":null,"Date Added":"2024-09-06 13:24:26","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Nuo Tang","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Director - Asset Management","Attendee Last Name":"Tang","Attendee First Name":"Nuo","Attendee Type (IEPA)":null,"Attendee Organization":"Middle River Power","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:30.421001+00:00', '2025-06-05T07:27:30.421001+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('2da85f10-fe62-4cfc-a6b2-be77e9184249', '7d3c335a-9bc9-4d95-97d1-1ecd2c9cb692', 'f6cff907-6762-4444-a7e9-fa8cd4435a11', 2024, 'IEPA Annual Meeting 2024', '2024-09-06T13:19:47+00:00', 'Non IEPA Member', NULL, 'publish', 'Grant Glazer', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, 'No rental needed', NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":10036,"City":"New York","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"New York","Gender":"Male","Status":"publish","Street":"1155 Avenue of the Americas","Country":null,"Date Added":"2024-09-06 13:19:47","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Grant Glazer","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Manager, Regulatory & Market Affairs","Attendee Last Name":"Glazer","Attendee First Name":"Grant","Attendee Type (IEPA)":null,"Attendee Organization":"MN8 Energy","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:30.855056+00:00', '2025-06-05T07:27:30.855056+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('223de4fc-2de1-4e26-a5fb-c6b5f7628f58', '5ad9b809-21c2-4590-9cdf-af8b4cdf783c', '0da43696-e4a4-44e6-948c-6a69ef616c77', 2024, 'IEPA Annual Meeting 2024', '2024-08-29T14:31:37+00:00', 'Speaker', NULL, 'publish', 'Kathleen Staks', 'Monday 9/23', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":80215,"City":"Lakewood","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24","State":"Colorado","Gender":"Female","Status":"publish","Street":"PO Box 150666","Country":null,"Date Added":"2024-08-29 14:31:37","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Kathleen Staks","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Executive Director","Attendee Last Name":"Staks","Attendee First Name":"Kathleen","Attendee Type (IEPA)":null,"Attendee Organization":"Western Freedom","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Monday 9/23","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:31.279327+00:00', '2025-06-05T07:27:31.279327+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('f023c3a9-103f-460c-bad9-4a4025a13e04', '94fa2d1a-7d43-42f8-9842-d0d73e019923', '6aa3220f-8348-44e0-875c-52f656eb82c0', 2024, 'IEPA Annual Meeting 2024', '2024-08-29T14:31:37+00:00', 'Non IEPA Member', NULL, 'publish', 'Bryan Long', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":10282,"City":"New York","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"New York","Gender":"Male","Status":"publish","Street":"200 West Street","Country":null,"Date Added":"2024-08-29 14:31:37","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Bryan Long","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Vice President, Power Origination","Attendee Last Name":"Long","Attendee First Name":"Bryan","Attendee Type (IEPA)":null,"Attendee Organization":"Goldman Sachs & Co.","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:31.712909+00:00', '2025-06-05T07:27:31.712909+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('da3da958-9737-4966-b749-a83c623f29da', 'f406d858-b26e-41c3-aff2-e9bcedb66093', 'a4cbcd86-6e6a-48a0-8396-00ab0c954f34', 2024, 'IEPA Annual Meeting 2024', '2024-08-29T14:31:37+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Vince Wiraatmadja', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegetarian', TRUE, 'No rental needed', NULL, 0, 2500, 'completed', '2024-attendee-export', '{"Zip":95687,"City":"Vacaville","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"813 Purple Sage Drive","Country":null,"Date Added":"2024-08-29 14:31:37","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Vince Wiraatmadja","Golf Tournament?":"Yes","Grand order total":2500,"Attendee Job Title":"Senior Legislative Manager","Attendee Last Name":"Wiraatmadja","Attendee First Name":"Vincent","Attendee Type (IEPA)":null,"Attendee Organization":"MCE (Marin Clean Energy)","Special Dietary Needs":"Vegetarian","Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:32.098661+00:00', '2025-06-05T07:27:32.098661+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('769cbe58-05ee-448b-9e16-b7e5f62cedfb', '1df8a19e-c75c-4dc8-91aa-1c0c9c8a8dc4', '45f36d4a-4062-43d6-af29-dae5edbd620b', 2024, 'IEPA Annual Meeting 2024', '2024-08-29T11:25:04+00:00', 'Sponsor Attendee', NULL, 'publish', 'William Paxton', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":20814,"City":"Bethesda","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Maryland","Gender":"Male","Status":"publish","Street":"4747 Bethesda Avenue Suite 1220","Country":null,"Date Added":"2024-08-29 11:25:04","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"William Paxton","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Director - Origination","Attendee Last Name":"Paxton","Attendee First Name":"William","Attendee Type (IEPA)":null,"Attendee Organization":"Hull Street Energy","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:32.504747+00:00', '2025-06-05T07:27:32.504747+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('5ced74f9-516e-44c9-8689-db2e881f389a', 'b1e58237-826e-417a-aef4-16e837dd29da', '04c1ebac-f14b-4b12-afb7-ab49837053c1', 2024, 'IEPA Annual Meeting 2024', '2024-08-29T11:25:04+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Poonum Agrawal', 'Staying both nights', ARRAY['3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegetarian', FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":94087,"City":"Chicago","Meals":"3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Illinois","Gender":"Female","Status":"publish","Street":"One South Wacker Drive, Suite 1800","Country":null,"Date Added":"2024-08-29 11:25:04","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Poonum Agrawal","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Senior Manager, Regulatory Affairs - West","Attendee Last Name":"Agrawal","Attendee First Name":"Poonum","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Invenergy LLC","Special Dietary Needs":"Vegetarian","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:32.912044+00:00', '2025-06-05T07:27:32.912044+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('e41b3413-7bd8-4fb3-bbcc-4b1939e78e0e', '540c2c9e-95e8-401c-89c0-af9762d65aae', '638f7d95-96cd-4de3-b75b-f0acbdd5cd5c', 2024, 'IEPA Annual Meeting 2024', '2024-08-29T11:25:04+00:00', 'Sponsor Attendee', NULL, 'publish', 'Brian Theaker', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":95667,"City":"Placerville","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"3161 Ken Derek Lane","Country":null,"Date Added":"2024-08-29 11:25:04","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Brian Theaker","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"VP Western Market and Regulatory Affairs","Attendee Last Name":"Theaker","Attendee First Name":"Brian","Attendee Type (IEPA)":null,"Attendee Organization":"Middle River Power","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:33.325111+00:00', '2025-06-05T07:27:33.325111+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('056dac56-ea16-43d3-a9dd-5f2a63df9893', '184bcf42-0977-437f-902c-361635715dc8', '2f893ced-51d0-496d-a8b4-d5e43f145f7a', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Speaker', NULL, 'publish', 'Joanne Bradley', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 1200, 'completed', '2024-attendee-export', '{"Zip":95630,"City":"Folsom","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"101 Parkshore Drive","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Joanne Bradley","Golf Tournament?":"No","Grand order total":1200,"Attendee Job Title":"Transmission Regulatory Policy Manager","Attendee Last Name":"Bradley","Attendee First Name":"Joanne","Attendee Type (IEPA)":null,"Attendee Organization":"LS Power","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:34.733692+00:00', '2025-06-05T07:27:34.733692+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('4b00d25c-6645-4d78-a578-88e70cda0111', 'e6576ded-6f0d-4f1a-9e5d-66b7d4394cff', 'a24e5597-eb1f-4313-bc8b-6b57c5cb8483', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'IEPA Member', 'IEPA-Board-Member', 'publish', 'Renae Steichen', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'do not eat beef or pork', FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":94596,"City":"Walnut Creek","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"2121 N California Blvd Suite 1000","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Renae Steichen","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Senior Director of Regulatory Affairs","Attendee Last Name":"Steichen","Attendee First Name":"Renae","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"REV Renewables","Special Dietary Needs":"do not eat beef or pork","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:35.152376+00:00', '2025-06-05T07:27:35.152376+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('7feafa62-45cd-412e-b6af-44d10835a718', '147eb542-c32f-4ac9-bbb1-639afab3ac5a', '2c2b5ae9-e598-4ca6-ae73-031d65370fe0', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Speaker', NULL, 'publish', 'Keith Martin', 'Not staying any nights.', ARRAY['2. Breakfast September 24','3. Lunch September 24'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":20001,"City":"Washington","Meals":"2. Breakfast September 24, 3. Lunch September 24","State":"District of Columbia","Gender":"Male","Status":"publish","Street":"799 Ninth Street, NW, Suite 1000","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Keith Martin","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Partner","Attendee Last Name":"Martin","Attendee First Name":"Keith","Attendee Type (IEPA)":null,"Attendee Organization":"Norton Rose Fulbright US LLP","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Not staying any nights.","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:35.574463+00:00', '2025-06-05T07:27:35.574463+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('b906a9dc-0322-407e-b83a-0ae2e0d8c2d3', 'c293afc7-a59b-450a-9130-5acab1f979ef', '622a95eb-cd5b-47eb-8746-ea4d0bab0e7f', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Speaker', NULL, 'publish', 'Matthew Langer', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 1200, 'completed', '2024-attendee-export', '{"Zip":90017,"City":"Los Angeles","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"801 S Grand Avenue, Suite 400","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Matthew Langer","Golf Tournament?":"No","Grand order total":1200,"Attendee Job Title":"Chief Operating Officer","Attendee Last Name":"Langer","Attendee First Name":"Matthew","Attendee Type (IEPA)":null,"Attendee Organization":"Clean Power Alliance of Southern CA","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:36.41989+00:00', '2025-06-05T07:27:36.41989+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('17dc683b-461e-454d-969a-04bd6bdca7dd', 'da3fdfe9-108d-44d8-a01c-df67146a4aff', 'fa2b61a2-0ef0-4b88-9e94-be09c13475c9', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Non IEPA Member', NULL, 'publish', 'Ravi Sankaran', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Non-lactose', TRUE, 'No rental needed', NULL, 0, 2850, 'completed', '2024-attendee-export', '{"Zip":90810,"City":"Long Beach","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"21818 S. Wilmington Avenue Suite 414","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Ravi Sankaran","Golf Tournament?":"Yes","Grand order total":2850,"Attendee Job Title":"Director of Business Development","Attendee Last Name":"Sankaran","Attendee First Name":"Ravi","Attendee Type (IEPA)":null,"Attendee Organization":"Southwestern Power Group","Special Dietary Needs":"Non-lactose","Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:36.858844+00:00', '2025-06-05T07:27:36.858844+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('66cf739a-e54d-4a32-84e9-8255ef22b4fe', '331bf4ab-b647-4b02-91fc-b6aa64d6c4ee', '06400780-fc17-4f4c-85b3-09725eb82b8b', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Non IEPA Member', NULL, 'publish', 'Christopher Marelich', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":95814,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"621 Capitol Mall, 18th Floor","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Christopher Marelich","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Attorney","Attendee Last Name":"Marelich","Attendee First Name":"Christopher","Attendee Type (IEPA)":null,"Attendee Organization":"Downey Brand","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:37.286714+00:00', '2025-06-05T07:27:37.286714+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('bdb6512d-2c34-462d-9026-cb7a392eb896', 'abcc7bfb-542f-4fe1-b518-039a312f7a2e', '95fc292e-99ec-4967-8c4b-745398b40208', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Matt Fleiner', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Avocados', FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":89503,"City":"Reno","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Nevada","Gender":"Male","Status":"publish","Street":"1015 Crown Drive","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Matt Fleiner","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Director, Asset Management","Attendee Last Name":"Fleiner","Attendee First Name":"Matt","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Onward Energy","Special Dietary Needs":"Avocados","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:37.692361+00:00', '2025-06-05T07:27:37.692361+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('3ebc9746-ef64-49c8-84a7-80234e1c0683', '7f75b7df-b539-4429-b4f1-2ca7088c4ad3', '49c13af3-675c-435e-9601-c7bd494f891a', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Speaker', NULL, 'publish', 'Adam Pierce', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 1200, 'completed', '2024-attendee-export', '{"Zip":92123,"City":"San Diego","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"8330 Century Park Court","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Adam Pierce","Golf Tournament?":"No","Grand order total":1200,"Attendee Job Title":"Vice President, Energy Procurement & Rates","Attendee Last Name":"Pierce","Attendee First Name":"Adam","Attendee Type (IEPA)":null,"Attendee Organization":"SDG&E","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:38.131783+00:00', '2025-06-05T07:27:38.131783+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('6b4d966b-a9f2-4d53-b5c4-d82e8bf09c0c', 'bc4babc5-b25e-4237-abcd-71b8d9e8b06c', 'a4ea102f-157c-430b-bcd3-ba635474c5f5', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Eric Little', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":95814,"City":"sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"1121 L Street, Suite 400","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Eric Little","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Director of Regulatory Affairs","Attendee Last Name":"Little","Attendee First Name":"Eric","Attendee Type (IEPA)":null,"Attendee Organization":"CalCCA","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:38.55187+00:00', '2025-06-05T07:27:38.55187+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('2f083cd7-8176-43ac-a15a-6eb283fcc414', 'bfc05c9f-a3f9-4e15-9b59-03b6d29d141c', '33d6a40a-dba7-4ca2-b8c2-d7a4f2ef41e9', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Sponsor Attendee', NULL, 'publish', 'Megan Somogyi', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94105,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"455 Market Street, Suite 1500","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":0,"Attendee Type":"Sponsor Attendee","Name on Badge":"Megan Somogyi","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Partner","Attendee Last Name":"Somogyi","Attendee First Name":"Megan","Attendee Type (IEPA)":null,"Attendee Organization":"Downey Brand","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:39.990218+00:00', '2025-06-05T07:27:39.990218+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('89e834e9-780a-4c63-8aa9-9cb2773e5c5c', '2a28fb8d-2f94-4b08-8fca-f8c7d808d910', '24e2c2af-a454-4b06-8f0e-23f0a75ff3fa', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Erin Grizard', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":60606,"City":"Chicago","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Illinois","Gender":"Female","Status":"publish","Street":"One South Wacker Drive","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Erin Grizard","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"VP, Government Affairs","Attendee Last Name":"Grizard","Attendee First Name":"Erin","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Invenergy LLC","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:40.39884+00:00', '2025-06-05T07:27:40.39884+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('95dd77a5-87fd-4f29-a476-e9a1c7886d29', '19da7c13-78ae-4cc6-a11d-3db4e5f70d35', '010e9e53-3af6-4d10-8d5b-03f0c64b2eed', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Speaker', NULL, 'publish', 'Siva Gunda', 'Monday 9/23', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24'], 'Pescatarian Preferred', FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":95814,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24","State":"California","Gender":"Male","Status":"publish","Street":"715 P Street","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Siva Gunda","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Vice Chair/Commissioner","Attendee Last Name":"Gunda","Attendee First Name":"Siva","Attendee Type (IEPA)":null,"Attendee Organization":"California Energy Commission","Special Dietary Needs":"Pescatarian Preferred","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Monday 9/23","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:40.807316+00:00', '2025-06-05T07:27:40.807316+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('8c5d08e1-d2af-4cf6-8930-c014094c0e73', '6491830b-16be-420e-b473-6dc6237d981e', '77776510-594c-429e-a9aa-97e028a2cdbc', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Lauren Carr', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":95630,"City":"Folsom","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"240 Natoma Station Drive Apt 285","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Lauren Carr","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Senior Market Policy Analyst","Attendee Last Name":"Carr","Attendee First Name":"Lauren","Attendee Type (IEPA)":null,"Attendee Organization":"CalCCA","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:41.236038+00:00', '2025-06-05T07:27:41.236038+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('a2f12194-5554-4e95-ae3b-88f05c2b6622', 'f48bbc75-64a3-4247-ad45-f0fbe3f3bdf6', 'aeb0d8ce-07a7-4014-8aec-ea7a7852401b', 2024, 'IEPA Annual Meeting 2024', '2024-08-28T13:04:07+00:00', 'Speaker', NULL, 'publish', 'Don Howerton', 'Monday 9/23', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24'], NULL, FALSE, NULL, NULL, 0, 0, 'completed', '2024-attendee-export', '{"Zip":94512,"City":"Oakland","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24","State":"California","Gender":"Male","Status":"publish","Street":"300 Lakeside Drive","Country":null,"Date Added":"2024-08-28 13:04:07","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Don Howerton","Golf Tournament?":"No","Grand order total":0,"Attendee Job Title":"Senior Director, Commercial Procurement","Attendee Last Name":"Howerton","Attendee First Name":"Don","Attendee Type (IEPA)":null,"Attendee Organization":"Pacific Gas and Electric Company","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Monday 9/23","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:41.636868+00:00', '2025-06-05T07:27:41.636868+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('0c56e370-1961-45d7-a562-911684c949a5', '475d31b1-f846-44b9-becb-d34bc9d59962', '71d545f1-88f1-4ec3-94ee-28638434b634', 2024, 'IEPA Annual Meeting 2024', '2024-08-20T16:18:01+00:00', 'Non IEPA Member', NULL, 'publish', 'Caitlin Liotiris', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":84111,"City":"Salt Lake City","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Utah","Gender":"Female","Status":"publish","Street":"111 E Broadway","Country":null,"Date Added":"2024-08-20 16:18:01","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Caitlin Liotiris","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Principal","Attendee Last Name":"Liotiris","Attendee First Name":"Caitlin","Attendee Type (IEPA)":null,"Attendee Organization":"Western Power Trading Forum (WPTF)","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:42.058807+00:00', '2025-06-05T07:27:42.058807+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('ed393a55-c932-4029-b7f0-dc41cc24f763', '65eb29e0-b62e-40ea-b76b-838abe0cd718', '5b88cdb8-885e-415b-b9d1-283e915ae1bb', 2024, 'IEPA Annual Meeting 2024', '2024-08-20T16:18:01+00:00', 'Non IEPA Member', NULL, 'publish', 'Lewis Bichkoff', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegetarian preferred', FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":92660,"City":"Newport Beach","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"100 Bayview Circle, Suite 340","Country":null,"Date Added":"2024-08-20 16:18:01","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Lewis Bichkoff","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Director of Development","Attendee Last Name":"Bichkoff","Attendee First Name":"Lewis","Attendee Type (IEPA)":null,"Attendee Organization":"esVolta","Special Dietary Needs":"Vegetarian preferred","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:42.461168+00:00', '2025-06-05T07:27:42.461168+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('9197482e-735e-47a5-aab2-1060a4496ddc', 'cc106401-7c91-45ab-b554-039cecdf33cf', '27ce2e1a-2395-4614-98eb-6b1bf8f99d33', 2024, 'IEPA Annual Meeting 2024', '2024-08-20T16:18:01+00:00', 'Non IEPA Member', NULL, 'publish', 'Juan Felipe Builes', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":94115,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"828 Baker Street","Country":null,"Date Added":"2024-08-20 16:18:01","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Juan Felipe Builes","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Sr Manager, Business Development","Attendee Last Name":"Builes","Attendee First Name":"Juan","Attendee Type (IEPA)":null,"Attendee Organization":"esVolta","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:42.870158+00:00', '2025-06-05T07:27:42.870158+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('338fb204-4a2a-4613-b535-719f47a6c5ef', 'eadd314e-2c39-4d5a-948c-5a8d4fda853f', '7a45c2df-32a9-4554-8ab8-de5bf16cdac0', 2024, 'IEPA Annual Meeting 2024', '2024-08-20T16:18:01+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Kayla Baum', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegetarian, and allergy to all nuts.', FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":95113,"City":"San Jose","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"200 E Santa Clara Street","Country":null,"Date Added":"2024-08-20 16:18:01","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Kayla Baum","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Regulatory Policy Specialist","Attendee Last Name":"Baum","Attendee First Name":"Kayla","Attendee Type (IEPA)":null,"Attendee Organization":"San Jose Clean Energy","Special Dietary Needs":"Vegetarian, and allergy to all nuts.","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:43.299378+00:00', '2025-06-05T07:27:43.299378+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('9a076a37-6011-45be-b865-5d5dec77beb2', 'baef950f-abb3-4bbf-9cd5-538ff886e826', '08ee0811-e31c-43b0-995a-081564e21931', 2024, 'IEPA Annual Meeting 2024', '2024-08-20T16:18:01+00:00', 'Speaker', NULL, 'publish', 'Brian Cragg', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 1200, 'completed', '2024-attendee-export', '{"Zip":94105,"City":"San Francisco","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"455 Market Street, Suite 1500","Country":null,"Date Added":"2024-08-20 16:18:01","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Brian Cragg","Golf Tournament?":"No","Grand order total":1200,"Attendee Job Title":"Partner","Attendee Last Name":"Cragg","Attendee First Name":"Brian","Attendee Type (IEPA)":null,"Attendee Organization":"Downey Brand","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:43.730041+00:00', '2025-06-05T07:27:43.730041+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('a3a127f1-796d-4dc6-a9af-f88cbd4c1c13', 'a09d3585-437d-41f4-982e-5ff3b0952504', 'f9141b43-03d1-4905-987c-82b7c1935073', 2024, 'IEPA Annual Meeting 2024', '2024-08-20T16:18:01+00:00', 'Non IEPA Member', NULL, 'publish', 'Carl R. Steen', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":90017,"City":"Los Angeles","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"601 S. Figueroa Street, Suite 2500","Country":null,"Date Added":"2024-08-20 16:18:01","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Carl R. Steen","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Partner","Attendee Last Name":"Steen","Attendee First Name":"Carl","Attendee Type (IEPA)":null,"Attendee Organization":"Dentons US LLP","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:45.139613+00:00', '2025-06-05T07:27:45.139613+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('4b5a1571-c29f-4fea-a1bd-591e41aa42cf', '337788be-c991-4bcf-8786-56a6530b911c', '3368b13d-2afc-4f63-8243-79473941d473', 2024, 'IEPA Annual Meeting 2024', '2024-08-20T16:18:00+00:00', 'Non IEPA Member', NULL, 'publish', 'Brian Biering', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, TRUE, 'No rental needed', NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":95816,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"2600 Capitol Avenue, Suite 400","Country":null,"Date Added":"2024-08-20 16:18:00","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Brian Biering","Golf Tournament?":"Yes","Grand order total":2650,"Attendee Job Title":"Attorney","Attendee Last Name":"Biering","Attendee First Name":"Brian","Attendee Type (IEPA)":null,"Attendee Organization":"Ellison Schneider Harris & Donlan LLP","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:45.6406+00:00', '2025-06-05T07:27:45.6406+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('255582eb-cc65-48c2-adce-12276b002030', 'ccb0db19-d063-41ef-9428-fde975390f20', '311db1d9-25fe-4316-bd0c-133c8e604ef0', 2024, 'IEPA Annual Meeting 2024', '2024-08-20T16:18:00+00:00', 'Non IEPA Member', NULL, 'publish', 'Jeff', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, TRUE, 'No rental needed', NULL, 0, 2850, 'completed', '2024-attendee-export', '{"Zip":92025,"City":"Escondido","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"848 Inspiration Lane","Country":null,"Date Added":"2024-08-20 16:18:00","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Jeff","Golf Tournament?":"Yes","Grand order total":2850,"Attendee Job Title":"Managing Director","Attendee Last Name":"Ghilardi","Attendee First Name":"Jeff","Attendee Type (IEPA)":null,"Attendee Organization":"Linea Energy","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:46.045181+00:00', '2025-06-05T07:27:46.045181+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('8c6d82f8-41cd-43ad-a73c-e4ec7acf4b78', '94ddc256-9cc6-41d4-b89d-d7678521a392', 'ddc727fd-190c-455f-8e77-03f75700c394', 2024, 'IEPA Annual Meeting 2024', '2024-08-20T16:17:01+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'Bob Zdebski', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":90071,"City":"Los Angeles","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25","State":"California","Gender":"Male","Status":"publish","Street":"633 W. 5th Street","Country":null,"Date Added":"2024-08-20 16:17:01","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Bob Zdebski","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Vice President Development","Attendee Last Name":"Zdebski","Attendee First Name":"Robert","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Diamond Generating Corporation","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:46.429618+00:00', '2025-06-05T07:27:46.429618+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('793d02be-ae2d-4a77-bc19-2d5b5bacb7f7', '80dbe29d-9bec-4ee1-9576-ed4fe9840f71', 'cb808d5c-2f0b-42f4-88bc-ccac80420b09', 2024, 'IEPA Annual Meeting 2024', '2024-08-12T12:25:26+00:00', 'Non IEPA Member', NULL, 'publish', 'Sarah Keane', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":80202,"City":"Denver","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Colorado","Gender":"Female","Status":"publish","Street":"1675 Broadway, Suite 2300","Country":null,"Date Added":"2024-08-12 12:25:26","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Sarah Keane","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Lawyer","Attendee Last Name":"Keane","Attendee First Name":"Sarah","Attendee Type (IEPA)":null,"Attendee Organization":"Kaplan Kirsch & Rockwell LLP","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:46.84562+00:00', '2025-06-05T07:27:46.84562+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('ebdb74e2-12ab-48fd-9323-f42ca4a876bc', 'c62f5aba-d419-4ba3-b905-2624d65cff89', 'c945feba-5f4a-4eb2-808c-4853ad8b72df', 2024, 'IEPA Annual Meeting 2024', '2024-08-12T12:25:25+00:00', 'IEPA Member', 'IEPA-Regular-Member', 'publish', 'GARET R EMMERSON', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'I don''t eat salad.', FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":96007,"City":"Anderson","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"19794 Riverside Avenue","Country":null,"Date Added":"2024-08-12 12:25:25","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"GARET R EMMERSON","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Director of Technical Services","Attendee Last Name":"Emmerson","Attendee First Name":"Garet","Attendee Type (IEPA)":"IEPA-Regular-Member","Attendee Organization":"Sierra Pacific Industries","Special Dietary Needs":"I don''t eat salad.","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:47.267153+00:00', '2025-06-05T07:27:47.267153+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('07d2e75e-1cbf-4467-a189-3c66a5bd8fd0', '5584d096-a523-4f4a-8e0c-fbebab101a69', '47ba3250-9125-4522-be12-9b4cbfa0093a', 2024, 'IEPA Annual Meeting 2024', '2024-07-29T15:24:10+00:00', 'Non IEPA Member', NULL, 'publish', 'Adam Smith', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, TRUE, 'No rental needed', NULL, 0, 2850, 'completed', '2024-attendee-export', '{"Zip":95817,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"2017-36TH Street","Country":null,"Date Added":"2024-07-29 15:24:10","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Adam Smith","Golf Tournament?":"Yes","Grand order total":2850,"Attendee Job Title":"Director","Attendee Last Name":"Smith","Attendee First Name":"Adam","Attendee Type (IEPA)":null,"Attendee Organization":"Southern California Edison","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:47.671357+00:00', '2025-06-05T07:27:47.671357+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('63d679ef-adb6-45ce-b73c-bc7149e92855', '1427084f-7596-42b6-b162-9952b7e8f95b', '02ad1317-284a-4a8c-b835-a4d19f16f423', 2024, 'IEPA Annual Meeting 2024', '2024-07-29T15:24:10+00:00', 'IEPA Member', 'IEPA-Board-Member', 'publish', 'Kevin Telford', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":20814,"City":"Bethesda","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Maryland","Gender":"Male","Status":"publish","Street":"4747 Bethesda Avenue","Country":null,"Date Added":"2024-07-29 15:24:10","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Kevin Telford","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Principal","Attendee Last Name":"Telford","Attendee First Name":"Kevin","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"Hull Street Energy","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:48.108641+00:00', '2025-06-05T07:27:48.108641+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('359c32d6-765b-4d34-9709-d07ee4c4e8fa', '93b771d9-769c-4010-96b6-12371e898698', '272cdefe-e46a-406c-98e7-0894da023f52', 2024, 'IEPA Annual Meeting 2024', '2024-07-25T16:16:50+00:00', 'IEPA Member', 'IEPA-Board-Member', 'publish', 'Frederick Redell', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":85283,"City":"Tempe","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Arizona","Gender":"Male","Status":"publish","Street":"1553 W. Todd Drive, Suite 204","Country":null,"Date Added":"2024-07-25 16:16:50","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Frederick Redell","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Managing Director, North America","Attendee Last Name":"Redell","Attendee First Name":"Frederick","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"Atlantica Sustainable Infrastructure","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:48.596762+00:00', '2025-06-05T07:27:48.596762+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('f54606d0-ed8c-456d-bd5f-beeed57da8d8', '76f475a9-f171-4350-a360-65b09a926cad', '1fe2b5d8-92d0-436d-9f91-4142c413b9b4', 2024, 'IEPA Annual Meeting 2024', '2024-07-25T16:16:50+00:00', 'IEPA Member', 'IEPA-Board-Member', 'publish', 'Ravneet Singh', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":85283,"City":"Tempe","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Arizona","Gender":"Male","Status":"publish","Street":"1553 W. Todd Drive, Suite 204","Country":null,"Date Added":"2024-07-25 16:16:50","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Ravneet Singh","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Director, U.S. Business Development","Attendee Last Name":"Singh","Attendee First Name":"Ravneet","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"Atlantica Sustainable Infrastructure","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:49.026846+00:00', '2025-06-05T07:27:49.026846+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('32caca9d-1ee1-44dc-b5c5-e3a41e140e4d', 'f4d23e4b-0fc6-46f5-912a-c14e8abcd615', '9d70d236-455a-4182-ae32-78abb57d738a', 2024, 'IEPA Annual Meeting 2024', '2024-07-25T16:16:50+00:00', 'IEPA Member', 'IEPA-Board-Member', 'publish', 'Christopher Morris', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":85283,"City":"Tempe","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Arizona","Gender":"Male","Status":"publish","Street":"1553 W. Todd Drive, Suite 204","Country":null,"Date Added":"2024-07-25 16:16:50","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Christopher Morris","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"U.S. General Counsel","Attendee Last Name":"Morris","Attendee First Name":"Christopher","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"Atlantica Sustainable Infrastructure","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:50.430346+00:00', '2025-06-05T07:27:50.430346+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('29819698-b7e5-4906-8083-da84c13f1861', 'c4cdc931-812a-44e3-bd97-9258cd49c5e6', '72c960c5-74f0-4237-994a-9b755cf8c682', 2024, 'IEPA Annual Meeting 2024', '2024-07-25T16:16:50+00:00', 'IEPA Member', 'IEPA-Board-Member', 'publish', 'Treshia Sewell', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":85283,"City":"Tempe","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Alabama","Gender":"Female","Status":"publish","Street":"1553 W. Todd Drive, Suite 204","Country":null,"Date Added":"2024-07-25 16:16:50","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Treshia Sewell","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Senior Policy & Regulatory Specialist","Attendee Last Name":"Sewell","Attendee First Name":"Treshia","Attendee Type (IEPA)":"IEPA-Board-Member","Attendee Organization":"Atlantica Sustainable Infrastructure","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:50.855372+00:00', '2025-06-05T07:27:50.855372+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('02ce8951-238f-403b-ae39-631baec79d9d', '5856dcc5-0ce2-49a9-ae60-4745cb9f47cc', '522c3603-54ce-474c-ba1b-10ee496d455a', 2024, 'IEPA Annual Meeting 2024', '2024-07-24T13:36:11+00:00', 'Non IEPA Member', NULL, 'publish', 'Erin P', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2650, 'completed', '2024-attendee-export', '{"Zip":91770,"City":"Rosemead","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"2244 Walnut Grove Avenue, Q-1d","Country":null,"Date Added":"2024-07-24 13:36:11","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Erin P","Golf Tournament?":"No","Grand order total":2650,"Attendee Job Title":"Director, Portfolio, Planning, & Analysis","Attendee Last Name":"Pulgar","Attendee First Name":"Erin","Attendee Type (IEPA)":null,"Attendee Organization":"Southern California Edison","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:51.295959+00:00', '2025-06-05T07:27:51.295959+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('cd5ca14d-b71b-4802-9f26-e3d1b50eb27c', 'e9bd3744-e43a-4a05-be6b-c5c69803f986', 'e2beb736-7caf-4c1a-92bb-4fcd8c57721a', 2024, 'IEPA Annual Meeting 2024', '2024-07-23T22:06:36+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Marina Pantchenko', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegetarian', FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":93940,"City":"Monterey","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"70 Garden Court, Suite 300","Country":null,"Date Added":"2024-07-23 22:06:36","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Marina Pantchenko","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Deputy General Counsel","Attendee Last Name":"Pantchenko","Attendee First Name":"Marina","Attendee Type (IEPA)":null,"Attendee Organization":"Central Coast Community Energy","Special Dietary Needs":"Vegetarian","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:51.789114+00:00', '2025-06-05T07:27:51.789114+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('cd4e105f-697e-4eae-adea-575bcbe4623b', '6e75d4e3-06af-4804-a22a-dedda4eb8d60', '60ff898d-2f64-4074-9b5c-233577d089ed', 2024, 'IEPA Annual Meeting 2024', '2024-07-22T16:40:18+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Catherine Stedman', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegan', FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":93940,"City":"Monterey","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"70 Garden Court, Suite 300","Country":null,"Date Added":"2024-07-22 16:40:18","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Catherine Stedman","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Chief Communications Officer","Attendee Last Name":"Stedman","Attendee First Name":"Catherine","Attendee Type (IEPA)":null,"Attendee Organization":"Central Coast Community Energy","Special Dietary Needs":"Vegan","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:52.195462+00:00', '2025-06-05T07:27:52.195462+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('9c4f0fbc-cff4-43dd-ba7a-7bbfa93264d7', '051b20da-e183-4f2e-ada5-4f5b11e5d5f8', 'a89dfbc0-c263-49a7-a757-15615caecc68', 2024, 'IEPA Annual Meeting 2024', '2024-07-19T15:09:00+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Lindsay Descagnia', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Vegetarian', FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":90017,"City":"Los Angeles","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"801 S Grand Avenue, Suite 400","Country":null,"Date Added":"2024-07-19 15:09:00","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Lindsay Descagnia","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"VP, Power Supply","Attendee Last Name":"Descagnia","Attendee First Name":"Lindsay","Attendee Type (IEPA)":null,"Attendee Organization":"Clean Power Alliance of Southern CA","Special Dietary Needs":"Vegetarian","Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:52.597008+00:00', '2025-06-05T07:27:52.597008+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('31e11ab8-9d84-4114-9a29-1a947c880d30', '9a43bdf0-ffb8-40d4-a9ea-0d96a5b44719', 'edbecd39-c350-44af-8f6b-d535dc01e410', 2024, 'IEPA Annual Meeting 2024', '2024-07-18T11:48:51+00:00', 'Speaker', NULL, 'publish', 'Bill Walsh', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, 'No rental needed', NULL, 0, 1200, 'completed', '2024-attendee-export', '{"Zip":91770,"City":"Rosemead","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"2244 Walnut Grove Avenue","Country":null,"Date Added":"2024-07-18 11:48:51","Golf Total":null,"Attendee Type":"Speaker","Name on Badge":"Bill Walsh","Golf Tournament?":"No","Grand order total":1200,"Attendee Job Title":"Vice President, Energy Procurement & Mnagement","Attendee Last Name":"Walsh","Attendee First Name":"William","Attendee Type (IEPA)":null,"Attendee Organization":"Southern California Edison","Special Dietary Needs":null,"Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:53.002392+00:00', '2025-06-05T07:27:53.002392+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('d99d4aad-fa43-47fd-8905-c5febab8ee88', '0908c6a4-d1db-4184-89bb-5c9ef40df25d', '88aed8f9-af5f-4eab-82bc-bdd22e591e1f', 2024, 'IEPA Annual Meeting 2024', '2024-07-17T12:21:16+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Saeed Farrokhpay', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2000, 'completed', '2024-attendee-export', '{"Zip":20426,"City":"Washington","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"District of Columbia","Gender":"Male","Status":"publish","Street":"888 First Street NE","Country":null,"Date Added":"2024-07-17 12:21:16","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Saeed Farrokhpay","Golf Tournament?":"No","Grand order total":2000,"Attendee Job Title":"Energy Industry Analyst","Attendee Last Name":"Farrokhpay","Attendee First Name":"Saeed","Attendee Type (IEPA)":null,"Attendee Organization":"FERC","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:53.399742+00:00', '2025-06-05T07:27:53.399742+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('71293b52-633d-4258-9b06-ad4aabcf5713', '3e3153ed-de5f-4440-bde2-bcdbc98488dd', '6bbf9468-7efd-4486-bc2a-8189620b6d3a', 2024, 'IEPA Annual Meeting 2024', '2024-07-16T09:04:01+00:00', 'Non IEPA Member', NULL, 'publish', 'Jeff', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], 'Gluten and Dairy Allergies', TRUE, 'No rental needed', NULL, 0, 2920, 'completed', '2024-attendee-export', '{"Zip":95816,"City":"Sacramento","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Male","Status":"publish","Street":"2600 Capitol Avenue, Suite 400","Country":null,"Date Added":"2024-07-16 09:04:01","Golf Total":null,"Attendee Type":"Non IEPA Member","Name on Badge":"Jeff","Golf Tournament?":"Yes","Grand order total":2920,"Attendee Job Title":"Partner","Attendee Last Name":"Harris","Attendee First Name":"Jeffery","Attendee Type (IEPA)":null,"Attendee Organization":"Ellison Schneider Harris & Donlan LLP","Special Dietary Needs":"Gluten and Dairy Allergies","Are you renting clubs?":"No rental needed","Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":"(*************"}'::jsonb, '2025-06-05T07:27:53.844564+00:00', '2025-06-05T07:27:53.844564+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('c633b2fa-a291-4dfc-8337-c21d47a162ee', 'd25df4cf-3caf-4693-8455-7d6f5632ed1b', '286bca12-bc1a-4df9-9624-6a20a6430ba3', 2024, 'IEPA Annual Meeting 2024', '2024-07-11T14:56:22+00:00', 'IEPA Member', NULL, 'publish', 'Courtney Krause', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":80202,"City":"Denver","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"Colorado","Gender":"Female","Status":"publish","Street":"600 Seventeenth Street, Suite 2400S","Country":null,"Date Added":"2024-07-11 14:56:22","Golf Total":null,"Attendee Type":"IEPA Member","Name on Badge":"Courtney Krause","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Assistant General Counsel, Regulatory","Attendee Last Name":"Krause","Attendee First Name":"Courtney","Attendee Type (IEPA)":null,"Attendee Organization":"Onward Energy","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:54.269544+00:00', '2025-06-05T07:27:54.269544+00:00');
INSERT INTO iepa_historical_registrations (id, user_id, profile_id, event_year, event_name, registration_date, attendee_type, attendee_type_iepa, status, name_on_badge, nights_staying, meals, special_dietary_needs, golf_tournament, golf_club_rental, golf_cell_number, golf_total, grand_total, payment_status, imported_from_source, original_data, created_at, updated_at) VALUES ('371c0106-6c24-4755-ad7f-6a8389fb0fb1', '57c3c738-7651-4028-9475-886a13232e60', 'a1dacad9-264d-48e3-abba-2c98e02b42ea', 2024, 'IEPA Annual Meeting 2024', '2024-07-10T13:07:06+00:00', 'Fed/State Government or CCA', NULL, 'publish', 'Monica Padilla', 'Staying both nights', ARRAY['1. Dinner September 23','2. Breakfast September 24','3. Lunch September 24','4. Dinner September 24','5. Breakfast September 25','6. Lunch September 25'], NULL, FALSE, NULL, NULL, 0, 2300, 'completed', '2024-attendee-export', '{"Zip":94087,"City":"Sunnyvale","Meals":"1. Dinner September 23, 2. Breakfast September 24, 3. Lunch September 24, 4. Dinner September 24, 5. Breakfast September 25, 6. Lunch September 25","State":"California","Gender":"Female","Status":"publish","Street":"333 W. El Camino Real, Suite 330","Country":null,"Date Added":"2024-07-10 13:07:06","Golf Total":null,"Attendee Type":"Fed/State Government or CCA","Name on Badge":"Monica Padilla","Golf Tournament?":"No","Grand order total":2300,"Attendee Job Title":"Chief Executive Officer","Attendee Last Name":"Padilla","Attendee First Name":"Monica","Attendee Type (IEPA)":null,"Attendee Organization":"Silicon Valley Clean Energy","Special Dietary Needs":null,"Are you renting clubs?":null,"Email For Attendee List":"<EMAIL>","Nights staying (lodging)":"Staying both nights","Phone Number For Attendee List":"(*************","Golfer’s cell number for last minute changes, etc.":null}'::jsonb, '2025-06-05T07:27:55.678062+00:00', '2025-06-05T07:27:55.678062+00:00');

-- iepa_attendee_registrations (6 rows)
INSERT INTO iepa_attendee_registrations (id, user_id, registration_type, full_name, email, first_name, last_name, name_on_badge, gender, phone_number, street_address, city, state, zip_code, organization, job_title, attending_golf, meals, dietary_needs, registration_total, golf_total, grand_total, payment_status, payment_id, created_at, updated_at, golf_club_handedness, golf_club_rental_total, golf_club_rental, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, night_one, night_two, sponsor_id, sponsor_discount_code, is_sponsor_attendee) VALUES ('2be83132-8010-43da-87db-174c4c6f1d45', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'iepa-member', 'Bob Johnson', '<EMAIL>', 'Bob', 'Johnson', 'Bob Johnson', 'male', '555-0103', '789 Pine St', 'San Francisco', 'CA', '94102', 'Green Energy Inc', 'CEO', TRUE, ARRAY['breakfast_day1','lunch_day1','dinner_day1'], 'Gluten-free', 100, 200, 300, 'completed', 'pi_test_bob_johnson', '2025-06-04T22:16:27.421709+00:00', '2025-06-06T06:28:55.956354+00:00', '', 0, FALSE, NULL, NULL, NULL, NULL, TRUE, TRUE, NULL, NULL, FALSE);
INSERT INTO iepa_attendee_registrations (id, user_id, registration_type, full_name, email, first_name, last_name, name_on_badge, gender, phone_number, street_address, city, state, zip_code, organization, job_title, attending_golf, meals, dietary_needs, registration_total, golf_total, grand_total, payment_status, payment_id, created_at, updated_at, golf_club_handedness, golf_club_rental_total, golf_club_rental, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, night_one, night_two, sponsor_id, sponsor_discount_code, is_sponsor_attendee) VALUES ('c3a0ffdf-8dcd-4814-9066-d11993a34db8', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'iepa-member', 'Test Attendee User', '<EMAIL>', 'Test', 'Attendee', 'Test Attendee', 'male', '(*************', '123 Test Street', 'San Francisco', 'CA', '94105', 'Test Organization Inc.', 'Test Manager', FALSE, ARRAY['sept16Breakfast','sept16Lunch'], 'None', 100, 0, 100, 'completed', 'pi_test_1234567890_attendee_e2e', '2025-06-04T18:11:22.557997+00:00', '2025-06-06T06:32:55.052734+00:00', '', 0, FALSE, NULL, NULL, NULL, NULL, TRUE, TRUE, NULL, NULL, FALSE);
INSERT INTO iepa_attendee_registrations (id, user_id, registration_type, full_name, email, first_name, last_name, name_on_badge, gender, phone_number, street_address, city, state, zip_code, organization, job_title, attending_golf, meals, dietary_needs, registration_total, golf_total, grand_total, payment_status, payment_id, created_at, updated_at, golf_club_handedness, golf_club_rental_total, golf_club_rental, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, night_one, night_two, sponsor_id, sponsor_discount_code, is_sponsor_attendee) VALUES ('3531b4fb-f55f-4808-aa1a-a7e99d6eb61b', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'iepa-member', 'John Smith', '<EMAIL>', 'John', 'Smith', 'John Smith', 'male', '555-0101', '123 Main St', 'Sacramento', 'CA', '95814', 'Energy Corp', 'Manager', TRUE, ARRAY['breakfast_day1','lunch_day1'], 'No allergies', 100, 200, 300, 'completed', 'pi_test_john_smith', '2025-06-04T22:16:27.421709+00:00', '2025-06-06T06:32:55.052734+00:00', '', 0, FALSE, NULL, NULL, NULL, NULL, TRUE, TRUE, NULL, NULL, FALSE);
INSERT INTO iepa_attendee_registrations (id, user_id, registration_type, full_name, email, first_name, last_name, name_on_badge, gender, phone_number, street_address, city, state, zip_code, organization, job_title, attending_golf, meals, dietary_needs, registration_total, golf_total, grand_total, payment_status, payment_id, created_at, updated_at, golf_club_handedness, golf_club_rental_total, golf_club_rental, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, night_one, night_two, sponsor_id, sponsor_discount_code, is_sponsor_attendee) VALUES ('c4526acf-69e2-43f4-b80e-d5fbadd2e259', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'non-member', 'Jane Doe', '<EMAIL>', 'Jane', 'Doe', 'Jane Doe', 'female', '555-0102', '456 Oak Ave', 'Los Angeles', 'CA', '90210', 'Power Solutions', 'Director', FALSE, ARRAY['lunch_day1','dinner_day1'], 'Vegetarian', 150, 0, 150, 'pending', NULL, '2025-06-04T22:16:27.421709+00:00', '2025-06-06T06:32:55.052734+00:00', '', 0, FALSE, NULL, NULL, NULL, NULL, TRUE, TRUE, NULL, NULL, FALSE);
INSERT INTO iepa_attendee_registrations (id, user_id, registration_type, full_name, email, first_name, last_name, name_on_badge, gender, phone_number, street_address, city, state, zip_code, organization, job_title, attending_golf, meals, dietary_needs, registration_total, golf_total, grand_total, payment_status, payment_id, created_at, updated_at, golf_club_handedness, golf_club_rental_total, golf_club_rental, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, night_one, night_two, sponsor_id, sponsor_discount_code, is_sponsor_attendee) VALUES ('a1bcef85-367b-468e-9e8b-e99aa4db4a22', '4ea60bd3-28ae-4e23-916d-46a6310a9c24', 'iepa-member', 'Golf Tester', '<EMAIL>', 'Golf', 'Tester', 'Golf T.', 'male', '************', '123 Golf Course Dr', 'Sacramento', 'CA', '95814', 'Test Energy Company', 'Golf Tester', TRUE, ARRAY['sept15Dinner','sept16Breakfast','sept16Lunch'], '', 2300, 200, 2570, 'completed', 'test_payment_123', '2025-06-05T22:46:51.524669+00:00', '2025-06-06T06:32:55.052734+00:00', 'right', 70, TRUE, 'invoices/invoice-attendee-A4DB4A22-2025-06-06.pdf', NULL, '2025-06-06T01:02:01.437+00:00', NULL, TRUE, TRUE, NULL, NULL, FALSE);
INSERT INTO iepa_attendee_registrations (id, user_id, registration_type, full_name, email, first_name, last_name, name_on_badge, gender, phone_number, street_address, city, state, zip_code, organization, job_title, attending_golf, meals, dietary_needs, registration_total, golf_total, grand_total, payment_status, payment_id, created_at, updated_at, golf_club_handedness, golf_club_rental_total, golf_club_rental, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, night_one, night_two, sponsor_id, sponsor_discount_code, is_sponsor_attendee) VALUES ('ee472d56-d5db-401f-aae3-34fd274d8719', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'comped-speaker', 'Dr. Emily Rodriguez', '<EMAIL>', 'Emily', 'Rodriguez', 'Dr. Emily Rodriguez', 'female', '************', '789 Innovation Drive', 'San Francisco', 'CA', '94105', 'SolarTech Innovations', 'Chief Technology Officer', TRUE, ARRAY['day1-breakfast','day1-lunch','day1-reception','day2-breakfast','day2-lunch'], 'Gluten-free diet. No dairy allergies.', 0, 200, 270, 'completed', NULL, '2025-06-06T05:27:58.05837+00:00', '2025-06-06T06:32:55.052734+00:00', 'right', 70, TRUE, NULL, NULL, NULL, NULL, TRUE, TRUE, NULL, NULL, FALSE);

-- iepa_speaker_registrations (1 rows)
INSERT INTO iepa_speaker_registrations (id, user_id, full_name, first_name, last_name, organization_name, job_title, presentation_file_url, bio, created_at, updated_at, email, phone_number, preferred_contact_method, presentation_title, presentation_description, presentation_duration, target_audience, learning_objectives, speaker_experience, previous_speaking, equipment_needs, special_requests, headshot_url, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at) VALUES ('bfdf5d8d-3b01-4c7e-8a78-2e88b7e1a8f6', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'Dr. Emily Rodriguez', 'Emily', 'Rodriguez', 'SolarTech Innovations', 'Chief Technology Officer', NULL, 'Dr. Emily Rodriguez is the Chief Technology Officer at SolarTech Innovations, where she leads cutting-edge research in photovoltaic technology and energy storage systems. With over 12 years of experience in renewable energy engineering, she has published over 30 peer-reviewed papers and holds 8 patents in solar energy technology.', '2025-06-06T05:29:12.836187+00:00', '2025-06-06T05:29:12.836187+00:00', '<EMAIL>', '************', 'email', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- iepa_sponsor_registrations (4 rows)
INSERT INTO iepa_sponsor_registrations (id, user_id, sponsor_name, sponsor_url, sponsor_video, sponsor_image_url, sponsor_description, payment_status, payment_id, created_at, updated_at, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, linked_attendee_email, company_domain) VALUES ('a7615495-9577-44c9-94a3-46ad01cfaee4', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'Test Sponsor Company', 'https://test-sponsor.com', NULL, NULL, 'A test sponsor company for end-to-end testing of the IEPA conference registration system.', 'completed', 'pi_test_1234567890_sponsor_e2e', '2025-06-04T18:13:17.470761+00:00', '2025-06-04T18:14:02.006456+00:00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO iepa_sponsor_registrations (id, user_id, sponsor_name, sponsor_url, sponsor_video, sponsor_image_url, sponsor_description, payment_status, payment_id, created_at, updated_at, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, linked_attendee_email, company_domain) VALUES ('f0182012-a8a7-4588-a3f7-d00d80f1fb92', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'Solar Dynamics Corp', 'https://solardynamics.com', NULL, NULL, 'Leading provider of solar energy solutions for commercial and residential applications.', 'completed', 'pi_test_solar_dynamics', '2025-06-04T22:18:37.951571+00:00', '2025-06-04T22:18:37.951571+00:00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO iepa_sponsor_registrations (id, user_id, sponsor_name, sponsor_url, sponsor_video, sponsor_image_url, sponsor_description, payment_status, payment_id, created_at, updated_at, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, linked_attendee_email, company_domain) VALUES ('9ac27af0-7ed1-483f-97e5-f3a893802728', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'Wind Tech Industries', 'https://windtech.com', NULL, NULL, 'Innovative wind turbine technology and maintenance services.', 'pending', NULL, '2025-06-04T22:18:37.951571+00:00', '2025-06-04T22:18:37.951571+00:00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO iepa_sponsor_registrations (id, user_id, sponsor_name, sponsor_url, sponsor_video, sponsor_image_url, sponsor_description, payment_status, payment_id, created_at, updated_at, invoice_url, receipt_url, invoice_generated_at, receipt_generated_at, linked_attendee_email, company_domain) VALUES ('83bcfdb6-14cd-4cef-849c-6cfed1fe003c', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'Energy Storage Solutions', 'https://energystorage.com', NULL, NULL, 'Advanced battery storage systems for renewable energy integration.', 'completed', 'pi_test_energy_storage', '2025-06-04T22:18:37.951571+00:00', '2025-06-04T22:18:37.951571+00:00', NULL, NULL, NULL, NULL, NULL, NULL);

-- iepa_payments (6 rows)
INSERT INTO iepa_payments (id, user_id, registration_id, registration_type, stripe_payment_intent_id, amount, currency, status, created_at, updated_at) VALUES ('c5b2543d-9691-4345-8e31-294a8abdedba', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'c3a0ffdf-8dcd-4814-9066-d11993a34db8', 'attendee', 'pi_test_1234567890_attendee_e2e', 100, 'usd', 'completed', '2025-06-04T18:11:59.879893+00:00', '2025-06-04T18:11:59.879893+00:00');
INSERT INTO iepa_payments (id, user_id, registration_id, registration_type, stripe_payment_intent_id, amount, currency, status, created_at, updated_at) VALUES ('0313b306-f305-4d10-96ae-bda32a86112d', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'a7615495-9577-44c9-94a3-46ad01cfaee4', 'sponsor', 'pi_test_1234567890_sponsor_e2e', 2500, 'usd', 'completed', '2025-06-04T18:13:35.273829+00:00', '2025-06-04T18:13:35.273829+00:00');
INSERT INTO iepa_payments (id, user_id, registration_id, registration_type, stripe_payment_intent_id, amount, currency, status, created_at, updated_at) VALUES ('5d6643e3-b7ec-4917-9ad9-828f5e333d23', '081b18ec-b721-45ba-a35d-5e10b76c788e', '3531b4fb-f55f-4808-aa1a-a7e99d6eb61b', 'attendee', 'pi_test_john_smith', 300, 'usd', 'completed', '2025-06-04T22:19:26.745225+00:00', '2025-06-04T22:19:26.745225+00:00');
INSERT INTO iepa_payments (id, user_id, registration_id, registration_type, stripe_payment_intent_id, amount, currency, status, created_at, updated_at) VALUES ('9057bb86-4069-482f-965e-a12577e15ea4', '081b18ec-b721-45ba-a35d-5e10b76c788e', '2be83132-8010-43da-87db-174c4c6f1d45', 'attendee', 'pi_test_bob_johnson', 300, 'usd', 'completed', '2025-06-04T22:19:26.745225+00:00', '2025-06-04T22:19:26.745225+00:00');
INSERT INTO iepa_payments (id, user_id, registration_id, registration_type, stripe_payment_intent_id, amount, currency, status, created_at, updated_at) VALUES ('08433451-3804-4227-984c-d1e6415a82ca', '081b18ec-b721-45ba-a35d-5e10b76c788e', 'f0182012-a8a7-4588-a3f7-d00d80f1fb92', 'sponsor', 'pi_test_solar_dynamics', 2500, 'usd', 'completed', '2025-06-04T22:19:26.745225+00:00', '2025-06-04T22:19:26.745225+00:00');
INSERT INTO iepa_payments (id, user_id, registration_id, registration_type, stripe_payment_intent_id, amount, currency, status, created_at, updated_at) VALUES ('5caa12be-38bc-4f7c-91a3-81f0344e0462', '081b18ec-b721-45ba-a35d-5e10b76c788e', '83bcfdb6-14cd-4cef-849c-6cfed1fe003c', 'sponsor', 'pi_test_energy_storage', 5000, 'usd', 'completed', '2025-06-04T22:19:26.745225+00:00', '2025-06-04T22:19:26.745225+00:00');

-- Re-enable foreign key checks and triggers
SET session_replication_role = DEFAULT;

-- Update sequences (if needed)
SELECT setval(pg_get_serial_sequence('iepa_user_profiles', 'id'), COALESCE((SELECT MAX(id) FROM iepa_user_profiles), 1));
-- Add more sequence updates as needed

-- Summary: 246 total rows imported
