# Parallax Footer Implementation

## Overview

Successfully implemented a parallax scrolling effect in the footer section using three SVG tree row images with different scroll speeds to create depth perception.

## Implementation Details

### Components Created

1. **ParallaxFooter.tsx** - Main component with parallax effect
2. **parallax-footer.css** - Styling for the parallax effect

### SVG Layer Configuration

- **row1.svg**: Foreground layer (800x212.81px) - Fastest scroll speed (0.5x multiplier)
- **row2.svg**: Middle layer (800x303.83px) - Medium scroll speed (0.3x multiplier)
- **row3.svg**: Background layer (800x353.11px) - Slowest scroll speed (0.1x multiplier)

### Technical Features

#### Parallax Effect

- Uses `transform: translateY()` for smooth scrolling
- Different scroll speed multipliers for each layer
- Scroll event listener with passive option for performance
- `will-change: transform` for GPU acceleration

#### Responsive Design

- Mobile-first approach with progressive enhancement
- Breakpoints at 640px (tablet) and 1024px (desktop)
- Adaptive footer height and spacing
- Grid layout that adapts to screen size

#### Accessibility

- **Reduced Motion Support**: Respects `prefers-reduced-motion: reduce`
- **Screen Reader**: Hidden decorative description for context
- **High Contrast**: Enhanced contrast mode support
- **Keyboard Navigation**: All footer links are keyboard accessible

#### Performance Optimizations

- Passive scroll event listeners
- GPU-accelerated transforms
- Optimized SVG loading with Next.js Image component
- Conditional parallax effect based on user preferences

### Visual Design

#### Background Gradient

```css
background: linear-gradient(
  to bottom,
  #87ceeb 0%,
  /* Sky blue at top */ #b8e6b8 50%,
  /* Light green in middle */ #228b22 100% /* Forest green at bottom */
);
```

#### Footer Content

- Semi-transparent dark blue background with backdrop blur
- IEPA branding with white logo treatment
- Organized link sections with hover effects
- Light blue accent color (#87ceeb) for headings and highlights

### Integration

- Replaced existing Footer component in layout.tsx
- Added CSS import to layout.tsx
- Maintains all existing footer content and functionality
- Preserves IEPA branding and color scheme

### Browser Support

- Modern browsers with CSS transforms support
- Graceful degradation for older browsers
- Fallback to static footer if parallax fails

### File Structure

```
src/
├── components/layout/
│   └── ParallaxFooter.tsx
├── styles/
│   └── parallax-footer.css
└── app/
    └── layout.tsx (updated)

public/
├── row1.svg
├── row2.svg
└── row3.svg
```

## Testing

- ✅ Development server running on <http://localhost:3000>
- ✅ Parallax effect visible when scrolling to footer
- ✅ SVG tree layers loading correctly with background images
- ✅ Different scroll speeds working (0.1x, 0.3x, 0.5x multipliers)
- ✅ Responsive behavior tested across breakpoints
- ✅ Accessibility features verified (reduced motion support)
- ✅ Cross-browser compatibility (Safari backdrop-filter support added)

## Future Enhancements

- Add intersection observer for performance optimization
- Consider adding subtle animation on layer entry
- Potential for seasonal SVG variations
- Performance monitoring for scroll events

## Implementation Notes

### SVG Loading Solution

- **Issue**: Next.js Image component with `fill` prop was not sizing SVG files correctly
- **Solution**: Switched to CSS background images with `background-size: cover` and `background-repeat: repeat-x`
- **Result**: SVG tree layers now display at full resolution and proper scale

### Technical Details

- SVG files are optimized for web performance
- Effect is disabled for users who prefer reduced motion
- Maintains semantic HTML structure for accessibility
- Compatible with existing IEPA brand guidelines
- Cross-browser support with Safari-specific backdrop-filter prefix
