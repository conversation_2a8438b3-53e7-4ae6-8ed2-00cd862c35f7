// Golf Add-On Types
// Type definitions for golf tournament add-on functionality

import type { Database } from './database';

// Database types
export type AttendeeRegistration = Database['public']['Tables']['iepa_attendee_registrations']['Row'];

// Golf add-on form data
export interface GolfAddOnFormData {
  golfTournament: boolean;
  golfClubRental: boolean;
  golfClubHandedness: 'left' | 'right' | '';
}

// Golf add-on pricing
export interface GolfAddOnPricing {
  golfTournamentFee: number;
  golfClubRentalFee: number;
  total: number;
}

// Golf add-on eligibility
export interface GolfAddOnEligibility {
  eligible: boolean;
  reason?: string;
  currentRegistration?: AttendeeRegistration;
  hasExistingGolf: boolean;
  canAddGolf: boolean;
  canAddClubRental: boolean;
}

// Golf add-on request
export interface GolfAddOnRequest {
  registrationId: string;
  golfTournament: boolean;
  golfClubRental: boolean;
  golfClubHandedness: string;
  golfTotal: number;
  golfClubRentalTotal: number;
  newGrandTotal: number;
}

// Golf add-on response
export interface GolfAddOnResponse {
  success: boolean;
  message: string;
  data?: {
    registrationId: string;
    checkoutSessionId?: string;
    updatedRegistration?: AttendeeRegistration;
  };
  error?: string;
}

// Golf add-on payment session data
export interface GolfAddOnSessionData {
  registrationId: string;
  userId: string;
  customerName: string;
  customerEmail: string;
  golfTournament: boolean;
  golfClubRental: boolean;
  golfClubHandedness: string;
  lineItems: Array<{
    name: string;
    description: string;
    price: number;
    quantity: number;
  }>;
  totalAmount: number;
  metadata: Record<string, string>;
}

// Golf add-on update request
export interface GolfAddOnUpdateRequest {
  registrationId: string;
  golfData: {
    attending_golf: boolean;
    golf_club_rental: boolean;
    golf_club_handedness: string;
    golf_total: number;
    golf_club_rental_total: number;
    grand_total: number;
  };
  paymentData?: {
    payment_id: string;
    payment_status: string;
  };
}

// Golf add-on constants
export const GOLF_ADDON_CONSTANTS = {
  GOLF_TOURNAMENT_FEE: 200,
  GOLF_CLUB_RENTAL_FEE: 75,
  GOLF_CLUB_OPTIONS: [
    { value: 'left', label: 'Left Handed' },
    { value: 'right', label: 'Right Handed' },
  ],
} as const;

// Golf add-on validation
export interface GolfAddOnValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Golf add-on state for UI components
export interface GolfAddOnState {
  loading: boolean;
  error: string | null;
  eligibility: GolfAddOnEligibility | null;
  formData: GolfAddOnFormData;
  pricing: GolfAddOnPricing;
  isSubmitting: boolean;
}

// Golf add-on hook return type
export interface UseGolfAddOnReturn {
  state: GolfAddOnState;
  actions: {
    checkEligibility: (registrationId: string) => Promise<void>;
    updateFormData: (data: Partial<GolfAddOnFormData>) => void;
    calculatePricing: (data: GolfAddOnFormData) => GolfAddOnPricing;
    submitGolfAddOn: (registrationId: string) => Promise<GolfAddOnResponse>;
    reset: () => void;
  };
}

// Export utility types
export type GolfHandedness = 'left' | 'right' | '';
export type GolfAddOnStep = 'eligibility' | 'form' | 'payment' | 'confirmation';
