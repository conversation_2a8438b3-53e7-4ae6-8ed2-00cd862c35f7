# Stripe Webhook Configuration Guide

## Overview

This guide explains how to configure Stripe webhooks for the IEPA conference registration system to enable real-time payment processing updates.

## Current Status

- ❌ **Webhook Secret**: Set to placeholder value `whsec_your_webhook_secret_here`
- ✅ **Webhook Handler**: Implemented at `/api/stripe/webhook`
- ✅ **Event Processing**: Handles `checkout.session.completed`, `payment_intent.succeeded`, `payment_intent.payment_failed`

## Setup Instructions

### 1. Access Stripe Dashboard

1. Go to [Stripe Dashboard](https://dashboard.stripe.com/test/webhooks)
2. Ensure you're in **Test Mode** (toggle in top-left)

### 2. Create Webhook Endpoint

1. Click **"Add endpoint"**
2. Set **Endpoint URL**: `http://localhost:3000/api/stripe/webhook` (for development)
3. For production, use: `https://your-domain.com/api/stripe/webhook`

### 3. Configure Events

Select these events to listen for:

- `checkout.session.completed`
- `payment_intent.succeeded`
- `payment_intent.payment_failed`

### 4. Get Webhook Secret

1. After creating the endpoint, click on it
2. Click **"Reveal"** in the **Signing secret** section
3. Copy the secret (starts with `whsec_`)

### 5. Update Environment Variables

Update `.env.local`:

```bash
STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret_here
```

### 6. Test Webhook

1. Use Stripe CLI: `stripe listen --forward-to localhost:3000/api/stripe/webhook`
2. Or use the **"Send test webhook"** button in the dashboard

## Webhook Events Handled

### `checkout.session.completed`

- Creates payment record in `iepa_payments` table
- Updates registration status to `completed`
- Links payment to registration via `registration_id`

### `payment_intent.succeeded`

- Updates payment status to `completed`
- Confirms successful payment processing

### `payment_intent.payment_failed`

- Updates payment status to `failed`
- Allows for retry logic

## Security Notes

- Webhook signatures are verified using the webhook secret
- All webhook events are logged for debugging
- Failed webhook processing is logged but doesn't block the payment flow

## Troubleshooting

### Common Issues

1. **403 Forbidden**: Check webhook URL and endpoint accessibility
2. **Signature Verification Failed**: Verify webhook secret is correct
3. **Events Not Processing**: Check event selection in Stripe dashboard

### Debug Steps

1. Check server logs for webhook processing
2. Use Stripe dashboard webhook logs
3. Test with Stripe CLI for local development

## Production Considerations

- Use HTTPS endpoint URL
- Set up webhook endpoint monitoring
- Configure retry logic for failed webhooks
- Monitor webhook delivery success rates
