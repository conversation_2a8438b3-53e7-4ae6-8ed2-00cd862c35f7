'use client';

// Golf Add-On Modal Component
// Modal interface for adding golf tournament to existing registration

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Button,
  Alert,
  AlertDescription
} from '@/components/ui';
import { Loader2, Target, AlertCircle } from 'lucide-react';
import { GolfAddOnForm } from './GolfAddOnForm';
import { useAuth } from '@/contexts/AuthContext';
import {
  checkGolfAddOnEligibility,
  calculateGolfAddOnPricing,
  createGolfAddOnCheckoutSession,
} from '@/services/golfAddOn';
import type {
  GolfAddOnEligibility,
  GolfAddOnFormData,
  GolfAddOnSessionData,
  AttendeeRegistration,
} from '@/types/golfAddOn';

interface GolfAddOnModalProps {
  isOpen: boolean;
  onClose: () => void;
  registration: AttendeeRegistration;
  onSuccess?: () => void;
}

export function GolfAddOnModal({
  isOpen,
  onClose,
  registration,
  onSuccess: _onSuccess,
}: GolfAddOnModalProps) {
  const { user } = useAuth();
  const [step, setStep] = useState<'loading' | 'eligibility' | 'form' | 'processing'>('loading');
  const [eligibility, setEligibility] = useState<GolfAddOnEligibility | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check eligibility when modal opens
  useEffect(() => {
    if (isOpen && user && registration) {
      checkEligibility();
    }
  }, [isOpen, user, registration]);

  const checkEligibility = async () => {
    if (!user) return;

    try {
      setStep('loading');
      setError(null);

      console.log('🏌️ Checking golf add-on eligibility for registration:', registration.id);

      const eligibilityResult = await checkGolfAddOnEligibility(user.id, registration.id);
      setEligibility(eligibilityResult);

      if (eligibilityResult.eligible) {
        setStep('form');
      } else {
        setStep('eligibility');
      }
    } catch (err) {
      console.error('❌ Error checking golf add-on eligibility:', err);
      setError(err instanceof Error ? err.message : 'Failed to check eligibility');
      setStep('eligibility');
    }
  };

  const handleFormSubmit = async (formData: GolfAddOnFormData) => {
    if (!user || !eligibility || !eligibility.currentRegistration) {
      setError('Missing required data for golf add-on');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      setStep('processing');

      console.log('🏌️ Processing golf add-on submission:', formData);

      // Calculate pricing
      const pricing = calculateGolfAddOnPricing(formData, eligibility);

      if (pricing.total <= 0) {
        setError('No golf add-on items selected or already included');
        setStep('form');
        return;
      }

      // Prepare line items for checkout
      const lineItems = [];
      
      if (formData.golfTournament && eligibility.canAddGolf) {
        lineItems.push({
          name: 'Golf Tournament',
          description: 'IEPA 2025 Conference Golf Tournament - September 16, 2025',
          price: pricing.golfTournamentFee,
          quantity: 1,
        });
      }

      if (formData.golfClubRental) {
        lineItems.push({
          name: 'Golf Club Rental',
          description: `Callaway Rogues (${formData.golfClubHandedness} handed) with 6 golf balls`,
          price: pricing.golfClubRentalFee,
          quantity: 1,
        });
      }

      // Prepare session data
      const sessionData: GolfAddOnSessionData = {
        registrationId: registration.id,
        userId: user.id,
        customerName: registration.full_name,
        customerEmail: registration.email,
        golfTournament: formData.golfTournament && eligibility.canAddGolf,
        golfClubRental: formData.golfClubRental,
        golfClubHandedness: formData.golfClubHandedness,
        lineItems,
        totalAmount: pricing.total,
        metadata: {
          originalGrandTotal: registration.grand_total.toString(),
          addOnType: 'golf',
        },
      };

      console.log('💳 Creating golf add-on checkout session:', sessionData);

      // Create checkout session
      const checkoutResult = await createGolfAddOnCheckoutSession(sessionData);

      if (!checkoutResult.success || !checkoutResult.sessionId) {
        throw new Error(checkoutResult.error || 'Failed to create checkout session');
      }

      console.log('✅ Golf add-on checkout session created, redirecting...');

      // Redirect to Stripe checkout
      const stripe = (await import('@stripe/stripe-js')).loadStripe(
        process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
      );

      const stripeInstance = await stripe;
      if (!stripeInstance) {
        throw new Error('Failed to load Stripe');
      }

      const { error: redirectError } = await stripeInstance.redirectToCheckout({
        sessionId: checkoutResult.sessionId,
      });

      if (redirectError) {
        throw new Error(redirectError.message);
      }

    } catch (err) {
      console.error('❌ Error processing golf add-on:', err);

      // Better error handling
      let errorMessage = 'Failed to process golf add-on';
      if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      } else if (err && typeof err === 'object') {
        errorMessage = JSON.stringify(err);
      }

      console.error('Golf add-on error details:', {
        error: err,
        errorType: typeof err,
        errorMessage,
        formData,
        eligibility
      });

      setError(errorMessage);
      setStep('form');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setStep('loading');
      setEligibility(null);
      setError(null);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-green-600" />
            Add Golf Tournament
          </DialogTitle>
          <DialogDescription>
            Add golf tournament participation to your existing IEPA 2025 Conference registration.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Loading State */}
          {step === 'loading' && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2">Checking eligibility...</span>
            </div>
          )}

          {/* Eligibility Check Failed */}
          {step === 'eligibility' && eligibility && !eligibility.eligible && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {eligibility.reason || 'You are not eligible to add golf to this registration.'}
              </AlertDescription>
            </Alert>
          )}

          {/* Golf Add-On Form */}
          {step === 'form' && eligibility && eligibility.eligible && (
            <GolfAddOnForm
              eligibility={eligibility}
              onSubmit={handleFormSubmit}
              loading={isSubmitting}
              error={error}
            />
          )}

          {/* Processing State */}
          {step === 'processing' && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2">Creating checkout session...</span>
            </div>
          )}

          {/* Error Display */}
          {error && step !== 'form' && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Footer Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            {step === 'eligibility' && eligibility && !eligibility.eligible && (
              <Button onClick={checkEligibility}>
                Check Again
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
