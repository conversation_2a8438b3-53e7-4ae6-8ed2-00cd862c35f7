import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('[EMAIL-CONTENT-API] Fetching email content for ID:', params.id);

    if (!params.id) {
      return NextResponse.json({
        success: false,
        error: 'Email ID is required'
      }, { status: 400 });
    }

    // Initialize Supabase admin client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({
        success: false,
        error: 'Missing Supabase configuration'
      }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // Get the email log
    const { data: emailLog, error: fetchError } = await supabase
      .from('iepa_email_log')
      .select('*')
      .eq('id', params.id)
      .single();

    if (fetchError || !emailLog) {
      console.error('[EMAIL-CONTENT-API] Failed to fetch email log:', fetchError);
      return NextResponse.json({
        success: false,
        error: 'Email not found'
      }, { status: 404 });
    }

    // For now, we'll simulate email content since we don't store full content
    // In a real implementation, you would fetch this from your email service or storage
    const mockContent = {
      html_content: generateMockHtmlContent(emailLog),
      text_content: generateMockTextContent(emailLog),
      attachments: emailLog.has_attachments ? [
        { name: 'IEPA_Annual_Meeting_Agenda.pdf', size: 245760 },
        { name: 'Hotel_Information.pdf', size: 156432 }
      ] : []
    };

    console.log(`[EMAIL-CONTENT-API] Retrieved content for email: ${params.id}`);

    return NextResponse.json({
      success: true,
      content: mockContent,
      email: emailLog
    });

  } catch (error) {
    console.error('[EMAIL-CONTENT-API] Failed to fetch email content:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch email content'
    }, { status: 500 });
  }
}

function generateMockHtmlContent(emailLog: any): string {
  const baseContent = emailLog.content_preview || 'Welcome to the IEPA Annual Meeting!';
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${emailLog.subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #1e40af, #059669); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .content { background: #f9fafb; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .footer { text-align: center; color: #6b7280; font-size: 14px; }
        .button { display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Independent Energy Producers Association</h1>
        <p>Annual Meeting 2025</p>
      </div>
      
      <div class="content">
        <h2>${emailLog.subject}</h2>
        <p>Dear ${emailLog.recipient_name || 'Valued Member'},</p>
        
        <p>${baseContent}</p>
        
        <p>We're excited to have you join us for the IEPA Annual Meeting. This year's event promises to be our best yet, with industry-leading speakers, networking opportunities, and valuable insights into the future of energy production in California.</p>
        
        <p><strong>Event Details:</strong></p>
        <ul>
          <li>Date: March 15-17, 2025</li>
          <li>Location: Monterey Plaza Hotel & Spa</li>
          <li>Registration includes all meals and networking events</li>
        </ul>
        
        ${emailLog.email_type === 'registration_confirmation' ? `
          <p><strong>Your Registration:</strong></p>
          <ul>
            <li>Registration Type: ${emailLog.registration_type || 'Standard'}</li>
            <li>Status: Confirmed</li>
            <li>Confirmation ID: ${emailLog.registration_id || emailLog.id}</li>
          </ul>
        ` : ''}
        
        ${emailLog.email_type === 'payment_confirmation' ? `
          <p><strong>Payment Confirmation:</strong></p>
          <p>Thank you for your payment. Your registration is now complete and confirmed.</p>
          <a href="#" class="button">Download Invoice</a>
        ` : ''}
        
        <p>If you have any questions, please don't hesitate to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
        
        <p>Best regards,<br>
        The IEPA Team</p>
      </div>
      
      <div class="footer">
        <p>Independent Energy Producers Association<br>
        1215 K Street, Suite 900, Sacramento, CA 95814<br>
        <a href="mailto:<EMAIL>"><EMAIL></a> | <a href="https://iepa.com">iepa.com</a></p>
        
        <p><small>This email was sent to ${emailLog.recipient_email}. If you no longer wish to receive these emails, you can <a href="#">unsubscribe</a>.</small></p>
      </div>
    </body>
    </html>
  `;
}

function generateMockTextContent(emailLog: any): string {
  const baseContent = emailLog.content_preview || 'Welcome to the IEPA Annual Meeting!';
  
  return `
INDEPENDENT ENERGY PRODUCERS ASSOCIATION
Annual Meeting 2025

${emailLog.subject}

Dear ${emailLog.recipient_name || 'Valued Member'},

${baseContent}

We're excited to have you join us for the IEPA Annual Meeting. This year's event promises to be our best yet, with industry-leading speakers, networking opportunities, and valuable insights into the future of energy production in California.

EVENT DETAILS:
- Date: March 15-17, 2025
- Location: Monterey Plaza Hotel & Spa
- Registration includes all meals and networking events

${emailLog.email_type === 'registration_confirmation' ? `
YOUR REGISTRATION:
- Registration Type: ${emailLog.registration_type || 'Standard'}
- Status: Confirmed
- Confirmation ID: ${emailLog.registration_id || emailLog.id}
` : ''}

${emailLog.email_type === 'payment_confirmation' ? `
PAYMENT CONFIRMATION:
Thank you for your payment. Your registration is now complete and confirmed.
` : ''}

If you have any questions, please don't hesitate to contact <NAME_EMAIL>.

Best regards,
The IEPA Team

---
Independent Energy Producers Association
1215 K Street, Suite 900, Sacramento, CA 95814
<EMAIL> | iepa.com

This email was sent to ${emailLog.recipient_email}.
  `.trim();
}
