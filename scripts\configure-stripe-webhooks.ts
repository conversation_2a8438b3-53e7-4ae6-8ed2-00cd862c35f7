#!/usr/bin/env node

/**
 * Stripe Webhook Configuration Script
 *
 * This script configures Stripe webhooks for both development and production
 * environments for the IEPA conference registration system.
 */

import Stripe from 'stripe';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: '.env.local' });

const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('❌ Error: STRIPE_SECRET_KEY not found in .env.local');
  process.exit(1);
}

// Initialize Stripe client
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-05-28.basil',
  typescript: true,
});

// Webhook configuration
const WEBHOOK_EVENTS: Stripe.WebhookEndpointCreateParams.EnabledEvent[] = [
  'checkout.session.completed',
  'payment_intent.succeeded',
  'payment_intent.payment_failed',
];

const WEBHOOK_ENDPOINTS = {
  development: [
    'http://localhost:3000/api/stripe/webhook',
    'http://localhost:3001/api/stripe/webhook',
    'http://localhost:3002/api/stripe/webhook',
    'http://localhost:8080/api/stripe/webhook',
  ],
  production: ['https://reg.iepa.com/api/stripe/webhook'],
};

interface WebhookConfig {
  id: string;
  url: string;
  secret: string;
  environment: 'development' | 'production';
}

async function listExistingWebhooks(): Promise<Stripe.WebhookEndpoint[]> {
  try {
    console.log('🔍 Checking existing webhooks...');
    const webhooks = await stripe.webhookEndpoints.list({ limit: 100 });

    console.log(`📋 Found ${webhooks.data.length} existing webhooks:`);
    webhooks.data.forEach((webhook, index) => {
      console.log(`  ${index + 1}. ${webhook.url} (${webhook.status})`);
      console.log(`     Events: ${webhook.enabled_events.join(', ')}`);
      console.log(`     ID: ${webhook.id}`);
      console.log('');
    });

    return webhooks.data;
  } catch (error) {
    console.error('❌ Error listing webhooks:', error);
    return [];
  }
}

async function createWebhook(
  url: string,
  environment: 'development' | 'production'
): Promise<WebhookConfig | null> {
  try {
    console.log(`🔧 Creating webhook for ${environment}: ${url}`);

    const webhook = await stripe.webhookEndpoints.create({
      url,
      enabled_events: WEBHOOK_EVENTS,
      description: `IEPA Conference Registration - ${environment}`,
      metadata: {
        environment,
        application: 'iepa-conference-registration',
        created_by: 'configure-stripe-webhooks-script',
        created_at: new Date().toISOString(),
      },
    });

    console.log(`✅ Created webhook: ${webhook.id}`);
    console.log(`   URL: ${webhook.url}`);
    console.log(`   Secret: ${webhook.secret}`);
    console.log('');

    return {
      id: webhook.id,
      url: webhook.url,
      secret: webhook.secret || '',
      environment,
    };
  } catch (error) {
    console.error(`❌ Error creating webhook for ${url}:`, error);
    return null;
  }
}

// Commented out unused function to avoid linting errors
// async function deleteWebhook(webhookId: string, url: string): Promise<boolean> {
//   try {
//     console.log(`🗑️  Deleting webhook: ${webhookId} (${url})`);
//     await stripe.webhookEndpoints.del(webhookId);
//     console.log(`✅ Deleted webhook: ${webhookId}`);
//     return true;
//   } catch (error) {
//     console.error(`❌ Error deleting webhook ${webhookId}:`, error);
//     return false;
//   }
// }

async function updateEnvironmentFile(
  webhookConfigs: WebhookConfig[]
): Promise<void> {
  try {
    const envPath = path.join(process.cwd(), '.env.local');
    let envContent = fs.readFileSync(envPath, 'utf8');

    // Find development webhook secret (use first localhost webhook)
    const devWebhook = webhookConfigs.find(
      w => w.environment === 'development'
    );
    // const prodWebhook = webhookConfigs.find(
    //   w => w.environment === 'production'
    // );

    if (devWebhook) {
      // Update or add STRIPE_WEBHOOK_SECRET
      const webhookSecretRegex = /^STRIPE_WEBHOOK_SECRET=.*$/m;
      const newWebhookSecret = `STRIPE_WEBHOOK_SECRET=${devWebhook.secret}`;

      if (webhookSecretRegex.test(envContent)) {
        envContent = envContent.replace(webhookSecretRegex, newWebhookSecret);
        console.log('✅ Updated STRIPE_WEBHOOK_SECRET in .env.local');
      } else {
        envContent += `\n${newWebhookSecret}\n`;
        console.log('✅ Added STRIPE_WEBHOOK_SECRET to .env.local');
      }

      fs.writeFileSync(envPath, envContent);
    }

    // Create webhook configuration documentation
    const webhookDocsPath = path.join(
      process.cwd(),
      '.docs',
      'stripe-webhook-configuration.md'
    );
    const webhookDocs = generateWebhookDocumentation(webhookConfigs);
    fs.writeFileSync(webhookDocsPath, webhookDocs);
    console.log('✅ Created webhook configuration documentation');
  } catch (error) {
    console.error('❌ Error updating environment file:', error);
  }
}

function generateWebhookDocumentation(webhookConfigs: WebhookConfig[]): string {
  const timestamp = new Date().toISOString();

  return `# Stripe Webhook Configuration

**Generated**: ${timestamp}
**Script**: configure-stripe-webhooks.ts

## Configured Webhooks

${webhookConfigs
  .map(
    config => `
### ${config.environment.toUpperCase()} Environment

- **Webhook ID**: \`${config.id}\`
- **URL**: \`${config.url}\`
- **Secret**: \`${config.secret}\`
- **Events**: 
  - \`checkout.session.completed\`
  - \`payment_intent.succeeded\`
  - \`payment_intent.payment_failed\`

`
  )
  .join('')}

## Environment Variables

### Development (.env.local)
\`\`\`env
STRIPE_WEBHOOK_SECRET=${webhookConfigs.find(w => w.environment === 'development')?.secret || 'NOT_CONFIGURED'}
\`\`\`

### Production
\`\`\`env
STRIPE_WEBHOOK_SECRET=${webhookConfigs.find(w => w.environment === 'production')?.secret || 'NOT_CONFIGURED'}
\`\`\`

## Testing Webhooks

### Development Testing
\`\`\`bash
# Test webhook delivery
curl -X POST ${webhookConfigs.find(w => w.environment === 'development')?.url || 'http://localhost:3000/api/stripe/webhook'} \\
  -H "Content-Type: application/json" \\
  -H "Stripe-Signature: test" \\
  -d '{"type": "checkout.session.completed"}'
\`\`\`

### Using Stripe CLI
\`\`\`bash
# Forward events to local development
stripe listen --forward-to localhost:3000/api/stripe/webhook

# Test specific events
stripe trigger checkout.session.completed
stripe trigger payment_intent.succeeded
stripe trigger payment_intent.payment_failed
\`\`\`

## Webhook Handler

The webhook handler is implemented at:
- **File**: \`src/app/api/stripe/webhook/route.ts\`
- **Endpoint**: \`/api/stripe/webhook\`

## Security Notes

1. **Webhook Secrets**: Keep webhook secrets secure and never commit to version control
2. **Signature Verification**: All webhooks are verified using Stripe's signature verification
3. **Environment Separation**: Development and production use separate webhook endpoints
4. **HTTPS Required**: Production webhooks must use HTTPS endpoints

## Troubleshooting

1. **Webhook Not Receiving Events**: Check Stripe dashboard webhook logs
2. **Signature Verification Failed**: Ensure webhook secret matches environment variable
3. **404 Errors**: Verify webhook URL is accessible and endpoint exists
4. **Database Updates Not Working**: Check webhook handler logs and database permissions
`;
}

async function main() {
  console.log('🚀 IEPA Conference Registration - Stripe Webhook Configuration');
  console.log(
    '================================================================'
  );
  console.log('');

  // List existing webhooks
  const existingWebhooks = await listExistingWebhooks();

  // Check for existing IEPA webhooks
  const iepaWebhooks = existingWebhooks.filter(
    webhook =>
      webhook.url.includes('iepa') ||
      webhook.url.includes('localhost') ||
      webhook.description?.includes('IEPA')
  );

  if (iepaWebhooks.length > 0) {
    console.log('⚠️  Found existing IEPA-related webhooks:');
    iepaWebhooks.forEach(webhook => {
      console.log(`   ${webhook.id}: ${webhook.url}`);
    });
    console.log('');

    // Ask if we should delete existing webhooks
    console.log(
      '🤔 Would you like to delete existing IEPA webhooks and create new ones?'
    );
    console.log('   This script will proceed with creating new webhooks...');
    console.log('');
  }

  const webhookConfigs: WebhookConfig[] = [];

  // Create development webhooks
  console.log('🔧 Creating development webhooks...');
  for (const url of WEBHOOK_ENDPOINTS.development) {
    const config = await createWebhook(url, 'development');
    if (config) {
      webhookConfigs.push(config);
    }
  }

  // Create production webhooks
  console.log('🔧 Creating production webhooks...');
  for (const url of WEBHOOK_ENDPOINTS.production) {
    const config = await createWebhook(url, 'production');
    if (config) {
      webhookConfigs.push(config);
    }
  }

  // Update environment files
  if (webhookConfigs.length > 0) {
    console.log('📝 Updating environment configuration...');
    await updateEnvironmentFile(webhookConfigs);
  }

  console.log('');
  console.log('🎉 Webhook configuration complete!');
  console.log(`✅ Created ${webhookConfigs.length} webhooks`);
  console.log('✅ Updated .env.local with webhook secret');
  console.log(
    '✅ Generated documentation in .docs/stripe-webhook-configuration.md'
  );
  console.log('');
  console.log('📋 Next Steps:');
  console.log('1. Restart your development server to load new webhook secret');
  console.log('2. Test webhook delivery using the test endpoints');
  console.log(
    '3. Configure production environment with production webhook secret'
  );
  console.log('4. Test end-to-end payment flow with webhook processing');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { main as configureStripeWebhooks };
