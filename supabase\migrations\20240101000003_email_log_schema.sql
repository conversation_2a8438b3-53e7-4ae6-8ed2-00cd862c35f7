-- Email logging system for IEPA Annual Meeting Registration

-- Create email log table
CREATE TABLE IF NOT EXISTS public.iepa_email_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Email details
  recipient_email VARCHAR(255) NOT NULL,
  recipient_name VA<PERSON>HA<PERSON>(255),
  sender_email VARCHAR(255) NOT NULL,
  sender_name <PERSON><PERSON><PERSON><PERSON>(255),
  subject VARCHAR(500) NOT NULL,
  
  -- Email classification
  email_type VARCHAR(50) NOT NULL, -- 'registration_confirmation', 'payment_confirmation', 'golf_addon', 'welcome_email', 'custom', etc.
  template_used VARCHAR(100), -- Template identifier if using templates
  
  -- Content reference (don't store full HTML for space)
  content_preview TEXT, -- First 500 chars of email content
  has_attachments BOOLEAN DEFAULT FALSE,
  
  -- Delivery status
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'sent', 'failed', 'pending'
  sendgrid_message_id VARCHAR(255), -- SendGrid message ID for tracking
  error_message TEXT, -- Error details if send failed
  
  -- Related records
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  registration_id UUID, -- Generic registration ID (could be attendee, speaker, or sponsor)
  registration_type VARCHAR(20), -- 'attendee', 'speaker', 'sponsor'
  payment_id UUID, -- Reference to iepa_payments if payment-related
  
  -- Timestamps
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on email log table
ALTER TABLE iepa_email_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies for iepa_email_log
CREATE POLICY "Users can view their own email logs"
    ON iepa_email_log FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all email logs"
    ON iepa_email_log FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

-- Admin users can view all email logs
CREATE POLICY "Admin users can view all email logs"
    ON iepa_email_log FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

-- Add updated_at trigger for email log
CREATE TRIGGER update_iepa_email_log_updated_at
    BEFORE UPDATE ON iepa_email_log
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_user_id ON iepa_email_log(user_id);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_registration_id ON iepa_email_log(registration_id);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_email_type ON iepa_email_log(email_type);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_status ON iepa_email_log(status);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_created_at ON iepa_email_log(created_at);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_recipient_email ON iepa_email_log(recipient_email);
