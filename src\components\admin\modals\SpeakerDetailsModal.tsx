'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { formatCurrency, formatDate } from '@/lib/pdf-generation/utils';
import {
  FiUser,
  FiBriefcase,
  FiMic,
  FiFile,
  FiDownload,
  FiCoffee,
  FiCalendar,
  FiCreditCard,
  FiEye,
} from 'react-icons/fi';

interface SpeakerDetailsModalProps {
  speaker: {
    id: string;
    full_name: string;
    email: string;
    phone_number?: string;
    organization_name: string;
    job_title: string;
    bio?: string;
    presentation_title?: string;
    presentation_duration?: string;
    presentation_description?: string;
    target_audience?: string;
    learning_objectives?: string;
    presentation_file_url?: string;
    headshot_url?: string;
    created_at: string;
    updated_at: string;
    attending_golf?: boolean;
    payment_status?: string;
    registration_fee?: number;
    equipment_needs?: string;
    special_requests?: string;
    dietary_needs?: string;
  };
  open: boolean;
  onClose: () => void;
}

export function SpeakerDetailsModal({
  speaker,
  open,
  onClose,
}: SpeakerDetailsModalProps) {
  if (!speaker) return null;

  const getPaymentStatusColor = (status: string): "default" | "success" | "info" | "destructive" | "outline" | "secondary" => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'outline';
      case 'failed':
        return 'destructive';
      default:
        return 'default';
    }
  };



  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FiMic className="w-5 h-5" />
            <span>Speaker Details - {speaker.full_name}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <FiUser className="w-5 h-5" />
                <span>Personal Information</span>
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Full Name
                </label>
                <p className="text-gray-900">{speaker.full_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Email
                </label>
                <p className="text-gray-900">{speaker.email}</p>
              </div>
              {speaker.phone_number && (
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Phone
                  </label>
                  <p className="text-gray-900">{speaker.phone_number}</p>
                </div>
              )}
            </CardBody>
          </Card>

          {/* Professional Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <FiBriefcase className="w-5 h-5" />
                <span>Professional Information</span>
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Organization
                </label>
                <p className="text-gray-900">{speaker.organization_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Job Title
                </label>
                <p className="text-gray-900">{speaker.job_title}</p>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Bio */}
        {speaker.bio && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Speaker Bio</CardTitle>
            </CardHeader>
            <CardBody>
              <p className="text-gray-900 whitespace-pre-wrap">{speaker.bio}</p>
            </CardBody>
          </Card>
        )}

        {/* Presentation Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center space-x-2">
              <FiMic className="w-5 h-5" />
              <span>Presentation Information</span>
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Presentation Title
                </label>
                <p className="text-gray-900">{speaker.presentation_title || 'Not provided'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Duration
                </label>
                <p className="text-gray-900">{speaker.presentation_duration || 'Not specified'}</p>
              </div>
            </div>
            
            {speaker.presentation_description && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Presentation Description
                </label>
                <p className="text-gray-900 whitespace-pre-wrap">{speaker.presentation_description}</p>
              </div>
            )}

            {speaker.target_audience && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Target Audience
                </label>
                <p className="text-gray-900">{speaker.target_audience}</p>
              </div>
            )}

            {speaker.learning_objectives && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Learning Objectives
                </label>
                <p className="text-gray-900 whitespace-pre-wrap">{speaker.learning_objectives}</p>
              </div>
            )}

            {/* File Downloads */}
            {(speaker.presentation_file_url || speaker.headshot_url) && (
              <div className="mt-6 pt-6 border-t">
                <label className="text-sm font-medium text-gray-500 mb-3 block">
                  Uploaded Files
                </label>
                <div className="space-y-3">
                  {speaker.presentation_file_url && (
                    <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2">
                        <FiFile className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium">Presentation File</span>
                      </div>
                      <a
                        href={speaker.presentation_file_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 text-[var(--iepa-primary-blue)] hover:underline text-sm font-medium"
                      >
                        <FiDownload className="w-3 h-3" />
                        Download
                      </a>
                    </div>
                  )}
                  {speaker.headshot_url && (
                    <div className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                      <div className="flex items-center gap-2">
                        <FiUser className="w-4 h-4 text-gray-600" />
                        <span className="text-sm font-medium">Professional Headshot</span>
                      </div>
                      <a
                        href={speaker.headshot_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 text-[var(--iepa-primary-blue)] hover:underline text-sm font-medium"
                      >
                        <FiEye className="w-3 h-3" />
                        View
                      </a>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardBody>
        </Card>

        {/* Registration & Payment Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <FiCalendar className="w-5 h-5" />
                <span>Registration Information</span>
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Registration Date
                </label>
                <p className="text-gray-900">{formatDate(speaker.created_at)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Last Updated
                </label>
                <p className="text-gray-900">{formatDate(speaker.updated_at)}</p>
              </div>
              {speaker.attending_golf !== undefined && (
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Golf Tournament
                  </label>
                  <Badge variant={speaker.attending_golf ? 'success' : 'secondary'}>
                    {speaker.attending_golf ? 'Participating' : 'Not Participating'}
                  </Badge>
                </div>
              )}
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <FiCreditCard className="w-5 h-5" />
                <span>Payment Information</span>
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              {speaker.payment_status && (
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Payment Status
                  </label>
                  <div className="mt-1">
                    <Badge variant={getPaymentStatusColor(speaker.payment_status)}>
                      {speaker.payment_status.charAt(0).toUpperCase() + speaker.payment_status.slice(1)}
                    </Badge>
                  </div>
                </div>
              )}
              {speaker.registration_fee && (
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Registration Fee
                  </label>
                  <p className="text-gray-900">{formatCurrency(speaker.registration_fee)}</p>
                </div>
              )}
            </CardBody>
          </Card>
        </div>

        {/* Requirements */}
        {(speaker.equipment_needs || speaker.special_requests || speaker.dietary_needs) && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <FiCoffee className="w-5 h-5" />
                <span>Requirements & Preferences</span>
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              {speaker.equipment_needs && (
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Equipment Needs
                  </label>
                  <p className="text-gray-900 whitespace-pre-wrap">{speaker.equipment_needs}</p>
                </div>
              )}
              {speaker.special_requests && (
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Special Requests
                  </label>
                  <p className="text-gray-900 whitespace-pre-wrap">{speaker.special_requests}</p>
                </div>
              )}
              {speaker.dietary_needs && (
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Dietary Restrictions
                  </label>
                  <p className="text-gray-900">{speaker.dietary_needs}</p>
                </div>
              )}
            </CardBody>
          </Card>
        )}
      </DialogContent>
    </Dialog>
  );
}
