## Mobile Navigation Fix – Task List

### Visual and Layout Corrections

- [x] Wrap logo, hamburger menu, and user info inside a single flex container
  `display: flex; align-items: center; justify-content: space-between; padding: 0.75rem;`

- [x] Ensure logo and menu icon align horizontally and do not shift on screen resize

- [x] Relocate or collapse the user info panel for small screens
  Option A: Hide inside hamburger drawer
  Option B: Move below header area

- [x] Add vertical spacing between breadcrumb and header container
  Example: `margin-top: 1rem; padding-left: 1rem;`

- [x] Normalize container shadows, padding, and borders to unify top bar and nav region

### Responsive Behavior

- [x] Add media query adjustments for small screens
  Example:
  ```css
  @media (max-width: 768px) {
    .header-wrapper {
      flex-direction: column;
      align-items: flex-start;
    }
  }

	•	[x] Ensure no overflow or squeeze effect on profile card or logo on narrow viewports
	•	[x] Collapse unnecessary duplicate components between header and drawer (user info especially)

