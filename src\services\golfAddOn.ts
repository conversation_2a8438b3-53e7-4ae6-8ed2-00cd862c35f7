// Golf Add-On Service
// Service functions for golf tournament add-on functionality

import { supabase } from '@/lib/supabase';
import type {
  GolfAddOnEligibility,
  GolfAddOnFormData,
  GolfAddOnPricing,
  GolfAddOnRequest,
  GolfAddOnSessionData,
  AttendeeRegistration,
} from '@/types/golfAddOn';
import { GOLF_ADDON_CONSTANTS } from '@/types/golfAddOn';

/**
 * Check if a user is eligible to add golf to their registration
 */
export async function checkGolfAddOnEligibility(
  userId: string,
  registrationId?: string
): Promise<GolfAddOnEligibility> {
  try {
    console.log('🏌️ Checking golf add-on eligibility for user:', userId);

    // Get user's attendee registration
    let query = supabase
      .from('iepa_attendee_registrations')
      .select('*')
      .eq('user_id', userId)
      .eq('payment_status', 'completed'); // Only allow add-ons for completed registrations

    if (registrationId) {
      query = query.eq('id', registrationId);
    }

    const { data: registrations, error } = await query;

    if (error) {
      console.error('❌ Error fetching registration:', error);
      return {
        eligible: false,
        reason: 'Error checking registration status',
        hasExistingGolf: false,
        canAddGolf: false,
        canAddClubRental: false,
      };
    }

    if (!registrations || registrations.length === 0) {
      return {
        eligible: false,
        reason: 'No completed attendee registration found',
        hasExistingGolf: false,
        canAddGolf: false,
        canAddClubRental: false,
      };
    }

    const registration = registrations[0] as AttendeeRegistration;
    const hasExistingGolf = registration.attending_golf;
    const hasExistingClubRental = registration.golf_club_rental;

    // User is eligible if they have a completed registration but no golf
    const canAddGolf = !hasExistingGolf;
    const canAddClubRental = hasExistingGolf && !hasExistingClubRental;

    const eligible = canAddGolf || canAddClubRental;

    let reason: string | undefined;
    if (!eligible) {
      if (hasExistingGolf && hasExistingClubRental) {
        reason = 'Golf tournament and club rental already included in registration';
      } else if (hasExistingGolf) {
        reason = 'Golf tournament already included in registration';
      }
    }

    return {
      eligible,
      reason,
      currentRegistration: registration,
      hasExistingGolf,
      canAddGolf,
      canAddClubRental,
    };
  } catch (error) {
    console.error('❌ Error checking golf add-on eligibility:', error);
    return {
      eligible: false,
      reason: 'System error checking eligibility',
      hasExistingGolf: false,
      canAddGolf: false,
      canAddClubRental: false,
    };
  }
}

/**
 * Calculate golf add-on pricing
 */
export function calculateGolfAddOnPricing(
  formData: GolfAddOnFormData,
  eligibility: GolfAddOnEligibility
): GolfAddOnPricing {
  let golfTournamentFee = 0;
  let golfClubRentalFee = 0;

  // Only charge for golf tournament if user doesn't already have it
  if (formData.golfTournament && eligibility.canAddGolf) {
    golfTournamentFee = GOLF_ADDON_CONSTANTS.GOLF_TOURNAMENT_FEE;
  }

  // Only charge for club rental if user is adding it
  if (formData.golfClubRental && (eligibility.canAddGolf || eligibility.canAddClubRental)) {
    golfClubRentalFee = GOLF_ADDON_CONSTANTS.GOLF_CLUB_RENTAL_FEE;
  }

  const total = golfTournamentFee + golfClubRentalFee;

  return {
    golfTournamentFee,
    golfClubRentalFee,
    total,
  };
}

/**
 * Create Stripe checkout session for golf add-on
 */
export async function createGolfAddOnCheckoutSession(
  sessionData: GolfAddOnSessionData
): Promise<{ success: boolean; sessionId?: string; error?: string }> {
  try {
    console.log('🏌️ Creating golf add-on checkout session:', sessionData);

    const response = await fetch('/api/golf-addon/create-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sessionData),
    });

    const result = await response.json();

    if (!response.ok) {
      console.error('❌ Failed to create checkout session:', result);
      return {
        success: false,
        error: result.error || 'Failed to create checkout session',
      };
    }

    console.log('✅ Golf add-on checkout session created:', result.sessionId);
    return {
      success: true,
      sessionId: result.sessionId,
    };
  } catch (error) {
    console.error('❌ Error creating golf add-on checkout session:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Update registration with golf add-on data
 */
export async function updateRegistrationWithGolfAddOn(
  registrationId: string,
  golfData: {
    attending_golf: boolean;
    golf_club_rental: boolean;
    golf_club_handedness: string;
    golf_total: number;
    golf_club_rental_total: number;
    grand_total: number;
  },
  paymentData?: {
    payment_id: string;
    payment_status: string;
  }
): Promise<{ success: boolean; error?: string; registration?: AttendeeRegistration }> {
  try {
    console.log('🏌️ Updating registration with golf add-on:', registrationId, golfData);

    const updateData = {
      ...golfData,
      updated_at: new Date().toISOString(),
      ...(paymentData && paymentData),
    };

    const { data, error } = await supabase
      .from('iepa_attendee_registrations')
      .update(updateData)
      .eq('id', registrationId)
      .select()
      .single();

    if (error) {
      console.error('❌ Error updating registration:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    console.log('✅ Registration updated with golf add-on:', data);
    return {
      success: true,
      registration: data as AttendeeRegistration,
    };
  } catch (error) {
    console.error('❌ Error updating registration with golf add-on:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Process golf add-on request (eligibility check + pricing calculation)
 */
export async function processGolfAddOnRequest(
  userId: string,
  registrationId: string,
  formData: GolfAddOnFormData
): Promise<GolfAddOnRequest | { error: string }> {
  try {
    // Check eligibility
    const eligibility = await checkGolfAddOnEligibility(userId, registrationId);
    
    if (!eligibility.eligible || !eligibility.currentRegistration) {
      return { error: eligibility.reason || 'Not eligible for golf add-on' };
    }

    // Calculate pricing
    const pricing = calculateGolfAddOnPricing(formData, eligibility);
    
    if (pricing.total <= 0) {
      return { error: 'No golf add-on items selected or already included' };
    }

    // Calculate new grand total
    const currentGrandTotal = eligibility.currentRegistration.grand_total || 0;
    const newGrandTotal = currentGrandTotal + pricing.total;

    return {
      registrationId,
      golfTournament: formData.golfTournament && eligibility.canAddGolf,
      golfClubRental: formData.golfClubRental,
      golfClubHandedness: formData.golfClubHandedness,
      golfTotal: formData.golfTournament && eligibility.canAddGolf ? pricing.golfTournamentFee : (eligibility.currentRegistration.golf_total || 0),
      golfClubRentalTotal: formData.golfClubRental ? pricing.golfClubRentalFee : 0,
      newGrandTotal,
    };
  } catch (error) {
    console.error('❌ Error processing golf add-on request:', error);
    return { error: error instanceof Error ? error.message : 'Unknown error' };
  }
}
