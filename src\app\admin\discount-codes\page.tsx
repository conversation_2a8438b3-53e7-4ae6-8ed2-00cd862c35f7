'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, CardTitle, Button } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  FiPlus,
  FiEdit,
  FiTrash2,
  FiPercent,
  FiDollarSign,
  FiRefreshCw,
  FiToggleLeft,
  FiToggleRight,
} from 'react-icons/fi';
import { showSuccess, showError } from '@/utils/notifications';
import CreateDiscountCodeForm from '@/components/admin/CreateDiscountCodeForm';

interface DiscountCode {
  id: string;
  code: string;
  name: string;
  description: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number;
  stripe_coupon_id: string | null;
  max_uses: number | null;
  max_uses_per_user: number;
  current_uses: number;
  valid_from: string;
  valid_until: string | null;
  minimum_amount: number | null;
  applicable_registration_types: string[] | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  usage_count: { count: number }[];
}

export default function DiscountCodesPage() {
  const [discountCodes, setDiscountCodes] = useState<DiscountCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const fetchDiscountCodes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/discount-codes');
      const result = await response.json();

      if (result.success) {
        setDiscountCodes(result.data);
      } else {
        showError('Error', 'Failed to fetch discount codes');
      }
    } catch (error) {
      console.error('Error fetching discount codes:', error);
      showError('Error', 'Failed to fetch discount codes');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDiscountCodes();
  }, []);

  const toggleDiscountCode = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/discount-codes/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !currentStatus,
          userId: 'admin-user', // TODO: Get from auth context
        }),
      });

      const result = await response.json();

      if (result.success) {
        showSuccess('Success', `Discount code ${!currentStatus ? 'activated' : 'deactivated'}`);
        fetchDiscountCodes();
      } else {
        showError('Error', result.error || 'Failed to update discount code');
      }
    } catch (error) {
      console.error('Error toggling discount code:', error);
      showError('Error', 'Failed to update discount code');
    }
  };

  const deleteDiscountCode = async (id: string) => {
    if (!confirm('Are you sure you want to delete this discount code?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/discount-codes/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        showSuccess('Success', result.message);
        fetchDiscountCodes();
      } else {
        showError('Error', result.error || 'Failed to delete discount code');
      }
    } catch (error) {
      console.error('Error deleting discount code:', error);
      showError('Error', 'Failed to delete discount code');
    }
  };

  const filteredCodes = discountCodes.filter(
    (code) =>
      code.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      code.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDiscountValue = (type: string, value: number) => {
    return type === 'percentage' ? `${value}%` : `$${value}`;
  };

  const getUsageCount = (code: DiscountCode) => {
    return code.usage_count?.[0]?.count || 0;
  };

  const isExpired = (validUntil: string | null) => {
    if (!validUntil) return false;
    return new Date(validUntil) < new Date();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Discount Codes</h1>
          <p className="text-gray-600 mt-1">
            Manage discount codes and promotional offers
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={fetchDiscountCodes}
            disabled={loading}
            variant="outline"
            size="sm"
          >
            <FiRefreshCw
              className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <FiPlus className="w-4 h-4 mr-2" />
                Create Discount Code
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Discount Code</DialogTitle>
                <DialogDescription>
                  Create a new discount code for conference registrations
                </DialogDescription>
              </DialogHeader>
              <CreateDiscountCodeForm
                onSuccess={() => {
                  setShowCreateDialog(false);
                  fetchDiscountCodes();
                }}
                onCancel={() => setShowCreateDialog(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardBody>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search discount codes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Discount Codes Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FiPercent className="w-5 h-5 mr-2" />
            Discount Codes ({filteredCodes.length})
          </CardTitle>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="text-center py-8">
              <FiRefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">Loading discount codes...</p>
            </div>
          ) : filteredCodes.length === 0 ? (
            <div className="text-center py-8">
              <FiPercent className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">No discount codes found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Code</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Discount</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Valid Until</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCodes.map((code) => (
                  <TableRow key={code.id}>
                    <TableCell>
                      <div className="font-mono font-semibold">
                        {code.code}
                      </div>
                      {code.stripe_coupon_id && (
                        <Badge variant="outline" className="mt-1">
                          Stripe
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{code.name}</div>
                      {code.description && (
                        <div className="text-sm text-gray-500">
                          {code.description}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {code.discount_type === 'percentage' ? (
                          <FiPercent className="w-4 h-4 mr-1" />
                        ) : (
                          <FiDollarSign className="w-4 h-4 mr-1" />
                        )}
                        {formatDiscountValue(code.discount_type, code.discount_value)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {getUsageCount(code)}
                        {code.max_uses && ` / ${code.max_uses}`}
                      </div>
                    </TableCell>
                    <TableCell>
                      {code.valid_until ? (
                        <div className={`text-sm ${isExpired(code.valid_until) ? 'text-red-600' : ''}`}>
                          {new Date(code.valid_until).toLocaleDateString()}
                        </div>
                      ) : (
                        <span className="text-gray-500">No expiry</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={code.is_active ? 'default' : 'secondary'}
                        className={
                          code.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }
                      >
                        {code.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => toggleDiscountCode(code.id, code.is_active)}
                        >
                          {code.is_active ? (
                            <FiToggleRight className="w-4 h-4" />
                          ) : (
                            <FiToggleLeft className="w-4 h-4" />
                          )}
                        </Button>
                        <Button size="sm" variant="outline">
                          <FiEdit className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => deleteDiscountCode(code.id)}
                        >
                          <FiTrash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
