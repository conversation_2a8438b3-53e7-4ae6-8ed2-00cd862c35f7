'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui';
import { FiMail, FiCheckCircle, FiRefreshCw, FiEdit3 } from 'react-icons/fi';
import Link from 'next/link';

// Import new components
import EmailStatusSummary from '@/components/admin/EmailStatusSummary';
import EmailLogCard from '@/components/admin/EmailLogCard';
import EmailLogFilters from '@/components/admin/EmailLogFilters';
import Send<PERSON>mailForm from '@/components/admin/SendEmailForm';
import EmailLogSkeleton from '@/components/admin/EmailLogSkeleton';
import EmailContentPreviewModal from '@/components/admin/EmailContentPreviewModal';

// Import hooks and utilities
import { useEmailLogs, useSendEmail, useClipboard } from '@/hooks/useEmailLogs';
import { generateEmailLogCSV, downloadCSV } from '@/lib/email-log-utils';

interface NotificationState {
  type: 'success' | 'error';
  message: string;
}

export default function AdminEmailsPage() {
  const [notification, setNotification] = useState<NotificationState | null>(
    null
  );
  const [previewEmail, setPreviewEmail] = useState<unknown | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Use custom hooks for email log management
  const {
    emailLogs,
    emailStats,
    pagination,
    loading,
    error,
    filters,
    setFilters,
    resetFilters,
    refreshEmailLogs,
    hasActiveFilters,
    totalResults,
  } = useEmailLogs();

  const { sendEmail, sending } = useSendEmail();
  const { copyToClipboard: copyText } = useClipboard();

  // Handle notifications
  const showNotification = useCallback(
    (type: 'success' | 'error', message: string) => {
      setNotification({ type, message });
      // Auto-hide success notifications after 5 seconds
      if (type === 'success') {
        setTimeout(() => setNotification(null), 5000);
      }
    },
    []
  );

  // Clear notification
  const clearNotification = useCallback(() => {
    setNotification(null);
  }, []);

  // Setup email logging table
  const setupEmailLogging = async () => {
    try {
      clearNotification();

      // First try to add sample logs (this will tell us if table exists)
      const sampleResponse = await fetch('/api/admin/add-sample-email-logs', {
        method: 'POST',
      });

      const sampleResult = await sampleResponse.json();

      if (sampleResult.success) {
        showNotification(
          'success',
          `Email logging is working! Added ${sampleResult.logsAdded} sample email logs.`
        );
        refreshEmailLogs(); // Reload data
      } else if (sampleResult.error?.includes('does not exist')) {
        showNotification(
          'error',
          'Email log table needs to be created. Please run the SQL setup in Supabase.'
        );
      } else {
        showNotification(
          'error',
          sampleResult.error || 'Failed to setup email logging'
        );
      }
    } catch (error) {
      console.error('Setup error:', error);
      showNotification('error', 'Failed to setup email logging');
    }
  };

  // Sync SendGrid delivery status
  const syncSendGridStatus = async () => {
    try {
      clearNotification();

      const response = await fetch('/api/admin/sync-sendgrid-status', {
        method: 'POST',
      });

      const result = await response.json();

      if (result.success) {
        showNotification('success', result.message);
        refreshEmailLogs(); // Reload data to show updated statuses
      } else {
        showNotification(
          'error',
          result.error || 'Failed to sync SendGrid status'
        );
      }
    } catch (error) {
      console.error('Sync error:', error);
      showNotification('error', 'Failed to sync SendGrid status');
    }
  };

  // Handle email sending
  const handleSendEmail = async (emailData: unknown) => {
    try {
      clearNotification();
      await sendEmail(emailData);
      showNotification('success', 'Email sent successfully!');
      refreshEmailLogs(); // Reload email logs to show new email
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to send email';
      showNotification('error', errorMessage);
    }
  };

  // Handle SendGrid ID copy
  const handleCopySendGridId = async (sendGridId: string) => {
    const success = await copyText(sendGridId);
    if (success) {
      showNotification('success', 'SendGrid ID copied to clipboard!');
    } else {
      showNotification('error', 'Failed to copy SendGrid ID');
    }
  };

  // Handle email content preview
  const handleViewContent = (email: unknown) => {
    setPreviewEmail(email);
    setIsPreviewOpen(true);
  };

  // Close preview modal
  const closePreview = () => {
    setIsPreviewOpen(false);
    setPreviewEmail(null);
  };

  // Handle retry failed email
  const handleRetryEmail = async (email: unknown) => {
    try {
      clearNotification();

      const response = await fetch('/api/admin/retry-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailLogId: email.id,
          recipientEmail: email.recipient_email,
          subject: email.subject,
          emailType: email.email_type,
        }),
      });

      const result = await response.json();

      if (result.success) {
        showNotification('success', 'Email retry initiated successfully!');
        refreshEmailLogs(); // Reload to show updated status
      } else {
        showNotification('error', result.error || 'Failed to retry email');
      }
    } catch (error) {
      console.error('Retry error:', error);
      showNotification('error', 'Failed to retry email');
    }
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setFilters({ page: newPage });
  };

  // Handle export
  const handleExport = async () => {
    try {
      clearNotification();

      // Generate filename with current date
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0];
      const filename = `iepa-email-logs-${dateStr}.csv`;

      // Generate CSV content
      const csvContent = generateEmailLogCSV(emailLogs);

      // Download the file
      downloadCSV(csvContent, filename);

      showNotification('success', `Email logs exported to ${filename}`);
    } catch (error) {
      console.error('Export error:', error);
      showNotification('error', 'Failed to export email logs');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Email Center</h1>
          <p className="text-gray-600 mt-1">
            Send emails and manage email communications for IEPA 2025 Annual
            Meeting
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Link href="/admin/email-templates">
            <Button variant="outline" size="sm">
              <FiEdit3 className="w-4 h-4 mr-2" />
              Manage Templates
            </Button>
          </Link>
          {emailStats.totalEmails === 0 && !loading && (
            <Button onClick={setupEmailLogging} variant="default" size="sm">
              <FiMail className="w-4 h-4 mr-2" />
              Setup Email Logging
            </Button>
          )}
          {emailStats.totalEmails > 0 && (
            <Button
              onClick={syncSendGridStatus}
              disabled={loading}
              variant="outline"
              size="sm"
            >
              <FiCheckCircle className="w-4 h-4 mr-2" />
              Sync SendGrid
            </Button>
          )}
          <Button
            onClick={refreshEmailLogs}
            disabled={loading}
            variant="outline"
            size="sm"
          >
            <FiRefreshCw
              className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div
          className={`flex items-center gap-3 p-4 rounded-lg border-l-4 ${
            notification.type === 'success'
              ? 'border-green-500 bg-green-50 text-green-700'
              : 'border-red-500 bg-red-50 text-red-700'
          }`}
        >
          {notification.type === 'success' ? (
            <FiCheckCircle className="w-5 h-5 flex-shrink-0" />
          ) : (
            <FiMail className="w-5 h-5 flex-shrink-0" />
          )}
          <p className="flex-1">{notification.message}</p>
          <button
            onClick={clearNotification}
            className="text-current hover:opacity-70 transition-opacity"
          >
            ×
          </button>
        </div>
      )}

      {/* Compact Status Summary */}
      <EmailStatusSummary
        stats={emailStats}
        loading={loading}
        onRefresh={refreshEmailLogs}
      />

      {/* Main 2-Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Email Logs Panel (2/3 width) */}
        <div className="lg:col-span-2 space-y-6">
          {/* Filters */}
          <EmailLogFilters
            status={filters.status}
            emailType={filters.emailType}
            search={filters.search}
            onStatusChange={status => setFilters({ status })}
            onEmailTypeChange={emailType => setFilters({ emailType })}
            onSearchChange={search => setFilters({ search })}
            onRefresh={refreshEmailLogs}
            onReset={resetFilters}
            onExport={handleExport}
            loading={loading}
            totalResults={totalResults}
          />

          {/* Email Logs */}
          <div className="space-y-4">
            {loading ? (
              <EmailLogSkeleton count={6} />
            ) : error ? (
              <div className="text-center py-12 text-red-600">
                <FiMail className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="font-medium">Error loading email logs</p>
                <p className="text-sm mt-2">{error}</p>
                <Button
                  onClick={refreshEmailLogs}
                  variant="outline"
                  size="sm"
                  className="mt-4"
                >
                  <FiRefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
              </div>
            ) : emailLogs.length === 0 ? (
              <div className="text-center py-12 text-gray-500">
                <FiMail className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="font-medium">No email logs found</p>
                <p className="text-sm mt-2">
                  {hasActiveFilters
                    ? 'Try adjusting your filters'
                    : 'No emails have been sent yet'}
                </p>
                {emailStats.totalEmails === 0 && (
                  <Button
                    onClick={setupEmailLogging}
                    variant="outline"
                    size="sm"
                    className="mt-4"
                  >
                    <FiMail className="w-4 h-4 mr-2" />
                    Setup Email Logging
                  </Button>
                )}
              </div>
            ) : (
              <>
                {/* Email Log Cards */}
                <div className="space-y-3">
                  {emailLogs.map(email => (
                    <EmailLogCard
                      key={email.id}
                      email={email}
                      onCopySendGridId={handleCopySendGridId}
                      onViewContent={handleViewContent}
                      onRetry={handleRetryEmail}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div className="text-sm text-gray-600">
                      Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
                      {Math.min(
                        pagination.page * pagination.limit,
                        pagination.total
                      )}{' '}
                      of {pagination.total} emails
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page <= 1}
                      >
                        Previous
                      </Button>
                      <span className="text-sm text-gray-600 px-3">
                        Page {pagination.page} of {pagination.totalPages}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page >= pagination.totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Send Email Panel (1/3 width) */}
        <div className="lg:col-span-1">
          <SendEmailForm onSendEmail={handleSendEmail} loading={sending} />
        </div>
      </div>

      {/* Email Content Preview Modal */}
      <EmailContentPreviewModal
        email={previewEmail}
        isOpen={isPreviewOpen}
        onClose={closePreview}
        onCopySendGridId={handleCopySendGridId}
      />
    </div>
  );
}
