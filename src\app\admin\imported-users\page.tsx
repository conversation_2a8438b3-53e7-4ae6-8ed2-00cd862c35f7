'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Users, Calendar, TrendingUp, Download } from 'lucide-react';
import {
  userProfileUtils,
  type UserProfile,
  type HistoricalRegistration,
} from '@/lib/user-profile-utils';

interface ImportedUserStats {
  totalImportedUsers: number;
  totalHistoricalRegistrations: number;
  currentYearRegistrations: number;
}

export default function ImportedUsersPage() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [stats, setStats] = useState<ImportedUserStats>({
    totalImportedUsers: 0,
    totalHistoricalRegistrations: 0,
    currentYearRegistrations: 0,
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [userHistory, setUserHistory] = useState<HistoricalRegistration[]>([]);

  // Load imported users and stats
  useEffect(() => {
    loadData();
  }, []);

  // Filter users based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredUsers(users);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = users.filter(
        user =>
          user.full_name.toLowerCase().includes(query) ||
          user.email.toLowerCase().includes(query) ||
          user.organization?.toLowerCase().includes(query) ||
          user.job_title?.toLowerCase().includes(query)
      );
      setFilteredUsers(filtered);
    }
  }, [searchQuery, users]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load imported users
      const { data: usersData, error: usersError } = await supabase
        .from('iepa_user_profiles')
        .select('*')
        .eq('imported_from_2024', true)
        .order('created_at', { ascending: false });

      if (usersError) throw usersError;

      // Load stats
      const statsData = await userProfileUtils.getAttendeeStats();

      setUsers(usersData || []);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUserHistory = async (user: UserProfile) => {
    try {
      const history = await userProfileUtils.getHistoricalRegistrations(
        user.user_id
      );
      setUserHistory(history);
      setSelectedUser(user);
    } catch (error) {
      console.error('Error loading user history:', error);
    }
  };

  const exportUsers = () => {
    const csvContent = [
      // CSV Header
      'Name,Email,Organization,Job Title,Phone,City,State,Import Date',
      // CSV Data
      ...filteredUsers.map(user =>
        [
          user.full_name,
          user.email,
          user.organization || '',
          user.job_title || '',
          user.phone_number || '',
          user.city || '',
          user.state || '',
          user.import_date || '',
        ]
          .map(field => `"${field}"`)
          .join(',')
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `imported-users-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading imported users...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Imported Users</h1>
          <p className="text-gray-600">
            Manage users imported from 2024 conference data
          </p>
        </div>
        <Button onClick={exportUsers} className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export CSV
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Imported Users
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalImportedUsers}</div>
            <p className="text-xs text-muted-foreground">
              From 2024 conference
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Historical Records
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.totalHistoricalRegistrations}
            </div>
            <p className="text-xs text-muted-foreground">Past registrations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Current Registrations
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.currentYearRegistrations}
            </div>
            <p className="text-xs text-muted-foreground">2025 conference</p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search Users</CardTitle>
          <CardDescription>
            Search by name, email, organization, or job title
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Users List and Details */}
      <Tabs defaultValue="list" className="space-y-4">
        <TabsList>
          <TabsTrigger value="list">
            Users List ({filteredUsers.length})
          </TabsTrigger>
          {selectedUser && (
            <TabsTrigger value="details">User Details</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <div className="grid gap-4">
            {filteredUsers.map(user => (
              <Card key={user.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold text-lg">
                          {user.full_name}
                        </h3>
                        <Badge variant="secondary">Imported</Badge>
                      </div>
                      <p className="text-gray-600">{user.email}</p>
                      <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                        {user.organization && (
                          <span>
                            <strong>Organization:</strong> {user.organization}
                          </span>
                        )}
                        {user.job_title && (
                          <span>
                            <strong>Title:</strong> {user.job_title}
                          </span>
                        )}
                        {user.city && user.state && (
                          <span>
                            <strong>Location:</strong> {user.city}, {user.state}
                          </span>
                        )}
                      </div>
                      {user.import_date && (
                        <p className="text-xs text-gray-400">
                          Imported:{' '}
                          {new Date(user.import_date).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadUserHistory(user)}
                    >
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}

            {filteredUsers.length === 0 && (
              <Card>
                <CardContent className="p-12 text-center">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No users found
                  </h3>
                  <p className="text-gray-600">
                    {searchQuery
                      ? 'Try adjusting your search criteria.'
                      : 'No imported users available.'}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {selectedUser && (
          <TabsContent value="details" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{selectedUser.full_name}</CardTitle>
                <CardDescription>
                  User profile and registration history
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Profile Information */}
                <div>
                  <h4 className="font-medium mb-3">Profile Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>Email:</strong> {selectedUser.email}
                    </div>
                    <div>
                      <strong>Phone:</strong>{' '}
                      {selectedUser.phone_number || 'Not provided'}
                    </div>
                    <div>
                      <strong>Organization:</strong>{' '}
                      {selectedUser.organization || 'Not provided'}
                    </div>
                    <div>
                      <strong>Job Title:</strong>{' '}
                      {selectedUser.job_title || 'Not provided'}
                    </div>
                    <div>
                      <strong>Address:</strong>{' '}
                      {selectedUser.street_address || 'Not provided'}
                    </div>
                    <div>
                      <strong>City, State:</strong>{' '}
                      {selectedUser.city && selectedUser.state
                        ? `${selectedUser.city}, ${selectedUser.state}`
                        : 'Not provided'}
                    </div>
                    <div>
                      <strong>Gender:</strong>{' '}
                      {selectedUser.gender || 'Not provided'}
                    </div>
                    <div>
                      <strong>Badge Name:</strong>{' '}
                      {selectedUser.preferred_name_on_badge || 'Not provided'}
                    </div>
                  </div>
                </div>

                {/* Registration History */}
                <div>
                  <h4 className="font-medium mb-3">Registration History</h4>
                  {userHistory.length > 0 ? (
                    <div className="space-y-3">
                      {userHistory.map(registration => (
                        <Card
                          key={registration.id}
                          className="border-l-4 border-l-blue-500"
                        >
                          <CardContent className="p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h5 className="font-medium">
                                {registration.event_name}
                              </h5>
                              <Badge
                                variant={
                                  registration.status === 'publish'
                                    ? 'default'
                                    : 'secondary'
                                }
                              >
                                {registration.status}
                              </Badge>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                              <div>
                                <strong>Type:</strong>{' '}
                                {registration.attendee_type}
                              </div>
                              <div>
                                <strong>Total:</strong> $
                                {registration.grand_total}
                              </div>
                              <div>
                                <strong>Golf:</strong>{' '}
                                {registration.golf_tournament ? 'Yes' : 'No'}
                              </div>
                              <div>
                                <strong>Meals:</strong>{' '}
                                {registration.meals?.length || 0} selected
                              </div>
                              {registration.special_dietary_needs && (
                                <div className="md:col-span-2">
                                  <strong>Dietary Needs:</strong>{' '}
                                  {registration.special_dietary_needs}
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-600">
                      No registration history available.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
