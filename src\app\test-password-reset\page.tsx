'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';

export default function TestPasswordResetPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [testUrl, setTestUrl] = useState('');

  const generateTestResetUrl = () => {
    // Generate a test URL that simulates what Supabase would send
    // Note: This is for testing purposes only - in production, Supabase generates these tokens
    const baseUrl = window.location.origin;
    const mockAccessToken = 'test_access_token_' + Date.now();
    const mockRefreshToken = 'test_refresh_token_' + Date.now();

    const url = `${baseUrl}/auth/reset-password?access_token=${mockAccessToken}&refresh_token=${mockRefreshToken}&type=recovery`;
    setTestUrl(url);
  };

  const generateErrorTestUrl = () => {
    // Generate a test URL with error parameters
    const baseUrl = window.location.origin;
    const url = `${baseUrl}/auth/reset-password?error=invalid_request&error_description=The+request+is+missing+a+required+parameter`;
    setTestUrl(url);
  };

  const generateExpiredTestUrl = () => {
    // Generate a test URL that simulates an expired token
    const baseUrl = window.location.origin;
    const url = `${baseUrl}/auth/reset-password?error=token_expired&error_description=The+token+has+expired`;
    setTestUrl(url);
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="iepa-heading-1 mb-4">🧪 Password Reset Testing</h1>
            <p className="iepa-body">
              Test different password reset scenarios and URL formats
            </p>
            <Badge variant="outline" className="mt-2">
              Development Testing Only
            </Badge>
          </div>

          {/* Test URL Generator */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Generate Test Reset URLs</CardTitle>
              <CardDescription>
                Create different types of password reset URLs for testing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="test-email">Test Email</Label>
                  <Input
                    id="test-email"
                    type="email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button onClick={generateTestResetUrl} className="w-full">
                    Generate Valid Reset URL
                  </Button>
                  <Button
                    onClick={generateErrorTestUrl}
                    variant="outline"
                    className="w-full"
                  >
                    Generate Error URL
                  </Button>
                  <Button
                    onClick={generateExpiredTestUrl}
                    variant="outline"
                    className="w-full"
                  >
                    Generate Expired URL
                  </Button>
                </div>

                {testUrl && (
                  <div className="space-y-2">
                    <Label>Generated Test URL:</Label>
                    <div className="p-3 bg-gray-100 rounded border text-sm break-all">
                      {testUrl}
                    </div>
                    <div className="flex space-x-2">
                      <Button asChild size="sm">
                        <a
                          href={testUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Test This URL
                        </a>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigator.clipboard.writeText(testUrl)}
                      >
                        Copy URL
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Real Password Reset Test */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Real Password Reset Flow</CardTitle>
              <CardDescription>
                Test the actual password reset functionality
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Alert>
                  <AlertDescription>
                    <strong>Note:</strong> This will send a real password reset
                    email if the email exists in the system. Use a test email
                    address that you have access to.
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button asChild className="w-full">
                    <Link href="/auth/forgot-password">
                      Go to Forgot Password Page
                    </Link>
                  </Button>
                  <Button asChild variant="outline" className="w-full">
                    <Link href="/auth-debug">View Debug Dashboard</Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Testing Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Testing Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">
                    1. Test Valid Reset URL
                  </h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Click &quot;Generate Valid Reset URL&quot; and then
                    &quot;Test This URL&quot; to see how the reset password page
                    handles valid tokens.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">
                    2. Test Error Scenarios
                  </h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Use the error URL generators to test how the page handles
                    different error conditions.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">3. Test Real Flow</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Use the &quot;Go to Forgot Password Page&quot; to test the
                    complete flow with real Supabase integration.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">4. Monitor Debug Logs</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Check the debug dashboard to see detailed logs of all
                    authentication operations.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Links */}
          <div className="mt-8 text-center space-x-4">
            <Button variant="outline" asChild>
              <Link href="/auth/login">Login Page</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/auth/signup">Signup Page</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/auth-test">Auth Test Page</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
