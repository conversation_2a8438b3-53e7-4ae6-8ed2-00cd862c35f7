import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

export async function POST() {
  try {
    console.log('[SAMPLE-EMAIL-LOGS] Adding sample email logs...');

    // Sample email logs to demonstrate the functionality
    const sampleLogs = [
      {
        recipient_email: '<EMAIL>',
        recipient_name: '<PERSON>',
        sender_email: '<EMAIL>',
        sender_name: 'IEPA Conference 2025',
        subject: 'IEPA 2025 Conference Registration Confirmation',
        email_type: 'registration_confirmation',
        content_preview: 'Thank you for registering for the IEPA 2025 Conference! We are excited to have you join us for this premier energy industry event...',
        has_attachments: false,
        status: 'sent',
        registration_type: 'attendee',
        sent_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        recipient_email: '<EMAIL>',
        recipient_name: 'Sarah Wilson',
        sender_email: '<EMAIL>',
        sender_name: 'IEPA Conference 2025',
        subject: 'Welcome to IEPA 2025 Conference',
        email_type: 'welcome_email',
        content_preview: 'Welcome to the IEPA 2025 Conference! This email contains important information about your upcoming conference experience...',
        has_attachments: true,
        status: 'sent',
        registration_type: 'attendee',
        sent_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
        created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
      },
      {
        recipient_email: '<EMAIL>',
        recipient_name: 'Mike Johnson',
        sender_email: '<EMAIL>',
        sender_name: 'IEPA Conference 2025',
        subject: 'Payment Confirmation - IEPA 2025 Conference',
        email_type: 'payment_confirmation',
        content_preview: 'Your payment for the IEPA 2025 Conference has been successfully processed. Please find your receipt attached...',
        has_attachments: true,
        status: 'sent',
        registration_type: 'attendee',
        sent_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()
      },
      {
        recipient_email: '<EMAIL>',
        recipient_name: 'Dr. Emily Smith',
        sender_email: '<EMAIL>',
        sender_name: 'IEPA Conference 2025',
        subject: 'Speaker Confirmation - IEPA 2025 Conference',
        email_type: 'speaker_confirmation',
        content_preview: 'Thank you for agreeing to speak at the IEPA 2025 Conference. This email contains important details about your presentation...',
        has_attachments: false,
        status: 'sent',
        registration_type: 'speaker',
        sent_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
      },
      {
        recipient_email: '<EMAIL>',
        recipient_name: 'Energy Sponsor Corp',
        sender_email: '<EMAIL>',
        sender_name: 'IEPA Conference 2025',
        subject: 'Sponsor Payment Instructions - IEPA 2025',
        email_type: 'sponsor_instructions',
        content_preview: 'Thank you for sponsoring the IEPA 2025 Conference. Please find payment instructions below. Payment by check should be sent to...',
        has_attachments: false,
        status: 'sent',
        registration_type: 'sponsor',
        sent_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
      },
      {
        recipient_email: '<EMAIL>',
        recipient_name: 'Failed User',
        sender_email: '<EMAIL>',
        sender_name: 'IEPA Conference 2025',
        subject: 'Test Email - Failed Delivery',
        email_type: 'custom',
        content_preview: 'This is a test email that failed to deliver...',
        has_attachments: false,
        status: 'failed',
        error_message: 'Invalid email address - recipient does not exist',
        created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
      },
      {
        recipient_email: '<EMAIL>',
        recipient_name: 'Pending User',
        sender_email: '<EMAIL>',
        sender_name: 'IEPA Conference 2025',
        subject: 'Test Email - Pending Delivery',
        email_type: 'custom',
        content_preview: 'This email is still pending delivery...',
        has_attachments: false,
        status: 'pending',
        created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString() // 10 minutes ago
      }
    ];

    // Check if table exists first
    const { error: testError } = await supabase
      .from('iepa_email_log')
      .select('id')
      .limit(1);

    if (testError) {
      return NextResponse.json({
        success: false,
        error: 'Email log table does not exist',
        message: 'Please create the email log table first using the setup endpoint',
        tableExists: false
      }, { status: 400 });
    }

    // Insert sample logs
    const { data: insertedLogs, error: insertError } = await supabase
      .from('iepa_email_log')
      .insert(sampleLogs)
      .select();

    if (insertError) {
      console.error('[SAMPLE-EMAIL-LOGS] Insert error:', insertError);
      return NextResponse.json({
        success: false,
        error: insertError.message,
        details: 'Failed to insert sample email logs'
      }, { status: 500 });
    }

    console.log(`[SAMPLE-EMAIL-LOGS] Added ${insertedLogs?.length || 0} sample email logs`);

    return NextResponse.json({
      success: true,
      message: `Successfully added ${insertedLogs?.length || 0} sample email logs`,
      logsAdded: insertedLogs?.length || 0,
      sampleTypes: ['registration_confirmation', 'welcome_email', 'payment_confirmation', 'speaker_confirmation', 'sponsor_instructions', 'failed', 'pending'],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[SAMPLE-EMAIL-LOGS] Failed to add sample logs:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to add sample email logs',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
