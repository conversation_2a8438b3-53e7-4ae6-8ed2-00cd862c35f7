'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui';

export default function FixAuthPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const fixAuth = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/fix-admin-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Fix Admin Authentication</h1>
        
        <p className="text-gray-600 mb-6">
          This will create a proper admin user using Supabase Auth API to fix the "Database error querying schema" issue.
        </p>
        
        <Button 
          onClick={fixAuth} 
          disabled={loading}
          className="mb-6"
        >
          {loading ? 'Fixing Authentication...' : 'Fix Admin Authentication'}
        </Button>
        
        {result && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-2">Result:</h2>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
