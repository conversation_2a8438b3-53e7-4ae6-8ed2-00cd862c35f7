/**
 * <PERSON><PERSON><PERSON> to update IEPA agenda data in Supabase
 * This script updates the iepa_agenda_days and iepa_agenda_events tables
 * with the new 2025 conference schedule
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// New agenda data from user's JSON
const newAgendaData = [
  {
    dayNumber: 1,
    title: 'Day 1 - September 15, 2025',
    date: '2025-09-15',
    color: 'var(--iepa-primary-blue)',
    events: [
      {
        time: '11:00 AM',
        title: 'Golf Tournament',
        description: 'Optional golf tournament at South Lake Tahoe Golf Course'
      },
      {
        time: '3:00 PM',
        title: 'Registration & Check-in',
        description: 'Welcome reception begins'
      },
      {
        time: '6:00 PM',
        title: 'Welcome Reception and Dinner',
        description: 'Cocktail reception with hors d\'oeuvres and plated dinner'
      }
    ]
  },
  {
    dayNumber: 2,
    title: 'Day 2 - Tuesday, September 16, 2025',
    date: '2025-09-16',
    color: 'var(--iepa-secondary-green)',
    events: [
      {
        time: '7:30 AM',
        title: 'Breakfast',
        description: 'Continental breakfast and networking'
      },
      {
        time: '8:30 AM',
        title: 'Speakers and Panel Discussions',
        description: 'Morning session with industry experts and panel discussions'
      },
      {
        time: '12:00 PM',
        title: 'Lunch with Keynote Speaker',
        description: 'Networking lunch with featured keynote presentation'
      },
      {
        time: '1:00 PM',
        title: 'Speakers and Panel Discussions',
        description: 'Afternoon session with industry experts and panel discussions'
      },
      {
        time: '6:00 PM',
        title: 'Reception and Dinner',
        description: 'Evening cocktail reception and plated dinner'
      }
    ]
  },
  {
    dayNumber: 3,
    title: 'Day 3 - September 17, 2025',
    date: '2025-09-17',
    color: 'var(--iepa-accent-teal)',
    events: [
      {
        time: '8:00 AM',
        title: 'Breakfast',
        description: 'Final networking breakfast'
      },
      {
        time: '8:30 AM',
        title: 'Speakers and Panel Discussions',
        description: 'Final morning session with industry experts'
      },
      {
        time: '11:30 AM',
        title: 'Final thoughts and Conclusion',
        description: 'Wrap-up presentations and closing remarks'
      },
      {
        time: '12:00 PM',
        title: 'Closing Lunch',
        description: 'Conference conclusion lunch and departures'
      },
      {
        time: '1:00 PM',
        title: 'Departure',
        description: 'Check-out and safe travels'
      }
    ]
  }
];

async function updateAgendaData() {
  console.log('🔄 Starting agenda data update...');

  try {
    // Step 1: Clear existing data
    console.log('🗑️ Clearing existing agenda data...');
    
    // Delete existing events first (due to foreign key constraints)
    const { error: deleteEventsError } = await supabase
      .from('iepa_agenda_events')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

    if (deleteEventsError) {
      console.error('❌ Error deleting existing events:', deleteEventsError);
      throw deleteEventsError;
    }

    // Delete existing days
    const { error: deleteDaysError } = await supabase
      .from('iepa_agenda_days')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

    if (deleteDaysError) {
      console.error('❌ Error deleting existing days:', deleteDaysError);
      throw deleteDaysError;
    }

    console.log('✅ Existing agenda data cleared');

    // Step 2: Insert new agenda days
    console.log('📅 Inserting new agenda days...');
    
    const agendaDaysToInsert = newAgendaData.map((day, index) => ({
      day_number: day.dayNumber,
      title: day.title,
      date: day.date,
      color: day.color,
      sort_order: index + 1,
      is_active: true
    }));

    const { data: insertedDays, error: insertDaysError } = await supabase
      .from('iepa_agenda_days')
      .insert(agendaDaysToInsert)
      .select('*');

    if (insertDaysError) {
      console.error('❌ Error inserting agenda days:', insertDaysError);
      throw insertDaysError;
    }

    console.log(`✅ Inserted ${insertedDays?.length} agenda days`);

    // Step 3: Insert new agenda events
    console.log('📋 Inserting new agenda events...');

    const agendaEventsToInsert: any[] = [];

    // Helper function to convert time string to 24-hour format for sorting
    function timeToSortOrder(timeStr: string): number {
      const [time, period] = timeStr.split(' ');
      const [hours, minutes] = time.split(':').map(Number);
      let hour24 = hours;

      if (period === 'PM' && hours !== 12) {
        hour24 += 12;
      } else if (period === 'AM' && hours === 12) {
        hour24 = 0;
      }

      return hour24 * 100 + (minutes || 0); // e.g., 11:00 AM = 1100, 3:00 PM = 1500
    }

    newAgendaData.forEach((day, dayIndex) => {
      const dayId = insertedDays?.[dayIndex]?.id;
      if (!dayId) {
        throw new Error(`Missing day ID for day ${dayIndex + 1}`);
      }

      // Sort events by time chronologically
      const sortedEvents = [...day.events].sort((a, b) =>
        timeToSortOrder(a.time) - timeToSortOrder(b.time)
      );

      sortedEvents.forEach((event, eventIndex) => {
        agendaEventsToInsert.push({
          agenda_day_id: dayId,
          time: event.time,
          title: event.title,
          description: event.description,
          sort_order: eventIndex + 1, // Now based on chronological order
          is_active: true
        });
      });
    });

    const { data: insertedEvents, error: insertEventsError } = await supabase
      .from('iepa_agenda_events')
      .insert(agendaEventsToInsert)
      .select('*');

    if (insertEventsError) {
      console.error('❌ Error inserting agenda events:', insertEventsError);
      throw insertEventsError;
    }

    console.log(`✅ Inserted ${insertedEvents?.length} agenda events`);

    // Step 4: Verify the data
    console.log('🔍 Verifying updated data...');
    
    const { data: verifyDays, error: verifyError } = await supabase
      .from('iepa_agenda_days')
      .select(`
        *,
        iepa_agenda_events (*)
      `)
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (verifyError) {
      console.error('❌ Error verifying data:', verifyError);
      throw verifyError;
    }

    console.log('✅ Data verification successful:');
    verifyDays?.forEach((day: any) => {
      console.log(`  📅 ${day.title} (${day.iepa_agenda_events?.length} events)`);
    });

    console.log('🎉 Agenda data update completed successfully!');
    console.log('🌐 You can now test the changes at: http://localhost:6969/agenda');

  } catch (error) {
    console.error('❌ Failed to update agenda data:', error);
    process.exit(1);
  }
}

// Run the update
updateAgendaData();
