const { test, expect } = require('@playwright/test');

// Test configuration for production environment
const TEST_SPEAKER = {
  firstName: 'Dr. <PERSON>',
  lastName: '<PERSON>',
  email: '<EMAIL>',
  password: 'TestPass123!',
  organization: 'Renewable Energy Institute',
  jobTitle: 'Senior Research Director',
  phone: '(*************',
  bio: 'Dr. <PERSON> is a leading expert in renewable energy systems with over 15 years of experience in solar and wind technology development.',
  nameOnBadge: 'Dr. <PERSON>',
  streetAddress: '456 Innovation Drive',
  city: 'San Francisco',
  state: 'CA',
  zipCode: '94105',
  presentationTitle: 'Future of Solar Energy Storage',
  presentationDescription: 'An in-depth look at emerging technologies in solar energy storage and their impact on grid stability.',
  sessionPreference: 'Morning',
  avRequirements: 'Projector, microphone, laptop connection'
};

test.describe('Speaker Registration Flow - Production E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set viewport for consistent testing
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Navigate to speaker registration
    await page.goto('/register/speaker');
    await page.waitForLoadState('networkidle');
  });

  test('should display speaker registration page with benefits box', async ({ page }) => {
    console.log('🧪 Testing speaker registration page layout...');
    
    // Check page title and main elements
    await expect(page).toHaveTitle(/IEPA.*Conference.*Registration/);
    await expect(page.locator('h1:has-text("Speaker Registration")')).toBeVisible();
    
    // Look for speaker benefits box (should be green and at top)
    const benefitsBox = page.locator('text=Speaker Benefits, text=Complimentary, text=Lodging, text=Meals');
    const benefitsCount = await benefitsBox.count();
    
    if (benefitsCount > 0) {
      console.log('✅ Found speaker benefits information');
      await page.screenshot({ path: 'test-results/speaker-benefits-box.png', fullPage: true });
    }
    
    // Take screenshot of full page
    await page.screenshot({ path: 'test-results/speaker-registration-page.png', fullPage: true });
    
    console.log('✅ Speaker registration page layout verified');
  });

  test('should display speaker registration type options (comped vs paid)', async ({ page }) => {
    console.log('🧪 Testing speaker registration type options...');
    
    // Look for registration type selection
    const compedOption = page.locator('text=Comped, text=$0, text=Complimentary');
    const paidOption = page.locator('text=Paid, text=$1,500, text=$1500');
    
    const compedCount = await compedOption.count();
    const paidCount = await paidOption.count();
    
    if (compedCount > 0) {
      console.log('✅ Found comped speaker option ($0)');
    }
    
    if (paidCount > 0) {
      console.log('✅ Found paid speaker option ($1,500)');
    }
    
    // Take screenshot of registration options
    await page.screenshot({ path: 'test-results/speaker-registration-types.png', fullPage: true });
    
    console.log('✅ Speaker registration type options verified');
  });

  test('should display file upload functionality for presentations', async ({ page }) => {
    console.log('🧪 Testing presentation file upload functionality...');
    
    // Look for file upload elements
    const fileInputs = page.locator('input[type="file"]');
    const uploadButtons = page.locator('button:has-text("Upload"), button:has-text("Choose File"), text=Upload Presentation');
    const fileTypes = page.locator('text=PDF, text=PPT, text=PPTX, text=Word, text=.pdf, text=.ppt, text=.pptx, text=.doc, text=.docx');
    
    const fileInputCount = await fileInputs.count();
    const uploadButtonCount = await uploadButtons.count();
    const fileTypeCount = await fileTypes.count();
    
    console.log(`✅ Found ${fileInputCount} file input(s)`);
    console.log(`✅ Found ${uploadButtonCount} upload button(s)`);
    console.log(`✅ Found ${fileTypeCount} file type reference(s)`);
    
    // Take screenshot of file upload section
    await page.screenshot({ path: 'test-results/speaker-file-upload.png', fullPage: true });
    
    console.log('✅ File upload functionality verified');
  });

  test('should fill out speaker-specific form fields', async ({ page }) => {
    console.log('🧪 Testing speaker form field completion...');
    
    // Fill basic speaker information
    await page.fill('input[placeholder*="first name"], input[name="firstName"]', TEST_SPEAKER.firstName);
    await page.fill('input[placeholder*="last name"], input[name="lastName"]', TEST_SPEAKER.lastName);
    await page.fill('input[placeholder*="email"], input[name="email"]', TEST_SPEAKER.email);
    await page.fill('input[placeholder*="organization"], input[name="organization"]', TEST_SPEAKER.organization);
    await page.fill('input[placeholder*="job title"], input[name="jobTitle"]', TEST_SPEAKER.jobTitle);
    
    await page.screenshot({ path: 'test-results/speaker-basic-info-filled.png', fullPage: true });
    
    // Fill speaker bio
    const bioField = page.locator('textarea[placeholder*="bio"], textarea[name="bio"], textarea[placeholder*="biography"]');
    if (await bioField.isVisible()) {
      await bioField.fill(TEST_SPEAKER.bio);
      console.log('✅ Filled speaker bio');
    }
    
    // Fill name on badge
    const badgeField = page.locator('input[placeholder*="badge"], input[name="nameOnBadge"]');
    if (await badgeField.isVisible()) {
      await badgeField.fill(TEST_SPEAKER.nameOnBadge);
      console.log('✅ Filled name on badge');
    }
    
    // Fill contact information
    await page.fill('input[placeholder*="phone"], input[name="phone"]', TEST_SPEAKER.phone);
    await page.fill('input[placeholder*="address"], input[name="streetAddress"]', TEST_SPEAKER.streetAddress);
    await page.fill('input[placeholder*="city"], input[name="city"]', TEST_SPEAKER.city);
    
    // Select state
    const stateSelect = page.locator('select[name="state"]');
    if (await stateSelect.isVisible()) {
      await stateSelect.selectOption(TEST_SPEAKER.state);
    }
    
    await page.fill('input[placeholder*="zip"], input[name="zipCode"]', TEST_SPEAKER.zipCode);
    
    await page.screenshot({ path: 'test-results/speaker-contact-info-filled.png', fullPage: true });
    
    console.log('✅ Speaker form fields completed');
  });

  test('should fill presentation details', async ({ page }) => {
    console.log('🧪 Testing presentation details form...');
    
    // Fill presentation title
    const titleField = page.locator('input[placeholder*="title"], input[name="presentationTitle"], input[placeholder*="presentation"]');
    if (await titleField.isVisible()) {
      await titleField.fill(TEST_SPEAKER.presentationTitle);
      console.log('✅ Filled presentation title');
    }
    
    // Fill presentation description
    const descField = page.locator('textarea[placeholder*="description"], textarea[name="presentationDescription"], textarea[placeholder*="abstract"]');
    if (await descField.isVisible()) {
      await descField.fill(TEST_SPEAKER.presentationDescription);
      console.log('✅ Filled presentation description');
    }
    
    // Select session preference
    const sessionField = page.locator('select[name="sessionPreference"], input[name="sessionPreference"]');
    if (await sessionField.isVisible()) {
      if (await sessionField.getAttribute('type') === 'select-one') {
        await sessionField.selectOption(TEST_SPEAKER.sessionPreference);
      } else {
        await sessionField.fill(TEST_SPEAKER.sessionPreference);
      }
      console.log('✅ Set session preference');
    }
    
    // Fill A/V requirements
    const avField = page.locator('textarea[placeholder*="requirements"], textarea[name="avRequirements"], input[placeholder*="audio"]');
    if (await avField.isVisible()) {
      await avField.fill(TEST_SPEAKER.avRequirements);
      console.log('✅ Filled A/V requirements');
    }
    
    await page.screenshot({ path: 'test-results/speaker-presentation-details.png', fullPage: true });
    
    console.log('✅ Presentation details completed');
  });

  test('should test meal and lodging selections for speaker packages', async ({ page }) => {
    console.log('🧪 Testing speaker meal and lodging options...');
    
    // Look for meal selection elements
    const mealElements = page.locator('text=Meal, text=Breakfast, text=Lunch, text=Dinner, input[name*="meal"]');
    const mealCount = await mealElements.count();
    
    if (mealCount > 0) {
      console.log(`✅ Found ${mealCount} meal-related element(s)`);
    }
    
    // Look for lodging elements
    const lodgingElements = page.locator('text=Lodging, text=Hotel, text=Night, input[name*="lodging"], input[name*="night"]');
    const lodgingCount = await lodgingElements.count();
    
    if (lodgingCount > 0) {
      console.log(`✅ Found ${lodgingCount} lodging-related element(s)`);
    }
    
    // Look for speaker-specific benefits
    const benefitElements = page.locator('text=Complimentary, text=Included, text=Speaker Benefits');
    const benefitCount = await benefitElements.count();
    
    if (benefitCount > 0) {
      console.log(`✅ Found ${benefitCount} speaker benefit element(s)`);
    }
    
    await page.screenshot({ path: 'test-results/speaker-meal-lodging-options.png', fullPage: true });
    
    console.log('✅ Speaker meal and lodging testing completed');
  });

  test('should test form validation for speaker registration', async ({ page }) => {
    console.log('🧪 Testing speaker form validation...');
    
    // Try to submit without required fields
    const submitButton = page.locator('button[type="submit"], button:has-text("Submit"), button:has-text("Register")');
    
    if (await submitButton.isVisible()) {
      await submitButton.click();
      await page.waitForTimeout(1000);
      
      // Look for validation errors
      const errorElements = page.locator('[class*="error"], [class*="invalid"], .text-red-500, text=required, text=Required');
      const errorCount = await errorElements.count();
      
      if (errorCount > 0) {
        console.log(`✅ Found ${errorCount} validation error(s)`);
        await page.screenshot({ path: 'test-results/speaker-validation-errors.png', fullPage: true });
      }
    }
    
    console.log('✅ Speaker form validation tested');
  });

  test('should test responsive design for speaker registration', async ({ page }) => {
    console.log('🧪 Testing speaker registration responsive design...');
    
    // Test mobile viewport (375px)
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/speaker-mobile-375px.png', fullPage: true });
    
    // Test tablet viewport (768px)
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/speaker-tablet-768px.png', fullPage: true });
    
    // Test desktop viewport (1200px)
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/speaker-desktop-1200px.png', fullPage: true });
    
    console.log('✅ Speaker responsive design testing completed');
  });

  test('should verify speaker benefits display at top of page', async ({ page }) => {
    console.log('🧪 Testing speaker benefits display...');
    
    // Check if benefits box is at the top of the page
    const benefitsBox = page.locator('text=Speaker Benefits').first();
    
    if (await benefitsBox.isVisible()) {
      const boundingBox = await benefitsBox.boundingBox();
      
      if (boundingBox && boundingBox.y < 300) {
        console.log('✅ Speaker benefits box found near top of page');
      }
      
      // Check for green styling (benefits box should be green)
      const parentElement = benefitsBox.locator('..');
      const classList = await parentElement.getAttribute('class');
      
      if (classList && (classList.includes('green') || classList.includes('success'))) {
        console.log('✅ Benefits box appears to have green styling');
      }
    }
    
    await page.screenshot({ path: 'test-results/speaker-benefits-verification.png', fullPage: true });
    
    console.log('✅ Speaker benefits display verified');
  });

  test('should test file upload acceptance for different file types', async ({ page }) => {
    console.log('🧪 Testing file upload acceptance...');
    
    // Look for file input with accept attribute
    const fileInput = page.locator('input[type="file"]').first();
    
    if (await fileInput.isVisible()) {
      const acceptAttr = await fileInput.getAttribute('accept');
      
      if (acceptAttr) {
        console.log(`✅ File input accepts: ${acceptAttr}`);
        
        // Check if it includes expected file types
        const expectedTypes = ['.pdf', '.ppt', '.pptx', '.doc', '.docx'];
        const acceptedTypes = expectedTypes.filter(type => acceptAttr.includes(type));
        
        console.log(`✅ Accepted presentation file types: ${acceptedTypes.join(', ')}`);
      }
    }
    
    // Look for file type instructions
    const fileTypeText = page.locator('text=PDF, text=PPT, text=PPTX, text=Word, text=.pdf, text=.ppt');
    const fileTypeCount = await fileTypeText.count();
    
    if (fileTypeCount > 0) {
      console.log(`✅ Found ${fileTypeCount} file type instruction(s)`);
    }
    
    await page.screenshot({ path: 'test-results/speaker-file-types.png', fullPage: true });
    
    console.log('✅ File upload acceptance testing completed');
  });
});

test.describe('Speaker Registration - Authentication Integration', () => {
  test('should handle speaker authentication flow', async ({ page }) => {
    console.log('🧪 Testing speaker authentication integration...');
    
    // Navigate to login page
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of login page
    await page.screenshot({ path: 'test-results/speaker-auth-login.png', fullPage: true });
    
    // Check for login elements
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    
    console.log('✅ Speaker authentication flow verified');
  });
});
