// Registration Constraints Hook for IEPA 2025 Conference Registration
// React hook for checking registration constraints and existing registrations

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  checkRegistrationConstraints,
  getUserRegistrationStatus,
  canEditRegistration,
  type RegistrationConstraintCheck,
  type ExistingRegistration,
} from '@/services/registrationConstraints';

interface UseRegistrationConstraintsProps {
  registrationType: 'attendee' | 'speaker' | 'sponsor';
  attendeeType?: 'attendee' | 'spouse' | 'child';
  autoCheck?: boolean;
}

interface UseRegistrationConstraintsReturn {
  constraintCheck: RegistrationConstraintCheck | null;
  loading: boolean;
  error: string | null;
  checkConstraints: () => Promise<void>;
  refresh: () => Promise<void>;
}

/**
 * Hook to check registration constraints for a specific registration type
 */
export function useRegistrationConstraints({
  registrationType,
  attendeeType = 'attendee',
  autoCheck = true,
}: UseRegistrationConstraintsProps): UseRegistrationConstraintsReturn {
  const { user } = useAuth();
  const [constraintCheck, setConstraintCheck] = useState<RegistrationConstraintCheck | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkConstraints = useCallback(async () => {
    if (!user?.id) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const result = await checkRegistrationConstraints(user.id, registrationType, attendeeType);
      setConstraintCheck(result);
    } catch (err) {
      console.error('Error checking registration constraints:', err);
      setError(err instanceof Error ? err.message : 'Failed to check registration constraints');
    } finally {
      setLoading(false);
    }
  }, [user?.id, registrationType, attendeeType]);

  useEffect(() => {
    if (autoCheck && user?.id) {
      checkConstraints();
    }
  }, [autoCheck, checkConstraints, user?.id]);

  return {
    constraintCheck,
    loading,
    error,
    checkConstraints,
    refresh: checkConstraints,
  };
}

interface UseUserRegistrationStatusReturn {
  hasAttendeeRegistration: boolean;
  hasSpeakerRegistration: boolean;
  hasSponsorRegistration: boolean;
  registrations: ExistingRegistration[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

/**
 * Hook to get overall registration status for a user
 */
export function useUserRegistrationStatus(): UseUserRegistrationStatusReturn {
  const { user } = useAuth();
  const [hasAttendeeRegistration, setHasAttendeeRegistration] = useState(false);
  const [hasSpeakerRegistration, setHasSpeakerRegistration] = useState(false);
  const [hasSponsorRegistration, setHasSponsorRegistration] = useState(false);
  const [registrations, setRegistrations] = useState<ExistingRegistration[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    if (!user?.id) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const status = await getUserRegistrationStatus(user.id);
      setHasAttendeeRegistration(status.hasAttendeeRegistration);
      setHasSpeakerRegistration(status.hasSpeakerRegistration);
      setHasSponsorRegistration(status.hasSponsorRegistration);
      setRegistrations(status.registrations);
    } catch (err) {
      console.error('Error fetching user registration status:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch registration status');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    if (user?.id) {
      fetchStatus();
    }
  }, [fetchStatus, user?.id]);

  return {
    hasAttendeeRegistration,
    hasSpeakerRegistration,
    hasSponsorRegistration,
    registrations,
    loading,
    error,
    refresh: fetchStatus,
  };
}

interface UseRegistrationEditPermissionsProps {
  registrationId: string;
  registrationType: 'attendee' | 'speaker' | 'sponsor';
  autoCheck?: boolean;
}

interface UseRegistrationEditPermissionsReturn {
  canEdit: boolean;
  reason: string;
  registration: ExistingRegistration | null;
  loading: boolean;
  error: string | null;
  checkPermissions: () => Promise<void>;
}

/**
 * Hook to check if a user can edit a specific registration
 */
export function useRegistrationEditPermissions({
  registrationId,
  registrationType,
  autoCheck = true,
}: UseRegistrationEditPermissionsProps): UseRegistrationEditPermissionsReturn {
  const { user } = useAuth();
  const [canEdit, setCanEdit] = useState(false);
  const [reason, setReason] = useState('');
  const [registration, setRegistration] = useState<ExistingRegistration | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkPermissions = useCallback(async () => {
    if (!user?.id || !registrationId) {
      setError('User not authenticated or registration ID missing');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const result = await canEditRegistration(user.id, registrationId, registrationType);
      setCanEdit(result.canEdit);
      setReason(result.reason);
      setRegistration(result.registration);
    } catch (err) {
      console.error('Error checking edit permissions:', err);
      setError(err instanceof Error ? err.message : 'Failed to check edit permissions');
    } finally {
      setLoading(false);
    }
  }, [user?.id, registrationId, registrationType]);

  useEffect(() => {
    if (autoCheck && user?.id && registrationId) {
      checkPermissions();
    }
  }, [autoCheck, checkPermissions, user?.id, registrationId]);

  return {
    canEdit,
    reason,
    registration,
    loading,
    error,
    checkPermissions,
  };
}

/**
 * Utility hook to determine which registration types a user can access
 */
export function useAvailableRegistrationTypes() {
  const { user } = useAuth();
  const [availableTypes, setAvailableTypes] = useState<{
    attendee: boolean;
    speaker: boolean;
    sponsor: boolean;
  }>({
    attendee: true,
    speaker: true,
    sponsor: true,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkAvailability = useCallback(async () => {
    if (!user?.id) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const [attendeeCheck, speakerCheck, sponsorCheck] = await Promise.all([
        checkRegistrationConstraints(user.id, 'attendee', 'attendee'),
        checkRegistrationConstraints(user.id, 'speaker'),
        checkRegistrationConstraints(user.id, 'sponsor'),
      ]);

      setAvailableTypes({
        attendee: attendeeCheck.canRegister,
        speaker: speakerCheck.canRegister,
        sponsor: sponsorCheck.canRegister,
      });
    } catch (err) {
      console.error('Error checking available registration types:', err);
      setError(err instanceof Error ? err.message : 'Failed to check available registration types');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    if (user?.id) {
      checkAvailability();
    }
  }, [checkAvailability, user?.id]);

  return {
    availableTypes,
    loading,
    error,
    refresh: checkAvailability,
  };
}
