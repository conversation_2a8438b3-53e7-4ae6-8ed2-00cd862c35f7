import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmailLogCard from '../EmailLogCard';

// Mock the UI components
jest.mock('@/components/ui', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardBody: ({ children, className }: any) => <div className={className}>{children}</div>,
  Badge: ({ children, className }: any) => <span className={className}>{children}</span>,
  Button: ({ children, onClick, disabled, className, title, ...props }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      className={className} 
      title={title}
      {...props}
    >
      {children}
    </button>
  ),
}));

// Mock React Icons
jest.mock('react-icons/fi', () => ({
  FiCheckCircle: () => <span data-testid="check-icon" />,
  FiXCircle: () => <span data-testid="x-icon" />,
  FiClock: () => <span data-testid="clock-icon" />,
  FiMail: () => <span data-testid="mail-icon" />,
  FiUser: () => <span data-testid="user-icon" />,
  FiCalendar: () => <span data-testid="calendar-icon" />,
  FiExternalLink: () => <span data-testid="external-link-icon" />,
  FiCopy: () => <span data-testid="copy-icon" />,
  FiPaperclip: () => <span data-testid="paperclip-icon" />,
  FiEye: () => <span data-testid="eye-icon" />,
  FiRefreshCw: () => <span data-testid="refresh-icon" />,
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(() => Promise.resolve()),
  },
});

const mockEmail = {
  id: 'test-email-1',
  recipient_email: '<EMAIL>',
  recipient_name: 'Test User',
  sender_email: '<EMAIL>',
  sender_name: 'IEPA Team',
  subject: 'Test Email Subject',
  email_type: 'registration_confirmation',
  status: 'sent' as const,
  sent_at: '2024-01-15T10:30:00Z',
  created_at: '2024-01-15T10:25:00Z',
  content_preview: 'This is a test email content preview...',
  has_attachments: true,
  sendgrid_message_id: 'sg_message_123',
};

describe('EmailLogCard', () => {
  it('renders email information correctly', () => {
    render(<EmailLogCard email={mockEmail} />);

    expect(screen.getByText('Test Email Subject')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('IEPA Team')).toBeInTheDocument();
  });

  it('displays correct status badge for sent email', () => {
    render(<EmailLogCard email={mockEmail} />);

    expect(screen.getByText('Sent')).toBeInTheDocument();
    expect(screen.getByTestId('check-icon')).toBeInTheDocument();
  });

  it('displays correct status badge for failed email', () => {
    const failedEmail = { ...mockEmail, status: 'failed' as const };
    render(<EmailLogCard email={failedEmail} />);

    expect(screen.getByText('Failed')).toBeInTheDocument();
    expect(screen.getByTestId('x-icon')).toBeInTheDocument();
  });

  it('displays correct status badge for pending email', () => {
    const pendingEmail = { ...mockEmail, status: 'pending' as const };
    render(<EmailLogCard email={pendingEmail} />);

    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByTestId('clock-icon')).toBeInTheDocument();
  });

  it('shows attachment badge when email has attachments', () => {
    render(<EmailLogCard email={mockEmail} />);

    expect(screen.getByText('Attachment')).toBeInTheDocument();
    expect(screen.getByTestId('paperclip-icon')).toBeInTheDocument();
  });

  it('does not show attachment badge when email has no attachments', () => {
    const emailWithoutAttachments = { ...mockEmail, has_attachments: false };
    render(<EmailLogCard email={emailWithoutAttachments} />);

    expect(screen.queryByText('Attachment')).not.toBeInTheDocument();
  });

  it('displays content preview when available', () => {
    render(<EmailLogCard email={mockEmail} />);

    expect(screen.getByText(/This is a test email content preview/)).toBeInTheDocument();
  });

  it('shows error message for failed emails', () => {
    const failedEmail = {
      ...mockEmail,
      status: 'failed' as const,
      error_message: 'SMTP connection failed',
    };
    render(<EmailLogCard email={failedEmail} />);

    expect(screen.getByText(/SMTP connection failed/)).toBeInTheDocument();
  });

  it('displays SendGrid ID and copy button when available', () => {
    render(<EmailLogCard email={mockEmail} />);

    expect(screen.getByText('SendGrid')).toBeInTheDocument();
    expect(screen.getByTestId('copy-icon')).toBeInTheDocument();
  });

  it('calls onCopySendGridId when copy button is clicked', async () => {
    const mockOnCopy = jest.fn();
    render(<EmailLogCard email={mockEmail} onCopySendGridId={mockOnCopy} />);

    const copyButton = screen.getByTitle(/Copy SendGrid ID/);
    fireEvent.click(copyButton);

    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('sg_message_123');
      expect(mockOnCopy).toHaveBeenCalledWith('sg_message_123');
    });
  });

  it('shows view content button when onViewContent is provided', () => {
    const mockOnViewContent = jest.fn();
    render(<EmailLogCard email={mockEmail} onViewContent={mockOnViewContent} />);

    const viewButton = screen.getByTitle(/View full email content/);
    expect(viewButton).toBeInTheDocument();
    expect(screen.getByTestId('eye-icon')).toBeInTheDocument();
  });

  it('calls onViewContent when view button is clicked', () => {
    const mockOnViewContent = jest.fn();
    render(<EmailLogCard email={mockEmail} onViewContent={mockOnViewContent} />);

    const viewButton = screen.getByTitle(/View full email content/);
    fireEvent.click(viewButton);

    expect(mockOnViewContent).toHaveBeenCalledWith(mockEmail);
  });

  it('shows retry button for failed emails when onRetry is provided', () => {
    const failedEmail = { ...mockEmail, status: 'failed' as const };
    const mockOnRetry = jest.fn();
    render(<EmailLogCard email={failedEmail} onRetry={mockOnRetry} />);

    const retryButton = screen.getByTitle(/Retry sending this email/);
    expect(retryButton).toBeInTheDocument();
    expect(screen.getByTestId('refresh-icon')).toBeInTheDocument();
  });

  it('does not show retry button for sent emails', () => {
    const mockOnRetry = jest.fn();
    render(<EmailLogCard email={mockEmail} onRetry={mockOnRetry} />);

    const retryButton = screen.queryByTitle(/Retry sending this email/);
    expect(retryButton).not.toBeInTheDocument();
  });

  it('calls onRetry when retry button is clicked', () => {
    const failedEmail = { ...mockEmail, status: 'failed' as const };
    const mockOnRetry = jest.fn();
    render(<EmailLogCard email={failedEmail} onRetry={mockOnRetry} />);

    const retryButton = screen.getByTitle(/Retry sending this email/);
    fireEvent.click(retryButton);

    expect(mockOnRetry).toHaveBeenCalledWith(failedEmail);
  });

  it('formats email type correctly', () => {
    render(<EmailLogCard email={mockEmail} />);

    expect(screen.getByText('Registration Confirmation')).toBeInTheDocument();
  });

  it('formats date correctly', () => {
    render(<EmailLogCard email={mockEmail} />);

    // Should show sent_at date when available
    expect(screen.getByText(/Jan 15, 2024/)).toBeInTheDocument();
  });

  it('shows created_at date when sent_at is not available', () => {
    const emailWithoutSentAt = { ...mockEmail, sent_at: undefined };
    render(<EmailLogCard email={emailWithoutSentAt} />);

    expect(screen.getByText(/Jan 15, 2024/)).toBeInTheDocument();
  });

  it('handles email without recipient name', () => {
    const emailWithoutRecipientName = { ...mockEmail, recipient_name: undefined };
    render(<EmailLogCard email={emailWithoutRecipientName} />);

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.queryByText('Test User')).not.toBeInTheDocument();
  });

  it('handles email without sender name', () => {
    const emailWithoutSenderName = { ...mockEmail, sender_name: undefined };
    render(<EmailLogCard email={emailWithoutSenderName} />);

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.queryByText('IEPA Team')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <EmailLogCard email={mockEmail} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('has proper accessibility attributes', () => {
    render(<EmailLogCard email={mockEmail} />);

    expect(screen.getByRole('article')).toBeInTheDocument();
    expect(screen.getByLabelText(/Email: Test Email <NAME_EMAIL>/)).toBeInTheDocument();
  });

  it('shows copying state when copy is in progress', async () => {
    // Mock a delayed clipboard operation
    (navigator.clipboard.writeText as jest.Mock).mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 100))
    );

    const mockOnCopy = jest.fn();
    render(<EmailLogCard email={mockEmail} onCopySendGridId={mockOnCopy} />);

    const copyButton = screen.getByTitle(/Copy SendGrid ID/);
    fireEvent.click(copyButton);

    // Check that the copy icon has animate-pulse class during copying
    const copyIcon = screen.getByTestId('copy-icon');
    expect(copyIcon).toHaveClass('animate-pulse');

    await waitFor(() => {
      expect(mockOnCopy).toHaveBeenCalled();
    });
  });
});
