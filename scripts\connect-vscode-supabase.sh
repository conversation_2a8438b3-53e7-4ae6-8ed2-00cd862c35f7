#!/bin/bash

# IEPA Conference Registration - VS Code Supabase Extension Connection Helper
# This script ensures the VS Code Supabase extension can connect properly

set -e

echo "🔌 Connecting VS Code Supabase Extension..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Ensure we're in the project root
cd "$(dirname "$0")/.."

# Check if we're in the right directory
if [ ! -f "supabase/config.toml" ]; then
    print_error "Not in the correct directory. Please run this from the project root."
    exit 1
fi

print_status "Current directory: $(pwd)"
print_status "Project ID: iepa-conf-reg"

# Check Supabase status
print_status "Checking Supabase status..."
if supabase status > /dev/null 2>&1; then
    print_success "Supabase is running"
    supabase status
else
    print_warning "Supabase is not running. Starting it now..."
    supabase start
fi

echo ""
print_success "🎉 VS Code Supabase Extension should now be able to connect!"
echo ""
echo "📋 Connection Details for VS Code Extension:"
echo "   Project Directory: $(pwd)"
echo "   Config File: $(pwd)/supabase/config.toml"
echo "   Project ID: iepa-conf-reg"
echo ""
echo "💡 In VS Code:"
echo "   1. Open the Supabase extension"
echo "   2. Click 'Connect to Local Project'"
echo "   3. Select this directory: $(pwd)"
echo "   4. The extension should detect the running Supabase instance"
echo ""
echo "🔗 Local URLs:"
echo "   Studio: http://127.0.0.1:54323"
echo "   API: http://127.0.0.1:54321"
echo "   Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres"
