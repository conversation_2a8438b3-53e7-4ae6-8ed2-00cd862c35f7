'use client';

import { useEffect, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardBody, CardHeader, Button } from '@/components/ui';
import { FiLock, FiUser, FiArrowRight } from 'react-icons/fi';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
  variant?: 'default' | 'soft' | 'registration';
}

function AuthGuardInner({
  children,
  fallback,
  redirectTo = '/auth/login',
  variant = 'default',
}: AuthGuardProps) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      if (variant === 'soft') {
        // Don't redirect for soft guards
        return;
      }

      // Prepare redirect URL with return path
      const currentPath = window.location.pathname + window.location.search;
      const redirectUrl = `${redirectTo}?returnTo=${encodeURIComponent(currentPath)}`;

      // Small delay to prevent flash
      const timer = setTimeout(() => {
        router.push(redirectUrl);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [user, loading, router, redirectTo, variant]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // User is authenticated
  if (user) {
    return <>{children}</>;
  }

  // User is not authenticated - show fallback or default UI
  if (fallback) {
    return <>{fallback}</>;
  }

  // Default authentication required UI
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Card>
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <FiLock className="w-6 h-6 text-blue-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Authentication Required
            </h1>
            <p className="text-gray-600">Please sign in to access this page</p>
          </CardHeader>

          <CardBody className="space-y-4">
            <div className="space-y-3">
              <Button
                onClick={() => {
                  const currentPath =
                    window.location.pathname + window.location.search;
                  const magicLinkUrl = `/auth/magic-link?returnTo=${encodeURIComponent(currentPath)}`;
                  router.push(magicLinkUrl);
                }}
                color="secondary"
                className="w-full"
              >
                <FiUser className="w-4 h-4 mr-2" />
                Sign In with Magic Link
                <FiArrowRight className="w-4 h-4 ml-2" />
              </Button>

              <Button
                variant="outline"
                onClick={() => {
                  const currentPath =
                    window.location.pathname + window.location.search;
                  const signupUrl = `/auth/signup?returnTo=${encodeURIComponent(currentPath)}`;
                  router.push(signupUrl);
                }}
                className="w-full"
              >
                Create Account
              </Button>
            </div>

            <div className="pt-4 border-t border-gray-200">
              <div className="text-sm text-iepa-gray-500 space-y-2">
                <p>
                  Don&apos;t have an account? You can create one during the
                  sign-in process.
                </p>
                <p>
                  <button
                    onClick={() => {
                      const currentPath =
                        window.location.pathname + window.location.search;
                      const loginUrl = `/auth/login?returnTo=${encodeURIComponent(currentPath)}`;
                      router.push(loginUrl);
                    }}
                    className="text-blue-600 hover:underline"
                  >
                    Use password instead
                  </button>
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}

export function AuthGuard(props: AuthGuardProps) {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      <AuthGuardInner {...props} />
    </Suspense>
  );
}

// Convenience wrapper for registration pages
export function RegistrationAuthGuard({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthGuard variant="registration" redirectTo="/auth/magic-link">
      {children}
    </AuthGuard>
  );
}
