{
  "extends": ["next/core-web-vitals"],
  "rules": {
    // Downgrade most TypeScript errors to warnings for production builds
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-explicit-any": "off",

    // React Hook rules - keep critical ones as errors
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "off",

    // JSX and React rules - turn off for production
    "react/no-unescaped-entities": "off",
    "@next/next/no-img-element": "off",

    // Import/export rules
    "import/no-anonymous-default-export": "off",

    // TypeScript function type rules
    "@typescript-eslint/no-unsafe-function-type": "off",

    // Allow console statements
    "no-console": "off"
  },
  "overrides": [
    {
      // More lenient rules for API routes and utility files
      "files": ["src/app/api/**/*.ts", "src/lib/**/*.ts", "src/utils/**/*.ts"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-unused-vars": "off"
      }
    }
  ]
}
