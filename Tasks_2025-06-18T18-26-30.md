[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:IEPA 2025 Annual Meeting Website Revisions DESCRIPTION:Complete all revisions and updates for the IEPA 2025 Annual Meeting registration website based on <PERSON>'s requirements from June 17, 2025
--[x] NAME:General Site Updates DESCRIPTION:Implement basic site-wide changes including data cleanup, homepage text updates, and form modifications
---[x] NAME:Delete duplicate registration add-on options DESCRIPTION:Remove duplicate 'Spouse' and 'Child' options from registration add-ons
---[x] NAME:Update homepage attendee count DESCRIPTION:Change homepage text from '300+ Attendees' to '100+'
---[x] NAME:Remove awards banquet references DESCRIPTION:Update all registration forms to reflect that there is no awards banquet
---[x] NAME:Update lodging nights information DESCRIPTION:Clearly state lodging nights: Night One (Monday, Sep 15, 2025) and Night Two (Tuesday, Sep 16, 2025)
---[x] NAME:Remove Conference Highlights items DESCRIPTION:Remove Exhibition, Awards Ceremony, and Resources from Conference Highlights section
--[x] NAME:Schedule and Event Updates DESCRIPTION:Update all schedule information, event descriptions, and timing throughout the site
---[x] NAME:Update Day 1 schedule (Monday, Sep 15) DESCRIPTION:Update schedule: 11:00 AM Golf Tournament, 3:00 PM Registration & Check-in, 6:00 PM Welcome Reception & Dinner
---[x] NAME:Update Day 2 schedule (Tuesday, Sep 16) DESCRIPTION:Update schedule: 7:30 AM Breakfast, 8:30 AM Sessions, 12:00 PM Lunch & Keynote, 1:00 PM Sessions, 6:00 PM Reception & Dinner
---[x] NAME:Update Day 3 schedule (Wednesday, Sep 17) DESCRIPTION:Update schedule: 7:30 AM Breakfast, 8:30 AM Sessions, 11:45 AM Final Thoughts, 12:00 PM Closing Lunch, 1:00 PM Departure
---[x] NAME:Update event descriptions and verbiage DESCRIPTION:Update meal and event descriptions with new verbiage for welcome reception, breakfasts, lunches, and dinners
--[x] NAME:Speaker Registration Enhancements DESCRIPTION:Implement speaker-specific registration improvements including golf options and meal descriptions
---[x] NAME:Update conference lodging description DESCRIPTION:Change description to 'Select which nights you plan to stay at the conference center.'
---[x] NAME:Update golf tournament options DESCRIPTION:Update description to 'Join us for the optional golf tournament. Additional fees apply.' with $200 cost
---[x] NAME:Add golf club rental options DESCRIPTION:Add 'Callaway Rogues Rental golf club needed?' (Yes/No) with $75 fee and required 'Right handed or left handed clubs?' choice
---[x] NAME:Update speaker meal descriptions DESCRIPTION:Update all meal descriptions for speakers: welcome reception, breakfast buffets, lunch buffets, and reception dinners
---[x] NAME:Implement partial speaker registration DESCRIPTION:Research and implement functionality for speakers to submit partial registration first, then follow up with presentation files later
--[x] NAME:Client Questions Resolution DESCRIPTION:Address client questions about document access, manual registration, payment methods, and feature clarifications
---[x] NAME:Document access solution DESCRIPTION:Provide solution for client to review important documents (Agenda, Maps, etc.) - investigate current document access methods
---[x] NAME:Manual registration process DESCRIPTION:Clarify and document how client can manually register attendees like 'Jan, Sara and myself' - check admin capabilities
---[x] NAME:Check payment verification DESCRIPTION:Verify that the system can still accept checks for payment and document the process
---[x] NAME:Clarify 'Pay with Link' feature DESCRIPTION:Investigate and explain what the 'Pay with Link' feature is and how it works
---[x] NAME:Clarify 'Get $5' feature DESCRIPTION:Investigate and explain what the 'Get $5' feature is and how it works
-[/] NAME:remove no binary/prefer not to say options from all registriaotn, only male / female please DESCRIPTION:
-[/] NAME:finish testing speaker reg DESCRIPTION:Speaker Test User:
Email: <EMAIL>
Password: TestPass123!

make sure there are headshots and presentation files uploaded, just take any pdf from our project

go all the way to end to end test with stripe test payment processed and redir back to reg confirmation page and accoutn page
-[/] NAME:ui, make sure all dropdown menus have white backgrounds DESCRIPTION:
-[ ] NAME: DESCRIPTION:
-[/] NAME:Complete Speaker Registration Testing DESCRIPTION:Finish comprehensive testing of Speaker Registration system including recent updates, file uploads, payment flows, and email confirmations
--[x] NAME:Phase 1: Recent Updates Verification DESCRIPTION:Test gender options (Male/Female only), updated meal schedules, golf pricing ($75), and lodging descriptions
--[x] NAME:Phase 2: Core Functionality Testing DESCRIPTION:Test complete registration flows, file uploads, partial registration feature, and form validation
--[x] NAME:Phase 3: Integration Testing DESCRIPTION:Test payment completion, email confirmations, PDF generation, and database verification
--[/] NAME:Do another test but make sure to use the speaker test email address etc DESCRIPTION:Update testing tracker, document results, and provide comprehensive test report
-[x] NAME:Speaker Registration Test with Proper Test Email DESCRIPTION:Complete speaker registration <NAME_EMAIL> as both the authenticated user AND the form email address, ensuring proper user association and registration flow
-[/] NAME:my-registrations - not showing reg data for speaker DESCRIPTION:this users data not showing on the my-registrations page


### 4. Speaker Test User

- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Purpose:** Speaker registration, presentation uploads, speaker-specific features
- **Registration Types:** Comped Speaker ($0), Paid Speaker ($1,500)
- **Status:** ✅ Verified Working
-[x] NAME:Fix My Registrations - Speaker Data Not Showing DESCRIPTION:Investigate and fix the issue where speaker registrations are not appearing in the My Registrations page due to user association problems. The registration data exists in the database but isn't properly linked to the authenticated user.
-[ ] NAME:test all button fucntions on my-reg page DESCRIPTION:
-[/] NAME:add presentation and bio photo add/upload/change button DESCRIPTION:on my-reg
-[x] NAME:Add Presentation and Bio Photo Upload/Change Functionality DESCRIPTION:Implement add/upload/change buttons for presentation files and bio photos in the speaker registration form. This should include proper file handling, upload progress, file replacement, and user feedback.