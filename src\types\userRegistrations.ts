// User Registration Types for IEPA 2025 Conference Registration
// Types specifically for user-facing registration management views

import type { Database } from './database';

// Database table types
export type AttendeeRegistration =
  Database['public']['Tables']['iepa_attendee_registrations']['Row'];
export type SpeakerRegistration =
  Database['public']['Tables']['iepa_speaker_registrations']['Row'];
export type SponsorRegistration =
  Database['public']['Tables']['iepa_sponsor_registrations']['Row'];
export type PaymentRecord =
  Database['public']['Tables']['iepa_payments']['Row'];

// Registration status enum
export enum RegistrationStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
}

// Payment status enum
export enum PaymentStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  CANCELLED = 'cancelled',
}

// User registration summary for dashboard display
export interface UserRegistrationSummary {
  id: string;
  type: 'attendee' | 'speaker' | 'sponsor';
  status: RegistrationStatus;
  paymentStatus: PaymentStatus;
  createdAt: string;
  updatedAt: string;
  amount: number;
  title: string; // Display title for the registration
  description: string; // Brief description
  canEdit: boolean; // Whether user can edit this registration
  hasReceipt: boolean;
  hasInvoice: boolean;
  receiptUrl?: string;
  invoiceUrl?: string;
}

// Detailed registration information
export interface UserRegistrationDetails {
  id: string;
  userId: string;
  type: 'attendee' | 'speaker' | 'sponsor';
  status: RegistrationStatus;
  paymentStatus: PaymentStatus;
  createdAt: string;
  updatedAt: string;

  // Personal information
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    organization?: string;
    title?: string;
    badgeName?: string;
    gender?: string;
  };

  // Registration-specific details
  registrationDetails: {
    registrationType?: string;
    membershipType?: string;
    specialRequests?: string;
    dietaryRestrictions?: string;
    accessibilityNeeds?: string;
  };

  // Event participation
  eventParticipation: {
    attendingGolf: boolean;
    golfClubRental?: boolean;
    golfClubHandedness?: string;
    mealSelections: Record<string, boolean>;
  };

  // Financial information
  financial: {
    registrationFee: number;
    golfFee?: number;
    golfClubRentalFee?: number;
    mealTotal?: number;
    subtotal: number;
    tax?: number;
    grandTotal: number;
  };

  // Speaker-specific (if applicable)
  speakerInfo?: {
    bio: string;
    presentationTitle: string;
    presentationDescription: string;
    presentationFileUrl?: string;
    headshotUrl?: string;
  };

  // Sponsor-specific (if applicable)
  sponsorInfo?: {
    organizationName: string;
    sponsorshipLevel: string;
    packageDetails: Record<string, unknown>;
    logoUrl?: string;
    websiteUrl?: string;
    description?: string;
  };

  // Document URLs
  documents: {
    receiptUrl?: string;
    invoiceUrl?: string;
    receiptGeneratedAt?: string;
    invoiceGeneratedAt?: string;
  };
}

// User payment record
export interface UserPaymentRecord {
  id: string;
  registrationId: string;
  registrationType: 'attendee' | 'speaker' | 'sponsor';
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string;
  stripePaymentIntentId?: string;
  stripeSessionId?: string;
  transactionId?: string;
  createdAt: string;
  updatedAt: string;
  description: string;
  metadata?: Record<string, unknown>;
}

// Combined user registrations response
export interface UserRegistrationsResponse {
  attendee?: UserRegistrationDetails;
  speaker?: UserRegistrationDetails;
  sponsor?: UserRegistrationDetails;
  payments: UserPaymentRecord[];
  summary: UserRegistrationSummary[];
}

// API response types
export interface UserRegistrationsApiResponse {
  success: boolean;
  data?: UserRegistrationsResponse;
  error?: string;
  message?: string;
}

// Hook state types
export interface UserRegistrationsState {
  registrations: UserRegistrationsResponse | null;
  loading: boolean;
  error: string | null;
  lastFetched: Date | null;
}

// Update registration request
export interface UpdateRegistrationRequest {
  registrationId: string;
  registrationType: 'attendee' | 'speaker' | 'sponsor';
  updates: {
    dietaryRestrictions?: string;
    accessibilityNeeds?: string;
    specialRequests?: string;
    mealSelections?: Record<string, boolean>;
    // Add other editable fields as needed
  };
}

// Registration update response
export interface UpdateRegistrationResponse {
  success: boolean;
  data?: UserRegistrationDetails;
  error?: string;
  message?: string;
}

// Utility types for filtering and sorting
export interface RegistrationFilters {
  type?: ('attendee' | 'speaker' | 'sponsor')[];
  status?: RegistrationStatus[];
  paymentStatus?: PaymentStatus[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface RegistrationSortOptions {
  field: 'createdAt' | 'updatedAt' | 'amount' | 'status';
  direction: 'asc' | 'desc';
}

// Error types
export interface UserRegistrationError {
  code: string;
  message: string;
  details?: unknown;
  field?: string;
}

// Constants
export const REGISTRATION_TYPES = ['attendee', 'speaker', 'sponsor'] as const;
export const EDITABLE_FIELDS = [
  'dietaryRestrictions',
  'accessibilityNeeds',
  'specialRequests',
  'mealSelections',
] as const;

// Type guards
export function isAttendeeRegistration(
  registration: UserRegistrationDetails
): registration is UserRegistrationDetails & { type: 'attendee' } {
  return registration.type === 'attendee';
}

export function isSpeakerRegistration(
  registration: UserRegistrationDetails
): registration is UserRegistrationDetails & { type: 'speaker' } {
  return registration.type === 'speaker';
}

export function isSponsorRegistration(
  registration: UserRegistrationDetails
): registration is UserRegistrationDetails & { type: 'sponsor' } {
  return registration.type === 'sponsor';
}

// Utility functions
export function getRegistrationDisplayTitle(
  registration: UserRegistrationDetails
): string {
  switch (registration.type) {
    case 'attendee':
      return `${registration.registrationDetails.registrationType || 'Attendee'} Registration`;
    case 'speaker':
      return 'Speaker Registration';
    case 'sponsor':
      return `${registration.sponsorInfo?.sponsorshipLevel || 'Sponsor'} Sponsorship`;
    default:
      return 'Registration';
  }
}

export function getRegistrationDisplayDescription(
  registration: UserRegistrationDetails
): string {
  switch (registration.type) {
    case 'attendee':
      return `Conference attendance with ${registration.eventParticipation.attendingGolf ? 'golf participation' : 'no golf'}`;
    case 'speaker':
      return (
        registration.speakerInfo?.presentationTitle || 'Speaker presentation'
      );
    case 'sponsor':
      return (
        registration.sponsorInfo?.organizationName || 'Conference sponsorship'
      );
    default:
      return 'IEPA Conference registration';
  }
}

export function canEditRegistration(
  registration: UserRegistrationDetails
): boolean {
  // Users can edit certain fields if registration is not cancelled
  return registration.status !== RegistrationStatus.CANCELLED;
}

export function hasDocuments(registration: UserRegistrationDetails): {
  hasReceipt: boolean;
  hasInvoice: boolean;
} {
  return {
    hasReceipt: !!registration.documents.receiptUrl,
    hasInvoice: !!registration.documents.invoiceUrl,
  };
}
