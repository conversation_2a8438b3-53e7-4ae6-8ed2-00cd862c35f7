#!/usr/bin/env node

/**
 * Test script to verify Supabase authentication redirect URLs
 * Run this to check if your Supabase project is configured correctly
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const appUrl = process.env.NEXT_PUBLIC_APP_URL;

console.log('🔧 Testing Supabase Authentication Configuration\n');

// Check environment variables
console.log('📋 Environment Variables:');
console.log(
  `   NEXT_PUBLIC_SUPABASE_URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`
);
console.log(
  `   NEXT_PUBLIC_SUPABASE_ANON_KEY: ${supabaseKey ? '✅ Set' : '❌ Missing'}`
);
console.log(`   NEXT_PUBLIC_APP_URL: ${appUrl || '❌ Missing'}\n`);

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function testAuthConfiguration() {
  try {
    console.log('🧪 Testing Authentication Configuration...\n');

    // Test 1: Check if we can connect to Supabase
    console.log('1️⃣ Testing Supabase Connection...');
    const { error } = await supabase.auth.getSession();

    if (error) {
      console.log(`   ❌ Connection Error: ${error.message}`);
    } else {
      console.log('   ✅ Successfully connected to Supabase');
    }

    // Test 2: Check current site URL configuration
    console.log('\n2️⃣ Checking Site URL Configuration...');
    console.log(`   Current App URL: ${appUrl}`);

    if (appUrl?.includes('localhost')) {
      console.log('   ⚠️  WARNING: App URL is set to localhost');
      console.log('   📝 For production, this should be: https://reg.iepa.com');
    } else if (appUrl?.includes('reg.iepa.com')) {
      console.log('   ✅ App URL is correctly set to production domain');
    } else {
      console.log('   ❓ App URL is set to an unexpected value');
    }

    // Test 3: Test password reset URL generation
    console.log('\n3️⃣ Testing Password Reset URL Generation...');

    // This won't actually send an email, but will show us what URL would be used
    const testEmail = '<EMAIL>';
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(
      testEmail,
      {
        redirectTo: `${appUrl}/auth/reset-password`,
      }
    );

    if (resetError) {
      console.log(`   ❌ Reset Password Error: ${resetError.message}`);
    } else {
      console.log('   ✅ Password reset request would be successful');
      console.log(
        `   📧 Reset URL would redirect to: ${appUrl}/auth/reset-password`
      );
    }

    // Test 4: Recommendations
    console.log('\n📋 Recommendations:');

    if (appUrl?.includes('localhost')) {
      console.log('   🔧 Update Supabase Dashboard Settings:');
      console.log('      - Site URL: https://reg.iepa.com');
      console.log('      - Redirect URLs: https://reg.iepa.com/**');
      console.log('   🔧 Update Production Environment Variables:');
      console.log('      - NEXT_PUBLIC_APP_URL=https://reg.iepa.com');
    } else {
      console.log('   ✅ Configuration looks good for production!');
    }

    console.log('\n🎯 Next Steps:');
    console.log('   1. Update Supabase Dashboard authentication settings');
    console.log('   2. Deploy with correct environment variables');
    console.log('   3. Test password reset flow on production site');
    console.log('   4. Verify email links redirect to correct domain');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAuthConfiguration();
