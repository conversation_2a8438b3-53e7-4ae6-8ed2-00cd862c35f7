'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardBody, CardHeader } from '@heroui/react';
import { CheckCircle, FileText, Mail, Calendar, User, Building } from 'lucide-react';
import { CONFERENCE_YEAR } from '@/lib/conference-config';

interface ConfirmationData {
  speakerId: string;
  attendeeId: string;
  speakerType: string;
  grandTotal: number;
  isPartial: boolean;
  email: string;
  name: string;
}

function SpeakerConfirmationContent() {
  const searchParams = useSearchParams();
  const [confirmationData, setConfirmationData] = useState<ConfirmationData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const dataParam = searchParams.get('data');
    if (dataParam) {
      try {
        const data = JSON.parse(decodeURIComponent(dataParam));
        setConfirmationData(data);
      } catch (error) {
        console.error('Error parsing confirmation data:', error);
      }
    }
    setLoading(false);
  }, [searchParams]);

  if (loading) {
    return (
      <div className="iepa-container">
        <div className="max-w-4xl mx-auto text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="iepa-body">Loading confirmation details...</p>
        </div>
      </div>
    );
  }

  if (!confirmationData) {
    return (
      <div className="iepa-container">
        <div className="max-w-4xl mx-auto text-center py-12">
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">
              Unable to load confirmation details. Please contact support if you continue to experience issues.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const isCompedSpeaker = confirmationData.speakerType === 'comped-speaker';
  const isFullMeetingSpeaker = confirmationData.speakerType === 'full-meeting-speaker';

  return (
    <div className="iepa-container">
      {/* Header */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex justify-center mb-6">
            <CheckCircle className="h-16 w-16 text-green-600" />
          </div>
          <h1 className="iepa-heading-1 mb-4 text-green-700">
            Speaker Registration Confirmed!
          </h1>
          <p className="iepa-body mb-6">
            Thank you for registering as a speaker for the IEPA {CONFERENCE_YEAR} Annual Meeting.
            Your registration has been successfully submitted and processed.
          </p>
        </div>
      </section>

      {/* Registration Summary */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 flex items-center gap-2">
                <User className="h-5 w-5" />
                Registration Summary
              </h2>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Speaker Details</h3>
                  <div className="space-y-2 text-sm">
                    <p><strong>Name:</strong> {confirmationData.name}</p>
                    <p><strong>Email:</strong> {confirmationData.email}</p>
                    <p><strong>Speaker ID:</strong> {confirmationData.speakerId}</p>
                    <p><strong>Attendee ID:</strong> {confirmationData.attendeeId}</p>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Registration Type</h3>
                  <div className="space-y-2 text-sm">
                    <p>
                      <strong>Type:</strong>{' '}
                      {isCompedSpeaker && 'Complimentary Speaker'}
                      {isFullMeetingSpeaker && 'Full Meeting Speaker'}
                    </p>
                    <p><strong>Total Cost:</strong> ${confirmationData.grandTotal.toFixed(2)}</p>
                    {confirmationData.isPartial && (
                      <p className="text-yellow-700">
                        <strong>Status:</strong> Partial Registration (Presentation file pending)
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>

      {/* Speaker Benefits */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 flex items-center gap-2">
                <Building className="h-5 w-5" />
                Your Speaker Benefits
              </h2>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {isCompedSpeaker && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-semibold text-blue-900 mb-2">Complimentary Speaker Package</h3>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Free conference registration</li>
                      <li>• One night lodging included</li>
                      <li>• All meals included</li>
                      <li>• Speaker dinner invitation</li>
                      <li>• Professional bio in materials</li>
                    </ul>
                  </div>
                )}
                
                {isFullMeetingSpeaker && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 className="font-semibold text-green-900 mb-2">Full Meeting Speaker Package</h3>
                    <ul className="text-sm text-green-800 space-y-1">
                      <li>• Full conference registration</li>
                      <li>• Two nights lodging included</li>
                      <li>• All meals and events included</li>
                      <li>• Speaker dinner invitation</li>
                      <li>• Professional bio in materials</li>
                      <li>• Priority networking opportunities</li>
                    </ul>
                  </div>
                )}
                
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <h3 className="font-semibold text-gray-900 mb-2">Additional Benefits</h3>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• Professional recognition</li>
                    <li>• Networking opportunities</li>
                    <li>• Presentation recording (optional)</li>
                    <li>• Conference materials access</li>
                    <li>• Certificate of participation</li>
                  </ul>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>

      {/* Next Steps */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Next Steps
              </h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Mail className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Confirmation Email</h3>
                    <p className="text-sm text-gray-600">
                      You will receive a detailed confirmation email at {confirmationData.email} within the next few minutes.
                      This email will contain your registration details and important conference information.
                    </p>
                  </div>
                </div>

                {confirmationData.isPartial && (
                  <div className="flex items-start gap-3">
                    <FileText className="h-5 w-5 text-yellow-600 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold text-gray-900">Upload Presentation File</h3>
                      <p className="text-sm text-gray-600">
                        You selected partial registration. Please remember to return and upload your presentation file
                        before the conference deadline. You can access your registration to complete this step.
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-start gap-3">
                  <Calendar className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Conference Preparation</h3>
                    <p className="text-sm text-gray-600">
                      Additional details about the conference schedule, venue information, and speaker guidelines
                      will be sent to you closer to the event date.
                    </p>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>

      {/* Contact Information */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto text-center">
          <div className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Questions or Need Help?</h3>
            <p className="text-sm text-gray-600 mb-4">
              If you have any questions about your registration or need to make changes,
              please don't hesitate to contact us.
            </p>
            <div className="space-y-2 text-sm">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Phone:</strong> (*************</p>
            </div>
          </div>
        </div>
      </section>

      {/* Action Buttons */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto text-center">
          <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <a
              href="/about"
              className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              View Conference Information
            </a>
            <a
              href="/"
              className="inline-block px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Return to Homepage
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}

export default function SpeakerConfirmationPage() {
  return (
    <Suspense fallback={
      <div className="iepa-container">
        <div className="max-w-4xl mx-auto text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="iepa-body">Loading confirmation details...</p>
        </div>
      </div>
    }>
      <SpeakerConfirmationContent />
    </Suspense>
  );
}
