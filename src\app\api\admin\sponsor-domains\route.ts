// Admin API for managing sponsor domains and automatic discounts

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';
import { createSponsorDomain, autoCreateSponsorDomain } from '@/lib/sponsor-attendee-utils';

// GET - List all sponsor domains
export async function GET(request: NextRequest) {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    const { data: sponsorDomains, error } = await supabaseAdmin
      .from('iepa_sponsor_domains')
      .select(`
        *,
        sponsor:iepa_sponsor_registrations!sponsor_id(
          id,
          sponsor_name,
          sponsor_url,
          payment_status
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch sponsor domains: ${error.message}`);
    }

    return NextResponse.json({
      success: true,
      data: sponsorDomains,
    });
  } catch (error) {
    console.error('Error fetching sponsor domains:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch sponsor domains',
      },
      { status: 500 }
    );
  }
}

// POST - Create new sponsor domain
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      sponsorId,
      domain,
      sponsorName,
      discountValue = 100,
      discountType = 'percentage',
      maxUses,
      autoCreate = false,
    } = body;

    // Validate required fields
    if (!sponsorId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Sponsor ID is required',
        },
        { status: 400 }
      );
    }

    let result;

    if (autoCreate) {
      // Auto-create domain from sponsor registration
      result = await autoCreateSponsorDomain(sponsorId);
    } else {
      // Manual domain creation
      if (!domain || !sponsorName) {
        return NextResponse.json(
          {
            success: false,
            error: 'Domain and sponsor name are required for manual creation',
          },
          { status: 400 }
        );
      }

      result = await createSponsorDomain(
        sponsorId,
        domain,
        sponsorName,
        discountValue,
        discountType,
        maxUses
      );
    }

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error,
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        sponsorDomainId: result.sponsorDomainId,
        discountCode: result.discountCode,
      },
      message: 'Sponsor domain created successfully',
    });
  } catch (error) {
    console.error('Error creating sponsor domain:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create sponsor domain',
      },
      { status: 500 }
    );
  }
}

// PUT - Update sponsor domain
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      id,
      domain,
      discountValue,
      discountType,
      maxUses,
      isActive,
    } = body;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Sponsor domain ID is required',
        },
        { status: 400 }
      );
    }

    const supabaseAdmin = createSupabaseAdmin();

    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (domain !== undefined) updateData.domain = domain.toLowerCase();
    if (discountValue !== undefined) updateData.discount_value = discountValue;
    if (discountType !== undefined) updateData.discount_type = discountType;
    if (maxUses !== undefined) updateData.max_uses = maxUses;
    if (isActive !== undefined) updateData.is_active = isActive;

    const { data, error } = await supabaseAdmin
      .from('iepa_sponsor_domains')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update sponsor domain: ${error.message}`);
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Sponsor domain updated successfully',
    });
  } catch (error) {
    console.error('Error updating sponsor domain:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update sponsor domain',
      },
      { status: 500 }
    );
  }
}

// DELETE - Delete sponsor domain
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Sponsor domain ID is required',
        },
        { status: 400 }
      );
    }

    const supabaseAdmin = createSupabaseAdmin();

    const { error } = await supabaseAdmin
      .from('iepa_sponsor_domains')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete sponsor domain: ${error.message}`);
    }

    return NextResponse.json({
      success: true,
      message: 'Sponsor domain deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting sponsor domain:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete sponsor domain',
      },
      { status: 500 }
    );
  }
}
