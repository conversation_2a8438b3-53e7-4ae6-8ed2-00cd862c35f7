import { NextRequest, NextResponse } from 'next/server';
import { EmailService } from '@/services/email';

/**
 * Admin API endpoint to send receipts to registered attendees
 * POST /api/admin/send-receipt
 */

export async function POST(request: NextRequest) {
  try {
    console.log('[ADMIN-RECEIPT] Processing receipt send request...');

    // Parse request body
    const body = await request.json();
    const { registrationId, registrationType, email, fullName } = body;

    // Validate required fields
    if (!registrationId) {
      return NextResponse.json(
        { success: false, error: 'Registration ID is required' },
        { status: 400 }
      );
    }

    if (!registrationType) {
      return NextResponse.json(
        { success: false, error: 'Registration type is required' },
        { status: 400 }
      );
    }

    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email address is required' },
        { status: 400 }
      );
    }

    if (!fullName) {
      return NextResponse.json(
        { success: false, error: 'Full name is required' },
        { status: 400 }
      );
    }

    // Validate registration type
    if (!['attendee', 'speaker', 'sponsor'].includes(registrationType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid registration type' },
        { status: 400 }
      );
    }

    console.log('[ADMIN-RECEIPT] Sending receipt:', {
      registrationId,
      registrationType,
      email,
      fullName,
    });

    // Initialize email service
    const emailService = new EmailService();

    // Send welcome email with receipt attachment
    // This will automatically generate a receipt for completed payments
    await emailService.sendWelcomeEmail(
      email,
      fullName,
      registrationId,
      registrationType as 'attendee' | 'speaker' | 'sponsor'
    );

    console.log('[ADMIN-RECEIPT] Receipt sent successfully');

    return NextResponse.json({
      success: true,
      message: 'Receipt sent successfully',
      details: {
        registrationId,
        registrationType,
        email,
        fullName,
      },
    });
  } catch (error) {
    console.error('[ADMIN-RECEIPT] Error sending receipt:', error);

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to send receipt',
      },
      { status: 500 }
    );
  }
}

/**
 * Handle GET requests - return method not allowed
 */
export async function GET() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
