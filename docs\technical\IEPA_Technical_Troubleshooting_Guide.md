# IEPA Conference Registration System - Technical Troubleshooting Guide

## Quick Reference

**System URL**: https://reg.iepa.com  
**Admin Panel**: https://reg.iepa.com/admin  
**Database**: Supabase (PostgreSQL)  
**Payment Processor**: Stripe  
**Email Service**: SendGrid  
**Hosting**: Vercel  

---

## Common Issues and Solutions

### Authentication Problems

#### Magic Link Not Working
**Symptoms**: Users report not receiving magic link emails
**Diagnosis Steps**:
1. Check SendGrid delivery logs in admin panel
2. Verify email address spelling and format
3. Check spam/junk folders
4. Confirm SendGrid API key is active

**Solutions**:
- Resend magic link from admin panel
- Use password reset as alternative
- Check email domain reputation
- Verify SendGrid account status

#### Password Reset Failures
**Symptoms**: Password reset emails not delivered
**Diagnosis**:
```sql
-- Check recent password reset attempts
SELECT * FROM auth.users 
WHERE email = '<EMAIL>' 
ORDER BY updated_at DESC LIMIT 5;
```

**Solutions**:
- Manually reset password in Supabase Auth dashboard
- Check email template configuration
- Verify user account exists and is active

### Registration Form Issues

#### Form Data Not Saving
**Symptoms**: Users lose form progress, auto-save not working
**Diagnosis**:
- Check browser localStorage capacity
- Verify JavaScript errors in console
- Test form persistence functionality

**Solutions**:
```javascript
// Clear localStorage for user
localStorage.removeItem('iepa-attendee-form-data');
localStorage.removeItem('iepa-speaker-form-data');
localStorage.removeItem('iepa-sponsor-form-data');
```

#### Validation Errors
**Symptoms**: Form submission fails with validation errors
**Common Fixes**:
- Phone number format: Ensure (XXX) XXX-XXXX format
- Email validation: Check for valid email format
- Required fields: Verify all required fields completed
- File uploads: Check file size and format restrictions

### Payment Processing Issues

#### Stripe Checkout Failures
**Symptoms**: Users can't complete payment, checkout session errors
**Diagnosis Steps**:
1. Check Stripe dashboard for failed payments
2. Review webhook delivery status
3. Verify API keys are correct and active

**Database Queries**:
```sql
-- Check payment status
SELECT p.*, ar.email, ar.full_name 
FROM iepa_payments p
JOIN iepa_attendee_registrations ar ON p.registration_id = ar.id
WHERE p.status = 'failed'
ORDER BY p.created_at DESC;

-- Check webhook processing
SELECT * FROM iepa_email_logs 
WHERE email_type = 'payment_confirmation'
AND status = 'failed'
ORDER BY created_at DESC;
```

#### Invoice Generation Problems
**Symptoms**: PDF invoices not generating or downloading
**Solutions**:
- Check Stripe invoice API status
- Verify PDF generation service
- Manually regenerate invoice from admin panel
- Check file storage permissions

### Database Issues

#### Connection Problems
**Symptoms**: Database timeouts, connection errors
**Diagnosis**:
```sql
-- Check active connections
SELECT count(*) FROM pg_stat_activity;

-- Check long-running queries
SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';
```

#### Data Integrity Issues
**Symptoms**: Orphaned records, missing relationships
**Cleanup Queries**:
```sql
-- Find attendee registrations without user profiles
SELECT ar.* FROM iepa_attendee_registrations ar
LEFT JOIN iepa_user_profiles up ON ar.user_id = up.user_id
WHERE up.user_id IS NULL;

-- Find payments without corresponding registrations
SELECT p.* FROM iepa_payments p
LEFT JOIN iepa_attendee_registrations ar ON p.registration_id = ar.id
WHERE ar.id IS NULL AND p.registration_type = 'attendee';
```

### Email Delivery Issues

#### SendGrid Configuration
**Check Email Settings**:
```sql
-- Verify email configuration
SELECT * FROM iepa_email_config 
WHERE is_active = true;

-- Check recent email logs
SELECT * FROM iepa_email_logs 
WHERE status = 'failed'
ORDER BY created_at DESC LIMIT 20;
```

**Common Solutions**:
- Verify SendGrid API key
- Check sender domain authentication
- Review email template syntax
- Confirm recipient email validity

#### Template Problems
**Symptoms**: Emails sending with incorrect content or formatting
**Diagnosis**:
- Check template variables and syntax
- Verify template selection logic
- Test email preview functionality

---

## Admin Panel Troubleshooting

### Access Issues
**Symptoms**: Admin users can't access dashboard
**Verification Steps**:
```sql
-- Check admin user status
SELECT * FROM iepa_admin_users 
WHERE email = '<EMAIL>';

-- Verify admin permissions
SELECT au.*, u.email FROM iepa_admin_users au
JOIN auth.users u ON au.email = u.email
WHERE au.is_active = true;
```

### Dashboard Performance
**Symptoms**: Slow loading admin dashboard
**Optimization**:
- Check database query performance
- Review dashboard data aggregation
- Implement query caching where appropriate

---

## System Monitoring

### Performance Monitoring
**Key Metrics to Track**:
- Page load times (target: <2 seconds)
- API response times (target: <200ms)
- Database query performance
- Error rates and types

**Monitoring Queries**:
```sql
-- Registration volume by day
SELECT DATE(created_at) as date, COUNT(*) as registrations
FROM iepa_attendee_registrations
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date;

-- Payment success rate
SELECT 
  COUNT(*) as total_attempts,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful,
  ROUND(COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM iepa_payments
WHERE created_at >= NOW() - INTERVAL '7 days';
```

### Error Tracking
**Common Error Patterns**:
- Authentication timeouts
- Payment processing failures
- Email delivery failures
- Form validation errors

---

## Backup and Recovery

### Database Backup
**Supabase Automatic Backups**:
- Daily automated backups
- Point-in-time recovery available
- Manual backup triggers available

**Manual Backup Commands**:
```bash
# Export specific tables
pg_dump --host=db.xxx.supabase.co --port=5432 --username=postgres \
  --table=iepa_attendee_registrations \
  --table=iepa_payments \
  --table=iepa_user_profiles \
  database_name > backup.sql
```

### Data Recovery Procedures
1. **Identify Issue**: Determine scope of data loss
2. **Stop Write Operations**: Prevent further data corruption
3. **Restore from Backup**: Use most recent clean backup
4. **Verify Integrity**: Check data consistency
5. **Resume Operations**: Gradually restore full functionality

---

## Security Incident Response

### Suspected Security Breach
**Immediate Actions**:
1. Change all API keys and passwords
2. Review access logs for suspicious activity
3. Check for unauthorized admin access
4. Verify payment processing integrity

**Investigation Queries**:
```sql
-- Check recent admin logins
SELECT * FROM auth.audit_log_entries 
WHERE event_type = 'signed_in'
AND created_at >= NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;

-- Review recent data modifications
SELECT table_name, operation, old_record, new_record, created_at
FROM audit.logged_actions
WHERE created_at >= NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;
```

### Data Privacy Compliance
**GDPR Data Requests**:
- User data export procedures
- Account deletion workflows
- Data retention policy enforcement
- Consent management verification

---

## Maintenance Procedures

### Regular Maintenance Tasks
**Daily**:
- Monitor error logs
- Check payment processing status
- Verify email delivery rates

**Weekly**:
- Review system performance metrics
- Update security patches
- Backup verification

**Monthly**:
- Database optimization
- Security audit
- Performance review

### System Updates
**Deployment Checklist**:
1. Test in staging environment
2. Backup current production data
3. Deploy during low-traffic hours
4. Monitor for errors post-deployment
5. Verify all functionality working

---

## Emergency Contacts

**Technical Support**:
- Primary Developer: [Contact Information]
- Database Administrator: [Contact Information]
- DevOps Engineer: [Contact Information]

**Third-Party Support**:
- Supabase Support: <EMAIL>
- Stripe Support: <EMAIL>
- SendGrid Support: <EMAIL>
- Vercel Support: <EMAIL>

**Escalation Procedures**:
1. Level 1: Technical team member
2. Level 2: Senior developer or team lead
3. Level 3: External vendor support
4. Level 4: Emergency contractor support

---

**Document Information**
**Document Type**: Technical Troubleshooting Guide
**Last Updated**: December 2024
**Document Version**: 1.0
**System Version**: v0.1.0
**Next Review**: March 2025
**Prepared By**: Technical Team
