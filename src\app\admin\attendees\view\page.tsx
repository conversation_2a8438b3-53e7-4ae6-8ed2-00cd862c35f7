'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AttendeeRegistration, SpeakerRegistration } from '@/types/database';
import { Button, Card, CardHeader, CardTitle, CardBody } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import { formatCurrency, formatDate } from '@/lib/pdf-generation/utils';
import {
  FiArrowLeft,
  FiEdit,
  FiMail,
  FiPhone,
  FiMapPin,
  FiBriefcase,
  FiCreditCard,
  FiCalendar,
  FiTarget,
  FiCoffee,
  FiMic,
  FiSend,
} from 'react-icons/fi';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { showSuccess, showError } from '@/utils/notifications';

// Import centralized meal utilities
import { getMealDisplayNames } from '@/lib/meal-utils';

// Complete list of all meals in chronological order
const ALL_MEALS_IN_ORDER = [
  'day1-reception',
  'day2-breakfast',
  'day2-lunch',
  'day2-dinner',
  'day3-breakfast',
  'day3-lunch'
];

// Use AttendeeRegistration directly since all needed fields are already defined
type ExtendedAttendeeRegistration = AttendeeRegistration;

export default function ViewAttendeePage() {
  console.log('🔍 ViewAttendeePage component mounting...');

  const router = useRouter();
  const searchParams = useSearchParams();
  const attendeeId = searchParams?.get('id');
  const { user } = useAuth();

  console.log('🔍 ViewAttendeePage params:', {
    attendeeId,
    userEmail: user?.email,
  });

  // Check for test mode bypass
  const isTestMode =
    process.env.NODE_ENV === 'development' &&
    searchParams?.get('testAdmin') === 'true';

  const { isAdmin, isLoading: adminLoading } = useAdminAccess();

  // Special <NAME_EMAIL>
  const isEnoteware = user?.email === '<EMAIL>';

  console.log('🔍 ViewAttendeePage admin access:', {
    isAdmin,
    adminLoading,
    isTestMode,
    isEnoteware,
  });

  const [attendee, setAttendee] = useState<ExtendedAttendeeRegistration | null>(
    null
  );
  const [speakerData, setSpeakerData] = useState<SpeakerRegistration | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mealDisplayNames, setMealDisplayNames] = useState<Record<string, string>>({});
  const [resendingEmail, setResendingEmail] = useState(false);

  // Load meal display names from database
  useEffect(() => {
    const loadMealDisplayNames = async () => {
      try {
        const displayNames = await getMealDisplayNames(ALL_MEALS_IN_ORDER);
        setMealDisplayNames(displayNames);
      } catch (error) {
        console.error('Error loading meal display names:', error);
      }
    };

    loadMealDisplayNames();
  }, []);

  useEffect(() => {
    console.log('🔍 View page access check:', {
      adminLoading,
      isAdmin,
      isTestMode,
      attendeeId,
      userEmail: 'checking...',
    });

    // Allow <NAME_EMAIL>, admin users, or test mode
    const hasAccess = isAdmin || isTestMode || isEnoteware;

    // Only redirect if we're sure the user is not an admin (loading is complete) and not enoteware
    if (!adminLoading && !hasAccess && !isTestMode && !isEnoteware) {
      console.log('🚫 Access denied - redirecting to admin dashboard');
      router.push('/admin');
      return;
    }

    // Only proceed if we have admin access, are in test mode, or are enoteware
    if ((hasAccess || isTestMode || isEnoteware) && attendeeId) {
      console.log('✅ Access granted - fetching attendee data');
      fetchAttendee();
    } else if (!attendeeId && !adminLoading) {
      console.log('❌ No attendee ID - redirecting to attendees list');
      router.push('/admin/attendees');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [attendeeId, isAdmin, adminLoading, isTestMode, isEnoteware, router]);

  const fetchAttendee = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('iepa_attendee_registrations')
        .select('*')
        .eq('id', attendeeId)
        .single();

      if (error) throw error;

      setAttendee(data);

      // Fetch related speaker data by email
      if (data?.email) {
        try {
          const { data: speakerData, error: speakerError } = await supabase
            .from('iepa_speaker_registrations')
            .select('*')
            .eq('email', data.email)
            .single();

          if (!speakerError && speakerData) {
            setSpeakerData(speakerData);
          }
        } catch {
          // No matching speaker found, which is fine
          console.log('No matching speaker found for attendee:', data.email);
        }
      }
    } catch (err) {
      console.error('Error fetching attendee:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch attendee');
    } finally {
      setLoading(false);
    }
  }, [attendeeId]);

  const handleBack = () => {
    router.push(`/admin/attendees${isTestMode ? '?testAdmin=true' : ''}`);
  };

  const handleResendWelcomeEmail = async () => {
    if (!attendee) return;

    setResendingEmail(true);
    try {
      const response = await fetch('/api/admin/resend-welcome-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          attendeeId: attendee.id,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        showSuccess('Email Sent', 'Welcome email sent successfully!');
      } else {
        showError('Email Failed', result.error || 'Failed to send welcome email');
      }
    } catch (error) {
      console.error('Error resending welcome email:', error);
      showError('Email Failed', 'Failed to send welcome email');
    } finally {
      setResendingEmail(false);
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatMealsAsLinks = (attendeeMeals: string[]) => {
    const attendeeMealSet = new Set(attendeeMeals || []);

    return (
      <div className="flex flex-wrap gap-2">
        {ALL_MEALS_IN_ORDER.map((mealKey, index) => {
          const isAttending = attendeeMealSet.has(mealKey);
          const displayName = mealDisplayNames[mealKey] || mealKey;

          return (
            <span key={mealKey}>
              {isAttending ? (
                <Link
                  href={`/admin/attendees?meal=${encodeURIComponent(mealKey)}${isTestMode ? '&testAdmin=true' : ''}`}
                  className="text-[var(--iepa-primary-blue)] hover:underline hover:text-blue-700 transition-colors"
                  title={`View all attendees registered for ${displayName}`}
                >
                  {displayName}
                </Link>
              ) : (
                <span
                  className="text-gray-400"
                  title={`Not attending ${displayName}`}
                >
                  {displayName}
                </span>
              )}
              {index < ALL_MEALS_IN_ORDER.length - 1 && (
                <span className="text-gray-400 ml-1">,</span>
              )}
            </span>
          );
        })}
      </div>
    );
  };

  if ((adminLoading && !isTestMode) || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--iepa-primary-blue)] mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading attendee...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={handleBack}>
          <FiArrowLeft className="w-4 h-4 mr-2" />
          Back to Attendees
        </Button>
      </div>
    );
  }

  if (!attendee) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Attendee not found</p>
        <Button onClick={handleBack} className="mt-4">
          <FiArrowLeft className="w-4 h-4 mr-2" />
          Back to Attendees
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back to Attendees
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Attendee Details
            </h1>
            <p className="text-gray-600">{attendee.full_name}</p>
          </div>
        </div>
        <div className="flex gap-2">
          {speakerData && (
            <Link
              href={`/admin/speakers/view?id=${speakerData.id}${isTestMode ? '&testAdmin=true' : ''}`}
            >
              <Button variant="outline">
                <FiMic className="w-4 h-4 mr-2" />
                View Speaker Profile
              </Button>
            </Link>
          )}
          <Button
            variant="outline"
            onClick={handleResendWelcomeEmail}
            disabled={resendingEmail || attendee.payment_status === 'draft'}
            title={
              attendee.payment_status === 'draft'
                ? 'Cannot send welcome email for draft registrations'
                : 'Resend welcome email to attendee'
            }
          >
            <FiSend className="w-4 h-4 mr-2" />
            {resendingEmail ? 'Sending...' : 'Resend Welcome Email'}
          </Button>
          <Link href={`/admin/attendees/edit?id=${attendee.id}`}>
            <Button>
              <FiEdit className="w-4 h-4 mr-2" />
              Edit Attendee
            </Button>
          </Link>
        </div>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Personal Information</CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Full Name
              </label>
              <p className="text-gray-900">
                {`${attendee.last_name}, ${attendee.first_name}`.trim()}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Badge Name
              </label>
              <p className="text-gray-900">{attendee.name_on_badge}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Gender
              </label>
              <p className="text-gray-900">{attendee.gender}</p>
            </div>
            <div className="flex items-center space-x-2">
              <FiMail className="w-4 h-4 text-gray-400" />
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Email
                </label>
                <p className="text-gray-900">{attendee.email}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <FiPhone className="w-4 h-4 text-gray-400" />
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Phone
                </label>
                <p className="text-gray-900">{attendee.phone_number}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Address Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <FiMapPin className="w-5 h-5 mr-2" />
              Address
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Street Address
              </label>
              <p className="text-gray-900">{attendee.street_address}</p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  City
                </label>
                <p className="text-gray-900">{attendee.city}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  State
                </label>
                <p className="text-gray-900">{attendee.state}</p>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                ZIP Code
              </label>
              <p className="text-gray-900">{attendee.zip_code}</p>
            </div>
          </CardBody>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <FiBriefcase className="w-5 h-5 mr-2" />
              Professional
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Organization
              </label>
              <p className="text-gray-900">{attendee.organization}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Job Title
              </label>
              <p className="text-gray-900">{attendee.job_title}</p>
            </div>
          </CardBody>
        </Card>

        {/* Registration Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Registration Details</CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Registration Type
              </label>
              <div className="mt-1">
                <Badge variant="outline">{attendee.registration_type}</Badge>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <FiCalendar className="w-4 h-4 text-gray-400" />
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Registered
                </label>
                <p className="text-gray-900">
                  {formatDate(new Date(attendee.created_at))}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Golf Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <FiTarget className="w-5 h-5 mr-2" />
              Golf Tournament
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Participating
              </label>
              <div className="mt-1">
                {attendee.attending_golf ? (
                  <Badge className="bg-green-100 text-green-800">Yes</Badge>
                ) : (
                  <Badge variant="secondary">No</Badge>
                )}
              </div>
            </div>
            {attendee.attending_golf && (
              <>
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Club Rental
                  </label>
                  <div className="mt-1">
                    {attendee.golf_club_rental ? (
                      <Badge className="bg-blue-100 text-blue-800">
                        Yes ({attendee.golf_club_handedness})
                      </Badge>
                    ) : (
                      <Badge variant="secondary">No</Badge>
                    )}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Golf Total
                  </label>
                  <p className="text-gray-900">
                    {formatCurrency(attendee.golf_total)}
                  </p>
                </div>
                {attendee.golf_club_rental && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Club Rental Total
                    </label>
                    <p className="text-gray-900">
                      {formatCurrency(attendee.golf_club_rental_total || 0)}
                    </p>
                  </div>
                )}
              </>
            )}
          </CardBody>
        </Card>

        {/* Meal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <FiCoffee className="w-5 h-5 mr-2" />
              Meals
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Selected Meals
              </label>
              <div className="text-gray-900 mt-1">
                {formatMealsAsLinks(attendee.meals)}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Dietary Needs
              </label>
              <p className="text-gray-900">
                {attendee.dietary_needs || 'None specified'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Meal Total
              </label>
              <p className="text-gray-900">
                {formatCurrency(attendee.meal_total || 0)}
              </p>
            </div>
          </CardBody>
        </Card>

        {/* Payment Information */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <FiCreditCard className="w-5 h-5 mr-2" />
              Payment Information
            </CardTitle>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Status
                </label>
                <div className="mt-1">
                  {getPaymentStatusBadge(attendee.payment_status)}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Registration Total
                </label>
                <p className="text-gray-900 font-semibold">
                  {formatCurrency(attendee.registration_total)}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Grand Total
                </label>
                <p className="text-gray-900 font-bold text-lg">
                  {formatCurrency(attendee.grand_total)}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Payment ID
                </label>
                <p className="text-gray-900 text-sm font-mono">
                  {attendee.payment_id || 'N/A'}
                </p>
              </div>
            </div>

            {(attendee.receipt_url || attendee.invoice_url) && (
              <div className="mt-6 pt-6 border-t">
                <label className="text-sm font-medium text-gray-500 mb-3 block">
                  Documents
                </label>
                <div className="flex space-x-4">
                  {attendee.receipt_url && (
                    <a
                      href={attendee.receipt_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[var(--iepa-primary-blue)] hover:underline text-sm"
                    >
                      View Receipt
                    </a>
                  )}
                  {attendee.invoice_url && (
                    <a
                      href={attendee.invoice_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[var(--iepa-primary-blue)] hover:underline text-sm"
                    >
                      View Invoice
                    </a>
                  )}
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
