import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { action, userId, registrationType } = await request.json();
    const supabaseAdmin = createSupabaseAdmin();

    if (action === 'test-duplicate-registration') {
      // Test creating duplicate registrations to verify constraints
      const testData = {
        user_id: userId || 'test-user-id',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
      };

      let tableName: string;
      let specificData: any = {};

      switch (registrationType) {
        case 'attendee':
          tableName = 'iepa_attendee_registrations';
          specificData = {
            registration_type: 'member',
            attendee_type: 'attendee',
            name_on_badge: 'Test User',
            gender: 'prefer-not-to-say',
            phone_number: '************',
            street_address: '123 Test St',
            city: 'Test City',
            state: 'CA',
            zip_code: '12345',
            organization: 'Test Org',
            job_title: 'Test Title',
            attending_golf: false,
            golf_club_rental: false,
            night_one: false,
            night_two: false,
            meals: [],
            registration_total: 0,
            golf_total: 0,
            golf_club_rental_total: 0,
            meal_total: 0,
            grand_total: 0,
            payment_status: 'pending',
          };
          break;
        case 'speaker':
          tableName = 'iepa_speaker_registrations';
          specificData = {
            organization_name: 'Test Org',
            job_title: 'Test Speaker',
            bio: 'Test bio',
          };
          break;
        case 'sponsor':
          tableName = 'iepa_sponsor_registrations';
          specificData = {
            sponsor_name: 'Test Sponsor',
            sponsor_url: 'https://test.com',
            sponsor_description: 'Test description',
            contact_name: 'Test Contact',
            contact_email: '<EMAIL>',
            contact_phone: '************',
            contact_title: 'Test Title',
            sponsorship_level: 'bronze',
            sponsorship_amount: 1000,
            billing_address: '123 Test St',
            billing_city: 'Test City',
            billing_state: 'CA',
            billing_zip: '12345',
            billing_country: 'United States',
            payment_status: 'pending',
          };
          break;
        default:
          throw new Error('Invalid registration type');
      }

      // Try to insert first registration
      const { data: firstInsert, error: firstError } = await supabaseAdmin
        .from(tableName)
        .insert([{ ...testData, ...specificData }])
        .select();

      if (firstError) {
        return NextResponse.json({
          success: false,
          error: 'Failed to create first registration',
          details: firstError.message,
        });
      }

      // Try to insert duplicate registration (should fail)
      const { data: secondInsert, error: secondError } = await supabaseAdmin
        .from(tableName)
        .insert([{ ...testData, ...specificData }])
        .select();

      // Clean up the test data
      if (firstInsert && firstInsert.length > 0) {
        await supabaseAdmin
          .from(tableName)
          .delete()
          .eq('id', firstInsert[0].id);
      }

      if (secondError) {
        // This is expected - the constraint should prevent the duplicate
        return NextResponse.json({
          success: true,
          message: 'Constraint working correctly - duplicate registration prevented',
          constraintError: secondError.message,
          firstRegistrationCreated: !!firstInsert,
          duplicateRegistrationPrevented: true,
        });
      } else {
        // This is unexpected - the constraint is not working
        // Clean up the second insert too
        if (secondInsert && secondInsert.length > 0) {
          await supabaseAdmin
            .from(tableName)
            .delete()
            .eq('id', secondInsert[0].id);
        }

        return NextResponse.json({
          success: false,
          message: 'Constraint NOT working - duplicate registration was allowed',
          firstRegistrationCreated: !!firstInsert,
          duplicateRegistrationPrevented: false,
        });
      }
    }

    if (action === 'apply-constraints') {
      // Apply the unique constraints manually
      const constraints = [];

      try {
        // For attendee registrations: only primary attendees (not spouse/child) can have one registration per user
        const { error: attendeeConstraintError } = await supabaseAdmin.rpc('exec_sql', {
          sql: `
            CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_unique_primary_attendee_per_user 
            ON iepa_attendee_registrations (user_id) 
            WHERE attendee_type = 'attendee';
          `
        });

        if (attendeeConstraintError) {
          constraints.push({
            type: 'attendee_index',
            success: false,
            error: attendeeConstraintError.message,
          });
        } else {
          constraints.push({
            type: 'attendee_index',
            success: true,
          });
        }

        // For speaker registrations: one registration per user
        const { error: speakerConstraintError } = await supabaseAdmin.rpc('exec_sql', {
          sql: `
            ALTER TABLE iepa_speaker_registrations 
            ADD CONSTRAINT unique_speaker_per_user UNIQUE (user_id);
          `
        });

        if (speakerConstraintError && !speakerConstraintError.message.includes('already exists')) {
          constraints.push({
            type: 'speaker_constraint',
            success: false,
            error: speakerConstraintError.message,
          });
        } else {
          constraints.push({
            type: 'speaker_constraint',
            success: true,
          });
        }

        // For sponsor registrations: one registration per user
        const { error: sponsorConstraintError } = await supabaseAdmin.rpc('exec_sql', {
          sql: `
            ALTER TABLE iepa_sponsor_registrations 
            ADD CONSTRAINT unique_sponsor_per_user UNIQUE (user_id);
          `
        });

        if (sponsorConstraintError && !sponsorConstraintError.message.includes('already exists')) {
          constraints.push({
            type: 'sponsor_constraint',
            success: false,
            error: sponsorConstraintError.message,
          });
        } else {
          constraints.push({
            type: 'sponsor_constraint',
            success: true,
          });
        }

        return NextResponse.json({
          success: true,
          message: 'Constraints application attempted',
          constraints,
        });
      } catch (error) {
        return NextResponse.json({
          success: false,
          error: 'Failed to apply constraints',
          details: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action specified',
    });
  } catch (error) {
    console.error('Error in test constraints:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to test constraints',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
