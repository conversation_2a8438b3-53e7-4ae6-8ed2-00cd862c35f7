'use client';

interface HeroImageSectionProps {
  backgroundImage: string;
  children: React.ReactNode;
  className?: string;
  overlayOpacity?: number;
}

export const HeroImageSection: React.FC<HeroImageSectionProps> = ({
  backgroundImage,
  children,
  className = '',
  overlayOpacity = 0.4,
}) => {
  return (
    <section
      className={`iepa-hero-image-section ${className}`}
      style={{
        position: 'relative',
        minHeight: '50vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
    >
      {/* Background Overlay */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: `rgba(57, 109, 164, ${overlayOpacity})`,
          zIndex: 1,
        }}
      />

      {/* Content Overlay */}
      <div
        style={{
          position: 'relative',
          zIndex: 10,
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'flex-end',
          justifyContent: 'center',
          padding: '2rem 0',
        }}
      >
        <div className="iepa-container">{children}</div>
      </div>
    </section>
  );
};
