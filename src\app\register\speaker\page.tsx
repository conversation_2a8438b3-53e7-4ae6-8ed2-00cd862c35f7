'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

// Force dynamic rendering to avoid SSG issues with useSearchParams
export const dynamic = 'force-dynamic';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
  Label,
  Checkbox,
  RegistrationCardRadio,
  CancellationPolicy,
  // IEPASubmitButton, // TODO: Create this component
} from '@/components/ui';
import { SimpleFileUpload } from '@/components/ui/SimpleFileUpload';
import { PhoneInput } from '@/components/ui/phone-input';
import { CONFERENCE_YEAR, dateUtils } from '@/lib/conference-config';
import { SPEAKER_PRICING } from '@/lib/pricing-config';
import { mealOptions } from '@/lib/meal-config';
import { STATE_PROVINCE_OPTIONS } from '@/lib/address-constants';
import { FORM_STORAGE_KEYS } from '@/lib/form-persistence';
import { useFormPersistence } from '@/hooks/useFormPersistence';
import {
  RestoreDataPrompt,
  FloatingSaveStatus,
  DataManagementPanel,
} from '@/components/ui/form-persistence-ui';
import { useAuth } from '@/contexts/AuthContext';
import { useFormPrefill } from '@/hooks/useFormPrefill';
import { ProtectedRegistrationPage } from '@/components/auth/ProtectedRoute';
import Link from 'next/link';

import { showSuccess, showError, showInfo } from '@/utils/notifications';
import { useRegistrationConstraints } from '@/hooks/useRegistrationConstraints';
import { ExistingRegistrationNotice } from '@/components/registration/ExistingRegistrationNotice';

export default function SpeakerRegistrationPage() {
  // Set page title
  useEffect(() => {
    document.title = 'Speaker Registration - IEPA Conference 2025';
  }, []);

  const { user } = useAuth();
  const prefillAttemptedRef = useRef(false);

  // Check registration constraints
  const {
    constraintCheck,
    loading: constraintLoading,
    error: constraintError,
  } = useRegistrationConstraints({
    registrationType: 'speaker',
    autoCheck: true,
  });
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: user?.email || '',
    // Contact Information
    phoneNumber: '',
    // Professional Information
    organizationName: '',
    jobTitle: '',
    bio: '',
    // File upload fields
    presentationFile: null as string | null,
    headshot: null as string | null,
    // Speaker Pricing & Attendee Information
    speakerPricingType: 'comped-speaker',
    nameOnBadge: '',
    gender: '',
    streetAddress: '',
    city: '',
    state: '',
    zipCode: '',
    golfTournament: false,
    golfClubRental: false,
    golfClubHandedness: '',
    // Night selections for conference lodging
    // Default for comped speakers: Night Two only (main conference night)
    nightOne: false,
    nightTwo: true,
    meals: {} as Record<string, boolean>,
    dietaryRestrictions: '',
    // Partial registration flag
    isPartialRegistration: false,
  });

  // Form persistence
  const persistence = useFormPersistence(formData, setFormData, {
    formKey: FORM_STORAGE_KEYS.SPEAKER,
    debounceMs: 1000,
    expirationDays: 7,
    excludeFields: ['presentationFile', 'headshot'],
    onDataRestored: data => {
      console.log('Speaker form data restored:', data);
      // Show success notification
      showSuccess(
        'Form Data Restored',
        'Your previous progress has been restored.'
      );
    },
    onDataSaved: () => {
      console.log('Speaker form data auto-saved');
    },
    onDataCleared: () => {
      console.log('Speaker form data cleared');
    },
  });

  // Form prefilling from user profile
  const formPrefill = useFormPrefill({
    formType: 'speaker',
    onPrefillComplete: (fieldsPopulated, summary) => {
      console.log('Speaker form prefilled:', { fieldsPopulated, summary });
      showInfo('Form Populated', summary);
    },
    onPrefillError: error => {
      console.error('Speaker form prefill error:', error);
      showError('Prefill Error', 'Could not load your profile information.');
    },
    enabled: true,
  });

  // Form prefilling effect
  const attemptPrefill = useCallback(async () => {
    // Prevent multiple prefill attempts
    if (prefillAttemptedRef.current) {
      return;
    }

    if (!formPrefill.canPrefill) {
      console.log('Cannot prefill speaker form:', {
        user: !!user,
        profile: !!formPrefill.profile,
        hasPrefillData: formPrefill.hasPrefillData,
        isLoading: formPrefill.isLoading,
      });
      return;
    }

    // Check if form already has significant data (from localStorage or user input)
    const hasExistingData =
      formData.firstName || formData.lastName || formData.organizationName;

    if (hasExistingData) {
      console.log('Speaker form already has data, skipping prefill');
      prefillAttemptedRef.current = true;
      return;
    }

    try {
      console.log('🔄 Starting speaker form prefill process');
      prefillAttemptedRef.current = true;
      const result = await formPrefill.prefillForm(formData);
      if (result && result.fieldsPopulated.length > 0) {
        console.log('Applying prefilled data to speaker form');
        setFormData(result.formData);
      }
    } catch (error) {
      console.error('Error during speaker form prefill:', error);
    }
  }, [formData, formPrefill, user]);

  useEffect(() => {
    // Only attempt prefill after a short delay to allow localStorage restoration to complete
    const timeoutId = setTimeout(attemptPrefill, 500);

    return () => clearTimeout(timeoutId);
  }, [attemptPrefill]); // Re-run when attemptPrefill changes

  // Submit button state management
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    // Handle boolean fields
    if (
      field === 'golfTournament' ||
      field === 'golfClubRental' ||
      field === 'nightOne' ||
      field === 'nightTwo'
    ) {
      setFormData(prev => {
        const newData = { ...prev, [field]: value === 'true' };

        // Special logic for night selection based on speaker pricing type
        if (
          (field === 'nightOne' || field === 'nightTwo') &&
          prev.speakerPricingType === 'comped-speaker'
        ) {
          // For comped speakers, only allow one night selection
          if (value === 'true') {
            // If selecting this night, unselect the other night
            if (field === 'nightOne') {
              newData.nightTwo = false;
            } else if (field === 'nightTwo') {
              newData.nightOne = false;
            }
          }
        }

        return newData;
      });
    } else if (field === 'speakerPricingType') {
      setFormData(prev => {
        const newData = { ...prev, [field]: value };

        // When changing speaker pricing type, adjust night selections
        if (value === 'comped-speaker') {
          // For comped speakers, default to night two only (main conference night)
          newData.nightOne = false;
          newData.nightTwo = true;
        } else if (value === 'full-meeting-speaker') {
          // For full meeting speakers, default to both nights
          newData.nightOne = true;
          newData.nightTwo = true;
        }

        return newData;
      });
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleFileUpload = (field: string, url: string | null) => {
    setFormData(prev => ({ ...prev, [field]: url }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Speaker form submitted:', formData);

    if (isSubmitting) return; // Prevent double submission

    try {
      setIsSubmitting(true);
      // Add debugging for required fields
      console.log('Form validation check:');
      console.log('firstName:', formData.firstName);
      console.log('lastName:', formData.lastName);
      console.log('email:', formData.email);
      console.log('phoneNumber:', formData.phoneNumber);
      console.log('organizationName:', formData.organizationName);
      console.log('jobTitle:', formData.jobTitle);
      console.log('bio:', formData.bio);

      // Check registration constraints before submission
      if (!user?.id) {
        throw new Error('Please log in to submit your registration.');
      }

      try {
        const { checkRegistrationConstraints } = await import(
          '@/services/registrationConstraints'
        );

        const constraintResult = await checkRegistrationConstraints(
          user.id,
          'speaker'
        );

        if (!constraintResult.canRegister) {
          throw new Error(constraintResult.message);
        }
      } catch (error) {
        console.error('Error checking registration constraints:', error);
        throw new Error(
          'Unable to verify registration eligibility. Please try again.'
        );
      }

      try {
        // Import Supabase client
        const { supabase } = await import('@/lib/supabase');

        // Use the authenticated user for registration
        if (!user?.id) {
          throw new Error(
            'User authentication required for registration submission.'
          );
        }

        const registrationUser = {
          id: user.id,
          email: formData.email || user.email || '<EMAIL>',
        };

        // Convert meals object to array format for database
        const selectedMeals = Object.entries(formData.meals)
          .filter(([, selected]) => selected)
          .map(([mealKey]) => mealKey);

        // Calculate pricing based on speaker type
        const speakerPricing = SPEAKER_PRICING.find(
          p => p.id === formData.speakerPricingType
        );

        if (!speakerPricing) {
          throw new Error('Invalid speaker pricing type selected.');
        }

        // Calculate costs
        const golfTotal = formData.golfTournament ? 200 : 0;
        const golfClubRentalTotal = formData.golfClubRental ? 75 : 0;
        const mealTotal = 0; // All meals are complimentary
        const grandTotal =
          speakerPricing.basePrice +
          golfTotal +
          golfClubRentalTotal +
          mealTotal;

        // Create attendee registration first
        const fullName = `${formData.firstName} ${formData.lastName}`.trim();
        const attendeeData = {
          user_id: registrationUser.id,
          registration_type: formData.speakerPricingType,
          email: formData.email,
          first_name: formData.firstName,
          last_name: formData.lastName,
          name_on_badge: formData.nameOnBadge || fullName,
          gender: formData.gender,
          phone_number: formData.phoneNumber,
          street_address: formData.streetAddress,
          city: formData.city,
          state: formData.state,
          zip_code: formData.zipCode,
          organization: formData.organizationName,
          job_title: formData.jobTitle,
          attending_golf: formData.golfTournament,
          golf_club_rental: formData.golfClubRental,
          golf_club_handedness: formData.golfClubHandedness,
          night_one: formData.nightOne,
          night_two: formData.nightTwo,
          meals: selectedMeals,
          dietary_needs: formData.dietaryRestrictions,
          registration_total: speakerPricing.basePrice,
          golf_total: golfTotal,
          golf_club_rental_total: golfClubRentalTotal,
          meal_total: mealTotal,
          grand_total: grandTotal,
          payment_status:
            speakerPricing.basePrice === 0 ? 'completed' : 'pending',
          is_speaker: true,
          speaker_pricing_type: formData.speakerPricingType,
        };

        console.log('Creating attendee registration:', attendeeData);

        const { data: attendeeResult, error: attendeeError } = await supabase
          .from('iepa_attendee_registrations')
          .insert([attendeeData])
          .select()
          .single();

        if (attendeeError) {
          throw new Error(
            `Failed to create attendee registration: ${attendeeError.message}`
          );
        }

        console.log('Attendee registration created:', attendeeResult);

        // Create speaker registration
        const speakerData = {
          user_id: registrationUser.id,
          first_name: formData.firstName,
          last_name: formData.lastName,
          email: formData.email,
          phone_number: formData.phoneNumber,
          organization_name: formData.organizationName,
          job_title: formData.jobTitle,
          bio: formData.bio,
          presentation_file_url: formData.presentationFile,
          headshot_url: formData.headshot,
          registration_status: formData.isPartialRegistration
            ? 'partial'
            : 'complete',
        };

        console.log('Creating speaker registration:', speakerData);

        const { data: speakerResult, error: speakerError } = await supabase
          .from('iepa_speaker_registrations')
          .insert([speakerData])
          .select()
          .single();

        if (speakerError) {
          throw new Error(
            `Failed to create speaker registration: ${speakerError.message}`
          );
        }

        console.log('Speaker registration created:', speakerResult);

        // Update attendee registration with speaker link
        const { error: updateError } = await supabase
          .from('iepa_attendee_registrations')
          .update({ speaker_registration_id: speakerResult.id })
          .eq('id', attendeeResult.id);

        if (updateError) {
          console.warn(
            'Failed to link speaker to attendee registration:',
            updateError
          );
        }

        console.log('Registration successful:', {
          speakerRegistrationId: speakerResult.id,
          attendeeRegistrationId: attendeeResult.id,
        });

        // 📧 SEND SPEAKER REGISTRATION CONFIRMATION EMAIL
        try {
          const emailResponse = await fetch('/api/send-registration-email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: formData.email,
              fullName: fullName,
              type: 'speaker',
              confirmationNumber: attendeeResult.id,
              userId: registrationUser.id,
              speakerPricingType: formData.speakerPricingType,
            }),
          });

          if (emailResponse.ok) {
            console.log(
              '[EMAIL-DEBUG] Speaker registration confirmation email sent to:',
              formData.email
            );
          } else {
            console.warn(
              '[EMAIL-WARNING] Email API returned non-OK status:',
              emailResponse.status
            );
          }
        } catch (emailError) {
          console.error(
            '[EMAIL-ERROR] Failed to send speaker registration confirmation email:',
            emailError
          );
          // Don't fail the registration if email fails
        }

        // Show success notification
        showSuccess(
          'Speaker Registration Submitted!',
          'You will receive a confirmation email shortly.'
        );

        // Clear persisted form data after successful submission
        persistence.clearFormData();

        // Redirect to payment if amount > 0 (golf fees)
        if (grandTotal > 0) {
          // Import Stripe utilities
          const { stripeUtils } = await import('@/lib/stripe-client');

          // Prepare payment data
          const paymentData = {
            registrationId: attendeeResult.id,
            registrationType: 'speaker' as const,
            customerEmail: formData.email,
            customerName: fullName,
            totalAmount: grandTotal,
            lineItems: [
              {
                name: 'Speaker Registration',
                description: `${formData.speakerPricingType === 'comped-speaker' ? 'Comped' : 'Paid'} Speaker Registration`,
                price: speakerPricing.basePrice,
                quantity: 1,
              },
              ...(formData.golfTournament
                ? [
                    {
                      name: 'Golf Tournament',
                      description: 'Golf Tournament Entry',
                      price: 200,
                      quantity: 1,
                    },
                  ]
                : []),
              ...(formData.golfClubRental
                ? [
                    {
                      name: 'Golf Club Rental',
                      description: 'Golf Club Rental',
                      price: 75,
                      quantity: 1,
                    },
                  ]
                : []),
            ],
            metadata: {
              registrationId: attendeeResult.id,
              registrationType: 'speaker',
              userId: registrationUser.id,
              speakerPricingType: formData.speakerPricingType,
              golfTournament: formData.golfTournament.toString(),
              golfClubRental: formData.golfClubRental.toString(),
            },
          };

          console.log('Initiating payment process for speaker...', paymentData);

          // Process payment (creates checkout session and redirects)
          const paymentResult = await stripeUtils.processPayment(paymentData);

          if (!paymentResult.success) {
            console.error('Payment initiation failed:', paymentResult.error);
            showError(
              'Payment Error',
              'Failed to process payment. Please try again or contact support.'
            );
            setIsSubmitting(false);
            return;
          }

          // Payment processing will redirect to Stripe, so we don't need to do anything else
          console.log(
            'Payment initiated successfully, redirecting to Stripe...'
          );
          return;
        }

        // Prepare confirmation data (for free registrations)
        const confirmationData = {
          speakerId: speakerResult.id,
          attendeeId: attendeeResult.id,
          speakerType: formData.speakerPricingType,
          grandTotal: grandTotal,
          isPartial: formData.isPartialRegistration,
          email: formData.email,
          name: `${formData.firstName} ${formData.lastName}`,
        };

        // Redirect to speaker confirmation page (for free registrations)
        window.location.href = `/register/speaker/confirmation?data=${encodeURIComponent(JSON.stringify(confirmationData))}`;

        // Reset form after successful submission (this may not execute due to redirect)
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phoneNumber: '',
          organizationName: '',
          jobTitle: '',
          bio: '',
          presentationFile: null,
          headshot: null,
          speakerPricingType: 'comped-speaker',
          nameOnBadge: '',
          gender: '',
          streetAddress: '',
          city: '',
          state: '',
          zipCode: '',
          golfTournament: false,
          golfClubRental: false,
          golfClubHandedness: '',
          // Default for comped speakers: Night Two only
          nightOne: false,
          nightTwo: true,
          meals: {},
          dietaryRestrictions: '',
          isPartialRegistration: false,
        });

        console.log('Speaker registration submitted successfully');
        setIsSubmitting(false);
      } catch (error) {
        console.error('Error in nested try block:', error);
        throw error; // Re-throw to be caught by outer catch
      }
    } catch (error) {
      console.error('Speaker form submission error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'An error occurred';
      showError('Registration Failed', errorMessage);
      setIsSubmitting(false);
    }
  };

  return (
    <ProtectedRegistrationPage>
      <div className="iepa-container">
        {/* Header */}
        <section className="iepa-section">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="iepa-heading-1 mb-4">
              Speaker Registration - IEPA {CONFERENCE_YEAR}
            </h1>
            <p className="iepa-body mb-6">
              Register as a speaker for the IEPA {CONFERENCE_YEAR} Annual
              Meeting. We welcome presentations on environmental protection,
              sustainability, and related topics.
            </p>
            <div
              className="p-4 rounded-lg"
              style={{ backgroundColor: 'var(--iepa-gray-50)' }}
            >
              <p className="iepa-body-small">
                <strong>Registration Deadline:</strong> Please submit your
                registration by the specified deadline. Registration
                confirmation will be sent and you will be provided with
                additional details.
              </p>
            </div>
          </div>
        </section>

        {/* Registration Constraint Check */}
        {constraintLoading && (
          <section className="iepa-section">
            <div className="max-w-4xl mx-auto text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="iepa-body">Checking registration eligibility...</p>
            </div>
          </section>
        )}

        {constraintError && (
          <section className="iepa-section">
            <div className="max-w-4xl mx-auto">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-800">
                  Error checking registration eligibility: {constraintError}
                </p>
              </div>
            </div>
          </section>
        )}

        {constraintCheck &&
          !constraintCheck.canRegister &&
          constraintCheck.existingRegistration && (
            <ExistingRegistrationNotice
              registration={constraintCheck.existingRegistration}
              message={constraintCheck.message}
              showEditOption={true}
              showViewOption={true}
            />
          )}

        {/* Only show the form if user can register or if we're still loading */}
        {(constraintLoading ||
          constraintError ||
          (constraintCheck && constraintCheck.canRegister)) && (
          <>
            {/* Speaker Benefits */}
            <section className="iepa-section">
              <div className="max-w-4xl mx-auto">
                <div className="mb-8 p-6 rounded-lg text-center text-white bg-green-700">
                  <h3 className="text-xl font-bold mb-4">Speaker Benefits</h3>
                  <ul className="text-left space-y-2 max-w-2xl mx-auto">
                    <li>
                      • Professional recognition and networking opportunities
                    </li>
                    <li>• Complimentary conference registration</li>
                    <li>• Speaker dinner and exclusive events</li>
                    <li>• Professional bio listing in conference materials</li>
                    <li>• Recording of your presentation (if desired)</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Form Persistence UI */}
            <section className="iepa-section">
              <div className="max-w-4xl mx-auto">
                <RestoreDataPrompt
                  show={persistence.showRestorePrompt}
                  dataAge={persistence.dataAge}
                  onRestore={persistence.restoreData}
                  onStartFresh={persistence.startFresh}
                  onDismiss={persistence.dismissPrompt}
                  formType="speaker registration"
                />

                {/* Data Management Panel */}
                {persistence.hasPersistedData &&
                  !persistence.showRestorePrompt && (
                    <DataManagementPanel
                      hasPersistedData={persistence.hasPersistedData}
                      dataAge={persistence.dataAge}
                      isDataExpired={persistence.isDataExpired}
                      onClearData={persistence.clearFormData}
                      formType="speaker registration"
                      className="mb-6"
                    />
                  )}
              </div>
            </section>

            {/* Speaker Form */}
            <section className="iepa-section">
              <div className="max-w-4xl mx-auto">
                <form onSubmit={handleSubmit} className="space-y-8">
                  {/* Personal Information */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">1. Speaker Information</h2>
                    </CardHeader>
                    <CardBody>
                      <div className="iepa-form-grid">
                        <div className="iepa-form-field">
                          <Input
                            label="First Name"
                            placeholder="Enter your first name"
                            value={formData.firstName}
                            onChange={e =>
                              handleInputChange('firstName', e.target.value)
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="Last Name"
                            placeholder="Enter your last name"
                            value={formData.lastName}
                            onChange={e =>
                              handleInputChange('lastName', e.target.value)
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="Email Address"
                            type="email"
                            placeholder="<EMAIL>"
                            value={formData.email}
                            onChange={e =>
                              handleInputChange('email', e.target.value)
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field">
                          <PhoneInput
                            label="Phone Number"
                            value={formData.phoneNumber}
                            onChange={value =>
                              handleInputChange('phoneNumber', value)
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="Organization"
                            placeholder="Your organization name"
                            value={formData.organizationName}
                            onChange={e =>
                              handleInputChange(
                                'organizationName',
                                e.target.value
                              )
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field col-span-full">
                          <Input
                            label="Job Title"
                            placeholder="Your job title"
                            value={formData.jobTitle}
                            onChange={e =>
                              handleInputChange('jobTitle', e.target.value)
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field col-span-full">
                          <Label htmlFor="bio">Professional Bio *</Label>
                          <Textarea
                            id="bio"
                            placeholder="Please provide a brief professional biography (200-300 words)"
                            value={formData.bio}
                            onChange={e =>
                              handleInputChange('bio', e.target.value)
                            }
                            required
                            rows={4}
                          />
                          <p className="text-sm text-gray-600 mt-1">
                            This will be used in conference materials and
                            speaker introductions
                          </p>
                        </div>

                        <div className="iepa-form-field col-span-full">
                          <SimpleFileUpload
                            label="Professional Headshot"
                            description="Upload a professional headshot (JPG, PNG, WebP - max 5MB)"
                            bucket="iepa-presentations"
                            folder="speaker-headshots"
                            maxSize={5242880} // 5MB
                            allowedTypes={[
                              'image/jpeg',
                              'image/png',
                              'image/webp',
                            ]}
                            accept=".jpg,.jpeg,.png,.webp"
                            onFileUpload={(url) =>
                              handleFileUpload('headshot', url)
                            }
                            placeholder="Upload your professional headshot"
                          />
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Presentation File Upload */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">2. Presentation File</h2>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-6">
                        {/* Partial Registration Option */}
                        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="flex items-start space-x-3">
                            <Checkbox
                              id="partial-registration"
                              checked={formData.isPartialRegistration}
                              onCheckedChange={checked =>
                                handleInputChange(
                                  'isPartialRegistration',
                                  checked.toString()
                                )
                              }
                            />
                            <div className="flex-1">
                              <Label
                                htmlFor="partial-registration"
                                className="font-medium cursor-pointer"
                              >
                                Submit partial registration (presentation file
                                can be uploaded later)
                              </Label>
                              <p className="text-sm text-blue-700 mt-1">
                                Check this option if you want to submit your
                                registration now for headcount purposes and
                                upload your presentation file at a later time.
                                You can return to complete your registration
                                with the presentation file before the
                                conference.
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* File Upload - Only show if not partial registration */}
                        {!formData.isPartialRegistration && (
                          <div className="iepa-form-grid">
                            <div className="iepa-form-field col-span-full">
                              <SimpleFileUpload
                                label="Presentation File"
                                description="Upload your presentation file (PDF, PPT, PPTX, DOC, DOCX - max 50MB)"
                                bucket="iepa-presentations"
                                folder="speaker-presentations"
                                maxSize={52428800} // 50MB
                                allowedTypes={[
                                  'application/pdf',
                                  'application/vnd.ms-powerpoint',
                                  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                                  'application/msword',
                                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                ]}
                                accept=".pdf,.ppt,.pptx,.doc,.docx"
                                onFileUpload={(url) =>
                                  handleFileUpload('presentationFile', url)
                                }
                                placeholder="Upload your presentation file"
                              />
                            </div>
                          </div>
                        )}

                        {/* Partial Registration Notice */}
                        {formData.isPartialRegistration && (
                          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <p className="text-sm text-yellow-800">
                              <strong>Partial Registration Selected:</strong>{' '}
                              You can submit your registration now without a
                              presentation file. Please remember to return and
                              upload your presentation file before the
                              conference deadline.
                            </p>
                          </div>
                        )}
                      </div>
                    </CardBody>
                  </Card>

                  {/* Speaker Registration Type */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">
                        3. Speaker Registration Type
                      </h2>
                      <p className="iepa-body text-gray-600">
                        Choose your conference registration option. As a
                        speaker, you have special pricing options.
                      </p>
                    </CardHeader>
                    <CardBody>
                      <div className="iepa-form-field">
                        <RegistrationCardRadio
                          options={SPEAKER_PRICING}
                          value={formData.speakerPricingType}
                          onValueChange={value =>
                            handleInputChange('speakerPricingType', value)
                          }
                          name="speakerPricingType"
                          required
                        />
                      </div>
                    </CardBody>
                  </Card>

                  {/* Conference Lodging - Moved from Step 6 to Step 4 */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">4. Conference Lodging</h2>
                      <div className="space-y-2">
                        <p className="iepa-body text-gray-600">
                          Select which nights you plan to stay at the conference
                          center.
                        </p>
                        {formData.speakerPricingType === 'comped-speaker' && (
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <p className="text-sm text-blue-800">
                              <strong>Complimentary Speaker:</strong> Your
                              registration includes one night of lodging
                              accommodation. Please select either Night One or
                              Night Two (you can only select one).
                            </p>
                          </div>
                        )}
                        {formData.speakerPricingType ===
                          'full-meeting-speaker' && (
                          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                            <p className="text-sm text-green-800">
                              <strong>Full Meeting Speaker:</strong> Your
                              registration includes two nights of lodging. Both
                              nights are pre-selected by default, but you can
                              adjust as needed.
                            </p>
                          </div>
                        )}
                      </div>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-4">
                        {(() => {
                          const nightOneInfo =
                            dateUtils.getLodgingNightInfo('nightOne');
                          const nightTwoInfo =
                            dateUtils.getLodgingNightInfo('nightTwo');

                          return (
                            <>
                              <div className="iepa-form-field">
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="nightOne"
                                    checked={formData.nightOne}
                                    onCheckedChange={checked =>
                                      handleInputChange(
                                        'nightOne',
                                        checked.toString()
                                      )
                                    }
                                  />
                                  <Label htmlFor="nightOne">
                                    <span className="hidden sm:inline">
                                      Night One ({nightOneInfo?.displayDate})
                                    </span>
                                    <span className="sm:hidden">
                                      Night One ({nightOneInfo?.mobileDate})
                                    </span>
                                  </Label>
                                </div>
                                <p className="text-sm text-gray-600 mt-1 ml-6">
                                  {nightOneInfo?.description}
                                </p>
                              </div>

                              <div className="iepa-form-field">
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="nightTwo"
                                    checked={formData.nightTwo}
                                    onCheckedChange={checked =>
                                      handleInputChange(
                                        'nightTwo',
                                        checked.toString()
                                      )
                                    }
                                  />
                                  <Label htmlFor="nightTwo">
                                    <span className="hidden sm:inline">
                                      Night Two ({nightTwoInfo?.displayDate})
                                    </span>
                                    <span className="sm:hidden">
                                      Night Two ({nightTwoInfo?.mobileDate})
                                    </span>
                                  </Label>
                                </div>
                                <p className="text-sm text-gray-600 mt-1 ml-6">
                                  {nightTwoInfo?.description}
                                </p>
                              </div>
                            </>
                          );
                        })()}
                      </div>
                    </CardBody>
                  </Card>

                  {/* Conference Registration Details */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">
                        5. Annual Meeting Registration Details
                      </h2>
                      <p className="iepa-body text-gray-600">
                        Please provide additional information for your annual
                        meeting registration.
                      </p>
                    </CardHeader>
                    <CardBody>
                      <div className="iepa-form-grid">
                        <div className="iepa-form-field">
                          <Input
                            label="Name on Badge"
                            placeholder="Name as it should appear on your badge"
                            value={formData.nameOnBadge}
                            onChange={e =>
                              handleInputChange('nameOnBadge', e.target.value)
                            }
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Label htmlFor="gender">Gender</Label>
                          <Select
                            value={formData.gender}
                            onValueChange={value =>
                              handleInputChange('gender', value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select gender" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="male">Male</SelectItem>
                              <SelectItem value="female">Female</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="iepa-form-field col-span-full">
                          <Input
                            label="Street Address"
                            placeholder="Your street address"
                            value={formData.streetAddress}
                            onChange={e =>
                              handleInputChange('streetAddress', e.target.value)
                            }
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="City"
                            placeholder="City"
                            value={formData.city}
                            onChange={e =>
                              handleInputChange('city', e.target.value)
                            }
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Label htmlFor="speaker-state-select">
                            State/Province
                          </Label>
                          <Select
                            value={formData.state}
                            onValueChange={value =>
                              handleInputChange('state', value)
                            }
                          >
                            <SelectTrigger id="speaker-state-select">
                              <SelectValue placeholder="Select state or province" />
                            </SelectTrigger>
                            <SelectContent>
                              {STATE_PROVINCE_OPTIONS.map(option => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="ZIP Code"
                            placeholder="ZIP Code"
                            value={formData.zipCode}
                            onChange={e =>
                              handleInputChange('zipCode', e.target.value)
                            }
                          />
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Golf Tournament */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">
                        6. Golf Tournament (Optional)
                      </h2>
                      <p className="iepa-body text-gray-600">
                        Join us for the optional golf tournament. Additional
                        fees apply.
                      </p>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-4">
                        <div className="iepa-form-field">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="golfTournament"
                              checked={formData.golfTournament}
                              onCheckedChange={checked =>
                                handleInputChange(
                                  'golfTournament',
                                  checked.toString()
                                )
                              }
                            />
                            <Label htmlFor="golfTournament">
                              Golf Tournament Registration ($200)
                            </Label>
                          </div>
                        </div>

                        {formData.golfTournament && (
                          <>
                            <div className="iepa-form-field">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="golfClubRental"
                                  checked={formData.golfClubRental}
                                  onCheckedChange={checked =>
                                    handleInputChange(
                                      'golfClubRental',
                                      checked.toString()
                                    )
                                  }
                                />
                                <Label htmlFor="golfClubRental">
                                  Callaway Rogues Rental golf club needed? ($75)
                                </Label>
                              </div>
                            </div>

                            {formData.golfClubRental && (
                              <div className="iepa-form-field">
                                <Label htmlFor="golfClubHandedness">
                                  Club Handedness
                                </Label>
                                <Select
                                  value={formData.golfClubHandedness}
                                  onValueChange={value =>
                                    handleInputChange(
                                      'golfClubHandedness',
                                      value
                                    )
                                  }
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select handedness" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="right">
                                      Right Handed
                                    </SelectItem>
                                    <SelectItem value="left">
                                      Left Handed
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </CardBody>
                  </Card>

                  {/* Meal Selection */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">7. Meal Selection</h2>
                      <p className="iepa-body text-gray-600">
                        Select the meals you plan to attend. All meals are
                        complimentary for conference attendees.
                      </p>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-6">
                        {mealOptions.map(dayOption => (
                          <div key={dayOption.day} className="space-y-3">
                            <h3 className="iepa-heading-3">
                              <span className="hidden sm:inline">
                                {dayOption.displayDate}
                              </span>
                              <span className="sm:hidden">
                                {dateUtils.formatMobileDate(dayOption.date)}
                              </span>
                            </h3>
                            <div className="grid gap-3">
                              {dayOption.meals.map(meal => (
                                <div
                                  key={meal.key}
                                  className="flex items-center space-x-2"
                                >
                                  <Checkbox
                                    id={meal.key}
                                    checked={formData.meals[meal.key] || false}
                                    onCheckedChange={checked => {
                                      const newMeals = { ...formData.meals };
                                      newMeals[meal.key] = checked as boolean;
                                      setFormData(prev => ({
                                        ...prev,
                                        meals: newMeals,
                                      }));
                                    }}
                                  />
                                  <Label htmlFor={meal.key} className="flex-1">
                                    <div className="flex justify-between items-center">
                                      <span>{meal.name}</span>
                                      <span className="text-sm text-gray-500">
                                        {meal.time}
                                      </span>
                                    </div>
                                    {meal.description && (
                                      <p className="text-sm text-gray-600 mt-1">
                                        {meal.description}
                                      </p>
                                    )}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}

                        <div className="iepa-form-field">
                          <Label htmlFor="dietaryRestrictions">
                            Dietary Restrictions
                          </Label>
                          <Textarea
                            id="dietaryRestrictions"
                            placeholder="Please list any dietary restrictions, allergies, or special meal requirements"
                            value={formData.dietaryRestrictions}
                            onChange={e =>
                              handleInputChange(
                                'dietaryRestrictions',
                                e.target.value
                              )
                            }
                            rows={3}
                          />
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Submit Section */}
                  <Card>
                    <CardBody>
                      <div className="text-center">
                        {/* Cancellation Policy - Displayed before payment/submission */}
                        <div className="mb-6">
                          <CancellationPolicy />
                        </div>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                          <Button asChild variant="bordered" size="lg">
                            <Link href="/register">
                              Back to Registration Options
                            </Link>
                          </Button>

                          <Button
                            type="submit"
                            disabled={
                              isSubmitting ||
                              !formData.firstName ||
                              !formData.lastName ||
                              !formData.email
                            }
                            className={`px-8 py-3 text-lg font-semibold transition-colors ${
                              isSubmitting ||
                              !formData.firstName ||
                              !formData.lastName ||
                              !formData.email
                                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                                : 'bg-green-600 hover:bg-green-700 text-white cursor-pointer'
                            }`}
                            data-testid="submit-speaker-registration-button"
                          >
                            {isSubmitting
                              ? 'Submitting Registration...'
                              : formData.isPartialRegistration
                                ? 'Submit Partial Registration'
                                : 'Submit Speaker Registration'}
                          </Button>
                        </div>

                        <p className="iepa-body-small text-gray-600 mt-4">
                          You will receive a confirmation email after submitting
                          your registration. Registration confirmation will be
                          sent within 2-3 business days.
                        </p>
                      </div>
                    </CardBody>
                  </Card>
                </form>
              </div>
            </section>

            {/* Floating Save Status */}
            <FloatingSaveStatus
              isAutoSaving={persistence.isAutoSaving}
              lastSavedAt={persistence.lastSavedAt}
              show={true}
              position="bottom-right"
            />
          </>
        )}
      </div>
    </ProtectedRegistrationPage>
  );
}
