'use client';

import React, { useState } from 'react';
import { Card, CardBody, CardHeader, Button } from '@/components/ui';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import {
  FiRefreshCw,
  FiTrash2,
  <PERSON>LogIn,
  FiAlertTriangle,
} from 'react-icons/fi';

export default function AuthRecoveryPage() {
  const [isClearing, setIsClearing] = useState(false);
  const [isCleared, setIsCleared] = useState(false);
  const router = useRouter();

  const clearAuthData = async () => {
    setIsClearing(true);

    try {
      // Sign out from Supabase
      await supabase.auth.signOut();

      // Clear localStorage
      if (typeof window !== 'undefined') {
        // Clear Supabase auth data
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('sb-') || key.includes('supabase')) {
            localStorage.removeItem(key);
          }
        });

        // Clear sessionStorage
        Object.keys(sessionStorage).forEach(key => {
          if (key.startsWith('sb-') || key.includes('supabase')) {
            sessionStorage.removeItem(key);
          }
        });
      }

      setIsCleared(true);

      // Redirect to login after a short delay
      setTimeout(() => {
        router.push('/auth/login');
      }, 2000);
    } catch (error) {
      console.error('Error clearing auth data:', error);
    } finally {
      setIsClearing(false);
    }
  };

  const goToLogin = () => {
    router.push('/auth/login');
  };

  const goHome = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <FiAlertTriangle className="w-6 h-6 text-yellow-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">
              Authentication Recovery
            </h1>
            <p className="text-gray-600 mt-2">
              Fix authentication issues and refresh token errors
            </p>
          </CardHeader>

          <CardBody className="space-y-6">
            {!isCleared ? (
              <>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="font-semibold text-yellow-800 mb-2">
                    Common Authentication Issues:
                  </h3>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• Invalid Refresh Token errors</li>
                    <li>• Session expired or corrupted</li>
                    <li>• Browser storage conflicts</li>
                    <li>• Multiple tab authentication issues</li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">
                    Recovery Options:
                  </h3>

                  <Button
                    onClick={clearAuthData}
                    disabled={isClearing}
                    className="w-full flex items-center justify-center gap-2"
                    variant="default"
                  >
                    {isClearing ? (
                      <>
                        <FiRefreshCw className="w-4 h-4 animate-spin" />
                        Clearing Authentication Data...
                      </>
                    ) : (
                      <>
                        <FiTrash2 className="w-4 h-4" />
                        Clear Auth Data & Reset Session
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={goToLogin}
                    variant="outline"
                    className="w-full flex items-center justify-center gap-2"
                  >
                    <FiLogIn className="w-4 h-4" />
                    Go to Login Page
                  </Button>

                  <Button onClick={goHome} variant="ghost" className="w-full">
                    Return to Home
                  </Button>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 mb-2">
                    Manual Recovery Steps:
                  </h4>
                  <ol className="text-sm text-blue-700 space-y-1">
                    <li>1. Open Developer Tools (F12)</li>
                    <li>2. Go to Application → Storage</li>
                    <li>3. Clear Local Storage & Session Storage</li>
                    <li>4. Clear Cookies for this site</li>
                    <li>5. Refresh the page and sign in again</li>
                  </ol>
                </div>
              </>
            ) : (
              <div className="text-center space-y-4">
                <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <FiRefreshCw className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-green-800 mb-2">
                    Authentication Data Cleared!
                  </h3>
                  <p className="text-green-700 text-sm">
                    Redirecting to login page...
                  </p>
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
