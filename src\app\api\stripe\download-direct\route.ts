// Direct PDF Download from Stripe
// GET /api/stripe/download-direct

import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');
    const registrationType = searchParams.get('registrationType');
    const documentType = searchParams.get('documentType'); // 'invoice' or 'receipt'

    // Validate required parameters
    if (!registrationId || !registrationType || !documentType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Registration ID, type, and document type are required',
        },
        { status: 400 }
      );
    }

    // Validate registration type
    if (!['attendee', 'speaker', 'sponsor'].includes(registrationType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid registration type',
        },
        { status: 400 }
      );
    }

    // Validate document type
    if (!['invoice', 'receipt'].includes(documentType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid document type. Must be "invoice" or "receipt"',
        },
        { status: 400 }
      );
    }

    if (documentType === 'invoice') {
      // Handle invoice download
      try {
        // Check if we're in test mode
        const isTestMode = searchParams.get('testMode') === 'true';

        // Also check if payment intent looks like test data
        const supabaseAdmin = createSupabaseAdmin();
        const { data: paymentData } = await supabaseAdmin
          .from('iepa_payments')
          .select('stripe_payment_intent_id')
          .eq('registration_id', registrationId)
          .eq('registration_type', registrationType)
          .single();

        const hasTestPaymentIntent = paymentData?.stripe_payment_intent_id?.startsWith('pi_test_') || false;
        const shouldUseTestMode = isTestMode || hasTestPaymentIntent;

        if (shouldUseTestMode) {
          // Create test invoice and return PDF directly
          const testResponse = await fetch(`${request.nextUrl.origin}/api/stripe/test-invoice`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              registrationId,
              registrationType,
            }),
          });

          if (!testResponse.ok) {
            throw new Error('Failed to create test invoice');
          }

          const testResult = await testResponse.json();

          if (!testResult.success) {
            throw new Error(testResult.error || 'Failed to create test invoice');
          }

          // Get the PDF URL from the invoice object
          const invoiceId = testResult.invoice.id;
          console.log('Getting PDF URL for invoice:', invoiceId);

          // Retrieve the invoice to get the invoice_pdf URL
          const invoice = await stripe.invoices.retrieve(invoiceId);

          if (!invoice.invoice_pdf) {
            throw new Error('Invoice PDF not available yet. Please try again in a moment.');
          }

          console.log('Invoice PDF URL:', invoice.invoice_pdf);

          // Fetch the PDF from the Stripe-provided URL
          const pdfResponse = await fetch(invoice.invoice_pdf);

          if (!pdfResponse.ok) {
            const errorText = await pdfResponse.text();
            console.error('PDF download error:', errorText);
            throw new Error(`Failed to download PDF: ${pdfResponse.status}`);
          }

          const pdfBuffer = await pdfResponse.arrayBuffer();

          return new NextResponse(pdfBuffer, {
            headers: {
              'Content-Type': 'application/pdf',
              'Content-Disposition': `attachment; filename="invoice-${registrationType}-${registrationId.slice(-8)}.pdf"`,
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0',
              'Content-Length': pdfBuffer.byteLength.toString(),
            },
          });
        }

        // Production mode - get existing invoice
        // Get payment information from database (already fetched above)
        if (!paymentData) {
          return NextResponse.json(
            {
              success: false,
              error: 'Payment record not found',
            },
            { status: 404 }
          );
        }

        const paymentIntentId = paymentData.stripe_payment_intent_id;

        // Check if this looks like test data - if so, fall back to test mode
        if (paymentIntentId?.startsWith('pi_test_') && !paymentIntentId.startsWith('pi_test_1')) {
          console.log('Detected test payment intent, falling back to test mode');
          // Redirect to test mode
          const testUrl = new URL(request.url);
          testUrl.searchParams.set('testMode', 'true');
          return Response.redirect(testUrl.toString());
        }

        // Get the payment intent to find the invoice
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
        
        if (!paymentIntent.invoice) {
          return NextResponse.json(
            {
              success: false,
              error: 'No invoice found for this payment',
            },
            { status: 404 }
          );
        }

        // Get the PDF URL from the invoice object
        const invoiceId = paymentIntent.invoice as string;

        // Retrieve the invoice to get the invoice_pdf URL
        const invoice = await stripe.invoices.retrieve(invoiceId);

        if (!invoice.invoice_pdf) {
          throw new Error('Invoice PDF not available yet. Please try again in a moment.');
        }

        // Fetch the PDF from the Stripe-provided URL
        const pdfResponse = await fetch(invoice.invoice_pdf);

        if (!pdfResponse.ok) {
          throw new Error('Failed to download PDF from Stripe');
        }

        const pdfBuffer = await pdfResponse.arrayBuffer();

        return new NextResponse(pdfBuffer, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="invoice-${registrationType}-${registrationId.slice(-8)}.pdf"`,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Content-Length': pdfBuffer.byteLength.toString(),
          },
        });
      } catch (stripeError) {
        console.error('Error retrieving Stripe invoice:', stripeError);
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to retrieve invoice from Stripe',
          },
          { status: 500 }
        );
      }
    } else {
      // Handle receipt download
      try {
        // Get payment information from database
        const supabaseAdmin = createSupabaseAdmin();
        
        const { data: paymentData, error: paymentError } = await supabaseAdmin
          .from('iepa_payments')
          .select('stripe_payment_intent_id, registration_id, registration_type')
          .eq('registration_id', registrationId)
          .eq('registration_type', registrationType)
          .single();

        if (paymentError || !paymentData) {
          return NextResponse.json(
            {
              success: false,
              error: 'Payment record not found',
            },
            { status: 404 }
          );
        }

        const paymentIntentId = paymentData.stripe_payment_intent_id;

        // Get the payment intent and its charges
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId, {
          expand: ['charges.data.receipt_url'],
        });

        const charges = (paymentIntent as any).charges;
        if (!charges?.data?.length) {
          return NextResponse.json(
            {
              success: false,
              error: 'No charges found for this payment',
            },
            { status: 404 }
          );
        }

        const charge = charges.data[0];
        const receiptUrl = charge.receipt_url;

        if (!receiptUrl) {
          return NextResponse.json(
            {
              success: false,
              error: 'No receipt available for this payment',
            },
            { status: 404 }
          );
        }

        // For receipts, redirect to Stripe's hosted receipt page
        // Stripe doesn't provide a direct PDF API for receipts
        return NextResponse.redirect(receiptUrl);
      } catch (stripeError) {
        console.error('Error retrieving Stripe receipt:', stripeError);
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to retrieve receipt from Stripe',
          },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error('Error in Stripe direct download API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}
