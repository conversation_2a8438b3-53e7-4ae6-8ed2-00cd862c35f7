'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { Card, CardBody, CardHeader } from '@/components/ui';

export default function DebugAdminPage() {
  const { user, loading: authLoading } = useAuth();
  const {
    isAdmin,
    adminUser,
    isLoading: adminLoading,
    error: adminError,
  } = useAdminAccess();

  const adminEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ];

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <h1 className="iepa-heading-1 mb-8">Admin Access Debug</h1>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Authentication Status */}
            <Card>
              <CardHeader>
                <h2 className="iepa-heading-2">Authentication Status</h2>
              </CardHeader>
              <CardBody>
                <div className="space-y-3">
                  <div>
                    <strong>Auth Loading:</strong> {authLoading ? 'Yes' : 'No'}
                  </div>
                  <div>
                    <strong>User Authenticated:</strong> {user ? 'Yes' : 'No'}
                  </div>
                  {user && (
                    <>
                      <div>
                        <strong>User Email:</strong> {user.email}
                      </div>
                      <div>
                        <strong>User ID:</strong> {user.id}
                      </div>
                      <div>
                        <strong>Email Confirmed:</strong>{' '}
                        {user.email_confirmed_at ? 'Yes' : 'No'}
                      </div>
                    </>
                  )}
                </div>
              </CardBody>
            </Card>

            {/* Admin Access Status */}
            <Card>
              <CardHeader>
                <h2 className="iepa-heading-2">Admin Access Status</h2>
              </CardHeader>
              <CardBody>
                <div className="space-y-3">
                  <div>
                    <strong>Admin Loading:</strong>{' '}
                    {adminLoading ? 'Yes' : 'No'}
                  </div>
                  <div>
                    <strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}
                  </div>
                  {adminError && (
                    <div>
                      <strong>Admin Error:</strong>
                      <span className="text-red-600 ml-2">{adminError}</span>
                    </div>
                  )}
                  {adminUser && (
                    <>
                      <div>
                        <strong>Admin Role:</strong> {adminUser.role}
                      </div>
                      <div>
                        <strong>Admin Email:</strong> {adminUser.email}
                      </div>
                      <div>
                        <strong>Admin Active:</strong>{' '}
                        {adminUser.is_active ? 'Yes' : 'No'}
                      </div>
                    </>
                  )}
                </div>
              </CardBody>
            </Card>

            {/* Email Check */}
            <Card>
              <CardHeader>
                <h2 className="iepa-heading-2">Email Verification</h2>
              </CardHeader>
              <CardBody>
                <div className="space-y-3">
                  <div>
                    <strong>Current Email:</strong>{' '}
                    {user?.email || 'Not logged in'}
                  </div>
                  <div>
                    <strong>In Admin List:</strong>{' '}
                    {user?.email && adminEmails.includes(user.email)
                      ? 'Yes'
                      : 'No'}
                  </div>
                  <div>
                    <strong>Admin Email List:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {adminEmails.map(email => (
                        <li
                          key={email}
                          className={
                            user?.email === email
                              ? 'text-green-600 font-bold'
                              : ''
                          }
                        >
                          {email}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <h2 className="iepa-heading-2">Quick Actions</h2>
              </CardHeader>
              <CardBody>
                <div className="space-y-3">
                  <a
                    href="/dashboard?testAdmin=true"
                    className="block p-3 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors"
                  >
                    Access Dashboard (Test Mode)
                  </a>
                  <a
                    href="/admin?testAdmin=true"
                    className="block p-3 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors"
                  >
                    Access Admin Panel (Test Mode)
                  </a>
                  <a
                    href="/auth/login"
                    className="block p-3 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Go to Login
                  </a>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Raw Data */}
          <Card className="mt-6">
            <CardHeader>
              <h2 className="iepa-heading-2">Raw Debug Data</h2>
            </CardHeader>
            <CardBody>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                {JSON.stringify(
                  {
                    user: user
                      ? {
                          id: user.id,
                          email: user.email,
                          email_confirmed_at: user.email_confirmed_at,
                          created_at: user.created_at,
                        }
                      : null,
                    adminAccess: {
                      isAdmin,
                      adminUser,
                      adminLoading,
                      adminError,
                    },
                    authLoading,
                  },
                  null,
                  2
                )}
              </pre>
            </CardBody>
          </Card>
        </div>
      </section>
    </div>
  );
}
