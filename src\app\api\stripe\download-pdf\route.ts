// Stripe PDF Download API
// GET /api/stripe/download-pdf

import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');
    const registrationType = searchParams.get('registrationType');
    const documentType = searchParams.get('documentType'); // 'invoice' or 'receipt'

    // Validate required parameters
    if (!registrationId || !registrationType || !documentType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Registration ID, type, and document type are required',
        },
        { status: 400 }
      );
    }

    // Validate registration type
    if (!['attendee', 'speaker', 'sponsor'].includes(registrationType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid registration type',
        },
        { status: 400 }
      );
    }

    // Validate document type
    if (!['invoice', 'receipt'].includes(documentType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid document type. Must be "invoice" or "receipt"',
        },
        { status: 400 }
      );
    }

    // Get payment information from database
    const supabaseAdmin = createSupabaseAdmin();
    
    // First get the payment record
    const { data: paymentData, error: paymentError } = await supabaseAdmin
      .from('iepa_payments')
      .select('stripe_payment_intent_id, registration_id, registration_type')
      .eq('registration_id', registrationId)
      .eq('registration_type', registrationType)
      .single();

    if (paymentError || !paymentData) {
      return NextResponse.json(
        {
          success: false,
          error: 'Payment record not found',
        },
        { status: 404 }
      );
    }

    const paymentIntentId = paymentData.stripe_payment_intent_id;

    if (documentType === 'invoice') {
      // Get Stripe invoice PDF
      try {
        // First, get the payment intent to find the invoice
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

        if (!paymentIntent.invoice) {
          // If no invoice exists, try to create a detailed one
          try {
            const createResponse = await fetch(`${request.nextUrl.origin}/api/stripe/create-detailed-invoice`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                registrationId,
                registrationType,
              }),
            });

            if (createResponse.ok) {
              const createResult = await createResponse.json();
              if (createResult.success) {
                return NextResponse.json({
                  success: true,
                  documentType: 'invoice',
                  downloadUrl: createResult.pdfUrl,
                  stripeInvoiceId: createResult.invoice.id,
                  fileName: `invoice-${registrationType}-${registrationId.slice(-8)}.pdf`,
                  metadata: createResult.invoice,
                  isNewInvoice: true,
                });
              }
            }
          } catch (createError) {
            console.error('Error creating detailed invoice:', createError);
          }

          return NextResponse.json(
            {
              success: false,
              error: 'No invoice found for this payment and could not create one',
            },
            { status: 404 }
          );
        }

        // Get the invoice PDF URL
        const invoiceId = paymentIntent.invoice as string;
        const pdfUrl = `https://api.stripe.com/v1/invoices/${invoiceId}/pdf`;

        // Get invoice details for metadata
        const invoice = await stripe.invoices.retrieve(invoiceId, {
          expand: ['lines.data'],
        });

        return NextResponse.json({
          success: true,
          documentType: 'invoice',
          downloadUrl: pdfUrl,
          stripeInvoiceId: invoiceId,
          fileName: `invoice-${registrationType}-${registrationId.slice(-8)}.pdf`,
          metadata: {
            invoiceNumber: invoice.number,
            amount: invoice.amount_paid / 100,
            currency: invoice.currency,
            status: invoice.status,
            created: invoice.created,
            customerName: invoice.customer_name,
            customerEmail: invoice.customer_email,
            lineItems: invoice.lines.data.map(item => ({
              description: item.description,
              amount: item.amount / 100,
              quantity: item.quantity,
            })),
          },
        });
      } catch (stripeError) {
        console.error('Error retrieving Stripe invoice:', stripeError);
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to retrieve invoice from Stripe',
          },
          { status: 500 }
        );
      }
    } else {
      // Get Stripe receipt URL
      try {
        // Get the payment intent and its charges
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId, {
          expand: ['charges.data.receipt_url'],
        });

        const charges = (paymentIntent as any).charges;
        if (!charges?.data?.length) {
          return NextResponse.json(
            {
              success: false,
              error: 'No charges found for this payment',
            },
            { status: 404 }
          );
        }

        const charge = charges.data[0];
        const receiptUrl = charge.receipt_url;

        if (!receiptUrl) {
          return NextResponse.json(
            {
              success: false,
              error: 'No receipt available for this payment',
            },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          documentType: 'receipt',
          downloadUrl: receiptUrl,
          stripeChargeId: charge.id,
          fileName: `receipt-${registrationType}-${registrationId.slice(-8)}.pdf`,
          metadata: {
            amount: charge.amount / 100,
            currency: charge.currency,
            status: charge.status,
            created: charge.created,
          },
        });
      } catch (stripeError) {
        console.error('Error retrieving Stripe receipt:', stripeError);
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to retrieve receipt from Stripe',
          },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error('Error in Stripe PDF download API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}

// POST method for direct PDF download (proxy)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { stripeInvoiceId, stripeChargeId, documentType } = body;

    if (!documentType || (!stripeInvoiceId && !stripeChargeId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Document type and either invoice ID or charge ID required',
        },
        { status: 400 }
      );
    }

    if (documentType === 'invoice' && stripeInvoiceId) {
      // Proxy Stripe invoice PDF
      const response = await fetch(`https://api.stripe.com/v1/invoices/${stripeInvoiceId}/pdf`, {
        headers: {
          'Authorization': `Bearer ${process.env.STRIPE_SECRET_KEY}`,
          'Accept': 'application/pdf',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch invoice PDF from Stripe');
      }

      const pdfBuffer = await response.arrayBuffer();
      
      return new NextResponse(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="invoice-${stripeInvoiceId}.pdf"`,
        },
      });
    } else if (documentType === 'receipt' && stripeChargeId) {
      // For receipts, we redirect to Stripe's hosted receipt page
      // since they don't provide a direct PDF API for receipts
      const charge = await stripe.charges.retrieve(stripeChargeId);
      
      if (!charge.receipt_url) {
        return NextResponse.json(
          {
            success: false,
            error: 'No receipt URL available',
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        redirectUrl: charge.receipt_url,
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Invalid document type or missing IDs',
      },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error in Stripe PDF proxy:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve PDF',
      },
      { status: 500 }
    );
  }
}
