-- Simple Email Configuration Schema for Manual Execution
-- Execute this in Supabase SQL Editor

-- Create the email configuration table
CREATE TABLE IF NOT EXISTS iepa_email_config (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_iepa_email_config_key ON iepa_email_config(config_key);
CREATE INDEX IF NOT EXISTS idx_iepa_email_config_active ON iepa_email_config(is_active);

-- Create email configuration change log table for audit trail
CREATE TABLE IF NOT EXISTS iepa_email_config_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL,
    old_value VARCHAR(255),
    new_value VARCHAR(255) NOT NULL,
    changed_by VARCHAR(255),
    change_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for audit log
CREATE INDEX IF NOT EXISTS idx_iepa_email_config_log_key ON iepa_email_config_log(config_key);
CREATE INDEX IF NOT EXISTS idx_iepa_email_config_log_date ON iepa_email_config_log(created_at);

-- Insert default email configuration values
INSERT INTO iepa_email_config (config_key, config_value, description, is_active) VALUES
    ('sender_email', '<EMAIL>', 'Primary sender email address for all outgoing emails', true),
    ('support_email', '<EMAIL>', 'Support email address for customer inquiries and replies', true),
    ('noreply_email', '<EMAIL>', 'No-reply email address for automated notifications', true),
    ('from_name', 'IEPA Conference 2025', 'Display name shown in the "From" field of emails', true),
    ('reply_to_email', '<EMAIL>', 'Reply-to email address for email responses', true),
    ('test_bcc_email', '<EMAIL>', 'BCC email for testing purposes (development only)', true)
ON CONFLICT (config_key) DO UPDATE SET
    config_value = EXCLUDED.config_value,
    description = EXCLUDED.description,
    updated_at = NOW();
