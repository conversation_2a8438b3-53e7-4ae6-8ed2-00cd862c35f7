'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { logAuthOperation } from '@/lib/auth-debug';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

function AuthConfirmContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const handleAuthConfirmation = async () => {
      const tokenHash = searchParams?.get('token_hash');
      const type = searchParams?.get('type');
      const next = searchParams?.get('next') || '/auth/reset-password';

      logAuthOperation('authConfirm', true, null, null, {
        step: 'page_loaded',
        hasTokenHash: !!tokenHash,
        type,
        next,
        allParams: searchParams
          ? Object.fromEntries(searchParams.entries())
          : {},
      });

      if (!tokenHash || !type) {
        const errorMsg =
          'Missing token_hash or type parameter in confirmation URL';
        setError(errorMsg);
        setIsLoading(false);
        logAuthOperation('authConfirm', false, null, errorMsg, {
          step: 'missing_parameters',
        });
        return;
      }

      try {
        logAuthOperation('authConfirm', true, null, null, {
          step: 'verifying_otp',
          type,
        });

        // Verify the OTP token (only email-based types)
        const validEmailTypes = [
          'recovery',
          'signup',
          'invite',
          'magiclink',
          'email_change',
        ];
        if (!validEmailTypes.includes(type)) {
          throw new Error(`Invalid verification type: ${type}`);
        }

        const { data, error: verifyError } = await supabase.auth.verifyOtp({
          token_hash: tokenHash,
          type: type as
            | 'recovery'
            | 'signup'
            | 'invite'
            | 'magiclink'
            | 'email_change',
        });

        if (verifyError) {
          throw verifyError;
        }

        logAuthOperation('authConfirm', true, data, null, {
          step: 'otp_verified_successfully',
          hasSession: !!data.session,
          hasUser: !!data.user,
        });

        setSuccess(true);
        setIsLoading(false);

        // For password recovery, redirect to the reset password page
        if (type === 'recovery') {
          logAuthOperation('authConfirm', true, null, null, {
            step: 'redirecting_to_reset_password',
            redirectUrl: next,
          });

          // Small delay to show success message, then redirect
          setTimeout(() => {
            router.push(next);
          }, 2000);
        } else if (type === 'magiclink') {
          // For magic link authentication, redirect to the intended destination
          logAuthOperation('authConfirm', true, null, null, {
            step: 'redirecting_after_magic_link',
            redirectUrl: next,
          });

          // Small delay to show success message, then redirect
          setTimeout(() => {
            router.push(next);
          }, 2000);
        } else {
          // For other types (email confirmation, etc.), redirect to my-registrations
          setTimeout(() => {
            router.push('/my-registrations');
          }, 2000);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error occurred';
        setError(errorMessage);
        setIsLoading(false);

        logAuthOperation('authConfirm', false, null, err, {
          step: 'verification_failed',
          errorMessage,
        });
      }
    };

    handleAuthConfirmation();
  }, [searchParams, router]);

  if (isLoading) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-center">Confirming...</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="iepa-body">
                    Please wait while we confirm your authentication...
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  if (error) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-center text-red-600">
                  Confirmation Failed
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Alert className="mb-4">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>

                <div className="space-y-3">
                  <Button asChild className="w-full">
                    <Link href="/auth/forgot-password">
                      Request New Reset Link
                    </Link>
                  </Button>

                  <Button variant="outline" asChild className="w-full">
                    <Link href="/auth/login">Back to Sign In</Link>
                  </Button>
                </div>

                {/* Debug Information (Development Only) */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded text-xs">
                    <strong>Debug Info:</strong>
                    <pre className="mt-1 whitespace-pre-wrap">
                      {JSON.stringify(
                        {
                          error,
                          searchParams: searchParams
                            ? Object.fromEntries(searchParams.entries())
                            : {},
                        },
                        null,
                        2
                      )}
                    </pre>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  if (success) {
    const type = searchParams?.get('type');
    const isRecovery = type === 'recovery';

    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-center text-green-600">
                  {isRecovery
                    ? 'Password Reset Confirmed!'
                    : 'Email Confirmed!'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center space-y-4">
                  <div className="text-green-600 text-4xl mb-4">✓</div>

                  <p className="iepa-body">
                    {isRecovery
                      ? 'Your password reset has been confirmed. You will be redirected to set your new password...'
                      : 'Your email has been confirmed successfully. You will be redirected...'}
                  </p>

                  <div className="pt-4">
                    <Button asChild className="w-full">
                      <Link
                        href={
                          isRecovery ? '/auth/reset-password' : '/dashboard'
                        }
                      >
                        {isRecovery
                          ? 'Continue to Reset Password'
                          : 'Continue to Dashboard'}
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  return null;
}

export default function AuthConfirmPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AuthConfirmContent />
    </Suspense>
  );
}
