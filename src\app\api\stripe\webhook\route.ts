// Stripe Webhook Handler
// POST /api/stripe/webhook

import { NextRequest, NextResponse } from 'next/server';
import { stripe, STRIPE_CONFIG, STRIPE_WEBHOOK_EVENTS } from '@/lib/stripe';
import { createSupabaseAdmin } from '@/lib/supabase';
import { emailService } from '@/services/email';
import { autoCreateSponsorDomain } from '@/lib/sponsor-attendee-utils';
import Stripe from 'stripe';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      console.error('Missing Stripe signature');
      return NextResponse.json(
        { error: 'Missing Stripe signature' },
        { status: 400 }
      );
    }

    if (!STRIPE_CONFIG.webhookSecret) {
      console.error('Missing webhook secret');
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      );
    }

    // Verify webhook signature
    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        STRIPE_CONFIG.webhookSecret
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    console.log(`Received webhook event: ${event.type}`);

    // Handle different event types
    switch (event.type) {
      case STRIPE_WEBHOOK_EVENTS.CHECKOUT_SESSION_COMPLETED:
        await handleCheckoutSessionCompleted(
          event.data.object as Stripe.Checkout.Session
        );
        break;

      case STRIPE_WEBHOOK_EVENTS.PAYMENT_INTENT_SUCCEEDED:
        await handlePaymentIntentSucceeded(
          event.data.object as Stripe.PaymentIntent
        );
        break;

      case STRIPE_WEBHOOK_EVENTS.PAYMENT_INTENT_PAYMENT_FAILED:
        await handlePaymentIntentFailed(
          event.data.object as Stripe.PaymentIntent
        );
        break;

      case STRIPE_WEBHOOK_EVENTS.INVOICE_PAYMENT_SUCCEEDED:
        await handleInvoicePaymentSucceeded(
          event.data.object as Stripe.Invoice
        );
        break;

      case STRIPE_WEBHOOK_EVENTS.INVOICE_PAYMENT_FAILED:
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

// Handle successful checkout session completion
async function handleCheckoutSessionCompleted(
  session: Stripe.Checkout.Session
) {
  try {
    console.log('Processing checkout session completed:', session.id);

    const { registrationId, registrationType, userId } = session.metadata || {};

    if (!registrationId || !registrationType) {
      console.error('Missing metadata in checkout session:', session.id);
      return;
    }

    // Handle golf add-on payments differently
    if (registrationType === 'golf-addon') {
      await handleGolfAddOnPayment(session);
      return;
    }

    // Map registration type to payment table categories
    // The payment table only accepts 'attendee' or 'sponsor'
    const paymentRegistrationType =
      registrationType === 'sponsor' ? 'sponsor' : 'attendee';

    // Create payment record for regular registrations
    const paymentData = {
      user_id: userId || null,
      registration_id: registrationId,
      registration_type: paymentRegistrationType as 'attendee' | 'sponsor',
      stripe_payment_intent_id: session.payment_intent as string,
      amount: (session.amount_total || 0) / 100, // Convert from cents
      currency: session.currency || 'usd',
      status: 'completed',
    };

    // Handle discount code usage tracking
    const discountCode = session.metadata?.discountCode;
    if (discountCode && session.total_details?.amount_discount) {
      await trackDiscountUsage(
        discountCode,
        userId,
        registrationId,
        registrationType,
        session.amount_subtotal || 0,
        session.total_details.amount_discount,
        session.amount_total || 0,
        session.id,
        session.payment_intent as string
      );
    }

    const supabaseAdmin = createSupabaseAdmin();
    const { error: paymentError } = await supabaseAdmin
      .from('iepa_payments')
      .insert([paymentData]);

    if (paymentError) {
      console.error('Error creating payment record:', paymentError);
      return;
    }

    // Update registration status - map to correct table name
    // Map registration types to their actual table names
    let tableName: string;
    if (registrationType === 'sponsor') {
      tableName = 'iepa_sponsor_registrations';
    } else {
      // All other types (attendee, speaker, sponsor-attendee) use attendee table
      tableName = 'iepa_attendee_registrations';
    }
    const { error: updateError } = await supabaseAdmin
      .from(tableName)
      .update({
        payment_status: 'completed',
        payment_id: session.payment_intent,
        updated_at: new Date().toISOString(),
      })
      .eq('id', registrationId);

    if (updateError) {
      console.error('Error updating registration status:', updateError);
      return;
    }

    console.log(
      `Successfully processed payment for ${registrationType} registration:`,
      registrationId
    );

    // 🏢 AUTO-CREATE SPONSOR DOMAIN FOR SPONSOR REGISTRATIONS
    if (registrationType === 'sponsor') {
      await createSponsorDomainAfterPayment(registrationId);
    }

    // 📧 SEND PAYMENT CONFIRMATION EMAIL
    await sendPaymentConfirmationEmail(
      session,
      registrationType,
      registrationId,
      userId
    );

    // 📧 SEND WELCOME EMAIL WITH CONFERENCE DETAILS
    await sendWelcomeEmail(
      session,
      paymentRegistrationType,
      registrationId,
      userId
    );
  } catch (error) {
    console.error('Error handling checkout session completed:', error);
  }
}

// Handle successful payment intent
async function handlePaymentIntentSucceeded(
  paymentIntent: Stripe.PaymentIntent
) {
  try {
    console.log('Processing payment intent succeeded:', paymentIntent.id);

    const supabaseAdmin = createSupabaseAdmin();
    // Update payment record status
    const { error } = await supabaseAdmin
      .from('iepa_payments')
      .update({
        status: 'completed',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_payment_intent_id', paymentIntent.id);

    if (error) {
      console.error('Error updating payment status:', error);
    }
  } catch (error) {
    console.error('Error handling payment intent succeeded:', error);
  }
}

// Handle failed payment intent
async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Processing payment intent failed:', paymentIntent.id);

    const supabaseAdmin = createSupabaseAdmin();
    // Update payment record status
    const { error } = await supabaseAdmin
      .from('iepa_payments')
      .update({
        status: 'failed',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_payment_intent_id', paymentIntent.id);

    if (error) {
      console.error('Error updating payment status:', error);
    }
  } catch (error) {
    console.error('Error handling payment intent failed:', error);
  }
}

// Handle successful invoice payment
async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    console.log('Processing invoice payment succeeded:', invoice.id);
    // Additional invoice-specific logic can be added here
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

// Handle failed invoice payment
async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  try {
    console.log('Processing invoice payment failed:', invoice.id);
    // Additional invoice-specific logic can be added here
  } catch (error) {
    console.error('Error handling invoice payment failed:', error);
  }
}

// Handle golf add-on payment completion
async function handleGolfAddOnPayment(session: Stripe.Checkout.Session) {
  try {
    console.log('Processing golf add-on payment:', session.id);

    const {
      registrationId,
      userId,
      golfTournament,
      golfClubRental,
      golfClubHandedness,
    } = session.metadata || {};

    if (!registrationId || !userId) {
      console.error(
        'Missing required metadata for golf add-on payment:',
        session.id
      );
      return;
    }

    const supabaseAdmin = createSupabaseAdmin();
    // Get current registration to calculate new totals
    const { data: currentRegistration, error: fetchError } = await supabaseAdmin
      .from('iepa_attendee_registrations')
      .select('*')
      .eq('id', registrationId)
      .single();

    if (fetchError || !currentRegistration) {
      console.error('Error fetching current registration:', fetchError);
      return;
    }

    // Calculate golf totals
    const addingGolfTournament =
      golfTournament === 'true' && !currentRegistration.attending_golf;
    const addingClubRental = golfClubRental === 'true';

    const golfTotal = addingGolfTournament
      ? 200
      : currentRegistration.golf_total || 0;
    const golfClubRentalTotal = addingClubRental ? 70 : 0;
    const addOnAmount = (session.amount_total || 0) / 100;
    const newGrandTotal = currentRegistration.grand_total + addOnAmount;

    // Update registration with golf add-on data
    const updateData = {
      attending_golf:
        addingGolfTournament || currentRegistration.attending_golf,
      golf_club_rental:
        addingClubRental || currentRegistration.golf_club_rental,
      golf_club_handedness:
        golfClubHandedness || currentRegistration.golf_club_handedness,
      golf_total: golfTotal,
      golf_club_rental_total: golfClubRentalTotal,
      grand_total: newGrandTotal,
      updated_at: new Date().toISOString(),
    };

    const { error: updateError } = await supabaseAdmin
      .from('iepa_attendee_registrations')
      .update(updateData)
      .eq('id', registrationId);

    if (updateError) {
      console.error(
        'Error updating registration with golf add-on:',
        updateError
      );
      return;
    }

    // Create payment record for golf add-on
    const paymentData = {
      user_id: userId,
      registration_id: registrationId,
      registration_type: 'attendee' as const,
      stripe_payment_intent_id: session.payment_intent as string,
      amount: addOnAmount,
      currency: session.currency || 'usd',
      status: 'completed',
    };

    const { error: paymentError } = await supabaseAdmin
      .from('iepa_payments')
      .insert([paymentData]);

    if (paymentError) {
      console.error('Error creating golf add-on payment record:', paymentError);
      return;
    }

    console.log(
      `Successfully processed golf add-on payment for registration: ${registrationId}`
    );

    // 📧 SEND GOLF ADD-ON CONFIRMATION EMAIL
    await sendGolfAddOnConfirmationEmail(session, registrationId, userId);
  } catch (error) {
    console.error('Error handling golf add-on payment:', error);
  }
}

// 📧 EMAIL HELPER FUNCTIONS

// Send payment confirmation email after successful payment
async function sendPaymentConfirmationEmail(
  session: Stripe.Checkout.Session,
  registrationType: string,
  registrationId: string,
  userId?: string
) {
  try {
    console.log('[EMAIL-DEBUG] Sending payment confirmation email', {
      sessionId: session.id,
      registrationType,
      registrationId,
      customerEmail: session.customer_details?.email,
    });

    const customerEmail =
      session.customer_details?.email || session.customer_email;
    const customerName =
      session.customer_details?.name ||
      session.metadata?.customerName ||
      'Conference Attendee';

    if (!customerEmail) {
      console.error(
        '[EMAIL-ERROR] No customer email found in session:',
        session.id
      );
      return;
    }

    // Get registration data for more detailed email
    const supabaseAdmin = createSupabaseAdmin();
    const tableName = `iepa_${registrationType}_registrations`;
    await supabaseAdmin
      .from(tableName)
      .select('*')
      .eq('id', registrationId)
      .single();

    // Prepare payment details
    const paymentDetails = {
      amount: (session.amount_total || 0) / 100,
      paymentId: session.payment_intent as string,
      registrationType: `${registrationType.charAt(0).toUpperCase() + registrationType.slice(1)} Registration`,
      receiptUrl: undefined, // Will be added when receipt generation is implemented
    };

    // Add metadata to payment details for email logging
    const paymentDetailsWithMetadata = {
      ...paymentDetails,
      userId,
      registrationId,
    };

    // Send email
    const success = await emailService.sendPaymentConfirmation(
      customerEmail,
      customerName,
      paymentDetailsWithMetadata
    );

    if (success) {
      console.log(
        '[EMAIL-DEBUG] Payment confirmation email sent successfully',
        {
          to: customerEmail,
          registrationId,
        }
      );
    } else {
      console.error('[EMAIL-ERROR] Failed to send payment confirmation email', {
        to: customerEmail,
        registrationId,
      });
    }
  } catch (error) {
    console.error('[EMAIL-ERROR] Error sending payment confirmation email:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      sessionId: session.id,
      registrationId,
    });
  }
}

// Send golf add-on confirmation email
async function sendGolfAddOnConfirmationEmail(
  session: Stripe.Checkout.Session,
  registrationId: string,
  userId: string
) {
  try {
    console.log('[EMAIL-DEBUG] Sending golf add-on confirmation email', {
      sessionId: session.id,
      registrationId,
      userId,
    });

    const customerEmail =
      session.customer_details?.email || session.customer_email;
    const customerName =
      session.customer_details?.name || 'Conference Attendee';

    if (!customerEmail) {
      console.error(
        '[EMAIL-ERROR] No customer email found for golf add-on:',
        session.id
      );
      return;
    }

    // Use enhanced golf add-on email with IEPA branding
    const golfDetails = {
      clubRental: session.metadata?.golfClubRental === 'true',
      clubType: session.metadata?.golfClubHandedness
        ? `${session.metadata.golfClubHandedness} handed Callaway Rogue Golf Clubs`
        : 'Callaway Rogue Golf Clubs',
    };

    const success = await emailService.sendPaymentConfirmation(
      customerEmail,
      customerName,
      {
        amount: (session.amount_total || 0) / 100,
        paymentId: session.payment_intent as string,
        registrationType: 'attendee',
        userId: userId,
        registrationId: registrationId,
        isGolfAddon: true,
        golfDetails: golfDetails,
      }
    );

    if (success) {
      console.log(
        '[EMAIL-DEBUG] Golf add-on confirmation email sent successfully',
        {
          to: customerEmail,
          registrationId,
        }
      );
    }
  } catch (error) {
    console.error(
      '[EMAIL-ERROR] Error sending golf add-on confirmation email:',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        sessionId: session.id,
        registrationId,
      }
    );
  }
}

// Send welcome email with conference details
async function sendWelcomeEmail(
  session: Stripe.Checkout.Session,
  registrationType: string,
  registrationId: string,
  userId?: string
) {
  try {
    console.log('[EMAIL-DEBUG] Sending welcome email', {
      sessionId: session.id,
      registrationType,
      registrationId,
      customerEmail: session.customer_details?.email,
    });

    const customerEmail =
      session.customer_details?.email || session.customer_email;
    const customerName =
      session.customer_details?.name ||
      session.metadata?.customerName ||
      'Conference Attendee';

    if (!customerEmail) {
      console.error(
        '[EMAIL-ERROR] No customer email found for welcome email:',
        session.id
      );
      return;
    }

    // Check if welcome email has already been sent to prevent duplicates
    const supabaseAdmin = createSupabaseAdmin();
    const { data: existingEmail } = await supabaseAdmin
      .from('iepa_email_logs')
      .select('id')
      .eq('email_type', 'welcome_email')
      .eq('registration_id', registrationId)
      .eq('recipient_email', customerEmail)
      .single();

    if (existingEmail) {
      console.log(
        '[EMAIL-DEBUG] Welcome email already sent for this registration, skipping duplicate',
        {
          registrationId,
          customerEmail,
        }
      );
      return;
    }

    // Get registration data to check for lodging and golf
    const tableName = `iepa_${registrationType}_registrations`;
    const { data: registration } = await supabaseAdmin
      .from(tableName)
      .select('*')
      .eq('id', registrationId)
      .single();

    // Send welcome email with conference details
    const success = await emailService.sendWelcomeEmail(
      customerEmail,
      customerName,
      {
        type: registrationType as 'attendee' | 'speaker' | 'sponsor',
        confirmationNumber: registrationId,
        userId: userId,
        hasLodging: registration?.night_one || registration?.night_two || false,
        hasGolf: registration?.attending_golf || false,
      }
    );

    if (success) {
      console.log('[EMAIL-DEBUG] Welcome email sent successfully', {
        to: customerEmail,
        registrationId,
      });
    } else {
      console.error('[EMAIL-ERROR] Failed to send welcome email', {
        to: customerEmail,
        registrationId,
      });
    }
  } catch (error) {
    console.error('[EMAIL-ERROR] Error sending welcome email:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      sessionId: session.id,
      registrationId,
    });
  }
}

// Create sponsor domain after successful sponsor payment
async function createSponsorDomainAfterPayment(sponsorId: string) {
  try {
    console.log(
      '[SPONSOR-DOMAIN] Auto-creating sponsor domain for:',
      sponsorId
    );

    const result = await autoCreateSponsorDomain(sponsorId);

    if (result.success) {
      console.log('[SPONSOR-DOMAIN] Successfully created sponsor domain:', {
        sponsorId,
        discountCode: result.discountCode,
      });
    } else {
      console.warn('[SPONSOR-DOMAIN] Failed to create sponsor domain:', {
        sponsorId,
        error: result.error,
      });
    }
  } catch (error) {
    console.error('[SPONSOR-DOMAIN] Error creating sponsor domain:', {
      sponsorId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// Track discount code usage
async function trackDiscountUsage(
  discountCode: string,
  userId: string | null,
  registrationId: string,
  registrationType: string,
  originalAmount: number,
  discountAmount: number,
  finalAmount: number,
  sessionId: string,
  paymentIntentId: string
) {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    // Find the discount code
    const { data: discountCodeData, error: fetchError } = await supabaseAdmin
      .from('iepa_discount_codes')
      .select('id, current_uses')
      .eq('code', discountCode.toUpperCase())
      .single();

    if (fetchError || !discountCodeData) {
      console.error(
        'Discount code not found for usage tracking:',
        discountCode
      );
      return;
    }

    // Record usage
    const { error: usageError } = await supabaseAdmin
      .from('iepa_discount_usage')
      .insert([
        {
          discount_code_id: discountCodeData.id,
          user_id: userId,
          registration_id: registrationId,
          registration_type: registrationType,
          original_amount: originalAmount / 100, // Convert from cents
          discount_amount: discountAmount / 100, // Convert from cents
          final_amount: finalAmount / 100, // Convert from cents
          stripe_session_id: sessionId,
          stripe_payment_intent_id: paymentIntentId,
        },
      ]);

    if (usageError) {
      console.error('Error recording discount usage:', usageError);
      return;
    }

    // Update current usage count (simple increment)
    const { error: updateError } = await supabaseAdmin
      .from('iepa_discount_codes')
      .update({
        current_uses: (discountCodeData.current_uses || 0) + 1,
        updated_at: new Date().toISOString(),
      })
      .eq('id', discountCodeData.id);

    if (updateError) {
      console.error('Error updating discount code usage count:', updateError);
    }

    console.log('Discount usage tracked successfully:', discountCode);
  } catch (error) {
    console.error('Error tracking discount usage:', error);
  }
}
