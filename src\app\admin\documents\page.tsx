'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardBody, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FileUpload } from '@/components/ui';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { supabase } from '@/lib/supabase';
import { showSuccess, showError } from '@/utils/notifications';
import { FiDownload, FiTrash2, FiUpload, FiFile, FiEdit3, FiSave, FiX, FiEye, FiEyeOff, FiToggleLeft, FiToggleRight } from 'react-icons/fi';

interface ConferenceDocument {
  id: string;
  name: string;
  description: string;
  file_url: string;
  file_size: number;
  file_type: string;
  display_order: number;
  is_public: boolean;
  is_active: boolean;
  uploaded_at: string;
  uploaded_by: string;
}

export default function AdminDocumentsPage() {
  const { isAdmin, isLoading: adminLoading } = useAdminAccess();
  const [documents, setDocuments] = useState<ConferenceDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingDocument, setEditingDocument] = useState<Partial<ConferenceDocument>>({});
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [filterVisibility, setFilterVisibility] = useState<'all' | 'public' | 'private'>('all');
  const [newDocument, setNewDocument] = useState({
    name: '',
    description: '',
    file_url: '',
    display_order: 0,
    is_public: true,
  });

  useEffect(() => {
    document.title = 'Document Management - IEPA Admin';
  }, []);

  useEffect(() => {
    if (isAdmin) {
      loadDocuments();
    }
  }, [isAdmin]);

  const loadDocuments = async () => {
    try {
      const { data, error } = await supabase
        .from('iepa_conference_documents')
        .select('*')
        .order('display_order', { ascending: true })
        .order('created_at', { ascending: false });

      if (error) throw error;
      setDocuments(data || []);
    } catch (error) {
      console.error('Error loading documents:', error);
      showError('Error', 'Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (url: string | null) => {
    if (url) {
      setNewDocument(prev => ({ ...prev, file_url: url }));
    }
  };

  const handleUploadDocument = async () => {
    if (!newDocument.name || !newDocument.file_url) {
      showError('Validation Error', 'Please provide a name and upload a file');
      return;
    }

    setUploading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { error } = await supabase
        .from('iepa_conference_documents')
        .insert([{
          name: newDocument.name,
          description: newDocument.description,
          file_url: newDocument.file_url,
          display_order: newDocument.display_order,
          is_public: newDocument.is_public,
          uploaded_by: user.id,
        }]);

      if (error) throw error;

      showSuccess('Success', 'Document uploaded successfully');
      setNewDocument({ name: '', description: '', file_url: '', display_order: 0, is_public: true });
      loadDocuments();
    } catch (error) {
      console.error('Error uploading document:', error);
      showError('Error', 'Failed to upload document');
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteDocument = async (id: string) => {
    if (!confirm('Are you sure you want to delete this document?')) return;

    try {
      const { error } = await supabase
        .from('iepa_conference_documents')
        .delete()
        .eq('id', id);

      if (error) throw error;

      showSuccess('Success', 'Document deleted successfully');
      loadDocuments();
    } catch (error) {
      console.error('Error deleting document:', error);
      showError('Error', 'Failed to delete document');
    }
  };

  const handleDownloadDocument = async (doc: ConferenceDocument) => {
    try {
      // Create a temporary link to download the file
      const link = document.createElement('a');
      link.href = doc.file_url;
      link.download = doc.name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading document:', error);
      showError('Error', 'Failed to download document');
    }
  };

  const handleEditDocument = (doc: ConferenceDocument) => {
    setEditingId(doc.id);
    setEditingDocument({
      name: doc.name,
      description: doc.description,
      display_order: doc.display_order,
      is_public: doc.is_public,
      is_active: doc.is_active,
    });
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditingDocument({});
  };

  const handleSaveEdit = async () => {
    if (!editingId || !editingDocument.name) {
      showError('Validation Error', 'Document name is required');
      return;
    }

    try {
      const { error } = await supabase
        .from('iepa_conference_documents')
        .update({
          name: editingDocument.name,
          description: editingDocument.description || '',
          display_order: editingDocument.display_order || 0,
          is_public: editingDocument.is_public ?? true,
          is_active: editingDocument.is_active ?? true,
        })
        .eq('id', editingId);

      if (error) throw error;

      showSuccess('Success', 'Document updated successfully');
      setEditingId(null);
      setEditingDocument({});
      loadDocuments();
    } catch (error) {
      console.error('Error updating document:', error);
      showError('Error', 'Failed to update document');
    }
  };

  const handleToggleStatus = async (id: string, field: 'is_active' | 'is_public', currentValue: boolean) => {
    try {
      const { error } = await supabase
        .from('iepa_conference_documents')
        .update({ [field]: !currentValue })
        .eq('id', id);

      if (error) throw error;

      const fieldName = field === 'is_active' ? 'status' : 'visibility';
      showSuccess('Success', `Document ${fieldName} updated successfully`);
      loadDocuments();
    } catch (error) {
      console.error(`Error updating document ${field}:`, error);
      showError('Error', `Failed to update document ${field}`);
    }
  };

  const handleSelectDocument = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedDocuments(prev => [...prev, id]);
    } else {
      setSelectedDocuments(prev => prev.filter(docId => docId !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedDocuments(filteredDocuments.map(doc => doc.id));
    } else {
      setSelectedDocuments([]);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedDocuments.length === 0) return;

    if (!confirm(`Are you sure you want to delete ${selectedDocuments.length} document(s)?`)) return;

    try {
      const { error } = await supabase
        .from('iepa_conference_documents')
        .delete()
        .in('id', selectedDocuments);

      if (error) throw error;

      showSuccess('Success', `${selectedDocuments.length} document(s) deleted successfully`);
      setSelectedDocuments([]);
      loadDocuments();
    } catch (error) {
      console.error('Error deleting documents:', error);
      showError('Error', 'Failed to delete documents');
    }
  };

  const handleBulkStatusUpdate = async (field: 'is_active' | 'is_public', value: boolean) => {
    if (selectedDocuments.length === 0) return;

    try {
      const { error } = await supabase
        .from('iepa_conference_documents')
        .update({ [field]: value })
        .in('id', selectedDocuments);

      if (error) throw error;

      const fieldName = field === 'is_active' ? 'status' : 'visibility';
      const action = value ? 'activated' : 'deactivated';
      showSuccess('Success', `${selectedDocuments.length} document(s) ${fieldName} ${action} successfully`);
      setSelectedDocuments([]);
      loadDocuments();
    } catch (error) {
      console.error(`Error updating documents ${field}:`, error);
      showError('Error', `Failed to update documents ${field}`);
    }
  };

  // Filter documents based on status and visibility
  const filteredDocuments = documents.filter(doc => {
    const statusMatch = filterStatus === 'all' ||
      (filterStatus === 'active' && doc.is_active) ||
      (filterStatus === 'inactive' && !doc.is_active);

    const visibilityMatch = filterVisibility === 'all' ||
      (filterVisibility === 'public' && doc.is_public) ||
      (filterVisibility === 'private' && !doc.is_public);

    return statusMatch && visibilityMatch;
  });

  if (adminLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading admin access...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p>You do not have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Conference Documents</h1>
        <p className="text-gray-600">
          Manage conference documents like agendas, maps, and other important files.
        </p>
      </div>

      {/* Upload New Document */}
      <Card className="mb-8">
        <CardHeader>
          <h2 className="text-xl font-semibold flex items-center">
            <FiUpload className="mr-2" />
            Upload New Document
          </h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <Label htmlFor="document-name">Document Name *</Label>
              <Input
                id="document-name"
                value={newDocument.name}
                onChange={(e) => setNewDocument(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Conference Agenda 2025"
              />
            </div>
            <div>
              <Label htmlFor="document-description">Description</Label>
              <Input
                id="document-description"
                value={newDocument.description}
                onChange={(e) => setNewDocument(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Brief description of the document"
              />
            </div>
            <div>
              <Label htmlFor="display-order">Display Order</Label>
              <Input
                id="display-order"
                type="number"
                value={newDocument.display_order}
                onChange={(e) => setNewDocument(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
                placeholder="0"
                min="0"
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is-public"
                checked={newDocument.is_public}
                onChange={(e) => setNewDocument(prev => ({ ...prev, is_public: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <Label htmlFor="is-public">Make document publicly visible</Label>
            </div>
            <div className="md:col-span-2 lg:col-span-4">
              <FileUpload
                label="Document File"
                description="Upload PDF, DOC, DOCX, or image files (max 10MB)"
                bucket="iepa-conference-docs"
                folder="conference-documents"
                maxSize={10485760} // 10MB
                allowedTypes={[
                  'application/pdf',
                  'application/msword',
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                  'image/jpeg',
                  'image/png',
                  'image/gif',
                ]}
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
                onFileUpload={handleFileUpload}
                placeholder="Upload conference document"
              />
            </div>
            <div className="md:col-span-2 lg:col-span-4">
              <Button
                onClick={handleUploadDocument}
                disabled={uploading || !newDocument.name || !newDocument.file_url}
                className="w-full md:w-auto"
              >
                {uploading ? 'Uploading...' : 'Upload Document'}
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Documents List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold flex items-center">
              <FiFile className="mr-2" />
              Conference Documents ({filteredDocuments.length} of {documents.length})
            </h2>
            <div className="flex items-center space-x-4">
              {/* Filters */}
              <div className="flex items-center space-x-2">
                <Label htmlFor="status-filter" className="text-sm">Status:</Label>
                <select
                  id="status-filter"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value as 'all' | 'active' | 'inactive')}
                  className="text-sm border border-gray-300 rounded px-2 py-1"
                >
                  <option value="all">All</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div className="flex items-center space-x-2">
                <Label htmlFor="visibility-filter" className="text-sm">Visibility:</Label>
                <select
                  id="visibility-filter"
                  value={filterVisibility}
                  onChange={(e) => setFilterVisibility(e.target.value as 'all' | 'public' | 'private')}
                  className="text-sm border border-gray-300 rounded px-2 py-1"
                >
                  <option value="all">All</option>
                  <option value="public">Public</option>
                  <option value="private">Private</option>
                </select>
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedDocuments.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-800">
                  {selectedDocuments.length} document(s) selected
                </span>
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkStatusUpdate('is_active', true)}
                    className="text-green-600 hover:text-green-700"
                  >
                    Activate
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkStatusUpdate('is_active', false)}
                    className="text-orange-600 hover:text-orange-700"
                  >
                    Deactivate
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkStatusUpdate('is_public', true)}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    Make Public
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkStatusUpdate('is_public', false)}
                    className="text-gray-600 hover:text-gray-700"
                  >
                    Make Private
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleBulkDelete}
                    className="text-red-600 hover:text-red-700"
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading documents...</p>
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FiFile className="mx-auto h-12 w-12 mb-4" />
              <p>No documents uploaded yet.</p>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FiFile className="mx-auto h-12 w-12 mb-4" />
              <p>No documents match the current filters.</p>
            </div>
          ) : (
            <>
              {/* Select All Checkbox */}
              <div className="flex items-center space-x-2 p-3 border-b border-gray-200">
                <input
                  type="checkbox"
                  id="select-all"
                  checked={selectedDocuments.length === filteredDocuments.length && filteredDocuments.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="select-all" className="text-sm font-medium">
                  Select All ({filteredDocuments.length})
                </Label>
              </div>
              <div className="space-y-4">
                {filteredDocuments.map((document) => (
                <div
                  key={document.id}
                  className="border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  {editingId === document.id ? (
                    // Edit Mode
                    <div className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <div>
                          <Label htmlFor={`edit-name-${document.id}`}>Document Name *</Label>
                          <Input
                            id={`edit-name-${document.id}`}
                            value={editingDocument.name || ''}
                            onChange={(e) => setEditingDocument(prev => ({ ...prev, name: e.target.value }))}
                            placeholder="Document name"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`edit-description-${document.id}`}>Description</Label>
                          <Input
                            id={`edit-description-${document.id}`}
                            value={editingDocument.description || ''}
                            onChange={(e) => setEditingDocument(prev => ({ ...prev, description: e.target.value }))}
                            placeholder="Brief description"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`edit-order-${document.id}`}>Display Order</Label>
                          <Input
                            id={`edit-order-${document.id}`}
                            type="number"
                            value={editingDocument.display_order || 0}
                            onChange={(e) => setEditingDocument(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
                            min="0"
                          />
                        </div>
                        <div className="flex flex-col space-y-2">
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`edit-public-${document.id}`}
                              checked={editingDocument.is_public ?? true}
                              onChange={(e) => setEditingDocument(prev => ({ ...prev, is_public: e.target.checked }))}
                              className="rounded border-gray-300"
                            />
                            <Label htmlFor={`edit-public-${document.id}`}>Public</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`edit-active-${document.id}`}
                              checked={editingDocument.is_active ?? true}
                              onChange={(e) => setEditingDocument(prev => ({ ...prev, is_active: e.target.checked }))}
                              className="rounded border-gray-300"
                            />
                            <Label htmlFor={`edit-active-${document.id}`}>Active</Label>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          onClick={handleSaveEdit}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          <FiSave className="mr-1" />
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCancelEdit}
                        >
                          <FiX className="mr-1" />
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // View Mode
                    <div className="flex items-center justify-between p-4">
                      <div className="flex items-center space-x-3 flex-1">
                        <input
                          type="checkbox"
                          id={`select-${document.id}`}
                          checked={selectedDocuments.includes(document.id)}
                          onChange={(e) => handleSelectDocument(document.id, e.target.checked)}
                          className="rounded border-gray-300"
                        />
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{document.name}</h3>
                          {document.description && (
                            <p className="text-sm text-gray-600 mt-1">{document.description}</p>
                          )}
                          <div className="flex items-center space-x-4 text-xs text-gray-500 mt-2">
                            <span>Uploaded: {new Date(document.uploaded_at).toLocaleDateString()}</span>
                            <span>Order: {document.display_order}</span>
                            <span className={document.is_public ? 'text-green-600' : 'text-orange-600'}>
                              {document.is_public ? 'Public' : 'Private'}
                            </span>
                            <span className={document.is_active ? 'text-green-600' : 'text-red-600'}>
                              {document.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleStatus(document.id, 'is_public', document.is_public)}
                          title={`Make ${document.is_public ? 'private' : 'public'}`}
                        >
                          {document.is_public ? <FiEye className="mr-1" /> : <FiEyeOff className="mr-1" />}
                          {document.is_public ? 'Public' : 'Private'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleStatus(document.id, 'is_active', document.is_active)}
                          title={`Make ${document.is_active ? 'inactive' : 'active'}`}
                        >
                          {document.is_active ? <FiToggleRight className="mr-1" /> : <FiToggleLeft className="mr-1" />}
                          {document.is_active ? 'Active' : 'Inactive'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownloadDocument(document)}
                        >
                          <FiDownload className="mr-1" />
                          Download
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditDocument(document)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <FiEdit3 className="mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteDocument(document.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <FiTrash2 className="mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
                ))}
              </div>
            </>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
