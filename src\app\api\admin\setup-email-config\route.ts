// API Route: Setup Email Configuration Database
// Creates the email configuration tables and inserts default values

import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST() {
  try {
    console.log(
      '[EMAIL-CONFIG-SETUP] Starting email configuration database setup...'
    );

    // Check if table already exists
    const { error: checkError } = await supabase
      .from('iepa_email_config')
      .select('id')
      .limit(1);

    if (checkError && checkError.code === '42P01') {
      console.log(
        '[EMAIL-CONFIG-SETUP] Table does not exist. Please create it manually.'
      );

      // Read the simple SQL file for manual execution
      const simpleSqlPath = join(
        process.cwd(),
        'src/lib/database/email-config-simple.sql'
      );
      const simpleSql = readFileSync(simpleSqlPath, 'utf8');

      return NextResponse.json(
        {
          success: false,
          message:
            'Email configuration table does not exist. Please create it manually using the SQL schema.',
          instructions: [
            '1. Open Supabase Dashboard → SQL Editor',
            '2. Copy and execute the SQL provided below',
            '3. Run this setup API again to verify the setup',
          ],
          sql: simpleSql,
        },
        { status: 400 }
      );
    }

    // Insert default configuration values
    const defaultConfigs = [
      {
        config_key: 'sender_email',
        config_value: '<EMAIL>',
        description: 'Primary sender email address for all outgoing emails',
        is_active: true,
      },
      {
        config_key: 'support_email',
        config_value: '<EMAIL>',
        description: 'Support email address for customer inquiries and replies',
        is_active: true,
      },
      {
        config_key: 'noreply_email',
        config_value: '<EMAIL>',
        description: 'No-reply email address for automated notifications',
        is_active: true,
      },
      {
        config_key: 'from_name',
        config_value: 'IEPA Conference 2025',
        description: 'Display name shown in the "From" field of emails',
        is_active: true,
      },
      {
        config_key: 'reply_to_email',
        config_value: '<EMAIL>',
        description: 'Reply-to email address for email responses',
        is_active: true,
      },
      {
        config_key: 'test_bcc_email',
        config_value: '<EMAIL>',
        description: 'BCC email for testing purposes (development only)',
        is_active: true,
      },
    ];

    // Insert or update default configurations
    for (const config of defaultConfigs) {
      const { error: upsertError } = await supabase
        .from('iepa_email_config')
        .upsert(config, {
          onConflict: 'config_key',
        });

      if (upsertError) {
        console.error(
          `[EMAIL-CONFIG-SETUP] Failed to upsert ${config.config_key}:`,
          upsertError
        );
      } else {
        console.log(
          `[EMAIL-CONFIG-SETUP] ✅ Configured ${config.config_key}: ${config.config_value}`
        );
      }
    }

    // Verify the setup by fetching all configs
    const { data: verifyData, error: verifyError } = await supabase
      .from('iepa_email_config')
      .select('config_key, config_value, is_active')
      .eq('is_active', true);

    if (verifyError) {
      throw new Error(`Verification failed: ${verifyError.message}`);
    }

    console.log(
      '[EMAIL-CONFIG-SETUP] ✅ Email configuration setup completed successfully'
    );

    return NextResponse.json({
      success: true,
      message: 'Email configuration database setup completed successfully',
      configurations: verifyData,
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    console.error('[EMAIL-CONFIG-SETUP] Setup failed:', error);

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Check if email configuration is already set up
    const { data, error } = await supabase
      .from('iepa_email_config')
      .select('config_key, config_value, is_active, created_at')
      .eq('is_active', true);

    if (error) {
      if (error.code === '42P01') {
        return NextResponse.json({
          setup: false,
          message: 'Email configuration table does not exist',
          configurations: [],
        });
      }
      throw error;
    }

    return NextResponse.json({
      setup: true,
      message: 'Email configuration is set up and active',
      configurations: data,
      count: data.length,
    });
  } catch (error: unknown) {
    console.error('[EMAIL-CONFIG-SETUP] Status check failed:', error);

    return NextResponse.json(
      {
        setup: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
        configurations: [],
      },
      { status: 500 }
    );
  }
}
