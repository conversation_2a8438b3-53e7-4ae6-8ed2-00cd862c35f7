# Test Data Auto-Fill Feature

## Overview

The IEPA registration system now includes an automatic test data filling feature that can populate all registration forms with realistic test data using URL query parameters. This feature is designed to speed up testing and development workflows.

## How to Use

### URL Query Parameters

Add any of the following query parameters to registration URLs to automatically fill forms with test data:

- `?test=true`
- `?autofill=true` 
- `?fill=true`

### Supported Registration Forms

The auto-fill feature works on all four registration types:

1. **Attendee Registration**: `/register/attendee?test=true`
2. **Speaker Registration**: `/register/speaker?test=true`
3. **Sponsor Registration**: `/register/sponsor?test=true`
4. **Sponsor-Attendee Registration**: `/register/sponsor-attendee?test=true`

## Test Data Templates

### Attendee Registration
- **Name**: <PERSON>
- **Email**: <EMAIL>
- **Organization**: Clean Energy Solutions
- **Job Title**: Senior Energy Analyst
- **Registration Type**: IEPA Member
- **Golf**: Tournament + Club Rental (Right-handed)
- **Lodging**: Both nights selected
- **Meals**: All meals selected
- **Emergency Contact**: <PERSON>

### Speaker Registration
- **Name**: Dr. <PERSON>
- **Email**: <EMAIL>
- **Organization**: Renewable Tech Innovations
- **Job Title**: Director of Research
- **Speaker Type**: Full Meeting Speaker
- **Bio**: Professional bio with renewable energy expertise
- **Golf**: Tournament + Club Rental (Left-handed)
- **Lodging**: Both nights selected
- **Meals**: Most meals selected

### Sponsor Registration
- **Organization**: Green Energy Corp
- **Website**: https://www.greenenergycorp.com
- **Contact**: Michael Davis, Marketing Director
- **Email**: <EMAIL>
- **Sponsorship Level**: Gold Level ($15,000)
- **Billing Address**: Los Angeles, CA
- **Description**: Sustainable energy solutions provider

### Sponsor-Attendee Registration
- **Name**: Lisa Chen
- **Email**: <EMAIL>
- **Organization**: Green Energy Corp
- **Job Title**: VP of Business Development
- **Registration Type**: Sponsor Attendee (Complimentary)
- **Lodging**: Both nights selected
- **Meals**: All meals selected
- **Emergency Contact**: David Chen (Spouse)

## Technical Implementation

### Hook: `useTestDataFill`

The feature is implemented using a custom React hook located at `src/hooks/useTestDataFill.ts`.

#### Key Features:
- **Debounced execution**: Prevents multiple fills from occurring
- **Query parameter detection**: Automatically detects test parameters
- **Form-specific templates**: Different data for each registration type
- **Notification system**: Shows success/error messages
- **Window function exposure**: Allows manual triggering via browser console

#### Usage in Components:
```typescript
const { fillTestData, isTestMode } = useTestDataFill(
  'attendee', // or 'speaker', 'sponsor', 'sponsor-attendee'
  setFormData,
  {
    onTestDataFilled: (summary) => showInfo('Test Data Loaded', summary),
    onError: (error) => showError('Test Data Error', error),
    enabled: true,
  }
);
```

### Manual Triggering

You can also manually trigger test data filling from the browser console:

- `fillTestDataAttendee()` - Fill attendee form
- `fillTestDataSpeaker()` - Fill speaker form  
- `fillTestDataSponsor()` - Fill sponsor form
- `fillTestDataSponsorAttendee()` - Fill sponsor-attendee form

## Benefits for Testing

### Development Workflow
- **Faster form testing**: No need to manually enter data repeatedly
- **Consistent test data**: Same realistic data every time
- **Complete form coverage**: All required and optional fields populated
- **Edge case testing**: Includes various options like golf, meals, lodging

### Quality Assurance
- **End-to-end testing**: Forms are fully populated for complete workflow testing
- **Payment flow testing**: Can proceed directly to payment/submission steps
- **Validation testing**: All fields have valid data for testing validation rules
- **Integration testing**: Realistic data for testing with external services

### Playwright Testing
The feature integrates seamlessly with Playwright tests:

```javascript
// Navigate with auto-fill enabled
await page.goto('http://localhost:6969/register/attendee?test=true');

// Form will be automatically populated
// Continue with test assertions and interactions
```

## Data Quality

All test data is:
- **Realistic**: Uses professional names, organizations, and contact information
- **Valid**: Passes all form validation rules
- **Consistent**: Related fields match (e.g., organization domains in emails)
- **Complete**: Includes all required fields and many optional ones
- **Diverse**: Different data for each registration type to test various scenarios

## Security Considerations

- **Development only**: This feature should only be used in development/testing environments
- **No production data**: All test data is fictional and safe for testing
- **Query parameter based**: Easy to enable/disable via URL parameters
- **No persistent storage**: Test data doesn't interfere with real user data

## Future Enhancements

Potential improvements for the test data auto-fill feature:

1. **Multiple data sets**: Different test data templates for various scenarios
2. **Partial filling**: Options to fill only specific sections
3. **Custom data**: URL parameters to override specific field values
4. **Random generation**: Generate random but valid test data
5. **Export/Import**: Save and load custom test data sets
