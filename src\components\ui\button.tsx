import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          'bg-[var(--iepa-primary-blue)] text-white shadow-xs hover:bg-[var(--iepa-primary-blue-dark)] focus-visible:ring-[var(--iepa-primary-blue)]/20',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'border border-[var(--iepa-primary-blue)] bg-background text-[var(--iepa-primary-blue)] shadow-xs hover:bg-[var(--iepa-primary-blue)] hover:text-white',
        bordered:
          'border border-[var(--iepa-primary-blue)] bg-background text-[var(--iepa-primary-blue)] shadow-xs hover:bg-[var(--iepa-primary-blue)] hover:text-white',
        secondary:
          'bg-[var(--iepa-secondary-green)] text-white shadow-xs hover:bg-[var(--iepa-secondary-green-dark)] focus-visible:ring-[var(--iepa-secondary-green)]/20',
        ghost:
          'hover:bg-[var(--iepa-gray-100)] hover:text-[var(--iepa-primary-blue)]',
        light:
          'bg-[var(--iepa-gray-100)] text-[var(--iepa-primary-blue)] shadow-xs hover:bg-[var(--iepa-gray-200)]',
        flat: 'bg-[var(--iepa-gray-50)] text-[var(--iepa-primary-blue)] shadow-none hover:bg-[var(--iepa-gray-100)]',
        solid:
          'bg-[var(--iepa-primary-blue)] text-white shadow-xs hover:bg-[var(--iepa-primary-blue-dark)] focus-visible:ring-[var(--iepa-primary-blue)]/20',
        link: 'text-[var(--iepa-primary-blue)] underline-offset-4 hover:underline hover:text-[var(--iepa-primary-blue-dark)]',
      },
      size: {
        default: 'h-11 px-4 py-2 has-[>svg]:px-3 min-h-[44px]', // Accessibility: 44px minimum touch target
        sm: 'h-10 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5 min-h-[44px]',
        md: 'h-11 px-4 py-2 has-[>svg]:px-3 min-h-[44px]', // HeroUI compatibility
        lg: 'h-12 rounded-md px-6 has-[>svg]:px-4 min-h-[44px]',
        icon: 'size-11 min-h-[44px] min-w-[44px]', // Accessibility: 44px minimum touch target
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

interface ButtonProps
  extends React.ComponentProps<'button'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  as?: React.ElementType;
  color?:
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'default';
}

// Polymorphic button props that allow href when using 'as' prop
interface PolymorphicButtonProps extends Omit<ButtonProps, 'as'> {
  as?: React.ElementType;
  href?: string;
  [key: string]: unknown;
}

function Button({
  className,
  variant,
  size,
  asChild = false,
  as,
  color,
  ...props
}: PolymorphicButtonProps) {
  // Map HeroUI color prop to variant for backward compatibility
  const colorToVariantMap = {
    primary: 'default',
    secondary: 'secondary',
    success: 'secondary',
    warning: 'outline',
    danger: 'destructive',
    default: 'outline',
  } as const;

  const mappedVariant = color
    ? colorToVariantMap[color as keyof typeof colorToVariantMap] || variant
    : variant;

  const Comp = asChild ? Slot : as || 'button';

  return (
    <Comp
      data-slot="button"
      className={cn(
        buttonVariants({ variant: mappedVariant, size, className })
      )}
      {...props}
    />
  );
}

export { Button, buttonVariants };
