'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Input,
  Checkbox,
} from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { CONFERENCE_YEAR } from '@/lib/conference-config';

export default function SignUpPage() {
  const { signUp, user } = useAuth();
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    organization: '',
    agreeToTerms: false,
    subscribeToUpdates: true,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      // Check if user came from a specific page and redirect back there
      const urlParams = new URLSearchParams(window.location.search);
      const returnTo = urlParams.get('returnTo') || '/my-registrations';
      console.log(
        '🔄 Signup - User already logged in, redirecting to:',
        returnTo
      );
      router.push(returnTo);
    }
  }, [user, router]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(''); // Clear error when user starts typing
  };

  const validateForm = () => {
    if (
      !formData.email ||
      !formData.password ||
      !formData.firstName ||
      !formData.lastName
    ) {
      setError('Please fill in all required fields');
      return false;
    }

    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    if (!formData.agreeToTerms) {
      setError('You must agree to the terms and conditions');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    if (!validateForm()) {
      setIsLoading(false);
      return;
    }

    try {
      await signUp(formData.email, formData.password);
      setSuccess(true);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'An error occurred during sign up'
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto text-center">
            <div className="iepa-status-success mb-6">
              <h2 className="iepa-heading-2 mb-2">
                Account Created Successfully!
              </h2>
              <p className="iepa-body">
                Please check your email for a verification link to complete your
                account setup.
              </p>
            </div>

            <div className="space-y-4">
              <Button
                as={Link}
                href={`/auth/login${typeof window !== 'undefined' && window.location.search ? window.location.search : ''}`}
                color="primary"
                size="lg"
                className="w-full"
              >
                Go to Sign In
              </Button>

              <Button
                as={Link}
                href="/"
                variant="bordered"
                size="lg"
                className="w-full"
              >
                Return to Homepage
              </Button>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="iepa-heading-1 mb-4">Create Account</h1>
            <p className="iepa-body">
              Create your account to register for the IEPA {CONFERENCE_YEAR}{' '}
              Annual Conference
            </p>
          </div>

          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 text-center">
                Join IEPA {CONFERENCE_YEAR}
              </h2>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="iepa-status-error">
                    <p className="iepa-body-small">{error}</p>
                  </div>
                )}

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      label="First Name *"
                      placeholder="First name"
                      value={formData.firstName}
                      onChange={e =>
                        handleInputChange('firstName', e.target.value)
                      }
                      isRequired
                      autoComplete="given-name"
                    />

                    <Input
                      label="Last Name *"
                      placeholder="Last name"
                      value={formData.lastName}
                      onChange={e =>
                        handleInputChange('lastName', e.target.value)
                      }
                      isRequired
                      autoComplete="family-name"
                    />
                  </div>

                  <Input
                    label="Email Address *"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={e => handleInputChange('email', e.target.value)}
                    isRequired
                    autoComplete="email"
                    description="This will be your login email"
                  />

                  <Input
                    label="Organization"
                    placeholder="Your organization (optional)"
                    value={formData.organization}
                    onChange={e =>
                      handleInputChange('organization', e.target.value)
                    }
                    autoComplete="organization"
                  />

                  <Input
                    label="Password *"
                    type="password"
                    placeholder="Create a password"
                    value={formData.password}
                    onChange={e =>
                      handleInputChange('password', e.target.value)
                    }
                    isRequired
                    autoComplete="new-password"
                    description="Must be at least 6 characters"
                  />

                  <Input
                    label="Confirm Password *"
                    type="password"
                    placeholder="Confirm your password"
                    value={formData.confirmPassword}
                    onChange={e =>
                      handleInputChange('confirmPassword', e.target.value)
                    }
                    isRequired
                    autoComplete="new-password"
                  />
                </div>

                <div className="space-y-3">
                  <Checkbox
                    id="agree-to-terms"
                    checked={formData.agreeToTerms}
                    onCheckedChange={checked =>
                      handleInputChange('agreeToTerms', checked === true)
                    }
                  >
                    <span className="iepa-body-small">
                      I agree to the{' '}
                      <Link
                        href="/terms"
                        className="hover:underline"
                        style={{ color: 'var(--iepa-primary-blue)' }}
                      >
                        Terms and Conditions
                      </Link>{' '}
                      and{' '}
                      <Link
                        href="/privacy"
                        className="hover:underline"
                        style={{ color: 'var(--iepa-primary-blue)' }}
                      >
                        Privacy Policy
                      </Link>
                    </span>
                  </Checkbox>

                  <Checkbox
                    id="subscribe-to-updates"
                    checked={formData.subscribeToUpdates}
                    onCheckedChange={checked =>
                      handleInputChange('subscribeToUpdates', checked === true)
                    }
                  >
                    <span className="iepa-body-small">
                      Subscribe to IEPA conference updates and announcements
                    </span>
                  </Checkbox>
                </div>

                <Button
                  type="submit"
                  color="secondary"
                  size="lg"
                  className="w-full"
                  disabled={
                    isLoading ||
                    !formData.email ||
                    !formData.password ||
                    !formData.agreeToTerms
                  }
                >
                  {isLoading ? 'Creating Account...' : 'Create Account'}
                </Button>
              </form>
            </CardBody>
          </Card>

          <div className="text-center mt-6">
            <p className="iepa-body-small">
              Already have an account?{' '}
              <Link
                href={`/auth/login${typeof window !== 'undefined' && window.location.search ? window.location.search : ''}`}
                className="font-semibold hover:underline"
                style={{ color: 'var(--iepa-primary-blue)' }}
              >
                Sign In
              </Link>
            </p>
          </div>

          <div
            className="mt-8 p-4 rounded-lg"
            style={{ backgroundColor: 'var(--iepa-gray-50)' }}
          >
            <h3 className="iepa-heading-3 mb-2">Why Create an Account?</h3>
            <ul className="iepa-body-small space-y-1">
              <li>
                • Secure registration for the IEPA {CONFERENCE_YEAR} conference
              </li>
              <li>• Save your registration progress</li>
              <li>• Access your registration history</li>
              <li>• Receive important conference updates</li>
              <li>• Manage your conference preferences</li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  );
}
