import { NextRequest, NextResponse } from 'next/server';
import { PRICING_CONSTANTS, pricingUtils } from '@/lib/pricing-config';
import { CONFERENCE_DATES, CONFERENCE_EVENTS, formatConferenceDateRange, formatEventTime } from '@/lib/conference-config';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { emailType, sampleData } = body;

    console.log(`[EMAIL-PREVIEW-API] Generating preview for email type: ${emailType}`);

    // Default sample data
    const defaultSampleData = {
      name: '<PERSON>',
      email: '<EMAIL>',
      type: 'attendee',
      confirmationNumber: 'CONF-12345',
      hasLodging: true,
      hasGolf: true,
      userId: 'user-123',
      speakerPricingType: 'comped-speaker',
      sponsorshipLevel: 'bronze-sponsor',
      ...sampleData
    };

    let preview;
    let dynamicVariables = {};

    switch (emailType) {
      case 'welcome':
        preview = await generateWelcomeEmailPreview(defaultSampleData);
        dynamicVariables = getWelcomeEmailVariables();
        break;
      
      case 'registration_confirmation':
        preview = await generateRegistrationConfirmationPreview(defaultSampleData);
        dynamicVariables = getRegistrationConfirmationVariables();
        break;
      
      case 'payment_confirmation':
        preview = await generatePaymentConfirmationPreview(defaultSampleData);
        dynamicVariables = getPaymentConfirmationVariables();
        break;
      
      default:
        return NextResponse.json({
          success: false,
          error: `Unknown email type: ${emailType}`
        }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      preview: {
        ...preview,
        variables: dynamicVariables
      },
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('[EMAIL-PREVIEW-API] Failed to generate preview:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate email preview',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

async function generateWelcomeEmailPreview(sampleData: Record<string, unknown>) {
  // Get dynamic configuration values (same as in sendWelcomeEmail)
  const conferenceDateRange = formatConferenceDateRange(CONFERENCE_DATES.startDate, CONFERENCE_DATES.endDate);
  const golfEvent = CONFERENCE_EVENTS.find(event => event.id === 'golf-tournament');
  const golfDate = CONFERENCE_DATES.golfTournament.displayDate;
  const golfTime = golfEvent ? formatEventTime(golfEvent.startTime) : '11:00 AM';
  
  const subject = `Welcome to IEPA's 2025 Annual Meeting - Important Conference Information`;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
      <!-- IEPA Header -->
      <div style="background: #3A6CA5; padding: 20px; text-align: center;">
        <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Welcome to IEPA's 2025 Annual Meeting!</h1>
      </div>

      <hr style="color: #3A6CA5; border: 2px solid #3A6CA5; margin: 0;" />

      <div style="padding: 30px;">
        <p><strong>Dear ${sampleData.name},</strong></p>

        <p>Thank you for registering for IEPA's 2025 Annual Meeting to be held <strong>${conferenceDateRange}</strong>. We are putting together another outstanding program and look forward to your participation.</p>

        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="margin: 0;"><strong>📋 In preparation of your stay, please review the information below and the important documents that include directions and maps to Stanford Sierra Conference Center at Fallen Leaf Lake.</strong></p>
        </div>

        <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="color: #3A6CA5; margin-top: 0;">📍 Stanford Sierra Conference Center</h3>
          <p><strong>Physical address:</strong> 130 Fallen Leaf Road, Fallen Leaf, CA 96151</p>
          <p><strong>Mailing address:</strong> PO Box 10618, South Lake Tahoe, CA 96158-1959</p>
          <p><strong>Phone number:</strong> (*************</p>
          <p><strong>Fax number:</strong> (*************</p>
          <p><strong>Website:</strong> <a href="http://stanfordsierra.com/" style="color: #3A6CA5;">stanfordsierra.com</a></p>
          <p><strong>Closest Airport:</strong> Reno-Tahoe International Airport</p>
        </div>

        <h3 style="color: #3A6CA5;">🏞️ About Stanford Sierra Conference Center</h3>
        <p>Stanford Sierra Conference Center (SSCC) is located on twenty acres of lakefront property on the south shore of beautiful Fallen Leaf Lake. It was established in 1907 as a summer family camp by William Wrightman Price, a Stanford professor and nature enthusiast. The Old Lodge, built in 1932, with hardwood floors, vaulted ceiling and stone fireplace is now a favorite spot for visiting groups to hold social hours.</p>

        <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="color: #155724; margin-top: 0;">👔 Dress Code</h3>
          <p style="color: #155724; margin: 0;"><strong><em>Please dress casually</em></strong>, whether you are a speaker or a participant. This location is rustic with dusty, wooded paths and comfortable clothing and sturdy shoes are a must. Don't forget to bring a light jacket in case the weather is cool. And a flashlight is necessary for walking to your cabin after dark as there are no lights along the paths.</p>
        </div>

        ${
          sampleData.hasLodging
            ? `
          <div style="background: #f8f9fa; border-left: 4px solid #3A6CA5; padding: 20px; margin: 25px 0;">
            <h3 style="color: #3A6CA5; margin-top: 0;">🏨 On-Site Lodging Information</h3>
            <p><strong>Important:</strong> Stanford Sierra Conference Center is NOT a hotel. Lodging is available for the nights of <strong>${CONFERENCE_DATES.startDate.dayOfWeek}, ${CONFERENCE_DATES.startDate.displayDate} and ${CONFERENCE_DATES.endDate.dayOfWeek === 'Wednesday' ? 'Tuesday' : CONFERENCE_DATES.endDate.dayOfWeek}, ${new Date(new Date(CONFERENCE_DATES.endDate.date).getTime() - 24*60*60*1000).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })} ONLY</strong>.</p>
            <p><strong>Check-in:</strong> Begins at 3 p.m. on ${CONFERENCE_DATES.startDate.dayOfWeek}, ${CONFERENCE_DATES.startDate.displayDate}</p>
            <p><strong>Check-out:</strong> No later than 10 a.m. on departure day</p>
            <p><strong>Departure:</strong> Our group must depart by 1:00 p.m. on ${CONFERENCE_DATES.endDate.dayOfWeek}, ${CONFERENCE_DATES.endDate.displayDate}</p>
            <p>Your cabin assignment and key will be provided upon check-in. If you have a favorite cabin from previous years, please email your request to <a href="mailto:<EMAIL>" style="color: #3A6CA5;"><EMAIL></a>.</p>
          </div>
        `
            : ''
        }

        ${
          sampleData.hasGolf
            ? `
          <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #155724; margin-top: 0;">⛳ Golf Tournament</h3>
            <p style="color: #155724;"><strong>Date:</strong> ${golfDate}</p>
            <p style="color: #155724;"><strong>Tee-off:</strong> Beginning at ${golfTime}</p>
            <p style="color: #155724;"><strong>Location:</strong> South Lake Tahoe Golf Course, 2500 Emerald Bay Road, South Lake Tahoe</p>
            <p style="color: #155724;"><strong>Phone:</strong> (*************</p>
            <p style="color: #155724;">A few days prior to the tournament, registered golfers will receive an email containing the list of foursomes and game rules.</p>
          </div>
        `
            : ''
        }

        <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="color: #3A6CA5; margin-top: 0;">🚗 Transportation & Parking</h3>
          <p><strong>Parking:</strong> Very limited at Stanford Sierra Camp. Attendees with lodging will receive a parking pass at registration.</p>
          <p><strong>Shuttle Service:</strong> Available from Reno-Tahoe International Airport. Contact SSCC at (************* to schedule (at least one week prior).</p>
          <p><strong>Shuttle Info:</strong> <a href="https://stanfordsierra.com/shuttle-info/" style="color: #3A6CA5;">stanfordsierra.com/shuttle-info</a></p>
        </div>

        <div style="background: #f8f9fa; border-left: 4px solid #3A6CA5; padding: 20px; margin: 25px 0;">
          <h3 style="color: #3A6CA5; margin-top: 0;">📅 Schedule Overview</h3>
          <p><strong>Registration Packets:</strong> Available in the main lodge lobby</p>
          <ul>
            <li>${CONFERENCE_DATES.startDate.dayOfWeek}, ${CONFERENCE_DATES.startDate.displayDate}: 3:00 - 6:00 PM</li>
            <li>${new Date(new Date(CONFERENCE_DATES.startDate.date).getTime() + 24*60*60*1000).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}: 8:00 - 9:00 AM</li>
          </ul>
          <p><strong>${CONFERENCE_DATES.startDate.dayOfWeek} Reception and Dinner:</strong> Reception at 6:00 PM, followed by dinner at 7:00 PM</p>
          <p><strong>Special dietary needs</strong> should be sent to <a href="mailto:<EMAIL>" style="color: #3A6CA5;"><EMAIL></a></p>
        </div>

        <div style="background: #e8f4f8; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="color: #3A6CA5; margin-top: 0;">💻 Business Services</h3>
          <p><strong>Internet:</strong> Free high-speed wireless access available in the main lodge and all cabins</p>
          <p><strong>Cell Phone:</strong> Reception is inconsistent. Three pay phones available in the main lodge</p>
          <p><strong>Business Center:</strong> Four computers available for guest use</p>
          <p><strong>Fax and Copy:</strong> Services available in the main office</p>
        </div>

        <p>The draft agenda is available on our website at <a href="http://www.iepa.com/" style="color: #3A6CA5;">www.iepa.com</a> under Annual Meeting. Agenda updates will be posted as they become available.</p>

        <p>If you have any questions, please don't hesitate to contact me at ************ or <a href="mailto:<EMAIL>" style="color: #3A6CA5;"><EMAIL></a>. We look forward to seeing you at IEPA's 2025 Annual Meeting.</p>

        <p style="margin-top: 30px;">Sincerely,<br>
        <strong>Jamie Parker</strong><br>
        <strong>Administrator/Annual Meeting Coordinator</strong></p>

        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="margin: 0;"><strong>📎 Important Documents:</strong> Please review the attached documents prior to your departure (available on our website):</p>
          <ul style="margin: 10px 0;">
            <li>IEPA Annual Meeting Draft Agenda</li>
            <li>SSCC Rules and Emergency Info</li>
            <li>Stanford Sierra Main Lodge Facilities</li>
            <li>Map of Stanford Cabins</li>
            <li>Driving Directions to SSCC</li>
            <li>Drive Map</li>
          </ul>
        </div>
      </div>

      <!-- Footer -->
      <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
        <p style="margin: 0; color: #6c757d; font-size: 14px;">
          Independent Energy Producers Association<br>
          California's oldest nonprofit trade association representing independent energy facilities
        </p>
      </div>
    </div>
  `;

  return {
    subject,
    html,
    text: `Welcome to IEPA's 2025 Annual Meeting!\n\nDear ${sampleData.name},\n\nThank you for registering for IEPA's 2025 Annual Meeting to be held ${conferenceDateRange}...`
  };
}

function getWelcomeEmailVariables() {
  const conferenceDateRange = formatConferenceDateRange(CONFERENCE_DATES.startDate, CONFERENCE_DATES.endDate);
  const golfEvent = CONFERENCE_EVENTS.find(event => event.id === 'golf-tournament');
  const golfTime = golfEvent ? formatEventTime(golfEvent.startTime) : '11:00 AM';
  
  return {
    'Conference Date Range': conferenceDateRange,
    'Golf Tournament Fee': pricingUtils.formatPrice(PRICING_CONSTANTS.GOLF_TOURNAMENT_FEE),
    'Golf Tournament Date': CONFERENCE_DATES.golfTournament.displayDate,
    'Golf Tournament Time': golfTime,
    'Conference Start Date': CONFERENCE_DATES.startDate.displayDate,
    'Conference End Date': CONFERENCE_DATES.endDate.displayDate,
    'Start Day': CONFERENCE_DATES.startDate.dayOfWeek,
    'End Day': CONFERENCE_DATES.endDate.dayOfWeek
  };
}

async function generateRegistrationConfirmationPreview(sampleData: Record<string, unknown>) {
  // This would use the existing registration confirmation logic
  // For now, return a simple preview
  return {
    subject: `Welcome to IEPA's 2025 Annual Meeting - Registration Confirmed!`,
    html: `<p>Registration confirmation preview for ${sampleData.name}</p>`,
    text: `Registration confirmation preview for ${sampleData.name}`
  };
}

function getRegistrationConfirmationVariables() {
  return {
    'Registration Type': 'Dynamic based on user selection',
    'Confirmation Number': 'Auto-generated unique ID'
  };
}

async function generatePaymentConfirmationPreview(sampleData: Record<string, unknown>) {
  // This would use the existing payment confirmation logic
  return {
    subject: `Payment Confirmation - IEPA 2025`,
    html: `<p>Payment confirmation preview for ${sampleData.name}</p>`,
    text: `Payment confirmation preview for ${sampleData.name}`
  };
}

function getPaymentConfirmationVariables() {
  return {
    'Payment Amount': 'Dynamic based on registration selections',
    'Payment Date': 'Current date/time'
  };
}
