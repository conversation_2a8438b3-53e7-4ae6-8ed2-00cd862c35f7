// Accessibility utilities for IEPA 2025 Conference Registration
// Implements WCAG 2.2 AA standards

/**
 * Generates a unique ID for form elements to ensure proper labeling
 */
export const generateId = (prefix: string = 'iepa'): string => {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Announces content to screen readers
 */
export const announceToScreenReader = (
  message: string,
  priority: 'polite' | 'assertive' = 'polite'
): void => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'iepa-sr-only';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

/**
 * Validates color contrast ratio (WCAG 2.2 AA requires 4.5:1 for normal text)
 */
export const getContrastRatio = (color1: string, color2: string): number => {
  const getLuminance = (color: string): number => {
    // Convert hex to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Calculate relative luminance
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * Checks if contrast ratio meets WCAG 2.2 AA standards
 */
export const meetsContrastRequirement = (
  color1: string,
  color2: string,
  isLargeText: boolean = false
): boolean => {
  const ratio = getContrastRatio(color1, color2);
  return isLargeText ? ratio >= 3 : ratio >= 4.5;
};

/**
 * Focus management utilities
 */
export const focusManagement = {
  /**
   * Trap focus within a container (for modals, dropdowns)
   */
  trapFocus: (container: HTMLElement): (() => void) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  },

  /**
   * Save and restore focus
   */
  saveFocus: (): (() => void) => {
    const activeElement = document.activeElement as HTMLElement;
    return () => {
      if (activeElement && activeElement.focus) {
        activeElement.focus();
      }
    };
  },

  /**
   * Focus first error in a form
   */
  focusFirstError: (container: HTMLElement): void => {
    const errorElement = container.querySelector(
      '[aria-invalid="true"], .error'
    ) as HTMLElement;
    if (errorElement) {
      errorElement.focus();
      errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  },
};

/**
 * Keyboard navigation utilities
 */
export const keyboardNavigation = {
  /**
   * Handle arrow key navigation for radio groups and select lists
   */
  handleArrowKeys: (
    event: KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    onChange: (index: number) => void
  ): void => {
    let newIndex = currentIndex;

    switch (event.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        newIndex = (currentIndex + 1) % items.length;
        break;
      case 'ArrowUp':
      case 'ArrowLeft':
        newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
        break;
      case 'Home':
        newIndex = 0;
        break;
      case 'End':
        newIndex = items.length - 1;
        break;
      default:
        return;
    }

    event.preventDefault();
    onChange(newIndex);
    items[newIndex].focus();
  },
};

/**
 * Form validation accessibility helpers
 */
export const formAccessibility = {
  /**
   * Generate accessible error message
   */
  generateErrorMessage: (fieldName: string, error: string): string => {
    return `${fieldName}: ${error}`;
  },

  /**
   * Generate accessible field description
   */
  generateFieldDescription: (
    description: string,
    isRequired: boolean = false
  ): string => {
    const requiredText = isRequired ? ' (required)' : '';
    return `${description}${requiredText}`;
  },

  /**
   * Validate form accessibility
   */
  validateFormAccessibility: (form: HTMLFormElement): string[] => {
    const issues: string[] = [];

    // Check for labels
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      const id = input.getAttribute('id');
      const ariaLabel = input.getAttribute('aria-label');
      const ariaLabelledBy = input.getAttribute('aria-labelledby');

      if (
        !id ||
        (!ariaLabel &&
          !ariaLabelledBy &&
          !form.querySelector(`label[for="${id}"]`))
      ) {
        issues.push(`Input missing proper label: ${input.tagName}`);
      }
    });

    // Check for fieldsets with legends
    const fieldsets = form.querySelectorAll('fieldset');
    fieldsets.forEach(fieldset => {
      if (!fieldset.querySelector('legend')) {
        issues.push('Fieldset missing legend');
      }
    });

    return issues;
  },
};

/**
 * Screen reader utilities
 */
export const screenReader = {
  /**
   * Create visually hidden text for screen readers
   */
  createSROnlyText: (text: string): HTMLSpanElement => {
    const span = document.createElement('span');
    span.className = 'iepa-sr-only';
    span.textContent = text;
    return span;
  },

  /**
   * Update aria-live region
   */
  updateLiveRegion: (
    regionId: string,
    message: string,
    priority: 'polite' | 'assertive' = 'polite'
  ): void => {
    let region = document.getElementById(regionId);

    if (!region) {
      region = document.createElement('div');
      region.id = regionId;
      region.setAttribute('aria-live', priority);
      region.setAttribute('aria-atomic', 'true');
      region.className = 'iepa-sr-only';
      document.body.appendChild(region);
    }

    region.textContent = message;
  },
};
