/**
 * Integration tests for retry email API endpoint
 * Tests the complete retry email workflow
 */

import { NextRequest } from 'next/server';
import { POST } from '../retry-email/route';

// Mock Supabase client
const mockSupabaseClient = {
  from: jest.fn(),
};

const mockQuery = {
  select: jest.fn(),
  eq: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  single: jest.fn(),
};

// Chain all query methods to return the mock query object
Object.keys(mockQuery).forEach(method => {
  mockQuery[method as keyof typeof mockQuery] = jest.fn().mockReturnValue(mockQuery);
});

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseClient),
}));

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';

const mockFailedEmail = {
  id: 'failed-email-1',
  recipient_email: '<EMAIL>',
  recipient_name: 'Test User',
  sender_email: '<EMAIL>',
  sender_name: 'IEPA Team',
  subject: 'Failed Email Subject',
  email_type: 'registration_confirmation',
  status: 'failed',
  created_at: '2024-01-15T10:00:00Z',
  error_message: 'SMTP connection failed',
  content_preview: 'This is a test email...',
  has_attachments: false,
  user_id: 'user-123',
  registration_id: 'reg-456',
  registration_type: 'standard',
  payment_id: 'pay-789',
};

const mockNewEmailLog = {
  id: 'retry-email-1',
  recipient_email: '<EMAIL>',
  recipient_name: 'Test User',
  sender_email: '<EMAIL>',
  sender_name: 'IEPA Team',
  subject: '[RETRY] Failed Email Subject',
  email_type: 'retry',
  status: 'pending',
  created_at: '2024-01-15T11:00:00Z',
  content_preview: 'This is a test email...',
  has_attachments: false,
  user_id: 'user-123',
  registration_id: 'reg-456',
  registration_type: 'standard',
  payment_id: 'pay-789',
};

describe('Retry Email API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabaseClient.from.mockReturnValue(mockQuery);
    
    // Mock setTimeout to avoid delays in tests
    jest.spyOn(global, 'setTimeout').mockImplementation((callback: any) => {
      callback();
      return {} as any;
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('POST /api/admin/retry-email', () => {
    it('successfully retries a failed email', async () => {
      // Mock finding the original failed email
      mockQuery.single.mockResolvedValueOnce({
        data: mockFailedEmail,
        error: null,
      });

      // Mock creating new retry email log
      mockQuery.select.mockReturnValue({
        single: jest.fn().mockResolvedValue({
          data: mockNewEmailLog,
          error: null,
        }),
      });

      const requestBody = {
        emailLogId: 'failed-email-1',
        recipientEmail: '<EMAIL>',
        subject: 'Failed Email Subject',
        emailType: 'registration_confirmation',
      };

      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Email retry initiated successfully');
      expect(data.retryEmailId).toBe('retry-email-1');
      expect(data.originalEmailId).toBe('failed-email-1');

      // Verify the original email was fetched
      expect(mockQuery.eq).toHaveBeenCalledWith('id', 'failed-email-1');

      // Verify new retry email was created
      expect(mockQuery.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          recipient_email: '<EMAIL>',
          subject: '[RETRY] Failed Email Subject',
          email_type: 'retry',
          status: 'pending',
        })
      );
    });

    it('validates required fields', async () => {
      const requestBody = {
        emailLogId: 'failed-email-1',
        // Missing recipientEmail and subject
      };

      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Missing required fields');
    });

    it('returns error when original email is not found', async () => {
      mockQuery.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Record not found' },
      });

      const requestBody = {
        emailLogId: 'nonexistent-email',
        recipientEmail: '<EMAIL>',
        subject: 'Test Subject',
      };

      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Original email not found');
    });

    it('prevents retrying non-failed emails', async () => {
      const sentEmail = { ...mockFailedEmail, status: 'sent' };
      
      mockQuery.single.mockResolvedValueOnce({
        data: sentEmail,
        error: null,
      });

      const requestBody = {
        emailLogId: 'sent-email-1',
        recipientEmail: '<EMAIL>',
        subject: 'Sent Email Subject',
      };

      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Can only retry failed emails');
    });

    it('handles database insertion errors', async () => {
      mockQuery.single.mockResolvedValueOnce({
        data: mockFailedEmail,
        error: null,
      });

      // Mock insertion error
      mockQuery.select.mockReturnValue({
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: 'Database insertion failed' },
        }),
      });

      const requestBody = {
        emailLogId: 'failed-email-1',
        recipientEmail: '<EMAIL>',
        subject: 'Failed Email Subject',
      };

      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to create retry email log');
    });

    it('preserves original email metadata in retry', async () => {
      mockQuery.single.mockResolvedValueOnce({
        data: mockFailedEmail,
        error: null,
      });

      mockQuery.select.mockReturnValue({
        single: jest.fn().mockResolvedValue({
          data: mockNewEmailLog,
          error: null,
        }),
      });

      const requestBody = {
        emailLogId: 'failed-email-1',
        recipientEmail: '<EMAIL>',
        subject: 'Failed Email Subject',
        emailType: 'registration_confirmation',
      };

      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      await POST(request);

      // Verify that original metadata is preserved
      expect(mockQuery.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          recipient_name: mockFailedEmail.recipient_name,
          sender_email: mockFailedEmail.sender_email,
          sender_name: mockFailedEmail.sender_name,
          content_preview: mockFailedEmail.content_preview,
          has_attachments: mockFailedEmail.has_attachments,
          user_id: mockFailedEmail.user_id,
          registration_id: mockFailedEmail.registration_id,
          registration_type: mockFailedEmail.registration_type,
          payment_id: mockFailedEmail.payment_id,
        })
      );
    });

    it('adds RETRY prefix to subject line', async () => {
      mockQuery.single.mockResolvedValueOnce({
        data: mockFailedEmail,
        error: null,
      });

      mockQuery.select.mockReturnValue({
        single: jest.fn().mockResolvedValue({
          data: mockNewEmailLog,
          error: null,
        }),
      });

      const requestBody = {
        emailLogId: 'failed-email-1',
        recipientEmail: '<EMAIL>',
        subject: 'Original Subject',
      };

      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      await POST(request);

      expect(mockQuery.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: '[RETRY] Original Subject',
        })
      );
    });

    it('handles missing environment variables', async () => {
      const originalUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const originalKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
      
      delete process.env.NEXT_PUBLIC_SUPABASE_URL;
      delete process.env.SUPABASE_SERVICE_ROLE_KEY;

      const requestBody = {
        emailLogId: 'failed-email-1',
        recipientEmail: '<EMAIL>',
        subject: 'Test Subject',
      };

      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Missing Supabase configuration');

      // Restore environment variables
      process.env.NEXT_PUBLIC_SUPABASE_URL = originalUrl;
      process.env.SUPABASE_SERVICE_ROLE_KEY = originalKey;
    });

    it('handles JSON parsing errors', async () => {
      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: 'invalid json',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBeTruthy();
    });

    it('simulates email sending with timeout', async () => {
      const realSetTimeout = global.setTimeout;
      const mockSetTimeout = jest.fn();
      global.setTimeout = mockSetTimeout as any;

      mockQuery.single.mockResolvedValueOnce({
        data: mockFailedEmail,
        error: null,
      });

      mockQuery.select.mockReturnValue({
        single: jest.fn().mockResolvedValue({
          data: mockNewEmailLog,
          error: null,
        }),
      });

      const requestBody = {
        emailLogId: 'failed-email-1',
        recipientEmail: '<EMAIL>',
        subject: 'Failed Email Subject',
      };

      const request = new NextRequest('http://localhost:3000/api/admin/retry-email', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      await POST(request);

      // Verify setTimeout was called to simulate email sending
      expect(mockSetTimeout).toHaveBeenCalledWith(expect.any(Function), 2000);

      global.setTimeout = realSetTimeout;
    });
  });
});
