// Meal utility functions for consistent meal display across the application
import { supabase } from '@/lib/supabase';

// Cache for meal display names to avoid repeated database calls
let mealDisplayCache: Record<string, string> | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get meal display name from cache or database
 */
export async function getMealDisplayName(mealKey: string): Promise<string> {
  // Check if cache is valid
  if (mealDisplayCache && Date.now() - cacheTimestamp < CACHE_DURATION) {
    return mealDisplayCache[mealKey] || mealKey;
  }

  // Refresh cache
  await refreshMealDisplayCache();
  return mealDisplayCache?.[mealKey] || mealKey;
}

/**
 * Get multiple meal display names efficiently
 */
export async function getMealDisplayNames(mealKeys: string[]): Promise<Record<string, string>> {
  // Check if cache is valid
  if (!mealDisplayCache || Date.now() - cacheTimestamp >= CACHE_DURATION) {
    await refreshMealDisplayCache();
  }

  const result: Record<string, string> = {};
  mealKeys.forEach(key => {
    result[key] = mealDisplayCache?.[key] || key;
  });

  return result;
}

/**
 * Refresh the meal display cache from database
 */
async function refreshMealDisplayCache(): Promise<void> {
  try {
    const { data, error } = await supabase
      .from('iepa_meals')
      .select('meal_key, display_date')
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching meal display names:', error);
      return;
    }

    mealDisplayCache = {};
    data?.forEach((meal: { meal_key: string; display_date: string }) => {
      if (mealDisplayCache) {
        mealDisplayCache[meal.meal_key] = meal.display_date;
      }
    });

    cacheTimestamp = Date.now();
  } catch (error) {
    console.error('Error refreshing meal display cache:', error);
  }
}

/**
 * Format meals for display (used in admin components)
 */
export async function formatMealsForDisplay(meals: string[] | undefined | null): Promise<string> {
  if (!meals || meals.length === 0) {
    return 'No meals selected';
  }

  const displayNames = await getMealDisplayNames(meals);
  return meals.map(meal => displayNames[meal] || meal).join(', ');
}

/**
 * Format meals as a simple string for display
 */
export async function formatMealsAsString(
  meals: string[] | undefined
): Promise<string> {
  if (!meals || meals.length === 0) {
    return 'No meals selected';
  }

  const displayNames = await getMealDisplayNames(meals);
  return meals.map(meal => displayNames[meal] || meal).join(', ');
}

/**
 * Clear the meal display cache (useful for testing or when meal data changes)
 */
export function clearMealDisplayCache(): void {
  mealDisplayCache = null;
  cacheTimestamp = 0;
}

/**
 * Legacy function for backward compatibility - maps old meal keys to display names
 * This provides a fallback for components that haven't been updated yet
 */
export function getLegacyMealDisplayName(mealKey: string): string {
  const legacyMealMap: { [key: string]: string } = {
    // Current meal keys with dates
    'day1-reception': 'Sep 15 Welcome Reception & Dinner',
    'day1-breakfast': 'Sep 15 Breakfast',
    'day1-lunch': 'Sep 15 Lunch',
    'day2-breakfast': 'Sep 16 Breakfast',
    'day2-lunch': 'Sep 16 Lunch',
    'day2-dinner': 'Sep 16 Reception & Dinner',
    'day3-breakfast': 'Sep 17 Breakfast',
    'day3-lunch': 'Sep 17 Lunch',
    // Legacy meal keys that might exist in data
    sept15Dinner: 'Sep 15 Welcome Reception & Dinner',
    sept16Breakfast: 'Sep 16 Breakfast',
    sept16Lunch: 'Sep 16 Lunch',
    sept16Dinner: 'Sep 16 Reception & Dinner',
    sept17Breakfast: 'Sep 17 Breakfast',
    sept17Lunch: 'Sep 17 Lunch',
  };
  
  return legacyMealMap[mealKey] || mealKey;
}
