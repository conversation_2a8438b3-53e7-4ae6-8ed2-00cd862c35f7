import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Function to check environment variables when actually needed
function checkEnvironmentVariables() {
  // Only check and throw errors in runtime, not during build
  if (typeof window !== 'undefined' || (process.env.NODE_ENV === 'development' && process.env.VERCEL !== '1')) {
    console.log('🔧 Supabase Environment Check:', {
      hasUrl: !!supabaseUrl,
      hasKey: !!supabaseAnonKey,
      urlLength: supabaseUrl?.length || 0,
      keyLength: supabaseAnonKey?.length || 0,
      environment: process.env.NODE_ENV
    });

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('❌ Missing Supabase environment variables:', {
        NEXT_PUBLIC_SUPABASE_URL: !!supabaseUrl,
        NEXT_PUBLIC_SUPABASE_ANON_KEY: !!supabaseAnonKey
      });
      throw new Error(
        'Missing Supabase environment variables. Please check your .env.local file.'
      );
    }
  }
}

// Client-side Supabase client (safe for browser)
// Use placeholder values during build if environment variables are missing
const url = supabaseUrl || 'https://placeholder.supabase.co';
const key = supabaseAnonKey || 'placeholder-key';

console.log('🚀 Creating Supabase client with config:', {
  hasUrl: !!supabaseUrl,
  hasKey: !!supabaseAnonKey,
  isClient: typeof window !== 'undefined',
  hasLocalStorage: typeof window !== 'undefined' && !!window.localStorage
});

export const supabase = createClient(url, key, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
  },
});

console.log('✅ Supabase client created successfully');

// Server-side client for API routes (respects RLS)
export function createSupabaseServerClient() {
  // Check environment variables when actually creating server client
  checkEnvironmentVariables();

  const url = supabaseUrl || 'https://placeholder.supabase.co';
  const key = supabaseAnonKey || 'placeholder-key';

  return createClient(url, key, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}

// Server-side admin client factory function
export function createSupabaseAdmin() {
  // Check environment variables when actually creating admin client
  checkEnvironmentVariables();

  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const url = supabaseUrl || 'https://placeholder.supabase.co';
  const key = serviceRoleKey || 'placeholder-service-key';

  if (!serviceRoleKey && process.env.NODE_ENV !== 'development') {
    throw new Error(
      'SUPABASE_SERVICE_ROLE_KEY is required for admin operations. This should only be used server-side.'
    );
  }

  return createClient(url, key, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}
