import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { email, role = 'admin' } = await request.json();

    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }

    const supabaseAdmin = createSupabaseAdmin();

    // First, check if the user exists in auth.users
    const { data: authUser, error: authError } = await supabaseAdmin
      .from('auth.users')
      .select('id, email')
      .eq('email', email)
      .single();

    if (authError && authError.code !== 'PGRST116') {
      console.error('Error checking auth user:', authError);
      return NextResponse.json(
        { success: false, error: 'Failed to check user existence' },
        { status: 500 }
      );
    }

    // If user doesn't exist in auth.users, we can still add them to admin table
    // They will be linked when they first sign up
    const user_id = authUser?.id || null;

    // Check if admin user already exists
    const { data: existingAdmin, error: checkError } = await supabaseAdmin
      .from('iepa_admin_users')
      .select('id, email, role, is_active')
      .eq('email', email)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing admin:', checkError);
      return NextResponse.json(
        { success: false, error: 'Failed to check existing admin' },
        { status: 500 }
      );
    }

    if (existingAdmin) {
      return NextResponse.json(
        {
          success: false,
          error: `User ${email} is already an admin with role: ${existingAdmin.role}`,
        },
        { status: 409 }
      );
    }

    // Add new admin user
    const { data: newAdmin, error: insertError } = await supabaseAdmin
      .from('iepa_admin_users')
      .insert({
        user_id,
        email,
        role,
        permissions: {
          dashboard: true,
          users: role === 'super_admin',
          settings: role === 'super_admin',
          reports: true,
          database: role === 'super_admin',
          audit: role === 'super_admin',
        },
        is_active: true,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting admin user:', insertError);
      return NextResponse.json(
        { success: false, error: 'Failed to add admin user' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Successfully added ${email} as ${role}`,
      admin: newAdmin,
    });
  } catch (error) {
    console.error('Error in add-admin-user API:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
