# Testing Documentation

This folder contains all testing-related documentation for the IEPA conference registration application.

## 📋 Contents

### Test Data & Credentials

- `test-users.md` - Test user credentials for different registration types (<PERSON><PERSON><PERSON>, Speaker, Sponsor)

### Testing Procedures

- `test-procedure-verification.md` - Comprehensive testing workflows and verification steps

## 🧪 Testing Workflow

1. **Setup**: Use credentials from `test-users.md`
2. **Procedures**: Follow workflows in `test-procedure-verification.md`
3. **Verification**: Document results and update test status

## 👥 Test User Types

- **Attendee**: Standard conference registration
- **Speaker**: Presentation and session management
- **Sponsor**: Sponsorship packages and benefits

## 🔗 Related Documentation

- Setup guides: `../01-setup-config/`
- Implementation logs: `../04-implementation-logs/`
- API testing: `../03-api-integrations/`
