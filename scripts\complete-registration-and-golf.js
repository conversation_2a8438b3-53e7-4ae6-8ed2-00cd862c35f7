#!/usr/bin/env node

/**
 * Complete Registration and Golf Add-On Test Script
 * Automates the full registration flow and golf add-on testing
 */

const puppeteer = require('puppeteer');

async function completeRegistrationAndGolf() {
  console.log('🚀 Starting complete registration and golf add-on test...');
  
  const browser = await puppeteer.launch({
    headless: false, // Keep visible for debugging
    defaultViewport: { width: 1280, height: 720 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();
  
  try {
    // Navigate to login page
    console.log('📱 Navigating to login page...');
    await page.goto('http://localhost:3000/auth/login', { waitUntil: 'networkidle0' });
    
    // Login with golf test user
    console.log('🔐 Logging in with golf test user...');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'GolfTest123!');
    await page.click('button[type="submit"]');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    
    console.log('✅ Login successful, navigating to registration...');
    
    // Navigate to attendee registration
    await page.goto('http://localhost:3000/register/attendee', { waitUntil: 'networkidle0' });
    
    console.log('📝 Starting registration form completion...');
    
    // Step 1: Registration Type
    console.log('📋 Step 1: Selecting registration type...');
    await page.waitForSelector('[data-testid="registration-type-step"]');
    
    // Select IEPA Member registration (first option)
    await page.click('input[value="iepa-member"]');
    await page.click('button[type="submit"]'); // Next step
    await page.waitForTimeout(1000);
    
    // Step 2: Personal Information
    console.log('👤 Step 2: Filling personal information...');
    await page.waitForSelector('input[name="firstName"]');
    
    await page.type('input[name="firstName"]', 'Golf');
    await page.type('input[name="lastName"]', 'Tester');
    await page.type('input[name="fullName"]', 'Golf Tester');
    await page.type('input[name="nameOnBadge"]', 'Golf T.');
    await page.type('input[name="email"]', '<EMAIL>');
    
    // Select gender
    await page.click('select[name="gender"]');
    await page.select('select[name="gender"]', 'male');
    
    await page.click('button[type="submit"]'); // Next step
    await page.waitForTimeout(1000);
    
    // Step 3: Contact Information
    console.log('📞 Step 3: Filling contact information...');
    await page.waitForSelector('input[name="phoneNumber"]');
    
    await page.type('input[name="phoneNumber"]', '************');
    await page.type('input[name="organization"]', 'Test Energy Company');
    await page.type('input[name="jobTitle"]', 'Golf Tester');
    await page.type('input[name="streetAddress"]', '123 Golf Course Dr');
    await page.type('input[name="city"]', 'Sacramento');
    
    // Select state
    await page.click('select[name="state"]');
    await page.select('select[name="state"]', 'CA');
    
    await page.type('input[name="zipCode"]', '95814');
    
    await page.click('button[type="submit"]'); // Next step
    await page.waitForTimeout(1000);
    
    // Step 4: Event Options (Skip golf for now - we'll add it later)
    console.log('🎯 Step 4: Skipping golf options (will add later)...');
    await page.waitForSelector('[data-testid*="event-options"]', { timeout: 5000 });
    
    // Select some meals
    const mealCheckboxes = [
      'input[name="meals.sept15Dinner"]',
      'input[name="meals.sept16Breakfast"]',
      'input[name="meals.sept16Lunch"]',
      'input[name="meals.sept17Breakfast"]'
    ];
    
    for (const checkbox of mealCheckboxes) {
      try {
        await page.click(checkbox);
      } catch (e) {
        console.log(`⚠️ Could not find meal checkbox: ${checkbox}`);
      }
    }
    
    await page.click('button[type="submit"]'); // Next step
    await page.waitForTimeout(1000);
    
    // Step 5: Emergency Contact
    console.log('🚨 Step 5: Filling emergency contact...');
    await page.waitForSelector('input[name="emergencyContact"]');
    
    await page.type('input[name="emergencyContact"]', 'Jane Tester');
    await page.type('input[name="emergencyPhone"]', '************');
    
    await page.click('button[type="submit"]'); // Next step
    await page.waitForTimeout(1000);
    
    // Step 6: Review & Payment (Submit without payment for testing)
    console.log('💳 Step 6: Reviewing and submitting registration...');
    await page.waitForSelector('[data-testid*="review"]', { timeout: 5000 });
    
    // Submit the registration
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    console.log('✅ Registration submitted! Checking for success...');
    
    // Wait for success notification or redirect
    try {
      await page.waitForSelector('.iepa-status-success, [data-testid*="success"]', { timeout: 10000 });
      console.log('🎉 Registration successful!');
    } catch (e) {
      console.log('⚠️ Success notification not found, but continuing...');
    }
    
    // Navigate to dashboard to find the registration
    console.log('📊 Navigating to dashboard to find registration...');
    await page.goto('http://localhost:3000/dashboard', { waitUntil: 'networkidle0' });
    
    // Look for golf add-on button or registration card
    console.log('🔍 Looking for golf add-on options...');
    
    // Wait for dashboard to load
    await page.waitForTimeout(2000);
    
    // Look for golf-related buttons or links
    const golfSelectors = [
      'button:contains("Add Golf")',
      'a:contains("Golf")',
      '[data-testid*="golf"]',
      'button:contains("Golf Tournament")',
      '.golf-addon-button'
    ];
    
    let golfButtonFound = false;
    for (const selector of golfSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 2000 });
        console.log(`🏌️ Found golf button with selector: ${selector}`);
        await page.click(selector);
        golfButtonFound = true;
        break;
      } catch (e) {
        console.log(`❌ Golf button not found with selector: ${selector}`);
      }
    }
    
    if (!golfButtonFound) {
      console.log('🔍 Golf button not found, taking screenshot for debugging...');
      await page.screenshot({ path: 'dashboard-screenshot.png', fullPage: true });
      console.log('📸 Screenshot saved as dashboard-screenshot.png');
      
      // Try to find any registration-related elements
      const registrationElements = await page.$$eval('*', elements => 
        elements.filter(el => 
          el.textContent && 
          (el.textContent.includes('registration') || 
           el.textContent.includes('Registration') ||
           el.textContent.includes('golf') ||
           el.textContent.includes('Golf'))
        ).map(el => ({
          tagName: el.tagName,
          textContent: el.textContent.substring(0, 100),
          className: el.className
        }))
      );
      
      console.log('🔍 Found registration-related elements:', registrationElements);
    }
    
    if (golfButtonFound) {
      console.log('🏌️ Golf add-on interface opened, filling golf form...');
      
      // Wait for golf form to load
      await page.waitForTimeout(1000);
      
      // Select golf tournament
      try {
        await page.click('input[name="golfTournament"]');
        console.log('✅ Golf tournament selected');
      } catch (e) {
        console.log('❌ Could not select golf tournament checkbox');
      }
      
      // Select golf club rental
      try {
        await page.click('input[name="golfClubRental"]');
        console.log('✅ Golf club rental selected');
      } catch (e) {
        console.log('❌ Could not select golf club rental checkbox');
      }
      
      // Select handedness
      try {
        await page.click('select[name="golfClubHandedness"]');
        await page.select('select[name="golfClubHandedness"]', 'right');
        console.log('✅ Golf club handedness selected (right)');
      } catch (e) {
        console.log('❌ Could not select golf club handedness');
      }
      
      // Submit golf add-on
      try {
        await page.click('button:contains("Add Golf")');
        console.log('🏌️ Golf add-on submitted!');
        await page.waitForTimeout(2000);
      } catch (e) {
        console.log('❌ Could not submit golf add-on');
      }
    }
    
    console.log('🎯 Test completed! Taking final screenshot...');
    await page.screenshot({ path: 'final-state-screenshot.png', fullPage: true });
    console.log('📸 Final screenshot saved as final-state-screenshot.png');
    
  } catch (error) {
    console.error('💥 Error during test:', error);
    await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
    console.log('📸 Error screenshot saved as error-screenshot.png');
  } finally {
    console.log('🔚 Closing browser...');
    await browser.close();
  }
}

// Run the test
completeRegistrationAndGolf().then(() => {
  console.log('✅ Test script completed!');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});
