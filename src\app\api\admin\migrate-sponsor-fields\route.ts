// API endpoint to run sponsor fields migration
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function POST() {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    console.log('🚀 Running sponsor fields migration...');

    // Try to add fields one by one using direct SQL
    const results: any = {};

    // Add contact_name field
    try {
      await supabaseAdmin.from('iepa_sponsor_registrations').select('contact_name').limit(1);
      results.contact_name = 'Field already exists';
    } catch (error) {
      // Field doesn't exist, we need to add it manually
      results.contact_name = 'Field needs to be added manually';
    }

    // Add contact_email field
    try {
      await supabaseAdmin.from('iepa_sponsor_registrations').select('contact_email').limit(1);
      results.contact_email = 'Field already exists';
    } catch (error) {
      results.contact_email = 'Field needs to be added manually';
    }

    // Add sponsorship_level field
    try {
      await supabaseAdmin.from('iepa_sponsor_registrations').select('sponsorship_level').limit(1);
      results.sponsorship_level = 'Field already exists';
    } catch (error) {
      results.sponsorship_level = 'Field needs to be added manually';
    }

    // Add sponsorship_amount field
    try {
      await supabaseAdmin.from('iepa_sponsor_registrations').select('sponsorship_amount').limit(1);
      results.sponsorship_amount = 'Field already exists';
    } catch (error) {
      results.sponsorship_amount = 'Field needs to be added manually';
    }

    return NextResponse.json({
      success: true,
      message: 'Field check completed',
      results: results,
      note: 'Since we cannot run DDL commands through Supabase client, fields need to be added manually through Supabase dashboard'
    });
    
  } catch (error) {
    console.error('Migration error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Migration failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
