'use client';

import { ConferenceAgenda } from '@/components/conference/ConferenceAgenda';
import { Button } from '@/components/ui';
import { CONFERENCE_DATES, CONFERENCE_YEAR } from '@/lib/conference-config';
import Link from 'next/link';
import { ArrowLeft, Calendar, Printer } from 'lucide-react';

export default function AgendaPage() {

  return (
    <div className="iepa-container">
      {/* Header Section */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          {/* Navigation */}
          <div className="mb-6">
            <Link
              href="/about"
              className="inline-flex items-center gap-2 text-[var(--iepa-primary-blue)] hover:text-[var(--iepa-secondary-green)] transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Annual Meeting Info</span>
            </Link>
          </div>

          {/* Page Header */}
          <div className="text-center mb-8">
            <Calendar className="w-12 h-12 mx-auto mb-4 text-[var(--iepa-primary-blue)]" />
            <h1 className="iepa-heading-1 mb-4 bg-gradient-to-r from-[var(--iepa-primary-blue)] to-[var(--iepa-secondary-green)] bg-clip-text text-transparent">
              IEPA {CONFERENCE_YEAR} Conference Agenda
            </h1>
            <p className="iepa-body text-[var(--iepa-gray-600)] mb-4">
              {CONFERENCE_DATES.startDate.displayDate} - {CONFERENCE_DATES.endDate.displayDate}
            </p>
            <Link href="/agenda/print" target="_blank">
              <Button
                variant="outline"
                className="border-[var(--iepa-primary-blue)] text-[var(--iepa-primary-blue)] hover:bg-[var(--iepa-primary-blue)] hover:text-white"
              >
                <Printer className="w-4 h-4 mr-2" />
                Print-Friendly Version
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Agenda Content */}
      <ConferenceAgenda showTitle={false} />

      {/* Additional Information */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="bg-[var(--iepa-gray-50)] rounded-xl p-6">
            <h3 className="iepa-heading-3 mb-4 text-[var(--iepa-primary-blue)]">
              Important Notes
            </h3>
            <ul className="space-y-2 iepa-body text-[var(--iepa-gray-700)]">
              <li className="flex items-start gap-2">
                <span className="text-[var(--iepa-accent-green)] mt-1">•</span>
                <span>All meals are complimentary for registered attendees</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-[var(--iepa-accent-green)] mt-1">•</span>
                <span>Golf tournament requires separate registration ($200 fee)</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-[var(--iepa-accent-green)] mt-1">•</span>
                <span>Schedule is subject to change - check back for updates</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-[var(--iepa-accent-green)] mt-1">•</span>
                <span>Business casual to casual dress code recommended</span>
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-br from-[var(--iepa-primary-blue)]/5 to-[var(--iepa-secondary-green)]/5 rounded-2xl p-8">
            <h3 className="iepa-heading-3 mb-4 text-[var(--iepa-primary-blue)]">
              Ready to Join Us?
            </h3>
            <p className="iepa-body text-[var(--iepa-gray-700)] mb-6 max-w-2xl mx-auto">
              Don&apos;t miss this opportunity to connect with environmental professionals,
              learn about the latest developments, and contribute to the future of 
              environmental protection in a stunning mountain setting.
            </p>
            <div className="flex flex-wrap items-center justify-center gap-4">
              <Link href="/register">
                <Button className="bg-[var(--iepa-accent-green)] hover:bg-[var(--iepa-accent-green)]/90 text-white">
                  Register for Conference
                </Button>
              </Link>
              <Link href="/contact">
                <Button variant="outline">
                  Contact Us
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
