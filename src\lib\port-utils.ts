/**
 * Utility functions for handling dynamic port detection and URL generation
 * Useful for development environments where the app might run on different ports
 */

/**
 * Get the current port from the browser location
 * Returns null if not in browser environment or if port is not specified
 */
export const getCurrentPort = (): number | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  const port = window.location.port;
  return port ? parseInt(port, 10) : null;
};

/**
 * Get the current hostname from the browser location
 * Returns 'localhost' as fallback if not in browser environment
 */
export const getCurrentHostname = (): string => {
  if (typeof window === 'undefined') {
    return 'localhost';
  }

  return window.location.hostname;
};

/**
 * Get the current protocol from the browser location
 * Returns 'http:' as fallback if not in browser environment
 */
export const getCurrentProtocol = (): string => {
  if (typeof window === 'undefined') {
    return 'http:';
  }

  return window.location.protocol;
};

/**
 * Generate a dynamic app URL based on current browser location
 * Falls back to environment variable or default localhost:3000
 */
export const getDynamicAppUrl = (): string => {
  // In browser environment, use current location
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }

  // In server environment, try environment variable first
  if (process.env.NEXT_PUBLIC_APP_URL) {
    // Check if we're in production and the env var is still localhost
    // This handles the case where production deployment uses localhost in env
    const envUrl = process.env.NEXT_PUBLIC_APP_URL;

    // If we're in production (Vercel) and env var is localhost, use Vercel URL
    if (process.env.VERCEL_URL && envUrl.includes('localhost')) {
      return `https://${process.env.VERCEL_URL}`;
    }

    // If we have a custom domain in production, prioritize it
    if (process.env.NODE_ENV === 'production') {
      // Check for common production domains
      if (process.env.VERCEL_URL?.includes('iepa') || process.env.VERCEL_URL?.includes('reg.iepa.com')) {
        return `https://${process.env.VERCEL_URL}`;
      }
      // If we detect we're on reg.iepa.com domain, use that
      if (process.env.VERCEL_URL === 'reg.iepa.com') {
        return 'https://reg.iepa.com';
      }
    }

    return envUrl;
  }

  // Final fallback
  return 'http://localhost:3000';
};

/**
 * Check if the current environment is development
 */
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

/**
 * Check if the current URL is localhost (any port)
 */
export const isLocalhost = (): boolean => {
  if (typeof window === 'undefined') {
    return true; // Assume localhost in server environment during development
  }

  const hostname = window.location.hostname;
  return (
    hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1'
  );
};

/**
 * Generate auth callback URL for the current environment
 */
export const getAuthCallbackUrl = (path: string = '/auth/callback'): string => {
  return `${getDynamicAppUrl()}${path}`;
};

/**
 * Get the production app URL based on environment
 */
export const getProductionAppUrl = (): string => {
  // Check for Vercel environment variables
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  // Check for custom domain environment variable
  if (process.env.NEXT_PUBLIC_PRODUCTION_URL) {
    return process.env.NEXT_PUBLIC_PRODUCTION_URL;
  }

  // Default production domain for IEPA
  if (process.env.NODE_ENV === 'production') {
    return 'https://reg.iepa.com';
  }

  return getDynamicAppUrl();
};

/**
 * Generate auth redirect URL for the current environment
 * Uses production URL in production environment to avoid localhost issues
 */
export const getAuthRedirectUrl = (path: string): string => {
  // In production, always use the production URL for auth redirects
  if (process.env.NODE_ENV === 'production') {
    return `${getProductionAppUrl()}${path}`;
  }

  // In development, use dynamic URL detection
  return `${getDynamicAppUrl()}${path}`;
};

/**
 * Log current environment info (useful for debugging)
 */
export const logEnvironmentInfo = (): void => {
  if (isDevelopment()) {
    console.log('🔧 Environment Info:', {
      appUrl: getDynamicAppUrl(),
      isLocalhost: isLocalhost(),
      port: getCurrentPort(),
      hostname: getCurrentHostname(),
      protocol: getCurrentProtocol(),
    });
  }
};
