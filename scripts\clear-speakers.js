#!/usr/bin/env node

// <PERSON><PERSON><PERSON> to clear all speaker registrations from the database
// This is useful when transitioning to the new speaker-attendee linking system

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function clearSpeakers() {
  try {
    console.log('🔍 Checking existing speakers...');
    
    // First, get count of existing speakers
    const { count, error: countError } = await supabase
      .from('iepa_speaker_registrations')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      throw countError;
    }

    console.log(`📊 Found ${count} speaker registrations`);

    if (count === 0) {
      console.log('✅ No speakers to delete');
      return;
    }

    // Confirm deletion
    console.log('⚠️  This will delete ALL speaker registrations');
    console.log('⚠️  This action cannot be undone');
    
    // In a real script, you might want to add a confirmation prompt
    // For now, we'll proceed with the deletion
    
    console.log('🗑️  Deleting all speaker registrations...');
    
    const { error: deleteError } = await supabase
      .from('iepa_speaker_registrations')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

    if (deleteError) {
      throw deleteError;
    }

    console.log('✅ All speaker registrations have been deleted successfully');
    
    // Verify deletion
    const { count: newCount, error: verifyError } = await supabase
      .from('iepa_speaker_registrations')
      .select('*', { count: 'exact', head: true });

    if (verifyError) {
      throw verifyError;
    }

    console.log(`📊 Remaining speakers: ${newCount}`);
    
    if (newCount === 0) {
      console.log('🎉 Speaker table successfully cleared');
    } else {
      console.log('⚠️  Some speakers may not have been deleted');
    }

  } catch (error) {
    console.error('❌ Error clearing speakers:', error.message);
    process.exit(1);
  }
}

// Also clear any orphaned attendee registrations that were created for speakers
async function clearSpeakerAttendees() {
  try {
    console.log('🔍 Checking for speaker-attendee registrations...');
    
    const { count, error: countError } = await supabase
      .from('iepa_attendee_registrations')
      .select('*', { count: 'exact', head: true })
      .eq('is_speaker', true);

    if (countError) {
      throw countError;
    }

    console.log(`📊 Found ${count} speaker-attendee registrations`);

    if (count === 0) {
      console.log('✅ No speaker-attendee registrations to delete');
      return;
    }

    console.log('🗑️  Deleting speaker-attendee registrations...');
    
    const { error: deleteError } = await supabase
      .from('iepa_attendee_registrations')
      .delete()
      .eq('is_speaker', true);

    if (deleteError) {
      throw deleteError;
    }

    console.log('✅ All speaker-attendee registrations have been deleted');

  } catch (error) {
    console.error('❌ Error clearing speaker-attendee registrations:', error.message);
  }
}

async function main() {
  console.log('🚀 Starting speaker cleanup process...');
  console.log('');
  
  await clearSpeakers();
  console.log('');
  await clearSpeakerAttendees();
  console.log('');
  
  console.log('🎉 Cleanup process completed');
  console.log('');
  console.log('📝 Next steps:');
  console.log('   1. Test the new speaker registration form');
  console.log('   2. Verify speaker-attendee linking works correctly');
  console.log('   3. Check admin interface displays new speakers properly');
}

if (require.main === module) {
  main();
}

module.exports = { clearSpeakers, clearSpeakerAttendees };
