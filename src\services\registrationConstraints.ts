// Registration Constraint Service for IEPA 2025 Conference Registration
// Service functions for checking and enforcing one-registration-per-user constraints

import { supabase } from '@/lib/supabase';

export interface ExistingRegistration {
  id: string;
  type: 'attendee' | 'speaker' | 'sponsor';
  status: string;
  created_at: string;
  updated_at: string;
  // Type-specific fields
  full_name?: string;
  email?: string;
  registration_type?: string;
  attendee_type?: string;
  sponsor_name?: string;
  organization_name?: string;
}

export interface RegistrationConstraintCheck {
  canRegister: boolean;
  existingRegistration: ExistingRegistration | null;
  constraintType: 'none' | 'primary_attendee' | 'speaker' | 'sponsor';
  message: string;
}

/**
 * Check if a user can register for a specific registration type
 */
export async function checkRegistrationConstraints(
  userId: string,
  registrationType: 'attendee' | 'speaker' | 'sponsor',
  attendeeType: 'attendee' | 'spouse' | 'child' = 'attendee'
): Promise<RegistrationConstraintCheck> {
  try {
    let existingRegistration: ExistingRegistration | null = null;
    let constraintType: 'none' | 'primary_attendee' | 'speaker' | 'sponsor' =
      'none';
    let canRegister = true;
    let message = '';

    switch (registrationType) {
      case 'attendee':
        // For primary attendees, check if they already have a primary attendee registration
        if (attendeeType === 'attendee') {
          const { data, error } = await supabase
            .from('iepa_attendee_registrations')
            .select(
              'id, registration_type, attendee_type, full_name, email, payment_status, created_at, updated_at'
            )
            .eq('user_id', userId)
            .eq('attendee_type', 'attendee')
            .maybeSingle();

          if (error && error.code !== 'PGRST116') {
            throw error;
          }

          if (data) {
            existingRegistration = {
              ...data,
              type: 'attendee' as const,
              status: data.payment_status, // Map payment_status to status for consistency
            };
            constraintType = 'primary_attendee';
            canRegister = false;
            message =
              'You already have a primary attendee registration. Each user can only have one primary attendee registration.';
          }
        }
        // Spouse/child registrations are allowed multiple per user
        break;

      case 'speaker':
        const { data: speakerData, error: speakerError } = await supabase
          .from('iepa_speaker_registrations')
          .select(
            'id, full_name, email, organization_name, created_at, updated_at'
          )
          .eq('user_id', userId)
          .maybeSingle();

        if (speakerError && speakerError.code !== 'PGRST116') {
          throw speakerError;
        }

        if (speakerData) {
          existingRegistration = {
            ...speakerData,
            type: 'speaker' as const,
            status: 'active', // Default status since speakers don't have a status field
          };
          constraintType = 'speaker';
          canRegister = false;
          message =
            'You already have a speaker registration. Each user can only register as a speaker once.';
        }
        break;

      case 'sponsor':
        const { data: sponsorData, error: sponsorError } = await supabase
          .from('iepa_sponsor_registrations')
          .select(
            'id, sponsor_name, linked_attendee_email, payment_status, created_at, updated_at'
          )
          .eq('user_id', userId)
          .maybeSingle();

        if (sponsorError && sponsorError.code !== 'PGRST116') {
          throw sponsorError;
        }

        if (sponsorData) {
          existingRegistration = {
            id: sponsorData.id,
            type: 'sponsor' as const,
            status: sponsorData.payment_status,
            created_at: sponsorData.created_at,
            updated_at: sponsorData.updated_at,
            email: sponsorData.linked_attendee_email || '', // Use linked attendee email or empty string
            full_name: sponsorData.sponsor_name,
          };
          constraintType = 'sponsor';
          canRegister = false;
          message =
            'You already have a sponsor registration. Each user can only register as a sponsor once.';
        }
        break;
    }

    if (canRegister) {
      message = `You can register as a ${registrationType}${attendeeType !== 'attendee' ? ` (${attendeeType})` : ''}.`;
    }

    return {
      canRegister,
      existingRegistration,
      constraintType,
      message,
    };
  } catch (error) {
    console.error('Error checking registration constraints:', error);
    throw new Error('Failed to check registration constraints');
  }
}

/**
 * Get all existing registrations for a user
 */
export async function getUserRegistrationStatus(userId: string): Promise<{
  hasAttendeeRegistration: boolean;
  hasSpeakerRegistration: boolean;
  hasSponsorRegistration: boolean;
  registrations: ExistingRegistration[];
}> {
  try {
    const [attendeeResult, speakerResult, sponsorResult] = await Promise.all([
      supabase
        .from('iepa_attendee_registrations')
        .select(
          'id, registration_type, attendee_type, full_name, email, payment_status, created_at, updated_at'
        )
        .eq('user_id', userId)
        .eq('attendee_type', 'attendee') // Only check primary attendee registrations
        .maybeSingle(),
      supabase
        .from('iepa_speaker_registrations')
        .select(
          'id, full_name, email, organization_name, created_at, updated_at'
        )
        .eq('user_id', userId)
        .maybeSingle(),
      supabase
        .from('iepa_sponsor_registrations')
        .select(
          'id, sponsor_name, linked_attendee_email, payment_status, created_at, updated_at'
        )
        .eq('user_id', userId)
        .maybeSingle(),
    ]);

    const registrations: ExistingRegistration[] = [];

    if (attendeeResult.data) {
      registrations.push({
        ...attendeeResult.data,
        type: 'attendee' as const,
        status: attendeeResult.data.payment_status, // Map payment_status to status
      });
    }

    if (speakerResult.data) {
      registrations.push({
        ...speakerResult.data,
        type: 'speaker' as const,
        status: 'active', // Default status for speakers
      });
    }

    if (sponsorResult.data) {
      const sponsorData = sponsorResult.data;
      registrations.push({
        id: sponsorData.id,
        type: 'sponsor' as const,
        status: sponsorData.payment_status,
        created_at: sponsorData.created_at,
        updated_at: sponsorData.updated_at,
        email: sponsorData.linked_attendee_email || '', // Use linked attendee email
        full_name: sponsorData.sponsor_name,
      });
    }

    return {
      hasAttendeeRegistration: !!attendeeResult.data,
      hasSpeakerRegistration: !!speakerResult.data,
      hasSponsorRegistration: !!sponsorResult.data,
      registrations,
    };
  } catch (error) {
    console.error('Error getting user registration status:', error);
    throw new Error('Failed to get user registration status');
  }
}

/**
 * Check if a user can edit an existing registration
 */
export async function canEditRegistration(
  userId: string,
  registrationId: string,
  registrationType: 'attendee' | 'speaker' | 'sponsor'
): Promise<{
  canEdit: boolean;
  reason: string;
  registration: ExistingRegistration | null;
}> {
  try {
    let tableName: string;
    let selectFields: string;

    switch (registrationType) {
      case 'attendee':
        tableName = 'iepa_attendee_registrations';
        selectFields =
          'id, registration_type, attendee_type, full_name, email, payment_status, created_at, updated_at';
        break;
      case 'speaker':
        tableName = 'iepa_speaker_registrations';
        selectFields =
          'id, full_name, email, organization_name, created_at, updated_at';
        break;
      case 'sponsor':
        tableName = 'iepa_sponsor_registrations';
        selectFields =
          'id, sponsor_name, linked_attendee_email, payment_status, created_at, updated_at';
        break;
    }

    const { data, error } = await supabase
      .from(tableName)
      .select(selectFields)
      .eq('id', registrationId)
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      throw error;
    }

    if (!data || typeof data !== 'object' || !('id' in data)) {
      return {
        canEdit: false,
        reason:
          'Registration not found or you do not have permission to edit it.',
        registration: null,
      };
    }

    const registration: ExistingRegistration = {
      id: (data as Record<string, unknown>).id as string,
      type: registrationType,
      status: (data as { payment_status?: string }).payment_status || 'active', // Use payment_status or default to 'active' for speakers
      created_at: (data as Record<string, unknown>).created_at as string,
      updated_at: (data as Record<string, unknown>).updated_at as string,
      email:
        (data as { email?: string; linked_attendee_email?: string }).email ||
        (data as { linked_attendee_email?: string }).linked_attendee_email ||
        '',
      full_name:
        (data as { full_name?: string; sponsor_name?: string }).full_name ||
        (data as { sponsor_name?: string }).sponsor_name ||
        '',
      organization_name: (data as { organization_name?: string })
        .organization_name,
    };

    // Check if registration is in a state that allows editing
    const editableStatuses = ['draft', 'pending', 'confirmed'];
    const currentStatus =
      (data as { payment_status?: string }).payment_status || 'active';
    if (!editableStatuses.includes(currentStatus)) {
      return {
        canEdit: false,
        reason: `Registration cannot be edited because it has status: ${currentStatus}`,
        registration,
      };
    }

    // Check if payment has been completed (for paid registrations)
    if (
      registrationType === 'attendee' &&
      (data as { payment_status?: string }).payment_status === 'completed'
    ) {
      return {
        canEdit: false,
        reason:
          'Registration cannot be edited after payment has been completed. Please contact support for changes.',
        registration,
      };
    }

    // TODO: Add time-based restrictions (e.g., no editing within 48 hours of event)
    // const eventDate = new Date('2025-09-15'); // Conference start date
    // const cutoffDate = new Date(eventDate.getTime() - 48 * 60 * 60 * 1000); // 48 hours before
    // if (new Date() > cutoffDate) {
    //   return {
    //     canEdit: false,
    //     reason: 'Registration editing is closed. Please contact support for changes.',
    //     registration,
    //   };
    // }

    return {
      canEdit: true,
      reason: 'Registration can be edited.',
      registration,
    };
  } catch (error) {
    console.error('Error checking edit permissions:', error);
    throw new Error('Failed to check edit permissions');
  }
}
