# Materialize Integration Guide for IEPA Conference Registration

## Overview

This guide outlines how to selectively integrate Materialize Next.js Admin Template components into our existing IEPA conference registration application while maintaining our current shadcn/ui setup and IEPA branding.

## Current IEPA Project Stack

- **Framework**: Next.js 15 with TypeScript
- **UI Library**: shadcn/ui (Radix UI + Tailwind CSS)
- **Styling**: Tailwind CSS
- **Forms**: React Hook Form (inferred from project structure)
- **Database**: Supabase
- **Icons**: React Icons, Lucide React

## Integration Strategy

### Approach: Selective Enhancement

Rather than replacing our entire UI system, we'll selectively adopt Materialize patterns and components that enhance our current setup without breaking existing functionality.

### Phase 1: Form Enhancement Components

#### 1. Enhanced Radio Button Cards

**Current**: Basic radio buttons for registration type selection
**Enhancement**: Materialize-style radio cards with better visual feedback

**Implementation**:

```tsx
// Create hybrid component combining shadcn/ui with Materialize patterns
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent } from '@/components/ui/card';

const RegistrationTypeCard = ({ value, label, description, price }) => (
  <Card className="cursor-pointer hover:shadow-lg transition-shadow">
    <CardContent className="p-6">
      <div className="flex items-center space-x-3">
        <RadioGroupItem value={value} id={value} />
        <div className="flex-1">
          <label htmlFor={value} className="font-semibold cursor-pointer">
            {label}
          </label>
          <p className="text-sm text-muted-foreground">{description}</p>
          <p className="text-lg font-bold text-iepa-primary">{price}</p>
        </div>
      </div>
    </CardContent>
  </Card>
);
```

#### 2. File Upload Component for Speakers

**Current**: No file upload functionality
**Enhancement**: React Dropzone integration for presentation uploads

**Dependencies to Add**:

```bash
npm install react-dropzone
```

**Implementation**:

```tsx
// Create Supabase-integrated file uploader
import { useDropzone } from 'react-dropzone';
import { supabase } from '@/lib/supabase';

const PresentationUploader = ({ onUpload }) => {
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        ['.pptx'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        ['.docx'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    onDrop: async files => {
      // Upload to Supabase storage
      const file = files[0];
      const { data, error } = await supabase.storage
        .from('presentations')
        .upload(`${Date.now()}-${file.name}`, file);

      if (!error) onUpload(data.path);
    },
  });

  return (
    <div
      {...getRootProps()}
      className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-iepa-primary transition-colors"
    >
      <input {...getInputProps()} />
      {isDragActive ? (
        <p>Drop your presentation here...</p>
      ) : (
        <div>
          <p>Drop your presentation here or click to browse</p>
          <p className="text-sm text-muted-foreground mt-2">
            Allowed: PDF, PPT, PPTX, DOC, DOCX (Max 10MB)
          </p>
        </div>
      )}
    </div>
  );
};
```

#### 3. Enhanced Progress Indicators

**Current**: Basic step indicators
**Enhancement**: Material Design progress with better visual feedback

**Implementation**:

```tsx
// Enhanced multi-step form progress
import { Progress } from '@/components/ui/progress';

const FormProgress = ({ currentStep, totalSteps, stepTitles }) => {
  const progress = (currentStep / totalSteps) * 100;

  return (
    <div className="w-full mb-8">
      <div className="flex justify-between mb-2">
        {stepTitles.map((title, index) => (
          <div
            key={index}
            className={`text-sm ${
              index < currentStep
                ? 'text-iepa-primary font-semibold'
                : index === currentStep
                  ? 'text-iepa-primary'
                  : 'text-muted-foreground'
            }`}
          >
            {index + 1}. {title}
          </div>
        ))}
      </div>
      <Progress value={progress} className="h-2" />
    </div>
  );
};
```

### Phase 2: Layout and Feedback Enhancements

#### 1. Enhanced Alert System

**Current**: Basic error handling
**Enhancement**: Material Design alerts with better UX

**Implementation**:

```tsx
// Enhanced alert component
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react';

const EnhancedAlert = ({ type, title, message, onDismiss }) => {
  const icons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertTriangle,
    info: Info,
  };

  const Icon = icons[type];

  return (
    <Alert
      className={`border-l-4 ${
        type === 'success'
          ? 'border-green-500 bg-green-50'
          : type === 'error'
            ? 'border-red-500 bg-red-50'
            : type === 'warning'
              ? 'border-yellow-500 bg-yellow-50'
              : 'border-blue-500 bg-blue-50'
      }`}
    >
      <Icon className="h-4 w-4" />
      <AlertDescription>
        {title && <div className="font-semibold">{title}</div>}
        {message}
      </AlertDescription>
      {onDismiss && (
        <button onClick={onDismiss} className="ml-auto">
          ×
        </button>
      )}
    </Alert>
  );
};
```

#### 2. Toast Notification System

**Current**: No toast notifications
**Enhancement**: User feedback system for actions

**Dependencies to Add**:

```bash
npm install sonner
```

**Implementation**:

```tsx
// Toast integration for user feedback
import { toast } from 'sonner';

// Usage in forms
const handleRegistrationSubmit = async data => {
  try {
    await submitRegistration(data);
    toast.success('Registration submitted successfully!', {
      description: 'You will receive a confirmation email shortly.',
    });
  } catch (error) {
    toast.error('Registration failed', {
      description: error.message,
    });
  }
};
```

### Phase 3: Advanced Component Integration

#### 1. Enhanced Dialog System

**Current**: Basic modals
**Enhancement**: Confirmation dialogs with better UX

**Implementation**:

```tsx
// Enhanced confirmation dialog
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

const RegistrationConfirmDialog = ({
  open,
  onOpenChange,
  registrationData,
  onConfirm,
}) => (
  <AlertDialog open={open} onOpenChange={onOpenChange}>
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>Confirm Registration</AlertDialogTitle>
        <AlertDialogDescription>
          Please review your registration details before submitting.
        </AlertDialogDescription>
      </AlertDialogHeader>

      <div className="py-4">
        <div className="space-y-2">
          <p>
            <strong>Type:</strong> {registrationData.type}
          </p>
          <p>
            <strong>Name:</strong> {registrationData.name}
          </p>
          <p>
            <strong>Total:</strong> ${registrationData.total}
          </p>
        </div>
      </div>

      <AlertDialogFooter>
        <AlertDialogCancel>Review</AlertDialogCancel>
        <AlertDialogAction onClick={onConfirm}>
          Submit Registration
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
);
```

## Theme Customization for IEPA Branding

### Tailwind CSS Configuration

```javascript
// tailwind.config.js - Add IEPA colors
module.exports = {
  theme: {
    extend: {
      colors: {
        'iepa-primary': '#1B4332', // Dark green
        'iepa-secondary': '#2D5A3D', // Medium green
        'iepa-accent': '#40916C', // Light green
        'iepa-light': '#95D5B2', // Very light green
      },
    },
  },
};
```

### Component Styling Standards

```css
/* Custom CSS for Materialize-inspired components */
.iepa-card-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:scale-[1.02];
}

.iepa-form-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6;
}

.iepa-progress-step {
  @apply flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-semibold;
}
```

## Implementation Checklist

### Phase 1 (Week 1-2)

- [ ] Implement enhanced radio button cards for registration types
- [ ] Add file upload component for speaker presentations
- [ ] Enhance form progress indicators
- [ ] Test integration with existing forms

### Phase 2 (Week 3-4)

- [ ] Implement enhanced alert system
- [ ] Add toast notification system
- [ ] Upgrade button styling and interactions
- [ ] Test user feedback flows

### Phase 3 (Week 5-6)

- [ ] Add confirmation dialog system
- [ ] Implement advanced form validation feedback
- [ ] Add loading states and transitions
- [ ] Comprehensive testing and refinement

## Quality Assurance

### Testing Requirements

1. **Component Integration**: Ensure new components work with existing forms
2. **Responsive Design**: Test on mobile, tablet, and desktop
3. **Accessibility**: Verify keyboard navigation and screen reader support
4. **Performance**: Monitor bundle size impact
5. **Browser Compatibility**: Test across major browsers

### Code Quality

1. **TypeScript**: Maintain strict typing for all new components
2. **ESLint**: Run `npm run check` before each commit
3. **Testing**: Add unit tests for new components
4. **Documentation**: Update component documentation

---

_Integration guide for selective Materialize component adoption_
_Maintains existing shadcn/ui setup while enhancing user experience_
