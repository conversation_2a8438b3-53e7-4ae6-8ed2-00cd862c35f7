'use client';

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardBody } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import {
  FiPercent,
  FiCheck,
  FiX,
  FiLoader,
  FiTag,
  FiDollarSign,
} from 'react-icons/fi';

interface DiscountCalculation {
  originalAmount: number;
  discountAmount: number;
  finalAmount: number;
  savings: number;
}

interface DiscountCodeData {
  id: string;
  code: string;
  name: string;
  description: string;
  discountType: 'percentage' | 'fixed_amount';
  discountValue: number;
  stripeCouponId: string | null;
}

interface DiscountCodeInputProps {
  registrationType: string;
  totalAmount: number;
  userId?: string;
  onDiscountApplied?: (discount: {
    code: DiscountCodeData;
    calculation: DiscountCalculation;
  } | null) => void;
  className?: string;
}

export default function DiscountCodeInput({
  registrationType,
  totalAmount,
  userId,
  onDiscountApplied,
  className = '',
}: DiscountCodeInputProps) {
  const [code, setCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [appliedDiscount, setAppliedDiscount] = useState<{
    code: DiscountCodeData;
    calculation: DiscountCalculation;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showInput, setShowInput] = useState(false);

  // Reset when total amount changes
  useEffect(() => {
    if (appliedDiscount) {
      // Recalculate discount with new amount
      validateDiscountCode(appliedDiscount.code.code);
    }
  }, [totalAmount]);

  const validateDiscountCode = async (discountCode: string) => {
    if (!discountCode.trim()) {
      setError('Please enter a discount code');
      return;
    }

    setIsValidating(true);
    setError(null);

    try {
      const response = await fetch('/api/discount-codes/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: discountCode.trim(),
          registrationType,
          totalAmount,
          userId,
        }),
      });

      const result = await response.json();

      if (result.success && result.valid) {
        const discountData = {
          code: result.discountCode,
          calculation: result.calculation,
        };
        
        setAppliedDiscount(discountData);
        setError(null);
        setCode('');
        setShowInput(false);
        
        // Notify parent component
        onDiscountApplied?.(discountData);
      } else {
        setError(result.error || 'Invalid discount code');
        setAppliedDiscount(null);
        onDiscountApplied?.(null);
      }
    } catch (error) {
      console.error('Error validating discount code:', error);
      setError('Failed to validate discount code. Please try again.');
      setAppliedDiscount(null);
      onDiscountApplied?.(null);
    } finally {
      setIsValidating(false);
    }
  };

  const handleApplyCode = () => {
    validateDiscountCode(code);
  };

  const handleRemoveDiscount = () => {
    setAppliedDiscount(null);
    setError(null);
    setCode('');
    setShowInput(false);
    onDiscountApplied?.(null);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleApplyCode();
    }
  };

  const formatDiscountValue = (type: string, value: number) => {
    return type === 'percentage' ? `${value}%` : `$${value}`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Applied Discount Display */}
      {appliedDiscount && (
        <Card className="border-green-200 bg-green-50">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                  <FiCheck className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-green-800">
                      {appliedDiscount.code.code}
                    </span>
                    <Badge variant="outline" className="text-green-700 border-green-300">
                      {appliedDiscount.code.discountType === 'percentage' ? (
                        <FiPercent className="w-3 h-3 mr-1" />
                      ) : (
                        <FiDollarSign className="w-3 h-3 mr-1" />
                      )}
                      {formatDiscountValue(
                        appliedDiscount.code.discountType,
                        appliedDiscount.code.discountValue
                      )}
                    </Badge>
                  </div>
                  <p className="text-sm text-green-700">
                    {appliedDiscount.code.name}
                  </p>
                  {appliedDiscount.code.description && (
                    <p className="text-xs text-green-600 mt-1">
                      {appliedDiscount.code.description}
                    </p>
                  )}
                </div>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={handleRemoveDiscount}
                className="text-green-700 border-green-300 hover:bg-green-100"
              >
                <FiX className="w-4 h-4" />
              </Button>
            </div>
            
            {/* Discount Calculation */}
            <div className="mt-3 pt-3 border-t border-green-200">
              <div className="space-y-1 text-sm">
                <div className="flex justify-between text-green-700">
                  <span>Original Amount:</span>
                  <span>${appliedDiscount.calculation.originalAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-green-700">
                  <span>Discount:</span>
                  <span>-${appliedDiscount.calculation.discountAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-semibold text-green-800 border-t border-green-200 pt-1">
                  <span>Final Amount:</span>
                  <span>${appliedDiscount.calculation.finalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Discount Code Input */}
      {!appliedDiscount && (
        <div>
          {!showInput ? (
            <Button
              variant="outline"
              onClick={() => setShowInput(true)}
              className="w-full"
            >
              <FiTag className="w-4 h-4 mr-2" />
              Have a discount code?
            </Button>
          ) : (
            <Card>
              <CardBody className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <FiTag className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">
                      Enter discount code
                    </span>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Enter code"
                      value={code}
                      onChange={(e) => setCode(e.target.value.toUpperCase())}
                      onKeyPress={handleKeyPress}
                      disabled={isValidating}
                      className="flex-1"
                    />
                    <Button
                      onClick={handleApplyCode}
                      disabled={!code.trim() || isValidating}
                      className="px-6"
                    >
                      {isValidating ? (
                        <FiLoader className="w-4 h-4 animate-spin" />
                      ) : (
                        'Apply'
                      )}
                    </Button>
                  </div>

                  {error && (
                    <div className="flex items-center space-x-2 text-red-600 text-sm">
                      <FiX className="w-4 h-4" />
                      <span>{error}</span>
                    </div>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setShowInput(false);
                      setCode('');
                      setError(null);
                    }}
                    className="text-gray-500"
                  >
                    Cancel
                  </Button>
                </div>
              </CardBody>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
