'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { <PERSON><PERSON>, Card, CardHeader, CardBody, Input } from '@/components/ui';
import { auth } from '@/lib/auth';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import { Eye, EyeOff, Mail, Key, ArrowLeft } from 'lucide-react';

function LoginContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [usePassword, setUsePassword] = useState(false);
  const [magicLinkSent, setMagicLinkSent] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Pre-fill email from URL if available and check for password mode
  useState(() => {
    const emailFromUrl = searchParams?.get('email');
    const usePasswordFromUrl = searchParams?.get('usePassword') === 'true';

    if (emailFromUrl) {
      setFormData(prev => ({ ...prev, email: emailFromUrl }));
    }

    if (usePasswordFromUrl) {
      setUsePassword(true);
    }
  });

  // Check for auth callback errors
  useEffect(() => {
    const error = searchParams?.get('error');
    if (error) {
      if (error === 'auth_callback_error') {
        setError('Magic link authentication failed. Please try again.');
      } else {
        setError('Authentication failed. Please try again.');
      }
    }
  }, [searchParams]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    // Clear error and success messages when user starts typing
    if (error) setError('');
    if (successMessage) setSuccessMessage('');
    if (magicLinkSent) setMagicLinkSent(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      if (usePassword) {
        // Password-based authentication
        console.log('🔐 Password Login - Form submitted');
        console.log('📧 Form Email:', formData.email);
        console.log('🔑 Form Password Length:', formData.password.length);

        console.log('🚀 Calling signIn function...');
        const result = await auth.signIn(formData.email, formData.password);

        console.log('📊 Login Result:', {
          hasResult: !!result,
          hasData: !!result?.data,
          hasError: !!result?.error,
          errorMessage: result?.error?.message,
        });

        if (result.error) {
          console.error('❌ Login Error:', result.error);
          setError(result.error.message || 'An error occurred during sign in');
        } else {
          console.log('✅ Login successful, redirecting');
          const returnTo = searchParams?.get('returnTo') || '/my-registrations';
          router.push(returnTo);
        }
      } else {
        // Magic link authentication - send email directly
        console.log('🔗 Magic Link Login - Sending email directly');
        console.log('📧 Form Email:', formData.email);

        const returnTo = searchParams?.get('returnTo') || '/my-registrations';

        console.log('🚀 Calling signInWithMagicLink function...');
        const result = await auth.signInWithMagicLink(formData.email, returnTo);

        console.log('📊 Magic Link Result:', {
          hasResult: !!result,
          hasData: !!result?.data,
          hasError: !!result?.error,
          errorMessage: result?.error?.message,
        });

        if (result.error) {
          console.error('❌ Magic Link Error:', result.error);
          setError(
            result.error.message ||
              'An error occurred while sending the magic link'
          );
        } else {
          console.log('✅ Magic link sent successfully');
          setMagicLinkSent(true);
          setSuccessMessage('Magic link sent! Check your email to sign in.');
        }
      }
    } catch (err) {
      console.error('💥 Login Catch Block Error:', {
        error: err,
        errorType: typeof err,
        errorMessage: err instanceof Error ? err.message : 'Unknown error',
      });
      setError(
        err instanceof Error
          ? err.message
          : 'An error occurred during authentication'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="iepa-heading-1 mb-4">Sign In</h1>
            <p className="iepa-body">
              Sign in to your account to register for the IEPA {CONFERENCE_YEAR}{' '}
              Annual Conference.
            </p>
          </div>

          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 text-center">Welcome Back</h2>
              <p className="iepa-body-small text-center text-gray-600">
                {usePassword
                  ? 'Enter your password to continue'
                  : 'We recommend using magic link for faster sign-in'}
              </p>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="iepa-status-error">
                    <p className="iepa-body-small">{error}</p>
                  </div>
                )}

                {successMessage && (
                  <div className="iepa-status-success">
                    <p className="iepa-body-small">{successMessage}</p>
                  </div>
                )}

                <div className="space-y-4">
                  {/* Email field - always visible */}
                  <Input
                    label="Email Address *"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      handleInputChange('email', e.target.value)
                    }
                    isRequired
                    autoComplete="email"
                  />

                  {/* Password field - only visible when usePassword is true */}
                  {usePassword && (
                    <div className="relative">
                      <Input
                        label="Password *"
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Enter your password"
                        value={formData.password}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          handleInputChange('password', e.target.value)
                        }
                        isRequired
                        autoComplete="current-password"
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-9 text-gray-500 hover:text-gray-700 focus:outline-none"
                        onClick={() => setShowPassword(!showPassword)}
                        aria-label={
                          showPassword ? 'Hide password' : 'Show password'
                        }
                      >
                        {showPassword ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  )}
                </div>

                <div className="flex flex-col space-y-4">
                  {!usePassword ? (
                    <>
                      {/* Magic Link Button - Primary Option */}
                      <Button
                        type="submit"
                        color="secondary"
                        size="lg"
                        className="w-full disabled:opacity-50"
                        disabled={isLoading || !formData.email || magicLinkSent}
                      >
                        <Mail className="w-5 h-5 mr-2" />
                        {isLoading
                          ? 'Sending magic link...'
                          : magicLinkSent
                            ? 'Magic link sent!'
                            : 'Sign in with magic link'}
                      </Button>

                      {/* Additional instructions when magic link is sent */}
                      {magicLinkSent && (
                        <div className="text-center space-y-2">
                          <p className="iepa-body-small text-gray-600">
                            Check your email and click the link to sign in.
                          </p>
                          <button
                            type="button"
                            onClick={() => {
                              setMagicLinkSent(false);
                              setSuccessMessage('');
                            }}
                            className="iepa-body-small hover:underline"
                            style={{ color: 'var(--iepa-primary-blue)' }}
                          >
                            Send another magic link
                          </button>
                        </div>
                      )}

                      {/* Toggle to password option - only show if magic link not sent */}
                      {!magicLinkSent && (
                        <button
                          type="button"
                          onClick={() => setUsePassword(true)}
                          className="iepa-body-small text-center hover:underline"
                          style={{ color: 'var(--iepa-primary-blue)' }}
                        >
                          or use password
                        </button>
                      )}
                    </>
                  ) : (
                    <>
                      {/* Password Login Button */}
                      <Button
                        type="submit"
                        color="secondary"
                        size="lg"
                        className="w-full"
                        disabled={
                          isLoading || !formData.email || !formData.password
                        }
                      >
                        <Key className="w-4 h-4 mr-2" />
                        {isLoading ? 'Signing In...' : 'Sign In with Password'}
                      </Button>

                      {/* Back to magic link option */}
                      <button
                        type="button"
                        onClick={() => {
                          setUsePassword(false);
                          setMagicLinkSent(false);
                          setSuccessMessage('');
                        }}
                        className="iepa-body-small text-center hover:underline flex items-center justify-center gap-1"
                        style={{ color: 'var(--iepa-primary-blue)' }}
                      >
                        <ArrowLeft className="w-4 h-4" />
                        Back to magic link
                      </button>

                      {/* Forgot password link - only show in password mode */}
                      <div className="text-center">
                        <Link
                          href="/auth/forgot-password"
                          className="iepa-body-small hover:underline"
                          style={{ color: 'var(--iepa-primary-blue)' }}
                        >
                          Forgot your password?
                        </Link>
                      </div>
                    </>
                  )}
                </div>
              </form>
            </CardBody>
          </Card>

          <div className="text-center mt-6">
            <p className="iepa-body-small">
              Don&apos;t have an account?{' '}
              <Link
                href="/auth/signup"
                className="font-semibold hover:underline"
                style={{ color: 'var(--iepa-primary-blue)' }}
              >
                Create Account
              </Link>
            </p>
          </div>

          <div
            className="mt-8 p-4 rounded-lg"
            style={{ backgroundColor: 'var(--iepa-gray-50)' }}
          >
            <h3 className="iepa-heading-3 mb-2">Need Help?</h3>
            <p className="iepa-body-small mb-3">
              If you&apos;re having trouble signing in or need assistance with
              registration:
            </p>
            <ul className="iepa-body-small space-y-1">
              <li>• Check that your email address is correct</li>
              <li>• Try resetting your password if you&apos;ve forgotten it</li>
              <li>• Contact our support team if you continue to have issues</li>
            </ul>
            <div className="mt-4">
              <Button as={Link} href="/contact" variant="bordered" size="sm">
                Contact Support
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense
      fallback={
        <div className="iepa-container">
          <section className="iepa-section">
            <div className="max-w-md mx-auto">
              <div className="text-center">
                <h1 className="iepa-heading-1 mb-4">Loading...</h1>
                <p className="iepa-body">
                  Please wait while we prepare the sign-in page.
                </p>
              </div>
            </div>
          </section>
        </div>
      }
    >
      <LoginContent />
    </Suspense>
  );
}
