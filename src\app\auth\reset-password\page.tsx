'use client';

import { useState, useEffect, Suspense } from 'react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Input } from '@/components/ui';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import {
  debugValidateResetSession,
  debugUpdatePassword,
  logAuthOperation,
  getAuthEnvironmentInfo,
} from '@/lib/auth-debug';

function ResetPasswordContent() {
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [success, setSuccess] = useState(false);
  const [validToken, setValidToken] = useState(false);
  const [debugInfo, setDebugInfo] = useState<{
    valid: boolean;
    error?: string;
    context?: Record<string, unknown>;
    session?: unknown;
    user?: unknown;
  } | null>(null);

  useEffect(() => {
    const validateSession = async () => {
      // Log environment info
      logAuthOperation('resetPasswordPage', true, null, null, {
        step: 'page_load',
        environment: getAuthEnvironmentInfo(),
      });

      // First check if user has a current session (from auth/confirm flow)
      const { data: sessionData, error: sessionError } =
        await supabase.auth.getSession();

      if (sessionData.session && !sessionError) {
        // User has valid session from auth/confirm flow
        setValidToken(true);
        setDebugInfo({
          valid: true,
          session: sessionData.session,
          user: sessionData.session.user,
          context: { source: 'current_session' },
        });

        logAuthOperation('resetPasswordPage', true, null, null, {
          step: 'valid_session_found',
          userId: sessionData.session.user.id,
          source: 'current_session',
        });
        return;
      }

      // Fallback: Check URL parameters (legacy flow)
      const validation = await debugValidateResetSession(searchParams || new URLSearchParams());

      setDebugInfo(validation);

      if (validation.valid) {
        setValidToken(true);
        logAuthOperation('resetPasswordPage', true, null, null, {
          step: 'session_valid',
          userId: validation.user?.id,
          source: 'url_parameters',
        });
      } else {
        setValidToken(false);
        setMessage(
          validation.error ||
            'Invalid or expired reset link. Please request a new password reset.'
        );
        logAuthOperation('resetPasswordPage', false, null, validation.error, {
          step: 'session_invalid',
          validationContext: validation.context,
        });
      }
    };

    validateSession();
  }, [searchParams]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (message) setMessage(''); // Clear error when user starts typing
  };

  const validateForm = () => {
    if (!formData.password || !formData.confirmPassword) {
      setMessage('Please fill in all fields');
      return false;
    }

    if (formData.password.length < 6) {
      setMessage('Password must be at least 6 characters long');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      setMessage('Passwords do not match');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    if (!validateForm()) {
      setIsLoading(false);
      return;
    }

    try {
      // Use debug password update for comprehensive logging
      const result = await debugUpdatePassword(formData.password);

      if (!result.success) {
        throw new Error(result.error);
      }

      setSuccess(true);
      logAuthOperation('resetPasswordPage', true, null, null, {
        step: 'password_updated_successfully',
      });
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'An error occurred while updating your password';

      setMessage(errorMessage);
      logAuthOperation('resetPasswordPage', false, null, err, {
        step: 'password_update_failed',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!validToken && !message.includes('Invalid')) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto text-center">
            <div className="iepa-status-info">
              <h1 className="iepa-heading-1 mb-4">Loading...</h1>
              <p className="iepa-body">Verifying your password reset link...</p>
            </div>
          </div>
        </section>
      </div>
    );
  }

  if (!validToken) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto text-center">
            <div className="iepa-status-error mb-8">
              <h1 className="iepa-heading-1 mb-4">Invalid Reset Link</h1>
              <p className="iepa-body mb-4">
                This password reset link is invalid or has expired.
              </p>
              <p className="iepa-body-small">
                Please request a new password reset to continue.
              </p>
            </div>

            <div className="space-y-4">
              <Button
                as={Link}
                href="/auth/forgot-password"
                color="primary"
                size="lg"
                className="w-full"
              >
                Request New Reset Link
              </Button>

              <Button
                as={Link}
                href="/auth/login"
                variant="bordered"
                size="lg"
                className="w-full"
              >
                Back to Sign In
              </Button>
            </div>
          </div>
        </section>
      </div>
    );
  }

  if (success) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto text-center">
            <div className="iepa-status-success mb-8">
              <h1 className="iepa-heading-1 mb-4">Password Updated!</h1>
              <p className="iepa-body mb-4">
                Your password has been successfully updated.
              </p>
              <p className="iepa-body-small">
                You can now sign in with your new password.
              </p>
            </div>

            <Button
              as={Link}
              href="/auth/login"
              color="primary"
              size="lg"
              className="w-full"
            >
              Sign In
            </Button>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="iepa-heading-1 mb-4">Set New Password</h1>
            <p className="iepa-body">
              Enter your new password below to complete the reset process.
            </p>
          </div>

          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 text-center">
                Create New Password
              </h2>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmit} className="space-y-6">
                {message && (
                  <div className="iepa-status-error">
                    <p className="iepa-body-small">{message}</p>
                  </div>
                )}

                <div className="space-y-4">
                  <Input
                    label="New Password *"
                    type="password"
                    placeholder="Enter your new password"
                    value={formData.password}
                    onChange={e =>
                      handleInputChange('password', e.target.value)
                    }
                    isRequired
                    autoComplete="new-password"
                    description="Must be at least 6 characters long"
                  />

                  <Input
                    label="Confirm New Password *"
                    type="password"
                    placeholder="Confirm your new password"
                    value={formData.confirmPassword}
                    onChange={e =>
                      handleInputChange('confirmPassword', e.target.value)
                    }
                    isRequired
                    autoComplete="new-password"
                  />
                </div>

                <Button
                  type="submit"
                  color="primary"
                  size="lg"
                  className="w-full"
                  disabled={
                    isLoading || !formData.password || !formData.confirmPassword
                  }
                >
                  {isLoading ? 'Updating Password...' : 'Update Password'}
                </Button>
              </form>
            </CardBody>
          </Card>

          <div className="text-center mt-6">
            <p className="iepa-body-small">
              Remember your password?{' '}
              <Link
                href="/auth/login"
                className="font-semibold hover:underline"
                style={{ color: 'var(--iepa-primary-blue)' }}
              >
                Sign In
              </Link>
            </p>
          </div>

          <div
            className="mt-8 p-4 rounded-lg"
            style={{ backgroundColor: 'var(--iepa-gray-50)' }}
          >
            <h3 className="iepa-heading-3 mb-2">Password Security Tips</h3>
            <ul className="iepa-body-small space-y-1">
              <li>• Use at least 6 characters (longer is better)</li>
              <li>• Include a mix of letters, numbers, and symbols</li>
              <li>• Avoid using personal information</li>
              <li>• Don&apos;t reuse passwords from other accounts</li>
              <li>• Consider using a password manager</li>
            </ul>
          </div>

          {/* Debug Information (Development Only) */}
          {process.env.NODE_ENV === 'development' && debugInfo && (
            <div className="mt-8 p-4 rounded-lg border border-yellow-300 bg-yellow-50">
              <h3 className="text-sm font-semibold mb-2 text-yellow-800">
                🔧 Debug Information (Development Only)
              </h3>
              <details className="text-xs">
                <summary className="cursor-pointer text-yellow-700 hover:text-yellow-900">
                  Click to view debug details
                </summary>
                <pre className="mt-2 p-2 bg-white rounded border text-xs overflow-auto max-h-40">
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ResetPasswordContent />
    </Suspense>
  );
}
