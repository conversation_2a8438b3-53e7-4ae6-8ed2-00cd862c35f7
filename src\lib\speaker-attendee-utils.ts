// Utility functions for managing speaker-attendee registration links
import { supabase } from '@/lib/supabase';
import { SPEAKER_PRICING } from '@/lib/pricing-config';

export interface SpeakerAttendeeData {
  // Speaker Information
  speakerData: {
    full_name: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number?: string;
    preferred_contact_method: string;
    organization_name: string;
    job_title: string;
    bio: string;
    presentation_title?: string;
    presentation_description?: string;
    presentation_duration?: string;
    target_audience?: string;
    learning_objectives?: string;
    speaker_experience?: string;
    previous_speaking?: string;
    equipment_needs?: string;
    special_requests?: string;
    presentation_file_url?: string;
    headshot_url?: string;
    speaker_pricing_type: string;
  };
  
  // Attendee Information
  attendeeData: {
    name_on_badge: string;
    gender: string;
    street_address: string;
    city: string;
    state: string;
    zip_code: string;
    attending_golf: boolean;
    golf_club_rental: boolean;
    golf_club_handedness: string;
    meals: string[];
    dietary_needs: string;
  };
}

export interface CreateSpeakerAttendeeResult {
  success: boolean;
  speakerRegistrationId?: string;
  attendeeRegistrationId?: string;
  error?: string;
}

/**
 * Create linked speaker and attendee registrations
 */
export async function createLinkedSpeakerAttendee(
  userId: string,
  data: SpeakerAttendeeData
): Promise<CreateSpeakerAttendeeResult> {
  try {
    // Get speaker pricing information
    const speakerPricing = SPEAKER_PRICING.find(
      p => p.id === data.speakerData.speaker_pricing_type
    );

    if (!speakerPricing) {
      return {
        success: false,
        error: 'Invalid speaker pricing type selected.',
      };
    }

    // Calculate costs
    const golfTotal = data.attendeeData.attending_golf ? 200 : 0;
    const golfClubRentalTotal = data.attendeeData.golf_club_rental ? 70 : 0;
    const mealTotal = 0; // All meals are complimentary
    const grandTotal = speakerPricing.basePrice + golfTotal + golfClubRentalTotal + mealTotal;

    // Create attendee registration first
    const attendeeRegistrationData = {
      user_id: userId,
      registration_type: data.speakerData.speaker_pricing_type,
      full_name: data.speakerData.full_name,
      email: data.speakerData.email,
      first_name: data.speakerData.first_name,
      last_name: data.speakerData.last_name,
      name_on_badge: data.attendeeData.name_on_badge || data.speakerData.full_name,
      gender: data.attendeeData.gender,
      phone_number: data.speakerData.phone_number || '',
      street_address: data.attendeeData.street_address,
      city: data.attendeeData.city,
      state: data.attendeeData.state,
      zip_code: data.attendeeData.zip_code,
      organization: data.speakerData.organization_name,
      job_title: data.speakerData.job_title,
      attending_golf: data.attendeeData.attending_golf,
      golf_club_rental: data.attendeeData.golf_club_rental,
      golf_club_handedness: data.attendeeData.golf_club_handedness,
      meals: data.attendeeData.meals,
      dietary_needs: data.attendeeData.dietary_needs,
      registration_total: speakerPricing.basePrice,
      golf_total: golfTotal,
      golf_club_rental_total: golfClubRentalTotal,
      meal_total: mealTotal,
      grand_total: grandTotal,
      payment_status: speakerPricing.basePrice === 0 ? 'completed' : 'pending',
      is_speaker: true,
      speaker_pricing_type: data.speakerData.speaker_pricing_type,
    };

    const { data: attendeeResult, error: attendeeError } = await supabase
      .from('iepa_attendee_registrations')
      .insert([attendeeRegistrationData])
      .select()
      .single();

    if (attendeeError) {
      return {
        success: false,
        error: `Failed to create attendee registration: ${attendeeError.message}`,
      };
    }

    // Create speaker registration with link to attendee
    const speakerRegistrationData = {
      user_id: userId,
      ...data.speakerData,
      attendee_registration_id: attendeeResult.id,
    };

    const { data: speakerResult, error: speakerError } = await supabase
      .from('iepa_speaker_registrations')
      .insert([speakerRegistrationData])
      .select()
      .single();

    if (speakerError) {
      // If speaker creation fails, we should clean up the attendee registration
      await supabase
        .from('iepa_attendee_registrations')
        .delete()
        .eq('id', attendeeResult.id);

      return {
        success: false,
        error: `Failed to create speaker registration: ${speakerError.message}`,
      };
    }

    // Update attendee registration with speaker link
    const { error: updateError } = await supabase
      .from('iepa_attendee_registrations')
      .update({ speaker_registration_id: speakerResult.id })
      .eq('id', attendeeResult.id);

    if (updateError) {
      console.warn('Failed to link speaker to attendee registration:', updateError);
      // Don't fail the entire operation for this
    }

    return {
      success: true,
      speakerRegistrationId: speakerResult.id,
      attendeeRegistrationId: attendeeResult.id,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Get speaker registration with linked attendee data
 */
export async function getSpeakerWithAttendeeData(speakerId: string) {
  const { data, error } = await supabase
    .from('iepa_speaker_registrations')
    .select(`
      *,
      attendee_data:iepa_attendee_registrations!attendee_registration_id(*)
    `)
    .eq('id', speakerId)
    .single();

  if (error) {
    throw new Error(`Failed to fetch speaker data: ${error.message}`);
  }

  return data;
}

/**
 * Update speaker attendee information
 */
export async function updateSpeakerAttendeeData(
  speakerId: string,
  speakerUpdates: Partial<any>,
  attendeeUpdates: Partial<any>
) {
  try {
    // Get the current speaker registration to find the linked attendee
    const speaker = await getSpeakerWithAttendeeData(speakerId);
    
    if (!speaker.attendee_registration_id) {
      throw new Error('No linked attendee registration found for this speaker');
    }

    // Update speaker registration
    if (Object.keys(speakerUpdates).length > 0) {
      const { error: speakerError } = await supabase
        .from('iepa_speaker_registrations')
        .update(speakerUpdates)
        .eq('id', speakerId);

      if (speakerError) {
        throw new Error(`Failed to update speaker: ${speakerError.message}`);
      }
    }

    // Update attendee registration
    if (Object.keys(attendeeUpdates).length > 0) {
      const { error: attendeeError } = await supabase
        .from('iepa_attendee_registrations')
        .update(attendeeUpdates)
        .eq('id', speaker.attendee_registration_id);

      if (attendeeError) {
        throw new Error(`Failed to update attendee data: ${attendeeError.message}`);
      }
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
