# IEPA Conference Dashboard - Admin Access Setup

## Overview

This document outlines the complete admin access configuration for the IEPA 2025 Conference Registration Dashboard. The system provides secure, role-based access control for conference administrators.

## ✅ Completed Configuration

### 1. **Supabase Database Setup**

#### **Admin Users Table**

- **Table**: `public.iepa_admin_users`
- **Purpose**: Manages admin user access and permissions
- **Columns**:
  - `id` (UUID, Primary Key)
  - `user_id` (UUID, References auth.users)
  - `email` (TEXT, Unique)
  - `role` (TEXT, 'admin' | 'super_admin')
  - `permissions` (JSONB)
  - `is_active` (BOOLEAN)
  - `created_at`, `updated_at` (Timestamps)

#### **Row Level Security (RLS)**

- ✅ Enabled on `iepa_admin_users` table
- ✅ Admin users can view all admin records
- ✅ Super admins can manage admin users
- ✅ Secure access policies implemented

#### **Database Functions**

- ✅ `is_admin(user_email)` - Check if user is admin
- ✅ `get_admin_role(user_email)` - Get user's admin role
- ✅ Security definer functions for safe access

### 2. **Authentication Configuration**

#### **Supabase Auth Settings**

- **Project**: NDS (ID: uffhyhpcuedjsisczocy)
- **Site URL**: `http://localhost:3000`
- **Allowed Redirect URLs**:
  - `http://localhost:3000/**`
  - `https://iepa-conf-reg.vercel.app/**`
  - `https://*.vercel.app/**`
  - `http://localhost:3000/auth/callback`
  - `http://localhost:3000/dashboard`
  - `http://localhost:3000/auth/login`

#### **Email Authentication**

- ✅ Email authentication enabled
- ✅ Email confirmation required
- ✅ Password reset functionality
- ✅ Secure email templates configured

### 3. **Admin Users Configured**

#### **Current Admin Users**

1. **<EMAIL>** (Super Admin)

   - Role: `super_admin`
   - Permissions: Full access (dashboard, users, settings, reports)
   - Status: Active

2. **<EMAIL>** (Super Admin)
   - Role: `super_admin`
   - Permissions: Full access (dashboard, users, settings, reports)
   - Status: Active

### 4. **Application Integration**

#### **Admin Access Hook** (`src/hooks/useAdminAccess.ts`)

- ✅ Real-time admin status checking
- ✅ Permission-based access control
- ✅ Role verification (admin/super_admin)
- ✅ Error handling and loading states
- ✅ Admin user management functions

#### **Dashboard Protection** (`src/app/dashboard/page.tsx`)

- ✅ Authentication requirement
- ✅ Admin access verification
- ✅ Role-based UI elements
- ✅ Access denied screen for non-admins
- ✅ Admin badge in welcome banner

## 🔐 Security Features

### **Multi-Layer Security**

1. **Authentication**: Supabase Auth with email verification
2. **Authorization**: Role-based access control (RBAC)
3. **Database Security**: Row Level Security (RLS) policies
4. **Application Security**: Client-side access checks
5. **API Security**: Server-side function security

### **Permission System**

```json
{
  "dashboard": true, // Access to dashboard
  "users": true, // Manage admin users (super_admin only)
  "settings": true, // System settings (super_admin only)
  "reports": true // Generate reports
}
```

### **Role Hierarchy**

- **Super Admin**: Full system access, can manage other admins
- **Admin**: Dashboard access, limited management capabilities

## 🚀 Testing the Setup

### **Step 1: Access the Dashboard**

1. Navigate to `http://localhost:3000/dashboard`
2. You should be redirected to login if not authenticated

### **Step 2: Sign Up/Login**

1. Go to `http://localhost:3000/auth/signup` or `http://localhost:3000/auth/login`
2. Use one of the configured admin emails:
   - `<EMAIL>`
   - `<EMAIL>`
3. Complete the authentication process

### **Step 3: Verify Admin Access**

1. After login, you should be redirected to the dashboard
2. Check for the admin badge in the welcome banner
3. Verify access to all dashboard sections
4. Confirm data loads properly

### **Step 4: Test Non-Admin Access**

1. Sign up with a different email address
2. Try to access `/dashboard`
3. Should see "Access Denied" screen
4. Verify proper error handling

## 📋 Admin Management

### **Adding New Admin Users**

```typescript
// Using the useAdminUsers hook
const { addAdminUser } = useAdminUsers();

await addAdminUser('<EMAIL>', 'admin');
```

### **Updating Admin Permissions**

```typescript
// Using the useAdminUsers hook
const { updateAdminUser } = useAdminUsers();

await updateAdminUser(userId, {
  role: 'super_admin',
  permissions: { dashboard: true, users: true, settings: true, reports: true },
});
```

### **Database Direct Access**

```sql
-- Add admin user directly
INSERT INTO public.iepa_admin_users (email, role, permissions, is_active)
VALUES ('<EMAIL>', 'admin', '{"dashboard": true, "reports": true}', true);

-- Check admin status
SELECT * FROM public.iepa_admin_users WHERE email = '<EMAIL>';

-- Deactivate admin
UPDATE public.iepa_admin_users SET is_active = false WHERE email = '<EMAIL>';
```

## 🔧 Configuration Files

### **Environment Variables** (`.env.local`)

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://uffhyhpcuedjsisczocy.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
```

### **Key Files**

- `src/hooks/useAdminAccess.ts` - Admin access management
- `src/app/dashboard/page.tsx` - Protected dashboard
- `src/types/dashboard.ts` - Type definitions
- `src/services/dashboard.ts` - Data services

## 🚨 Troubleshooting

### **Common Issues**

#### **"Access Denied" for Valid Admin**

1. Check if user email exists in `iepa_admin_users` table
2. Verify `is_active = true`
3. Check Supabase RLS policies
4. Confirm user is authenticated

#### **Dashboard Not Loading**

1. Check browser console for errors
2. Verify Supabase connection
3. Check admin access hook functionality
4. Confirm database permissions

#### **Authentication Issues**

1. Verify redirect URLs in Supabase
2. Check email confirmation status
3. Confirm site URL configuration
4. Test with different browsers/incognito

### **Debug Commands**

```sql
-- Check admin users
SELECT email, role, is_active, permissions FROM public.iepa_admin_users;

-- Check auth users
SELECT id, email, email_confirmed_at FROM auth.users;

-- Test admin function
SELECT public.is_admin('<EMAIL>');
```

## 📈 Next Steps

### **Production Deployment**

1. Update redirect URLs for production domain
2. Configure production email settings
3. Set up monitoring and logging
4. Implement backup procedures

### **Enhanced Features**

1. Admin activity logging
2. Session management
3. Two-factor authentication
4. Advanced permission granularity
5. Admin user invitation system

---

**Admin access is now fully configured and ready for use!** 🎉

The dashboard provides secure, role-based access for IEPA conference administrators with comprehensive data management capabilities.
