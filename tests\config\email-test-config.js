/**
 * Email Testing Configuration for IEPA Conference Registration
 * Manages test email accounts and environment-specific settings
 */

/**
 * Test email accounts configuration
 * Add your test email credentials here
 */
const EMAIL_TEST_ACCOUNTS = {
  // Primary test account (Gmail with app-specific password)
  primary: {
    email: process.env.TEST_EMAIL_ADDRESS || '<EMAIL>',
    password: process.env.TEST_EMAIL_PASSWORD || '', // App-specific password
    provider: 'gmail',
  },

  // Secondary test account (for testing multiple recipients)
  secondary: {
    email: process.env.TEST_EMAIL_SECONDARY || '<EMAIL>',
    password: process.env.TEST_EMAIL_PASSWORD_SECONDARY || '',
    provider: 'gmail',
  },

  // Mailtrap account (alternative testing service)
  mailtrap: {
    email: process.env.MAILTRAP_EMAIL || '',
    password: process.env.MAILTRAP_PASSWORD || '',
    provider: 'mailtrap',
  },
};

/**
 * Email testing environment configuration
 */
const EMAIL_TEST_CONFIG = {
  // Default timeout for waiting for emails (30 seconds)
  defaultTimeout: 30000,

  // Whether to use Supabase Inbucket for local testing
  useInbucketForLocal: true,

  // Whether to mark emails as read after checking
  markAsRead: true,

  // Retry configuration
  maxRetries: 3,
  retryDelay: 5000,

  // Email content validation settings
  validation: {
    requireIEPABranding: true,
    requireFromAddress: '<EMAIL>',
    allowedFromDomains: ['iepa.com', 'notewaredigital.com'],

    // Expected email types and their validation rules
    emailTypes: {
      registration_confirmation: {
        subjectContains: ['confirmation', 'registration'],
        bodyContains: ['iepa', 'conference', 'confirmation'],
        shouldHaveLinks: true,
        shouldHaveAttachments: false,
      },

      welcome: {
        subjectContains: ['welcome', 'iepa'],
        bodyContains: ['welcome', 'conference', 'iepa'],
        shouldHaveLinks: true,
        shouldHaveAttachments: false,
      },

      payment_confirmation: {
        subjectContains: ['payment', 'confirmation', 'receipt'],
        bodyContains: ['payment', 'confirmation', 'amount'],
        shouldHaveLinks: true,
        shouldHaveAttachments: true, // Should include PDF receipt
      },

      speaker_confirmation: {
        subjectContains: ['speaker', 'confirmation'],
        bodyContains: ['speaker', 'presentation', 'iepa'],
        shouldHaveLinks: true,
        shouldHaveAttachments: false,
      },

      sponsor_instructions: {
        subjectContains: ['sponsor', 'payment', 'instructions'],
        bodyContains: ['sponsor', 'check', 'payment', 'address'],
        shouldHaveLinks: false,
        shouldHaveAttachments: false,
      },
    },
  },
};

/**
 * Get email test account based on environment and preferences
 * @param {string} accountType - Type of account ('primary', 'secondary', 'mailtrap')
 * @returns {Object} Email account configuration
 */
function getEmailTestAccount(accountType = 'primary') {
  const account = EMAIL_TEST_ACCOUNTS[accountType];

  if (!account) {
    throw new Error(`Unknown email test account type: ${accountType}`);
  }

  // Check if credentials are available
  if (!account.email || !account.password) {
    console.warn(
      `⚠️ Email credentials not configured for ${accountType} account`
    );
    console.warn('Consider using Supabase Inbucket for local testing instead');
    return null;
  }

  return account;
}

/**
 * Determine if we should use Inbucket based on environment
 * @returns {boolean} Whether to use Inbucket
 */
function shouldUseInbucket() {
  // Use Inbucket if we're in local development and it's enabled
  const isLocal =
    process.env.NODE_ENV === 'development' ||
    process.env.PLAYWRIGHT_BASE_URL?.includes('localhost');

  return isLocal && EMAIL_TEST_CONFIG.useInbucketForLocal;
}

/**
 * Get validation rules for a specific email type
 * @param {string} emailType - Type of email to validate
 * @returns {Object} Validation rules
 */
function getEmailValidationRules(emailType) {
  const rules = EMAIL_TEST_CONFIG.validation.emailTypes[emailType];

  if (!rules) {
    console.warn(`⚠️ No validation rules defined for email type: ${emailType}`);
    return {
      subjectContains: [],
      bodyContains: ['iepa'], // At minimum, should contain IEPA branding
      shouldHaveLinks: false,
      shouldHaveAttachments: false,
    };
  }

  return rules;
}

/**
 * Generate a unique test email address for testing
 * @param {string} prefix - Prefix for the email address
 * @returns {string} Unique test email address
 */
function generateTestEmail(prefix = 'test') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}.${timestamp}.${random}@iepa-test.com`;
}

/**
 * Clean up test emails (mark as read, delete if needed)
 * @param {Object} account - Email account configuration
 * @param {Array<string>} subjects - Email subjects to clean up
 */
async function cleanupTestEmails(account, subjects = []) {
  if (!account || shouldUseInbucket()) {
    console.log('📧 Skipping email cleanup (using Inbucket or no account)');
    return;
  }

  console.log(`📧 Cleaning up test emails for ${account.email}...`);

  try {
    const imaps = require('imap-simple');

    const config = {
      imap: {
        user: account.email,
        password: account.password,
        host:
          account.provider === 'gmail' ? 'imap.gmail.com' : 'imap.mailtrap.io',
        port: 993,
        tls: true,
        authTimeout: 10000,
      },
    };

    const connection = await imaps.connect(config);
    await connection.openBox('INBOX');

    // Mark test emails as read
    for (const subject of subjects) {
      const searchCriteria = ['UNSEEN', ['SUBJECT', subject]];
      const fetchOptions = { bodies: [], markSeen: true };

      const messages = await connection.search(searchCriteria, fetchOptions);
      console.log(
        `📧 Marked ${messages.length} emails with subject "${subject}" as read`
      );
    }

    await connection.end();
    console.log('✅ Email cleanup completed');
  } catch (error) {
    console.warn('⚠️ Email cleanup failed:', error.message);
  }
}

/**
 * Test email configuration and connectivity
 * @param {string} accountType - Type of account to test
 * @returns {Promise<boolean>} Whether the configuration is working
 */
async function testEmailConfiguration(accountType = 'primary') {
  console.log(`🧪 Testing email configuration for ${accountType} account...`);

  if (shouldUseInbucket()) {
    console.log('📧 Testing Supabase Inbucket connectivity...');

    try {
      const response = await fetch(
        'http://127.0.0.1:54324/api/v1/mailbox/test'
      );
      const isWorking = response.status === 200 || response.status === 404; // 404 is OK (empty mailbox)

      if (isWorking) {
        console.log('✅ Supabase Inbucket is accessible');
        return true;
      } else {
        console.log('❌ Supabase Inbucket is not accessible');
        return false;
      }
    } catch (error) {
      console.log('❌ Supabase Inbucket connection failed:', error.message);
      return false;
    }
  }

  const account = getEmailTestAccount(accountType);
  if (!account) {
    console.log('❌ Email account not configured');
    return false;
  }

  try {
    const imaps = require('imap-simple');

    const config = {
      imap: {
        user: account.email,
        password: account.password,
        host:
          account.provider === 'gmail' ? 'imap.gmail.com' : 'imap.mailtrap.io',
        port: 993,
        tls: true,
        authTimeout: 10000,
      },
    };

    const connection = await imaps.connect(config);
    await connection.openBox('INBOX');
    await connection.end();

    console.log(`✅ Email configuration for ${accountType} account is working`);
    return true;
  } catch (error) {
    console.log(`❌ Email configuration test failed:`, error.message);
    return false;
  }
}

module.exports = {
  EMAIL_TEST_ACCOUNTS,
  EMAIL_TEST_CONFIG,
  getEmailTestAccount,
  shouldUseInbucket,
  getEmailValidationRules,
  generateTestEmail,
  cleanupTestEmails,
  testEmailConfiguration,
};
