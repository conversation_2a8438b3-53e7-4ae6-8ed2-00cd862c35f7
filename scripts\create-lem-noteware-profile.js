#!/usr/bin/env node

// <PERSON>rip<PERSON> to create a user and profile for Lem Noteware
// Email: <EMAIL>

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

// Create admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createLemNotewareProfile() {
  console.log('🚀 Creating user and profile for Lem Noteware...');
  
  const email = '<EMAIL>';
  const password = 'LemTest123!'; // Strong test password
  
  try {
    // First, check if user already exists
    console.log('🔍 Checking if user already exists...');
    const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      console.error('❌ Error fetching users:', listError);
      return;
    }
    
    let authUser = existingUsers.users.find(user => user.email === email);
    
    if (authUser) {
      console.log('✅ User already exists in auth.users:');
      console.log('📧 Email:', authUser.email);
      console.log('🆔 ID:', authUser.id);
      console.log('✉️ Email Confirmed:', !!authUser.email_confirmed_at);
    } else {
      // Create the auth user
      console.log('👤 Creating new auth user...');
      const { data: createUserData, error: createUserError } = await supabase.auth.admin.createUser({
        email: email,
        password: password,
        email_confirm: true, // Auto-confirm email
        user_metadata: {
          full_name: 'Lem Noteware',
          first_name: 'Lem',
          last_name: 'Noteware'
        }
      });
      
      if (createUserError) {
        console.error('❌ Error creating auth user:', createUserError);
        return;
      }
      
      authUser = createUserData.user;
      console.log('✅ Auth user created successfully:');
      console.log('📧 Email:', authUser.email);
      console.log('🆔 ID:', authUser.id);
      console.log('✉️ Email Confirmed:', !!authUser.email_confirmed_at);
    }
    
    // Check if profile already exists
    console.log('🔍 Checking if user profile already exists...');
    const { data: existingProfile, error: profileCheckError } = await supabase
      .from('iepa_user_profiles')
      .select('*')
      .eq('user_id', authUser.id)
      .single();
    
    if (profileCheckError && profileCheckError.code !== 'PGRST116') {
      console.error('❌ Error checking existing profile:', profileCheckError);
      return;
    }
    
    if (existingProfile) {
      console.log('⚠️ User profile already exists:');
      console.log('📋 Profile data:', {
        name: `${existingProfile.first_name} ${existingProfile.last_name}`,
        email: existingProfile.email,
        phone: existingProfile.phone_number,
        organization: existingProfile.organization,
        address: `${existingProfile.street_address}, ${existingProfile.city}, ${existingProfile.state} ${existingProfile.zip_code}`
      });
      
      // Update the existing profile with new address info
      console.log('🔄 Updating existing profile with new address...');
      const { data: updatedProfile, error: updateError } = await supabase
        .from('iepa_user_profiles')
        .update({
          street_address: '123 Quinny Street',
          city: 'Meow City',
          state: 'CA',
          zip_code: '92117',
          country: 'United States'
        })
        .eq('user_id', authUser.id)
        .select()
        .single();
      
      if (updateError) {
        console.error('❌ Error updating profile:', updateError);
      } else {
        console.log('✅ Profile updated with new address!');
      }
      
      return;
    }
    
    // Create the user profile
    console.log('📝 Creating user profile...');
    const profileData = {
      user_id: authUser.id,
      first_name: 'Lem',
      last_name: 'Noteware',
      email: email,
      phone_number: '5551234568', // Different from test user
      organization: 'Noteware Digital',
      job_title: 'Creative Director',
      street_address: '123 Quinny Street',
      city: 'Meow City',
      state: 'CA',
      zip_code: '92117',
      country: 'United States',
      gender: 'prefer_not_to_say',
      preferred_name_on_badge: 'Lem Noteware',
      imported_from_2024: false
    };
    
    const { data: profileResult, error: profileError } = await supabase
      .from('iepa_user_profiles')
      .insert(profileData)
      .select()
      .single();
    
    if (profileError) {
      console.error('❌ Error creating profile:', profileError);
      return;
    }
    
    console.log('✅ Profile created successfully!');
    console.log('📋 Profile data:', {
      id: profileResult.id,
      name: `${profileResult.first_name} ${profileResult.last_name}`,
      email: profileResult.email,
      phone: profileResult.phone_number,
      organization: profileResult.organization,
      jobTitle: profileResult.job_title,
      address: `${profileResult.street_address}, ${profileResult.city}, ${profileResult.state} ${profileResult.zip_code}`
    });
    
    // Test the profile retrieval
    console.log('\n🧪 Testing profile retrieval...');
    const { data: retrievedProfile, error: retrieveError } = await supabase
      .from('iepa_user_profiles')
      .select('*')
      .eq('user_id', authUser.id)
      .single();
    
    if (retrieveError) {
      console.error('❌ Error retrieving profile:', retrieveError);
    } else {
      console.log('✅ Profile retrieval test successful!');
      console.log('🔗 Profile linked to user ID:', retrievedProfile.user_id);
    }
    
    console.log('\n🎯 Lem Noteware profile creation complete!');
    console.log('📝 Login Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: LemTest123!');
    console.log('\n🔗 Test URLs:');
    console.log('   Login: https://reg.iepa.com/auth/login');
    console.log('   Attendee Form: https://reg.iepa.com/register/attendee');
    console.log('   Speaker Form: https://reg.iepa.com/register/speaker');
    console.log('\n📋 Expected Prefill Data:');
    console.log('   Name: Lem Noteware');
    console.log('   Email: <EMAIL>');
    console.log('   Phone: (*************');
    console.log('   Organization: Noteware Digital');
    console.log('   Job Title: Creative Director');
    console.log('   Address: 123 Quinny Street, Meow City, CA 92117');
    
    console.log('\n🎉 Ready for testing!');
    console.log('Lem can now log in and test the form prefilling functionality.');
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Run the script
createLemNotewareProfile().catch(console.error);
