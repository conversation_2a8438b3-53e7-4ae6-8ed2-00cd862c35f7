-- Fix RLS Policy for iepa_admin_users to prevent 406 errors
-- This migration fixes the circular dependency in admin access checking

-- Drop existing problematic policy
DROP POLICY IF EXISTS "Admin users can view all admin records" ON iepa_admin_users;

-- Create new policy that allows users to check their own admin status
-- This prevents the circular dependency issue
CREATE POLICY "Users can check their own admin status"
    ON iepa_admin_users FOR SELECT
    USING (
        -- Allow users to see their own admin record based on email
        email = auth.jwt() ->> 'email'
        OR
        -- Allow service role to see all records
        auth.jwt() ->> 'role' = 'service_role'
    );

-- Keep the super admin policy for managing other admin users
-- This policy allows super admins to view/manage all admin records
CREATE POLICY "Super admins can view all admin records"
    ON iepa_admin_users FOR SELECT
    USING (
        -- Service role can see everything
        auth.jwt() ->> 'role' = 'service_role'
        OR
        -- Super admins can see all admin records
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.role = 'super_admin'
            AND admin.is_active = true
        )
    );

-- Comment explaining the fix
COMMENT ON POLICY "Users can check their own admin status" ON iepa_admin_users IS 
'Allows users to check if they are an admin by querying their own email. This prevents the circular dependency that was causing 406 errors when non-admin users tried to access admin areas.';

COMMENT ON POLICY "Super admins can view all admin records" ON iepa_admin_users IS 
'Allows super admins to view all admin records for management purposes. Service role always has full access.';
