'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { <PERSON><PERSON>, Badge, Skeleton } from '@/components/ui';
import {
  FiMail,
  FiUser,
  FiCalendar,
  FiExternalLink,
  FiCopy,
  FiPaperclip,
  FiEye,
  FiCode,
  FiX,
} from 'react-icons/fi';

interface EmailLog {
  id: string;
  recipient_email: string;
  recipient_name?: string;
  sender_email: string;
  sender_name?: string;
  subject: string;
  email_type: string;
  status: 'sent' | 'failed' | 'pending';
  sent_at?: string;
  created_at: string;
  error_message?: string;
  content_preview?: string;
  has_attachments?: boolean;
  sendgrid_message_id?: string;
}

interface EmailContentPreviewModalProps {
  email: EmailLog | null;
  isOpen: boolean;
  onClose: () => void;
  onCopySendGridId?: (id: string) => void;
}

export default function EmailContentPreviewModal({
  email,
  isOpen,
  onClose,
  onCopySendGridId,
}: EmailContentPreviewModalProps) {
  const [emailContent, setEmailContent] = useState<{
    html_content?: string;
    text_content?: string;
    attachments?: Array<{ name: string; url: string; size?: number }>;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'html' | 'text'>('html');
  const [error, setError] = useState<string | null>(null);

  // Fetch full email content when modal opens
  useEffect(() => {
    if (isOpen && email) {
      fetchEmailContent(email.id);
    } else {
      setEmailContent(null);
      setError(null);
    }
  }, [isOpen, email]);

  const fetchEmailContent = async (emailId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/email-content/${emailId}`);
      const data = await response.json();

      if (data.success) {
        setEmailContent(data.content);
      } else {
        setError(data.error || 'Failed to load email content');
      }
    } catch (err) {
      setError('Failed to fetch email content');
      console.error('Error fetching email content:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'sent':
        return { color: 'text-green-600', bgColor: 'bg-green-50', label: 'Sent' };
      case 'failed':
        return { color: 'text-red-600', bgColor: 'bg-red-50', label: 'Failed' };
      case 'pending':
        return { color: 'text-yellow-600', bgColor: 'bg-yellow-50', label: 'Pending' };
      default:
        return { color: 'text-gray-600', bgColor: 'bg-gray-50', label: 'Unknown' };
    }
  };

  if (!email) return null;

  const statusConfig = getStatusConfig(email.status);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <FiMail className="w-5 h-5" />
            Email Content Preview
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Email Header Info */}
          <div className="flex-shrink-0 border-b border-gray-200 pb-4 mb-4">
            <div className="space-y-3">
              {/* Subject and Status */}
              <div className="flex items-start justify-between gap-4">
                <h3 className="text-lg font-semibold text-gray-900 flex-1">
                  {email.subject}
                </h3>
                <Badge
                  variant="outline"
                  className={`${statusConfig.color} ${statusConfig.bgColor} border-current`}
                >
                  {statusConfig.label}
                </Badge>
              </div>

              {/* Email Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <FiUser className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">To:</span>
                    <span className="font-medium">
                      {email.recipient_name ? (
                        <>
                          {email.recipient_name} <span className="text-gray-500">({email.recipient_email})</span>
                        </>
                      ) : (
                        email.recipient_email
                      )}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FiMail className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">From:</span>
                    <span className="font-medium">
                      {email.sender_name ? (
                        <>
                          {email.sender_name} <span className="text-gray-500">({email.sender_email})</span>
                        </>
                      ) : (
                        email.sender_email
                      )}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <FiCalendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">
                      {email.sent_at ? 'Sent:' : 'Created:'}
                    </span>
                    <span className="font-medium">
                      {formatDate(email.sent_at || email.created_at)}
                    </span>
                  </div>
                  {email.sendgrid_message_id && (
                    <div className="flex items-center gap-2">
                      <FiExternalLink className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">SendGrid ID:</span>
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded font-mono">
                        {email.sendgrid_message_id}
                      </code>
                      {onCopySendGridId && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onCopySendGridId(email.sendgrid_message_id!)}
                          className="h-6 px-2"
                        >
                          <FiCopy className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Error Message */}
              {email.error_message && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="text-sm text-red-800">
                    <strong>Error:</strong> {email.error_message}
                  </div>
                </div>
              )}

              {/* Attachments */}
              {email.has_attachments && emailContent?.attachments && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="flex items-center gap-2 mb-2">
                    <FiPaperclip className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">Attachments</span>
                  </div>
                  <div className="space-y-1">
                    {emailContent.attachments.map((attachment, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <span className="text-blue-700">{attachment.name}</span>
                        {attachment.size && (
                          <span className="text-blue-600">({(attachment.size / 1024).toFixed(1)} KB)</span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Content View Controls */}
          <div className="flex-shrink-0 flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'html' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('html')}
                disabled={!emailContent?.html_content}
              >
                <FiEye className="w-4 h-4 mr-2" />
                HTML View
              </Button>
              <Button
                variant={viewMode === 'text' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('text')}
                disabled={!emailContent?.text_content}
              >
                <FiCode className="w-4 h-4 mr-2" />
                Text View
              </Button>
            </div>
          </div>

          {/* Content Display */}
          <div className="flex-1 overflow-auto border border-gray-200 rounded-md">
            {loading ? (
              <div className="p-6 space-y-4">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-32 w-full" />
              </div>
            ) : error ? (
              <div className="p-6 text-center text-red-600">
                <p className="font-medium">Failed to load email content</p>
                <p className="text-sm mt-1">{error}</p>
              </div>
            ) : emailContent ? (
              <div className="p-6">
                {viewMode === 'html' && emailContent.html_content ? (
                  <div
                    className="prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: emailContent.html_content }}
                  />
                ) : viewMode === 'text' && emailContent.text_content ? (
                  <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                    {emailContent.text_content}
                  </pre>
                ) : email.content_preview ? (
                  <div className="text-gray-600">
                    <p className="font-medium mb-2">Content Preview:</p>
                    <p className="text-sm">{email.content_preview}</p>
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    <p>No email content available</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-6 text-center text-gray-500">
                <p>Loading email content...</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
