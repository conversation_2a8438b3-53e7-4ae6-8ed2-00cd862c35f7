// API Route: Test Email Configuration Service
// Test the email configuration service directly

import { NextRequest, NextResponse } from 'next/server';
import { emailConfigService } from '@/services/email-config';

export async function GET(request: NextRequest) {
  try {
    console.log('[EMAIL-CONFIG-TEST] Testing email configuration service...');

    // Test getting email configuration
    const config = await emailConfigService.getEmailConfig();
    
    console.log('[EMAIL-CONFIG-TEST] Configuration loaded:', config);

    return NextResponse.json({
      success: true,
      message: 'Email configuration service is working',
      config: config,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('[EMAIL-CONFIG-TEST] Test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error occurred',
      fallback: 'Using environment variable fallback',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { testEmail } = await request.json();
    
    if (!testEmail) {
      return NextResponse.json({
        success: false,
        error: 'testEmail is required'
      }, { status: 400 });
    }

    console.log('[EMAIL-CONFIG-TEST] Testing email sending with database config...');

    // Test sending email with database configuration
    const success = await emailConfigService.testConfiguration(testEmail);
    
    return NextResponse.json({
      success: true,
      emailSent: success,
      message: success ? 'Test email sent successfully' : 'Test email failed to send',
      testEmail: testEmail,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('[EMAIL-CONFIG-TEST] Email test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error occurred',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
