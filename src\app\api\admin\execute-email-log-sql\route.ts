import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

export async function POST() {
  try {
    console.log('[EXECUTE-EMAIL-LOG-SQL] Setting up email log table...');

    // Read the SQL file
    const sqlFilePath = join(process.cwd(), '.docs/01-setup-config/email-log-table-setup.sql');
    let sqlContent: string;
    
    try {
      sqlContent = readFileSync(sqlFilePath, 'utf8');
      console.log('[EXECUTE-EMAIL-LOG-SQL] SQL file loaded successfully');
    } catch (fileError) {
      console.error('[EXECUTE-EMAIL-LOG-SQL] Failed to read SQL file:', fileError);
      
      // Fallback to inline SQL
      sqlContent = `
        -- Create email log table
        CREATE TABLE IF NOT EXISTS public.iepa_email_log (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          
          -- Email details
          recipient_email VARCHAR(255) NOT NULL,
          recipient_name VARCHAR(255),
          sender_email VARCHAR(255) NOT NULL,
          sender_name VARCHAR(255),
          subject VARCHAR(500) NOT NULL,
          
          -- Email classification
          email_type VARCHAR(50) NOT NULL,
          template_used VARCHAR(100),
          
          -- Content reference
          content_preview TEXT,
          has_attachments BOOLEAN DEFAULT FALSE,
          
          -- Delivery status
          status VARCHAR(20) NOT NULL DEFAULT 'pending',
          sendgrid_message_id VARCHAR(255),
          error_message TEXT,
          
          -- Related records
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          registration_id UUID,
          registration_type VARCHAR(20),
          payment_id UUID,
          
          -- Timestamps
          sent_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_iepa_email_log_recipient ON public.iepa_email_log(recipient_email);
        CREATE INDEX IF NOT EXISTS idx_iepa_email_log_type ON public.iepa_email_log(email_type);
        CREATE INDEX IF NOT EXISTS idx_iepa_email_log_status ON public.iepa_email_log(status);
        CREATE INDEX IF NOT EXISTS idx_iepa_email_log_user_id ON public.iepa_email_log(user_id);
        CREATE INDEX IF NOT EXISTS idx_iepa_email_log_registration ON public.iepa_email_log(registration_id, registration_type);
        CREATE INDEX IF NOT EXISTS idx_iepa_email_log_created_at ON public.iepa_email_log(created_at);

        -- Create trigger function for updated_at
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';

        -- Create trigger
        DROP TRIGGER IF EXISTS update_iepa_email_log_updated_at ON public.iepa_email_log;
        CREATE TRIGGER update_iepa_email_log_updated_at 
          BEFORE UPDATE ON public.iepa_email_log 
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

        -- Enable RLS
        ALTER TABLE public.iepa_email_log ENABLE ROW LEVEL SECURITY;

        -- Create policies
        DROP POLICY IF EXISTS "Service role full access" ON public.iepa_email_log;
        DROP POLICY IF EXISTS "Admins can view all email logs" ON public.iepa_email_log;
        DROP POLICY IF EXISTS "Admins can insert email logs" ON public.iepa_email_log;

        CREATE POLICY "Service role full access" 
          ON public.iepa_email_log FOR ALL 
          TO service_role 
          USING (true) 
          WITH CHECK (true);

        CREATE POLICY "Admins can view all email logs" 
          ON public.iepa_email_log FOR SELECT 
          TO authenticated 
          USING (
            EXISTS (
              SELECT 1 FROM public.iepa_admin_users 
              WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
              AND is_active = true
            )
          );

        CREATE POLICY "Admins can insert email logs" 
          ON public.iepa_email_log FOR INSERT 
          TO authenticated 
          WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.iepa_admin_users 
              WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
              AND is_active = true
            )
          );

        -- Test the table
        INSERT INTO public.iepa_email_log (
          recipient_email,
          sender_email,
          subject,
          email_type,
          content_preview,
          status
        ) VALUES (
          '<EMAIL>',
          '<EMAIL>',
          'Email Log Table Setup Test',
          'setup_test',
          'This is a test email log entry to verify the table was created successfully.',
          'sent'
        );

        -- Clean up test record
        DELETE FROM public.iepa_email_log WHERE email_type = 'setup_test';
      `;
    }

    // Since we can't execute raw SQL directly through the client, we'll try a different approach
    // Let's create the table by attempting operations and handling the responses

    console.log('[EXECUTE-EMAIL-LOG-SQL] Attempting to create table through operations...');

    // First, try to insert a test record to see if table exists
    const testRecord = {
      recipient_email: '<EMAIL>',
      sender_email: '<EMAIL>',
      subject: 'Email Log Setup Test',
      email_type: 'setup_test',
      content_preview: 'Testing email log table creation',
      status: 'sent'
    };

    const { data: insertData, error: insertError } = await supabase
      .from('iepa_email_log')
      .insert([testRecord])
      .select();

    if (insertError) {
      if (insertError.message && insertError.message.includes('does not exist')) {
        // Table doesn't exist, return SQL for manual creation
        return NextResponse.json({
          success: false,
          error: 'Email log table does not exist',
          requiresManualSetup: true,
          sqlToExecute: sqlContent,
          instructions: {
            step1: 'Go to your Supabase project dashboard',
            step2: 'Navigate to SQL Editor',
            step3: 'Copy and paste the SQL provided in the sqlToExecute field',
            step4: 'Execute the SQL to create the table and all related components',
            step5: 'Return to this page and try the setup again'
          }
        });
      } else {
        // Some other error
        console.error('[EXECUTE-EMAIL-LOG-SQL] Insert error:', insertError);
        return NextResponse.json({
          success: false,
          error: insertError.message,
          details: 'Failed to test email log table'
        }, { status: 500 });
      }
    }

    // Table exists and insert worked, clean up test record
    if (insertData && insertData[0]) {
      await supabase
        .from('iepa_email_log')
        .delete()
        .eq('id', insertData[0].id);
    }

    console.log('[EXECUTE-EMAIL-LOG-SQL] Email log table is working correctly');

    return NextResponse.json({
      success: true,
      message: 'Email log table is already set up and working correctly',
      tableExists: true,
      testPassed: true,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[EXECUTE-EMAIL-LOG-SQL] Setup failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to set up email log table',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
