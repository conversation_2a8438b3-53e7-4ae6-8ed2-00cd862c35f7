// Dashboard types for IEPA 2025 Conference Registration

import type { Database } from './database';

// Database table types
export type AttendeeRegistration =
  Database['public']['Tables']['iepa_attendee_registrations']['Row'];
export type SpeakerRegistration =
  Database['public']['Tables']['iepa_speaker_registrations']['Row'];
export type SponsorRegistration =
  Database['public']['Tables']['iepa_sponsor_registrations']['Row'];
export type Payment = Database['public']['Tables']['iepa_payments']['Row'];

// Dashboard statistics types
export interface DashboardStats {
  totalAttendees: number;
  totalSpeakers: number;
  totalSponsors: number;
  totalRevenue: number;
  pendingPayments: number;
  completedPayments: number;
  golfParticipants: number;
  mealSelections: Record<string, number>;
}

// Registration status types
export type RegistrationStatus = 'pending' | 'confirmed' | 'cancelled';
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded';

// Enhanced registration types with computed fields
export interface EnhancedAttendeeRegistration extends AttendeeRegistration {
  status: RegistrationStatus;
  daysUntilEvent: number;
  totalMeals: number;
}

export interface EnhancedSpeakerRegistration extends SpeakerRegistration {
  status: RegistrationStatus;
  hasPresentation: boolean;
  hasHeadshot: boolean;
}

export interface EnhancedSponsorRegistration extends SponsorRegistration {
  status: RegistrationStatus;
  hasAssets: boolean;
}

// Chart data types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface TimeSeriesDataPoint {
  date: string;
  value: number;
  category?: string;
}

// Filter types
export interface DashboardFilters {
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  registrationType: string[];
  paymentStatus: PaymentStatus[];
  searchQuery: string;
}

// Table configuration types
export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: unknown, row: T) => React.ReactNode;
}

export interface TableConfig<T> {
  columns: TableColumn<T>[];
  defaultSort?: {
    key: keyof T;
    direction: 'asc' | 'desc';
  };
  pageSize?: number;
  searchable?: boolean;
  exportable?: boolean;
}

// Action types
export interface BulkAction<T> {
  id: string;
  label: string;
  icon?: string;
  action: (selectedRows: T[]) => Promise<void>;
  confirmationRequired?: boolean;
  confirmationMessage?: string;
}

// Modal types
export interface ModalProps<T> {
  isOpen: boolean;
  onClose: () => void;
  data: T | null;
  onSave?: (data: T) => Promise<void>;
  onDelete?: (id: string) => Promise<void>;
}

// Export types
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  includeColumns: string[];
  filters?: DashboardFilters;
}

// Dashboard section types
export type DashboardSection =
  | 'overview'
  | 'attendees'
  | 'speakers'
  | 'sponsors'
  | 'payments'
  | 'analytics';

// API response types
export interface DashboardDataResponse {
  stats: DashboardStats;
  attendees: EnhancedAttendeeRegistration[];
  speakers: EnhancedSpeakerRegistration[];
  sponsors: EnhancedSponsorRegistration[];
  payments: Payment[];
  chartData: {
    registrationTrends: TimeSeriesDataPoint[];
    paymentBreakdown: ChartDataPoint[];
    demographics: ChartDataPoint[];
  };
}

// Error types
export interface DashboardError {
  code: string;
  message: string;
  details?: unknown;
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  error: DashboardError | null;
  lastUpdated: Date | null;
}
