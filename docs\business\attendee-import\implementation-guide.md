# IEPA Attendee Import Implementation Guide

## Overview

This guide documents the implementation of the attendee import system for the IEPA conference registration application. The system allows importing historical attendee data from previous conferences to create user accounts and pre-populate registration forms for returning attendees.

## Features Implemented

### 1. Database Schema Enhancement

**New Tables:**

- `iepa_user_profiles` - Stores general user profile information
- `iepa_historical_registrations` - Stores past event registration data

**Key Benefits:**

- Separates general user data from event-specific data
- Supports multiple events per user
- Maintains data integrity with proper foreign key relationships
- Includes import tracking fields

### 2. Import Utilities

**File:** `src/lib/attendee-import.ts`

**Key Functions:**

- `processAttendeeData()` - Transforms raw JSON data into structured format
- `createUserAccount()` - Creates Supabase auth users with admin privileges
- `createUserProfile()` - Creates user profile records
- `createHistoricalRegistration()` - Creates historical event records
- `importAttendeesFromJson()` - Main import orchestration function

**Features:**

- Batch processing to avoid database overload
- Duplicate detection and handling
- Secure password generation
- Comprehensive error handling and reporting
- Dry-run mode for testing

### 3. Import Script

**File:** `scripts/import-2024-attendees.ts`

**Usage:**

```bash
# Dry run (no changes made)
npm run import-attendees -- --dry-run

# Full import with default settings
npm run import-attendees

# Custom batch size
npm run import-attendees -- --batch-size 5

# Don't skip existing users
npm run import-attendees -- --skip-existing false
```

**Output Files:**

- `generated-passwords.json` - Secure passwords for new accounts
- `import-report.json` - Detailed import results and errors

### 4. User Profile Utilities

**File:** `src/lib/user-profile-utils.ts`

**Key Functions:**

- `getUserProfile()` - Fetch user profile by ID
- `getUserProfileByEmail()` - Fetch user profile by email
- `getHistoricalRegistrations()` - Get past event data
- `getUserProfileWithHistory()` - Complete user data with history
- `getFormDefaults()` - Pre-populate form data from profile
- `isReturningAttendee()` - Check if user has attended before

## Data Mapping

### From 2024 JSON to Database

| JSON Field                       | Database Table                  | Database Field            |
| -------------------------------- | ------------------------------- | ------------------------- |
| `Attendee First Name`            | `iepa_user_profiles`            | `first_name`              |
| `Attendee Last Name`             | `iepa_user_profiles`            | `last_name`               |
| `Email For Attendee List`        | `iepa_user_profiles`            | `email`                   |
| `Phone Number For Attendee List` | `iepa_user_profiles`            | `phone_number`            |
| `Attendee Organization`          | `iepa_user_profiles`            | `organization`            |
| `Attendee Job Title`             | `iepa_user_profiles`            | `job_title`               |
| `Street`                         | `iepa_user_profiles`            | `street_address`          |
| `City`                           | `iepa_user_profiles`            | `city`                    |
| `State`                          | `iepa_user_profiles`            | `state`                   |
| `Zip`                            | `iepa_user_profiles`            | `zip_code`                |
| `Gender`                         | `iepa_user_profiles`            | `gender`                  |
| `Name on Badge`                  | `iepa_user_profiles`            | `preferred_name_on_badge` |
| `Attendee Type`                  | `iepa_historical_registrations` | `attendee_type`           |
| `Attendee Type (IEPA)`           | `iepa_historical_registrations` | `attendee_type_iepa`      |
| `Date Added`                     | `iepa_historical_registrations` | `registration_date`       |
| `Status`                         | `iepa_historical_registrations` | `status`                  |
| `Meals`                          | `iepa_historical_registrations` | `meals`                   |
| `Special Dietary Needs`          | `iepa_historical_registrations` | `special_dietary_needs`   |
| `Golf Tournament?`               | `iepa_historical_registrations` | `golf_tournament`         |
| `Are you renting clubs?`         | `iepa_historical_registrations` | `golf_club_rental`        |
| `Golf Total`                     | `iepa_historical_registrations` | `golf_total`              |
| `Grand order total`              | `iepa_historical_registrations` | `grand_total`             |

## Security Considerations

### Password Generation

- 16-character passwords with mixed case, numbers, and symbols
- Passwords stored securely in output file
- Email confirmation skipped for imported users
- Users should be prompted to change passwords on first login

### Row Level Security (RLS)

- Users can only access their own profile data
- Users can only view their own historical registrations
- Service role has full access for admin operations
- Proper foreign key constraints maintain data integrity

### Data Privacy

- Original JSON data stored in `original_data` JSONB field for audit purposes
- Import tracking fields identify imported vs. manually created users
- Sensitive data handled according to privacy best practices

## Usage Examples

### 1. Running the Import

```bash
# First, ensure environment variables are set
export NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Run a dry run first to test
npm run import-attendees -- --dry-run

# If dry run looks good, run the actual import
npm run import-attendees
```

### 2. Pre-populating Registration Forms

```typescript
import { userProfileUtils } from '@/lib/user-profile-utils';

// In your registration form component
const { data: user } = await supabase.auth.getUser();
if (user?.user) {
  const profileWithHistory = await userProfileUtils.getUserProfileWithHistory(
    user.user.id
  );
  const formDefaults = userProfileUtils.getFormDefaults(profileWithHistory);

  // Use formDefaults to pre-populate your form
  setFormData(formDefaults);
}
```

### 3. Checking Returning Attendee Status

```typescript
import { userProfileUtils } from '@/lib/user-profile-utils';

const isReturning = await userProfileUtils.isReturningAttendee(userId);
if (isReturning) {
  // Show welcome back message
  // Offer to use previous preferences
}
```

## Database Setup

1. **Run the updated schema:**

   ```sql
   -- Execute the contents of src/lib/database-schema.sql in your Supabase SQL editor
   ```

2. **Verify tables were created:**

   ```sql
   SELECT table_name FROM information_schema.tables
   WHERE table_schema = 'public'
   AND table_name LIKE 'iepa_%';
   ```

3. **Check RLS policies:**
   ```sql
   SELECT schemaname, tablename, policyname
   FROM pg_policies
   WHERE tablename LIKE 'iepa_%';
   ```

## Troubleshooting

### Common Issues

1. **Environment Variables Missing**

   - Ensure `NEXT_PUBLIC_SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY` are set
   - Service role key is required for creating user accounts

2. **Duplicate Email Errors**

   - Use `--skip-existing true` to skip users that already exist
   - Check the error report for specific duplicate issues

3. **Permission Errors**

   - Verify RLS policies are correctly applied
   - Ensure service role has proper permissions

4. **Import Failures**
   - Check the import report for detailed error messages
   - Verify JSON file format matches expected structure
   - Ensure database schema is up to date

### Monitoring Import Progress

The import script provides real-time progress updates:

- Batch processing status
- Individual user import results
- Error reporting with specific details
- Final summary statistics

## Next Steps

1. **User Communication**

   - Send welcome emails to imported users
   - Provide secure password distribution
   - Include instructions for first login

2. **Form Enhancement**

   - Update registration forms to use pre-populated data
   - Add "returning attendee" indicators
   - Implement preference suggestions

3. **Admin Tools**

   - Create admin interface for managing imported users
   - Add bulk operations for user management
   - Implement data export capabilities

4. **Testing**
   - Test registration flow with imported users
   - Verify data integrity and relationships
   - Validate security and privacy controls
