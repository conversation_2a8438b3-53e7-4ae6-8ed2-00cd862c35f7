// Admin Discount Codes Management API
// GET /api/admin/discount-codes - List all discount codes
// POST /api/admin/discount-codes - Create new discount code

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';
import { stripe } from '@/lib/stripe';

// GET - List all discount codes
export async function GET(request: NextRequest) {
  try {
    const supabaseAdmin = createSupabaseAdmin();
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    // Get discount codes with usage statistics
    const { data: discountCodes, error, count } = await supabaseAdmin
      .from('iepa_discount_codes')
      .select(`
        *,
        usage_count:iepa_discount_usage(count)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching discount codes:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch discount codes' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: discountCodes,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    });
  } catch (error) {
    console.error('Error in GET /api/admin/discount-codes:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new discount code
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      code,
      name,
      description,
      discountType,
      discountValue,
      maxUses,
      maxUsesPerUser,
      validFrom,
      validUntil,
      minimumAmount,
      applicableRegistrationTypes,
      createStripeCoupon,
      userId, // Admin user creating the code
    } = body;

    // Validate required fields
    if (!code || !name || !discountType || !discountValue) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: code, name, discountType, discountValue',
        },
        { status: 400 }
      );
    }

    // Validate discount type and value
    if (!['percentage', 'fixed_amount'].includes(discountType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid discount type' },
        { status: 400 }
      );
    }

    if (discountValue <= 0) {
      return NextResponse.json(
        { success: false, error: 'Discount value must be greater than 0' },
        { status: 400 }
      );
    }

    if (discountType === 'percentage' && discountValue > 100) {
      return NextResponse.json(
        { success: false, error: 'Percentage discount cannot exceed 100%' },
        { status: 400 }
      );
    }

    const supabaseAdmin = createSupabaseAdmin();

    // Check if code already exists
    const { data: existingCode } = await supabaseAdmin
      .from('iepa_discount_codes')
      .select('id')
      .eq('code', code.toUpperCase())
      .single();

    if (existingCode) {
      return NextResponse.json(
        { success: false, error: 'Discount code already exists' },
        { status: 400 }
      );
    }

    let stripeCouponId = null;

    // Create Stripe coupon if requested
    if (createStripeCoupon) {
      try {
        const couponData: any = {
          id: code.toLowerCase().replace(/[^a-z0-9]/g, '_'),
          name: name,
          duration: 'once',
        };

        if (discountType === 'percentage') {
          couponData.percent_off = discountValue;
        } else {
          couponData.amount_off = Math.round(discountValue * 100); // Convert to cents
          couponData.currency = 'usd';
        }

        if (maxUses) {
          couponData.max_redemptions = maxUses;
        }

        if (validUntil) {
          couponData.redeem_by = Math.floor(new Date(validUntil).getTime() / 1000);
        }

        const stripeCoupon = await stripe.coupons.create(couponData);
        stripeCouponId = stripeCoupon.id;
      } catch (stripeError: any) {
        console.error('Error creating Stripe coupon:', stripeError);
        return NextResponse.json(
          {
            success: false,
            error: `Failed to create Stripe coupon: ${stripeError.message}`,
          },
          { status: 400 }
        );
      }
    }

    // Create discount code in database
    const { data: newDiscountCode, error: insertError } = await supabaseAdmin
      .from('iepa_discount_codes')
      .insert([
        {
          code: code.toUpperCase(),
          name,
          description,
          discount_type: discountType,
          discount_value: discountValue,
          stripe_coupon_id: stripeCouponId,
          max_uses: maxUses || null,
          max_uses_per_user: maxUsesPerUser || 1,
          valid_from: validFrom || new Date().toISOString(),
          valid_until: validUntil || null,
          minimum_amount: minimumAmount || null,
          applicable_registration_types: applicableRegistrationTypes || null,
          created_by: userId,
          updated_by: userId,
        },
      ])
      .select()
      .single();

    if (insertError) {
      console.error('Error creating discount code:', insertError);
      
      // Clean up Stripe coupon if it was created
      if (stripeCouponId) {
        try {
          await stripe.coupons.del(stripeCouponId);
        } catch (cleanupError) {
          console.error('Error cleaning up Stripe coupon:', cleanupError);
        }
      }

      return NextResponse.json(
        { success: false, error: 'Failed to create discount code' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: newDiscountCode,
      message: 'Discount code created successfully',
    });
  } catch (error) {
    console.error('Error in POST /api/admin/discount-codes:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
