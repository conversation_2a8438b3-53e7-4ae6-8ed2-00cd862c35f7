// Golf Add-On Eligibility API
// Check if user is eligible to add golf to their existing registration

import { NextRequest, NextResponse } from 'next/server';
import { checkGolfAddOnEligibility } from '@/services/golfAddOn';

export async function GET(request: NextRequest) {
  try {
    console.log('🏌️ Golf add-on eligibility check request');

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const registrationId = searchParams.get('registrationId');

    // Validate required parameters
    if (!userId) {
      console.error('❌ Missing userId parameter');
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Checking eligibility for user:', userId, 'registration:', registrationId);

    // Check eligibility
    const eligibility = await checkGolfAddOnEligibility(userId, registrationId || undefined);

    console.log('✅ Eligibility check result:', eligibility);

    return NextResponse.json({
      success: true,
      data: eligibility,
    });

  } catch (error) {
    console.error('❌ Error in golf add-on eligibility check:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🏌️ Golf add-on eligibility check (POST) request');

    const body = await request.json();
    const { userId, registrationId } = body;

    // Validate required parameters
    if (!userId) {
      console.error('❌ Missing userId in request body');
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Checking eligibility for user:', userId, 'registration:', registrationId);

    // Check eligibility
    const eligibility = await checkGolfAddOnEligibility(userId, registrationId);

    console.log('✅ Eligibility check result:', eligibility);

    return NextResponse.json({
      success: true,
      data: eligibility,
    });

  } catch (error) {
    console.error('❌ Error in golf add-on eligibility check:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    );
  }
}
