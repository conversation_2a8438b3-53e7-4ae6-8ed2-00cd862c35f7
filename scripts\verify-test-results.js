#!/usr/bin/env node

/**
 * IEPA Conference Registration - Test Results Verification Script
 * 
 * This script verifies that the test user journey actually created
 * the expected database records and system state.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Test user email from the test run
const TEST_USER_EMAIL = '<EMAIL>';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function verifyTestResults() {
  log('🔍 IEPA Test Results Verification', 'bright');
  log('=' .repeat(50), 'cyan');
  
  if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    log('❌ Missing Supabase configuration', 'red');
    log('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set', 'yellow');
    process.exit(1);
  }
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
  
  try {
    log(`📧 Checking for test user: ${TEST_USER_EMAIL}`, 'blue');
    
    // Check if user profile was created
    log('\n1. Checking iepa_user_profiles table...', 'cyan');
    const { data: userProfiles, error: profileError } = await supabase
      .from('iepa_user_profiles')
      .select('*')
      .eq('email', TEST_USER_EMAIL);
    
    if (profileError) {
      log(`❌ Error querying user profiles: ${profileError.message}`, 'red');
    } else if (userProfiles && userProfiles.length > 0) {
      log('✅ User profile found!', 'green');
      const profile = userProfiles[0];
      log(`   Name: ${profile.first_name} ${profile.last_name}`, 'cyan');
      log(`   Organization: ${profile.organization}`, 'cyan');
      log(`   Created: ${profile.created_at}`, 'cyan');
    } else {
      log('⚠️ No user profile found', 'yellow');
    }
    
    // Check if attendee registration was created
    log('\n2. Checking iepa_attendee_registrations table...', 'cyan');
    const { data: attendeeRegs, error: attendeeError } = await supabase
      .from('iepa_attendee_registrations')
      .select('*')
      .eq('email', TEST_USER_EMAIL);
    
    if (attendeeError) {
      log(`❌ Error querying attendee registrations: ${attendeeError.message}`, 'red');
    } else if (attendeeRegs && attendeeRegs.length > 0) {
      log('✅ Attendee registration found!', 'green');
      const registration = attendeeRegs[0];
      log(`   Registration Type: ${registration.registration_type}`, 'cyan');
      log(`   Payment Status: ${registration.payment_status}`, 'cyan');
      log(`   Total: $${registration.grand_total}`, 'cyan');
      log(`   Created: ${registration.created_at}`, 'cyan');
    } else {
      log('⚠️ No attendee registration found', 'yellow');
    }
    
    // Check auth.users table (if accessible)
    log('\n3. Checking auth.users table...', 'cyan');
    const { data: authUsers, error: authError } = await supabase
      .from('auth.users')
      .select('id, email, created_at, email_confirmed_at')
      .eq('email', TEST_USER_EMAIL);
    
    if (authError) {
      log(`⚠️ Cannot access auth.users table: ${authError.message}`, 'yellow');
    } else if (authUsers && authUsers.length > 0) {
      log('✅ Auth user found!', 'green');
      const user = authUsers[0];
      log(`   User ID: ${user.id}`, 'cyan');
      log(`   Email Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`, 'cyan');
      log(`   Created: ${user.created_at}`, 'cyan');
    } else {
      log('⚠️ No auth user found', 'yellow');
    }
    
    // Check for any recent test users
    log('\n4. Checking for recent test users...', 'cyan');
    const { data: recentUsers, error: recentError } = await supabase
      .from('iepa_user_profiles')
      .select('email, first_name, last_name, created_at')
      .or('email.like.%iepa-test.com,email.like.%new-user-%')
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (recentError) {
      log(`❌ Error querying recent test users: ${recentError.message}`, 'red');
    } else if (recentUsers && recentUsers.length > 0) {
      log(`✅ Found ${recentUsers.length} recent test user(s):`, 'green');
      recentUsers.forEach((user, index) => {
        log(`   ${index + 1}. ${user.first_name} ${user.last_name} (${user.email}) - ${user.created_at}`, 'cyan');
      });
    } else {
      log('⚠️ No recent test users found', 'yellow');
    }
    
    // Check email logs
    log('\n5. Checking iepa_email_logs table...', 'cyan');
    const { data: emailLogs, error: emailError } = await supabase
      .from('iepa_email_logs')
      .select('*')
      .eq('recipient_email', TEST_USER_EMAIL)
      .order('created_at', { ascending: false })
      .limit(3);
    
    if (emailError) {
      log(`⚠️ Cannot access email logs: ${emailError.message}`, 'yellow');
    } else if (emailLogs && emailLogs.length > 0) {
      log(`✅ Found ${emailLogs.length} email log(s):`, 'green');
      emailLogs.forEach((email, index) => {
        log(`   ${index + 1}. ${email.email_type} - ${email.status} - ${email.created_at}`, 'cyan');
      });
    } else {
      log('⚠️ No email logs found', 'yellow');
    }
    
    log('\n📊 Verification Summary:', 'bright');
    log('=' .repeat(30), 'cyan');
    
    const hasProfile = userProfiles && userProfiles.length > 0;
    const hasRegistration = attendeeRegs && attendeeRegs.length > 0;
    const hasEmailLogs = emailLogs && emailLogs.length > 0;
    
    if (hasProfile && hasRegistration) {
      log('🎉 SUCCESS: Test user journey completed successfully!', 'green');
      log('✅ User account created', 'green');
      log('✅ User profile saved', 'green');
      log('✅ Registration submitted', 'green');
      if (hasEmailLogs) {
        log('✅ Email notifications sent', 'green');
      } else {
        log('⚠️ Email notifications not found (may be disabled in test)', 'yellow');
      }
    } else if (hasProfile) {
      log('⚠️ PARTIAL SUCCESS: User created but registration incomplete', 'yellow');
      log('✅ User account created', 'green');
      log('✅ User profile saved', 'green');
      log('❌ Registration not submitted', 'red');
    } else {
      log('❌ FAILURE: Test user not found in database', 'red');
      log('This could indicate:', 'yellow');
      log('- Authentication failed during test', 'yellow');
      log('- Database connection issues', 'yellow');
      log('- Form submission errors', 'yellow');
    }
    
  } catch (error) {
    log(`💥 Verification failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run verification
if (require.main === module) {
  verifyTestResults();
}

module.exports = { verifyTestResults };
