// PDF Generation Types for IEPA Conference Registration
// Type definitions for PDF receipts

export interface AttendeeRegistrationData {
  id: string;
  registrationType: string;
  firstName: string;
  lastName: string;
  nameOnBadge: string;
  email: string;
  phoneNumber: string;
  organization: string;
  jobTitle: string;
  streetAddress: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  golfTournament: boolean;
  golfClubRental: boolean;
  golfClubHandedness: string;
  // Night staying options
  nightOne?: boolean; // September 15-16, 2025 (Monday night)
  nightTwo?: boolean; // September 16-17, 2025 (Tuesday night)
  meals: {
    [key: string]: boolean;
  };
  dietaryNeeds: string;
  registrationTotal: number;
  golfTotal: number;
  golfClubRentalTotal: number;
  mealTotal: number;
  grandTotal: number;
  paymentStatus: string;
  paymentId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SpeakerRegistrationData {
  id: string;
  fullName: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  organizationName: string;
  jobTitle: string;
  bio: string;
  presentationTitle: string;
  presentationDescription: string;
  presentationDuration: string;
  targetAudience: string;
  learningObjectives: string;
  speakerExperience: string;
  previousSpeaking: string;
  equipmentNeeds: string;
  specialRequests: string;
  presentationFileUrl?: string;
  headshotUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SponsorRegistrationData {
  id: string;
  sponsorName: string;
  sponsorUrl: string;
  sponsorImageUrl?: string;
  sponsorDescription: string;
  sponsorshipLevel: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  contactTitle: string;
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingZip: string;
  billingCountry: string;
  companyDescription: string;
  marketingGoals: string;
  exhibitRequirements: string;
  specialRequests: string;
  attendeeCount: string;
  attendeeNames: string;
  sponsorshipTotal: number;
  paymentStatus: string;
  paymentId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PDFReceiptData {
  type: 'attendee' | 'speaker' | 'sponsor';
  registrationData:
    | AttendeeRegistrationData
    | SpeakerRegistrationData
    | SponsorRegistrationData;
  receiptNumber: string;
  issueDate: string;
  paymentMethod?: string;
  transactionId?: string;
  lineItems: PDFLineItem[];
  subtotal: number;
  tax: number;
  total: number;
}

export interface PDFLineItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  category?: 'registration' | 'golf' | 'meals' | 'sponsorship' | 'other';
}

export interface PDFCompanyInfo {
  name: string;
  fullName: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  phone: string;
  email: string;
  supportEmail?: string; // Optional support email for receipt questions
  website: string;
  taxId?: string;
}

export interface PDFConferenceInfo {
  name: string;
  year: number;
  dates: {
    start: string;
    end: string;
  };
  venue: {
    name: string;
    address: string;
    city: string;
    state: string;
  };
}

export interface PDFGenerationOptions {
  includeWatermark?: boolean;
  includeLogo?: boolean;
  format?: 'A4' | 'Letter';
  orientation?: 'portrait' | 'landscape';
  margins?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface PDFGenerationResult {
  success: boolean;
  pdfBuffer?: Buffer;
  fileName?: string;
  filePath?: string;
  publicUrl?: string;
  error?: string;
}

export interface PDFStorageResult {
  success: boolean;
  fileName?: string;
  filePath?: string;
  publicUrl?: string;
  error?: string;
}

// PDF Template Types
export interface PDFTemplate {
  type: 'receipt';
  registrationType: 'attendee' | 'speaker' | 'sponsor';
}

// Email Integration Types
export interface PDFEmailAttachment {
  fileName: string;
  content: Buffer;
  contentType: string;
}

export interface PDFEmailData {
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  attachments: PDFEmailAttachment[];
}
