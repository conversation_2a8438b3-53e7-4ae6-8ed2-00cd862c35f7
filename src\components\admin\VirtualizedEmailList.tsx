'use client';

import React, { useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import EmailLogCard from './EmailLogCard';

interface EmailLog {
  id: string;
  recipient_email: string;
  recipient_name?: string;
  sender_email: string;
  sender_name?: string;
  subject: string;
  email_type: string;
  status: 'sent' | 'failed' | 'pending';
  sent_at?: string;
  created_at: string;
  error_message?: string;
  content_preview?: string;
  has_attachments?: boolean;
  sendgrid_message_id?: string;
}

interface VirtualizedEmailListProps {
  emails: EmailLog[];
  height: number;
  onCopySendGridId?: (id: string) => void;
  onViewContent?: (email: EmailLog) => void;
  onRetry?: (email: EmailLog) => void;
  className?: string;
}

interface EmailItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    emails: EmailLog[];
    onCopySendGridId?: (id: string) => void;
    onViewContent?: (email: EmailLog) => void;
    onRetry?: (email: EmailLog) => void;
  };
}

const EmailItem: React.FC<EmailItemProps> = ({ index, style, data }) => {
  const { emails, onCopySendGridId, onViewContent, onRetry } = data;
  const email = emails[index];

  if (!email) {
    return <div style={style} />;
  }

  return (
    <div style={{ ...style, padding: '0 0 12px 0' }}>
      <EmailLogCard
        email={email}
        onCopySendGridId={onCopySendGridId}
        onViewContent={onViewContent}
        onRetry={onRetry}
      />
    </div>
  );
};

export default function VirtualizedEmailList({
  emails,
  height,
  onCopySendGridId,
  onViewContent,
  onRetry,
  className = '',
}: VirtualizedEmailListProps) {
  const itemData = useMemo(
    () => ({
      emails,
      onCopySendGridId,
      onViewContent,
      onRetry,
    }),
    [emails, onCopySendGridId, onViewContent, onRetry]
  );

  // Estimate item height based on content
  const getItemHeight = () => {
    // Base height for a typical email card
    // This can be adjusted based on your actual card heights
    return 180; // pixels
  };

  if (emails.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height }}>
        <div className="text-center text-gray-500">
          <p className="text-lg font-medium">No emails found</p>
          <p className="text-sm mt-1">Try adjusting your filters</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <List
        height={height}
        itemCount={emails.length}
        itemSize={getItemHeight()}
        itemData={itemData}
        overscanCount={5} // Render 5 extra items outside visible area for smooth scrolling
        className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
      >
        {EmailItem}
      </List>
    </div>
  );
}

// Hook for calculating optimal list height
export function useOptimalListHeight(
  containerRef: React.RefObject<HTMLElement>,
  defaultHeight: number = 600
): number {
  const [height, setHeight] = React.useState(defaultHeight);

  React.useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const availableHeight = viewportHeight - containerRect.top - 100; // 100px buffer
        setHeight(Math.max(400, Math.min(800, availableHeight))); // Min 400px, max 800px
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, [containerRef]);

  return height;
}
