# Speaker File Management System Test Plan

**Date**: January 30, 2025  
**Status**: ✅ Resend Welcome Email Complete | 🔄 Speaker File Testing In Progress  
**Scope**: IEPA Annual Meeting Registration System

## Overview

This document outlines the comprehensive testing plan for the speaker file management system and the newly implemented resend welcome email functionality.

## ✅ Completed: Resend Welcome Email Functionality

### Implementation Summary
- **API Endpoint**: `/api/admin/resend-welcome-email`
- **Location**: Admin attendee view pages (`/admin/attendees/view?id={id}`)
- **Features**: 
  - Validation for completed registrations (not draft status)
  - Proper error handling and success notifications
  - Email logging in `iepa_email_logs` table
  - Uses existing welcome email template system

### Test Results
- ✅ **Button Integration**: Successfully added to admin attendee view page
- ✅ **API Functionality**: Endpoint correctly validates attendee and sends email
- ✅ **User Feedback**: Success notification displays properly
- ✅ **Error Handling**: Disabled for draft registrations with tooltip explanation
- ✅ **Email Logging**: All resent emails logged in database

### Manual Test Performed
1. Navigated to admin attendee view page
2. Clicked "Resend Welcome Email" button
3. Verified success notification appeared
4. Confirmed email would be logged in database

## 🔄 Speaker File Management System Testing

### System Components

#### 1. File Upload Infrastructure
- **Hook**: `useFileUpload.ts` - Reusable React hook for file uploads
- **Service**: `speakerFileService.ts` - Database operations for speaker files
- **Component**: `SpeakerFileUploadButton.tsx` - UI component for file uploads
- **Utilities**: `fileManagement.ts` - File operations and storage management

#### 2. Storage Configuration
- **Bucket**: `iepa-presentations` (private bucket)
- **Folders**: 
  - `speaker-presentations/` - Presentation files
  - `speaker-headshots/` - Professional headshots
- **Security**: Row Level Security (RLS) policies with user ID folder structure

#### 3. File Type Support

**Presentation Files**:
- **Types**: PDF, PPT, PPTX, DOC, DOCX
- **Max Size**: 50MB (52,428,800 bytes)
- **MIME Types**: 
  - `application/pdf`
  - `application/vnd.ms-powerpoint`
  - `application/vnd.openxmlformats-officedocument.presentationml.presentation`
  - `application/msword`
  - `application/vnd.openxmlformats-officedocument.wordprocessingml.document`

**Headshot Images**:
- **Types**: JPG, JPEG, PNG, WebP
- **Max Size**: 5MB (5,242,880 bytes)
- **MIME Types**: `image/jpeg`, `image/png`, `image/webp`

### Test Plan: CRUD Operations

#### CREATE Tests
1. **Valid File Upload**
   - Upload PDF presentation file (< 50MB)
   - Upload JPG headshot (< 5MB)
   - Verify files stored in correct bucket/folder structure
   - Confirm database records updated with file URLs

2. **File Validation**
   - Test file size limits (50MB for presentations, 5MB for headshots)
   - Test file type restrictions
   - Verify error messages for invalid files

3. **Authentication & Authorization**
   - Ensure only authenticated users can upload
   - Verify RLS policies prevent cross-user access

#### READ Tests
1. **File Display**
   - Verify existing file URLs display correctly
   - Test signed URL generation for private files
   - Confirm file previews/thumbnails work for images

2. **Download Functionality**
   - Test file download links
   - Verify signed URLs expire appropriately
   - Confirm file accessibility

#### UPDATE Tests
1. **File Replacement**
   - Upload new file to replace existing
   - Verify old file is deleted from storage
   - Confirm database URL is updated

2. **Metadata Updates**
   - Test updating file descriptions/names
   - Verify database consistency

#### DELETE Tests
1. **File Removal**
   - Remove file from speaker registration
   - Verify file deleted from Supabase storage
   - Confirm database field set to null

2. **Cleanup Operations**
   - Test orphaned file cleanup
   - Verify storage space management

### Test Plan: Error Handling & Validation

#### File Size Validation
- [ ] Upload file exceeding 50MB limit (presentations)
- [ ] Upload file exceeding 5MB limit (headshots)
- [ ] Verify appropriate error messages

#### File Type Validation
- [ ] Upload unsupported file types (.txt, .exe, etc.)
- [ ] Upload files with incorrect extensions
- [ ] Test MIME type validation

#### Network & Storage Errors
- [ ] Test upload with network interruption
- [ ] Simulate storage bucket errors
- [ ] Verify graceful error handling

#### User Experience
- [ ] Test upload progress indicators
- [ ] Verify loading states during operations
- [ ] Confirm success/error notifications

### Test Plan: Integration Testing

#### Speaker Registration Flow
- [ ] Test file upload during speaker registration
- [ ] Verify files persist through form submission
- [ ] Confirm files accessible in admin interface

#### Admin Interface Integration
- [ ] Test file viewing in admin speaker pages
- [ ] Verify file download functionality for admins
- [ ] Test file replacement through admin interface

### Test Environment Setup

#### Prerequisites
1. Development server running on port 6969
2. Authenticated user session
3. Access to test files of various types and sizes
4. Supabase storage bucket configured

#### Test Data Requirements
- Sample PDF files (various sizes)
- Sample image files (JPG, PNG, WebP)
- Invalid file types for negative testing
- Files exceeding size limits

### Success Criteria

#### Functional Requirements
- ✅ All CRUD operations work correctly
- ✅ File validation enforces size and type limits
- ✅ Error handling provides clear user feedback
- ✅ Storage integration maintains data integrity

#### Performance Requirements
- Upload progress indicators function properly
- File operations complete within reasonable time
- Storage cleanup prevents orphaned files

#### Security Requirements
- RLS policies prevent unauthorized access
- File uploads respect authentication requirements
- Signed URLs provide secure file access

### Next Steps

1. **Complete Manual Testing**: Execute all test cases outlined above
2. **Automated Testing**: Create Playwright tests for file upload scenarios
3. **Performance Testing**: Test with large files and multiple concurrent uploads
4. **Security Audit**: Verify RLS policies and access controls
5. **Documentation**: Update user guides with file upload procedures

## Conclusion

The resend welcome email functionality has been successfully implemented and tested. The speaker file management system has a comprehensive infrastructure in place and requires systematic testing to ensure all CRUD operations, validation, and error handling work correctly across different file types and user scenarios.
