import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

export async function POST() {
  try {
    console.log('[FORCE-CREATE-EMAIL-TABLE] Starting table creation...');

    // Try to create the table by inserting a record and handling the error
    const testRecord = {
      recipient_email: '<EMAIL>',
      sender_email: '<EMAIL>',
      subject: 'Table Creation Test',
      email_type: 'setup_test',
      status: 'sent'
    };

    // First, try to insert to see if table exists
    const { data: insertData, error: insertError } = await supabase
      .from('iepa_email_log')
      .insert([testRecord])
      .select();

    if (insertError) {
      console.log('[FORCE-CREATE-EMAIL-TABLE] Insert error:', insertError);
      
      if (insertError.message && insertError.message.includes('does not exist')) {
        // Table doesn't exist, we need to create it manually
        return NextResponse.json({
          success: false,
          error: 'Table does not exist and cannot be created automatically',
          message: 'Please create the table manually in Supabase dashboard',
          instructions: {
            step1: 'Go to Supabase dashboard',
            step2: 'Navigate to SQL Editor',
            step3: 'Run the SQL from scripts/create-email-log-table.sql',
            step4: 'Try this endpoint again'
          },
          sqlNeeded: `
CREATE TABLE public.iepa_email_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  recipient_email VARCHAR(255) NOT NULL,
  recipient_name VARCHAR(255),
  sender_email VARCHAR(255) NOT NULL,
  sender_name VARCHAR(255),
  subject VARCHAR(500) NOT NULL,
  email_type VARCHAR(50) NOT NULL,
  template_used VARCHAR(100),
  content_preview TEXT,
  has_attachments BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  sendgrid_message_id VARCHAR(255),
  error_message TEXT,
  user_id UUID,
  registration_id UUID,
  registration_type VARCHAR(20),
  payment_id UUID,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.iepa_email_log ENABLE ROW LEVEL SECURITY;

-- Create service role policy
CREATE POLICY "Service role full access" 
  ON public.iepa_email_log FOR ALL 
  TO service_role 
  USING (true) 
  WITH CHECK (true);
          `
        });
      } else {
        return NextResponse.json({
          success: false,
          error: insertError.message,
          details: 'Unexpected error during table test'
        }, { status: 500 });
      }
    }

    // If we get here, the table exists and insert worked
    console.log('[FORCE-CREATE-EMAIL-TABLE] Table exists and working');
    
    // Clean up test record
    if (insertData && insertData[0]) {
      await supabase
        .from('iepa_email_log')
        .delete()
        .eq('id', insertData[0].id);
    }

    return NextResponse.json({
      success: true,
      message: 'Email log table is working correctly',
      tableExists: true,
      testPassed: true
    });

  } catch (error) {
    console.error('[FORCE-CREATE-EMAIL-TABLE] Error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Failed to test or create email log table'
    }, { status: 500 });
  }
}
