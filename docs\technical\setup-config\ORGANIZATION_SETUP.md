# Organization Dropdown Setup for IEPA Conference Registration

This document outlines the steps to set up the organization dropdown feature for the attendee registration form.

## Overview

The organization dropdown feature allows users to:
- Select from a list of existing organizations (prepopulated from historical data)
- Search and filter organizations 
- Manually enter new organizations
- Automatically track organization usage frequency

## Database Changes Required

### 1. Apply Schema Changes

Run the updated database schema from `src/lib/database-schema.sql` in your Supabase SQL editor. This includes:

- **New `iepa_organizations` table** with fields:
  - `id` (UUID, primary key)
  - `name` (TEXT, unique organization name)
  - `normalized_name` (TEXT, lowercase for searching)
  - `usage_count` (INTEGER, tracks frequency)
  - `last_used_at` (TIMESTAMP)
  - `is_active` (BOOLEAN, for soft deletion)

- **Updated `iepa_historical_registrations` table** with:
  - `organization` (TEXT, organization name)
  - `job_title` (TEXT, job title)

### 2. Populate Organizations Table

Run the population script to import existing organizations:

```bash
# From project root
npx ts-node scripts/populate-organizations.ts
```

This script will:
- Extract unique organizations from all registration tables
- Calculate usage frequency
- Populate the `iepa_organizations` table

## Features Implemented

### 1. Organization API (`/api/organizations`)

- **GET**: Fetch organizations with optional search filtering
- **POST**: Create new organizations
- **PATCH**: Update usage count when organization is selected

### 2. OrganizationCombobox Component

Located at `src/components/ui/OrganizationCombobox.tsx`, this component provides:
- Searchable dropdown of existing organizations
- Real-time search with debouncing
- "Create new" option for unlisted organizations
- Usage count display for frequently used organizations
- Automatic usage tracking

### 3. Updated Attendee Form

The attendee registration form now uses the OrganizationCombobox instead of a simple text input, providing a better user experience.

## Setup Steps

1. **Apply Database Schema**:
   - Copy contents of `src/lib/database-schema.sql`
   - Run in Supabase SQL editor

2. **Populate Organizations**:
   ```bash
   npm run populate-organizations
   ```

3. **Test the Feature**:
   - Visit `/register/attendee`
   - Navigate to the "Contact Information" step
   - Try the organization dropdown

## API Usage

### Fetch Organizations
```javascript
GET /api/organizations?search=invenergy&limit=50
```

### Create New Organization
```javascript
POST /api/organizations
{
  "name": "New Organization Name"
}
```

### Update Usage Count
```javascript
PATCH /api/organizations
{
  "organizationName": "Existing Organization"
}
```

## Benefits

- **Better UX**: Users can quickly select from existing organizations
- **Data Consistency**: Reduces duplicate/misspelled organization names
- **Popular First**: Most-used organizations appear at the top
- **Flexible**: Still allows manual entry for new organizations
- **Tracking**: Automatically tracks which organizations are used most

## Future Enhancements

- Admin interface for managing organizations
- Organization categories/industries
- Bulk import from external sources
- Organization profiles with additional metadata 