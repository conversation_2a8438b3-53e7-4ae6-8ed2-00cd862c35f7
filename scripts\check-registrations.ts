#!/usr/bin/env npx tsx

/**
 * Check Current Registrations Script
 * 
 * This script displays current registrations in the database to help
 * verify the state before and after testing.
 */

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Check attendee registrations
 */
async function checkAttendees(): Promise<void> {
  console.log('👥 ATTENDEE REGISTRATIONS');
  console.log('========================');
  
  const { data: attendees, error } = await supabase
    .from('iepa_attendee_registrations')
    .select('id, first_name, last_name, email, organization, created_at')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('❌ Error fetching attendees:', error.message);
    return;
  }
  
  if (!attendees || attendees.length === 0) {
    console.log('✅ No attendee registrations found\n');
    return;
  }
  
  console.log(`📊 Total: ${attendees.length} registrations`);
  attendees.forEach((attendee, index) => {
    const date = new Date(attendee.created_at).toLocaleDateString();
    console.log(`${index + 1}. ${attendee.first_name} ${attendee.last_name}`);
    console.log(`   Email: ${attendee.email}`);
    console.log(`   Organization: ${attendee.organization || 'N/A'}`);
    console.log(`   Registered: ${date}`);
    console.log('');
  });
}

/**
 * Check speaker registrations
 */
async function checkSpeakers(): Promise<void> {
  console.log('🎤 SPEAKER REGISTRATIONS');
  console.log('========================');
  
  const { data: speakers, error } = await supabase
    .from('iepa_speaker_registrations')
    .select('id, first_name, last_name, email, presentation_title, created_at')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('❌ Error fetching speakers:', error.message);
    return;
  }
  
  if (!speakers || speakers.length === 0) {
    console.log('✅ No speaker registrations found\n');
    return;
  }
  
  console.log(`📊 Total: ${speakers.length} registrations`);
  speakers.forEach((speaker, index) => {
    const date = new Date(speaker.created_at).toLocaleDateString();
    console.log(`${index + 1}. ${speaker.first_name} ${speaker.last_name}`);
    console.log(`   Email: ${speaker.email}`);
    console.log(`   Presentation: ${speaker.presentation_title || 'N/A'}`);
    console.log(`   Registered: ${date}`);
    console.log('');
  });
}

/**
 * Check sponsor registrations
 */
async function checkSponsors(): Promise<void> {
  console.log('🏢 SPONSOR REGISTRATIONS');
  console.log('========================');
  
  const { data: sponsors, error } = await supabase
    .from('iepa_sponsor_registrations')
    .select('id, contact_name, contact_email, sponsorship_level, amount, created_at')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('❌ Error fetching sponsors:', error.message);
    return;
  }
  
  if (!sponsors || sponsors.length === 0) {
    console.log('✅ No sponsor registrations found\n');
    return;
  }
  
  console.log(`📊 Total: ${sponsors.length} registrations`);
  sponsors.forEach((sponsor, index) => {
    const date = new Date(sponsor.created_at).toLocaleDateString();
    console.log(`${index + 1}. ${sponsor.contact_name}`);
    console.log(`   Email: ${sponsor.contact_email}`);
    console.log(`   Level: ${sponsor.sponsorship_level}`);
    console.log(`   Amount: $${sponsor.amount || 0}`);
    console.log(`   Registered: ${date}`);
    console.log('');
  });
}

/**
 * Check user profiles
 */
async function checkUserProfiles(): Promise<void> {
  console.log('👤 USER PROFILES');
  console.log('================');
  
  const { data: profiles, error } = await supabase
    .from('iepa_user_profiles')
    .select('id, first_name, last_name, email, created_at')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('❌ Error fetching user profiles:', error.message);
    return;
  }
  
  if (!profiles || profiles.length === 0) {
    console.log('✅ No user profiles found\n');
    return;
  }
  
  console.log(`📊 Total: ${profiles.length} profiles`);
  profiles.forEach((profile, index) => {
    const date = new Date(profile.created_at).toLocaleDateString();
    console.log(`${index + 1}. ${profile.first_name} ${profile.last_name}`);
    console.log(`   Email: ${profile.email}`);
    console.log(`   Created: ${date}`);
    console.log('');
  });
}

/**
 * Check email logs (if table exists)
 */
async function checkEmailLogs(): Promise<void> {
  console.log('📧 EMAIL LOGS');
  console.log('=============');
  
  const { data: emailLogs, error } = await supabase
    .from('iepa_email_logs')
    .select('id, recipient_email, subject, status, created_at')
    .order('created_at', { ascending: false })
    .limit(10);
  
  if (error) {
    if (error.code === '42P01') {
      console.log('ℹ️  Email logs table does not exist yet\n');
    } else {
      console.error('❌ Error fetching email logs:', error.message);
    }
    return;
  }
  
  if (!emailLogs || emailLogs.length === 0) {
    console.log('✅ No email logs found\n');
    return;
  }
  
  console.log(`📊 Total: ${emailLogs.length} recent logs (showing last 10)`);
  emailLogs.forEach((log, index) => {
    const date = new Date(log.created_at).toLocaleDateString();
    console.log(`${index + 1}. To: ${log.recipient_email}`);
    console.log(`   Subject: ${log.subject}`);
    console.log(`   Status: ${log.status}`);
    console.log(`   Sent: ${date}`);
    console.log('');
  });
}

/**
 * Main function to check all registrations
 */
async function checkAllRegistrations(): Promise<void> {
  console.log('🔍 IEPA REGISTRATION DATABASE STATUS');
  console.log('====================================\n');
  
  try {
    await checkAttendees();
    await checkSpeakers();
    await checkSponsors();
    await checkUserProfiles();
    await checkEmailLogs();
    
    console.log('✅ Database check completed successfully');
    console.log('🎯 Ready for testing!');
    
  } catch (error) {
    console.error('\n❌ Error during database check:', error);
    process.exit(1);
  }
}

// Run the check
if (require.main === module) {
  checkAllRegistrations();
}

export { checkAllRegistrations };
