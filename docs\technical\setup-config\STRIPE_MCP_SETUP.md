# Stripe MCP (Model Context Protocol) Setup

This document explains how to set up and use the Stripe MCP server for AI assistant integration with Stripe APIs.

## Overview

The Stripe Agent Toolkit enables AI assistants to interact with Stripe APIs through function calling using the Model Context Protocol (MCP). This allows AI assistants to:

- Manage customers, products, and pricing
- Process payments and create payment links
- Handle subscriptions and invoicing
- Manage refunds and disputes
- Access account information and balances

## Prerequisites

1. **Stripe Account**: You need a Stripe account with API keys
2. **Environment Variables**: Stripe API keys must be configured
3. **Node.js**: Version 18+ required

## Environment Setup

Ensure your `.env.local` file contains your Stripe API keys:

```env
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

⚠️ **Security Note**: Never commit your actual API keys to version control. Use test keys for development.

## Installation Methods

### Method 1: Using npx (Recommended for Quick Start)

The simplest way to run the Stripe MCP server:

```bash
# Using environment variable
npx -y @stripe/mcp --tools=all --api-key=$STRIPE_SECRET_KEY

# Or specify the key directly (not recommended for production)
npx -y @stripe/mcp --tools=all --api-key=sk_test_your_key_here
```

### Method 2: Custom Server (Recommended for Development)

Use the custom server script included in this project:

```bash
# Run the custom MCP server
npx tsx scripts/stripe-mcp-server.ts
```

This method provides:

- Better error handling
- Comprehensive API coverage
- Environment variable integration
- Graceful shutdown handling

## Available Stripe Operations

The MCP server provides access to the following Stripe API operations:

### Customer Management

- Create, list, retrieve, and update customers
- Manage customer payment methods

### Products & Pricing

- Create and manage products
- Set up pricing models
- Handle price updates

### Payment Processing

- Create and manage PaymentIntents
- Generate payment links
- Process one-time payments

### Subscriptions

- Create and manage subscriptions
- Handle subscription updates and cancellations
- Manage billing cycles

### Invoicing

- Create and finalize invoices
- Add invoice items
- Process invoice payments

### Financial Operations

- Process refunds
- Retrieve account balance
- Handle disputes

### Promotions

- Create and manage coupons
- Apply discounts

## Usage with AI Assistants

### Claude Desktop (MCP)

1. Add the MCP server to your Claude Desktop configuration
2. Start the server using one of the methods above
3. Claude will automatically detect available Stripe functions

### Other AI Frameworks

The toolkit also supports:

- **OpenAI's Agent SDK**
- **LangChain**
- **CrewAI**
- **Vercel's AI SDK**

Example with LangChain:

```typescript
import { StripeAgentToolkit } from '@stripe/agent-toolkit/langchain';

const stripeToolkit = new StripeAgentToolkit({
  secretKey: process.env.STRIPE_SECRET_KEY!,
  configuration: {
    actions: {
      paymentLinks: { create: true },
      customers: { create: true, list: true },
    },
  },
});

const tools = stripeToolkit.getTools();
```

## Configuration Options

### Basic Configuration

```typescript
const toolkit = new StripeAgentToolkit({
  secretKey: 'sk_test_...',
  configuration: {
    actions: {
      customers: { create: true, list: true },
      paymentLinks: { create: true },
    },
  },
});
```

### Connected Accounts

For Stripe Connect platforms:

```typescript
const toolkit = new StripeAgentToolkit({
  secretKey: 'sk_test_...',
  configuration: {
    context: {
      account: 'acct_connected_account_id',
    },
    actions: {
      // ... your actions
    },
  },
});
```

## Testing

Test your MCP server setup:

1. **Start the server**:

   ```bash
   npx tsx scripts/stripe-mcp-server.ts
   ```

2. **Verify connection**: The server should output "Stripe MCP Server running on stdio"

3. **Test with AI assistant**: Try asking your AI assistant to perform Stripe operations

## Troubleshooting

### Common Issues

1. **Missing API Key**:

   ```
   Error: STRIPE_SECRET_KEY environment variable is required
   ```

   Solution: Ensure your `.env.local` file contains the correct Stripe secret key.

2. **Permission Errors**:

   ```
   Error: You do not have permission to access this resource
   ```

   Solution: Check that your Stripe API key has the necessary permissions.

3. **Network Issues**:
   ```
   Error: connect ECONNREFUSED
   ```
   Solution: Verify your internet connection and Stripe API accessibility.

### Debug Mode

Enable debug logging by setting:

```bash
export DEBUG=stripe:*
npx tsx scripts/stripe-mcp-server.ts
```

## Security Best Practices

1. **Use Test Keys**: Always use test API keys (`sk_test_...`) during development
2. **Environment Variables**: Store API keys in environment variables, not in code
3. **Webhook Secrets**: Verify webhook signatures in production
4. **Rate Limiting**: Be aware of Stripe's API rate limits
5. **Error Handling**: Implement proper error handling for API failures

## Next Steps

1. Configure your AI assistant to use the MCP server
2. Test basic operations like creating customers or payment links
3. Explore advanced features like subscription management
4. Set up webhook handling for real-time events (separate from MCP)

For more information, visit:

- [Stripe Agent Toolkit Documentation](https://docs.stripe.com/agents)
- [Model Context Protocol Specification](https://modelcontextprotocol.com/)
- [Stripe API Documentation](https://docs.stripe.com/api)
