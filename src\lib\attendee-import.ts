import { supabase } from './supabase';
import { createClient } from '@supabase/supabase-js';

// Types for the import data
interface ImportAttendeeData {
  'Attendee Last Name': string;
  'Attendee First Name': string;
  'Attendee Type': string;
  'Attendee Type (IEPA)': string | null;
  'Date Added': string;
  Street: string;
  City: string;
  State: string;
  Zip: number;
  'Email For Attendee List': string;
  'Attendee Job Title': string;
  'Attendee Organization': string;
  Gender: string;
  'Nights staying (lodging)': string | null;
  Meals: string;
  'Special Dietary Needs': string | null;
  Status: string;
  'Grand order total': number | null;
  'Name on Badge': string;
  'Golf Tournament?': string;
  'Are you renting clubs?': string | null;
  'Phone Number For Attendee List': string;
  'Golf Total': number | null;
  "Golf<PERSON>'s cell number for last minute changes, etc.": string | null;
  Country: string | null;
}

interface ProcessedAttendeeData {
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  phoneNumber: string;
  organization: string;
  jobTitle: string;
  streetAddress: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  gender: string;
  preferredNameOnBadge: string;
  // Historical registration data
  attendeeType: string;
  attendeeTypeIepa: string | null;
  registrationDate: string;
  status: string;
  nightsStaying: string | null;
  meals: string[];
  specialDietaryNeeds: string | null;
  golfTournament: boolean;
  golfClubRental: string | null;
  golfCellNumber: string | null;
  golfTotal: number;
  grandTotal: number;
  originalData: ImportAttendeeData;
}

// Utility functions
export const attendeeImportUtils = {
  // Generate a secure random password for imported users
  generateSecurePassword: (): string => {
    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 16; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  },

  // Parse meals string into array
  parseMeals: (mealsString: string): string[] => {
    if (!mealsString) return [];
    return mealsString
      .split(',')
      .map(meal => meal.trim())
      .filter(meal => meal.length > 0);
  },

  // Convert golf tournament response to boolean
  parseGolfTournament: (golfResponse: string): boolean => {
    return golfResponse?.toLowerCase() === 'yes';
  },

  // Clean and format phone number
  formatPhoneNumber: (phone: string): string => {
    if (!phone) return '';
    // Remove all non-digit characters except + at the beginning
    return phone.replace(/[^\d+]/g, '').replace(/(?!^)\+/g, '');
  },

  // Process raw import data into structured format
  processAttendeeData: (rawData: ImportAttendeeData): ProcessedAttendeeData => {
    const meals = attendeeImportUtils.parseMeals(rawData.Meals);
    const golfTournament = attendeeImportUtils.parseGolfTournament(
      rawData['Golf Tournament?']
    );
    const phoneNumber = attendeeImportUtils.formatPhoneNumber(
      rawData['Phone Number For Attendee List']
    );

    return {
      email: rawData['Email For Attendee List'].toLowerCase().trim(),
      firstName: rawData['Attendee First Name'].trim(),
      lastName: rawData['Attendee Last Name'].trim(),
      fullName: `${rawData['Attendee First Name'].trim()} ${rawData['Attendee Last Name'].trim()}`,
      phoneNumber,
      organization: rawData['Attendee Organization'].trim(),
      jobTitle: rawData['Attendee Job Title'].trim(),
      streetAddress: rawData.Street.trim(),
      city: rawData.City.trim(),
      state: rawData.State.trim(),
      zipCode: rawData.Zip.toString(),
      country: rawData.Country || 'United States',
      gender: rawData.Gender.trim(),
      preferredNameOnBadge: rawData['Name on Badge'].trim(),
      // Historical registration data
      attendeeType: rawData['Attendee Type'].trim(),
      attendeeTypeIepa: rawData['Attendee Type (IEPA)'],
      registrationDate: rawData['Date Added'],
      status: rawData.Status.trim(),
      nightsStaying: rawData['Nights staying (lodging)'],
      meals,
      specialDietaryNeeds: rawData['Special Dietary Needs'],
      golfTournament,
      golfClubRental: rawData['Are you renting clubs?'],
      golfCellNumber:
        rawData["Golfer's cell number for last minute changes, etc."],
      golfTotal: rawData['Golf Total'] || 0,
      grandTotal: rawData['Grand order total'] || 0,
      originalData: rawData,
    };
  },

  // Check if user already exists by email
  checkUserExists: async (
    email: string
  ): Promise<{ exists: boolean; userId?: string }> => {
    try {
      const { data, error } = await supabase
        .from('iepa_user_profiles')
        .select('user_id')
        .eq('email', email.toLowerCase())
        .single();

      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "not found"
        throw error;
      }

      return {
        exists: !!data,
        userId: data?.user_id,
      };
    } catch (error) {
      console.error('Error checking user existence:', error);
      return { exists: false };
    }
  },

  // Create user account using admin client
  createUserAccount: async (
    email: string,
    password: string
  ): Promise<{ success: boolean; userId?: string; error?: string }> => {
    try {
      // Use service role client for admin operations
      const adminClient = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false,
          },
        }
      );

      const { data, error } = await adminClient.auth.admin.createUser({
        email,
        password,
        email_confirm: true, // Skip email confirmation for imported users
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, userId: data.user?.id };
    } catch (error) {
      console.error('Error creating user account:', error);
      return { success: false, error: 'Failed to create user account' };
    }
  },

  // Create user profile
  createUserProfile: async (
    userId: string,
    profileData: ProcessedAttendeeData
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const { error } = await supabase.from('iepa_user_profiles').insert({
        user_id: userId,
        first_name: profileData.firstName,
        last_name: profileData.lastName,
        email: profileData.email,
        phone_number: profileData.phoneNumber,
        organization: profileData.organization,
        job_title: profileData.jobTitle,
        street_address: profileData.streetAddress,
        city: profileData.city,
        state: profileData.state,
        zip_code: profileData.zipCode,
        country: profileData.country,
        gender: profileData.gender,
        preferred_name_on_badge: profileData.preferredNameOnBadge,
        imported_from_2024: true,
        import_date: new Date().toISOString(),
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error creating user profile:', error);
      return { success: false, error: 'Failed to create user profile' };
    }
  },

  // Create historical registration record
  createHistoricalRegistration: async (
    userId: string,
    profileId: string,
    registrationData: ProcessedAttendeeData
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const { error } = await supabase
        .from('iepa_historical_registrations')
        .insert({
          user_id: userId,
          profile_id: profileId,
          event_year: 2024,
          event_name: 'IEPA Annual Meeting 2024',
          registration_date: registrationData.registrationDate,
          attendee_type: registrationData.attendeeType,
          attendee_type_iepa: registrationData.attendeeTypeIepa,
          status: registrationData.status,
          name_on_badge: registrationData.preferredNameOnBadge,
          nights_staying: registrationData.nightsStaying,
          meals: registrationData.meals,
          special_dietary_needs: registrationData.specialDietaryNeeds,
          golf_tournament: registrationData.golfTournament,
          golf_club_rental: registrationData.golfClubRental,
          golf_cell_number: registrationData.golfCellNumber,
          golf_total: registrationData.golfTotal,
          grand_total: registrationData.grandTotal,
          payment_status: 'completed',
          imported_from_source: '2024-attendee-export',
          original_data: registrationData.originalData,
        });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error creating historical registration:', error);
      return {
        success: false,
        error: 'Failed to create historical registration',
      };
    }
  },
};

// Main import function
export interface ImportResult {
  success: boolean;
  totalProcessed: number;
  successfulImports: number;
  skippedExisting: number;
  errors: Array<{ email: string; error: string }>;
  generatedPasswords: Array<{ email: string; password: string }>;
}

export const importAttendeesFromJson = async (
  attendeeData: ImportAttendeeData[],
  options: {
    skipExisting?: boolean;
    dryRun?: boolean;
    batchSize?: number;
  } = {}
): Promise<ImportResult> => {
  const { skipExisting = true, dryRun = false, batchSize = 10 } = options;

  const result: ImportResult = {
    success: false,
    totalProcessed: 0,
    successfulImports: 0,
    skippedExisting: 0,
    errors: [],
    generatedPasswords: [],
  };

  try {
    console.log(`Starting import of ${attendeeData.length} attendees...`);

    // Process in batches to avoid overwhelming the database
    for (let i = 0; i < attendeeData.length; i += batchSize) {
      const batch = attendeeData.slice(i, i + batchSize);

      for (const rawAttendee of batch) {
        result.totalProcessed++;

        try {
          // Process the raw data
          const processedData =
            attendeeImportUtils.processAttendeeData(rawAttendee);

          // Check if user already exists
          const { exists, userId: existingUserId } =
            await attendeeImportUtils.checkUserExists(processedData.email);

          if (exists && skipExisting) {
            console.log(`Skipping existing user: ${processedData.email}`);
            result.skippedExisting++;
            continue;
          }

          if (dryRun) {
            console.log(`[DRY RUN] Would import: ${processedData.email}`);
            result.successfulImports++;
            continue;
          }

          let userId = existingUserId;
          let password = '';

          // Create user account if it doesn't exist
          if (!exists) {
            password = attendeeImportUtils.generateSecurePassword();
            const userResult = await attendeeImportUtils.createUserAccount(
              processedData.email,
              password
            );

            if (!userResult.success) {
              result.errors.push({
                email: processedData.email,
                error: `Failed to create user account: ${userResult.error}`,
              });
              continue;
            }

            userId = userResult.userId!;
            result.generatedPasswords.push({
              email: processedData.email,
              password,
            });
          }

          // Create user profile
          const profileResult = await attendeeImportUtils.createUserProfile(
            userId!,
            processedData
          );
          if (!profileResult.success) {
            result.errors.push({
              email: processedData.email,
              error: `Failed to create profile: ${profileResult.error}`,
            });
            continue;
          }

          // Get the profile ID for the historical registration
          const { data: profileData } = await supabase
            .from('iepa_user_profiles')
            .select('id')
            .eq('user_id', userId)
            .single();

          if (!profileData) {
            result.errors.push({
              email: processedData.email,
              error: 'Failed to retrieve profile ID',
            });
            continue;
          }

          // Create historical registration
          const historyResult =
            await attendeeImportUtils.createHistoricalRegistration(
              userId!,
              profileData.id,
              processedData
            );

          if (!historyResult.success) {
            result.errors.push({
              email: processedData.email,
              error: `Failed to create historical registration: ${historyResult.error}`,
            });
            continue;
          }

          console.log(`Successfully imported: ${processedData.email}`);
          result.successfulImports++;
        } catch (error) {
          console.error(
            `Error processing attendee ${rawAttendee['Email For Attendee List']}:`,
            error
          );
          result.errors.push({
            email: rawAttendee['Email For Attendee List'],
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      // Add a small delay between batches to be gentle on the database
      if (i + batchSize < attendeeData.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    result.success = result.errors.length === 0 || result.successfulImports > 0;

    console.log('Import completed:', {
      totalProcessed: result.totalProcessed,
      successful: result.successfulImports,
      skipped: result.skippedExisting,
      errors: result.errors.length,
    });

    return result;
  } catch (error) {
    console.error('Import failed:', error);
    result.errors.push({
      email: 'SYSTEM',
      error: error instanceof Error ? error.message : 'Unknown system error',
    });
    return result;
  }
};
