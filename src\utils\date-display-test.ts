// Test utility to verify date display functionality
// This file can be used to test the new date mapping utilities

import { dateUtils } from '@/lib/conference-config';
import { mealOptions } from '@/lib/meal-config';

/**
 * Test the date mapping functionality
 */
export function testDateMapping() {
  console.log('🧪 Testing Date Mapping Utilities');
  console.log('=====================================');

  // Test database field mapping
  console.log('\n📅 Database Field Mapping:');
  console.log('nightOne (desktop):', dateUtils.mapDatabaseFieldToDate('nightOne'));
  console.log('nightOne (mobile):', dateUtils.mapDatabaseFieldToDate('nightOne', { mobile: true }));
  console.log('nightTwo (desktop):', dateUtils.mapDatabaseFieldToDate('nightTwo'));
  console.log('nightTwo (mobile):', dateUtils.mapDatabaseFieldToDate('nightTwo', { mobile: true }));

  // Test lodging night info
  console.log('\n🏨 Lodging Night Information:');
  const nightOneInfo = dateUtils.getLodgingNightInfo('nightOne');
  const nightTwoInfo = dateUtils.getLodgingNightInfo('nightTwo');
  
  console.log('Night One Info:', {
    displayDate: nightOneInfo?.displayDate,
    mobileDate: nightOneInfo?.mobileDate,
    description: nightOneInfo?.description,
  });
  
  console.log('Night Two Info:', {
    displayDate: nightTwoInfo?.displayDate,
    mobileDate: nightTwoInfo?.mobileDate,
    description: nightTwoInfo?.description,
  });

  // Test meal options with new date format
  console.log('\n🍽️ Meal Options with Dates:');
  mealOptions.forEach((dayOption, index) => {
    console.log(`Day ${index + 1}:`, {
      day: dayOption.day,
      displayDate: dayOption.displayDate,
      mobileDate: dateUtils.formatMobileDate(dayOption.date),
      mealsCount: dayOption.meals.length,
    });
  });

  // Test date formatting utilities
  console.log('\n📱 Date Formatting:');
  const testDate = '2025-09-15';
  console.log('formatDisplayDate:', dateUtils.formatDisplayDate(testDate));
  console.log('formatShortDate:', dateUtils.formatShortDate(testDate));
  console.log('formatMobileDate:', dateUtils.formatMobileDate(testDate));

  console.log('\n✅ Date mapping tests completed!');
}

/**
 * Test meal configuration consistency
 */
export function testMealConfiguration() {
  console.log('\n🧪 Testing Meal Configuration');
  console.log('=====================================');

  // Check that all meals have proper date information
  let allMealsValid = true;
  
  mealOptions.forEach((dayOption, dayIndex) => {
    console.log(`\nDay ${dayIndex + 1}: ${dayOption.displayDate}`);
    
    if (!dayOption.date || !dayOption.displayDate || !dayOption.dayOfWeek) {
      console.error(`❌ Missing date information for day ${dayIndex + 1}`);
      allMealsValid = false;
    }
    
    dayOption.meals.forEach((meal, mealIndex) => {
      if (!meal.date || !meal.displayDate || !meal.dayOfWeek) {
        console.error(`❌ Missing date information for meal: ${meal.name}`);
        allMealsValid = false;
      } else {
        console.log(`  ✅ ${meal.name} - ${meal.displayDate}`);
      }
    });
  });

  if (allMealsValid) {
    console.log('\n✅ All meal configurations have proper date information!');
  } else {
    console.log('\n❌ Some meal configurations are missing date information!');
  }

  return allMealsValid;
}

/**
 * Run all date display tests
 */
export function runAllDateTests() {
  console.log('🚀 Running All Date Display Tests');
  console.log('===================================');
  
  try {
    testDateMapping();
    const mealConfigValid = testMealConfiguration();
    
    console.log('\n🎉 All tests completed!');
    return {
      success: true,
      mealConfigValid,
      message: 'Date display functionality is working correctly',
    };
  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Date display tests failed',
    };
  }
}

// Export for use in components or testing
export default {
  testDateMapping,
  testMealConfiguration,
  runAllDateTests,
};
