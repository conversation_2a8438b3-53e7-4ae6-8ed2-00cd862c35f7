// PDF Download Button Component
// Component for downloading PDF receipts and invoices

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { usePDFGeneration } from '@/hooks/usePDFGeneration';
import { FiDownload, FiFileText, FiLoader } from 'react-icons/fi';

interface PDFDownloadButtonProps {
  registrationId: string;
  registrationType: 'attendee' | 'speaker' | 'sponsor';
  documentType: 'receipt' | 'invoice';
  paymentMethod?: string;
  transactionId?: string;
  dueDate?: string;
  paymentTerms?: string;
  notes?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  testMode?: boolean;
  onSuccess?: (url: string) => void;
  onError?: (error: string) => void;
}

export function PDFDownloadButton({
  registrationId,
  registrationType,
  documentType,
  paymentMethod,
  transactionId,
  dueDate,
  paymentTerms,
  notes,
  variant = 'outline',
  size = 'md',
  className,
  testMode = false,
  onSuccess,
  onError,
}: PDFDownloadButtonProps) {
  const {
    generating,
    error,
    receiptUrl,
    invoiceUrl,
    generateReceipt,
    generateInvoice,
    getReceiptUrl,
    getInvoiceUrl,
    downloadPDF,
  } = usePDFGeneration();

  const [existingUrl, setExistingUrl] = useState<string | null>(null);
  const [checked, setChecked] = useState(false);

  const currentUrl = documentType === 'receipt' ? receiptUrl : invoiceUrl;
  const finalUrl = currentUrl || existingUrl;

  // Check for existing PDF on mount
  useEffect(() => {
    if (!checked) {
      const checkExisting = async () => {
        try {
          let url: string | null = null;
          if (documentType === 'receipt') {
            url = await getReceiptUrl(registrationId, registrationType);
          } else {
            url = await getInvoiceUrl(registrationId, registrationType);
          }
          setExistingUrl(url);
        } catch (err) {
          console.error('Error checking for existing PDF:', err);
        } finally {
          setChecked(true);
        }
      };
      checkExisting();
    }
  }, [
    checked,
    documentType,
    registrationId,
    registrationType,
    getReceiptUrl,
    getInvoiceUrl,
  ]);

  // Handle success/error callbacks
  useEffect(() => {
    if (finalUrl && onSuccess) {
      onSuccess(finalUrl);
    }
  }, [finalUrl, onSuccess]);

  useEffect(() => {
    if (error && onError) {
      onError(error);
    }
  }, [error, onError]);

  const handleGenerate = async () => {
    try {
      let success = false;

      if (testMode) {
        // Use test endpoints for test mode
        const endpoint =
          documentType === 'receipt'
            ? '/api/pdf/test-generate-receipt'
            : '/api/pdf/test-generate-invoice';

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            registrationId,
            registrationType,
            paymentMethod,
            transactionId,
            dueDate,
            paymentTerms,
            notes,
          }),
        });

        const result = await response.json();

        if (result.success) {
          success = true;
          if (onSuccess) {
            onSuccess(result.receiptUrl || result.invoiceUrl);
          }
        } else {
          if (onError) {
            onError(result.error || 'Failed to generate PDF');
          }
        }
      } else {
        // Use regular endpoints for production mode
        if (documentType === 'receipt') {
          success = await generateReceipt({
            registrationId,
            registrationType,
            paymentMethod,
            transactionId,
          });
        } else {
          success = await generateInvoice({
            registrationId,
            registrationType,
            dueDate,
            paymentTerms,
            notes,
          });
        }
      }

      if (!success) {
        console.error('Failed to generate PDF');
      }
    } catch (err) {
      console.error('Error generating PDF:', err);
      if (onError) {
        onError(err instanceof Error ? err.message : 'Unknown error');
      }
    }
  };

  const handleDownload = async () => {
    try {
      const fileName = `${documentType}-${registrationType}-${registrationId.slice(-8)}.pdf`;
      await downloadPDF(registrationId, registrationType, documentType, fileName);
    } catch (error) {
      console.error('Download failed:', error);
      if (onError) {
        onError(error instanceof Error ? error.message : 'Download failed');
      }
    }
  };

  const handleClick = () => {
    if (finalUrl) {
      handleDownload();
    } else {
      handleGenerate();
    }
  };

  const getButtonText = () => {
    if (generating) {
      return `Generating ${documentType}...`;
    }
    if (finalUrl) {
      return `Download ${documentType}`;
    }
    return `Generate ${documentType}`;
  };

  const getButtonIcon = () => {
    if (generating) {
      return <FiLoader className="animate-spin" />;
    }
    if (finalUrl) {
      return <FiDownload />;
    }
    return <FiFileText />;
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={generating || !checked}
      className={className}
    >
      {getButtonIcon()}
      <span className="ml-2">{getButtonText()}</span>
    </Button>
  );
}

// Simplified component for just downloading existing PDFs
interface PDFDownloadLinkProps {
  registrationId: string;
  registrationType: 'attendee' | 'speaker' | 'sponsor';
  documentType: 'receipt' | 'invoice';
  fileName?: string;
  children?: React.ReactNode;
  className?: string;
}

export function PDFDownloadLink({
  registrationId,
  registrationType,
  documentType,
  fileName,
  children,
  className,
}: PDFDownloadLinkProps) {
  const { downloadPDF } = usePDFGeneration();

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    try {
      const defaultFileName = fileName || `${documentType}-${registrationType}-${registrationId.slice(-8)}.pdf`;
      await downloadPDF(registrationId, registrationType, documentType, defaultFileName);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  return (
    <a
      href="#"
      onClick={handleClick}
      className={className}
      role="button"
    >
      {children || (
        <>
          <FiDownload className="inline mr-2" />
          Download PDF
        </>
      )}
    </a>
  );
}

// Component for displaying PDF status and actions
interface PDFStatusProps {
  registrationId: string;
  registrationType: 'attendee' | 'speaker' | 'sponsor';
  receiptUrl?: string | null;
  invoiceUrl?: string | null;
  receiptGeneratedAt?: string | null;
  invoiceGeneratedAt?: string | null;
  showGenerateButtons?: boolean;
  paymentMethod?: string;
  transactionId?: string;
}

export function PDFStatus({
  registrationId,
  registrationType,
  receiptUrl,
  invoiceUrl,
  receiptGeneratedAt,
  invoiceGeneratedAt,
  showGenerateButtons = true,
  paymentMethod,
  transactionId,
}: PDFStatusProps) {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">PDF Documents</h3>

      {/* Receipt Section */}
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h4 className="font-medium">Receipt</h4>
          {receiptUrl ? (
            <p className="text-sm text-gray-600">
              Generated:{' '}
              {receiptGeneratedAt ? formatDate(receiptGeneratedAt) : 'Unknown'}
            </p>
          ) : (
            <p className="text-sm text-gray-500">Not generated</p>
          )}
        </div>
        <div className="flex gap-2">
          {receiptUrl ? (
            <PDFDownloadLink
              registrationId={registrationId}
              registrationType={registrationType}
              documentType="receipt"
              fileName={`receipt-${registrationType}-${registrationId.slice(-8)}.pdf`}
            >
              <Button variant="outline" size="sm">
                <FiDownload className="mr-2" />
                Download
              </Button>
            </PDFDownloadLink>
          ) : (
            showGenerateButtons && (
              <PDFDownloadButton
                registrationId={registrationId}
                registrationType={registrationType}
                documentType="receipt"
                paymentMethod={paymentMethod}
                transactionId={transactionId}
                variant="outline"
                size="sm"
              />
            )
          )}
        </div>
      </div>

      {/* Invoice Section */}
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h4 className="font-medium">Invoice</h4>
          {invoiceUrl ? (
            <p className="text-sm text-gray-600">
              Generated:{' '}
              {invoiceGeneratedAt ? formatDate(invoiceGeneratedAt) : 'Unknown'}
            </p>
          ) : (
            <p className="text-sm text-gray-500">Not generated</p>
          )}
        </div>
        <div className="flex gap-2">
          {invoiceUrl ? (
            <PDFDownloadLink
              registrationId={registrationId}
              registrationType={registrationType}
              documentType="invoice"
              fileName={`invoice-${registrationType}-${registrationId.slice(-8)}.pdf`}
            >
              <Button variant="outline" size="sm">
                <FiDownload className="mr-2" />
                Download
              </Button>
            </PDFDownloadLink>
          ) : (
            showGenerateButtons && (
              <PDFDownloadButton
                registrationId={registrationId}
                registrationType={registrationType}
                documentType="invoice"
                variant="outline"
                size="sm"
              />
            )
          )}
        </div>
      </div>
    </div>
  );
}
