import { NextRequest, NextResponse } from 'next/server';
import { emailTemplateService } from '@/services/email-templates';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log(`[EMAIL-TEMPLATES-API] Getting template: ${id}`);

    const template = await emailTemplateService.getTemplateById(id);
    
    if (!template) {
      return NextResponse.json({
        success: false,
        error: 'Template not found'
      }, { status: 404 });
    }

    console.log(`[EMAIL-TEMPLATES-API] Retrieved template: ${template.template_key}`);

    return NextResponse.json({
      success: true,
      template,
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('[EMAIL-TEMPLATES-API] Failed to get template:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to retrieve email template',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log(`[EMAIL-TEMPLATES-API] Updating template: ${id}`);

    const body = await request.json();
    const { updates, changedBy, changeReason } = body;

    if (!updates || typeof updates !== 'object') {
      return NextResponse.json({
        success: false,
        error: 'Invalid updates data'
      }, { status: 400 });
    }

    if (!changedBy) {
      return NextResponse.json({
        success: false,
        error: 'changedBy is required'
      }, { status: 400 });
    }

    const updatedTemplate = await emailTemplateService.updateTemplate(
      id,
      updates,
      changedBy,
      changeReason
    );

    console.log(`[EMAIL-TEMPLATES-API] Template updated successfully: ${id}`);

    return NextResponse.json({
      success: true,
      message: 'Email template updated successfully',
      template: updatedTemplate,
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('[EMAIL-TEMPLATES-API] Failed to update template:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update email template',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log(`[EMAIL-TEMPLATES-API] Deleting template: ${id}`);

    const body = await request.json();
    const { changedBy } = body;

    if (!changedBy) {
      return NextResponse.json({
        success: false,
        error: 'changedBy is required'
      }, { status: 400 });
    }

    await emailTemplateService.deleteTemplate(id, changedBy);

    console.log(`[EMAIL-TEMPLATES-API] Template deleted successfully: ${id}`);

    return NextResponse.json({
      success: true,
      message: 'Email template deleted successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('[EMAIL-TEMPLATES-API] Failed to delete template:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete email template',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
