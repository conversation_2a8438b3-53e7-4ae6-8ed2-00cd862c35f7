'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  CardTitle,
  Button,
  Input,
} from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import {
  FiFileText,
  FiMail,
  FiDownload,
  FiRefreshCw,
  FiPlus,
  FiCalendar,
  FiDollarSign,
  FiCheck,
  FiRotateCcw,
} from 'react-icons/fi';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency, formatDate } from '@/lib/pdf-generation/utils';
import EmailReceiptModal from '@/components/admin/modals/EmailReceiptModal';

interface ReceiptRecord {
  id: string;
  registration_id: string;
  registration_type: 'attendee' | 'speaker' | 'sponsor';
  full_name: string;
  email: string;
  organization: string;
  grand_total: number;
  receipt_url: string | null;
  receipt_generated_at: string | null;
  payment_status: string;
  payment_id: string | null;
  created_at: string;
}

interface ReceiptFilters {
  search: string;
  registrationType: string;
  hasReceipt: string;
  paymentStatus: string;
}

export default function ReceiptsPage() {
  const [receipts, setReceipts] = useState<ReceiptRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedReceipt, setSelectedReceipt] = useState<ReceiptRecord | null>(
    null
  );
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [regeneratingReceipts, setRegeneratingReceipts] = useState<Set<string>>(
    new Set()
  );

  const [filters, setFilters] = useState<ReceiptFilters>({
    search: '',
    registrationType: 'all',
    hasReceipt: 'all',
    paymentStatus: 'completed', // Default to completed payments (eligible for receipts)
  });

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0,
  });

  // Fetch receipt records from all registration tables
  const fetchReceipts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch from all registration tables using regular client
      const [attendeeResult, speakerResult] = await Promise.all([
        supabase
          .from('iepa_attendee_registrations')
          .select(
            'id, first_name, last_name, email, organization, grand_total, receipt_url, receipt_generated_at, payment_status, payment_id, created_at'
          )
          .order('last_name', { ascending: true, nullsFirst: false })
          .order('first_name', { ascending: true, nullsFirst: false }),
        supabase
          .from('iepa_speaker_registrations')
          .select(
            'id, first_name, last_name, email, organization_name, receipt_url, receipt_generated_at, created_at'
          )
          .order('last_name', { ascending: true, nullsFirst: false })
          .order('first_name', { ascending: true, nullsFirst: false }),
      ]);

      // Check for errors
      if (attendeeResult.error) throw attendeeResult.error;
      if (speakerResult.error) throw speakerResult.error;

      // Combine and format results - only include those eligible for receipts
      const allReceipts: ReceiptRecord[] = [
        // Attendees with completed payments get receipts
        ...(attendeeResult.data || [])
          .filter(item => item.payment_status === 'completed')
          .map(item => ({
            id: item.id,
            full_name: `${item.last_name}, ${item.first_name}`.trim(),
            email: item.email,
            organization: item.organization,
            registration_id: item.id,
            registration_type: 'attendee' as const,
            grand_total: item.grand_total || 0,
            receipt_url: item.receipt_url,
            receipt_generated_at: item.receipt_generated_at,
            payment_status: item.payment_status,
            payment_id: item.payment_id,
            created_at: item.created_at,
          })),
        // Speakers get receipts (comped registrations)
        ...(speakerResult.data || []).map(item => ({
          id: item.id,
          full_name: `${item.last_name}, ${item.first_name}`.trim(),
          email: item.email || '',
          organization: item.organization_name,
          registration_id: item.id,
          registration_type: 'speaker' as const,
          grand_total: 0, // Speakers typically don't pay
          receipt_url: item.receipt_url,
          receipt_generated_at: item.receipt_generated_at,
          payment_status: 'completed', // Speakers are comped
          payment_id: null,
          created_at: item.created_at,
        })),
        // Note: Sponsors don't get receipts, they get invoices (pay by check)
      ];

      // Apply filters
      let filteredReceipts = allReceipts;

      if (filters.search) {
        filteredReceipts = filteredReceipts.filter(
          receipt =>
            receipt.full_name
              .toLowerCase()
              .includes(filters.search.toLowerCase()) ||
            receipt.email
              .toLowerCase()
              .includes(filters.search.toLowerCase()) ||
            receipt.organization
              .toLowerCase()
              .includes(filters.search.toLowerCase())
        );
      }

      if (filters.registrationType && filters.registrationType !== 'all') {
        filteredReceipts = filteredReceipts.filter(
          receipt => receipt.registration_type === filters.registrationType
        );
      }

      if (filters.hasReceipt && filters.hasReceipt !== 'all') {
        filteredReceipts = filteredReceipts.filter(receipt =>
          filters.hasReceipt === 'yes'
            ? !!receipt.receipt_url
            : !receipt.receipt_url
        );
      }

      if (filters.paymentStatus && filters.paymentStatus !== 'all') {
        filteredReceipts = filteredReceipts.filter(
          receipt => receipt.payment_status === filters.paymentStatus
        );
      }

      // Apply pagination
      const total = filteredReceipts.length;
      const from = (pagination.page - 1) * pagination.limit;
      const to = from + pagination.limit;
      const paginatedReceipts = filteredReceipts.slice(from, to);

      setReceipts(paginatedReceipts);
      setPagination(prev => ({ ...prev, total }));
    } catch (err) {
      console.error('Error fetching receipts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch receipts');
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.page, pagination.limit]);

  // Download receipt for a registration
  const handleDownloadReceipt = async (receipt: ReceiptRecord) => {
    try {
      console.log(
        '🔽 [RECEIPT-DOWNLOAD] Starting download for:',
        receipt.registration_id
      );

      // Use the correct PDF download API for our custom PDFs
      const downloadApiUrl = `/api/pdf/download?registrationId=${receipt.registration_id}&registrationType=${receipt.registration_type}&documentType=receipt`;

      // Get the signed download URL
      const response = await fetch(downloadApiUrl);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get download URL');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to get download URL');
      }

      console.log('📥 [RECEIPT-DOWNLOAD] Got signed URL, initiating download');

      // Create a temporary link and trigger download using the signed URL
      const link = document.createElement('a');
      link.href = result.downloadUrl;
      link.download =
        result.fileName ||
        `receipt-${receipt.registration_type}-${receipt.registration_id.slice(-8)}.pdf`;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('✅ [RECEIPT-DOWNLOAD] Download initiated successfully');
    } catch (err) {
      console.error('❌ [RECEIPT-DOWNLOAD] Error downloading receipt:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to download receipt'
      );
    }
  };

  // Generate receipt for a registration
  const handleGenerateReceipt = async (
    receipt: ReceiptRecord,
    forceRegenerate = false
  ) => {
    const receiptKey = `${receipt.registration_type}-${receipt.registration_id}`;

    try {
      console.log('🎯 [RECEIPT-GEN] Starting receipt generation for:', {
        registrationId: receipt.registration_id,
        registrationType: receipt.registration_type,
        fullName: receipt.full_name,
        email: receipt.email,
        paymentStatus: receipt.payment_status,
        currentReceiptUrl: receipt.receipt_url,
        forceRegenerate,
      });

      // Add loading state for this specific receipt
      setRegeneratingReceipts(prev => new Set(prev).add(receiptKey));
      setError(null);

      console.log(
        '🚀 [RECEIPT-GEN] Making API call to /api/pdf/generate-receipt'
      );

      const response = await fetch('/api/pdf/generate-receipt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          registrationId: receipt.registration_id,
          registrationType: receipt.registration_type,
          forceRegenerate,
        }),
      });

      console.log('📡 [RECEIPT-GEN] API response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Receipt generation failed:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText,
          registrationDetails: {
            id: receipt.registration_id,
            type: receipt.registration_type,
            name: receipt.full_name,
            email: receipt.email,
          },
        });
        throw new Error(
          `Failed to generate receipt: ${response.status} ${response.statusText} - ${errorText}`
        );
      }

      const result = await response.json();
      console.log('✅ [RECEIPT-GEN] Receipt generated successfully:', result);

      // Show success message
      console.log('🔄 [RECEIPT-GEN] Refreshing receipt data...');

      // Refresh the receipt data
      await fetchReceipts();

      console.log(
        '✅ [RECEIPT-GEN] Receipt generation completed successfully!'
      );
    } catch (err) {
      console.error('❌ [RECEIPT-GEN] Error generating receipt:', err);
      console.error('❌ [RECEIPT-GEN] Error details:', {
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
        registrationId: receipt.registration_id,
      });
      setError(
        err instanceof Error ? err.message : 'Failed to generate receipt'
      );
    } finally {
      // Remove loading state for this specific receipt
      setRegeneratingReceipts(prev => {
        const newSet = new Set(prev);
        newSet.delete(receiptKey);
        return newSet;
      });
    }
  };

  // Send receipt via email
  const handleSendReceipt = async (receipt: ReceiptRecord) => {
    try {
      const response = await fetch('/api/admin/send-receipt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          registrationId: receipt.registration_id,
          registrationType: receipt.registration_type,
          email: receipt.email,
          fullName: receipt.full_name,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send receipt');
      }

      const result = await response.json();
      console.log('Receipt sent:', result);

      // Show success message
      setError(null);
      // You could add a success toast here
    } catch (err) {
      console.error('Error sending receipt:', err);
      setError(err instanceof Error ? err.message : 'Failed to send receipt');
    }
  };

  // Bulk generate receipts
  const handleBulkGenerate = async () => {
    try {
      setLoading(true);

      // Get all registrations without receipts
      const registrationsWithoutReceipts = receipts.filter(
        receipt => !receipt.receipt_url
      );

      for (const receipt of registrationsWithoutReceipts) {
        await handleGenerateReceipt(receipt);
      }

      await fetchReceipts();
    } catch (err) {
      console.error('Error in bulk generation:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to generate receipts'
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReceipts();
  }, [fetchReceipts]);

  const getRegistrationTypeBadge = (type: string) => {
    switch (type) {
      case 'attendee':
        return <Badge className="bg-blue-100 text-blue-800">Attendee</Badge>;
      case 'speaker':
        return <Badge className="bg-green-100 text-green-800">Speaker</Badge>;
      default:
        return <Badge variant="secondary">{type}</Badge>;
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);
  const receiptsWithoutReceipt = receipts.filter(
    receipt => !receipt.receipt_url
  ).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Receipt Management
          </h1>
          <p className="text-gray-600 mt-1">
            Generate and send receipts for completed registrations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={handleBulkGenerate}
            disabled={loading || receiptsWithoutReceipt === 0}
            variant="outline"
            size="sm"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            Bulk Generate ({receiptsWithoutReceipt})
          </Button>
          <Button onClick={fetchReceipts} variant="outline" size="sm">
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Eligible
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {pagination.total}
                </p>
              </div>
              <FiCheck className="w-8 h-8 text-gray-400" />
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  With Receipts
                </p>
                <p className="text-2xl font-bold text-green-600">
                  {receipts.filter(r => r.receipt_url).length}
                </p>
              </div>
              <FiDownload className="w-8 h-8 text-green-400" />
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Pending Receipts
                </p>
                <p className="text-2xl font-bold text-yellow-600">
                  {receiptsWithoutReceipt}
                </p>
              </div>
              <FiCalendar className="w-8 h-8 text-yellow-400" />
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Paid</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatCurrency(
                    receipts.reduce((sum, rec) => sum + rec.grand_total, 0)
                  )}
                </p>
              </div>
              <FiDollarSign className="w-8 h-8 text-blue-400" />
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Input
                placeholder="Search registrations..."
                value={filters.search}
                onChange={e =>
                  setFilters(prev => ({ ...prev, search: e.target.value }))
                }
                className="w-full"
              />
            </div>
            <Select
              value={filters.registrationType}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, registrationType: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Registration Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="attendee">Attendee</SelectItem>
                <SelectItem value="speaker">Speaker</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.hasReceipt}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, hasReceipt: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Receipt Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="yes">Has Receipt</SelectItem>
                <SelectItem value="no">No Receipt</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.paymentStatus}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, paymentStatus: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Payment Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody>
            <p className="text-red-600">{error}</p>
          </CardBody>
        </Card>
      )}

      {/* Receipts Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Receipt Records ({pagination.total})</span>
            <div className="text-sm text-gray-500">
              Page {pagination.page} of {totalPages}
            </div>
          </CardTitle>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="text-center py-8">
              <FiRefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">Loading receipt records...</p>
            </div>
          ) : receipts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No receipt records found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Organization</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Payment</TableHead>
                    <TableHead>Receipt</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {receipts.map(receipt => (
                    <TableRow
                      key={`${receipt.registration_type}-${receipt.registration_id}`}
                    >
                      <TableCell className="font-medium">
                        {receipt.full_name}
                      </TableCell>
                      <TableCell>{receipt.email}</TableCell>
                      <TableCell>{receipt.organization}</TableCell>
                      <TableCell>
                        {getRegistrationTypeBadge(receipt.registration_type)}
                      </TableCell>
                      <TableCell>
                        {formatCurrency(receipt.grand_total)}
                      </TableCell>
                      <TableCell>
                        {getPaymentStatusBadge(receipt.payment_status)}
                      </TableCell>
                      <TableCell>
                        {receipt.receipt_url ? (
                          <div className="flex items-center space-x-2">
                            <Badge className="bg-green-100 text-green-800">
                              Generated
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {receipt.receipt_generated_at &&
                                formatDate(
                                  new Date(receipt.receipt_generated_at)
                                )}
                            </span>
                          </div>
                        ) : (
                          <Badge variant="secondary">Not Generated</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {receipt.receipt_url ? (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDownloadReceipt(receipt)}
                                title="Download Receipt"
                              >
                                <FiDownload className="w-4 h-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleSendReceipt(receipt)}
                                title="Send Receipt via Email"
                              >
                                <FiMail className="w-4 h-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  handleGenerateReceipt(receipt, true)
                                }
                                title="Force Regenerate Receipt"
                                disabled={regeneratingReceipts.has(
                                  `${receipt.registration_type}-${receipt.registration_id}`
                                )}
                              >
                                {regeneratingReceipts.has(
                                  `${receipt.registration_type}-${receipt.registration_id}`
                                ) ? (
                                  <FiRefreshCw className="w-4 h-4 animate-spin" />
                                ) : (
                                  <FiRotateCcw className="w-4 h-4" />
                                )}
                              </Button>
                            </>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleGenerateReceipt(receipt)}
                              title="Generate Receipt"
                              disabled={regeneratingReceipts.has(
                                `${receipt.registration_type}-${receipt.registration_id}`
                              )}
                            >
                              {regeneratingReceipts.has(
                                `${receipt.registration_type}-${receipt.registration_id}`
                              ) ? (
                                <FiRefreshCw className="w-4 h-4 animate-spin mr-1" />
                              ) : (
                                <FiFileText className="w-4 h-4 mr-1" />
                              )}
                              Generate
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-500">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{' '}
                of {pagination.total} results
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPagination(prev => ({ ...prev, page: prev.page - 1 }))
                  }
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPagination(prev => ({ ...prev, page: prev.page + 1 }))
                  }
                  disabled={pagination.page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Email Modal */}
      {selectedReceipt && (
        <EmailReceiptModal
          receipt={selectedReceipt}
          open={showEmailModal}
          onClose={() => {
            setShowEmailModal(false);
            setSelectedReceipt(null);
          }}
          onSent={() => {
            setShowEmailModal(false);
            setSelectedReceipt(null);
          }}
        />
      )}
    </div>
  );
}
