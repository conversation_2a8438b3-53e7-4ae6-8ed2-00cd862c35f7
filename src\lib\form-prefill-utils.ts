// Form Prefill Utilities for IEPA Conference Registration
// Provides automatic form prefilling from user profile data

import { UserProfile } from '@/lib/user-profile-utils';
import { phoneUtils } from '@/utils/schema-utils';

export interface FormPrefillData {
  // Personal Information
  firstName?: string;
  lastName?: string;
  email?: string;
  nameOnBadge?: string;
  
  // Contact Information
  phoneNumber?: string;
  
  // Professional Information
  organization?: string;
  organizationName?: string; // Speaker form uses this field name
  jobTitle?: string;
  
  // Address Information
  streetAddress?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  
  // Additional fields that might be in profile
  gender?: string;
  preferredNameOnBadge?: string;
}

export interface PrefillResult {
  data: FormPrefillData;
  fieldsPopulated: string[];
  source: 'profile' | 'none';
}

/**
 * Field mapping between iepa_user_profiles and form fields
 */
const PROFILE_TO_FORM_MAPPING: Record<keyof UserProfile, string[]> = {
  first_name: ['firstName'],
  last_name: ['lastName'],
  email: ['email'],
  phone_number: ['phoneNumber'],
  organization: ['organization', 'organizationName'],
  job_title: ['jobTitle'],
  street_address: ['streetAddress'],
  city: ['city'],
  state: ['state'],
  zip_code: ['zipCode'],
  country: ['country'],
  gender: ['gender'],
  preferred_name_on_badge: ['nameOnBadge', 'preferredNameOnBadge'],
  // Fields we don't map to forms
  id: [],
  user_id: [],
  full_name: [],
  imported_from_2024: [],
  import_date: [],
  created_at: [],
  updated_at: [],
};

/**
 * Transform user profile data into form prefill data
 */
export const transformProfileToFormData = (
  profile: UserProfile,
  formType: 'attendee' | 'speaker' = 'attendee'
): PrefillResult => {
  const formData: FormPrefillData = {};
  const fieldsPopulated: string[] = [];

  // Map each profile field to form fields
  Object.entries(PROFILE_TO_FORM_MAPPING).forEach(([profileField, formFields]) => {
    const profileValue = profile[profileField as keyof UserProfile];
    
    if (profileValue && formFields.length > 0) {
      formFields.forEach(formField => {
        // Special handling for different field types
        if (formField === 'phoneNumber' && typeof profileValue === 'string') {
          // Format phone number using existing utility
          formData[formField as keyof FormPrefillData] = phoneUtils.parseAndFormat(profileValue);
        } else if (formField === 'nameOnBadge' || formField === 'preferredNameOnBadge') {
          // Use preferred name on badge if available, otherwise construct from first/last name
          if (profile.preferred_name_on_badge) {
            formData[formField as keyof FormPrefillData] = profile.preferred_name_on_badge;
          } else if (profile.first_name && profile.last_name) {
            formData[formField as keyof FormPrefillData] = `${profile.first_name} ${profile.last_name}`;
          }
        } else if (formField === 'organizationName' && formType === 'speaker') {
          // Speaker form uses organizationName instead of organization
          formData.organizationName = profileValue as string;
        } else {
          // Direct mapping for most fields
          formData[formField as keyof FormPrefillData] = profileValue as string;
        }
        
        if (formData[formField as keyof FormPrefillData]) {
          fieldsPopulated.push(formField);
        }
      });
    }
  });

  return {
    data: formData,
    fieldsPopulated,
    source: 'profile'
  };
};

/**
 * Merge prefill data with existing form data, respecting user input
 */
export const mergeWithExistingData = (
  existingData: any,
  prefillData: FormPrefillData
): { mergedData: any; newFieldsPopulated: string[] } => {
  const mergedData = { ...existingData };
  const newFieldsPopulated: string[] = [];

  // Only populate fields that are empty or undefined in existing data
  Object.entries(prefillData).forEach(([field, value]) => {
    if (value && (!existingData[field] || existingData[field] === '')) {
      mergedData[field] = value;
      newFieldsPopulated.push(field);
    }
  });

  return { mergedData, newFieldsPopulated };
};

/**
 * Check if form data appears to be empty (for determining if we should prefill)
 */
export const isFormDataEmpty = (formData: any): boolean => {
  const significantFields = [
    'firstName', 'lastName', 'email', 'phoneNumber', 
    'organization', 'organizationName', 'jobTitle'
  ];
  
  return significantFields.every(field => 
    !formData[field] || formData[field] === ''
  );
};

/**
 * Generate user-friendly field names for notifications
 */
export const getFieldDisplayName = (fieldName: string): string => {
  const displayNames: Record<string, string> = {
    firstName: 'First Name',
    lastName: 'Last Name',
    email: 'Email Address',
    phoneNumber: 'Phone Number',
    organization: 'Organization',
    organizationName: 'Organization',
    jobTitle: 'Job Title',
    streetAddress: 'Street Address',
    city: 'City',
    state: 'State/Province',
    zipCode: 'ZIP/Postal Code',
    country: 'Country',
    nameOnBadge: 'Name on Badge',
    gender: 'Gender',
  };
  
  return displayNames[fieldName] || fieldName;
};

/**
 * Create a summary message for prefilled fields
 */
export const createPrefillSummary = (fieldsPopulated: string[]): string => {
  if (fieldsPopulated.length === 0) {
    return 'No fields were populated from your profile.';
  }
  
  if (fieldsPopulated.length === 1) {
    return `${getFieldDisplayName(fieldsPopulated[0])} was populated from your account.`;
  }
  
  if (fieldsPopulated.length <= 3) {
    const names = fieldsPopulated.map(getFieldDisplayName);
    return `${names.join(', ')} were populated from your account.`;
  }
  
  return `${fieldsPopulated.length} fields were populated from your account information.`;
};
