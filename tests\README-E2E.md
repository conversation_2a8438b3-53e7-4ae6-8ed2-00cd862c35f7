# IEPA Member Registration E2E Test

This document describes the end-to-end test for the IEPA member registration flow, including golf tournament registration and promo code application.

## Test Overview

The E2E test simulates a complete user journey:

1. **Navigation** - Go to the attendee registration page
2. **Registration Type** - Select "IEPA Member" registration
3. **Personal Information** - Fill out user details
4. **Contact Information** - Add contact and organization details
5. **Event Options** - Add golf tournament with right-handed club rental
6. **Emergency Contact** - Provide emergency contact information
7. **Checkout** - Apply "TEST" promo code for 100% discount
8. **Payment** - Complete Stripe payment (should be $0)
9. **Verification** - Confirm registration appears in "My Registrations"
10. **Email Verification** - Check that welcome email is sent

## Prerequisites

### 1. Environment Setup

Ensure your local development environment is configured:

```bash
# Install dependencies
npm install

# Install Playwright browsers
npx playwright install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration
```

### 2. Database Setup

The test requires a working database with:

- User authentication system
- Registration tables
- Discount codes table with "TEST" code configured

### 3. Email Configuration

For email verification:

- SendGrid API key configured
- Email logging enabled
- Admin access to email logs (optional)

### 4. Stripe Configuration

For payment testing:

- Stripe test keys configured
- Webhook endpoints set up
- Test discount codes in Stripe (optional)

## Running the Test

### Quick Start

```bash
# Run the IEPA member registration test
npm run test:e2e:iepa-member

# Run with browser visible (headed mode)
npm run test:e2e:iepa-member:headed
```

### Manual Playwright Commands

```bash
# Run specific test file
npx playwright test tests/iepa-member-registration-e2e.spec.js

# Run with specific browser
npx playwright test tests/iepa-member-registration-e2e.spec.js --project=chromium

# Run in headed mode (browser visible)
npx playwright test tests/iepa-member-registration-e2e.spec.js --headed

# Run with debug mode
npx playwright test tests/iepa-member-registration-e2e.spec.js --debug

# Generate and view report
npx playwright test tests/iepa-member-registration-e2e.spec.js
npx playwright show-report
```

### Development Server

The test expects the development server to be running on port 6969:

```bash
# Start development server
npm run dev

# Or specify port explicitly
PORT=6969 npm run dev
```

## Test Configuration

### Test Data

The test uses the following test data (configured in the test file):

```javascript
const TEST_CONFIG = {
  testUser: {
    firstName: 'John',
    lastName: 'Tester',
    email: '<EMAIL>',
    // ... other fields
  },
  registration: {
    type: 'iepa-member',
    golfClubHandedness: 'right-handed',
  },
  promoCode: 'TEST',
};
```

### Timeouts

- **Navigation**: 30 seconds
- **Form Fill**: 5 seconds per field
- **Payment**: 60 seconds
- **Email**: 30 seconds

### Screenshots

The test automatically takes screenshots at each major step:

- `01-registration-page.png`
- `02-registration-type-selected.png`
- `03-personal-info-filled.png`
- `04-contact-info-filled.png`
- `05-event-options-configured.png`
- `06-emergency-contact-filled.png`
- `07-checkout-page.png`
- `08-promo-code-applied.png`
- `09-payment-processing.png`
- `10-payment-success.png`
- `11-my-registrations.png`

Screenshots are saved to `test-results/artifacts/`

## Test Results

### Success Indicators

✅ **Registration Type Selection** - IEPA Member option selected  
✅ **Form Completion** - All required fields filled  
✅ **Golf Options** - Tournament and club rental added  
✅ **Promo Code** - TEST code applied, total shows $0  
✅ **Payment** - Stripe checkout completed successfully  
✅ **Confirmation** - Success page displayed  
✅ **Registration Record** - Appears in My Registrations  
✅ **Email** - Welcome email sent (if verifiable)

### Common Issues

❌ **Form Validation Errors** - Check required fields  
❌ **Promo Code Rejection** - Verify TEST code exists in database  
❌ **Payment Failure** - Check Stripe configuration  
❌ **Email Not Sent** - Verify SendGrid configuration  
❌ **Registration Not Found** - Check database connectivity

## Debugging

### Enable Debug Mode

```bash
# Run with Playwright inspector
npx playwright test tests/iepa-member-registration-e2e.spec.js --debug

# Run with browser console
npx playwright test tests/iepa-member-registration-e2e.spec.js --headed
```

### Check Logs

```bash
# View development server logs
npm run dev

# Check browser console in headed mode
# Check network tab for API calls
# Verify form submissions
```

### Database Verification

```bash
# Check if test registration was created
# Verify discount code exists
# Check email logs table
```

## Extending the Test

### Adding New Steps

1. Add helper method to `RegistrationHelpers` class
2. Call method in main test flow
3. Add screenshot capture
4. Update documentation

### Testing Different Scenarios

Create additional test cases:

- Non-IEPA member registration
- Registration without golf
- Different promo codes
- Payment failures
- Form validation errors

### Cross-Browser Testing

```bash
# Test on all browsers
npx playwright test tests/iepa-member-registration-e2e.spec.js

# Test specific browsers
npx playwright test tests/iepa-member-registration-e2e.spec.js --project=firefox
npx playwright test tests/iepa-member-registration-e2e.spec.js --project=webkit
```

## Continuous Integration

For CI/CD pipelines:

```bash
# Install dependencies
npm ci
npx playwright install --with-deps

# Run tests
npm run test:e2e:iepa-member

# Generate reports
npx playwright show-report --reporter=html
```

## Support

For issues with the E2E test:

1. Check this documentation
2. Verify environment configuration
3. Review test logs and screenshots
4. Check application logs
5. Contact development team

---

**Last Updated**: June 2025  
**Test Version**: 1.0.0  
**Playwright Version**: Latest
