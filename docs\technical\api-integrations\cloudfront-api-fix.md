# CloudFront API Configuration Fix

## Problem Summary

CloudFront is blocking POST requests to API routes with 403 errors, preventing payment processing.

**Error**: "This distribution is not configured to allow the HTTP request method that was used for this request"

## Solution Steps

### 1. Identify CloudFront Distribution

1. Log into AWS Console
2. Navigate to CloudFront service
3. Find the distribution serving your domain
4. Note the Distribution ID

### 2. Configure Cache Behaviors for API Routes

#### Create New Cache Behavior for API Routes

1. Go to **Behaviors** tab in your CloudFront distribution
2. Click **Create Behavior**
3. Configure the following settings:

**Path Pattern**: `/api/*`

**Origin and Origin Groups**:

- Select your origin (likely pointing to your Next.js app)

**Viewer Protocol Policy**:

- `Redirect HTTP to HTTPS` (recommended)

**Allowed HTTP Methods**:

- ✅ **GET, HEAD, OPTIONS, PUT, POST, PATCH, DELETE**
- (This is the critical setting - must include POST, PUT, PATCH, DELETE)

**Cache Key and Origin Requests**:

- **Cache Policy**: `CachingDisabled` (or create custom policy)
- **Origin Request Policy**: `CORS-S3Origin` or `AllViewer`

**Response Headers Policy**:

- `CORS-With-Preflight` (if using CORS)

#### Alternative: Modify Existing Behavior

If you have an existing catch-all behavior (`*`):

1. Edit the default behavior
2. Change **Allowed HTTP Methods** to include all methods
3. Ensure caching is disabled for dynamic content

### 3. Cache Policy Configuration

#### Option A: Use Existing Policy

- Select **CachingDisabled** for API routes

#### Option B: Create Custom Policy

1. Go to **Policies** → **Cache Policies**
2. Click **Create Cache Policy**
3. Configure:
   - **Name**: `API-No-Cache`
   - **TTL Settings**:
     - Default TTL: `0`
     - Maximum TTL: `0`
   - **Cache Key Settings**:
     - Include all query strings
     - Include all headers
     - Include all cookies

### 4. Origin Request Policy

Ensure API requests forward all necessary data:

- **Query Strings**: All
- **Headers**: All or whitelist required headers
- **Cookies**: All (if using session-based auth)

### 5. Deploy and Test

#### Deploy Changes

1. Click **Save Changes**
2. Wait for deployment (5-15 minutes)
3. Status will show "Deployed" when ready

#### Test Configuration

```bash
# Test POST request
curl -X POST https://your-domain.com/api/stripe/test-config \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# Should return 200, not 403
```

### 6. Verification Checklist

- [ ] CloudFront behavior created for `/api/*`
- [ ] All HTTP methods allowed (GET, HEAD, OPTIONS, PUT, POST, PATCH, DELETE)
- [ ] Caching disabled for API routes (TTL = 0)
- [ ] All query strings and headers forwarded
- [ ] Changes deployed successfully
- [ ] POST requests return 200 from browser
- [ ] Payment flow test successful

## Alternative Solutions

### Option 1: Bypass CloudFront for API Routes

Configure your DNS to point API subdomain directly to origin:

- `api.yourdomain.com` → Direct to server
- `yourdomain.com` → Through CloudFront

### Option 2: Use Different Path for APIs

Move APIs to a path that bypasses CloudFront:

- Change API routes from `/api/*` to `/server-api/*`
- Configure CloudFront to not cache `/server-api/*`

### Option 3: Disable CloudFront Temporarily

For testing purposes only:

- Point domain directly to origin server
- Test payment flow without CloudFront
- Re-enable CloudFront after configuration

## Common Issues

### Issue: Changes Not Taking Effect

**Solution**:

- Wait for full deployment (up to 15 minutes)
- Clear browser cache
- Try incognito/private browsing mode

### Issue: Still Getting 403 Errors

**Check**:

- Behavior order (more specific paths should be first)
- Origin configuration
- Security groups and firewall rules

### Issue: CORS Errors After Fix

**Solution**:

- Add CORS response headers policy
- Ensure OPTIONS requests are allowed
- Configure proper CORS headers in your API

## Testing Commands

```bash
# Test API connectivity
curl -X GET https://your-domain.com/api/stripe/test-config

# Test POST request
curl -X POST https://your-domain.com/api/stripe/create-checkout-session \
  -H "Content-Type: application/json" \
  -d '{
    "registrationId": "test-123",
    "registrationType": "attendee",
    "customerEmail": "<EMAIL>",
    "totalAmount": 100
  }'

# Check response headers
curl -I https://your-domain.com/api/stripe/test-config
```

## Next Steps After Fix

1. **Test Payment Flow**

   - Complete checkout session creation
   - Verify Stripe redirect
   - Test payment completion

2. **Configure Webhooks**

   - Set up webhook endpoint in Stripe
   - Update webhook secret in environment

3. **End-to-End Testing**
   - Test full registration → payment → confirmation flow
   - Verify database updates
   - Test error scenarios

## Support Resources

- [AWS CloudFront Documentation](https://docs.aws.amazon.com/cloudfront/)
- [CloudFront Cache Behaviors](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/distribution-web-values-specify.html#DownloadDistValuesCacheBehavior)
- [HTTP Methods in CloudFront](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/distribution-web-values-specify.html#DownloadDistValuesAllowedHTTPMethods)
