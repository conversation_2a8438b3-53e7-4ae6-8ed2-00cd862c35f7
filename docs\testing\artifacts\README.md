# Testing Artifacts

This directory contains test artifacts, sample files, and testing utilities used during the development and testing of the IEPA Conference Registration System.

## 📋 Available Artifacts

### Test HTML Files

#### [test-download.html](./test-download.html)
Test file for download functionality validation including:
- File download testing procedures
- Browser compatibility testing
- Download link validation

#### [test-hover-effects.html](./test-hover-effects.html)
Test file for UI hover effects and interactions including:
- CSS hover state testing
- Interactive element validation
- User experience testing

### Test PDF Files

#### [test-invoice.pdf](./test-invoice.pdf)
Sample invoice PDF for testing invoice generation and delivery including:
- PDF format validation
- Invoice template testing
- Email attachment testing

#### [test-invoice-fixed.pdf](./test-invoice-fixed.pdf)
Updated test invoice PDF with fixes applied including:
- Corrected formatting and layout
- Fixed data population
- Improved template structure

### Test JavaScript Files

#### [test-night-selection.js](./test-night-selection.js)
JavaScript test file for night selection functionality including:
- Form interaction testing
- Date selection validation
- Dynamic pricing calculation testing

### Test Screenshots

#### [error-screenshot.png](./error-screenshot.png)
Screenshot of error state for debugging and documentation including:
- Error message display validation
- UI error state documentation
- Bug reproduction evidence

## 🎯 Purpose and Usage

### Development Testing
- **HTML Files**: Used for testing specific UI components and functionality
- **PDF Files**: Used for testing invoice generation and email attachment systems
- **JavaScript Files**: Used for testing form interactions and dynamic functionality
- **Screenshots**: Used for bug documentation and visual regression testing

### Quality Assurance
- **Validation Testing**: Artifacts used to validate specific system behaviors
- **Regression Testing**: Reference files for ensuring consistent functionality
- **Bug Documentation**: Visual evidence and reproduction materials

### Documentation
- **Visual References**: Screenshots and files that document system behavior
- **Test Examples**: Sample files that demonstrate expected system outputs
- **Debugging Materials**: Artifacts that help troubleshoot issues

## 🔧 Artifact Categories

### UI Testing Artifacts
- HTML test files for component testing
- Screenshots for visual validation
- Interactive element testing files

### PDF Testing Artifacts
- Sample invoice PDFs for template validation
- Test documents for attachment functionality
- Format and layout validation files

### Functional Testing Artifacts
- JavaScript files for form and interaction testing
- Dynamic functionality validation scripts
- User experience testing materials

### Debug and Documentation Artifacts
- Error screenshots for issue documentation
- Visual evidence for bug reports
- System behavior documentation

## 🚀 Usage Guidelines

### For Developers
1. Use HTML test files to validate UI components during development
2. Reference PDF samples when working on invoice generation
3. Use JavaScript test files for form interaction development

### For QA Engineers
1. Use artifacts as reference materials for test case development
2. Compare system outputs against sample files
3. Use screenshots for visual regression testing

### For Bug Reporting
1. Reference error screenshots when documenting issues
2. Use test files to reproduce specific problems
3. Include relevant artifacts in bug reports

## 🔄 Maintenance

### Regular Updates
- Update test files when system functionality changes
- Refresh sample PDFs when invoice templates are modified
- Update screenshots when UI changes are implemented

### Cleanup Procedures
- Remove outdated test files that are no longer relevant
- Archive historical artifacts that may be needed for reference
- Maintain current versions of all test materials

### Version Control
- Track changes to test artifacts alongside code changes
- Maintain historical versions for regression testing
- Document the purpose and usage of each artifact

## ⚠️ Important Notes

### File Usage
- **Test Files Only**: These files are for testing purposes and should not be used in production
- **Sample Data**: All data in these files is test data and not real user information
- **Development Environment**: These artifacts are intended for development and testing environments only

### Security Considerations
- Test files do not contain sensitive or real user data
- PDF files use sample data for testing purposes
- Screenshots may contain test environment information only

## 📞 Support

For questions about testing artifacts:
- Reference the **[Testing Procedures](../procedures/README.md)** for context on artifact usage
- Check the **[Main Testing Documentation](../README.md)** for overall testing approach
- Contact the QA team for specific artifact questions

---

**Document Information**  
**Document Type**: Directory README  
**Last Updated**: December 2024  
**Document Version**: 1.0  
**System Version**: v0.1.0  
**Prepared By**: Technical Team
