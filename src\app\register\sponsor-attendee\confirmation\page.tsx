'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardBody, CardHeader } from '@heroui/react';
import {
  CheckCircle,
  FileText,
  Mail,
  Calendar,
  User,
  Building,
  DollarSign,
} from 'lucide-react';
import { CONFERENCE_YEAR } from '@/lib/conference-config';

interface ConfirmationData {
  registrationId: string;
  grandTotal: number;
  email: string;
  name: string;
  organization: string;
  sponsorName: string;
}

function SponsorAttendeeConfirmationContent() {
  const searchParams = useSearchParams();
  const [confirmationData, setConfirmationData] =
    useState<ConfirmationData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const dataParam = searchParams.get('data');
    if (dataParam) {
      try {
        const data = JSON.parse(decodeURIComponent(dataParam));
        setConfirmationData(data);
      } catch (error) {
        console.error('Error parsing confirmation data:', error);
      }
    }
    setLoading(false);
  }, [searchParams]);

  if (loading) {
    return (
      <div className="iepa-container">
        <div className="max-w-4xl mx-auto text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="iepa-body">Loading confirmation details...</p>
        </div>
      </div>
    );
  }

  if (!confirmationData) {
    return (
      <div className="iepa-container">
        <div className="max-w-4xl mx-auto text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h1 className="iepa-h1 text-red-800 mb-4">
              Confirmation Not Found
            </h1>
            <p className="iepa-body text-red-700 mb-6">
              We couldn&apos;t find your registration confirmation details. This
              may happen if:
            </p>
            <ul className="text-left text-red-700 space-y-2 mb-6">
              <li>• The confirmation link is invalid or expired</li>
              <li>
                • You accessed this page directly without completing
                registration
              </li>
              <li>• There was an error during the registration process</li>
            </ul>
            <p className="iepa-body text-red-700">
              Please contact{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-red-800 underline"
              >
                <EMAIL>
              </a>{' '}
              for assistance.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      <div className="max-w-4xl mx-auto py-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-600" />
          </div>
          <h1 className="iepa-h1 text-green-800 mb-2">
            Sponsor Attendee Registration Confirmed!
          </h1>
          <p className="iepa-body-large text-gray-600">
            Thank you for registering for the IEPA {CONFERENCE_YEAR} Annual
            Meeting
          </p>
        </div>

        {/* Registration Details */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Attendee Information */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                <h2 className="iepa-h3">Attendee Information</h2>
              </div>
            </CardHeader>
            <CardBody className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-500">Name</p>
                <p className="iepa-body">{confirmationData.name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Email</p>
                <p className="iepa-body">{confirmationData.email}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Organization
                </p>
                <p className="iepa-body">{confirmationData.organization}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Sponsor</p>
                <p className="iepa-body">{confirmationData.sponsorName}</p>
              </div>
            </CardBody>
          </Card>

          {/* Registration Summary */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                <h2 className="iepa-h3">Registration Summary</h2>
              </div>
            </CardHeader>
            <CardBody className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Registration Type
                </p>
                <p className="iepa-body">Sponsor Attendee</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Registration ID
                </p>
                <p className="iepa-body font-mono text-sm">
                  {confirmationData.registrationId}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Total Amount
                </p>
                <p className="iepa-body font-semibold">
                  {confirmationData.grandTotal > 0
                    ? `$${confirmationData.grandTotal.toLocaleString()}`
                    : 'Complimentary'}
                </p>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Next Steps */}
        <Card className="mt-6">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              <h2 className="iepa-h3">Next Steps</h2>
            </div>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Mail className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="iepa-body font-medium">Confirmation Email</p>
                  <p className="iepa-body-small text-gray-600">
                    You will receive a confirmation email at{' '}
                    {confirmationData.email} within the next few minutes.
                  </p>
                </div>
              </div>

              {confirmationData.grandTotal > 0 && (
                <div className="flex items-start gap-3">
                  <DollarSign className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="iepa-body font-medium">Payment Processing</p>
                    <p className="iepa-body-small text-gray-600">
                      Your payment is being processed. You will receive a
                      receipt once payment is confirmed.
                    </p>
                  </div>
                </div>
              )}

              <div className="flex items-start gap-3">
                <Building className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="iepa-body font-medium">Conference Details</p>
                  <p className="iepa-body-small text-gray-600">
                    Additional conference information and agenda details will be
                    sent closer to the event date.
                  </p>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Contact Information */}
        <div className="mt-8 text-center">
          <p className="iepa-body-small text-gray-600">
            Questions about your registration? Contact us at{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function SponsorAttendeeConfirmationPage() {
  return (
    <Suspense
      fallback={
        <div className="iepa-container">
          <div className="max-w-4xl mx-auto text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="iepa-body">Loading confirmation details...</p>
          </div>
        </div>
      }
    >
      <SponsorAttendeeConfirmationContent />
    </Suspense>
  );
}
