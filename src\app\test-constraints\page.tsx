'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import { useRegistrationConstraints, useUserRegistrationStatus } from '@/hooks/useRegistrationConstraints';

export default function TestConstraintsPage() {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Test the constraint hooks
  const attendeeConstraints = useRegistrationConstraints({
    registrationType: 'attendee',
    attendeeType: 'attendee',
    autoCheck: !!user,
  });

  const speakerConstraints = useRegistrationConstraints({
    registrationType: 'speaker',
    autoCheck: !!user,
  });

  const sponsorConstraints = useRegistrationConstraints({
    registrationType: 'sponsor',
    autoCheck: !!user,
  });

  const userRegistrationStatus = useUserRegistrationStatus();

  const runConstraintTest = async (action: string, registrationType: string) => {
    if (!user) {
      alert('Please log in to run tests');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/test-constraints', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          userId: user.id,
          registrationType,
        }),
      });

      const result = await response.json();
      setTestResults(result);
    } catch (error) {
      console.error('Test failed:', error);
      setTestResults({
        success: false,
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setLoading(false);
    }
  };

  const applyConstraints = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-constraints', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'apply-constraints',
        }),
      });

      const result = await response.json();
      setTestResults(result);
    } catch (error) {
      console.error('Apply constraints failed:', error);
      setTestResults({
        success: false,
        error: 'Apply constraints failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <h1 className="iepa-heading-1 mb-6">Registration Constraints Test Page</h1>
          
          {!user && (
            <Card className="mb-6">
              <CardBody>
                <p className="text-center text-gray-600">
                  Please log in to test registration constraints.
                </p>
              </CardBody>
            </Card>
          )}

          {user && (
            <>
              {/* Current User Status */}
              <Card className="mb-6">
                <CardHeader>
                  <h2 className="iepa-heading-2">Current User Registration Status</h2>
                </CardHeader>
                <CardBody>
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h3 className="font-semibold mb-2">Attendee Registration</h3>
                      <p className="text-sm">
                        Can Register: {attendeeConstraints.constraintCheck?.canRegister ? '✅ Yes' : '❌ No'}
                      </p>
                      <p className="text-sm">
                        Has Registration: {userRegistrationStatus.hasAttendeeRegistration ? '✅ Yes' : '❌ No'}
                      </p>
                      {attendeeConstraints.constraintCheck?.message && (
                        <p className="text-xs text-gray-600 mt-1">
                          {attendeeConstraints.constraintCheck.message}
                        </p>
                      )}
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h3 className="font-semibold mb-2">Speaker Registration</h3>
                      <p className="text-sm">
                        Can Register: {speakerConstraints.constraintCheck?.canRegister ? '✅ Yes' : '❌ No'}
                      </p>
                      <p className="text-sm">
                        Has Registration: {userRegistrationStatus.hasSpeakerRegistration ? '✅ Yes' : '❌ No'}
                      </p>
                      {speakerConstraints.constraintCheck?.message && (
                        <p className="text-xs text-gray-600 mt-1">
                          {speakerConstraints.constraintCheck.message}
                        </p>
                      )}
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h3 className="font-semibold mb-2">Sponsor Registration</h3>
                      <p className="text-sm">
                        Can Register: {sponsorConstraints.constraintCheck?.canRegister ? '✅ Yes' : '❌ No'}
                      </p>
                      <p className="text-sm">
                        Has Registration: {userRegistrationStatus.hasSponsorRegistration ? '✅ Yes' : '❌ No'}
                      </p>
                      {sponsorConstraints.constraintCheck?.message && (
                        <p className="text-xs text-gray-600 mt-1">
                          {sponsorConstraints.constraintCheck.message}
                        </p>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Test Controls */}
              <Card className="mb-6">
                <CardHeader>
                  <h2 className="iepa-heading-2">Database Constraint Tests</h2>
                </CardHeader>
                <CardBody>
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold mb-2">Apply Database Constraints</h3>
                      <p className="text-sm text-gray-600 mb-2">
                        Apply the unique constraints to the database tables.
                      </p>
                      <Button
                        onClick={applyConstraints}
                        disabled={loading}
                        color="primary"
                      >
                        {loading ? 'Applying...' : 'Apply Constraints'}
                      </Button>
                    </div>

                    <div>
                      <h3 className="font-semibold mb-2">Test Duplicate Registration Prevention</h3>
                      <p className="text-sm text-gray-600 mb-2">
                        Test that the database prevents duplicate registrations.
                      </p>
                      <div className="flex gap-2">
                        <Button
                          onClick={() => runConstraintTest('test-duplicate-registration', 'attendee')}
                          disabled={loading}
                          variant="bordered"
                        >
                          Test Attendee
                        </Button>
                        <Button
                          onClick={() => runConstraintTest('test-duplicate-registration', 'speaker')}
                          disabled={loading}
                          variant="bordered"
                        >
                          Test Speaker
                        </Button>
                        <Button
                          onClick={() => runConstraintTest('test-duplicate-registration', 'sponsor')}
                          disabled={loading}
                          variant="bordered"
                        >
                          Test Sponsor
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Test Results */}
              {testResults && (
                <Card>
                  <CardHeader>
                    <h2 className="iepa-heading-2">Test Results</h2>
                  </CardHeader>
                  <CardBody>
                    <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                      {JSON.stringify(testResults, null, 2)}
                    </pre>
                  </CardBody>
                </Card>
              )}
            </>
          )}
        </div>
      </section>
    </div>
  );
}
