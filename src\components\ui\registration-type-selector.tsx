'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@/components/ui';
import Link from 'next/link';
import { FaUser, FaMicrophone, FaBuilding, FaStar, FaCheck, FaExclamationTriangle } from 'react-icons/fa';
import { cn } from '@/lib/utils';
import { useAvailableRegistrationTypes } from '@/hooks/useRegistrationConstraints';

interface RegistrationTypeSelectorProps {
  /**
   * Whether to show authentication-dependent button text
   * If true, shows "Login to Register" when user is not authenticated
   * If false, always shows "Register" text
   */
  showAuthButtons?: boolean;
  /**
   * Whether the user is authenticated (only used when showAuthButtons is true)
   */
  isAuthenticated?: boolean;
  /**
   * Optional custom class name for the container
   */
  className?: string;
  /**
   * Whether to show the section header
   */
  showHeader?: boolean;
  /**
   * Custom header title (defaults to "Choose Your Registration Type")
   */
  headerTitle?: string;
  /**
   * Custom header description
   */
  headerDescription?: string;
}

export function RegistrationTypeSelector({
  showAuthButtons = false,
  isAuthenticated = false,
  className = '',
  showHeader = true,
  headerTitle = 'Choose Your Registration Type',
  headerDescription = 'Select the registration option that best fits your needs. All registrations include access to annual meeting sessions, meals, and networking events.',
}: RegistrationTypeSelectorProps) {
  // Check available registration types if user is authenticated
  const { availableTypes, loading: constraintLoading } = useAvailableRegistrationTypes();
  return (
    <section className={`iepa-section ${className}`}>
      {showHeader && (
        <div className="text-center mb-12">
          <h2 className="iepa-heading-2 mb-4">{headerTitle}</h2>
          <p className="iepa-body max-w-2xl mx-auto">{headerDescription}</p>
        </div>
      )}

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
        {/* Attendee Registration */}
        <Card className={cn(
          'h-full transition-all duration-250 ease-out cursor-pointer group flex flex-col',
          'hover:-translate-y-1 hover:shadow-[0_8px_25px_rgba(0,0,0,0.15)] hover:bg-[var(--iepa-gray-50)]',
          'hover:border-2 hover:border-[var(--iepa-primary-blue)]',
          'focus-within:ring-2 focus-within:ring-[var(--iepa-primary-blue)]/30 focus-within:ring-offset-2',
          'focus-within:-translate-y-1 focus-within:shadow-[0_8px_25px_rgba(0,0,0,0.15)]',
          'focus-within:border-2 focus-within:border-[var(--iepa-primary-blue)]'
        )}>
          <CardHeader className="text-center pb-6 flex-grow">
            <div className="flex justify-center items-center mb-4 relative">
              <FaUser
                className="text-4xl transition-all duration-250 ease-out group-hover:scale-110"
                style={{ color: 'var(--iepa-primary-blue)' }}
              />
              {isAuthenticated && !constraintLoading && (
                <div className="absolute -top-2 -right-2">
                  {availableTypes.attendee ? (
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <FaCheck className="text-white text-xs" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                      <FaExclamationTriangle className="text-white text-xs" />
                    </div>
                  )}
                </div>
              )}
            </div>
            <h3 className="iepa-heading-3 mb-2 transition-colors duration-250 ease-out group-hover:text-[var(--iepa-primary-blue)]">
              Attendee
            </h3>
            <p className="iepa-body-small text-gray-600">
              Perfect for professionals attending annual meeting sessions and
              networking events. Also includes spouse and child registration options.
            </p>
            {isAuthenticated && !constraintLoading && !availableTypes.attendee && (
              <Chip color="warning" size="sm" className="mt-2">
                Already Registered
              </Chip>
            )}
          </CardHeader>
          <CardBody className="pt-0 mt-auto">
            <Button
              as={Link}
              href="/register/attendee"
              color="primary"
              className="w-full transition-all duration-250 ease-out group-hover:bg-[var(--iepa-primary-blue-dark)]"
              disabled={(showAuthButtons && !isAuthenticated) || (isAuthenticated && !availableTypes.attendee)}
            >
              {showAuthButtons && !isAuthenticated
                ? 'Login to Register'
                : isAuthenticated && !availableTypes.attendee
                  ? 'Already Registered'
                  : 'Register'}
            </Button>
          </CardBody>
        </Card>

        {/* Speaker Registration */}
        <Card
          className={cn(
            'h-full transition-all duration-250 ease-out cursor-pointer group flex flex-col',
            'hover:-translate-y-1 hover:shadow-[0_8px_25px_rgba(0,0,0,0.15)] hover:bg-[var(--iepa-gray-50)]',
            'hover:border-2 hover:border-[var(--iepa-primary-blue)]',
            'focus-within:ring-2 focus-within:ring-[var(--iepa-primary-blue)]/30 focus-within:ring-offset-2',
            'focus-within:-translate-y-1 focus-within:shadow-[0_8px_25px_rgba(0,0,0,0.15)]',
            'focus-within:border-2 focus-within:border-[var(--iepa-primary-blue)]'
          )}
        >
          <CardHeader className="text-center pb-6 flex-grow">
            <div className="flex justify-center items-center mb-4 relative">
              <FaMicrophone
                className="text-4xl transition-all duration-250 ease-out group-hover:scale-110"
                style={{ color: 'var(--iepa-primary-blue)' }}
              />
              {isAuthenticated && !constraintLoading && (
                <div className="absolute -top-2 -right-2">
                  {availableTypes.speaker ? (
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <FaCheck className="text-white text-xs" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                      <FaExclamationTriangle className="text-white text-xs" />
                    </div>
                  )}
                </div>
              )}
            </div>
            <h3 className="iepa-heading-3 mb-2 transition-colors duration-250 ease-out group-hover:text-[var(--iepa-primary-blue)]">
              Speaker
            </h3>
            <p className="iepa-body-small text-gray-600">
              For presenters and session leaders sharing expertise with the
              community
            </p>
            {isAuthenticated && !constraintLoading && !availableTypes.speaker && (
              <Chip color="warning" size="sm" className="mt-2">
                Already Registered
              </Chip>
            )}
          </CardHeader>
          <CardBody className="pt-0 mt-auto">
            <Button
              as={Link}
              href="/register/speaker"
              color="primary"
              className="w-full transition-all duration-250 ease-out group-hover:bg-[var(--iepa-primary-blue-dark)]"
              disabled={(showAuthButtons && !isAuthenticated) || (isAuthenticated && !availableTypes.speaker)}
            >
              {showAuthButtons && !isAuthenticated
                ? 'Login to Register'
                : isAuthenticated && !availableTypes.speaker
                  ? 'Already Registered'
                  : 'Register'}
            </Button>
          </CardBody>
        </Card>

        {/* Sponsor Registration */}
        <Card className={cn(
          'h-full transition-all duration-250 ease-out cursor-pointer group flex flex-col',
          'hover:-translate-y-1 hover:shadow-[0_8px_25px_rgba(0,0,0,0.15)] hover:bg-[var(--iepa-gray-50)]',
          'hover:border-2 hover:border-[var(--iepa-primary-blue)]',
          'focus-within:ring-2 focus-within:ring-[var(--iepa-primary-blue)]/30 focus-within:ring-offset-2',
          'focus-within:-translate-y-1 focus-within:shadow-[0_8px_25px_rgba(0,0,0,0.15)]',
          'focus-within:border-2 focus-within:border-[var(--iepa-primary-blue)]'
        )}>
          <CardHeader className="text-center pb-6 flex-grow">
            <div className="flex justify-center items-center mb-4 relative">
              <FaBuilding
                className="text-4xl transition-all duration-250 ease-out group-hover:scale-110"
                style={{ color: 'var(--iepa-primary-blue)' }}
              />
              {isAuthenticated && !constraintLoading && (
                <div className="absolute -top-2 -right-2">
                  {availableTypes.sponsor ? (
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <FaCheck className="text-white text-xs" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                      <FaExclamationTriangle className="text-white text-xs" />
                    </div>
                  )}
                </div>
              )}
            </div>
            <h3 className="iepa-heading-3 mb-2 transition-colors duration-250 ease-out group-hover:text-[var(--iepa-primary-blue)]">
              Sponsor
            </h3>
            <p className="iepa-body-small text-gray-600">
              Showcase your organization and connect with industry
              professionals
            </p>
            {isAuthenticated && !constraintLoading && !availableTypes.sponsor && (
              <Chip color="warning" size="sm" className="mt-2">
                Already Registered
              </Chip>
            )}
          </CardHeader>
          <CardBody className="pt-0 mt-auto">
            <Button
              as={Link}
              href="/register/sponsor"
              color="primary"
              className="w-full transition-all duration-250 ease-out group-hover:bg-[var(--iepa-primary-blue-dark)]"
              disabled={(showAuthButtons && !isAuthenticated) || (isAuthenticated && !availableTypes.sponsor)}
            >
              {showAuthButtons && !isAuthenticated
                ? 'Login to Register'
                : isAuthenticated && !availableTypes.sponsor
                  ? 'Already Registered'
                  : 'Register'}
            </Button>
          </CardBody>
        </Card>

        {/* Note: Sponsor Attendee Registration is only accessible via email links */}
      </div>
    </section>
  );
}
