/**
 * Transform database data (snake_case) to PDF format (camelCase)
 */
export function transformDatabaseDataForPDF(
  registrationType: string,
  data: Record<string, unknown>
): Record<string, unknown> {
  const baseTransform = {
    id: data.id,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };

  switch (registrationType) {
    case 'attendee':
      return {
        ...baseTransform,
        registrationType: data.registration_type || 'attendee',
        firstName: data.first_name,
        lastName: data.last_name,
        fullName: `${data.first_name || ''} ${data.last_name || ''}`.trim(),
        nameOnBadge: data.name_on_badge,
        email: data.email,
        phoneNumber: data.phone_number,
        organization: data.organization,
        jobTitle: data.job_title,
        streetAddress: data.street_address,
        city: data.city,
        state: data.state,
        zipCode: data.zip_code,
        country: data.country || 'United States',
        golfTournament: data.attending_golf,
        golfClubRental: data.golf_club_rental,
        golfClubHandedness: data.golf_club_handedness,
        // Night staying options
        nightOne: data.night_one,
        nightTwo: data.night_two,
        meals: data.meals || {},
        dietaryNeeds: data.dietary_needs,
        registrationTotal: data.registration_total,
        golfTotal: data.golf_total,
        golfClubRentalTotal: data.golf_club_rental_total,
        mealTotal: data.meal_total,
        grandTotal: data.grand_total,
        paymentStatus: data.payment_status,
        paymentId: data.payment_id,
      };

    case 'speaker':
      return {
        ...baseTransform,
        fullName:
          data.full_name ||
          `${data.first_name || ''} ${data.last_name || ''}`.trim(),
        firstName: data.first_name,
        lastName: data.last_name,
        email: data.email,
        phoneNumber: data.phone_number,
        organizationName: data.organization_name,
        jobTitle: data.job_title,
        bio: data.bio,
        presentationTitle: data.presentation_title || 'Speaker Presentation',
        presentationDescription: data.presentation_description,
        presentationDuration: data.presentation_duration,
        targetAudience: data.target_audience,
        learningObjectives: data.learning_objectives,
        speakerExperience: data.speaker_experience,
        previousSpeaking: data.previous_speaking,
        equipmentNeeds: data.equipment_needs,
        specialRequests: data.special_requests,
        presentationFileUrl: data.presentation_file_url,
        headshotUrl: data.headshot_url,
        // Speakers are typically comped (free)
        registrationType: 'speaker',
        grandTotal: 0,
        paymentStatus: 'comped',
      };

    case 'sponsor':
      return {
        ...baseTransform,
        sponsorName: data.sponsor_name,
        sponsorUrl: data.sponsor_url,
        sponsorVideo: data.sponsor_video,
        sponsorImageUrl: data.sponsor_image_url,
        sponsorDescription: data.sponsor_description,
        paymentStatus: data.payment_status,
        paymentId: data.payment_id,
        // Add contact fields if they exist in the sponsor table
        contactName: data.contact_name || data.sponsor_name,
        contactEmail: data.contact_email || data.email,
        contactPhone: data.contact_phone,
        contactTitle: data.contact_title,
        sponsorshipLevel: data.sponsorship_level,
      };

    default:
      return data;
  }
}
