'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@/components/ui';
import { stripeUtils } from '@/lib/stripe-client';
import {
  FaBug,
  FaPlay,
  FaCheckCircle,
  FaExclamationTriangle,
} from 'react-icons/fa';

interface DebugStep {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  result?: unknown;
  error?: string;
}

export default function PaymentDebugPage() {
  const [steps, setSteps] = useState<DebugStep[]>([
    { name: 'Validate Stripe Configuration', status: 'pending' },
    { name: 'Test API Connectivity', status: 'pending' },
    { name: 'Create Checkout Session', status: 'pending' },
    { name: 'Load Stripe.js Library', status: 'pending' },
    { name: 'Attempt Redirect', status: 'pending' },
  ]);

  const updateStep = (index: number, updates: Partial<DebugStep>) => {
    setSteps(prev =>
      prev.map((step, i) => (i === index ? { ...step, ...updates } : step))
    );
  };

  const runDebugSequence = async () => {
    // Reset all steps
    setSteps(prev =>
      prev.map(step => ({
        ...step,
        status: 'pending',
        result: undefined,
        error: undefined,
      }))
    );

    // Step 1: Validate Stripe Configuration
    updateStep(0, { status: 'running' });
    try {
      const config = stripeUtils.validateConfig();
      updateStep(0, {
        status: config.isValid ? 'success' : 'error',
        result: config,
        error: config.isValid ? undefined : config.errors.join(', '),
      });
    } catch (error) {
      updateStep(0, { status: 'error', error: String(error) });
      return;
    }

    // Step 2: Test API Connectivity
    updateStep(1, { status: 'running' });
    try {
      const response = await fetch('/api/stripe/test-config');
      const data = await response.json();
      updateStep(1, {
        status: response.ok ? 'success' : 'error',
        result: data,
        error: response.ok ? undefined : `HTTP ${response.status}`,
      });
    } catch (error) {
      updateStep(1, { status: 'error', error: String(error) });
      return;
    }

    // Step 3: Create Checkout Session
    updateStep(2, { status: 'running' });
    try {
      const sessionResult = await stripeUtils.createCheckoutSession({
        registrationId: 'debug-test-' + Date.now(),
        registrationType: 'attendee',
        customerEmail: '<EMAIL>',
        customerName: 'Debug User',
        totalAmount: 1, // $1 test
        lineItems: [
          {
            name: 'Debug Test Registration',
            description: 'Debug test for payment flow',
            price: 1,
            quantity: 1,
          },
        ],
        metadata: {
          debug: 'true',
          source: 'debug-page',
        },
      });

      updateStep(2, {
        status: sessionResult.success ? 'success' : 'error',
        result: sessionResult,
        error: sessionResult.success ? undefined : sessionResult.error,
      });

      if (!sessionResult.success) return;

      // Step 4: Load Stripe.js Library
      updateStep(3, { status: 'running' });
      try {
        const { getStripe } = await import('@/lib/stripe-client');
        const stripe = await getStripe();
        updateStep(3, {
          status: stripe ? 'success' : 'error',
          result: { loaded: !!stripe },
          error: stripe ? undefined : 'Failed to load Stripe.js',
        });

        if (!stripe) return;

        // Step 5: Attempt Redirect (but don't actually redirect in debug mode)
        updateStep(4, { status: 'running' });
        updateStep(4, {
          status: 'success',
          result: {
            sessionId: sessionResult.sessionId,
            url: sessionResult.url,
            note: 'Redirect would occur here in normal flow',
          },
        });
      } catch (error) {
        updateStep(3, { status: 'error', error: String(error) });
      }
    } catch (error) {
      updateStep(2, { status: 'error', error: String(error) });
    }
  };

  const getStatusIcon = (status: DebugStep['status']) => {
    switch (status) {
      case 'success':
        return <FaCheckCircle className="text-green-600" />;
      case 'error':
        return <FaExclamationTriangle className="text-red-600" />;
      case 'running':
        return (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" />
        );
      default:
        return (
          <div className="w-4 h-4 rounded-full border-2 border-gray-300" />
        );
    }
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="iepa-heading-1 mb-4 flex items-center justify-center gap-2">
              <FaBug className="text-blue-600" />
              Payment Flow Debugger
            </h1>
            <p className="iepa-body text-gray-600">
              Debug and diagnose payment processing issues step by step
            </p>
          </div>

          <Card className="mb-6">
            <CardHeader>
              <h2 className="iepa-heading-2">Debug Sequence</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4 mb-6">
                {steps.map((step, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-3 border rounded-lg"
                  >
                    <div className="mt-1">{getStatusIcon(step.status)}</div>
                    <div className="flex-1">
                      <h3 className="font-semibold">{step.name}</h3>
                      {step.error && (
                        <p className="text-red-600 text-sm mt-1">
                          ❌ {step.error}
                        </p>
                      )}
                      {step.result !== undefined && step.result !== null && (
                        <details className="mt-2">
                          <summary className="text-sm text-gray-600 cursor-pointer">
                            View Details
                          </summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                            {JSON.stringify(step.result, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="text-center">
                <Button
                  onClick={runDebugSequence}
                  color="primary"
                  size="lg"
                  className="flex items-center gap-2"
                >
                  <FaPlay />
                  Run Debug Sequence
                </Button>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2">Common Issues & Solutions</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-red-600">
                    403 Forbidden on API Calls
                  </h3>
                  <p className="text-sm text-gray-600">
                    Check browser security settings, CORS configuration, or
                    proxy/firewall blocking requests.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-red-600">
                    Stripe.js Loading Multiple Times
                  </h3>
                  <p className="text-sm text-gray-600">
                    Clear browser cache, check for duplicate script tags, or
                    restart development server.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-red-600">
                    Redirect Not Working
                  </h3>
                  <p className="text-sm text-gray-600">
                    Verify checkout session URL is valid, check browser console
                    for errors, ensure Stripe.js loaded correctly.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-yellow-600">
                    Webhook Secret Missing
                  </h3>
                  <p className="text-sm text-gray-600">
                    Configure webhook endpoint in Stripe dashboard and update
                    STRIPE_WEBHOOK_SECRET in .env.local
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>
    </div>
  );
}
