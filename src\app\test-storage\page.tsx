'use client';

import React, { useState } from 'react';
import { Card, CardBody, CardHeader } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { FileUpload } from '@/components/ui/FileUpload';
import { useAuth } from '@/contexts/AuthContext';
import { convertToSignedUrl } from '@/lib/fileManagement';
import { showSuccess, showError, showInfo } from '@/utils/notifications';

export default function TestStoragePage() {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testStorageAccess = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/fix-storage', {
        method: 'GET',
      });
      const result = await response.json();
      setTestResults(result);
      
      if (result.success) {
        showSuccess('Storage Test', result.message);
      } else {
        showError('Storage Test Failed', result.message);
      }
    } catch (error) {
      console.error('Storage test error:', error);
      showError('Test Error', 'Failed to test storage access');
    } finally {
      setLoading(false);
    }
  };

  const fixStoragePolicies = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/fix-storage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'fix-policies' }),
      });
      const result = await response.json();
      
      if (result.success) {
        showSuccess('Policies Fixed', result.message);
      } else {
        showError('Fix Failed', result.message);
      }
    } catch (error) {
      console.error('Policy fix error:', error);
      showError('Fix Error', 'Failed to fix storage policies');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (url: string | null, file: File | null) => {
    if (url && file) {
      showSuccess('File Uploaded', `Successfully uploaded: ${file.name}`);
      console.log('Uploaded file URL:', url);

      // Test signed URL conversion
      try {
        const signedResult = await convertToSignedUrl(url, 'iepa-presentations');
        if (signedResult.success) {
          showInfo('Signed URL Generated', 'File can be accessed via signed URL');
          console.log('Signed URL:', signedResult.url);
        } else {
          showError('Signed URL Failed', signedResult.error || 'Unknown error');
        }
      } catch (error) {
        console.error('Error testing signed URL:', error);
      }
    }
  };

  if (!user) {
    return (
      <div className="iepa-container">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <h1 className="iepa-heading-1">Storage Test</h1>
            </CardHeader>
            <CardBody>
              <p className="iepa-body">Please log in to test storage functionality.</p>
            </CardBody>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <h1 className="iepa-heading-1">Storage Functionality Test</h1>
            <p className="iepa-body">Test file upload and storage policies</p>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex gap-4">
                <Button
                  onClick={testStorageAccess}
                  disabled={loading}
                  variant="outline"
                >
                  {loading ? 'Testing...' : 'Test Storage Access'}
                </Button>
                <Button
                  onClick={fixStoragePolicies}
                  disabled={loading}
                  variant="outline"
                >
                  {loading ? 'Fixing...' : 'Fix Storage Policies'}
                </Button>
              </div>

              {testResults && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-semibold mb-2">Test Results:</h3>
                  <pre className="text-sm overflow-auto">
                    {JSON.stringify(testResults, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="iepa-heading-2">Test File Upload</h2>
            <p className="iepa-body">User ID: {user.id}</p>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              <div>
                <h3 className="iepa-heading-3 mb-4">Presentation File Upload</h3>
                <FileUpload
                  label="Test Presentation File"
                  description="Upload a test presentation file (PDF, PPT, PPTX, DOC, DOCX - max 50MB)"
                  bucket="iepa-presentations"
                  folder="speaker-presentations"
                  maxSize={52428800} // 50MB
                  allowedTypes={[
                    'application/pdf',
                    'application/vnd.ms-powerpoint',
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                  ]}
                  accept=".pdf,.ppt,.pptx,.doc,.docx"
                  onFileUpload={handleFileUpload}
                  placeholder="Upload test presentation file"
                />
              </div>

              <div>
                <h3 className="iepa-heading-3 mb-4">Headshot Upload</h3>
                <FileUpload
                  label="Test Headshot"
                  description="Upload a test headshot (JPG, PNG, WebP - max 5MB)"
                  bucket="iepa-presentations"
                  folder="speaker-headshots"
                  maxSize={5242880} // 5MB
                  allowedTypes={['image/jpeg', 'image/png', 'image/webp']}
                  accept=".jpg,.jpeg,.png,.webp"
                  onFileUpload={handleFileUpload}
                  placeholder="Upload test headshot"
                />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="iepa-heading-2">Debug Information</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-2 text-sm">
              <p><strong>User ID:</strong> {user.id}</p>
              <p><strong>User Email:</strong> {user.email}</p>
              <p><strong>Expected folder structure:</strong></p>
              <ul className="list-disc list-inside ml-4">
                <li>Presentations: <code>{user.id}/speaker-presentations/filename</code></li>
                <li>Headshots: <code>{user.id}/speaker-headshots/filename</code></li>
              </ul>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
