// Dashboard data hook for IEPA 2025 Conference Registration

import { useState, useEffect, useCallback } from 'react';
import {
  fetchDashboardData,
  fetchAttendeeRegistrations,
  fetchSpeakerRegistrations,
  fetchSponsorRegistrations,
} from '@/services/dashboard';
import type {
  DashboardDataResponse,
  DashboardFilters,
  LoadingState,
  DashboardError,
  EnhancedAttendeeRegistration,
  EnhancedSpeakerRegistration,
  EnhancedSponsorRegistration,
} from '@/types/dashboard';

interface UseDashboardDataOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
  initialFilters?: DashboardFilters;
}

interface UseDashboardDataReturn {
  data: DashboardDataResponse | null;
  loading: LoadingState;
  filters: DashboardFilters;
  setFilters: (filters: DashboardFilters) => void;
  refreshData: () => Promise<void>;
  refreshAttendees: () => Promise<void>;
  refreshSpeakers: () => Promise<void>;
  refreshSponsors: () => Promise<void>;
}

const defaultFilters: DashboardFilters = {
  dateRange: {
    start: null,
    end: null,
  },
  registrationType: [],
  paymentStatus: [],
  searchQuery: '',
};

export function useDashboardData(
  options: UseDashboardDataOptions = {}
): UseDashboardDataReturn {
  const {
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    initialFilters = defaultFilters,
  } = options;

  const [data, setData] = useState<DashboardDataResponse | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: null,
    lastUpdated: null,
  });
  const [filters, setFilters] = useState<DashboardFilters>(initialFilters);

  /**
   * Fetch complete dashboard data
   */
  const fetchData = useCallback(
    async (currentFilters?: DashboardFilters) => {
      try {
        setLoading(prev => ({ ...prev, isLoading: true, error: null }));

        const filtersToUse = currentFilters || filters;
        const dashboardData = await fetchDashboardData(filtersToUse);

        setData(dashboardData);
        setLoading({
          isLoading: false,
          error: null,
          lastUpdated: new Date(),
        });
      } catch (error) {
        const dashboardError: DashboardError = {
          code: 'FETCH_ERROR',
          message:
            error instanceof Error
              ? error.message
              : 'Failed to fetch dashboard data',
          details: error,
        };

        setLoading({
          isLoading: false,
          error: dashboardError,
          lastUpdated: null,
        });
      }
    },
    [] // Remove filters dependency to prevent unnecessary re-renders
  );

  /**
   * Refresh complete dashboard data
   */
  const refreshData = useCallback(async () => {
    try {
      setLoading(prev => ({ ...prev, isLoading: true, error: null }));
      const dashboardData = await fetchDashboardData(filters);
      setData(dashboardData);
      setLoading({
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      const dashboardError: DashboardError = {
        code: 'FETCH_ERROR',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to fetch dashboard data',
        details: error,
      };
      setLoading({
        isLoading: false,
        error: dashboardError,
        lastUpdated: null,
      });
    }
  }, [filters]);

  /**
   * Refresh only attendee data
   */
  const refreshAttendees = useCallback(async () => {
    if (!data) return;

    try {
      const attendees = await fetchAttendeeRegistrations(filters);
      setData(prev => (prev ? { ...prev, attendees } : null));
    } catch (error) {
      console.error('Error refreshing attendees:', error);
    }
  }, [data, filters]);

  /**
   * Refresh only speaker data
   */
  const refreshSpeakers = useCallback(async () => {
    if (!data) return;

    try {
      const speakers = await fetchSpeakerRegistrations(filters);
      setData(prev => (prev ? { ...prev, speakers } : null));
    } catch (error) {
      console.error('Error refreshing speakers:', error);
    }
  }, [data, filters]);

  /**
   * Refresh only sponsor data
   */
  const refreshSponsors = useCallback(async () => {
    if (!data) return;

    try {
      const sponsors = await fetchSponsorRegistrations(filters);
      setData(prev => (prev ? { ...prev, sponsors } : null));
    } catch (error) {
      console.error('Error refreshing sponsors:', error);
    }
  }, [data, filters]);

  /**
   * Update filters and refetch data
   */
  const updateFilters = useCallback((newFilters: DashboardFilters) => {
    setFilters(newFilters);
    // Directly call fetchData with new filters to avoid dependency issues
    (async () => {
      try {
        setLoading(prev => ({ ...prev, isLoading: true, error: null }));
        const dashboardData = await fetchDashboardData(newFilters);
        setData(dashboardData);
        setLoading({
          isLoading: false,
          error: null,
          lastUpdated: new Date(),
        });
      } catch (error) {
        const dashboardError: DashboardError = {
          code: 'FETCH_ERROR',
          message:
            error instanceof Error
              ? error.message
              : 'Failed to fetch dashboard data',
          details: error,
        };
        setLoading({
          isLoading: false,
          error: dashboardError,
          lastUpdated: null,
        });
      }
    })();
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, []); // Remove fetchData dependency to prevent infinite loops

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (!loading.isLoading) {
        refreshData();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, loading.isLoading, refreshData]);

  // Remove the problematic refetch effect that was causing flicker

  return {
    data,
    loading,
    filters,
    setFilters: updateFilters,
    refreshData,
    refreshAttendees,
    refreshSpeakers,
    refreshSponsors,
  };
}

/**
 * Hook for attendee-specific data
 */
export function useAttendeeData(filters?: DashboardFilters) {
  const [attendees, setAttendees] = useState<EnhancedAttendeeRegistration[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<DashboardError | null>(null);

  const fetchAttendees = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchAttendeeRegistrations(filters);
      setAttendees(data);
    } catch (err) {
      setError({
        code: 'ATTENDEE_FETCH_ERROR',
        message:
          err instanceof Error ? err.message : 'Failed to fetch attendees',
        details: err,
      });
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchAttendees();
  }, [fetchAttendees]);

  return {
    attendees,
    loading,
    error,
    refresh: fetchAttendees,
  };
}

/**
 * Hook for speaker-specific data
 */
export function useSpeakerData(filters?: DashboardFilters) {
  const [speakers, setSpeakers] = useState<EnhancedSpeakerRegistration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<DashboardError | null>(null);

  const fetchSpeakers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchSpeakerRegistrations(filters);
      setSpeakers(data);
    } catch (err) {
      setError({
        code: 'SPEAKER_FETCH_ERROR',
        message:
          err instanceof Error ? err.message : 'Failed to fetch speakers',
        details: err,
      });
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchSpeakers();
  }, [fetchSpeakers]);

  return {
    speakers,
    loading,
    error,
    refresh: fetchSpeakers,
  };
}

/**
 * Hook for sponsor-specific data
 */
export function useSponsorData(filters?: DashboardFilters) {
  const [sponsors, setSponsors] = useState<EnhancedSponsorRegistration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<DashboardError | null>(null);

  const fetchSponsors = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchSponsorRegistrations(filters);
      setSponsors(data);
    } catch (err) {
      setError({
        code: 'SPONSOR_FETCH_ERROR',
        message:
          err instanceof Error ? err.message : 'Failed to fetch sponsors',
        details: err,
      });
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchSponsors();
  }, [fetchSponsors]);

  return {
    sponsors,
    loading,
    error,
    refresh: fetchSponsors,
  };
}
