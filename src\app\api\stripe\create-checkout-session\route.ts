// Stripe Checkout Session Creation API
// POST /api/stripe/create-checkout-session

import { NextRequest, NextResponse } from 'next/server';
import {
  stripe,
  STRIPE_CONFIG,
  formatAmountForStripe,
  getStripeUrls,
} from '@/lib/stripe';
import { headers } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      registrationId,
      registrationType,
      customerEmail,
      customerName,
      lineItems,
      totalAmount,
      metadata = {},
    } = body;

    // Validate required fields
    if (
      !registrationId ||
      !registrationType ||
      !customerEmail ||
      !totalAmount
    ) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Missing required fields: registrationId, registrationType, customerEmail, totalAmount',
        },
        { status: 400 }
      );
    }

    // Validate registration type
    if (!['attendee', 'sponsor', 'speaker', 'sponsor-attendee'].includes(registrationType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid registration type. Must be "attendee", "sponsor", "speaker", or "sponsor-attendee"',
        },
        { status: 400 }
      );
    }

    // Validate amount
    if (typeof totalAmount !== 'number' || totalAmount <= 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid total amount. Must be a positive number',
        },
        { status: 400 }
      );
    }

    // Get the base URL for redirect URLs
    const headersList = await headers();
    const host = headersList.get('host');
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;

    // Get dynamic URLs
    const urls = getStripeUrls(baseUrl);

    // Prepare line items for Stripe
    const stripeLineItems =
      lineItems?.length > 0
        ? lineItems.map(
            (item: {
              name: string;
              description?: string;
              price: number;
              quantity?: number;
            }) => ({
              price_data: {
                currency: STRIPE_CONFIG.currency,
                product_data: {
                  name: item.name,
                  description: item.description || undefined,
                },
                unit_amount: formatAmountForStripe(item.price),
              },
              quantity: item.quantity || 1,
            })
          )
        : [
            {
              price_data: {
                currency: STRIPE_CONFIG.currency,
                product_data: {
                  name: `IEPA 2025 Conference - ${registrationType.charAt(0).toUpperCase() + registrationType.slice(1)} Registration`,
                  description: 'IEPA 2025 Annual Conference Registration',
                },
                unit_amount: formatAmountForStripe(totalAmount),
              },
              quantity: 1,
            },
          ];

    // Prepare checkout session configuration
    const sessionConfig: Stripe.Checkout.SessionCreateParams = {
      ...STRIPE_CONFIG.checkoutDefaults,
      payment_method_types: STRIPE_CONFIG.paymentMethodTypes,
      line_items: stripeLineItems,
      customer_email: customerEmail,
      success_url: `${urls.success}?session_id={CHECKOUT_SESSION_ID}&registration_id=${registrationId}`,
      cancel_url: `${urls.cancel}?registration_id=${registrationId}`,
      metadata: {
        registrationId,
        registrationType,
        customerName: customerName || '',
        ...metadata,
      },
      // Add customer creation and billing details
      billing_address_collection: 'required',
      // Completely remove phone number collection to avoid validation issues
      // Note: Setting to null should remove the field entirely
      // Set automatic tax calculation if needed
      automatic_tax: {
        enabled: false, // Set to true if you want Stripe to calculate taxes
      },
      // Add invoice creation for record keeping
      invoice_creation: {
        enabled: true,
        invoice_data: {
          description: `IEPA 2025 Conference - ${registrationType.charAt(0).toUpperCase() + registrationType.slice(1)} Registration`,
          metadata: {
            registrationId,
            registrationType,
          },
          footer:
            'Thank you for registering for the IEPA 2025 Annual Conference!',
        },
      },
    };

    // Discount codes are now handled by Stripe's promotion code field in checkout
    // Users can enter promotion codes directly in the Stripe checkout interface

    // Create checkout session
    const session = await stripe.checkout.sessions.create(sessionConfig);

    return NextResponse.json({
      success: true,
      sessionId: session.id,
      url: session.url,
      message: 'Checkout session created successfully',
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);

    // Handle specific Stripe errors
    if (error instanceof Error) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error while creating checkout session',
      },
      { status: 500 }
    );
  }
}

// GET method to retrieve session details
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');

    if (!sessionId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Session ID is required',
        },
        { status: 400 }
      );
    }

    // Retrieve session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['line_items', 'customer', 'payment_intent'],
    });

    return NextResponse.json({
      success: true,
      session: {
        id: session.id,
        payment_status: session.payment_status,
        customer_email: session.customer_email,
        amount_total: session.amount_total,
        currency: session.currency,
        metadata: session.metadata,
        created: session.created,
      },
    });
  } catch (error) {
    console.error('Error retrieving checkout session:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve checkout session',
      },
      { status: 500 }
    );
  }
}
