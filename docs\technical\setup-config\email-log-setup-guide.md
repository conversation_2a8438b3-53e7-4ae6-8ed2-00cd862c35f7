# Email Log Table Setup Guide - IEPA Conference Registration

## Current Status

**Email System Status**: ✅ **OPERATIONAL** - Emails are sending successfully through SendGrid  
**Database Logging Status**: ❌ **NEEDS SETUP** - Email log table does not exist  
**Impact**: Email delivery works perfectly, but email logs are not being stored for audit purposes

## Quick Setup Instructions

### Step 1: Access Supabase Dashboard
1. Go to your Supabase project dashboard: https://supabase.com/dashboard
2. Navigate to your NDS project (uffhyhpcuedjsisczocy)
3. Click on "SQL Editor" in the left sidebar

### Step 2: Create Email Log Table
1. Copy the entire SQL content from: `.docs/01-setup-config/email-log-table-setup.sql`
2. Paste it into the SQL Editor
3. Click "Run" to execute the SQL
4. You should see "Email log table created successfully!" message

### Step 3: Verify Setup
Run this API endpoint to verify the table was created correctly:
```bash
curl -X POST http://localhost:6969/api/admin/setup-email-log
```

Expected response:
```json
{
  "success": true,
  "message": "Email log table is fully operational",
  "tableExists": true,
  "canInsert": true,
  "canDelete": true
}
```

## What the Email Log Table Does

### Purpose
- **Audit Trail**: Complete record of all emails sent by the system
- **Debugging**: Track email delivery failures and troubleshoot issues
- **Analytics**: Monitor email engagement and delivery rates
- **Compliance**: Maintain records for business and regulatory purposes

### Email Types Logged
- `registration_confirmation` - Welcome emails after registration
- `payment_confirmation` - Payment receipt emails
- `golf_addon` - Golf tournament registration emails
- `welcome_email` - Comprehensive conference information emails
- `password_reset` - Password reset emails
- `custom` - Admin-generated emails

### Data Stored
- **Email Details**: Recipient, sender, subject, type
- **Delivery Status**: pending, sent, failed
- **SendGrid Integration**: Message IDs for tracking
- **Relationships**: Links to users, registrations, payments
- **Content Preview**: First 500 characters (not full HTML)
- **Timestamps**: Created, sent, updated times

## Current Email System Status

### ✅ Working Features
- **SendGrid Integration**: Fully operational with API key configured
- **Email Templates**: Professional IEPA-branded templates
- **Registration Emails**: Speaker, sponsor, and attendee confirmations
- **Payment Emails**: Enhanced payment and golf tournament confirmations
- **Error Handling**: Email failures don't block registration process

### ❌ Missing Feature
- **Database Logging**: Email logs not being stored (table doesn't exist)

## Manual Table Creation SQL

If you prefer to create the table manually, here's the essential SQL:

```sql
-- Create email log table
CREATE TABLE IF NOT EXISTS public.iepa_email_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  recipient_email VARCHAR(255) NOT NULL,
  recipient_name VARCHAR(255),
  sender_email VARCHAR(255) NOT NULL,
  sender_name VARCHAR(255),
  subject VARCHAR(500) NOT NULL,
  email_type VARCHAR(50) NOT NULL,
  template_used VARCHAR(100),
  content_preview TEXT,
  has_attachments BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  sendgrid_message_id VARCHAR(255),
  error_message TEXT,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  registration_id UUID,
  registration_type VARCHAR(20),
  payment_id UUID,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.iepa_email_log ENABLE ROW LEVEL SECURITY;

-- Service role full access (CRITICAL for email logging)
CREATE POLICY "Service role full access" 
  ON public.iepa_email_log FOR ALL 
  TO service_role 
  USING (true) 
  WITH CHECK (true);
```

## Troubleshooting

### Issue: "relation does not exist"
**Solution**: The table hasn't been created yet. Follow the setup instructions above.

### Issue: "permission denied"
**Solution**: Check that Row Level Security policies are set up correctly, especially the service_role policy.

### Issue: Email logging still fails after table creation
**Solution**: 
1. Verify the service role key is correctly set in environment variables
2. Check that the service_role policy exists and allows INSERT operations
3. Test the setup endpoint: `curl -X POST http://localhost:6969/api/admin/setup-email-log`

## Testing Email Logging

After setup, test the email system:

```bash
# Test email configuration
curl http://localhost:6969/api/test-email

# Send test registration email
curl -X POST http://localhost:6969/api/test-email \
  -H "Content-Type: application/json" \
  -d '{"testEmail": "<EMAIL>", "testType": "registration"}'
```

You should see successful email sending AND successful database logging in the server logs.

## Production Considerations

### Environment Variables Required
```bash
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME="IEPA Conference 2025"
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Monitoring
- Check email logs regularly: `SELECT * FROM iepa_email_log ORDER BY created_at DESC LIMIT 10;`
- Monitor delivery rates: `SELECT status, COUNT(*) FROM iepa_email_log GROUP BY status;`
- Track email types: `SELECT email_type, COUNT(*) FROM iepa_email_log GROUP BY email_type;`

---

**Next Steps**: Once the table is created, the email system will be 100% operational with full logging capabilities.
