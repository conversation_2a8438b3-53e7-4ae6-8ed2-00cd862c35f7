# Stripe Webhook Configuration

**Generated**: 2025-06-05T05:10:00.315Z
**Updated**: 2025-01-27T21:50:00.000Z
**Script**: configure-stripe-webhooks.ts

## Configured Webhooks

### PRODUCTION Environment

- **Webhook ID**: `we_1RWW3wIOwAzWO5brq631Ndi3`
- **URL**: `https://iepa.com/api/stripe/webhook`
- **Secret**: `whsec_bshk8molmEQqdUi3as7zjhUFY4C9YkYb`
- **Status**: ✅ **CONFIGURED** (endpoint returns 404 until production deployment)
- **Events**:
  - `checkout.session.completed`
  - `payment_intent.succeeded`
  - `payment_intent.payment_failed`

### DEVELOPMENT Environment

- **Method**: Stripe CLI forwarding (recommended for development)
- **Local URL**: `http://localhost:3000/api/stripe/webhook`
- **Status**: ✅ **READY** (endpoint accessible and validating signatures)
- **Setup Command**: `./scripts/setup-development-webhooks.sh`
- **Events**: Same as production (forwarded via Stripe CLI)

## Environment Variables

### Development (.env.local)

```env
STRIPE_WEBHOOK_SECRET=NOT_CONFIGURED
```

### Production

```env
STRIPE_WEBHOOK_SECRET=whsec_bshk8molmEQqdUi3as7zjhUFY4C9YkYb
```

## Development Setup

### Quick Start for Development

```bash
# 1. Start your development server
npm run dev

# 2. In a new terminal, start webhook forwarding
./scripts/setup-development-webhooks.sh

# 3. Test webhook events
stripe trigger checkout.session.completed
```

### Manual Development Setup

```bash
# Install Stripe CLI (if not already installed)
brew install stripe/stripe-cli/stripe  # macOS
# or follow: https://github.com/stripe/stripe-cli#installation

# Login to Stripe CLI
stripe login

# Forward webhooks to local development
stripe listen --forward-to localhost:3000/api/stripe/webhook \
  --events checkout.session.completed,payment_intent.succeeded,payment_intent.payment_failed
```

## Testing Webhooks

### Automated Testing

```bash
# Run comprehensive webhook tests
npx tsx scripts/test-webhooks.ts
```

### Manual Testing

#### Development Testing

```bash
# Test webhook endpoint accessibility
curl -X POST http://localhost:3000/api/stripe/webhook \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: test" \
  -d '{"type": "checkout.session.completed"}'

# Expected response: 400 Bad Request (signature validation working)
```

#### Using Stripe CLI Events

```bash
# Test specific webhook events
stripe trigger checkout.session.completed
stripe trigger payment_intent.succeeded
stripe trigger payment_intent.payment_failed

# Test with custom data
stripe trigger checkout.session.completed \
  --add checkout_session:metadata[registrationId]=test-123 \
  --add checkout_session:metadata[registrationType]=attendee
```

## Webhook Handler

The webhook handler is implemented at:

- **File**: `src/app/api/stripe/webhook/route.ts`
- **Endpoint**: `/api/stripe/webhook`

## Security Notes

1. **Webhook Secrets**: Keep webhook secrets secure and never commit to version control
2. **Signature Verification**: All webhooks are verified using Stripe's signature verification
3. **Environment Separation**: Development and production use separate webhook endpoints
4. **HTTPS Required**: Production webhooks must use HTTPS endpoints

## Troubleshooting

1. **Webhook Not Receiving Events**: Check Stripe dashboard webhook logs
2. **Signature Verification Failed**: Ensure webhook secret matches environment variable
3. **404 Errors**: Verify webhook URL is accessible and endpoint exists
4. **Database Updates Not Working**: Check webhook handler logs and database permissions
