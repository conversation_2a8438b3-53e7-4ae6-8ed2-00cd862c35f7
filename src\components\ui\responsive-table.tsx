'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
// import { Badge } from '@/components/ui/badge'; // Removed - not used in this component
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { FiMoreVertical } from 'react-icons/fi';
// import { FiEye, FiEdit, FiTrash2 } from 'react-icons/fi'; // Removed - passed as props

interface ResponsiveTableProps {
  children: React.ReactNode;
  className?: string;
}

interface ResponsiveTableRowProps {
  children: React.ReactNode;
  className?: string;
}

interface ResponsiveTableCellProps {
  children: React.ReactNode;
  className?: string;
  priority?: 'high' | 'medium' | 'low'; // Controls visibility on different screen sizes
  label?: string; // For mobile stacked layout
  colSpan?: number; // For spanning multiple columns
}

interface ResponsiveTableHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface ResponsiveTableBodyProps {
  children: React.ReactNode;
  className?: string;
}

interface ActionButtonsProps {
  actions: Array<{
    label: string;
    icon: React.ComponentType<{ className?: string }>;
    onClick: () => void;
    variant?: 'default' | 'destructive';
    href?: string;
  }>;
  compact?: boolean;
}

// Main responsive table wrapper
const ResponsiveTable = React.forwardRef<HTMLDivElement, ResponsiveTableProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('w-full overflow-x-auto', className)}
      {...props}
    >
      <table className="w-full caption-bottom text-sm">{children}</table>
    </div>
  )
);
ResponsiveTable.displayName = 'ResponsiveTable';

// Table header - only shows on desktop
const ResponsiveTableHeader = React.forwardRef<
  HTMLTableSectionElement,
  ResponsiveTableHeaderProps
>(({ className, children, ...props }, ref) => (
  <thead ref={ref} className={cn('[&_tr]:border-b', className)} {...props}>
    {children}
  </thead>
));
ResponsiveTableHeader.displayName = 'ResponsiveTableHeader';

// Table body
const ResponsiveTableBody = React.forwardRef<
  HTMLTableSectionElement,
  ResponsiveTableBodyProps
>(({ className, children, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn('[&_tr:last-child]:border-0', className)}
    {...props}
  >
    {children}
  </tbody>
));
ResponsiveTableBody.displayName = 'ResponsiveTableBody';

// Table row - renders as card on mobile
const ResponsiveTableRow = React.forwardRef<
  HTMLTableRowElement,
  ResponsiveTableRowProps
>(({ className, children, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted',
      className
    )}
    {...props}
  >
    {children}
  </tr>
));
ResponsiveTableRow.displayName = 'ResponsiveTableRow';

// Table cell - adapts based on screen size
const ResponsiveTableCell = React.forwardRef<
  HTMLTableCellElement,
  ResponsiveTableCellProps
>(({ className, children, priority = 'medium', colSpan, ...props }, ref) => {
  const priorityClasses = {
    high: 'table-cell', // Always visible on desktop
    medium: 'hidden xl:table-cell', // Hidden on smaller desktop screens
    low: 'hidden 2xl:table-cell', // Only visible on very large screens
  };

  return (
    <td
      ref={ref}
      className={cn(
        'py-2 px-2 align-middle [&:has([role=checkbox])]:pr-0 text-sm',
        priorityClasses[priority],
        className
      )}
      colSpan={colSpan}
      {...props}
    >
      {children}
    </td>
  );
});
ResponsiveTableCell.displayName = 'ResponsiveTableCell';

// Table head - only shows on desktop
const ResponsiveTableHead = React.forwardRef<
  HTMLTableCellElement,
  ResponsiveTableCellProps
>(({ className, children, priority = 'medium', ...props }, ref) => {
  const priorityClasses = {
    high: 'table-cell', // Always visible on desktop
    medium: 'hidden xl:table-cell', // Hidden on smaller desktop screens
    low: 'hidden 2xl:table-cell', // Only visible on very large screens
  };

  return (
    <th
      ref={ref}
      className={cn(
        'h-8 px-2 text-left align-middle font-medium text-muted-foreground text-xs [&:has([role=checkbox])]:pr-0',
        priorityClasses[priority],
        className
      )}
      {...props}
    >
      {children}
    </th>
  );
});
ResponsiveTableHead.displayName = 'ResponsiveTableHead';

// Action buttons component for table actions
const ActionButtons: React.FC<ActionButtonsProps> = ({
  actions,
  compact = false,
}) => {
  if (compact || actions.length > 3) {
    // Use dropdown for compact view or many actions
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <FiMoreVertical className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {actions.map((action, index) => (
            <DropdownMenuItem
              key={index}
              onClick={action.onClick}
              className={action.variant === 'destructive' ? 'text-red-600' : ''}
            >
              <action.icon className="w-4 h-4 mr-2" />
              {action.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Show individual buttons for desktop
  return (
    <div className="flex items-center space-x-2">
      {actions.map((action, index) => (
        <Button
          key={index}
          size="sm"
          variant="outline"
          onClick={action.onClick}
          className={
            action.variant === 'destructive'
              ? 'text-red-600 hover:text-red-700'
              : ''
          }
          title={action.label}
        >
          <action.icon className="w-4 h-4" />
        </Button>
      ))}
    </div>
  );
};

export {
  ResponsiveTable,
  ResponsiveTableHeader,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell,
  ResponsiveTableHead,
  ActionButtons,
};
