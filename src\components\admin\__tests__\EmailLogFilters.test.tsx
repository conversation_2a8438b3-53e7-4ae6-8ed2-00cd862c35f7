import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmailLogFilters from '../EmailLogFilters';

// Mock the UI components
jest.mock('@/components/ui', () => ({
  Input: ({ value, onChange, placeholder, className, ...props }: any) => (
    <input 
      value={value} 
      onChange={onChange} 
      placeholder={placeholder} 
      className={className}
      {...props}
    />
  ),
  Button: ({ children, onClick, disabled, className, title }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      className={className} 
      title={title}
    >
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      <button onClick={() => onValueChange('test-value')}>
        {children}
      </button>
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => (
    <div data-value={value}>{children}</div>
  ),
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}));

// Mock React Icons
jest.mock('react-icons/fi', () => ({
  FiSearch: () => <span data-testid="search-icon" />,
  FiRefreshCw: () => <span data-testid="refresh-icon" />,
  FiFilter: () => <span data-testid="filter-icon" />,
  FiX: () => <span data-testid="x-icon" />,
  FiDownload: () => <span data-testid="download-icon" />,
}));

const defaultProps = {
  status: 'all',
  emailType: 'all',
  search: '',
  onStatusChange: jest.fn(),
  onEmailTypeChange: jest.fn(),
  onSearchChange: jest.fn(),
  onRefresh: jest.fn(),
  onReset: jest.fn(),
};

describe('EmailLogFilters', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all filter controls', () => {
    render(<EmailLogFilters {...defaultProps} />);

    expect(screen.getByPlaceholderText('Search by subject, recipient, or sender...')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Email Type')).toBeInTheDocument();
    expect(screen.getByText('Refresh')).toBeInTheDocument();
  });

  it('displays search input with correct value', () => {
    render(<EmailLogFilters {...defaultProps} search="test search" />);

    const searchInput = screen.getByPlaceholderText('Search by subject, recipient, or sender...');
    expect(searchInput).toHaveValue('test search');
  });

  it('calls onSearchChange when search input changes', () => {
    const mockOnSearchChange = jest.fn();
    render(<EmailLogFilters {...defaultProps} onSearchChange={mockOnSearchChange} />);

    const searchInput = screen.getByPlaceholderText('Search by subject, recipient, or sender...');
    fireEvent.change(searchInput, { target: { value: 'new search' } });

    expect(mockOnSearchChange).toHaveBeenCalledWith('new search');
  });

  it('shows clear search button when search has value', () => {
    render(<EmailLogFilters {...defaultProps} search="test" />);

    const clearButton = screen.getByTestId('x-icon');
    expect(clearButton).toBeInTheDocument();
  });

  it('calls onSearchChange with empty string when clear button is clicked', () => {
    const mockOnSearchChange = jest.fn();
    render(
      <EmailLogFilters 
        {...defaultProps} 
        search="test" 
        onSearchChange={mockOnSearchChange} 
      />
    );

    const clearButton = screen.getByTestId('x-icon').closest('button');
    fireEvent.click(clearButton!);

    expect(mockOnSearchChange).toHaveBeenCalledWith('');
  });

  it('calls onRefresh when refresh button is clicked', () => {
    const mockOnRefresh = jest.fn();
    render(<EmailLogFilters {...defaultProps} onRefresh={mockOnRefresh} />);

    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);

    expect(mockOnRefresh).toHaveBeenCalledTimes(1);
  });

  it('disables refresh button when loading', () => {
    render(<EmailLogFilters {...defaultProps} loading={true} />);

    const refreshButton = screen.getByText('Refresh');
    expect(refreshButton).toBeDisabled();
  });

  it('shows loading state on refresh icon when loading', () => {
    render(<EmailLogFilters {...defaultProps} loading={true} />);

    const refreshIcon = screen.getByTestId('refresh-icon');
    expect(refreshIcon).toHaveClass('animate-spin');
  });

  it('displays total results when provided', () => {
    render(<EmailLogFilters {...defaultProps} totalResults={150} />);

    expect(screen.getByText('150 results')).toBeInTheDocument();
  });

  it('displays singular result when total is 1', () => {
    render(<EmailLogFilters {...defaultProps} totalResults={1} />);

    expect(screen.getByText('1 result')).toBeInTheDocument();
  });

  it('shows active filters indicator when filters are applied', () => {
    render(<EmailLogFilters {...defaultProps} status="sent" />);

    expect(screen.getByText('Filters active')).toBeInTheDocument();
    expect(screen.getByTestId('filter-icon')).toBeInTheDocument();
  });

  it('shows clear filters button when filters are active', () => {
    render(<EmailLogFilters {...defaultProps} status="sent" />);

    expect(screen.getByText('Clear Filters')).toBeInTheDocument();
  });

  it('calls onReset when clear filters button is clicked', () => {
    const mockOnReset = jest.fn();
    render(
      <EmailLogFilters 
        {...defaultProps} 
        status="sent" 
        onReset={mockOnReset} 
      />
    );

    const clearButton = screen.getByText('Clear Filters');
    fireEvent.click(clearButton);

    expect(mockOnReset).toHaveBeenCalledTimes(1);
  });

  it('shows export button when onExport is provided', () => {
    const mockOnExport = jest.fn();
    render(<EmailLogFilters {...defaultProps} onExport={mockOnExport} />);

    expect(screen.getByText('Export CSV')).toBeInTheDocument();
    expect(screen.getByTestId('download-icon')).toBeInTheDocument();
  });

  it('calls onExport when export button is clicked', () => {
    const mockOnExport = jest.fn();
    render(<EmailLogFilters {...defaultProps} onExport={mockOnExport} />);

    const exportButton = screen.getByText('Export CSV');
    fireEvent.click(exportButton);

    expect(mockOnExport).toHaveBeenCalledTimes(1);
  });

  it('disables export button when loading', () => {
    const mockOnExport = jest.fn();
    render(
      <EmailLogFilters 
        {...defaultProps} 
        onExport={mockOnExport} 
        loading={true} 
      />
    );

    const exportButton = screen.getByText('Export CSV');
    expect(exportButton).toBeDisabled();
  });

  it('displays active filters with remove buttons', () => {
    render(
      <EmailLogFilters 
        {...defaultProps} 
        status="sent" 
        emailType="welcome" 
        search="test" 
      />
    );

    expect(screen.getByText('Active filters:')).toBeInTheDocument();
    expect(screen.getByText(/Status: Sent/)).toBeInTheDocument();
    expect(screen.getByText(/Type: Welcome/)).toBeInTheDocument();
    expect(screen.getByText(/Search: "test"/)).toBeInTheDocument();
  });

  it('calls appropriate handlers when active filter remove buttons are clicked', () => {
    const mockOnStatusChange = jest.fn();
    const mockOnEmailTypeChange = jest.fn();
    const mockOnSearchChange = jest.fn();

    render(
      <EmailLogFilters 
        {...defaultProps} 
        status="sent" 
        emailType="welcome" 
        search="test"
        onStatusChange={mockOnStatusChange}
        onEmailTypeChange={mockOnEmailTypeChange}
        onSearchChange={mockOnSearchChange}
      />
    );

    // Find and click the remove buttons for each active filter
    const removeButtons = screen.getAllByTestId('x-icon');
    
    // Click status filter remove button
    fireEvent.click(removeButtons[1]); // First x-icon is for search clear
    expect(mockOnStatusChange).toHaveBeenCalledWith('all');

    // Click email type filter remove button
    fireEvent.click(removeButtons[2]);
    expect(mockOnEmailTypeChange).toHaveBeenCalledWith('all');

    // Click search filter remove button
    fireEvent.click(removeButtons[3]);
    expect(mockOnSearchChange).toHaveBeenCalledWith('');
  });

  it('renders date range filters when handlers are provided', () => {
    const mockOnDateFromChange = jest.fn();
    const mockOnDateToChange = jest.fn();

    render(
      <EmailLogFilters 
        {...defaultProps} 
        onDateFromChange={mockOnDateFromChange}
        onDateToChange={mockOnDateToChange}
      />
    );

    expect(screen.getByText('From Date')).toBeInTheDocument();
    expect(screen.getByText('To Date')).toBeInTheDocument();
  });

  it('calls date change handlers when date inputs change', () => {
    const mockOnDateFromChange = jest.fn();
    const mockOnDateToChange = jest.fn();

    render(
      <EmailLogFilters 
        {...defaultProps} 
        onDateFromChange={mockOnDateFromChange}
        onDateToChange={mockOnDateToChange}
      />
    );

    const fromDateInput = screen.getByLabelText('From Date');
    const toDateInput = screen.getByLabelText('To Date');

    fireEvent.change(fromDateInput, { target: { value: '2024-01-01' } });
    fireEvent.change(toDateInput, { target: { value: '2024-01-31' } });

    expect(mockOnDateFromChange).toHaveBeenCalledWith('2024-01-01');
    expect(mockOnDateToChange).toHaveBeenCalledWith('2024-01-31');
  });

  it('applies custom className', () => {
    const { container } = render(
      <EmailLogFilters {...defaultProps} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('has proper accessibility attributes', () => {
    render(<EmailLogFilters {...defaultProps} />);

    expect(screen.getByRole('search')).toBeInTheDocument();
    expect(screen.getByLabelText('Email log filters')).toBeInTheDocument();
    expect(screen.getByRole('searchbox')).toBeInTheDocument();
  });

  it('uses custom email types when provided', () => {
    const customEmailTypes = ['custom_type_1', 'custom_type_2'];
    render(
      <EmailLogFilters 
        {...defaultProps} 
        emailTypes={customEmailTypes} 
      />
    );

    // The select component should receive the custom email types
    // This would be more thoroughly tested in integration tests
    expect(screen.getByTestId('select')).toBeInTheDocument();
  });
});
