/**
 * Email Testing Utilities for IEPA Conference Registration
 * Supports both IMAP (Gmail/Mailtrap) and Supabase Inbucket for email verification
 */

const imaps = require('imap-simple');
const { simpleParser } = require('mailparser');

/**
 * Configuration for different email testing environments
 */
const EMAIL_CONFIGS = {
  // Gmail with app-specific password
  gmail: {
    imap: {
      host: 'imap.gmail.com',
      port: 993,
      tls: true,
      authTimeout: 10000,
    },
  },

  // Mailtrap for testing
  mailtrap: {
    imap: {
      host: 'imap.mailtrap.io',
      port: 993,
      tls: true,
      authTimeout: 10000,
    },
  },

  // Local Supabase Inbucket (for development)
  inbucket: {
    baseUrl: 'http://127.0.0.1:54324',
    apiUrl: 'http://127.0.0.1:54324/api/v1',
  },
};

/**
 * Wait for an email using IMAP connection
 * @param {Object} options - Email waiting options
 * @param {string} options.email - Email address to check
 * @param {string} options.password - Email password or app-specific password
 * @param {string} options.provider - Email provider ('gmail' or 'mailtrap')
 * @param {string} options.subjectMatch - Text that should be in the email subject
 * @param {string} options.toMatch - Email address that should be in the 'to' field
 * @param {number} options.timeout - Timeout in milliseconds (default: 30000)
 * @param {boolean} options.markAsRead - Whether to mark email as read (default: true)
 * @returns {Promise<Object>} Parsed email object
 */
async function waitForEmailIMAP({
  email,
  password,
  provider = 'gmail',
  subjectMatch,
  toMatch = null,
  timeout = 30000,
  markAsRead = true,
}) {
  console.log(
    `📧 Waiting for email with subject containing: "${subjectMatch}"`
  );

  const config = {
    ...EMAIL_CONFIGS[provider],
    imap: {
      ...EMAIL_CONFIGS[provider].imap,
      user: email,
      password: password,
    },
  };

  let connection;
  try {
    connection = await imaps.connect(config);
    await connection.openBox('INBOX');

    const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
    const end = Date.now() + timeout;
    let attempts = 0;

    while (Date.now() < end) {
      attempts++;
      console.log(`📧 Checking for emails (attempt ${attempts})...`);

      const searchCriteria = ['UNSEEN'];
      const fetchOptions = {
        bodies: ['HEADER', 'TEXT', ''],
        markSeen: markAsRead,
      };

      const messages = await connection.search(searchCriteria, fetchOptions);
      console.log(`📧 Found ${messages.length} unread message(s)`);

      for (const message of messages) {
        try {
          const all = message.parts.find(part => part.which === '');
          if (!all) continue;

          const parsed = await simpleParser(all.body);
          console.log(
            `📧 Checking email: "${parsed.subject}" to: ${parsed.to?.text}`
          );

          // Check subject match
          const subjectMatches =
            parsed.subject && parsed.subject.includes(subjectMatch);

          // Check recipient match if specified
          const toMatches =
            !toMatch || (parsed.to && parsed.to.text.includes(toMatch));

          if (subjectMatches && toMatches) {
            console.log(`✅ Found matching email: "${parsed.subject}"`);
            await connection.end();
            return {
              subject: parsed.subject,
              from: parsed.from?.text,
              to: parsed.to?.text,
              date: parsed.date,
              text: parsed.text,
              html: parsed.html,
              attachments: parsed.attachments || [],
            };
          }
        } catch (parseError) {
          console.warn(`⚠️ Failed to parse email:`, parseError.message);
        }
      }

      await delay(3000); // Wait 3 seconds before next check
    }

    throw new Error(
      `Email with subject "${subjectMatch}" not received within ${timeout}ms`
    );
  } finally {
    if (connection) {
      try {
        await connection.end();
      } catch (error) {
        console.warn(
          'Warning: Failed to close IMAP connection:',
          error.message
        );
      }
    }
  }
}

/**
 * Wait for an email using Supabase Inbucket (for local development)
 * @param {Object} options - Email waiting options
 * @param {string} options.toEmail - Email address to check
 * @param {string} options.subjectMatch - Text that should be in the email subject
 * @param {number} options.timeout - Timeout in milliseconds (default: 30000)
 * @returns {Promise<Object>} Email object from Inbucket
 */
async function waitForEmailInbucket({
  toEmail,
  subjectMatch,
  timeout = 30000,
}) {
  console.log(
    `📧 Waiting for email in Inbucket to: ${toEmail} with subject: "${subjectMatch}"`
  );

  const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
  const end = Date.now() + timeout;
  let attempts = 0;

  // Extract mailbox name from email (part before @)
  const mailboxName = toEmail.split('@')[0];
  const mailboxUrl = `${EMAIL_CONFIGS.inbucket.apiUrl}/mailbox/${mailboxName}`;

  while (Date.now() < end) {
    attempts++;
    console.log(`📧 Checking Inbucket mailbox (attempt ${attempts})...`);

    try {
      const response = await fetch(mailboxUrl);
      if (!response.ok) {
        console.log(`📧 Mailbox not found or empty, waiting...`);
        await delay(3000);
        continue;
      }

      const messages = await response.json();
      console.log(`📧 Found ${messages.length} message(s) in Inbucket`);

      for (const message of messages) {
        if (message.subject && message.subject.includes(subjectMatch)) {
          console.log(
            `✅ Found matching email in Inbucket: "${message.subject}"`
          );

          // Fetch full message content
          const messageUrl = `${EMAIL_CONFIGS.inbucket.apiUrl}/mailbox/${mailboxName}/${message.id}`;
          const messageResponse = await fetch(messageUrl);
          const fullMessage = await messageResponse.json();

          return {
            id: message.id,
            subject: message.subject,
            from: message.from,
            to: message.to,
            date: message.date,
            text: fullMessage.body?.text,
            html: fullMessage.body?.html,
            attachments: fullMessage.attachments || [],
          };
        }
      }
    } catch (error) {
      console.warn(`⚠️ Error checking Inbucket:`, error.message);
    }

    await delay(3000);
  }

  throw new Error(
    `Email with subject "${subjectMatch}" not received in Inbucket within ${timeout}ms`
  );
}

/**
 * Extract confirmation links from email content
 * @param {string} emailContent - Email text or HTML content
 * @returns {Array<string>} Array of found URLs
 */
function extractLinksFromEmail(emailContent) {
  if (!emailContent) return [];

  // Match HTTP/HTTPS URLs
  const urlRegex = /https?:\/\/[^\s<>"']+/gi;
  const matches = emailContent.match(urlRegex) || [];

  return matches.map(url => {
    // Clean up URLs that might have trailing punctuation
    return url.replace(/[.,;!?]+$/, '');
  });
}

/**
 * Validate email content for IEPA conference registration
 * @param {Object} email - Parsed email object
 * @param {Object} expectedContent - Expected content to validate
 * @returns {Object} Validation result
 */
function validateEmailContent(email, expectedContent = {}) {
  const validation = {
    isValid: true,
    errors: [],
    warnings: [],
  };

  // Check required fields
  if (!email.subject) {
    validation.isValid = false;
    validation.errors.push('Email subject is missing');
  }

  if (!email.from) {
    validation.isValid = false;
    validation.errors.push('Email from address is missing');
  }

  if (!email.to) {
    validation.isValid = false;
    validation.errors.push('Email to address is missing');
  }

  // Check IEPA branding
  const content = (email.html || email.text || '').toLowerCase();
  if (!content.includes('iepa')) {
    validation.warnings.push('Email does not contain IEPA branding');
  }

  // Check expected content
  if (expectedContent.subjectContains) {
    expectedContent.subjectContains.forEach(text => {
      if (!email.subject.toLowerCase().includes(text.toLowerCase())) {
        validation.errors.push(`Subject should contain: "${text}"`);
        validation.isValid = false;
      }
    });
  }

  if (expectedContent.bodyContains) {
    expectedContent.bodyContains.forEach(text => {
      if (!content.includes(text.toLowerCase())) {
        validation.errors.push(`Body should contain: "${text}"`);
        validation.isValid = false;
      }
    });
  }

  // Check for confirmation links
  const links = extractLinksFromEmail(email.html || email.text);
  if (expectedContent.shouldHaveLinks && links.length === 0) {
    validation.warnings.push(
      'Email should contain confirmation or action links'
    );
  }

  return validation;
}

/**
 * Main email waiting function that automatically chooses the best method
 * @param {Object} options - Email waiting options
 * @returns {Promise<Object>} Parsed email object
 */
async function waitForEmail(options) {
  const {
    email,
    password,
    provider,
    subjectMatch,
    toMatch,
    timeout = 30000,
    useInbucket = false,
  } = options;

  // Use Inbucket for local development if specified or if no IMAP credentials
  if (useInbucket || (!email && !password)) {
    return waitForEmailInbucket({
      toEmail: toMatch || email,
      subjectMatch,
      timeout,
    });
  }

  // Use IMAP for external email providers
  return waitForEmailIMAP({
    email,
    password,
    provider: provider || 'gmail',
    subjectMatch,
    toMatch,
    timeout,
  });
}

module.exports = {
  waitForEmail,
  waitForEmailIMAP,
  waitForEmailInbucket,
  extractLinksFromEmail,
  validateEmailContent,
  EMAIL_CONFIGS,
};
