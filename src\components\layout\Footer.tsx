'use client';

import Link from 'next/link';
import Image from 'next/image';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import { getShortVersion } from '@/lib/version';
import { DebugTrigger } from '@/components/debug/DebugTrigger';

export function Footer() {
  const currentYear = new Date().getFullYear();
  const versionString = getShortVersion();

  // Debug: Log version info in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Footer version debug:', {
      versionString,
      gitCommit: process.env.NEXT_PUBLIC_GIT_COMMIT_SHORT,
      gitBranch: process.env.NEXT_PUBLIC_GIT_BRANCH,
      buildTime: process.env.NEXT_PUBLIC_BUILD_TIME,
    });
  }

  // Temporary hardcoded version for testing
  const testVersion = 'v0.1.0 (18e454a) [development]';

  const footerLinks = [
    {
      title: 'Annual Meeting',
      links: [
        {
          name: 'Annual Meeting Info',
          href: 'https://iepa.com/annual-meeting',
          external: true,
        },
        { name: 'Agenda', href: '/agenda' },
        { name: 'Registration', href: '/register' },
        {
          name: 'Contact Us',
          href: 'https://iepa.com/contact',
          external: true,
        },
        {
          name: 'Join IEPA',
          href: 'https://iepa.com/iep-membership',
          external: true,
        },
      ],
    },
    {
      title: 'Registration',
      links: [
        { name: 'Attendee Registration', href: '/register/attendee' },
        { name: 'Speaker Registration', href: '/register/speaker' },
        { name: 'Sponsorship', href: '/register/sponsor' },
        { name: 'My Registrations', href: '/my-registrations' },
      ],
    },
    {
      title: 'Support',
      links: [
        { name: 'Contact Support', href: '/contact' },
        {
          name: 'Terms & Conditions',
          href: 'https://iepa.com/terms',
          external: true,
        },
        {
          name: 'Job Postings',
          href: 'https://iepa.com/job-postings',
          external: true,
        },
      ],
    },
  ];

  return (
    <footer className="iepa-footer">
      <div className="iepa-footer-content">
        <div className="iepa-container">
          <div className="iepa-footer-grid">
            {/* Logo and Description */}
            <div className="iepa-footer-brand">
              <Link href="/" className="iepa-footer-logo-link">
                <Image
                  src="/iepa_svg_logo.svg"
                  alt="IEPA Logo"
                  width={140}
                  height={40}
                  className="iepa-footer-logo"
                  priority
                />
              </Link>
              <p className="iepa-footer-description">
                Independent Energy Producers Association - California&apos;s
                oldest nonprofit trade association representing developers and
                operators of independent energy facilities and power marketers.
              </p>
              <p className="iepa-footer-conference">
                IEPA {CONFERENCE_YEAR} Annual Meeting Registration
              </p>
            </div>

            {/* Footer Links */}
            {footerLinks.map(section => (
              <div key={section.title} className="iepa-footer-section">
                <h3 className="iepa-footer-section-title">{section.title}</h3>
                <ul className="iepa-footer-links">
                  {section.links.map(link => (
                    <li key={link.name}>
                      {link.external ? (
                        <a
                          href={link.href}
                          className="iepa-footer-link"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {link.name}
                        </a>
                      ) : (
                        <Link href={link.href} className="iepa-footer-link">
                          {link.name}
                        </Link>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Footer Bottom */}
          <div className="iepa-footer-bottom">
            <div className="iepa-footer-bottom-content">
              <div className="iepa-footer-copyright-section">
                <p className="iepa-footer-copyright">
                  © {currentYear} Independent Energy Producers Association. All
                  rights reserved.
                </p>
                <p
                  className="iepa-footer-version"
                  style={{
                    color: 'yellow',
                    backgroundColor: 'red',
                    padding: '4px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                  }}
                >
                  {testVersion}
                </p>
              </div>
              <div className="iepa-footer-bottom-links">
                <a
                  href="https://iepa.com/terms"
                  className="iepa-footer-bottom-link"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Terms
                </a>
                <a
                  href="https://iepa.com/contact"
                  className="iepa-footer-bottom-link"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Contact
                </a>
                <DebugTrigger className="ml-2" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
