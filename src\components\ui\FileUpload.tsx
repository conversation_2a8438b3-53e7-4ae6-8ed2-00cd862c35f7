import React, { useRef, useState, useId } from 'react';
import { cn } from '@/lib/utils';
import { Label } from './label';
import { Button } from './button';
import { useFileUpload, FileUploadOptions } from '@/hooks/useFileUpload';
import { FiUpload, FiFile, FiX, FiCheck, FiAlertCircle, FiDownload, FiEye, FiEdit3, FiPlus } from 'react-icons/fi';

export interface FileUploadProps extends FileUploadOptions {
  label?: string;
  description?: string;
  isRequired?: boolean;
  onFileUpload?: (url: string | null, file: File | null) => void;
  className?: string;
  accept?: string;
  placeholder?: string;
  disabled?: boolean;
  // Enhanced props for existing files
  existingFileUrl?: string | null;
  existingFileName?: string;
  showPreview?: boolean; // For images
  allowDownload?: boolean;
  allowView?: boolean;
  onRemoveExisting?: () => void;
}

export function FileUpload({
  label,
  description,
  isRequired,
  onFileUpload,
  className,
  accept,
  placeholder = 'Click to upload or drag and drop',
  disabled = false,
  // Enhanced props
  existingFileUrl,
  existingFileName,
  showPreview = false,
  allowDownload = true,
  allowView = true,
  onRemoveExisting,
  ...uploadOptions
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const uniqueId = useId();

  const { uploading, progress, error, url, uploadFile, resetState } =
    useFileUpload(uploadOptions);

  const handleFileSelect = async (file: File) => {
    setSelectedFile(file);
    resetState();

    const uploadedUrl = await uploadFile(file);
    onFileUpload?.(uploadedUrl, file);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) return;

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    resetState();
    onFileUpload?.(null, null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (disabled || !fileInputRef.current) {
      return;
    }

    fileInputRef.current.click();
  };

  const getStatusIcon = () => {
    if (uploading) return <FiUpload className="animate-spin" />;
    if (error) return <FiAlertCircle className="text-red-500" />;
    if (url) return <FiCheck className="text-green-500" />;
    return <FiFile />;
  };

  const getStatusText = () => {
    if (uploading) return `Uploading... ${progress}%`;
    if (error) return error;
    if (url && selectedFile) return `Uploaded: ${selectedFile.name}`;
    if (selectedFile) return selectedFile.name;
    return placeholder;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileNameFromUrl = (url: string) => {
    try {
      const urlParts = url.split('/');
      const fullFileName = urlParts[urlParts.length - 1].split('?')[0];

      // If the filename is very long (likely a signed URL), extract just the original filename
      if (fullFileName.length > 50) {
        // Look for patterns like "timestamp-originalname.ext"
        const match = fullFileName.match(/^\d+-(.+)$/);
        if (match) {
          return match[1]; // Return just the original filename part
        }

        // If no pattern match, truncate and add ellipsis
        return fullFileName.substring(0, 30) + '...' + fullFileName.substring(fullFileName.lastIndexOf('.'));
      }

      return fullFileName;
    } catch {
      return 'Unknown file';
    }
  };

  const isImageFile = (url: string) => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
    return imageExtensions.some(ext => url.toLowerCase().includes(ext));
  };

  const getFileTypeFromUrl = (url: string) => {
    try {
      const fileName = url.split('/').pop()?.split('?')[0] || '';
      const extension = fileName.split('.').pop()?.toLowerCase() || '';

      const typeMap: { [key: string]: string } = {
        'pdf': 'PDF Document',
        'doc': 'Word Document',
        'docx': 'Word Document',
        'ppt': 'PowerPoint',
        'pptx': 'PowerPoint',
        'jpg': 'JPEG Image',
        'jpeg': 'JPEG Image',
        'png': 'PNG Image',
        'webp': 'WebP Image',
        'gif': 'GIF Image',
      };

      return typeMap[extension] || 'Document';
    } catch {
      return 'File';
    }
  };

  const hasExistingFile = existingFileUrl && !selectedFile && !url;
  const hasNewFile = selectedFile || url;

  return (
    <div className={cn('space-y-3', className)}>
      {label && (
        <Label>
          {label}
          {isRequired && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}

      {/* Hidden file input - always present */}
      <input
        ref={fileInputRef}
        id={`file-input-${uniqueId}`}
        type="file"
        className="hidden"
        accept={accept}
        onChange={handleInputChange}
        disabled={disabled}
      />

      {/* Existing File Display */}
      {hasExistingFile && (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <div className="flex items-start justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <FiCheck className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-gray-900">Current File</span>
              </div>

              {showPreview && existingFileUrl && isImageFile(existingFileUrl) ? (
                <div className="mb-3">
                  <div className="relative inline-block">
                    <img
                      src={existingFileUrl}
                      alt="Current file preview"
                      className="w-32 h-32 object-cover rounded-lg border shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (existingFileUrl) window.open(existingFileUrl, '_blank');
                      }}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 rounded-lg transition-all cursor-pointer flex items-center justify-center opacity-0 hover:opacity-100">
                      <FiEye className="w-6 h-6 text-white drop-shadow-lg" />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2 text-center">Click to view full size</p>
                </div>
              ) : (
                <div className="mb-3 p-3 bg-gray-50 rounded-lg border">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <FiFile className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="flex-1 min-w-0 overflow-hidden">
                      <p className="text-sm font-medium text-gray-900 break-all line-clamp-2">
                        {existingFileName || getFileNameFromUrl(existingFileUrl || '')}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {getFileTypeFromUrl(existingFileUrl || '')} • Click &quot;View&quot; to open
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex flex-wrap gap-2">
                {allowView && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (existingFileUrl) window.open(existingFileUrl, '_blank');
                    }}
                    className="h-8 px-3 text-xs bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700 hover:text-blue-800"
                  >
                    <FiEye className="w-3 h-3 mr-1" />
                    View
                  </Button>
                )}
                {allowDownload && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (existingFileUrl) {
                        const link = document.createElement('a');
                        link.href = existingFileUrl;
                        link.download = existingFileName || getFileNameFromUrl(existingFileUrl);
                        link.click();
                      }
                    }}
                    className="h-8 px-3 text-xs bg-green-50 hover:bg-green-100 border-green-200 text-green-700 hover:text-green-800"
                  >
                    <FiDownload className="w-3 h-3 mr-1" />
                    Download
                  </Button>
                )}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClick();
                  }}
                  className="h-8 px-3 text-xs bg-orange-50 hover:bg-orange-100 border-orange-200 text-orange-700 hover:text-orange-800"
                  disabled={disabled}
                >
                  <FiEdit3 className="w-3 h-3 mr-1" />
                  Change
                </Button>
                {onRemoveExisting && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemoveExisting();
                    }}
                    className="h-8 px-3 text-xs bg-red-50 hover:bg-red-100 border-red-200 text-red-600 hover:text-red-700"
                  >
                    <FiX className="w-3 h-3 mr-1" />
                    Remove
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Upload Area - Show when no existing file or when uploading new file */}
      {(!hasExistingFile || hasNewFile) && (
        <div
          className={cn(
            'relative border-2 border-dashed rounded-lg p-6 transition-colors',
            'hover:border-primary/50 focus-within:border-primary focus-within:ring-2 focus-within:ring-primary/20',
            dragActive && 'border-primary bg-primary/5',
            error && 'border-red-300 bg-red-50',
            url && 'border-green-300 bg-green-50',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >


          <div className="flex flex-col items-center justify-center space-y-3">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span
                className={cn(
                  'text-sm font-medium',
                  error && 'text-red-600',
                  url && 'text-green-600'
                )}
              >
                {getStatusText()}
              </span>
            </div>

            {selectedFile && (
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                <span>{formatFileSize(selectedFile.size)}</span>
                {!uploading && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={e => {
                      e.stopPropagation();
                      handleRemoveFile();
                    }}
                    className="h-auto p-1"
                  >
                    <FiX className="h-3 w-3" />
                  </Button>
                )}
              </div>
            )}

            {uploading && (
              <div className="w-full max-w-xs">
                <div className="bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>
            )}

            {!selectedFile && !uploading && (
              <>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleClick}
                    disabled={disabled}
                    className="h-8 px-3 text-xs"
                  >
                    <FiPlus className="w-3 h-3 mr-1" />
                    {hasExistingFile ? 'Upload New' : 'Add File'}
                  </Button>
                </div>
                <p className="text-xs text-gray-500 text-center">
                  {accept && (
                    <>
                      Accepted formats: {accept.replace(/\./g, '').toUpperCase()}
                      <br />
                    </>
                  )}
                  {uploadOptions.maxSize && (
                    <>
                      Max size: {formatFileSize(uploadOptions.maxSize)}
                      <br />
                    </>
                  )}
                  Click button to browse files
                </p>
              </>
            )}
          </div>
        </div>
      )}

      {description && <p className="text-sm text-gray-600">{description}</p>}
    </div>
  );
}
