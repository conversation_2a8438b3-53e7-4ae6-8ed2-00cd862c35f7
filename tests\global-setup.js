/**
 * Global Setup for IEPA E2E Tests
 *
 * This file runs before all tests and sets up the testing environment.
 * It ensures the application is ready for testing and creates necessary test data.
 */

import { chromium } from '@playwright/test';

async function globalSetup() {
  console.log('🚀 Starting global setup for IEPA E2E tests...');

  try {
    // Launch browser for setup tasks
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();

    // Set base URL
    const baseURL = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:6969';

    // Wait for application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto(`${baseURL}/health`);

    // Check if health endpoint responds
    try {
      await page.waitForResponse(
        response =>
          response.url().includes('/health') && response.status() === 200,
        { timeout: 30000 }
      );
      console.log('✅ Application health check passed');
    } catch (error) {
      console.log('⚠️ Health check failed, proceeding anyway...');
    }

    // Verify database connectivity
    console.log('🔍 Verifying database connectivity...');
    try {
      await page.goto(`${baseURL}/api/health`);
      const response = await page.waitForResponse(
        response => response.url().includes('/api/health'),
        { timeout: 10000 }
      );

      if (response.status() === 200) {
        console.log('✅ Database connectivity verified');
      }
    } catch (error) {
      console.log('⚠️ Database connectivity check failed:', error.message);
    }

    // Setup test discount code if needed
    console.log('🎫 Setting up test discount code...');
    try {
      await setupTestDiscountCode(page, baseURL);
    } catch (error) {
      console.log('⚠️ Failed to setup test discount code:', error.message);
    }

    // Cleanup
    await browser.close();

    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  }
}

async function setupTestDiscountCode(page, baseURL) {
  // This would typically create a test discount code in the database
  // For now, we'll just verify the discount code validation endpoint works

  const testCode = 'TEST';
  const testData = {
    code: testCode,
    registrationType: 'iepa-member',
    totalAmount: 2300,
    userId: null,
  };

  try {
    const response = await page.request.post(
      `${baseURL}/api/discount-codes/validate`,
      {
        data: testData,
      }
    );

    if (response.ok()) {
      console.log('✅ Test discount code validation endpoint working');
    } else {
      console.log(
        '⚠️ Discount code validation endpoint returned:',
        response.status()
      );
    }
  } catch (error) {
    console.log('⚠️ Could not test discount code endpoint:', error.message);
  }
}

export default globalSetup;
