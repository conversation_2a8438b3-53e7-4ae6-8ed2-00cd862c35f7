# SendGrid Setup Guide - IEPA Conference Registration

## Overview

This document provides step-by-step instructions for setting up SendGrid email integration for the IEPA 2025 Conference Registration Application. SendGrid handles business transactional emails while Supabase continues to handle authentication emails.

## Prerequisites

1. **SendGrid Account**: Sign up at [sendgrid.com](https://sendgrid.com)
2. **Domain Verification**: Access to DNS settings for your domain
3. **Environment Access**: Ability to modify `.env.local` file
4. **Admin Access**: Supabase admin access for database setup

## 📧 Email Service Strategy

### Separation of Concerns
- **Supabase Auth**: Password resets, email verification, account confirmations
- **SendGrid**: Registration confirmations, payment receipts, notifications, marketing

### Email Types Handled by SendGrid
- Registration confirmation emails (immediate after form submission)
- Payment confirmation emails (after successful Stripe payment)
- Golf tournament add-on confirmations
- Custom admin communications
- Conference updates and reminders

## Step 1: Create SendGrid Account

1. **Sign Up**: Go to [sendgrid.com](https://sendgrid.com) and create an account
2. **Choose Plan**: 
   - **Free Plan**: 100 emails/day (good for testing)
   - **Essentials**: $19.95/month for 50k emails/month (recommended for production)
3. **Verify Email**: Complete email verification for your account

## Step 2: Domain Authentication

### Single Sender Verification (Quick Setup)
1. Go to **Settings** → **Sender Authentication** → **Single Sender Verification**
2. Add email address: `<EMAIL>` (or your domain)
3. Complete verification process

### Domain Authentication (Recommended for Production)
1. Go to **Settings** → **Sender Authentication** → **Authenticate Your Domain**
2. Enter your domain: `iepaconference.com`
3. Select DNS provider or manual setup
4. Add the provided DNS records to your domain:

```dns
# Example DNS records (actual values will be provided by SendGrid)
CNAME: em1234.iepaconference.com → u1234567.wl.sendgrid.net
CNAME: s1._domainkey.iepaconference.com → s1.domainkey.u1234567.wl.sendgrid.net
CNAME: s2._domainkey.iepaconference.com → s2.domainkey.u1234567.wl.sendgrid.net
```

5. **Verify**: Click "Verify" in SendGrid dashboard
6. **Link Management**: Enable link branding for professional emails

## Step 3: Create API Key

1. **Navigate**: Go to **Settings** → **API Keys**
2. **Create Key**: Click "Create API Key"
3. **Name**: Enter descriptive name: `IEPA Conference Registration App`
4. **Permissions**: Select **Full Access** or **Mail Send** (minimum required)
5. **Generate**: Click "Create & View"
6. **Save Key**: Copy the API key immediately (only shown once)

```
# Example API key format
SG.1234567890abcdef.1234567890abcdef1234567890abcdef1234567890abcdef
```

## Step 4: Configure Environment Variables

Add the following variables to your `.env.local` file:

```bash
# SendGrid Configuration
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME="IEPA Conference 2025"

# Optional: Additional email addresses
SENDGRID_SUPPORT_EMAIL=<EMAIL>
SENDGRID_NOREPLY_EMAIL=<EMAIL>
```

### Required Variables
- **`SENDGRID_API_KEY`**: Your SendGrid API key with send permissions
- **`SENDGRID_FROM_EMAIL`**: Verified sender email address
- **`SENDGRID_FROM_NAME`**: Display name for emails

### Optional Variables
- **`SENDGRID_SUPPORT_EMAIL`**: Support/reply-to email address
- **`SENDGRID_NOREPLY_EMAIL`**: No-reply email address for automated messages

## Step 5: Set Up Email Logging Database

### Option A: Use API Setup (Recommended)
```bash
curl -X POST http://localhost:6969/api/admin/setup-email-log
```

### Option B: Manual SQL Setup
1. Open Supabase SQL Editor
2. Copy contents from `src/lib/database/email-log-schema.sql`
3. Execute the SQL to create:
   - `iepa_email_log` table
   - Performance indexes
   - Row Level Security policies
   - Admin access controls

## Step 6: Test Configuration

### Test Email Service Configuration
```bash
# Check configuration status
curl http://localhost:6969/api/test-email
```

Expected response:
```json
{
  "status": "ready",
  "configured": true,
  "config": {
    "fromEmail": "<EMAIL>",
    "fromName": "IEPA Conference 2025",
    "supportEmail": "<EMAIL>"
  }
}
```

### Send Test Email
```bash
curl -X POST http://localhost:6969/api/test-email \
  -H "Content-Type: application/json" \
  -d '{
    "testEmail": "<EMAIL>",
    "testType": "basic"
  }'
```

### Test Different Email Types
```bash
# Registration confirmation test
curl -X POST http://localhost:6969/api/test-email \
  -H "Content-Type: application/json" \
  -d '{
    "testEmail": "<EMAIL>",
    "testType": "registration"
  }'

# Payment confirmation test
curl -X POST http://localhost:6969/api/test-email \
  -H "Content-Type: application/json" \
  -d '{
    "testEmail": "<EMAIL>",
    "testType": "payment"
  }'

# Password reset test
curl -X POST http://localhost:6969/api/test-email \
  -H "Content-Type: application/json" \
  -d '{
    "testEmail": "<EMAIL>",
    "testType": "password-reset"
  }'
```

## Step 7: Verify Integration

### Registration Flow Test
1. **Navigate**: Go to `/register/attendee`
2. **Submit Form**: Complete attendee registration
3. **Check Email**: Verify registration confirmation email received
4. **Check Logs**: Query `iepa_email_log` table for entry

### Payment Flow Test
1. **Complete Registration**: Submit registration with payment
2. **Process Payment**: Use Stripe test cards to complete payment
3. **Check Email**: Verify payment confirmation email received
4. **Check Logs**: Verify email logged with payment metadata

### Golf Add-On Test
1. **Register Attendee**: Complete basic attendee registration
2. **Add Golf**: Purchase golf tournament add-on
3. **Check Email**: Verify golf-specific confirmation email
4. **Check Logs**: Verify specialized golf email logged

## 📊 Monitoring and Analytics

### Email Log Queries
```sql
-- View recent emails
SELECT 
  recipient_email,
  email_type,
  status,
  sent_at,
  subject
FROM iepa_email_log 
WHERE created_at >= NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;

-- Check delivery success rate
SELECT 
  email_type,
  COUNT(*) as total,
  COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent,
  ROUND(
    COUNT(CASE WHEN status = 'sent' THEN 1 END) * 100.0 / COUNT(*),
    2
  ) as success_rate
FROM iepa_email_log 
GROUP BY email_type;

-- Failed email troubleshooting
SELECT 
  recipient_email,
  subject,
  error_message,
  created_at
FROM iepa_email_log 
WHERE status = 'failed'
ORDER BY created_at DESC;
```

### SendGrid Dashboard
1. **Activity**: Monitor email delivery and engagement
2. **Suppressions**: Check bounced and blocked emails
3. **Analytics**: View open rates, click rates, and delivery metrics
4. **Alerts**: Set up notifications for delivery issues

## 🔧 Troubleshooting

### Common Issues

#### 1. "SendGrid API key not found"
- **Cause**: Missing or incorrect `SENDGRID_API_KEY` in `.env.local`
- **Solution**: Verify API key is correctly set and restart development server

#### 2. "From email not verified"
- **Cause**: Sender email not verified in SendGrid
- **Solution**: Complete single sender verification or domain authentication

#### 3. "Email sending failed"
- **Cause**: Various SendGrid API errors
- **Solution**: Check `iepa_email_log` table for specific error messages

#### 4. "Database logging not available"
- **Cause**: Missing Supabase service role key or email log table
- **Solution**: Verify `SUPABASE_SERVICE_ROLE_KEY` and run email log setup

#### 5. Registration emails not sending
- **Cause**: Integration not properly configured
- **Solution**: Check browser console for `[EMAIL-ERROR]` messages

### Debug Process
1. **Check Environment**: Verify all required environment variables
2. **Test Configuration**: Use `/api/test-email` endpoint
3. **Check Logs**: Review `iepa_email_log` table for error details
4. **SendGrid Activity**: Check SendGrid dashboard for delivery status
5. **Console Output**: Look for `[EMAIL-DEBUG]` and `[EMAIL-ERROR]` messages

### Log Analysis
```sql
-- Recent errors
SELECT * FROM iepa_email_log 
WHERE status = 'failed' 
AND created_at >= NOW() - INTERVAL '1 day';

-- Specific user emails
SELECT * FROM iepa_email_log 
WHERE recipient_email = '<EMAIL>'
ORDER BY created_at DESC;

-- Email type performance
SELECT 
  email_type,
  AVG(EXTRACT(EPOCH FROM (sent_at - created_at))) as avg_send_time_seconds
FROM iepa_email_log 
WHERE status = 'sent'
GROUP BY email_type;
```

## 🔐 Security Best Practices

### API Key Security
- ✅ Store API key in environment variables only
- ✅ Never commit API keys to version control
- ✅ Use different keys for development and production
- ✅ Regenerate keys if compromised

### Email Content Security
- ✅ Validate all user input in email content
- ✅ Use template-based emails to prevent injection
- ✅ Sanitize dynamic content in emails
- ✅ Log preview only, not full email content

### Access Control
- ✅ Row Level Security on email logs
- ✅ Admin-only access to email analytics
- ✅ Service role isolation for logging
- ✅ Regular audit of email permissions

## 🚀 Production Deployment

### Pre-Production Checklist
- [ ] Domain authentication completed
- [ ] Production API key created with mail send permissions
- [ ] Environment variables updated for production
- [ ] Email templates tested and approved
- [ ] Monitoring and alerting configured
- [ ] Backup email service identified (if needed)

### Production Environment Variables
```bash
# Production SendGrid Configuration
SENDGRID_API_KEY=your_production_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME="IEPA Conference 2025"
SENDGRID_SUPPORT_EMAIL=<EMAIL>
```

### Monitoring Setup
1. **SendGrid Alerts**: Configure delivery and engagement alerts
2. **Database Monitoring**: Set up alerts for high email failure rates
3. **Performance Monitoring**: Track email sending and logging performance
4. **Error Tracking**: Monitor application logs for email-related errors

## 📋 Maintenance

### Regular Tasks
- **Weekly**: Review failed email logs and resolve issues
- **Monthly**: Analyze email engagement metrics
- **Quarterly**: Review and update email templates
- **Annually**: Audit email permissions and security

### Email Log Cleanup
```sql
-- Archive old email logs (older than 1 year)
-- Run monthly as maintenance task
DELETE FROM iepa_email_log 
WHERE created_at < NOW() - INTERVAL '1 year'
AND status IN ('sent', 'failed');
```

### Performance Optimization
- Monitor email log table growth
- Optimize queries based on usage patterns
- Consider partitioning for high-volume use
- Archive old logs to maintain performance

---

**Setup Status**: Ready for implementation  
**Production Ready**: With domain authentication  
**Monitoring**: Email logs and SendGrid analytics  
**Support**: Complete audit trail and debugging capabilities  
**Last Updated**: January 2025 