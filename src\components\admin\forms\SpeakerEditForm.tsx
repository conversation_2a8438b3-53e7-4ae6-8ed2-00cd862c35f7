'use client';

import React, { useState, useMemo } from 'react';
import { <PERSON>, CardBody, CardHeader, AdminSubmitButton } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PhoneInput } from '@/components/ui/phone-input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { FileUpload } from '@/components/ui/FileUpload';
import { updateSpeakerFiles } from '@/lib/fileManagement';
import { useSignedUrls } from '@/hooks/useSignedUrl';
import { showSuccess, showError } from '@/utils/notifications';
import { useAdminSubmitButton } from '@/hooks/useSubmitButton';
import {
  FiUser,
  FiBriefcase,
  FiMic,
  FiFile,
  FiSave,
  FiAlertCircle,
} from 'react-icons/fi';

interface SpeakerData {
  id: string;
  full_name: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  organization_name: string;
  job_title: string;
  bio: string;
  presentation_title?: string;
  presentation_description?: string;
  presentation_duration?: string;
  target_audience?: string;
  learning_objectives?: string;
  speaker_experience?: string;
  previous_speaking?: string;
  equipment_needs?: string;
  special_requests?: string;
  presentation_file_url?: string | null;
  headshot_url?: string | null;
}

interface SpeakerEditFormProps {
  speakerData: SpeakerData;
  onSave: (updatedData: Partial<SpeakerData>) => Promise<void>;
  saving?: boolean;
}

export function SpeakerEditForm({ speakerData, onSave, saving = false }: SpeakerEditFormProps) {
  const [formData, setFormData] = useState<Partial<SpeakerData>>(speakerData);
  const [newFiles, setNewFiles] = useState<{
    presentationFile?: File;
    headshot?: File;
  }>({});
  const [fileUploading, setFileUploading] = useState(false);

  // Submit button state management
  const { isSubmitting, handleSubmit: handleSubmitButton } = useAdminSubmitButton((error) => {
    showError('Update Failed', error);
  });

  // Memoize URLs array to prevent infinite loops
  const urlsToFetch = useMemo(() => [
    {
      url: speakerData.presentation_file_url,
      bucket: 'iepa-presentations',
      key: 'presentation',
    },
    {
      url: speakerData.headshot_url,
      bucket: 'iepa-presentations',
      key: 'headshot',
    },
  ], [speakerData.presentation_file_url, speakerData.headshot_url]);

  // Get signed URLs for current files
  const signedUrls = useSignedUrls(urlsToFetch, { enabled: true });

  const handleInputChange = (field: keyof SpeakerData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFileUpload = (type: 'presentationFile' | 'headshot', file: File | null) => {
    if (file) {
      setNewFiles(prev => ({
        ...prev,
        [type]: file,
      }));
    }
  };

  const handleSave = async () => {
    await handleSubmitButton(async () => {
      setFileUploading(true);

      try {
        // Update files if any were uploaded
        let fileUpdateResult;
        if (newFiles.presentationFile || newFiles.headshot) {
          fileUpdateResult = await updateSpeakerFiles(
            speakerData.id,
            newFiles,
            {
              presentationFileUrl: speakerData.presentation_file_url,
              headshotUrl: speakerData.headshot_url,
            }
          );

          if (!fileUpdateResult.success) {
            throw new Error(fileUpdateResult.error);
          }
        }

        // Prepare update data
        const updateData = {
          ...formData,
          updated_at: new Date().toISOString(),
        };

        // Include updated file URLs if files were uploaded
        if (fileUpdateResult?.updatedUrls) {
          if (fileUpdateResult.updatedUrls.presentationFileUrl) {
            updateData.presentation_file_url = fileUpdateResult.updatedUrls.presentationFileUrl;
          }
          if (fileUpdateResult.updatedUrls.headshotUrl) {
            updateData.headshot_url = fileUpdateResult.updatedUrls.headshotUrl;
          }
        }

        await onSave(updateData);

        // Reset file uploads after successful save
        setNewFiles({});

        showSuccess('Profile Updated', 'Speaker profile updated successfully!');
      } finally {
        setFileUploading(false);
      }
    });
  };

  const isLoading = saving || fileUploading || isSubmitting;

  return (
    <div className="space-y-6">
      {/* Personal Information */}
      <Card>
        <CardHeader>
          <h3 className="iepa-heading-3 flex items-center gap-2">
            <FiUser className="w-5 h-5" />
            Personal Information
          </h3>
        </CardHeader>
        <CardBody>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="first_name">First Name</Label>
              <Input
                id="first_name"
                value={formData.first_name || ''}
                onChange={e => handleInputChange('first_name', e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="last_name">Last Name</Label>
              <Input
                id="last_name"
                value={formData.last_name || ''}
                onChange={e => handleInputChange('last_name', e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email || ''}
                onChange={e => handleInputChange('email', e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="phone_number">Phone Number</Label>
              <PhoneInput
                id="phone_number"
                value={formData.phone_number || ''}
                onChange={value => handleInputChange('phone_number', value)}
                disabled={isLoading}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Professional Information */}
      <Card>
        <CardHeader>
          <h3 className="iepa-heading-3 flex items-center gap-2">
            <FiBriefcase className="w-5 h-5" />
            Professional Information
          </h3>
        </CardHeader>
        <CardBody>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="organization_name">Organization</Label>
              <Input
                id="organization_name"
                value={formData.organization_name || ''}
                onChange={e => handleInputChange('organization_name', e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="job_title">Job Title</Label>
              <Input
                id="job_title"
                value={formData.job_title || ''}
                onChange={e => handleInputChange('job_title', e.target.value)}
                disabled={isLoading}
              />
            </div>
          </div>
          <div className="mt-4">
            <Label htmlFor="bio">Biography</Label>
            <Textarea
              id="bio"
              rows={4}
              value={formData.bio || ''}
              onChange={e => handleInputChange('bio', e.target.value)}
              placeholder="Tell us about yourself and your expertise..."
              disabled={isLoading}
            />
          </div>
        </CardBody>
      </Card>

      {/* Presentation Information */}
      <Card>
        <CardHeader>
          <h3 className="iepa-heading-3 flex items-center gap-2">
            <FiMic className="w-5 h-5" />
            Presentation Information
          </h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <div>
              <Label htmlFor="presentation_title">Presentation Title</Label>
              <Input
                id="presentation_title"
                value={formData.presentation_title || ''}
                onChange={e => handleInputChange('presentation_title', e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="presentation_description">Presentation Description</Label>
              <Textarea
                id="presentation_description"
                rows={4}
                value={formData.presentation_description || ''}
                onChange={e => handleInputChange('presentation_description', e.target.value)}
                placeholder="Describe your presentation topic and key points..."
                disabled={isLoading}
              />
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="presentation_duration">Duration</Label>
                <Input
                  id="presentation_duration"
                  value={formData.presentation_duration || ''}
                  onChange={e => handleInputChange('presentation_duration', e.target.value)}
                  placeholder="e.g., 45 minutes"
                  disabled={isLoading}
                />
              </div>
              <div>
                <Label htmlFor="target_audience">Target Audience</Label>
                <Input
                  id="target_audience"
                  value={formData.target_audience || ''}
                  onChange={e => handleInputChange('target_audience', e.target.value)}
                  placeholder="e.g., Energy professionals, Developers"
                  disabled={isLoading}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="learning_objectives">Learning Objectives</Label>
              <Textarea
                id="learning_objectives"
                rows={3}
                value={formData.learning_objectives || ''}
                onChange={e => handleInputChange('learning_objectives', e.target.value)}
                placeholder="What will attendees learn from your presentation?"
                disabled={isLoading}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* File Uploads */}
      <Card>
        <CardHeader>
          <h3 className="iepa-heading-3 flex items-center gap-2">
            <FiFile className="w-5 h-5" />
            Files & Media
          </h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-6">
            <div>
              <FileUpload
                label="Presentation File"
                description="Upload presentation file (PDF, PPT, PPTX, DOC, DOCX - max 50MB)"
                bucket="iepa-presentations"
                folder="speaker-presentations"
                maxSize={52428800} // 50MB
                allowedTypes={[
                  'application/pdf',
                  'application/vnd.ms-powerpoint',
                  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                  'application/msword',
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                ]}
                accept=".pdf,.ppt,.pptx,.doc,.docx"
                onFileUpload={(url, file) => handleFileUpload('presentationFile', file)}
                placeholder="Upload presentation file"
                disabled={isLoading}
                existingFileUrl={formData.presentation_file_url ? (signedUrls.presentation?.signedUrl || formData.presentation_file_url) : null}
                existingFileName={formData.presentation_file_url ?
                  (() => {
                    const fullFileName = formData.presentation_file_url.split('/').pop()?.split('?')[0] || '';
                    // Extract clean filename from timestamp-filename pattern
                    const match = fullFileName.match(/^\d+-(.+)$/);
                    return match ? match[1] : (fullFileName.length > 30 ? fullFileName.substring(0, 30) + '...' : fullFileName);
                  })() : undefined}
                allowDownload={true}
                allowView={true}
                onRemoveExisting={() => {
                  setFormData(prev => ({ ...prev, presentation_file_url: null }));
                  setNewFiles(prev => ({ ...prev, presentationFile: undefined }));
                }}
              />
            </div>

            <div>
              <FileUpload
                label="Professional Headshot"
                description="Upload professional headshot (JPG, PNG, WebP - max 5MB)"
                bucket="iepa-presentations"
                folder="speaker-headshots"
                maxSize={5242880} // 5MB
                allowedTypes={['image/jpeg', 'image/png', 'image/webp']}
                accept=".jpg,.jpeg,.png,.webp"
                onFileUpload={(url, file) => handleFileUpload('headshot', file)}
                placeholder="Upload professional headshot"
                disabled={isLoading}
                existingFileUrl={formData.headshot_url ? (signedUrls.headshot?.signedUrl || formData.headshot_url) : null}
                existingFileName={formData.headshot_url ?
                  (() => {
                    const fullFileName = formData.headshot_url.split('/').pop()?.split('?')[0] || '';
                    // Extract clean filename from timestamp-filename pattern
                    const match = fullFileName.match(/^\d+-(.+)$/);
                    return match ? match[1] : (fullFileName.length > 30 ? fullFileName.substring(0, 30) + '...' : fullFileName);
                  })() : undefined}
                showPreview={true}
                allowDownload={true}
                allowView={true}
                onRemoveExisting={() => {
                  setFormData(prev => ({ ...prev, headshot_url: null }));
                  setNewFiles(prev => ({ ...prev, headshot: undefined }));
                }}
              />


            </div>
          </div>
        </CardBody>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <AdminSubmitButton
          onClick={handleSave}
          isSubmitting={isSubmitting}
          submittingText={fileUploading ? 'Uploading Files...' : 'Saving Changes...'}
          disabled={isLoading}
          className="flex items-center gap-2"
          data-testid="save-speaker-changes-button"
        >
          <FiSave className="w-4 h-4" />
          Save Changes
        </AdminSubmitButton>
      </div>

      {/* File Upload Warning */}
      {(newFiles.presentationFile || newFiles.headshot) && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <FiAlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800 mb-1">Files Ready to Upload</h4>
              <p className="text-sm text-yellow-700">
                You have selected new files. Click "Save Changes" to upload them and update the speaker profile.
              </p>
              {newFiles.presentationFile && (
                <p className="text-sm text-yellow-700 mt-1">
                  • New presentation file: {newFiles.presentationFile.name}
                </p>
              )}
              {newFiles.headshot && (
                <p className="text-sm text-yellow-700 mt-1">
                  • New headshot: {newFiles.headshot.name}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
