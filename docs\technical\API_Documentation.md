# IEPA Conference Registration System - API Documentation

## Overview

This document provides comprehensive API documentation for the IEPA Conference Registration System. The system uses RESTful APIs built with Next.js API routes and integrates with Supabase for database operations.

## Base URL

- **Production**: `https://reg.iepa.com/api`
- **Development**: `http://localhost:6969/api`

## Authentication

All API endpoints require authentication via Supabase JWT tokens. Include the authorization header:

```
Authorization: Bearer <supabase_jwt_token>
```

## API Endpoints

### Registration Endpoints

#### POST /api/registration/attendee
Create a new attendee registration.

**Request Body:**
```json
{
  "firstName": "string",
  "lastName": "string",
  "email": "string",
  "phone": "string",
  "organization": "string",
  "registrationType": "iepa-member|non-iepa-member|day-use-iepa|day-use-non-iepa|federal-state|cca",
  "golfTournament": boolean,
  "golfClubRental": boolean,
  "meals": {
    "tuesday_lunch": boolean,
    "tuesday_dinner": boolean,
    "wednesday_breakfast": boolean,
    "wednesday_lunch": boolean,
    "wednesday_dinner": boolean
  },
  "emergencyContactName": "string",
  "emergencyContactPhone": "string"
}
```

**Response:**
```json
{
  "success": true,
  "registrationId": "uuid",
  "totalAmount": number,
  "paymentRequired": boolean
}
```

#### POST /api/registration/speaker
Create a new speaker registration.

**Request Body:**
```json
{
  "firstName": "string",
  "lastName": "string",
  "email": "string",
  "phone": "string",
  "organization": "string",
  "speakerType": "comped|paid",
  "presentationTitle": "string",
  "presentationDescription": "string",
  "bioInformation": "string",
  "golfTournament": boolean,
  "golfClubRental": boolean
}
```

#### POST /api/registration/sponsor
Create a new sponsor registration.

**Request Body:**
```json
{
  "companyName": "string",
  "contactFirstName": "string",
  "contactLastName": "string",
  "contactEmail": "string",
  "contactPhone": "string",
  "sponsorshipLevel": "platinum|gold|silver|bronze",
  "attendeeCount": number,
  "specialRequests": "string"
}
```

### Payment Endpoints

#### POST /api/payment/create-checkout-session
Create a Stripe checkout session for payment processing.

**Request Body:**
```json
{
  "registrationId": "uuid",
  "registrationType": "attendee|speaker|sponsor",
  "amount": number,
  "currency": "usd"
}
```

**Response:**
```json
{
  "success": true,
  "checkoutUrl": "string",
  "sessionId": "string"
}
```

#### POST /api/payment/webhook
Stripe webhook endpoint for payment status updates.

### User Management Endpoints

#### GET /api/user/profile
Get current user profile information.

**Response:**
```json
{
  "userId": "uuid",
  "email": "string",
  "firstName": "string",
  "lastName": "string",
  "organization": "string",
  "phone": "string",
  "createdAt": "timestamp"
}
```

#### PUT /api/user/profile
Update user profile information.

#### GET /api/user/registrations
Get all registrations for the current user.

**Response:**
```json
{
  "attendeeRegistrations": [],
  "speakerRegistrations": [],
  "sponsorRegistrations": []
}
```

### Admin Endpoints

#### GET /api/admin/registrations
Get all registrations (admin only).

**Query Parameters:**
- `type`: Filter by registration type
- `status`: Filter by payment status
- `limit`: Number of results per page
- `offset`: Pagination offset

#### PUT /api/admin/registration/{id}
Update registration details (admin only).

#### GET /api/admin/analytics
Get registration and payment analytics (admin only).

**Response:**
```json
{
  "totalRegistrations": number,
  "totalRevenue": number,
  "registrationsByType": {},
  "paymentsByStatus": {},
  "dailyRegistrations": []
}
```

## Error Handling

All API endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {}
  }
}
```

### Common Error Codes

- `AUTHENTICATION_REQUIRED`: User not authenticated
- `AUTHORIZATION_FAILED`: Insufficient permissions
- `VALIDATION_ERROR`: Request validation failed
- `REGISTRATION_EXISTS`: User already has registration
- `PAYMENT_FAILED`: Payment processing failed
- `INTERNAL_ERROR`: Server error

## Rate Limiting

API endpoints are rate limited to prevent abuse:
- **Registration endpoints**: 5 requests per minute per user
- **Payment endpoints**: 3 requests per minute per user
- **Admin endpoints**: 100 requests per minute per admin

## Data Validation

All API endpoints include comprehensive data validation:
- Email format validation
- Phone number format validation
- Required field validation
- Data type validation
- Business rule validation

## Security Considerations

- All endpoints require HTTPS in production
- JWT tokens expire after 1 hour
- Sensitive data is encrypted at rest
- Input sanitization prevents injection attacks
- Rate limiting prevents abuse

---

**Last Updated**: December 2024  
**API Version**: v1.0  
**Documentation Version**: 1.0
