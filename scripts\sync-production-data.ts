#!/usr/bin/env tsx

/**
 * IEPA Conference Registration - Production Data Sync
 * 
 * This script syncs production data to your local Supabase development environment.
 * It safely copies data from production while preserving local development settings.
 * 
 * Usage:
 *   npm run sync-production-data [options]
 * 
 * Options:
 *   --tables <table1,table2>  Sync specific tables (default: all)
 *   --dry-run                 Show what would be synced without making changes
 *   --clear-local             Clear local data before sync (default: false)
 *   --help                    Show this help message
 */

import { createClient } from '@supabase/supabase-js';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

// Production Supabase credentials
const PRODUCTION_CONFIG = {
  url: 'https://uffhyhpcuedjsisczocy.supabase.co',
  serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVmZmh5aHBjdWVkanNpc2N6b2N5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NDkxNywiZXhwIjoyMDY0MTQwOTE3fQ.bBd3qf8TicnfVhCKjC4aYNbe9xLQI7tEyIjWME43MQA'
};

// Local Supabase credentials
const LOCAL_CONFIG = {
  url: 'http://127.0.0.1:54321',
  serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'
};

// Tables to sync (in dependency order)
const IEPA_TABLES = [
  'iepa_user_profiles',
  'iepa_organizations', 
  'iepa_historical_registrations',
  'iepa_attendee_registrations',
  'iepa_speaker_registrations',
  'iepa_sponsor_registrations',
  'iepa_golf_registrations',
  'iepa_payments',
  'iepa_email_log'
];

interface SyncOptions {
  tables?: string[];
  dryRun?: boolean;
  clearLocal?: boolean;
}

interface SyncResult {
  table: string;
  productionCount: number;
  localCount: number;
  synced: number;
  errors: string[];
}

// Create Supabase clients
const productionClient = createClient(PRODUCTION_CONFIG.url, PRODUCTION_CONFIG.serviceRoleKey);
const localClient = createClient(LOCAL_CONFIG.url, LOCAL_CONFIG.serviceRoleKey);

// Utility functions
const log = (message: string) => console.log(`[INFO] ${message}`);
const warn = (message: string) => console.log(`[WARN] ${message}`);
const error = (message: string) => console.log(`[ERROR] ${message}`);
const success = (message: string) => console.log(`[SUCCESS] ${message}`);

// Check if local Supabase is running
const checkLocalSupabase = async (): Promise<boolean> => {
  try {
    const { data, error } = await localClient.from('iepa_user_profiles').select('count', { count: 'exact', head: true });
    return !error;
  } catch (err) {
    return false;
  }
};

// Get table row count
const getTableCount = async (client: any, table: string): Promise<number> => {
  try {
    const { count, error } = await client.from(table).select('*', { count: 'exact', head: true });
    if (error) throw error;
    return count || 0;
  } catch (err) {
    return 0;
  }
};

// Sync a single table
const syncTable = async (tableName: string, options: SyncOptions): Promise<SyncResult> => {
  const result: SyncResult = {
    table: tableName,
    productionCount: 0,
    localCount: 0,
    synced: 0,
    errors: []
  };

  try {
    log(`Syncing table: ${tableName}`);

    // Get production data count
    result.productionCount = await getTableCount(productionClient, tableName);
    log(`Production ${tableName}: ${result.productionCount} rows`);

    if (result.productionCount === 0) {
      warn(`No data in production table ${tableName}, skipping`);
      return result;
    }

    // Get current local count
    result.localCount = await getTableCount(localClient, tableName);
    log(`Local ${tableName}: ${result.localCount} rows`);

    if (options.dryRun) {
      log(`[DRY RUN] Would sync ${result.productionCount} rows to ${tableName}`);
      return result;
    }

    // Clear local data if requested
    if (options.clearLocal && result.localCount > 0) {
      log(`Clearing local ${tableName} data...`);
      const { error: deleteError } = await localClient.from(tableName).delete().neq('id', '00000000-0000-0000-0000-000000000000');
      if (deleteError) {
        result.errors.push(`Failed to clear local data: ${deleteError.message}`);
        return result;
      }
    }

    // Fetch production data in batches
    const batchSize = 1000;
    let offset = 0;
    let totalSynced = 0;

    while (offset < result.productionCount) {
      log(`Fetching batch ${Math.floor(offset / batchSize) + 1}...`);
      
      const { data: productionData, error: fetchError } = await productionClient
        .from(tableName)
        .select('*')
        .range(offset, offset + batchSize - 1);

      if (fetchError) {
        result.errors.push(`Failed to fetch production data: ${fetchError.message}`);
        break;
      }

      if (!productionData || productionData.length === 0) {
        break;
      }

      // Insert data into local database
      const { error: insertError } = await localClient
        .from(tableName)
        .upsert(productionData, { onConflict: 'id' });

      if (insertError) {
        result.errors.push(`Failed to insert batch: ${insertError.message}`);
        break;
      }

      totalSynced += productionData.length;
      offset += batchSize;
      
      log(`Synced ${totalSynced}/${result.productionCount} rows`);
    }

    result.synced = totalSynced;
    success(`Completed syncing ${tableName}: ${result.synced} rows`);

  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    result.errors.push(errorMessage);
    error(`Failed to sync ${tableName}: ${errorMessage}`);
  }

  return result;
};

// Main sync function
const syncProductionData = async (options: SyncOptions): Promise<void> => {
  console.log('🔄 IEPA Production Data Sync');
  console.log('============================');
  console.log('');

  // Check if local Supabase is running
  log('Checking local Supabase connection...');
  const isLocalRunning = await checkLocalSupabase();
  if (!isLocalRunning) {
    error('Local Supabase is not running or not accessible');
    error('Please run: supabase start');
    process.exit(1);
  }
  success('Local Supabase is running');

  // Determine which tables to sync
  const tablesToSync = options.tables || IEPA_TABLES;
  log(`Tables to sync: ${tablesToSync.join(', ')}`);

  if (options.dryRun) {
    warn('DRY RUN MODE - No changes will be made');
  }

  // Sync each table
  const results: SyncResult[] = [];
  for (const table of tablesToSync) {
    const result = await syncTable(table, options);
    results.push(result);
  }

  // Generate summary report
  console.log('');
  console.log('📊 Sync Summary');
  console.log('================');
  
  let totalProductionRows = 0;
  let totalSyncedRows = 0;
  let totalErrors = 0;

  results.forEach(result => {
    totalProductionRows += result.productionCount;
    totalSyncedRows += result.synced;
    totalErrors += result.errors.length;
    
    const status = result.errors.length > 0 ? '❌' : '✅';
    console.log(`${status} ${result.table}: ${result.synced}/${result.productionCount} rows (${result.errors.length} errors)`);
    
    if (result.errors.length > 0) {
      result.errors.forEach(err => console.log(`   Error: ${err}`));
    }
  });

  console.log('');
  console.log(`Total Production Rows: ${totalProductionRows}`);
  console.log(`Total Synced Rows: ${totalSyncedRows}`);
  console.log(`Total Errors: ${totalErrors}`);

  // Save detailed report
  const reportDir = '.docs/sync-reports';
  if (!existsSync(reportDir)) {
    mkdirSync(reportDir, { recursive: true });
  }

  const reportFile = join(reportDir, `sync-report-${new Date().toISOString().split('T')[0]}.json`);
  writeFileSync(reportFile, JSON.stringify({
    timestamp: new Date().toISOString(),
    options,
    results,
    summary: {
      totalProductionRows,
      totalSyncedRows,
      totalErrors
    }
  }, null, 2));

  log(`Detailed report saved to: ${reportFile}`);

  if (totalErrors === 0) {
    success('🎉 Data sync completed successfully!');
  } else {
    warn(`⚠️ Data sync completed with ${totalErrors} errors`);
  }
};

// CLI interface
const showHelp = () => {
  console.log(`
IEPA Production Data Sync

This script syncs production data to your local Supabase development environment.

Usage:
  npm run sync-production-data [options]

Options:
  --tables <table1,table2>  Sync specific tables (default: all IEPA tables)
  --dry-run                 Show what would be synced without making changes
  --clear-local             Clear local data before sync (default: false)
  --help                    Show this help message

Examples:
  npm run sync-production-data --dry-run
  npm run sync-production-data --tables iepa_user_profiles,iepa_attendee_registrations
  npm run sync-production-data --clear-local

Available tables:
  ${IEPA_TABLES.join(', ')}

Note: Make sure your local Supabase is running (supabase start) before running this script.
`);
};

// Parse command line arguments
const parseArgs = (): SyncOptions => {
  const args = process.argv.slice(2);
  const options: SyncOptions = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--help':
        showHelp();
        process.exit(0);
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--clear-local':
        options.clearLocal = true;
        break;
      case '--tables':
        if (i + 1 < args.length) {
          options.tables = args[i + 1].split(',').map(t => t.trim());
          i++; // Skip next argument
        }
        break;
    }
  }

  return options;
};

// Main execution
if (require.main === module) {
  const options = parseArgs();

  syncProductionData(options).catch(err => {
    error(`Sync failed: ${err.message}`);
    process.exit(1);
  });
}
