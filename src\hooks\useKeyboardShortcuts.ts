'use client';

import { useEffect, useCallback } from 'react';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  metaKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  action: () => void;
  description: string;
  preventDefault?: boolean;
}

interface UseKeyboardShortcutsProps {
  shortcuts: KeyboardShortcut[];
  enabled?: boolean;
}

export function useKeyboardShortcuts({ shortcuts, enabled = true }: UseKeyboardShortcutsProps) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Don't trigger shortcuts when user is typing in input fields
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    ) {
      return;
    }

    for (const shortcut of shortcuts) {
      const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase();
      const ctrlMatches = !!shortcut.ctrlKey === event.ctrlKey;
      const metaMatches = !!shortcut.metaKey === event.metaKey;
      const shiftMatches = !!shortcut.shiftKey === event.shiftKey;
      const altMatches = !!shortcut.altKey === event.altKey;

      if (keyMatches && ctrlMatches && metaMatches && shiftMatches && altMatches) {
        if (shortcut.preventDefault !== false) {
          event.preventDefault();
        }
        shortcut.action();
        break;
      }
    }
  }, [shortcuts, enabled]);

  useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [handleKeyDown, enabled]);
}

// Predefined shortcuts for email center
export function useEmailCenterShortcuts({
  onRefresh,
  onExport,
  onSearch,
  onClearFilters,
  onToggleHelp,
  enabled = true,
}: {
  onRefresh?: () => void;
  onExport?: () => void;
  onSearch?: () => void;
  onClearFilters?: () => void;
  onToggleHelp?: () => void;
  enabled?: boolean;
}) {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'r',
      action: () => onRefresh?.(),
      description: 'Refresh email logs',
    },
    {
      key: 'e',
      action: () => onExport?.(),
      description: 'Export email logs',
    },
    {
      key: 'f',
      ctrlKey: true,
      action: () => onSearch?.(),
      description: 'Focus search field',
    },
    {
      key: '/',
      action: () => onSearch?.(),
      description: 'Focus search field',
    },
    {
      key: 'c',
      action: () => onClearFilters?.(),
      description: 'Clear all filters',
    },
    {
      key: '?',
      action: () => onToggleHelp?.(),
      description: 'Show keyboard shortcuts',
    },
    {
      key: 'h',
      action: () => onToggleHelp?.(),
      description: 'Show keyboard shortcuts',
    },
  ];

  useKeyboardShortcuts({ shortcuts, enabled });

  return shortcuts;
}

// Utility function to format keyboard shortcuts for display
export const formatKeyboardShortcut = (shortcut: KeyboardShortcut): string => {
  const parts: string[] = [];

  if (shortcut.ctrlKey) parts.push('Ctrl');
  if (shortcut.metaKey) parts.push('⌘');
  if (shortcut.altKey) parts.push('Alt');
  if (shortcut.shiftKey) parts.push('Shift');

  parts.push(shortcut.key.toUpperCase());

  return parts.join(' + ');
};
