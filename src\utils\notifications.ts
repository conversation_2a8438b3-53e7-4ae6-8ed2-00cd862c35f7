// DOM-based notification utilities for IEPA Conference Registration
// Replaces alert() popups with professional, accessible notifications

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface NotificationOptions {
  title: string;
  message: string;
  type: NotificationType;
  duration?: number; // in milliseconds, default 5000
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  dismissible?: boolean;
}

/**
 * Show a DOM-based notification
 */
export const showNotification = (options: NotificationOptions): void => {
  const {
    title,
    message,
    type,
    duration = 5000,
    position = 'top-right',
    dismissible = true,
  } = options;

  // Create notification element
  const notification = document.createElement('div');
  notification.className = getNotificationClasses(type, position);
  notification.setAttribute('role', 'alert');
  notification.setAttribute('aria-live', 'assertive');
  notification.setAttribute('data-testid', `notification-${type}`);

  // Create notification content
  const icon = getNotificationIcon(type);
  const dismissButton = dismissible
    ? `<button class="ml-auto text-white hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 rounded" aria-label="Dismiss notification">✕</button>`
    : '';

  notification.innerHTML = `
    <div class="flex items-start">
      <div class="mr-3 text-lg">${icon}</div>
      <div class="flex-1">
        <div class="font-bold">${escapeHtml(title)}</div>
        <div class="text-sm mt-1">${escapeHtml(message)}</div>
      </div>
      ${dismissButton}
    </div>
  `;

  // Add dismiss functionality
  if (dismissible) {
    const dismissBtn = notification.querySelector('button');
    if (dismissBtn) {
      dismissBtn.addEventListener('click', () => {
        removeNotification(notification);
      });
    }
  }

  // Add to DOM
  document.body.appendChild(notification);

  // Auto-remove after duration
  if (duration > 0) {
    setTimeout(() => {
      removeNotification(notification);
    }, duration);
  }

  // Announce to screen readers
  announceToScreenReader(`${title}: ${message}`);
};

/**
 * Remove notification from DOM
 */
const removeNotification = (notification: HTMLElement): void => {
  if (notification.parentNode) {
    // Add fade-out animation
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(100%)';

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }
};

/**
 * Get CSS classes for notification type and position
 */
const getNotificationClasses = (
  type: NotificationType,
  position: string
): string => {
  const baseClasses =
    'fixed p-4 rounded-lg shadow-lg z-50 max-w-md transition-all duration-300 ease-in-out';

  const typeClasses = {
    success: 'bg-green-500 text-white',
    error: 'bg-red-500 text-white',
    warning: 'bg-yellow-500 text-black',
    info: 'bg-blue-500 text-white',
  };

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
  };

  return `${baseClasses} ${typeClasses[type]} ${positionClasses[position as keyof typeof positionClasses]}`;
};

/**
 * Get icon for notification type
 */
const getNotificationIcon = (type: NotificationType): string => {
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️',
  };

  return icons[type];
};

/**
 * Escape HTML to prevent XSS
 */
const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

/**
 * Announce to screen readers
 */
const announceToScreenReader = (message: string): void => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', 'assertive');
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className =
    'sr-only absolute -top-10 -left-10 w-1 h-1 overflow-hidden';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  // Remove after announcement
  setTimeout(() => {
    if (announcement.parentNode) {
      announcement.parentNode.removeChild(announcement);
    }
  }, 1000);
};

/**
 * Convenience functions for common notification types
 */
export const showSuccess = (
  title: string,
  message: string,
  duration?: number
): void => {
  showNotification({ title, message, type: 'success', duration });
};

export const showError = (
  title: string,
  message: string,
  duration?: number
): void => {
  showNotification({
    title,
    message,
    type: 'error',
    duration: duration || 7000,
  });
};

export const showWarning = (
  title: string,
  message: string,
  duration?: number
): void => {
  showNotification({ title, message, type: 'warning', duration });
};

export const showInfo = (
  title: string,
  message: string,
  duration?: number
): void => {
  showNotification({ title, message, type: 'info', duration });
};

/**
 * Clear all notifications
 */
export const clearAllNotifications = (): void => {
  const notifications = document.querySelectorAll(
    '[data-testid^="notification-"]'
  );
  notifications.forEach(notification => {
    removeNotification(notification as HTMLElement);
  });
};

/**
 * Test notification system
 */
export const testNotifications = (): void => {
  showSuccess('Success Test', 'This is a success notification test');

  setTimeout(() => {
    showError('Error Test', 'This is an error notification test');
  }, 1000);

  setTimeout(() => {
    showWarning('Warning Test', 'This is a warning notification test');
  }, 2000);

  setTimeout(() => {
    showInfo('Info Test', 'This is an info notification test');
  }, 3000);
};
