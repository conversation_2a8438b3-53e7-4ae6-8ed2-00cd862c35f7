#!/bin/bash

# IEPA Conference Registration - Development Webhook Setup
# This script sets up Stripe webhooks for local development using Stripe CLI

set -e

echo "🚀 IEPA Conference Registration - Development Webhook Setup"
echo "=========================================================="
echo ""

# Check if Stripe CLI is installed
if ! command -v stripe &> /dev/null; then
    echo "❌ Stripe CLI is not installed."
    echo ""
    echo "📥 Install Stripe CLI:"
    echo "   macOS: brew install stripe/stripe-cli/stripe"
    echo "   Linux: https://github.com/stripe/stripe-cli#installation"
    echo "   Windows: https://github.com/stripe/stripe-cli#installation"
    echo ""
    exit 1
fi

echo "✅ Stripe CLI is installed"

# Check if user is logged in to Stripe CLI
if ! stripe config --list &> /dev/null; then
    echo "🔐 Please login to Stripe CLI first:"
    echo "   stripe login"
    echo ""
    exit 1
fi

echo "✅ Stripe CLI is authenticated"

# Load environment variables
if [ -f ".env.local" ]; then
    export $(grep -v '^#' .env.local | xargs)
else
    echo "❌ .env.local file not found"
    exit 1
fi

# Check if we have the required environment variables
if [ -z "$STRIPE_SECRET_KEY" ]; then
    echo "❌ STRIPE_SECRET_KEY not found in .env.local"
    exit 1
fi

echo "✅ Environment variables loaded"

# Determine the port to use (default to 3000)
PORT=${1:-3000}
WEBHOOK_URL="http://localhost:$PORT/api/stripe/webhook"

echo ""
echo "🔧 Setting up development webhook forwarding..."
echo "   Local URL: $WEBHOOK_URL"
echo "   Events: checkout.session.completed, payment_intent.succeeded, payment_intent.payment_failed"
echo ""

# Create a webhook forwarding session
echo "🎯 Starting Stripe webhook forwarding..."
echo "   This will forward Stripe webhook events to your local development server."
echo "   Keep this terminal open while developing."
echo ""
echo "📋 To test webhooks manually:"
echo "   stripe trigger checkout.session.completed"
echo "   stripe trigger payment_intent.succeeded"
echo "   stripe trigger payment_intent.payment_failed"
echo ""
echo "🛑 Press Ctrl+C to stop webhook forwarding"
echo ""

# Start the webhook forwarding
stripe listen \
  --forward-to "$WEBHOOK_URL" \
  --events checkout.session.completed,payment_intent.succeeded,payment_intent.payment_failed \
  --print-secret
