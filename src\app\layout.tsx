import type { Metada<PERSON> } from 'next';
import { <PERSON>ei<PERSON>, <PERSON><PERSON>st_Mono } from 'next/font/google';
import './globals.css';
import '@/styles/iepa-brand.css';
import '@/styles/parallax-footer.css';
import { Providers } from './providers';
import { ConditionalLayout } from '@/components/layout/ConditionalLayout';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'IEPA 2025 Annual Meeting Registration',
  description:
    'Register for the Independent Energy Producers Association (IEPA) 2025 Annual Meeting - Attendees, Speakers, and Sponsors',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <Providers>
          <ConditionalLayout>{children}</ConditionalLayout>
        </Providers>
      </body>
    </html>
  );
}
