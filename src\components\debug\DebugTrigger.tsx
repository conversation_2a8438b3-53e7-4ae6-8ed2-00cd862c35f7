'use client';

import React, { useState, useEffect } from 'react';
import { DebugModal } from './DebugModal';
import { getDebugStats, debugLog } from '@/lib/debug-logger';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { FaBug, FaExclamationCircle } from 'react-icons/fa';

interface DebugTriggerProps {
  className?: string;
  showOnlyInDevelopment?: boolean;
}

export function DebugTrigger({
  className,
  showOnlyInDevelopment = true,
}: DebugTriggerProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [stats, setStats] = useState(getDebugStats());
  const [hasErrors, setHasErrors] = useState(false);

  // Update stats periodically
  useEffect(() => {
    const updateStats = () => {
      const newStats = getDebugStats();
      setStats(newStats);
      setHasErrors((newStats.byLevel.error || 0) > 0);
    };

    updateStats();
    const interval = setInterval(updateStats, 3000);
    return () => clearInterval(interval);
  }, []);

  // Log when debug trigger is mounted
  useEffect(() => {
    debugLog.info('general', 'Debug trigger mounted', {
      page:
        typeof window !== 'undefined' ? window.location.pathname : 'unknown',
      showOnlyInDevelopment,
    });
  }, [showOnlyInDevelopment]);

  // Don't show in production if showOnlyInDevelopment is true
  if (showOnlyInDevelopment && process.env.NODE_ENV === 'production') {
    return null;
  }

  const handleClick = () => {
    debugLog.info('general', 'Debug modal opened');
    setIsModalOpen(true);
  };

  const handleClose = () => {
    debugLog.info('general', 'Debug modal closed');
    setIsModalOpen(false);
  };

  return (
    <>
      <button
        onClick={handleClick}
        className={cn(
          'inline-flex items-center gap-2 px-3 py-1 rounded-md text-xs font-medium transition-colors',
          'hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          hasErrors
            ? 'bg-red-50 text-red-700 border border-red-200'
            : 'bg-gray-50 text-gray-600 border border-gray-200',
          className
        )}
        title={`Debug Console - ${stats.total} logs${hasErrors ? ' (has errors)' : ''}`}
      >
        <FaBug className="w-3 h-3" />
        <span>Debug</span>

        {stats.total > 0 && (
          <Badge
            variant={hasErrors ? 'destructive' : 'secondary'}
            className="text-xs px-1 py-0 h-4 min-w-[16px]"
          >
            {stats.total}
          </Badge>
        )}

        {hasErrors && <FaExclamationCircle className="w-3 h-3 text-red-500" />}
      </button>

      <DebugModal isOpen={isModalOpen} onClose={handleClose} />
    </>
  );
}
