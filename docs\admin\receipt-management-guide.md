# IEPA Admin Receipt Management Guide

## Overview

The IEPA Annual Meeting registration system provides comprehensive receipt management functionality for administrators. This guide covers all aspects of generating, managing, and troubleshooting receipts for completed registrations.

## Key Features

### ✅ **Receipt-Only System**

- **Simplified Approach**: The system uses receipts for all registration types (attendees, speakers, sponsors)
- **No Invoices**: Invoice functionality has been removed for a cleaner, more consistent experience
- **Universal Coverage**: All completed registrations receive receipts regardless of payment method

### 🔄 **Force Regenerate Functionality**

- **Smart Regeneration**: Ability to force regenerate existing receipts when needed
- **Loading States**: Visual feedback during regeneration process
- **Timestamp Updates**: New receipts get current generation timestamps

## Receipt Management Interface

### Navigation

Access receipt management at: **Admin Dashboard → Receipts**

- URL: `https://reg.iepa.com/admin/receipts`
- Requires admin authentication

### Dashboard Statistics

The receipt management page displays:

- **Total Eligible**: Number of registrations eligible for receipts
- **With Receipts**: Count of registrations that have generated receipts
- **Pending Receipts**: Count of registrations missing receipts
- **Total Paid**: Sum of all paid registration amounts

### Receipt Actions

#### For Existing Receipts (3 buttons):

1. **📥 Download Receipt** - Download the existing PDF receipt
2. **📧 Send Receipt via Email** - Email the receipt to the registrant
3. **🔄 Force Regenerate Receipt** - Create a new receipt, overwriting the existing one

#### For Missing Receipts (1 button):

1. **📄 Generate** - Create a new receipt for the registration

### Loading States

- Buttons show spinning animation during processing
- Buttons are disabled while operations are in progress
- Page refreshes automatically after successful operations

## When to Use Force Regenerate

### ✅ **Recommended Use Cases:**

- **Data Updates**: Registration details have been modified
- **Template Changes**: PDF template has been updated
- **Error Correction**: Receipt contains incorrect information
- **Timestamp Refresh**: Need current date/time on receipt
- **Troubleshooting**: Resolving receipt generation issues

### ⚠️ **Considerations:**

- Force regeneration creates a completely new PDF
- Previous receipt file is overwritten in storage
- Generation timestamp is updated to current time
- Operation cannot be undone

## Technical Implementation

### API Endpoint

- **URL**: `/api/pdf/generate-receipt`
- **Method**: POST
- **Parameters**:
  - `registrationId`: UUID of the registration
  - `registrationType`: 'attendee', 'speaker', or 'sponsor'
  - `forceRegenerate`: Boolean flag for force regeneration

### Database Updates

- Receipt URL stored in registration table
- Generation timestamp updated on each creation
- File stored in Supabase storage under `receipts/` folder

### File Naming Convention

- Format: `receipt-{type}-{shortId}-{date}.pdf`
- Example: `receipt-attendee-B663032A-2025-06-24.pdf`

## Filtering and Search

### Available Filters

- **Search**: Filter by name, email, or organization
- **Registration Type**: Filter by attendee, speaker, or sponsor
- **Receipt Status**: Filter by has receipt or no receipt
- **Payment Status**: Filter by payment completion status

### Bulk Operations

- **Bulk Generate**: Generate receipts for all registrations missing them
- **Refresh**: Reload the receipt data from database

## Troubleshooting

### Common Issues

#### Receipt Generation Fails

1. Check registration data completeness
2. Verify Supabase storage permissions
3. Review server logs for PDF generation errors
4. Try force regeneration if receipt exists

#### Missing Receipts

1. Verify payment status is 'completed'
2. Check if registration type is eligible
3. Use bulk generate for multiple missing receipts
4. Review email logs for delivery issues

#### Download Issues

1. Verify receipt URL exists in database
2. Check Supabase storage file existence
3. Confirm signed URL generation is working
4. Try force regeneration to create new file

### Error Messages

- **"Registration not found"**: Invalid registration ID
- **"Failed to generate PDF"**: PDF creation error
- **"Storage error"**: Supabase file storage issue
- **"Database error"**: Registration data access problem

## Best Practices

### Regular Maintenance

- Monitor pending receipts count
- Use bulk generate for new registrations
- Verify receipt downloads periodically
- Check email delivery logs

### Data Integrity

- Force regenerate after registration updates
- Verify receipt data matches registration details
- Maintain consistent file naming
- Regular backup of receipt files

### Performance Optimization

- Use filters to reduce data load
- Batch operations during low-traffic periods
- Monitor storage usage
- Clean up old temporary files

## System Integration

### Email System

- Receipts automatically attached to confirmation emails
- Manual email sending available from admin interface
- Email logs track delivery status
- Support contact: <EMAIL>

### Payment Processing

- Receipts generated after successful Stripe payments
- Manual generation available for comped registrations
- Payment method displayed on receipts
- Transaction IDs included when available

### File Storage

- All receipts stored in Supabase storage
- Signed URLs for secure downloads
- Automatic file organization by type
- Retention policy follows IEPA requirements

---

**Last Updated**: June 24, 2025
**Feature Version**: v2.0 (Invoice system removed, Force regenerate added)
**Admin Access**: Super Admin and Admin roles
**Support Contact**: <EMAIL>
