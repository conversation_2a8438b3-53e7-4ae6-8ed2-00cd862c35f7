import { NextRequest, NextResponse } from 'next/server';
import { emailTemplateService } from '@/services/email-templates';

export async function GET() {
  try {
    console.log('[EMAIL-TEMPLATES-API] Getting all email templates...');

    const templates = await emailTemplateService.getAllTemplates();
    
    console.log('[EMAIL-TEMPLATES-API] Retrieved templates:', templates.length);

    return NextResponse.json({
      success: true,
      templates,
      count: templates.length,
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('[EMAIL-TEMPLATES-API] Failed to get templates:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to retrieve email templates',
      templates: [],
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('[EMAIL-TEMPLATES-API] Creating new email template...');

    const body = await request.json();
    const { template, changedBy } = body;

    if (!template || typeof template !== 'object') {
      return NextResponse.json({
        success: false,
        error: 'Invalid template data'
      }, { status: 400 });
    }

    if (!changedBy) {
      return NextResponse.json({
        success: false,
        error: 'changedBy is required'
      }, { status: 400 });
    }

    // Validate required fields
    const requiredFields = ['template_key', 'template_name', 'subject_template', 'html_template'];
    for (const field of requiredFields) {
      if (!template[field] || typeof template[field] !== 'string' || !template[field].trim()) {
        return NextResponse.json({
          success: false,
          error: `${field} is required and cannot be empty`
        }, { status: 400 });
      }
    }

    const newTemplate = await emailTemplateService.createTemplate(template, changedBy);

    console.log('[EMAIL-TEMPLATES-API] Template created successfully:', newTemplate.id);

    return NextResponse.json({
      success: true,
      message: 'Email template created successfully',
      template: newTemplate,
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('[EMAIL-TEMPLATES-API] Failed to create template:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create email template',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
