'use client';

import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FaExclamationTriangle } from 'react-icons/fa';
import { CANCELLATION_POLICY } from '@/lib/pricing-config';

interface CancellationPolicyProps {
  className?: string;
}

export function CancellationPolicy({ className = '' }: CancellationPolicyProps) {
  return (
    <Alert 
      className={`
        border-red-200 bg-red-50 text-red-800 
        iepa-compact-padding
        ${className}
      `}
      role="alert"
      aria-label="Cancellation Policy"
    >
      <FaExclamationTriangle className="h-4 w-4 text-red-600" />
      <AlertDescription className="text-sm leading-relaxed">
        <strong className="font-semibold">Cancellation Policy:</strong>{' '}
        {CANCELLATION_POLICY.details}
      </AlertDescription>
    </Alert>
  );
}

export default CancellationPolicy;
