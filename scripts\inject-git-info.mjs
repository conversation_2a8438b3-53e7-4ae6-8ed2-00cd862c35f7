#!/usr/bin/env node

/**
 * <PERSON>ript to inject git information into environment variables for build
 * This runs before the build to capture git commit and branch information
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

function getGitInfo() {
  try {
    const gitCommit = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
    const gitBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
    const gitCommitShort = gitCommit.substring(0, 7);
    
    return {
      NEXT_PUBLIC_GIT_COMMIT: gitCommit,
      NEXT_PUBLIC_GIT_COMMIT_SHORT: gitCommitShort,
      NEXT_PUBLIC_GIT_BRANCH: gitBranch,
      NEXT_PUBLIC_BUILD_TIME: new Date().toISOString(),
    };
  } catch (error) {
    console.warn('Warning: Could not get git information:', error.message);
    return {
      NEXT_PUBLIC_BUILD_TIME: new Date().toISOString(),
    };
  }
}

function updateEnvFile() {
  const gitInfo = getGitInfo();
  const envPath = path.join(process.cwd(), '.env.local');
  
  let envContent = '';
  
  // Read existing .env.local if it exists
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }
  
  // Remove existing git info lines
  const linesToRemove = [
    'NEXT_PUBLIC_GIT_COMMIT=',
    'NEXT_PUBLIC_GIT_COMMIT_SHORT=',
    'NEXT_PUBLIC_GIT_BRANCH=',
    'NEXT_PUBLIC_BUILD_TIME=',
  ];
  
  const lines = envContent.split('\n').filter(line => {
    return !linesToRemove.some(prefix => line.startsWith(prefix));
  });
  
  // Add new git info
  Object.entries(gitInfo).forEach(([key, value]) => {
    lines.push(`${key}=${value}`);
  });
  
  // Write back to file
  fs.writeFileSync(envPath, lines.filter(line => line.trim()).join('\n') + '\n');
  
  console.log('✅ Git information injected into .env.local:');
  Object.entries(gitInfo).forEach(([key, value]) => {
    console.log(`   ${key}=${value}`);
  });
}

// Run the script
updateEnvFile();

export { getGitInfo, updateEnvFile };
