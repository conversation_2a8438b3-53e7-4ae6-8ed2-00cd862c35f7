'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User,
  Session,
  AuthError,
  AuthChangeEvent,
} from '@supabase/supabase-js';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (
    email: string,
    password: string
  ) => Promise<{
    data: unknown;
    error: AuthError | null;
  }>;
  signIn: (
    email: string,
    password: string
  ) => Promise<{
    data: unknown;
    error: AuthError | null;
  }>;
  signInWithMagicLink: (
    email: string,
    redirectTo?: string
  ) => Promise<{
    data: unknown;
    error: AuthError | null;
  }>;
  signOut: () => Promise<void>;
  resetPassword: (
    email: string
  ) => Promise<{ data: unknown; error: AuthError | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  // Test mode bypass for admin testing
  const isTestMode =
    process.env.NODE_ENV === 'development' &&
    typeof window !== 'undefined' &&
    window.location.search.includes('testAdmin=true');

  useEffect(() => {
    let mounted = true;
    let subscription: { unsubscribe: () => void } | null = null;

    // Test mode bypass
    if (isTestMode) {
      const mockUser = {
        id: 'test-admin-user-id',
        email: '<EMAIL>',
        aud: 'authenticated',
        role: 'authenticated',
        email_confirmed_at: new Date().toISOString(),
        phone: '',
        confirmed_at: new Date().toISOString(),
        last_sign_in_at: new Date().toISOString(),
        app_metadata: {},
        user_metadata: {},
        identities: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      } as User;

      const mockSession = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
        expires_at: Date.now() / 1000 + 3600,
        token_type: 'bearer',
        user: mockUser,
      } as Session;

      setUser(mockUser);
      setSession(mockSession);
      setLoading(false);
      return;
    }

    // Initialize Supabase client and auth
    const initializeAuth = async () => {
      try {
        if (!mounted) return;

        // Dynamically import Supabase client
        const { supabase } = await import('@/lib/supabase');

        // Get initial session
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        // Handle refresh token errors
        if (sessionError) {
          console.warn('Auth session error:', sessionError.message);
          if (
            sessionError.message.includes('Invalid Refresh Token') ||
            sessionError.message.includes('Refresh Token Not Found')
          ) {
            console.log('🔄 Clearing corrupted session data');
            await supabase.auth.signOut();
          }
        }

        if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);
        }

        // Set up auth state listener
        const {
          data: { subscription: authSubscription },
        } = supabase.auth.onAuthStateChange(
          (event: AuthChangeEvent, session: Session | null) => {
            console.log('🔐 Auth state change:', event, session?.user?.email);

            // Handle token refresh errors
            if (event === 'TOKEN_REFRESHED' && !session) {
              console.warn('Token refresh failed, signing out');
              supabase.auth.signOut();
            }

            if (mounted) {
              setSession(session);
              setUser(session?.user ?? null);
            }
          }
        );

        subscription = authSubscription;

        if (mounted) {
          setLoading(false);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        if (mounted) {
          setSession(null);
          setUser(null);
          setLoading(false);
        }
      }
    };

    // Initialize
    initializeAuth();

    // Cleanup function
    return () => {
      mounted = false;
      if (subscription) {
        try {
          subscription.unsubscribe();
        } catch (error) {
          console.error('Error unsubscribing from auth changes:', error);
        }
      }
    };
  }, [isTestMode]);

  const signUp = async (email: string, password: string) => {
    try {
      const { supabase } = await import('@/lib/supabase');
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      return { data, error };
    } catch (error) {
      return { data: null, error: error as AuthError };
    }
  };

  const signIn = async (email: string, password: string) => {
    console.log('🔐 SignIn Debug - Starting authentication process');
    console.log('📧 Email:', email);
    console.log('🔑 Password length:', password.length);

    try {
      console.log('📦 Importing Supabase client...');
      const { supabase } = await import('@/lib/supabase');
      console.log('✅ Supabase client imported successfully');

      console.log('🚀 Attempting sign in with password...');
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      console.log('📊 SignIn Response:', {
        hasData: !!data,
        hasUser: !!data?.user,
        hasSession: !!data?.session,
        hasError: !!error,
        errorMessage: error?.message,
        errorCode: error?.status,
      });

      if (error) {
        console.error('❌ SignIn Error Details:', {
          message: error.message,
          status: error.status,
          name: error.name,
          stack: error.stack,
        });
      }

      if (data?.user) {
        console.log('👤 User authenticated:', {
          id: data.user.id,
          email: data.user.email,
          emailConfirmed: !!data.user.email_confirmed_at,
        });
      }

      return { data, error };
    } catch (error) {
      console.error('💥 SignIn Catch Block Error:', {
        error,
        errorType: typeof error,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorStack: error instanceof Error ? error.stack : 'No stack trace',
      });
      return { data: null, error: error as AuthError };
    }
  };

  const signOut = async () => {
    try {
      const { supabase } = await import('@/lib/supabase');
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      throw error as AuthError;
    }
  };

  const signInWithMagicLink = async (email: string, redirectTo?: string) => {
    console.log('🔗 Magic Link Debug - Starting magic link authentication');
    console.log('📧 Email:', email);
    console.log('🔄 Redirect to:', redirectTo);

    try {
      console.log('📦 Importing Supabase client...');
      const { supabase } = await import('@/lib/supabase');
      const { getAuthRedirectUrl } = await import('@/lib/port-utils');
      console.log('✅ Supabase client imported successfully');

      // Determine the redirect URL
      const finalRedirectTo = redirectTo || '/my-registrations';
      const authRedirectUrl = getAuthRedirectUrl(
        `/auth/callback?next=${encodeURIComponent(finalRedirectTo)}`
      );

      console.log('🚀 Sending magic link...');
      console.log('📍 Auth redirect URL:', authRedirectUrl);

      const { data, error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: authRedirectUrl,
        },
      });

      console.log('📊 Magic Link Response:', {
        hasData: !!data,
        hasError: !!error,
        errorMessage: error?.message,
        errorCode: error?.status,
      });

      if (error) {
        console.error('❌ Magic Link Error Details:', {
          message: error.message,
          status: error.status,
          name: error.name,
        });
      } else {
        console.log('✅ Magic link sent successfully');
      }

      return { data, error };
    } catch (error) {
      console.error('💥 Magic Link Catch Block Error:', {
        error,
        errorType: typeof error,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      });
      return { data: null, error: error as AuthError };
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { supabase } = await import('@/lib/supabase');
      const { getAuthRedirectUrl } = await import('@/lib/port-utils');
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: getAuthRedirectUrl('/auth/reset-password'),
      });
      return { data, error };
    } catch (error) {
      return { data: null, error: error as AuthError };
    }
  };

  const value = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signInWithMagicLink,
    signOut,
    resetPassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
