#!/usr/bin/env tsx

/**
 * IEPA 2024 Attendee Import Script
 *
 * This script imports attendee data from the 2024 conference JSON file
 * and creates user accounts with profiles and historical registration data.
 *
 * Usage:
 *   npm run import-attendees [options]
 *
 * Options:
 *   --dry-run          Run without making changes (default: false)
 *   --skip-existing    Skip users that already exist (default: true)
 *   --batch-size       Number of users to process at once (default: 10)
 *   --help             Show this help message
 */

// Load environment variables from .env.local
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables first
dotenv.config({ path: '.env.local' });

// Validate environment variables
if (
  !process.env.NEXT_PUBLIC_SUPABASE_URL ||
  !process.env.SUPABASE_SERVICE_ROLE_KEY
) {
  console.error('❌ Missing required environment variables:');
  console.error(
    '   NEXT_PUBLIC_SUPABASE_URL:',
    process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Missing'
  );
  console.error(
    '   SUPABASE_SERVICE_ROLE_KEY:',
    process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Missing'
  );
  process.exit(1);
}

// Import Supabase client directly
import { createClient } from '@supabase/supabase-js';

// Create Supabase clients
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Regular client for reading data
const supabase = createClient(
  supabaseUrl,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Admin client for creating users
const adminClient = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Types for the import data
interface ImportAttendeeData {
  'Attendee Last Name': string;
  'Attendee First Name': string;
  'Attendee Type': string;
  'Attendee Type (IEPA)': string | null;
  'Date Added': string;
  Street: string;
  City: string;
  State: string;
  Zip: number;
  'Email For Attendee List': string;
  'Attendee Job Title': string;
  'Attendee Organization': string;
  Gender: string;
  'Nights staying (lodging)': string | null;
  Meals: string;
  'Special Dietary Needs': string | null;
  Status: string;
  'Grand order total': number | null;
  'Name on Badge': string;
  'Golf Tournament?': string;
  'Are you renting clubs?': string | null;
  'Phone Number For Attendee List': string;
  'Golf Total': number | null;
  "Golfer's cell number for last minute changes, etc.": string | null;
  Country: string | null;
}

interface ProcessedAttendeeData {
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  phoneNumber: string;
  organization: string;
  jobTitle: string;
  streetAddress: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  gender: string;
  preferredNameOnBadge: string;
  // Historical registration data
  attendeeType: string;
  attendeeTypeIepa: string | null;
  registrationDate: string;
  status: string;
  nightsStaying: string | null;
  meals: string[];
  specialDietaryNeeds: string | null;
  golfTournament: boolean;
  golfClubRental: string | null;
  golfCellNumber: string | null;
  golfTotal: number;
  grandTotal: number;
  originalData: ImportAttendeeData;
}

interface ImportResult {
  success: boolean;
  totalProcessed: number;
  successfulImports: number;
  skippedExisting: number;
  errors: Array<{ email: string; error: string }>;
  generatedPasswords: Array<{ email: string; password: string }>;
}

// Utility functions
const generateSecurePassword = (): string => {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 16; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
};

const parseMeals = (mealsString: string): string[] => {
  if (!mealsString) return [];
  return mealsString
    .split(',')
    .map(meal => meal.trim())
    .filter(meal => meal.length > 0);
};

const parseGolfTournament = (golfResponse: string): boolean => {
  return golfResponse?.toLowerCase() === 'yes';
};

const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  return phone.replace(/[^\d+]/g, '').replace(/(?!^)\+/g, '');
};

const processAttendeeData = (
  rawData: ImportAttendeeData
): ProcessedAttendeeData => {
  const meals = parseMeals(rawData.Meals);
  const golfTournament = parseGolfTournament(rawData['Golf Tournament?']);
  const phoneNumber = formatPhoneNumber(
    rawData['Phone Number For Attendee List']
  );

  return {
    email: rawData['Email For Attendee List'].toLowerCase().trim(),
    firstName: rawData['Attendee First Name'].trim(),
    lastName: rawData['Attendee Last Name'].trim(),
    fullName: `${rawData['Attendee First Name'].trim()} ${rawData['Attendee Last Name'].trim()}`,
    phoneNumber,
    organization: rawData['Attendee Organization'].trim(),
    jobTitle: rawData['Attendee Job Title'].trim(),
    streetAddress: rawData.Street.trim(),
    city: rawData.City.trim(),
    state: rawData.State.trim(),
    zipCode: rawData.Zip.toString(),
    country: rawData.Country || 'United States',
    gender: rawData.Gender.trim(),
    preferredNameOnBadge: rawData['Name on Badge'].trim(),
    attendeeType: rawData['Attendee Type'].trim(),
    attendeeTypeIepa: rawData['Attendee Type (IEPA)'],
    registrationDate: rawData['Date Added'],
    status: rawData.Status.trim(),
    nightsStaying: rawData['Nights staying (lodging)'],
    meals,
    specialDietaryNeeds: rawData['Special Dietary Needs'],
    golfTournament,
    golfClubRental: rawData['Are you renting clubs?'],
    golfCellNumber:
      rawData["Golfer's cell number for last minute changes, etc."],
    golfTotal: rawData['Golf Total'] || 0,
    grandTotal: rawData['Grand order total'] || 0,
    originalData: rawData,
  };
};

// Check if user already exists by email
const checkUserExists = async (
  email: string
): Promise<{ exists: boolean; userId?: string }> => {
  try {
    const { data, error } = await adminClient
      .from('iepa_user_profiles')
      .select('user_id')
      .eq('email', email.toLowerCase())
      .single();

    if (error && error.code !== 'PGRST116') {
      // PGRST116 is "not found"
      throw error;
    }

    return {
      exists: !!data,
      userId: data?.user_id,
    };
  } catch (error) {
    console.error('Error checking user existence:', error);
    return { exists: false };
  }
};

// Create user account using admin client
const createUserAccount = async (
  email: string,
  password: string
): Promise<{ success: boolean; userId?: string; error?: string }> => {
  try {
    const { data, error } = await adminClient.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Skip email confirmation for imported users
    });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, userId: data.user?.id };
  } catch (error) {
    console.error('Error creating user account:', error);
    return { success: false, error: 'Failed to create user account' };
  }
};

// Create user profile
const createUserProfile = async (
  userId: string,
  profileData: ProcessedAttendeeData
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await adminClient.from('iepa_user_profiles').insert({
      user_id: userId,
      first_name: profileData.firstName,
      last_name: profileData.lastName,
      email: profileData.email,
      phone_number: profileData.phoneNumber,
      organization: profileData.organization,
      job_title: profileData.jobTitle,
      street_address: profileData.streetAddress,
      city: profileData.city,
      state: profileData.state,
      zip_code: profileData.zipCode,
      country: profileData.country,
      gender: profileData.gender,
      preferred_name_on_badge: profileData.preferredNameOnBadge,
      imported_from_2024: true,
      import_date: new Date().toISOString(),
    });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error creating user profile:', error);
    return { success: false, error: 'Failed to create user profile' };
  }
};

// Create historical registration record
const createHistoricalRegistration = async (
  userId: string,
  profileId: string,
  registrationData: ProcessedAttendeeData
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await adminClient
      .from('iepa_historical_registrations')
      .insert({
        user_id: userId,
        profile_id: profileId,
        event_year: 2024,
        event_name: 'IEPA Annual Meeting 2024',
        registration_date: registrationData.registrationDate,
        attendee_type: registrationData.attendeeType,
        attendee_type_iepa: registrationData.attendeeTypeIepa,
        status: registrationData.status,
        name_on_badge: registrationData.preferredNameOnBadge,
        nights_staying: registrationData.nightsStaying,
        meals: registrationData.meals,
        special_dietary_needs: registrationData.specialDietaryNeeds,
        golf_tournament: registrationData.golfTournament,
        golf_club_rental: registrationData.golfClubRental,
        golf_cell_number: registrationData.golfCellNumber,
        golf_total: registrationData.golfTotal,
        grand_total: registrationData.grandTotal,
        payment_status: 'completed',
        imported_from_source: '2024-attendee-export',
        original_data: registrationData.originalData,
      });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error creating historical registration:', error);
    return {
      success: false,
      error: 'Failed to create historical registration',
    };
  }
};

// Main import function
const importAttendeesFromJson = async (
  attendeeData: ImportAttendeeData[],
  options: {
    skipExisting?: boolean;
    dryRun?: boolean;
    batchSize?: number;
  } = {}
): Promise<ImportResult> => {
  const { skipExisting = true, dryRun = false, batchSize = 10 } = options;

  const result: ImportResult = {
    success: false,
    totalProcessed: 0,
    successfulImports: 0,
    skippedExisting: 0,
    errors: [],
    generatedPasswords: [],
  };

  try {
    console.log(`Starting import of ${attendeeData.length} attendees...`);

    // Process in batches to avoid overwhelming the database
    for (let i = 0; i < attendeeData.length; i += batchSize) {
      const batch = attendeeData.slice(i, i + batchSize);

      for (const rawAttendee of batch) {
        result.totalProcessed++;

        try {
          // Process the raw data
          const processedData = processAttendeeData(rawAttendee);

          // Check if user already exists
          const { exists, userId: existingUserId } = await checkUserExists(
            processedData.email
          );

          if (exists && skipExisting) {
            console.log(`Skipping existing user: ${processedData.email}`);
            result.skippedExisting++;
            continue;
          }

          if (dryRun) {
            console.log(`[DRY RUN] Would import: ${processedData.email}`);
            result.successfulImports++;
            continue;
          }

          let userId = existingUserId;
          let password = '';

          // Create user account if it doesn't exist
          if (!exists) {
            password = generateSecurePassword();
            const userResult = await createUserAccount(
              processedData.email,
              password
            );

            if (!userResult.success) {
              result.errors.push({
                email: processedData.email,
                error: `Failed to create user account: ${userResult.error}`,
              });
              continue;
            }

            userId = userResult.userId!;
            result.generatedPasswords.push({
              email: processedData.email,
              password,
            });
          }

          // Create user profile
          const profileResult = await createUserProfile(userId!, processedData);
          if (!profileResult.success) {
            result.errors.push({
              email: processedData.email,
              error: `Failed to create profile: ${profileResult.error}`,
            });
            continue;
          }

          // Get the profile ID for the historical registration
          const { data: profileData } = await adminClient
            .from('iepa_user_profiles')
            .select('id')
            .eq('user_id', userId)
            .single();

          if (!profileData) {
            result.errors.push({
              email: processedData.email,
              error: 'Failed to retrieve profile ID',
            });
            continue;
          }

          // Create historical registration
          const historyResult = await createHistoricalRegistration(
            userId!,
            profileData.id,
            processedData
          );

          if (!historyResult.success) {
            result.errors.push({
              email: processedData.email,
              error: `Failed to create historical registration: ${historyResult.error}`,
            });
            continue;
          }

          console.log(`Successfully imported: ${processedData.email}`);
          result.successfulImports++;
        } catch (error) {
          console.error(
            `Error processing attendee ${rawAttendee['Email For Attendee List']}:`,
            error
          );
          result.errors.push({
            email: rawAttendee['Email For Attendee List'],
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      // Add a small delay between batches to be gentle on the database
      if (i + batchSize < attendeeData.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    result.success = result.errors.length === 0 || result.successfulImports > 0;

    console.log('Import completed:', {
      totalProcessed: result.totalProcessed,
      successful: result.successfulImports,
      skipped: result.skippedExisting,
      errors: result.errors.length,
    });

    return result;
  } catch (error) {
    console.error('Import failed:', error);
    result.errors.push({
      email: 'SYSTEM',
      error: error instanceof Error ? error.message : 'Unknown system error',
    });
    return result;
  }
};

// Configuration
const CONFIG = {
  jsonFilePath: '.docs/attendee-import/attendee-import-2025.json',
  outputDir: '.docs/attendee-import/results',
  passwordsFile: 'generated-passwords.json',
  reportFile: 'import-report.json',
};

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    skipExisting: true,
    batchSize: 10,
    help: false,
  };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--skip-existing':
        options.skipExisting = args[i + 1] !== 'false';
        if (args[i + 1] === 'false' || args[i + 1] === 'true') i++;
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[i + 1]) || 10;
        i++;
        break;
      case '--help':
        options.help = true;
        break;
    }
  }

  return options;
}

// Show help message
function showHelp() {
  console.log(`
IEPA 2024 Attendee Import Script

This script imports attendee data from the 2024 conference JSON file
and creates user accounts with profiles and historical registration data.

Usage:
  npm run import-attendees [options]

Options:
  --dry-run          Run without making changes (default: false)
  --skip-existing    Skip users that already exist (default: true)
  --batch-size N     Number of users to process at once (default: 10)
  --help             Show this help message

Examples:
  npm run import-attendees --dry-run
  npm run import-attendees --batch-size 5
  npm run import-attendees --skip-existing false

Files:
  Input:  ${CONFIG.jsonFilePath}
  Output: ${CONFIG.outputDir}/
`);
}

// Ensure output directory exists
function ensureOutputDir() {
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
}

// Load attendee data from JSON file
function loadAttendeeData() {
  try {
    const filePath = path.resolve(CONFIG.jsonFilePath);

    if (!fs.existsSync(filePath)) {
      throw new Error(`Attendee data file not found: ${filePath}`);
    }

    const jsonData = fs.readFileSync(filePath, 'utf-8');
    const attendeeData = JSON.parse(jsonData);

    if (!Array.isArray(attendeeData)) {
      throw new Error('Attendee data must be an array');
    }

    console.log(
      `Loaded ${attendeeData.length} attendee records from ${filePath}`
    );
    return attendeeData;
  } catch (error) {
    console.error('Error loading attendee data:', error);
    process.exit(1);
  }
}

// Save import results
function saveResults(result: ImportResult, options: any) {
  ensureOutputDir();

  // Save passwords (if any were generated)
  if (result.generatedPasswords.length > 0) {
    const passwordsPath = path.join(CONFIG.outputDir, CONFIG.passwordsFile);
    fs.writeFileSync(
      passwordsPath,
      JSON.stringify(result.generatedPasswords, null, 2)
    );
    console.log(`Generated passwords saved to: ${passwordsPath}`);
    console.log(
      '⚠️  IMPORTANT: Store these passwords securely and share them with users safely!'
    );
  }

  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    options,
    summary: {
      totalProcessed: result.totalProcessed,
      successfulImports: result.successfulImports,
      skippedExisting: result.skippedExisting,
      errors: result.errors.length,
    },
    errors: result.errors,
    passwordsGenerated: result.generatedPasswords.length,
  };

  const reportPath = path.join(CONFIG.outputDir, CONFIG.reportFile);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`Import report saved to: ${reportPath}`);
}

// Main function
async function main() {
  const options = parseArgs();

  if (options.help) {
    showHelp();
    return;
  }

  console.log('🚀 IEPA 2024 Attendee Import Script');
  console.log('=====================================');

  if (options.dryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
  }

  console.log(`Options:`, {
    dryRun: options.dryRun,
    skipExisting: options.skipExisting,
    batchSize: options.batchSize,
  });

  // Load attendee data
  const attendeeData = loadAttendeeData();

  // Environment variables already validated at startup

  try {
    // Run the import
    console.log('\n📥 Starting import process...');
    const result = await importAttendeesFromJson(attendeeData, options);

    // Display results
    console.log('\n📊 Import Results:');
    console.log('==================');
    console.log(`Total Processed: ${result.totalProcessed}`);
    console.log(`Successful Imports: ${result.successfulImports}`);
    console.log(`Skipped Existing: ${result.skippedExisting}`);
    console.log(`Errors: ${result.errors.length}`);
    console.log(`Passwords Generated: ${result.generatedPasswords.length}`);

    if (result.errors.length > 0) {
      console.log('\n❌ Errors:');
      result.errors.forEach(error => {
        console.log(`   ${error.email}: ${error.error}`);
      });
    }

    // Save results
    if (!options.dryRun) {
      saveResults(result, options);
    }

    if (result.success) {
      console.log('\n✅ Import completed successfully!');

      if (result.generatedPasswords.length > 0) {
        console.log('\n🔐 Next Steps:');
        console.log('1. Review the generated passwords file');
        console.log('2. Securely distribute passwords to users');
        console.log('3. Consider implementing a password reset flow');
        console.log('4. Update users about their new accounts');
      }
    } else {
      console.log(
        '\n⚠️  Import completed with errors. Check the report for details.'
      );
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Import failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}
