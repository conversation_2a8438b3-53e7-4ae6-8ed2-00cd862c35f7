'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Input,
  Textarea,
} from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CONFERENCE_YEAR } from '@/lib/conference-config';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    organization: '',
    subject: '',
    category: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000));

    setSubmitted(true);
    setIsSubmitting(false);
  };

  const categoryOptions = [
    { value: 'registration', label: 'Registration Questions' },
    { value: 'technical', label: 'Technical Support' },
    { value: 'speaker', label: 'Speaker Inquiries' },
    { value: 'sponsor', label: 'Sponsorship Opportunities' },
    { value: 'general', label: 'General Information' },
    { value: 'other', label: 'Other' },
  ];

  if (submitted) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-2xl mx-auto text-center">
            <div className="iepa-status-success mb-8">
              <h1 className="iepa-heading-1 mb-4">
                Message Sent Successfully!
              </h1>
              <p className="iepa-body">
                Thank you for contacting us. We&apos;ve received your message
                and will respond within 1-2 business days.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={() => {
                  setSubmitted(false);
                  setFormData({
                    name: '',
                    email: '',
                    organization: '',
                    subject: '',
                    category: '',
                    message: '',
                  });
                }}
                variant="bordered"
                size="lg"
              >
                Send Another Message
              </Button>
              <Button as="a" href="/" color="primary" size="lg">
                Return to Homepage
              </Button>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      {/* Header */}
      <section className="iepa-section text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="iepa-heading-1 mb-4">Contact Us</h1>
          <p className="iepa-body mb-8 max-w-2xl mx-auto">
            Have questions about the IEPA {CONFERENCE_YEAR} Annual Conference?
            We&apos;re here to help! Send us a message and we&apos;ll get back
            to you promptly.
          </p>
        </div>
      </section>

      <div className="max-w-6xl mx-auto grid lg:grid-cols-3 gap-8">
        {/* Contact Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2">Send Us a Message</h2>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <Input
                    label="Full Name *"
                    placeholder="Your full name"
                    value={formData.name}
                    onChange={e => handleInputChange('name', e.target.value)}
                    isRequired
                  />

                  <Input
                    label="Email Address *"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={e => handleInputChange('email', e.target.value)}
                    isRequired
                  />
                </div>

                <Input
                  label="Organization"
                  placeholder="Your organization (optional)"
                  value={formData.organization}
                  onChange={e =>
                    handleInputChange('organization', e.target.value)
                  }
                />

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-[var(--iepa-gray-700)] mb-2 block">
                      Category *
                    </label>
                    <Select
                      value={formData.category}
                      onValueChange={value =>
                        handleInputChange('category', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categoryOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Input
                    label="Subject *"
                    placeholder="Brief subject line"
                    value={formData.subject}
                    onChange={e => handleInputChange('subject', e.target.value)}
                    isRequired
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-[var(--iepa-gray-700)] mb-2 block">
                    Message *
                  </label>
                  <Textarea
                    placeholder="Please provide details about your question or inquiry..."
                    value={formData.message}
                    onChange={e => handleInputChange('message', e.target.value)}
                    rows={5}
                  />
                  <p className="text-sm text-[var(--iepa-gray-600)] mt-1">
                    Please be as specific as possible to help us provide the
                    best assistance
                  </p>
                </div>

                <Button
                  type="submit"
                  color="primary"
                  size="lg"
                  className="w-full"
                  disabled={
                    isSubmitting ||
                    !formData.name ||
                    !formData.email ||
                    !formData.subject ||
                    !formData.message
                  }
                >
                  {isSubmitting ? 'Sending Message...' : 'Send Message'}
                </Button>
              </form>
            </CardBody>
          </Card>
        </div>

        {/* Contact Information */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <h3 className="iepa-heading-3">Conference Information</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4 iepa-body">
                <div>
                  <h4 className="font-semibold mb-1">Registration Support</h4>
                  <p className="iepa-body-small">
                    For questions about registration, pricing, or account issues
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-1">Speaker Inquiries</h4>
                  <p className="iepa-body-small">
                    Information about speaking opportunities and proposal
                    submissions
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-1">Sponsorship</h4>
                  <p className="iepa-body-small">
                    Partnership and sponsorship opportunities
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-1">Technical Support</h4>
                  <p className="iepa-body-small">
                    Website issues, login problems, or technical assistance
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="iepa-heading-3">Response Times</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-3 iepa-body-small">
                <div className="flex justify-between">
                  <span>General Inquiries:</span>
                  <span className="font-semibold">1-2 business days</span>
                </div>
                <div className="flex justify-between">
                  <span>Registration Support:</span>
                  <span className="font-semibold">Same day</span>
                </div>
                <div className="flex justify-between">
                  <span>Technical Issues:</span>
                  <span className="font-semibold">4-6 hours</span>
                </div>
                <div className="flex justify-between">
                  <span>Urgent Matters:</span>
                  <span className="font-semibold">Within 2 hours</span>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="iepa-heading-3">Frequently Asked Questions</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <h4 className="iepa-body font-semibold mb-1">
                    When is registration deadline?
                  </h4>
                  <p className="iepa-body-small">
                    Registration remains open until the conference begins.
                  </p>
                </div>

                <div>
                  <h4 className="iepa-body font-semibold mb-1">
                    Can I modify my registration?
                  </h4>
                  <p className="iepa-body-small">
                    Yes, you can modify your registration up to 7 days before
                    the conference. Contact us for assistance.
                  </p>
                </div>

                <div>
                  <h4 className="iepa-body font-semibold mb-1">
                    What&apos;s the cancellation policy?
                  </h4>
                  <p className="iepa-body-small">
                    <strong>Cancellation Policy:</strong> Cancellations received
                    in writing or via email before August 15, 2025 will be
                    refunded minus a $100 cancellation fee. Cancellations
                    received in writing/email on or after Friday, August 15,
                    2025 will NOT be refunded; however, substitutions will be
                    accepted.
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>

          <div
            className="p-4 rounded-lg"
            style={{
              backgroundColor: 'var(--iepa-secondary-green)',
              color: 'white',
            }}
          >
            <h4 className="font-semibold mb-2">Need Immediate Help?</h4>
            <p className="text-sm mb-3">
              For urgent registration or technical issues during business hours:
            </p>
            <p className="text-sm font-semibold">
              Call: ************
              <br />
              Hours: Mon-Fri 8AM-6PM PST
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
