#!/usr/bin/env node

// <PERSON>ript to test URL generation logic for different environments
// This helps verify that magic links will use the correct URLs

console.log('🧪 Testing URL Generation Logic');
console.log('=' .repeat(50));

// Simulate different environment scenarios
const scenarios = [
  {
    name: 'Local Development',
    env: {
      NODE_ENV: 'development',
      NEXT_PUBLIC_APP_URL: 'http://localhost:6969',
    }
  },
  {
    name: 'Production with Localhost Env (BROKEN)',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_APP_URL: 'http://localhost:6969',
      VERCEL_URL: 'reg.iepa.com',
    }
  },
  {
    name: 'Production with Correct Env',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_APP_URL: 'https://reg.iepa.com',
    }
  },
  {
    name: 'Production with Vercel URL',
    env: {
      NODE_ENV: 'production',
      VERCEL_URL: 'reg.iepa.com',
    }
  },
  {
    name: 'Production with Custom Production URL',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_PRODUCTION_URL: 'https://reg.iepa.com',
    }
  }
];

// Mock the port-utils functions
function getDynamicAppUrl(env) {
  // Simulate server environment (no window)
  
  if (env.NEXT_PUBLIC_APP_URL) {
    const envUrl = env.NEXT_PUBLIC_APP_URL;
    
    // If we're in production (Vercel) and env var is localhost, use Vercel URL
    if (env.VERCEL_URL && envUrl.includes('localhost')) {
      return `https://${env.VERCEL_URL}`;
    }
    
    // Production domain detection
    if (env.NODE_ENV === 'production') {
      if (env.VERCEL_URL?.includes('iepa') || env.VERCEL_URL?.includes('reg.iepa.com')) {
        return `https://${env.VERCEL_URL}`;
      }
      if (env.VERCEL_URL === 'reg.iepa.com') {
        return 'https://reg.iepa.com';
      }
    }
    
    return envUrl;
  }

  return 'http://localhost:3000';
}

function getProductionAppUrl(env) {
  if (env.VERCEL_URL) {
    return `https://${env.VERCEL_URL}`;
  }
  
  if (env.NEXT_PUBLIC_PRODUCTION_URL) {
    return env.NEXT_PUBLIC_PRODUCTION_URL;
  }
  
  if (env.NODE_ENV === 'production') {
    return 'https://reg.iepa.com';
  }
  
  return getDynamicAppUrl(env);
}

function getAuthRedirectUrl(path, env) {
  // In production, always use the production URL for auth redirects
  if (env.NODE_ENV === 'production') {
    return `${getProductionAppUrl(env)}${path}`;
  }
  
  // In development, use dynamic URL detection
  return `${getDynamicAppUrl(env)}${path}`;
}

// Test each scenario
scenarios.forEach((scenario, index) => {
  console.log(`\n${index + 1}. ${scenario.name}`);
  console.log('-'.repeat(30));
  
  console.log('Environment Variables:');
  Object.entries(scenario.env).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  
  const dynamicUrl = getDynamicAppUrl(scenario.env);
  const productionUrl = getProductionAppUrl(scenario.env);
  const authRedirectUrl = getAuthRedirectUrl('/auth/callback?next=%2Fmy-registrations', scenario.env);
  
  console.log('\nGenerated URLs:');
  console.log(`  getDynamicAppUrl(): ${dynamicUrl}`);
  console.log(`  getProductionAppUrl(): ${productionUrl}`);
  console.log(`  getAuthRedirectUrl(): ${authRedirectUrl}`);
  
  // Check if the URL is correct for production
  const isCorrect = scenario.env.NODE_ENV === 'production' 
    ? authRedirectUrl.startsWith('https://reg.iepa.com')
    : authRedirectUrl.includes('localhost');
    
  console.log(`  Status: ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
});

console.log('\n🎯 Summary');
console.log('=' .repeat(50));
console.log('✅ The enhanced URL generation logic should fix the magic link issue');
console.log('✅ Production magic links will now use https://reg.iepa.com');
console.log('✅ Development magic links will continue to use localhost');
console.log('\n📋 Next Steps:');
console.log('1. Deploy the updated code to production');
console.log('2. Update NEXT_PUBLIC_APP_URL to https://reg.iepa.com in Vercel');
console.log('3. Test magic link generation from reg.iepa.com');
console.log('4. Verify magic links point to production domain');
