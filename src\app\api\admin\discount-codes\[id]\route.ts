// Individual Discount Code Management API
// GET /api/admin/discount-codes/[id] - Get specific discount code
// PATCH /api/admin/discount-codes/[id] - Update discount code
// DELETE /api/admin/discount-codes/[id] - Delete discount code

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';
import { stripe } from '@/lib/stripe';

// GET - Get specific discount code with usage details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabaseAdmin = createSupabaseAdmin();

    // Get discount code with usage statistics
    const { data: discountCode, error } = await supabaseAdmin
      .from('iepa_discount_codes')
      .select(`
        *,
        usage:iepa_discount_usage(
          id,
          user_id,
          registration_id,
          registration_type,
          original_amount,
          discount_amount,
          final_amount,
          used_at
        )
      `)
      .eq('id', id)
      .single();

    if (error || !discountCode) {
      return NextResponse.json(
        { success: false, error: 'Discount code not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: discountCode,
    });
  } catch (error) {
    console.error('Error in GET /api/admin/discount-codes/[id]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH - Update discount code
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const {
      name,
      description,
      discountType,
      discountValue,
      maxUses,
      maxUsesPerUser,
      validFrom,
      validUntil,
      minimumAmount,
      applicableRegistrationTypes,
      isActive,
      userId, // Admin user updating the code
    } = body;

    const supabaseAdmin = createSupabaseAdmin();

    // Get existing discount code
    const { data: existingCode, error: fetchError } = await supabaseAdmin
      .from('iepa_discount_codes')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !existingCode) {
      return NextResponse.json(
        { success: false, error: 'Discount code not found' },
        { status: 404 }
      );
    }

    // Validate discount type and value if provided
    if (discountType && !['percentage', 'fixed_amount'].includes(discountType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid discount type' },
        { status: 400 }
      );
    }

    if (discountValue !== undefined && discountValue <= 0) {
      return NextResponse.json(
        { success: false, error: 'Discount value must be greater than 0' },
        { status: 400 }
      );
    }

    if (discountType === 'percentage' && discountValue > 100) {
      return NextResponse.json(
        { success: false, error: 'Percentage discount cannot exceed 100%' },
        { status: 400 }
      );
    }

    // Update Stripe coupon if it exists and relevant fields changed
    if (existingCode.stripe_coupon_id && (name || discountType || discountValue)) {
      try {
        const updateData: any = {};
        if (name) updateData.name = name;
        
        await stripe.coupons.update(existingCode.stripe_coupon_id, updateData);
      } catch (stripeError: any) {
        console.error('Error updating Stripe coupon:', stripeError);
        // Continue with database update even if Stripe update fails
      }
    }

    // Prepare update data
    const updateData: any = {
      updated_by: userId,
      updated_at: new Date().toISOString(),
    };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (discountType !== undefined) updateData.discount_type = discountType;
    if (discountValue !== undefined) updateData.discount_value = discountValue;
    if (maxUses !== undefined) updateData.max_uses = maxUses;
    if (maxUsesPerUser !== undefined) updateData.max_uses_per_user = maxUsesPerUser;
    if (validFrom !== undefined) updateData.valid_from = validFrom;
    if (validUntil !== undefined) updateData.valid_until = validUntil;
    if (minimumAmount !== undefined) updateData.minimum_amount = minimumAmount;
    if (applicableRegistrationTypes !== undefined) {
      updateData.applicable_registration_types = applicableRegistrationTypes;
    }
    if (isActive !== undefined) updateData.is_active = isActive;

    // Update discount code in database
    const { data: updatedCode, error: updateError } = await supabaseAdmin
      .from('iepa_discount_codes')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating discount code:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update discount code' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedCode,
      message: 'Discount code updated successfully',
    });
  } catch (error) {
    console.error('Error in PATCH /api/admin/discount-codes/[id]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete discount code
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabaseAdmin = createSupabaseAdmin();

    // Get existing discount code
    const { data: existingCode, error: fetchError } = await supabaseAdmin
      .from('iepa_discount_codes')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !existingCode) {
      return NextResponse.json(
        { success: false, error: 'Discount code not found' },
        { status: 404 }
      );
    }

    // Check if discount code has been used
    const { count: usageCount } = await supabaseAdmin
      .from('iepa_discount_usage')
      .select('*', { count: 'exact' })
      .eq('discount_code_id', id);

    if (usageCount && usageCount > 0) {
      // Don't delete if it has been used, just deactivate
      const { error: deactivateError } = await supabaseAdmin
        .from('iepa_discount_codes')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', id);

      if (deactivateError) {
        return NextResponse.json(
          { success: false, error: 'Failed to deactivate discount code' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Discount code deactivated (cannot delete codes that have been used)',
      });
    }

    // Delete Stripe coupon if it exists
    if (existingCode.stripe_coupon_id) {
      try {
        await stripe.coupons.del(existingCode.stripe_coupon_id);
      } catch (stripeError) {
        console.error('Error deleting Stripe coupon:', stripeError);
        // Continue with database deletion even if Stripe deletion fails
      }
    }

    // Delete discount code from database
    const { error: deleteError } = await supabaseAdmin
      .from('iepa_discount_codes')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting discount code:', deleteError);
      return NextResponse.json(
        { success: false, error: 'Failed to delete discount code' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Discount code deleted successfully',
    });
  } catch (error) {
    console.error('Error in DELETE /api/admin/discount-codes/[id]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
