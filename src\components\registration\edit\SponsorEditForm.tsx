'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Input,
  Textarea,
  Label,
} from '@/components/ui';
import type { SponsorRegistration } from '@/types/database';
import { FiSave, FiUser, FiMapPin } from 'react-icons/fi';

interface SponsorEditFormProps {
  registration: SponsorRegistration;
  onSave: (data: Partial<SponsorRegistration>) => Promise<void>;
  saving: boolean;
  error: string | null;
}

export function SponsorEditForm({
  registration,
  onSave,
  saving,
  error,
}: SponsorEditFormProps) {
  const [formData, setFormData] = useState({
    sponsor_name: registration.sponsor_name || '',
    sponsor_url: registration.sponsor_url || '',
    sponsor_description: registration.sponsor_description || '',
    linked_attendee_email: registration.linked_attendee_email || '',
    company_domain: registration.company_domain || '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody>
            <p className="text-red-600 text-sm">{error}</p>
          </CardBody>
        </Card>
      )}

      {/* Sponsor Information */}
      <Card>
        <CardHeader>
          <h2 className="iepa-heading-2 flex items-center gap-2">
            <FiUser className="w-5 h-5" />
            Sponsor Information
          </h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <div>
              <Label htmlFor="sponsor_name">Organization Name *</Label>
              <Input
                id="sponsor_name"
                value={formData.sponsor_name}
                onChange={e =>
                  handleInputChange('sponsor_name', e.target.value)
                }
                required
              />
            </div>
            <div>
              <Label htmlFor="sponsor_url">Website</Label>
              <Input
                id="sponsor_url"
                type="url"
                value={formData.sponsor_url}
                onChange={e => handleInputChange('sponsor_url', e.target.value)}
                placeholder="https://example.com"
              />
            </div>

            <div>
              <Label htmlFor="sponsor_description">
                Organization Description
              </Label>
              <Textarea
                id="sponsor_description"
                value={formData.sponsor_description}
                onChange={e =>
                  handleInputChange('sponsor_description', e.target.value)
                }
                placeholder="Brief description of your organization..."
                rows={3}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Attendee Linking */}
      <Card>
        <CardHeader>
          <h2 className="iepa-heading-2 flex items-center gap-2">
            <FiMapPin className="w-5 h-5" />
            Attendee Linking
          </h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <div>
              <Label htmlFor="linked_attendee_email">
                Linked Attendee Email
              </Label>
              <Input
                id="linked_attendee_email"
                type="email"
                value={formData.linked_attendee_email}
                onChange={e =>
                  handleInputChange('linked_attendee_email', e.target.value)
                }
                placeholder="<EMAIL>"
              />
              <p className="text-sm text-gray-600 mt-1">
                Email of the primary attendee associated with this sponsorship.
              </p>
            </div>
            <div>
              <Label htmlFor="company_domain">Company Domain</Label>
              <Input
                id="company_domain"
                value={formData.company_domain}
                onChange={e =>
                  handleInputChange('company_domain', e.target.value)
                }
                placeholder="company.com"
              />
              <p className="text-sm text-gray-600 mt-1">
                Company domain for automatic discount application to attendees.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={saving}
          className="flex items-center gap-2"
        >
          <FiSave className="w-4 h-4" />
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </form>
  );
}
