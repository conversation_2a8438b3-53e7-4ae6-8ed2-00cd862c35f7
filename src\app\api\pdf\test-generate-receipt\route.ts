// API Route for Test PDF Receipt Generation
// POST /api/pdf/test-generate-receipt

import { NextRequest, NextResponse } from 'next/server';
import { generateAndStoreReceiptPDF } from '@/lib/pdf-generation/services/pdfGenerator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { registrationId, registrationType, paymentMethod, transactionId } =
      body;

    // Validate required fields
    if (!registrationId || !registrationType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Registration ID and type are required',
        },
        { status: 400 }
      );
    }

    // Validate registration type
    if (!['attendee', 'speaker', 'sponsor'].includes(registrationType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid registration type',
        },
        { status: 400 }
      );
    }

    // Create mock registration data for testing
    let mockRegistrationData;

    switch (registrationType) {
      case 'attendee':
        mockRegistrationData = {
          id: registrationId,
          registrationType: 'iepa-member',
          fullName: '<PERSON>',
          firstName: '<PERSON>',
          lastName: 'Doe',
          nameOnBadge: '<PERSON>',
          email: '<EMAIL>',
          phoneNumber: '(*************',
          organization: 'Sample Energy Company',
          jobTitle: 'Energy Analyst',
          streetAddress: '123 Main Street',
          city: 'Sacramento',
          state: 'CA',
          zipCode: '95814',
          country: 'United States',
          golfTournament: true,
          golfClubRental: true,
          golfClubHandedness: 'right',
          // Night staying options
          nightOne: true, // September 15-16, 2025 (Monday night)
          nightTwo: true, // September 16-17, 2025 (Tuesday night)
          meals: {
            sept15Dinner: true,
            sept16Breakfast: true,
            sept16Lunch: true,
            sept16Dinner: true,
            sept17Breakfast: true,
            sept17Lunch: true,
          },
          dietaryNeeds: 'Vegetarian, no nuts',
          registrationTotal: 2300,
          golfTotal: 200,
          golfClubRentalTotal: 75,
          mealTotal: 0,
          grandTotal: 2575,
          paymentStatus: 'paid',
          paymentId: transactionId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        break;

      case 'speaker':
        mockRegistrationData = {
          id: registrationId,
          fullName: 'Jane Smith',
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          phoneNumber: '(*************',
          organizationName: 'Renewable Energy Solutions',
          jobTitle: 'Senior Engineer',
          bio: 'Jane is a leading expert in renewable energy systems with over 15 years of experience.',
          presentationTitle: 'Future of Solar Energy in California',
          presentationDescription:
            "An overview of emerging solar technologies and their impact on California's energy grid.",
          presentationDuration: '45 minutes',
          targetAudience: 'Energy professionals and policymakers',
          learningObjectives:
            'Understanding solar technology trends and policy implications',
          speakerExperience:
            'Extensive speaking experience at industry conferences',
          previousSpeaking: 'Keynote speaker at California Energy Summit 2024',
          equipmentNeeds: 'Projector and microphone',
          specialRequests: 'None',
          presentationFileUrl: undefined,
          headshotUrl: undefined,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        break;

      case 'sponsor':
        mockRegistrationData = {
          id: registrationId,
          sponsorName: 'Green Energy Corp',
          sponsorUrl: 'https://greenenergycorp.com',
          sponsorImageUrl: undefined,
          sponsorDescription:
            'Leading provider of sustainable energy solutions',
          sponsorshipLevel: 'platinum',
          contactName: 'Mike Johnson',
          contactEmail: '<EMAIL>',
          contactPhone: '(*************',
          contactTitle: 'Marketing Director',
          billingAddress: '456 Corporate Blvd',
          billingCity: 'San Francisco',
          billingState: 'CA',
          billingZip: '94105',
          billingCountry: 'United States',
          companyDescription:
            'We develop innovative renewable energy technologies',
          marketingGoals: 'Increase brand awareness and generate leads',
          exhibitRequirements: 'Standard booth with power and internet',
          specialRequests: 'None',
          attendeeCount: '3',
          attendeeNames: 'Mike Johnson, Sarah Wilson, Tom Brown',
          sponsorshipTotal: 5000,
          paymentStatus: 'paid',
          paymentId: transactionId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        break;

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid registration type',
          },
          { status: 400 }
        );
    }

    // Generate and store PDF receipt
    const result = await generateAndStoreReceiptPDF(
      registrationType as 'attendee' | 'speaker' | 'sponsor',
      mockRegistrationData,
      paymentMethod,
      transactionId
    );

    if (!result.success) {
      console.error('PDF generation failed:', result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          details: 'PDF generation or storage failed',
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Test receipt generated successfully',
      receiptUrl: result.publicUrl,
      fileName: result.fileName,
      mockData: true,
    });
  } catch (error) {
    console.error('Error in test-generate-receipt API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}
