'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@/components/ui';
import Link from 'next/link';
import { FiEdit, <PERSON><PERSON>ye, <PERSON><PERSON>ser, FiCalendar, FiMail } from 'react-icons/fi';
import { FaBuilding } from 'react-icons/fa';
import { formatDate } from '@/lib/pdf-generation/utils';
import type { ExistingRegistration } from '@/services/registrationConstraints';

interface ExistingRegistrationNoticeProps {
  registration: ExistingRegistration;
  message: string;
  showEditOption?: boolean;
  showViewOption?: boolean;
  className?: string;
}

export function ExistingRegistrationNotice({
  registration,
  message,
  showEditOption = true,
  showViewOption = true,
  className = '',
}: ExistingRegistrationNoticeProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'danger';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const getRegistrationTypeLabel = (type: string) => {
    switch (type) {
      case 'attendee':
        return 'Attendee Registration';
      case 'speaker':
        return 'Speaker Registration';
      case 'sponsor':
        return 'Sponsor Registration';
      default:
        return 'Registration';
    }
  };

  const getRegistrationIcon = (type: string) => {
    switch (type) {
      case 'attendee':
        return <FiUser className="w-6 h-6" />;
      case 'speaker':
        return <FiMail className="w-6 h-6" />;
      case 'sponsor':
        return <FaBuilding className="w-6 h-6" />;
      default:
        return <FiUser className="w-6 h-6" />;
    }
  };

  return (
    <div className={`iepa-container ${className}`}>
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <Card className="border-2 border-orange-200 bg-orange-50">
            <CardHeader>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 p-3 bg-orange-100 rounded-full text-orange-600">
                  {getRegistrationIcon(registration.type)}
                </div>
                <div className="flex-grow">
                  <h2 className="iepa-heading-2 mb-2 text-orange-800">
                    Registration Already Exists
                  </h2>
                  <p className="iepa-body text-orange-700 mb-4">
                    {message}
                  </p>
                  <div className="flex items-center gap-2">
                    <Chip
                      color={getStatusColor(registration.status)}
                      size="sm"
                    >
                      {registration.status.charAt(0).toUpperCase() + registration.status.slice(1)}
                    </Chip>
                    <span className="text-sm text-orange-600">
                      {getRegistrationTypeLabel(registration.type)}
                    </span>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardBody>
              {/* Registration Details */}
              <div className="grid md:grid-cols-2 gap-6 mb-6">
                <div className="space-y-3">
                  <h3 className="iepa-heading-3 text-orange-800">Registration Details</h3>
                  
                  {registration.full_name && (
                    <div className="flex items-center gap-2 text-sm">
                      <FiUser className="w-4 h-4 text-orange-600" />
                      <span className="font-medium">Name:</span>
                      <span>{registration.full_name}</span>
                    </div>
                  )}
                  
                  {registration.email && (
                    <div className="flex items-center gap-2 text-sm">
                      <FiMail className="w-4 h-4 text-orange-600" />
                      <span className="font-medium">Email:</span>
                      <span>{registration.email}</span>
                    </div>
                  )}
                  
                  {registration.organization_name && (
                    <div className="flex items-center gap-2 text-sm">
                      <FaBuilding className="w-4 h-4 text-orange-600" />
                      <span className="font-medium">Organization:</span>
                      <span>{registration.organization_name}</span>
                    </div>
                  )}
                  
                  {registration.registration_type && (
                    <div className="flex items-center gap-2 text-sm">
                      <FiUser className="w-4 h-4 text-orange-600" />
                      <span className="font-medium">Type:</span>
                      <span className="capitalize">{registration.registration_type}</span>
                    </div>
                  )}
                </div>
                
                <div className="space-y-3">
                  <h3 className="iepa-heading-3 text-orange-800">Registration Info</h3>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <FiCalendar className="w-4 h-4 text-orange-600" />
                    <span className="font-medium">Created:</span>
                    <span>{formatDate(registration.created_at)}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <FiCalendar className="w-4 h-4 text-orange-600" />
                    <span className="font-medium">Updated:</span>
                    <span>{formatDate(registration.updated_at)}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <span className="font-medium">ID:</span>
                    <span className="font-mono text-xs bg-orange-100 px-2 py-1 rounded">
                      {registration.id.slice(0, 8)}...
                    </span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4 border-t border-orange-200">
                <div className="flex gap-2">
                  {showViewOption && (
                    <Button
                      as={Link}
                      href="/my-registrations"
                      variant="bordered"
                      color="primary"
                      startContent={<FiEye className="w-4 h-4" />}
                    >
                      View Registration
                    </Button>
                  )}
                  
                  {showEditOption && (
                    <Button
                      as={Link}
                      href={`/my-registrations/edit/${registration.id}?type=${registration.type}`}
                      color="primary"
                      startContent={<FiEdit className="w-4 h-4" />}
                    >
                      Edit Registration
                    </Button>
                  )}
                </div>
                
                <div className="flex gap-2 sm:ml-auto">
                  <Button
                    as={Link}
                    href="/register"
                    variant="light"
                  >
                    Back to Registration Options
                  </Button>
                  
                  <Button
                    as={Link}
                    href="/contact"
                    variant="bordered"
                  >
                    Contact Support
                  </Button>
                </div>
              </div>

              {/* Additional Information */}
              <div className="mt-6 p-4 bg-orange-100 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-2">Need to Make Changes?</h4>
                <p className="text-sm text-orange-700 mb-3">
                  If you need to modify your registration or have special circumstances, 
                  please contact our support team. We're here to help!
                </p>
                <div className="flex flex-col sm:flex-row gap-2 text-sm">
                  <span className="font-medium text-orange-800">Support Options:</span>
                  <div className="flex gap-4">
                    <Button
                      as={Link}
                      href="/contact"
                      size="sm"
                      variant="light"
                      className="text-orange-700 hover:text-orange-800"
                    >
                      Contact Form
                    </Button>
                    <span className="text-orange-600">|</span>
                    <span className="text-orange-700">Email: <EMAIL></span>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>
    </div>
  );
}
