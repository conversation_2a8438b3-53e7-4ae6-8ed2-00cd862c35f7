// UI Components for Form Persistence
// Provides visual feedback for auto-save, restore prompts, and data management

'use client';

import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  CheckCircle, 
  Clock, 
  RotateCcw, 
  Trash2, 
  AlertTriangle,
  Save,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Auto-save indicator component
export interface AutoSaveIndicatorProps {
  isAutoSaving: boolean;
  lastSavedAt: Date | null;
  className?: string;
}

export function AutoSaveIndicator({ 
  isAutoSaving, 
  lastSavedAt, 
  className 
}: AutoSaveIndicatorProps) {
  if (!isAutoSaving && !lastSavedAt) return null;

  return (
    <div className={cn(
      "flex items-center gap-2 text-sm text-[var(--iepa-gray-600)]",
      className
    )}>
      {isAutoSaving ? (
        <>
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Saving...</span>
        </>
      ) : lastSavedAt ? (
        <>
          <CheckCircle className="h-4 w-4 text-green-600" />
          <span>
            Saved at {lastSavedAt.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </span>
        </>
      ) : null}
    </div>
  );
}

// Restore data prompt component
export interface RestoreDataPromptProps {
  show: boolean;
  dataAge: string | null;
  onRestore: () => void;
  onStartFresh: () => void;
  onDismiss: () => void;
  formType?: string;
}

export function RestoreDataPrompt({
  show,
  dataAge,
  onRestore,
  onStartFresh,
  onDismiss,
  formType = 'form'
}: RestoreDataPromptProps) {
  if (!show) return null;

  return (
    <Alert className="mb-6 border-[var(--iepa-blue-200)] bg-[var(--iepa-blue-50)]">
      <RotateCcw className="h-4 w-4" />
      <AlertDescription>
        <div className="space-y-3">
          <div>
            <strong>Continue where you left off?</strong>
            <p className="text-sm text-[var(--iepa-gray-600)] mt-1">
              We found saved {formType} data from {dataAge}. Would you like to continue 
              with your previous progress or start fresh?
            </p>
          </div>
          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={onRestore}
              size="sm"
              className="bg-[var(--iepa-blue-600)] hover:bg-[var(--iepa-blue-700)] text-white"
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Continue Previous
            </Button>
            <Button
              onClick={onStartFresh}
              variant="outline"
              size="sm"
              className="border-[var(--iepa-gray-300)] text-[var(--iepa-gray-700)]"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Start Fresh
            </Button>
            <Button
              onClick={onDismiss}
              variant="ghost"
              size="sm"
              className="text-[var(--iepa-gray-600)]"
            >
              Dismiss
            </Button>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
}

// Data management panel component
export interface DataManagementPanelProps {
  hasPersistedData: boolean;
  dataAge: string | null;
  isDataExpired: boolean;
  onClearData: () => void;
  onRestoreData?: () => void;
  formType?: string;
  className?: string;
}

export function DataManagementPanel({
  hasPersistedData,
  dataAge,
  isDataExpired,
  onClearData,
  onRestoreData,
  formType = 'form',
  className
}: DataManagementPanelProps) {
  if (!hasPersistedData) return null;

  return (
    <Card className={cn("border-[var(--iepa-gray-200)]", className)}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            {isDataExpired ? (
              <AlertTriangle className="h-4 w-4 text-amber-500" />
            ) : (
              <Save className="h-4 w-4 text-[var(--iepa-blue-600)]" />
            )}
            <div>
              <h4 className="text-sm font-medium text-[var(--iepa-gray-900)]">
                Saved {formType} Data
              </h4>
              <p className="text-xs text-[var(--iepa-gray-600)]">
                {isDataExpired ? (
                  <>Expired data from {dataAge}</>
                ) : (
                  <>Last saved {dataAge}</>
                )}
              </p>
            </div>
          </div>
          <div className="flex gap-1">
            {!isDataExpired && onRestoreData && (
              <Button
                onClick={onRestoreData}
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-[var(--iepa-blue-600)] hover:text-[var(--iepa-blue-700)]"
              >
                <RotateCcw className="h-3 w-3" />
              </Button>
            )}
            <Button
              onClick={onClearData}
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-[var(--iepa-gray-500)] hover:text-red-600"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Floating save status component
export interface FloatingSaveStatusProps {
  isAutoSaving: boolean;
  lastSavedAt: Date | null;
  show?: boolean;
  position?: 'top-right' | 'bottom-right' | 'bottom-left' | 'top-left';
}

export function FloatingSaveStatus({
  isAutoSaving,
  lastSavedAt,
  show = true,
  position = 'bottom-right'
}: FloatingSaveStatusProps) {
  if (!show || (!isAutoSaving && !lastSavedAt)) return null;

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-left': 'top-4 left-4',
  };

  return (
    <div className={cn(
      "fixed z-50 pointer-events-none",
      positionClasses[position]
    )}>
      <div className={cn(
        "bg-white border border-[var(--iepa-gray-200)] rounded-lg shadow-lg px-3 py-2",
        "transition-all duration-300 ease-in-out",
        isAutoSaving ? "opacity-100 scale-100" : "opacity-75 scale-95"
      )}>
        <AutoSaveIndicator 
          isAutoSaving={isAutoSaving} 
          lastSavedAt={lastSavedAt}
          className="text-xs"
        />
      </div>
    </div>
  );
}

// Privacy notice component
export interface PrivacyNoticeProps {
  onClearAllData?: () => void;
  className?: string;
}

export function PrivacyNotice({ onClearAllData, className }: PrivacyNoticeProps) {
  return (
    <Alert className={cn("border-[var(--iepa-gray-200)]", className)}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription>
        <div className="space-y-2">
          <p className="text-sm">
            <strong>Privacy Notice:</strong> Form data is saved locally in your browser 
            to prevent data loss. This data is automatically deleted after 7 days or 
            when you successfully submit the form.
          </p>
          {onClearAllData && (
            <Button
              onClick={onClearAllData}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Clear All Saved Data
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}
