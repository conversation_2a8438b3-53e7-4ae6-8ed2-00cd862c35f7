# Supabase Setup Instructions

This document provides step-by-step instructions for setting up Supa<PERSON> for the IEPA 2025 Conference Registration Application.

## Prerequisites

1. A Supabase account (sign up at [supabase.com](https://supabase.com))
2. Node.js and npm installed locally

## Step 1: Create a New Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `iepa-conf-reg-2025`
   - **Database Password**: Generate a strong password and save it securely
   - **Region**: Choose the region closest to your users
5. Click "Create new project"
6. Wait for the project to be created (this may take a few minutes)

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Project API Keys** > **anon public** key
   - **Project API Keys** > **service_role** key (keep this secret!)

## Step 3: Configure Environment Variables

1. Copy `.env.example` to `.env.local`:

   ```bash
   cp .env.example .env.local
   ```

2. Update `.env.local` with your Supabase credentials:

   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
   ```

## Step 4: Set Up the Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Click "New query"
3. Copy the contents of `src/lib/database-schema.sql`
4. Paste it into the SQL editor
5. Click "Run" to execute the schema

This will create:

- All necessary tables (`iepa_attendee_registrations`, `iepa_speaker_registrations`, `iepa_sponsor_registrations`, `iepa_payments`)
- Row Level Security (RLS) policies
- Storage buckets for file uploads (`iepa-presentations`, `iepa-sponsor-assets`)
- Triggers for automatic `updated_at` timestamps

## Step 5: Configure Authentication

1. In your Supabase dashboard, go to **Authentication** > **Settings**
2. Configure the following:
   - **Site URL**: `http://localhost:3000` (default for development)
   - **Redirect URLs**: The following patterns are already configured for flexible localhost testing:
     - `http://localhost:**` (any port on localhost)
     - `https://localhost:**` (any port on localhost with HTTPS)
     - `http://127.0.0.1:**` (any port on 127.0.0.1)
     - `https://127.0.0.1:**` (any port on 127.0.0.1 with HTTPS)
     - `https://iepa-conf-reg.vercel.app/**` (production)
     - `https://*.vercel.app/**` (Vercel preview deployments)
3. Under **Email Templates**, customize the email templates if needed

### Development Port Flexibility

The authentication system is configured to work with **any localhost port** for testing:

- You can run the app on `http://localhost:3000`, `http://localhost:3001`, `http://localhost:8080`, etc.
- The auth system will automatically detect the current port and use it for redirects
- No need to manually update Supabase settings when changing ports during development

## Step 6: Configure Storage

1. Go to **Storage** in your Supabase dashboard
2. Verify that the following buckets were created:
   - `presentations` (for speaker presentation files)
   - `sponsor-assets` (for sponsor logos and images)
3. The buckets should be private by default with RLS policies applied

## Step 7: Test the Connection

1. Start your development server:

   ```bash
   npm run dev
   ```

2. The application should now be able to connect to Supabase
3. Check the browser console for any connection errors

## Security Notes

- **Never commit your `.env.local` file** - it contains sensitive credentials
- The `service_role` key has admin privileges - only use it server-side
- RLS policies ensure users can only access their own data
- File uploads are organized by user ID for security

## Troubleshooting

### Connection Issues

- Verify your environment variables are correct
- Check that your Supabase project is active
- Ensure you're using the correct project URL and keys

### Database Issues

- Make sure the schema was executed successfully
- Check the Supabase logs for any errors
- Verify RLS is enabled on all tables

### Authentication Issues

- Check your Site URL and Redirect URLs in Supabase settings
- Verify email templates are configured
- Check browser console for auth errors

## Next Steps

After completing this setup:

1. Test user registration and login
2. Test file uploads to storage buckets
3. Verify RLS policies are working correctly
4. Set up Stripe for payment processing
5. Configure email service for notifications

For production deployment, you'll need to:

1. Update Site URL and Redirect URLs to your production domain
2. Configure custom SMTP for email delivery
3. Set up database backups
4. Configure monitoring and alerts
