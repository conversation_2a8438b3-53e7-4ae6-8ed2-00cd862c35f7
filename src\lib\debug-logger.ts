/**
 * Debug Logger for IEPA Registration System
 * Comprehensive logging and debugging utilities for registration forms
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export type LogCategory =
  | 'form'
  | 'api'
  | 'auth'
  | 'payment'
  | 'validation'
  | 'general';

export interface DebugLogEntry {
  id: string;
  timestamp: string;
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: unknown;
  error?: {
    name: string;
    message: string;
    stack?: string;
    cause?: unknown;
  };
  context?: {
    page?: string;
    user?: string;
    formData?: Record<string, unknown>;
    environment?: Record<string, unknown>;
  };
}

// Debug log storage
const debugLogs: DebugLogEntry[] = [];
const MAX_LOGS = 500;

/**
 * Generate unique ID for log entries
 */
const generateLogId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Serialize error objects for logging
 */
const serializeError = (error: unknown): DebugLogEntry['error'] | undefined => {
  if (!error) return undefined;

  if (error instanceof Error) {
    return {
      name: error.name,
      message: error.message,
      stack: error.stack,
      cause: error.cause,
    };
  }

  if (typeof error === 'string') {
    return {
      name: 'StringError',
      message: error,
    };
  }

  if (typeof error === 'object') {
    return {
      name: 'ObjectError',
      message: JSON.stringify(error),
    };
  }

  return {
    name: 'UnknownError',
    message: String(error),
  };
};

/**
 * Get current environment context
 */
const getEnvironmentContext = (): Record<string, unknown> => {
  const context: Record<string, unknown> = {
    nodeEnv: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
  };

  if (typeof window !== 'undefined') {
    context.userAgent = window.navigator.userAgent;
    context.location = window.location.href;
    context.viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };
    context.localStorage = {
      available: typeof Storage !== 'undefined',
      itemCount: localStorage.length,
    };
  }

  return context;
};

/**
 * Core logging function
 */
export const logDebug = (
  level: LogLevel,
  category: LogCategory,
  message: string,
  data?: unknown,
  error?: unknown,
  context?: Partial<DebugLogEntry['context']>
): void => {
  const logEntry: DebugLogEntry = {
    id: generateLogId(),
    timestamp: new Date().toISOString(),
    level,
    category,
    message,
    data: data ? JSON.parse(JSON.stringify(data)) : undefined,
    error: serializeError(error),
    context: {
      environment: getEnvironmentContext(),
      ...context,
    },
  };

  // Add to debug logs
  debugLogs.push(logEntry);

  // Keep only the most recent logs
  if (debugLogs.length > MAX_LOGS) {
    debugLogs.shift();
  }

  // Console logging in development
  if (process.env.NODE_ENV === 'development') {
    console.group(`🐛 [${level.toUpperCase()}] ${category}: ${message}`);
    if (data) console.log('Data:', data);
    if (error) console.error('Error:', error);
    if (context) console.log('Context:', context);
    console.groupEnd();
  }
};

/**
 * Convenience logging functions
 */
export const debugLog = {
  debug: (
    category: LogCategory,
    message: string,
    data?: unknown,
    context?: Partial<DebugLogEntry['context']>
  ) => logDebug('debug', category, message, data, undefined, context),

  info: (
    category: LogCategory,
    message: string,
    data?: unknown,
    context?: Partial<DebugLogEntry['context']>
  ) => logDebug('info', category, message, data, undefined, context),

  warn: (
    category: LogCategory,
    message: string,
    data?: unknown,
    context?: Partial<DebugLogEntry['context']>
  ) => logDebug('warn', category, message, data, undefined, context),

  error: (
    category: LogCategory,
    message: string,
    error?: unknown,
    data?: unknown,
    context?: Partial<DebugLogEntry['context']>
  ) => logDebug('error', category, message, data, error, context),

  // Specialized logging functions
  formSubmission: (
    formType: string,
    formData: Record<string, unknown>,
    success: boolean,
    error?: unknown
  ) =>
    logDebug(
      success ? 'info' : 'error',
      'form',
      `Form submission ${success ? 'succeeded' : 'failed'}: ${formType}`,
      { formData, success },
      error,
      { page: formType }
    ),

  apiCall: (
    endpoint: string,
    method: string,
    data?: unknown,
    response?: unknown,
    error?: unknown
  ) =>
    logDebug(
      error ? 'error' : 'info',
      'api',
      `API ${method} ${endpoint} ${error ? 'failed' : 'succeeded'}`,
      { endpoint, method, requestData: data, response },
      error
    ),

  validation: (
    field: string,
    value: unknown,
    isValid: boolean,
    errorMessage?: string
  ) =>
    logDebug(
      isValid ? 'debug' : 'warn',
      'validation',
      `Field validation ${isValid ? 'passed' : 'failed'}: ${field}`,
      { field, value, isValid, errorMessage }
    ),

  auth: (
    operation: string,
    success: boolean,
    data?: unknown,
    error?: unknown
  ) =>
    logDebug(
      success ? 'info' : 'error',
      'auth',
      `Auth operation ${success ? 'succeeded' : 'failed'}: ${operation}`,
      data,
      error
    ),

  payment: (
    operation: string,
    amount?: number,
    success?: boolean,
    error?: unknown,
    data?: Record<string, unknown>
  ) =>
    logDebug(
      error ? 'error' : 'info',
      'payment',
      `Payment operation: ${operation}${amount ? ` ($${amount})` : ''}`,
      { operation, amount, success, ...(data || {}) },
      error
    ),
};

/**
 * Get all debug logs
 */
export const getDebugLogs = (): DebugLogEntry[] => {
  return [...debugLogs];
};

/**
 * Get filtered debug logs
 */
export const getFilteredDebugLogs = (filters: {
  level?: LogLevel[];
  category?: LogCategory[];
  since?: Date;
  search?: string;
}): DebugLogEntry[] => {
  return debugLogs.filter(log => {
    if (filters.level && !filters.level.includes(log.level)) return false;
    if (filters.category && !filters.category.includes(log.category))
      return false;
    if (filters.since && new Date(log.timestamp) < filters.since) return false;
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const searchableText =
        `${log.message} ${JSON.stringify(log.data)} ${JSON.stringify(log.context)}`.toLowerCase();
      if (!searchableText.includes(searchLower)) return false;
    }
    return true;
  });
};

/**
 * Clear debug logs
 */
export const clearDebugLogs = (): void => {
  debugLogs.length = 0;
  debugLog.info('general', 'Debug logs cleared');
};

/**
 * Export debug logs as JSON
 */
export const exportDebugLogs = (): string => {
  return JSON.stringify(debugLogs, null, 2);
};

/**
 * Get debug statistics
 */
export const getDebugStats = () => {
  const stats = {
    total: debugLogs.length,
    byLevel: {} as Record<LogLevel, number>,
    byCategory: {} as Record<LogCategory, number>,
    timeRange: {
      oldest: debugLogs.length > 0 ? debugLogs[0].timestamp : null,
      newest:
        debugLogs.length > 0 ? debugLogs[debugLogs.length - 1].timestamp : null,
    },
  };

  debugLogs.forEach(log => {
    stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1;
    stats.byCategory[log.category] = (stats.byCategory[log.category] || 0) + 1;
  });

  return stats;
};
