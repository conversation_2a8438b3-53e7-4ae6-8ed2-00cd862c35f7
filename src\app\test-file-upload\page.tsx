'use client';

import React, { useState, useEffect } from 'react';
import { FileUpload } from '@/components/ui/FileUpload';
import { Card, CardHeader, CardBody } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';

export default function TestFileUploadPage() {
  const [presentationFile, setPresentationFile] = useState<string | null>(null);
  const [headshotFile, setHeadshotFile] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);

  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();
        if (error) {
          console.error('Auth error:', error);
          setUploadErrors(prev => [...prev, `Auth error: ${error.message}`]);
        }
        setUser(user);
      } catch (error) {
        console.error('Auth check failed:', error);
        setUploadErrors(prev => [...prev, 'Authentication check failed']);
      } finally {
        setAuthLoading(false);
      }
    };
    checkAuth();
  }, []);

  // Mock existing files for demonstration
  const [existingPresentation] = useState<string | null>(
    'https://example.com/sample-presentation.pdf'
  );
  const [existingHeadshot] = useState<string | null>(
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
  );

  const handlePresentationUpload = (url: string | null, file: File | null) => {
    setPresentationFile(url);
    console.log('Presentation uploaded:', { url, file });
    if (url) {
      setUploadErrors(prev => prev.filter(e => !e.includes('Presentation')));
    }
  };

  const handleHeadshotUpload = (url: string | null, file: File | null) => {
    setHeadshotFile(url);
    console.log('Headshot uploaded:', { url, file });
    if (url) {
      setUploadErrors(prev => prev.filter(e => !e.includes('Headshot')));
    }
  };

  const handleRemoveExistingPresentation = () => {
    console.log('Removing existing presentation');
    // In real app, this would call an API to remove the file
  };

  const handleRemoveExistingHeadshot = () => {
    console.log('Removing existing headshot');
    // In real app, this would call an API to remove the file
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Enhanced FileUpload Component Test
          </h1>
          <p className="text-gray-600">
            Testing the enhanced FileUpload component with add/upload/change functionality
          </p>
        </div>

        <div className="space-y-8">
          {/* Presentation File Upload */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">Presentation File Upload</h2>
              <p className="text-sm text-gray-600">
                Test presentation file upload with existing file management
              </p>
            </CardHeader>
            <CardBody>
              <FileUpload
                label="Presentation File"
                description="Upload your presentation (PDF, PPT, PPTX, DOC, DOCX - max 50MB)"
                bucket="iepa-presentations"
                folder="speaker-presentations"
                maxSize={52428800} // 50MB
                allowedTypes={[
                  'application/pdf',
                  'application/vnd.ms-powerpoint',
                  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                  'application/msword',
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                ]}
                accept=".pdf,.ppt,.pptx,.doc,.docx"
                onFileUpload={handlePresentationUpload}
                placeholder="Upload your presentation file"
                existingFileUrl={existingPresentation}
                existingFileName="sample-presentation.pdf"
                allowDownload={true}
                allowView={true}
                onRemoveExisting={handleRemoveExistingPresentation}
              />
              
              {presentationFile && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-800">
                    ✅ New presentation file uploaded: {presentationFile}
                  </p>
                </div>
              )}
            </CardBody>
          </Card>

          {/* Professional Headshot Upload */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">Professional Headshot Upload</h2>
              <p className="text-sm text-gray-600">
                Test image upload with preview functionality
              </p>
            </CardHeader>
            <CardBody>
              <FileUpload
                label="Professional Headshot"
                description="Upload your professional headshot (JPG, PNG, WebP - max 5MB)"
                bucket="iepa-presentations"
                folder="speaker-headshots"
                maxSize={5242880} // 5MB
                allowedTypes={['image/jpeg', 'image/png', 'image/webp']}
                accept=".jpg,.jpeg,.png,.webp"
                onFileUpload={handleHeadshotUpload}
                placeholder="Upload your professional headshot"
                existingFileUrl={existingHeadshot}
                existingFileName="current-headshot.jpg"
                showPreview={true}
                allowDownload={true}
                allowView={true}
                onRemoveExisting={handleRemoveExistingHeadshot}
              />
              
              {headshotFile && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-800">
                    ✅ New headshot uploaded: {headshotFile}
                  </p>
                </div>
              )}
            </CardBody>
          </Card>

          {/* No Existing File Example */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">New File Upload (No Existing File)</h2>
              <p className="text-sm text-gray-600">
                Test upload component when no existing file is present
              </p>
            </CardHeader>
            <CardBody>
              <FileUpload
                label="New Document"
                description="Upload a new document (PDF, DOC, DOCX - max 10MB)"
                bucket="iepa-presentations"
                folder="test-documents"
                maxSize={10485760} // 10MB
                allowedTypes={[
                  'application/pdf',
                  'application/msword',
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                ]}
                accept=".pdf,.doc,.docx"
                onFileUpload={(url, file) => console.log('New document:', { url, file })}
                placeholder="Upload your document"
                allowDownload={true}
                allowView={true}
              />
            </CardBody>
          </Card>

          {/* Back Button */}
          <div className="flex justify-center">
            <Button
              onClick={() => window.history.back()}
              variant="outline"
              className="px-6"
            >
              ← Back to Previous Page
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
