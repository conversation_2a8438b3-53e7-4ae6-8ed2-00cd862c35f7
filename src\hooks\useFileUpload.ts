import { useState, useCallback } from 'react';
import { supabase } from '@/lib/supabase';

export interface FileUploadOptions {
  bucket: string;
  folder?: string;
  maxSize?: number; // in bytes
  allowedTypes?: string[];
}

export interface FileUploadState {
  uploading: boolean;
  progress: number;
  error: string | null;
  url: string | null;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

export function useFileUpload(options: FileUploadOptions) {
  const [state, setState] = useState<FileUploadState>({
    uploading: false,
    progress: 0,
    error: null,
    url: null,
  });

  const validateFile = useCallback(
    (file: File): FileValidationResult => {
      // Check file size
      if (options.maxSize && file.size > options.maxSize) {
        const maxSizeMB = Math.round(options.maxSize / (1024 * 1024));
        return {
          isValid: false,
          error: `File size must be less than ${maxSizeMB}MB`,
        };
      }

      // Check file type
      if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
        const allowedExtensions = options.allowedTypes
          .map(type => {
            switch (type) {
              case 'application/pdf':
                return 'PDF';
              case 'application/vnd.ms-powerpoint':
                return 'PPT';
              case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                return 'PPTX';
              case 'application/msword':
                return 'DOC';
              case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return 'DOCX';
              case 'image/jpeg':
                return 'JPG';
              case 'image/png':
                return 'PNG';
              case 'image/webp':
                return 'WebP';
              default:
                return type;
            }
          })
          .join(', ');

        return {
          isValid: false,
          error: `File type not allowed. Accepted formats: ${allowedExtensions}`,
        };
      }

      return { isValid: true };
    },
    [options.maxSize, options.allowedTypes]
  );

  const uploadFile = useCallback(
    async (file: File, customFileName?: string): Promise<string | null> => {
      // Validate file
      const validation = validateFile(file);
      if (!validation.isValid) {
        setState(prev => ({ ...prev, error: validation.error || null }));
        return null;
      }

      setState(prev => ({
        ...prev,
        uploading: true,
        progress: 0,
        error: null,
        url: null,
      }));

      try {
        // Get current user for folder structure
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
          throw new Error('User not authenticated. Please log in and try again.');
        }

        // Generate unique filename
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const fileExtension = file.name.split('.').pop();
        const fileName =
          customFileName || `${timestamp}-${randomString}.${fileExtension}`;

        // Create file path with user ID as first folder (required by RLS policies)
        const filePath = options.folder
          ? `${user.id}/${options.folder}/${fileName}`
          : `${user.id}/${fileName}`;

        console.log('Uploading file to path:', filePath);

        // Upload file to Supabase Storage
        const { data, error } = await supabase.storage
          .from(options.bucket)
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: false,
          });

        if (error) {
          console.error('Storage upload error:', error);
          throw error;
        }

        // Check if bucket is public and generate appropriate URL
        let fileUrl: string;

        // For public buckets, generate public URL directly
        if (options.bucket === 'iepa-conference-docs') {
          const { data: publicUrlData } = supabase.storage
            .from(options.bucket)
            .getPublicUrl(data.path);

          fileUrl = publicUrlData.publicUrl;
        } else {
          // For private buckets, get signed URL (expires in 1 hour)
          const { data: signedUrlData, error: signedUrlError } = await supabase.storage
            .from(options.bucket)
            .createSignedUrl(data.path, 3600); // 1 hour expiry

          if (signedUrlError) {
            console.error('Error creating signed URL:', signedUrlError);
            throw signedUrlError;
          }

          fileUrl = signedUrlData.signedUrl;
        }

        setState(prev => ({
          ...prev,
          uploading: false,
          progress: 100,
          url: fileUrl,
        }));

        return fileUrl;
      } catch (error) {
        console.error('File upload error:', error);
        setState(prev => ({
          ...prev,
          uploading: false,
          progress: 0,
          error:
            error instanceof Error
              ? error.message
              : 'An error occurred during file upload',
        }));
        return null;
      }
    },
    [options, validateFile]
  );

  const resetState = useCallback(() => {
    setState({
      uploading: false,
      progress: 0,
      error: null,
      url: null,
    });
  }, []);

  return {
    ...state,
    uploadFile,
    validateFile,
    resetState,
  };
}
