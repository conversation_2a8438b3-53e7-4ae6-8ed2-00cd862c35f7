{"$schema": "http://json-schema.org/draft-07/schema#", "title": "IEPA 2025 Speaker Registration Schema", "description": "Form schema for IEPA 2025 Conference speaker registration", "type": "object", "properties": {"personalInfo": {"type": "object", "title": "Personal Information", "properties": {"firstName": {"type": "string", "title": "First Name", "minLength": 1}, "lastName": {"type": "string", "title": "Last Name", "minLength": 1}, "email": {"type": "string", "title": "Email Address", "description": "Primary contact email for speaker communications", "format": "email"}}, "required": ["firstName", "lastName", "email"]}, "contactInfo": {"type": "object", "title": "Contact Information", "properties": {"phoneNumber": {"type": "string", "title": "Phone Number", "description": "Contact phone number", "pattern": "^[\\d\\s\\-\\(\\)\\+\\.]+$"}, "preferredContactMethod": {"type": "string", "title": "Preferred Contact Method", "enum": ["email", "phone"], "enumNames": ["Email", "Phone"], "default": "email"}}, "required": ["phoneNumber", "preferredContactMethod"]}, "professionalInfo": {"type": "object", "title": "Professional Information", "properties": {"organizationName": {"type": "string", "title": "Organization Name", "description": "Your current organization or affiliation", "minLength": 1}, "jobTitle": {"type": "string", "title": "Job Title", "description": "Your current position or title", "minLength": 1}}, "required": ["organizationName", "jobTitle"]}, "eventOptions": {"type": "object", "title": "Event Options", "properties": {"nightOne": {"type": "boolean", "title": "Night One: Monday, September 15, 2025", "description": "Monday night lodging - Arrival day and welcome dinner", "default": true}, "nightTwo": {"type": "boolean", "title": "Night Two: Tuesday, September 16, 2025", "description": "Tuesday night lodging - Main conference day and networking dinner", "default": true}, "attendingGolf": {"type": "boolean", "title": "Are you attending the golf tournament?", "description": "$200 Fee", "default": false}}, "required": ["nightOne", "nightTwo", "<PERSON><PERSON><PERSON><PERSON>"]}, "presentationInfo": {"type": "object", "title": "Presentation Information", "properties": {"presentationTitle": {"type": "string", "title": "Presentation Title", "description": "Title of your presentation or session", "minLength": 5, "maxLength": 200}, "presentationAbstract": {"type": "string", "title": "Presentation Abstract", "description": "Brief description of your presentation content (100-500 words)", "minLength": 100, "maxLength": 2500}, "presentationFile": {"type": "string", "title": "Presentation File", "description": "Upload your presentation file (PDF, PPT, PPTX, max 50MB)", "format": "uri", "_validation": {"allowedTypes": ["application/pdf", "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation"], "maxSize": 52428800, "required": false}}, "bio": {"type": "string", "title": "Speaker Biography", "description": "Please provide a brief professional biography (50-500 words)", "minLength": 50, "maxLength": 2500}, "headshot": {"type": "string", "title": "Professional Headshot", "description": "Upload a professional headshot (JPG, PNG, max 5MB)", "format": "uri", "_validation": {"allowedTypes": ["image/jpeg", "image/png", "image/webp"], "maxSize": 5242880, "required": false}}}, "required": ["presentationTitle", "presentationAbstract", "bio"]}}, "required": ["personalInfo", "contactInfo", "professionalInfo", "eventOptions", "presentationInfo"], "additionalProperties": false, "_metadata": {"version": "1.1.0", "lastUpdated": "2025-01-30", "conferenceYear": 2025, "notes": "Updated for Task 2.3: Added contact information, enhanced presentation requirements, improved file validation", "dependencies": {"schemaUtils": "src/utils/schema-utils.ts"}, "validationRules": {"email": "Must be valid email format", "phoneNumber": "Must match phone pattern", "presentationFile": "PDF, PPT, PPTX only, max 50MB", "headshot": "JPG, PNG, WebP only, max 5MB", "bio": "50-500 words required", "presentationAbstract": "100-500 words required"}, "fileUploadLimits": {"presentationFile": {"maxSize": "50MB", "allowedTypes": ["PDF", "PPT", "PPTX"]}, "headshot": {"maxSize": "5MB", "allowedTypes": ["JPG", "PNG", "WebP"]}}}}