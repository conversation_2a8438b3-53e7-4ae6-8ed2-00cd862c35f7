'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>eader, CardContent } from '@/components/ui';
import { CONFERENCE_DATES, CONFERENCE_YEAR } from '@/lib/conference-config';
import { ConferenceHighlights } from '@/components/conference/ConferenceHighlights';
import { ConferenceAgenda } from '@/components/conference/ConferenceAgenda';
import { ConferenceResources } from '@/components/conference/ConferenceResources';
import Link from 'next/link';
import { MapPin, Calendar, Clock, Users, Star, Phone, ArrowRight } from 'lucide-react';

export default function AboutPage() {
  return (
    <div className="iepa-container">
      {/* Hero Section */}
      <section className="iepa-section text-center bg-gradient-to-br from-[var(--iepa-gray-50)] to-[var(--iepa-gray-100)] rounded-2xl mx-4 mb-8">
        <div className="max-w-4xl mx-auto py-16">
          <div className="mb-6">
            <Calendar className="w-16 h-16 mx-auto mb-4 text-[var(--iepa-primary-blue)]" />
            <h1 className="iepa-heading-1 mb-4 bg-gradient-to-r from-[var(--iepa-primary-blue)] to-[var(--iepa-secondary-green)] bg-clip-text text-transparent">
              IEPA {CONFERENCE_YEAR} Annual Meeting
            </h1>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 mb-6 shadow-lg">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Clock className="w-5 h-5 text-[var(--iepa-primary-blue)]" />
              <p className="iepa-body-large font-semibold text-[var(--iepa-gray-800)]">
                {CONFERENCE_DATES.startDate.displayDate} -{' '}
                {CONFERENCE_DATES.endDate.displayDate}
              </p>
            </div>
            <div className="flex items-center justify-center gap-2 text-[var(--iepa-gray-600)]">
              <MapPin className="w-4 h-4" />
              <p className="iepa-body">
                Stanford Sierra Conference Center, Lake Tahoe
              </p>
            </div>
          </div>

          <p className="iepa-body mb-8 max-w-2xl mx-auto text-[var(--iepa-gray-700)] leading-relaxed">
            Join environmental professionals from across the region for three
            days of learning, networking, and collaboration at the premier
            environmental protection conference in a stunning mountain setting.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register">
              <Button
                size="lg"
                className="bg-[var(--iepa-secondary-green)] hover:bg-[var(--iepa-secondary-green-dark)] text-white shadow-lg font-semibold"
              >
                Register Now
              </Button>
            </Link>
            <Link href="/contact">
              <Button
                variant="outline"
                size="lg"
                className="border-[var(--iepa-primary-blue)] text-[var(--iepa-primary-blue)] hover:bg-[var(--iepa-primary-blue)] hover:text-white font-semibold"
              >
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Key Information Cards */}
      <section className="iepa-section">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <Card className="text-center hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border-[var(--iepa-gray-200)] hover:border-[var(--iepa-primary-blue)]/20 bg-white">
              <CardContent className="p-6">
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 rounded-full flex items-center justify-center bg-gradient-to-br from-[var(--iepa-primary-blue)] to-[var(--iepa-accent-teal)] shadow-lg">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                </div>
                <h3 className="iepa-heading-3 mb-2 text-[var(--iepa-primary-blue)]">100+ Attendees</h3>
                <p className="iepa-body text-[var(--iepa-gray-600)]">
                  Environmental professionals from across California and beyond
                </p>
              </CardContent>
            </Card>
            <Card className="text-center hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border-[var(--iepa-gray-200)] hover:border-[var(--iepa-secondary-green)]/20 bg-white">
              <CardContent className="p-6">
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 rounded-full flex items-center justify-center bg-gradient-to-br from-[var(--iepa-secondary-green)] to-[var(--iepa-secondary-green-light)] shadow-lg">
                    <Star className="w-8 h-8 text-white" />
                  </div>
                </div>
                <h3 className="iepa-heading-3 mb-2 text-[var(--iepa-primary-blue)]">Expert Speakers</h3>
                <p className="iepa-body text-[var(--iepa-gray-600)]">
                  Industry leaders and renowned researchers sharing insights
                </p>
              </CardContent>
            </Card>
            <Card className="text-center hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border-[var(--iepa-gray-200)] hover:border-[var(--iepa-accent-teal)]/20 bg-white">
              <CardContent className="p-6">
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 rounded-full flex items-center justify-center bg-gradient-to-br from-[var(--iepa-accent-teal)] to-[var(--iepa-primary-blue)] shadow-lg">
                    <MapPin className="w-8 h-8 text-white" />
                  </div>
                </div>
                <h3 className="iepa-heading-3 mb-2 text-[var(--iepa-primary-blue)]">Scenic Venue</h3>
                <p className="iepa-body text-[var(--iepa-gray-600)]">
                  Beautiful lakefront setting in the Sierra Nevada mountains
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Conference Overview */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <h2 className="iepa-heading-2 text-center mb-8">
            Conference Overview
          </h2>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <Card className="hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border-[var(--iepa-gray-200)] hover:border-[var(--iepa-primary-blue)]/20 bg-white">
              <CardContent className="p-8">
                <h3 className="iepa-heading-3 mb-4 text-[var(--iepa-primary-blue)]">Mission</h3>
                <p className="iepa-body text-[var(--iepa-gray-700)] leading-relaxed">
                  The IEPA Annual Meeting brings together environmental
                  professionals, policymakers, researchers, and industry leaders
                  to share knowledge, discuss emerging challenges, and develop
                  innovative solutions for environmental protection and
                  sustainability.
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border-[var(--iepa-gray-200)] hover:border-[var(--iepa-secondary-green)]/20 bg-white">
              <CardContent className="p-8">
                <h3 className="iepa-heading-3 mb-4 text-[var(--iepa-primary-blue)]">Who Should Attend</h3>
                <ul className="iepa-body space-y-3 text-[var(--iepa-gray-700)]">
                  <li className="flex items-start gap-3">
                    <span className="w-2 h-2 bg-[var(--iepa-primary-blue)] rounded-full mt-2 flex-shrink-0"></span>
                    <span>Environmental consultants and engineers</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="w-2 h-2 bg-[var(--iepa-primary-blue)] rounded-full mt-2 flex-shrink-0"></span>
                    <span>Government agency professionals</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="w-2 h-2 bg-[var(--iepa-primary-blue)] rounded-full mt-2 flex-shrink-0"></span>
                    <span>Corporate sustainability managers</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="w-2 h-2 bg-[var(--iepa-primary-blue)] rounded-full mt-2 flex-shrink-0"></span>
                    <span>Academic researchers and students</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="w-2 h-2 bg-[var(--iepa-primary-blue)] rounded-full mt-2 flex-shrink-0"></span>
                    <span>Legal and policy professionals</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="w-2 h-2 bg-[var(--iepa-primary-blue)] rounded-full mt-2 flex-shrink-0"></span>
                    <span>Technology and service providers</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Conference Highlights */}
      <ConferenceHighlights />

      {/* Conference Agenda Overview */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="iepa-heading-2 mb-4">Annual Meeting Agenda</h2>
            <p className="iepa-body text-[var(--iepa-gray-600)] mb-6">
              Get an overview of our three-day annual meeting schedule
            </p>
            <Link href="/agenda">
              <Button
                variant="outline"
                className="border-[var(--iepa-primary-blue)] text-[var(--iepa-primary-blue)] hover:bg-[var(--iepa-primary-blue)] hover:text-white"
              >
                View Full Agenda
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
          </div>

          {/* Compact agenda display */}
          <ConferenceAgenda
            showTitle={false}
            compact={true}
            className="mt-8"
          />
        </div>
      </section>

      {/* Conference Resources */}
      <ConferenceResources />

      {/* Enhanced Location Information */}
      <section
        className="iepa-section"
        style={{ backgroundColor: 'var(--iepa-gray-50)' }}
      >
        <div className="max-w-6xl mx-auto">
          <h2 className="iepa-heading-2 text-center mb-8">Location & Venue</h2>

          <div className="grid lg:grid-cols-2 gap-8">
            <Card className="hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border-[var(--iepa-gray-200)] hover:border-[var(--iepa-primary-blue)]/20 bg-white">
              <CardContent>
                <div className="text-center mb-6">
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 rounded-full flex items-center justify-center bg-gradient-to-br from-[var(--iepa-primary-blue)] to-[var(--iepa-accent-teal)] shadow-lg">
                      <MapPin className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  <h3 className="iepa-heading-3 mb-2 text-[var(--iepa-primary-blue)]">
                    Stanford Sierra Conference Center
                  </h3>
                  <p className="iepa-body text-[var(--iepa-gray-600)]">
                    Located on the beautiful shores of Fallen Leaf Lake in the
                    Sierra Nevada mountains, our venue provides an inspiring
                    setting for learning and networking.
                  </p>
                </div>

                <div className="bg-[var(--iepa-gray-50)] rounded-lg p-4 mb-4 border border-[var(--iepa-primary-blue)]/10">
                  <h4 className="font-semibold mb-2 text-[var(--iepa-primary-blue)]">
                    Contact Information
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-[var(--iepa-primary-blue)]" />
                      <span className="text-[var(--iepa-gray-700)]">
                        130 Fallen Leaf Road, Fallen Leaf Lake, CA 96151
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-[var(--iepa-primary-blue)]" />
                      <span className="text-[var(--iepa-gray-700)]">(*************</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-4 h-4 text-center text-[var(--iepa-primary-blue)]">
                        🌐
                      </span>
                      <a
                        href="http://stanfordsierra.com/"
                        className="text-[var(--iepa-primary-blue)] hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        stanfordsierra.com
                      </a>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="iepa-heading-3 mb-3 text-[var(--iepa-primary-blue)]">Venue Features</h4>
                    <ul className="iepa-body space-y-1 text-sm text-[var(--iepa-gray-700)]">
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-secondary-green)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Historic lakefront lodge</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-secondary-green)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Modern conference facilities</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-secondary-green)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Comfortable cabin lodging</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-secondary-green)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Full-service dining</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-secondary-green)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Beautiful natural surroundings</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-secondary-green)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>WiFi and business services</span>
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="iepa-heading-3 mb-3 text-[var(--iepa-primary-blue)]">
                      What&apos;s Included
                    </h4>
                    <ul className="iepa-body space-y-1 text-sm text-[var(--iepa-gray-700)]">
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-accent-teal)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Two nights lodging</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-accent-teal)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>All meals and refreshments</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-accent-teal)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Conference materials</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-accent-teal)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Access to all sessions</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-accent-teal)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Networking events</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 bg-[var(--iepa-accent-teal)] rounded-full mt-2 flex-shrink-0"></span>
                        <span>Transportation from Reno airport available</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border-[var(--iepa-gray-200)] hover:border-[var(--iepa-secondary-green)]/20 bg-white">
              <CardContent>
                <h3 className="iepa-heading-3 mb-4 text-[var(--iepa-primary-blue)]">Travel & Accommodation</h3>

                <div className="space-y-4">
                  <div className="bg-[var(--iepa-gray-50)] rounded-lg p-4 border border-[var(--iepa-secondary-green)]/10">
                    <h4 className="font-semibold mb-2 text-[var(--iepa-secondary-green)]">
                      ✈️ Getting There
                    </h4>
                    <ul className="text-sm space-y-1 text-[var(--iepa-gray-700)]">
                      <li>
                        <strong>Closest Airport:</strong> Reno-Tahoe
                        International Airport (1 hour 20 minutes)
                      </li>
                      <li>
                        <strong>Shuttle Service:</strong> Available from airport
                        (book at least one week prior)
                      </li>
                      <li>
                        <strong>Driving:</strong> Scenic drive through the
                        Sierra Nevada mountains
                      </li>
                    </ul>
                  </div>

                  <div className="bg-[var(--iepa-gray-50)] rounded-lg p-4 border border-[var(--iepa-accent-teal)]/10">
                    <h4 className="font-semibold mb-2 text-[var(--iepa-accent-teal)]">
                      🏨 Lodging
                    </h4>
                    <ul className="text-sm space-y-1 text-[var(--iepa-gray-700)]">
                      <li>
                        <strong>On-site Cabins:</strong> Rustic mountain cabins
                        with modern amenities
                      </li>
                      <li>
                        <strong>Check-in:</strong> Monday, September 15 at 3:00
                        PM
                      </li>
                      <li>
                        <strong>Check-out:</strong> Wednesday, September 17 by
                        10:00 AM
                      </li>
                      <li>
                        <strong>Parking:</strong> Limited on-site parking
                        (passes provided)
                      </li>
                    </ul>
                  </div>

                  <div className="bg-[var(--iepa-gray-50)] rounded-lg p-4 border border-[var(--iepa-primary-blue)]/10">
                    <h4 className="font-semibold mb-2 text-[var(--iepa-primary-blue)]">
                      👔 What to Bring
                    </h4>
                    <ul className="text-sm space-y-1 text-[var(--iepa-gray-700)]">
                      <li>
                        <strong>Dress Code:</strong> Business casual to casual
                      </li>
                      <li>
                        <strong>Footwear:</strong> Comfortable walking shoes for
                        dusty paths
                      </li>
                      <li>
                        <strong>Weather:</strong> Light jacket for cool mountain
                        evenings
                      </li>
                      <li>
                        <strong>Essentials:</strong> Flashlight for walking to
                        cabins after dark
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Enhanced Call to Action */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-white rounded-2xl p-8 shadow-xl border border-[var(--iepa-gray-200)]">
            <h2 className="iepa-heading-2 mb-4 text-[var(--iepa-primary-blue)]">
              Ready to Join Us?
            </h2>
            <p className="iepa-body mb-8 text-[var(--iepa-gray-700)] max-w-2xl mx-auto">
              Don&apos;t miss this opportunity to connect with environmental
              professionals, learn about the latest developments, and contribute
              to the future of environmental protection in a stunning mountain
              setting.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/register">
                <Button
                  size="lg"
                  className="bg-[var(--iepa-secondary-green)] hover:bg-[var(--iepa-secondary-green-dark)] text-white font-semibold shadow-lg"
                >
                  Register Now
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-[var(--iepa-primary-blue)] text-[var(--iepa-primary-blue)] hover:bg-[var(--iepa-primary-blue)] hover:text-white font-semibold"
                >
                  Contact Us
                </Button>
              </Link>
            </div>

            <div className="mt-8 pt-6 border-t border-[var(--iepa-gray-200)]">
              <p className="text-sm text-[var(--iepa-gray-600)]">
                Questions? Contact Jamie Parker at{' '}
                <strong className="text-[var(--iepa-gray-800)]">(916) 448-9499</strong> or{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="underline hover:no-underline text-[var(--iepa-primary-blue)] font-medium"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
