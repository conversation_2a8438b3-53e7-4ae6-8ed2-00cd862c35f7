/**
 * Authentication Debug Utilities
 * Comprehensive logging and debugging tools for auth operations
 */

import { supabase } from './supabase';
import { getDynamicAppUrl, getAuthRedirectUrl } from './port-utils';

export interface AuthDebugInfo {
  timestamp: string;
  operation: string;
  success: boolean;
  data?: unknown;
  error?: unknown;
  context?: Record<string, unknown>;
}

// Debug log storage
const debugLogs: AuthDebugInfo[] = [];

/**
 * Log auth operation for debugging
 */
export const logAuthOperation = (
  operation: string,
  success: boolean,
  data?: unknown,
  error?: unknown,
  context?: Record<string, unknown>
): void => {
  const logEntry: AuthDebugInfo = {
    timestamp: new Date().toISOString(),
    operation,
    success,
    data: data ? JSON.parse(JSON.stringify(data)) : undefined,
    error: error
      ? {
          message: error instanceof Error ? error.message : String(error),
          name: error instanceof Error ? error.name : 'Unknown',
          stack: error instanceof Error ? error.stack : undefined,
          ...(typeof error === 'object' && error !== null ? error : {}),
        }
      : undefined,
    context,
  };

  debugLogs.push(logEntry);

  // Keep only last 100 logs
  if (debugLogs.length > 100) {
    debugLogs.shift();
  }

  // Console log in development
  if (process.env.NODE_ENV === 'development') {
    console.group(`🔐 Auth Debug: ${operation}`);
    console.log('Success:', success);
    if (data) console.log('Data:', data);
    if (error) console.error('Error:', error);
    if (context) console.log('Context:', context);
    console.groupEnd();
  }
};

/**
 * Get all debug logs
 */
export const getAuthDebugLogs = (): AuthDebugInfo[] => {
  return [...debugLogs];
};

/**
 * Clear debug logs
 */
export const clearAuthDebugLogs = (): void => {
  debugLogs.length = 0;
};

/**
 * Enhanced password reset with comprehensive logging
 */
export const debugResetPassword = async (email: string) => {
  const operation = 'resetPassword';
  const context = {
    email,
    appUrl: getDynamicAppUrl(),
    timestamp: new Date().toISOString(),
  };

  logAuthOperation(operation, true, null, null, {
    ...context,
    step: 'starting',
  });

  try {
    const redirectUrl = getAuthRedirectUrl('/auth/reset-password');

    logAuthOperation(operation, true, null, null, {
      ...context,
      step: 'calling_supabase',
      redirectUrl,
    });

    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectUrl,
    });

    if (error) {
      logAuthOperation(operation, false, data, error, {
        ...context,
        step: 'supabase_error',
        redirectUrl,
      });
      return { data, error };
    }

    logAuthOperation(operation, true, data, null, {
      ...context,
      step: 'success',
      redirectUrl,
    });

    return { data, error };
  } catch (error) {
    logAuthOperation(operation, false, null, error, {
      ...context,
      step: 'exception',
    });
    return { data: null, error };
  }
};

/**
 * Debug session validation for reset password page
 */
export const debugValidateResetSession = async (
  searchParams: URLSearchParams
) => {
  const operation = 'validateResetSession';
  const context = {
    hasAccessToken: !!searchParams.get('access_token'),
    hasRefreshToken: !!searchParams.get('refresh_token'),
    hasError: !!searchParams.get('error'),
    hasErrorDescription: !!searchParams.get('error_description'),
    allParams: Object.fromEntries(searchParams.entries()),
    timestamp: new Date().toISOString(),
  };

  logAuthOperation(operation, true, null, null, {
    ...context,
    step: 'starting',
  });

  try {
    const accessToken = searchParams.get('access_token');
    const refreshToken = searchParams.get('refresh_token');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    // Check for URL errors first
    if (error) {
      logAuthOperation(
        operation,
        false,
        null,
        {
          error,
          errorDescription,
        },
        { ...context, step: 'url_error' }
      );

      return {
        valid: false,
        error: `URL Error: ${error} - ${errorDescription}`,
        context,
      };
    }

    // Check for required tokens
    if (!accessToken || !refreshToken) {
      logAuthOperation(operation, false, null, null, {
        ...context,
        step: 'missing_tokens',
      });

      return {
        valid: false,
        error: 'Missing access_token or refresh_token in URL',
        context,
      };
    }

    // Try to set the session
    logAuthOperation(operation, true, null, null, {
      ...context,
      step: 'setting_session',
    });

    const { data: sessionData, error: sessionError } =
      await supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken,
      });

    if (sessionError) {
      logAuthOperation(operation, false, sessionData, sessionError, {
        ...context,
        step: 'session_error',
      });

      return {
        valid: false,
        error: `Session Error: ${sessionError.message}`,
        context,
        sessionError,
      };
    }

    // Verify the session is valid
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      logAuthOperation(operation, false, userData, userError, {
        ...context,
        step: 'user_verification_failed',
      });

      return {
        valid: false,
        error: `User verification failed: ${userError?.message || 'No user found'}`,
        context,
        userError,
      };
    }

    logAuthOperation(
      operation,
      true,
      {
        session: sessionData,
        user: userData,
      },
      null,
      { ...context, step: 'success' }
    );

    return {
      valid: true,
      session: sessionData.session,
      user: userData.user,
      context,
    };
  } catch (error) {
    logAuthOperation(operation, false, null, error, {
      ...context,
      step: 'exception',
    });

    return {
      valid: false,
      error: `Exception: ${error instanceof Error ? error.message : 'Unknown error'}`,
      context,
      exception: error,
    };
  }
};

/**
 * Debug password update operation
 */
export const debugUpdatePassword = async (newPassword: string) => {
  const operation = 'updatePassword';
  const context = {
    passwordLength: newPassword.length,
    timestamp: new Date().toISOString(),
  };

  logAuthOperation(operation, true, null, null, {
    ...context,
    step: 'starting',
  });

  try {
    // Check current session first
    const { data: sessionData, error: sessionError } =
      await supabase.auth.getSession();

    if (sessionError || !sessionData.session) {
      logAuthOperation(operation, false, sessionData, sessionError, {
        ...context,
        step: 'no_session',
      });

      return {
        success: false,
        error: 'No valid session found for password update',
        context,
      };
    }

    logAuthOperation(operation, true, null, null, {
      ...context,
      step: 'session_valid',
      userId: sessionData.session.user.id,
    });

    // Update password
    const { data, error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      logAuthOperation(operation, false, data, error, {
        ...context,
        step: 'update_error',
      });

      return {
        success: false,
        error: error.message,
        context,
        supabaseError: error,
      };
    }

    logAuthOperation(operation, true, data, null, {
      ...context,
      step: 'success',
    });

    return {
      success: true,
      data,
      context,
    };
  } catch (error) {
    logAuthOperation(operation, false, null, error, {
      ...context,
      step: 'exception',
    });

    return {
      success: false,
      error: `Exception: ${error instanceof Error ? error.message : 'Unknown error'}`,
      context,
      exception: error,
    };
  }
};

/**
 * Get environment info for debugging
 */
export const getAuthEnvironmentInfo = () => {
  return {
    appUrl: getDynamicAppUrl(),
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
    hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    nodeEnv: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
    userAgent:
      typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    location: typeof window !== 'undefined' ? window.location.href : 'server',
  };
};
