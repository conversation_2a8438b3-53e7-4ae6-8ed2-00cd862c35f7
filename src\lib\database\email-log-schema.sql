-- Email Log Table for IEPA Conference Registration System
-- This table tracks all emails sent via Send<PERSON><PERSON> for auditing and debugging

-- Create email log table
CREATE TABLE IF NOT EXISTS public.iepa_email_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Email details
  recipient_email VARCHAR(255) NOT NULL,
  recipient_name VARCHAR(255),
  sender_email VARCHAR(255) NOT NULL,
  sender_name <PERSON><PERSON><PERSON><PERSON>(255),
  subject VARCHAR(500) NOT NULL,
  
  -- Email classification
  email_type VARCHAR(50) NOT NULL, -- 'registration_confirmation', 'payment_confirmation', 'golf_addon', 'custom', etc.
  template_used VARCHAR(100), -- Template identifier if using templates
  
  -- Content reference (don't store full HTML for space)
  content_preview TEXT, -- First 500 chars of email content
  has_attachments BOOLEAN DEFAULT FALSE,
  
  -- Delivery status
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'sent', 'failed', 'pending'
  sendgrid_message_id VARCHAR(255), -- SendGrid message ID for tracking
  error_message TEXT, -- Error details if send failed
  
  -- Related records
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  registration_id UUID, -- Generic registration ID (could be attendee, speaker, or sponsor)
  registration_type VARCHAR(20), -- 'attendee', 'speaker', 'sponsor'
  payment_id UUID, -- Reference to iepa_payments if payment-related
  
  -- Timestamps
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for common queries
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_recipient ON public.iepa_email_log(recipient_email);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_type ON public.iepa_email_log(email_type);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_status ON public.iepa_email_log(status);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_user_id ON public.iepa_email_log(user_id);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_registration ON public.iepa_email_log(registration_id, registration_type);
CREATE INDEX IF NOT EXISTS idx_iepa_email_log_created_at ON public.iepa_email_log(created_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_iepa_email_log_updated_at 
  BEFORE UPDATE ON public.iepa_email_log 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE public.iepa_email_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Admins can view all email logs
CREATE POLICY "Admins can view all email logs" 
  ON public.iepa_email_log FOR SELECT 
  TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM public.iepa_admin_users 
      WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
      AND is_active = true
    )
  );

-- Admins can insert email logs (for manual logging if needed)
CREATE POLICY "Admins can insert email logs" 
  ON public.iepa_email_log FOR INSERT 
  TO authenticated 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.iepa_admin_users 
      WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
      AND is_active = true
    )
  );

-- Service role can do everything (for the email service)
CREATE POLICY "Service role full access" 
  ON public.iepa_email_log FOR ALL 
  TO service_role 
  USING (true) 
  WITH CHECK (true);

-- Users can view their own email logs
CREATE POLICY "Users can view their own email logs" 
  ON public.iepa_email_log FOR SELECT 
  TO authenticated 
  USING (user_id = auth.uid());

-- Add comments for documentation
COMMENT ON TABLE public.iepa_email_log IS 'Tracks all emails sent via SendGrid for the IEPA Conference registration system';
COMMENT ON COLUMN public.iepa_email_log.email_type IS 'Type of email: registration_confirmation, payment_confirmation, golf_addon, password_reset, custom, etc.';
COMMENT ON COLUMN public.iepa_email_log.status IS 'Email delivery status: pending, sent, failed';
COMMENT ON COLUMN public.iepa_email_log.sendgrid_message_id IS 'SendGrid message ID for tracking delivery status';
COMMENT ON COLUMN public.iepa_email_log.content_preview IS 'First 500 characters of email content for reference';
COMMENT ON COLUMN public.iepa_email_log.registration_type IS 'Type of registration if related: attendee, speaker, sponsor'; 