# Testing Workflow

This document describes the testing workflow to prevent client-side JavaScript errors from being committed to the repository.

## Problem Solved

Previously, client-side JavaScript errors like circular dependencies and initialization issues could be committed without detection, causing runtime errors in production. The error `Cannot access 'P' before initialization` is a common example of such issues.

## Testing Scripts

### 1. Client-Side Error Testing

```bash
npm run test:client
```

**What it does:**
- Launches a headless browser using Playwright
- Tests key admin pages for JavaScript runtime errors
- Detects critical errors like circular dependencies
- Filters out non-critical console warnings (406 errors, hydration warnings)
- Requires the development server to be running on port 6969

**Pages tested:**
- Admin Attendees List
- Admin Attendee Detail
- Admin Attendees with Meal Filter
- Admin Speakers
- Admin Dashboard

### 2. Build Testing

```bash
npm run test:build
```

**What it does:**
- Runs `npm run build` to test production build
- Catches TypeScript compilation errors
- Validates that all imports and dependencies resolve correctly

### 3. Comprehensive Pre-Commit Testing

```bash
npm run test:pre-commit
```

**What it does:**
- Runs build test first (includes TypeScript validation)
- Runs client-side error testing if dev server is available
- Provides clear feedback on what passed/failed
- Gracefully skips client tests if server isn't running

## Usage Workflow

### Before Committing Changes

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Run comprehensive tests:**
   ```bash
   npm run test:pre-commit
   ```

3. **If tests pass, commit your changes:**
   ```bash
   git add .
   git commit -m "your commit message"
   ```

### Automated Pre-Commit Hooks

The existing lint-staged configuration will automatically:
- Run ESLint with auto-fix
- Run Prettier formatting
- Catch basic linting issues

The new testing scripts provide additional protection against runtime errors.

## Error Detection

### Critical Errors (Will Fail Tests)
- `Cannot access [variable] before initialization` - Circular dependencies
- JavaScript syntax errors
- Module import/export errors
- React component initialization errors

### Non-Critical Errors (Filtered Out)
- 406 HTTP status errors from API calls
- HTML validation warnings
- Hydration warnings (expected in development)

## Troubleshooting

### "Server not running" Message
If you see this message, start the development server:
```bash
npm run dev
```

Then run the tests again. The build test will still run even without the server.

### Tests Failing on Existing Code
The tests focus on detecting critical runtime errors. If tests fail on existing code:
1. Check if the error is related to your changes
2. Fix any circular dependencies or initialization issues
3. Use `eslint-disable` comments for unavoidable dependency warnings

### Performance
- Build test: ~30-60 seconds
- Client test: ~15-30 seconds (when server running)
- Total pre-commit test: ~45-90 seconds

## Integration with Development

This testing workflow integrates with:
- ✅ Existing lint-staged pre-commit hooks
- ✅ ESLint and Prettier formatting
- ✅ TypeScript compilation checking
- ✅ Next.js build process
- ✅ Playwright browser testing

The goal is to catch issues early in the development process, before they reach production.
