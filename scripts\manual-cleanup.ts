#!/usr/bin/env npx tsx

/**
 * Manual Cleanup Script
 * 
 * This script manually removes specific test registrations that don't match
 * the automatic test patterns.
 */

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Remove specific test registrations
 */
async function manualCleanup(): Promise<void> {
  console.log('🧹 Manual cleanup of specific test registrations...\n');
  
  // Remove Jamie Speaker test registration
  const { data: jamieAttendee, error: fetchError } = await supabase
    .from('iepa_attendee_registrations')
    .select('*')
    .eq('email', '<EMAIL>')
    .single();
  
  if (fetchError && fetchError.code !== 'PGRST116') {
    console.error('❌ Error fetching Jamie Speaker registration:', fetchError);
    return;
  }
  
  if (jamieAttendee) {
    console.log('🔍 Found Jamie Speaker test registration:');
    console.log(`   Name: ${jamieAttendee.first_name} ${jamieAttendee.last_name}`);
    console.log(`   Email: ${jamieAttendee.email}`);
    console.log(`   Organization: ${jamieAttendee.organization}`);
    
    const { error: deleteError } = await supabase
      .from('iepa_attendee_registrations')
      .delete()
      .eq('id', jamieAttendee.id);
    
    if (deleteError) {
      console.error('❌ Error deleting Jamie Speaker registration:', deleteError);
      return;
    }
    
    console.log('✅ Deleted Jamie Speaker test registration');
  } else {
    console.log('ℹ️  Jamie Speaker test registration not found');
  }
  
  // Remove any test user profiles with speaker emails
  const { data: testProfiles, error: profileError } = await supabase
    .from('iepa_user_profiles')
    .select('*')
    .or('<EMAIL>,<EMAIL>');
  
  if (profileError) {
    console.error('❌ Error fetching test user profiles:', profileError);
    return;
  }
  
  if (testProfiles && testProfiles.length > 0) {
    console.log(`\n🔍 Found ${testProfiles.length} test user profiles:`);
    testProfiles.forEach(profile => {
      console.log(`   - ${profile.first_name} ${profile.last_name} (${profile.email})`);
    });
    
    const profileIds = testProfiles.map(p => p.id);
    
    const { error: deleteProfileError } = await supabase
      .from('iepa_user_profiles')
      .delete()
      .in('id', profileIds);
    
    if (deleteProfileError) {
      console.error('❌ Error deleting test user profiles:', deleteProfileError);
      return;
    }
    
    console.log(`✅ Deleted ${testProfiles.length} test user profiles`);
  } else {
    console.log('\nℹ️  No test user profiles found');
  }
  
  console.log('\n✅ Manual cleanup completed');
  console.log('🎯 Database is now ready for fresh testing!');
}

// Run the cleanup
if (require.main === module) {
  manualCleanup();
}

export { manualCleanup };
