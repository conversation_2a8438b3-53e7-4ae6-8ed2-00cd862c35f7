import { test, expect } from '@playwright/test';

/**
 * Performance tests for IEPA Email Center
 * Tests loading times, responsiveness, and resource usage
 */

test.describe('Email Center Performance Tests', () => {
  test('page load performance', async ({ page }) => {
    // Start timing
    const startTime = Date.now();
    
    // Navigate to email center
    await page.goto('/admin/emails');
    
    // Wait for main content to load
    await page.waitForSelector('[data-testid="email-status-summary"]');
    await page.waitForSelector('[data-testid="email-log-filters"]');
    await page.waitForSelector('[data-testid="send-email-form"]');
    
    const loadTime = Date.now() - startTime;
    
    // Assert load time is reasonable (under 3 seconds)
    expect(loadTime).toBeLessThan(3000);
    console.log(`Page load time: ${loadTime}ms`);
  });

  test('email logs loading performance', async ({ page }) => {
    await page.goto('/admin/emails');
    
    const startTime = Date.now();
    
    // Wait for email logs to load
    await page.waitForSelector('[data-testid="email-log-card"]', { timeout: 10000 });
    
    const loadTime = Date.now() - startTime;
    
    // Email logs should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
    console.log(`Email logs load time: ${loadTime}ms`);
  });

  test('filter response time', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-log-card"]');
    
    const startTime = Date.now();
    
    // Apply status filter
    await page.locator('[data-testid="status-filter"]').click();
    await page.locator('text=Sent').click();
    
    // Wait for filtered results
    await page.waitForTimeout(100); // Small delay to ensure filter is applied
    
    const filterTime = Date.now() - startTime;
    
    // Filtering should be fast (under 1 second)
    expect(filterTime).toBeLessThan(1000);
    console.log(`Filter response time: ${filterTime}ms`);
  });

  test('search performance', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-log-card"]');
    
    const searchInput = page.locator('input[placeholder*="Search"]');
    
    const startTime = Date.now();
    
    // Perform search
    await searchInput.fill('test');
    await searchInput.press('Enter');
    
    // Wait for search results
    await page.waitForTimeout(500);
    
    const searchTime = Date.now() - startTime;
    
    // Search should be fast (under 2 seconds)
    expect(searchTime).toBeLessThan(2000);
    console.log(`Search response time: ${searchTime}ms`);
  });

  test('pagination performance', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-log-card"]');
    
    const nextButton = page.locator('button:has-text("Next")');
    
    if (await nextButton.isVisible()) {
      const startTime = Date.now();
      
      await nextButton.click();
      await page.waitForSelector('[data-testid="email-log-card"]');
      
      const paginationTime = Date.now() - startTime;
      
      // Pagination should be fast (under 2 seconds)
      expect(paginationTime).toBeLessThan(2000);
      console.log(`Pagination response time: ${paginationTime}ms`);
    }
  });

  test('memory usage during scrolling', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-log-card"]');
    
    // Get initial memory usage
    const initialMetrics = await page.evaluate(() => {
      return {
        usedJSHeapSize: (performance as any).memory?.usedJSHeapSize || 0,
        totalJSHeapSize: (performance as any).memory?.totalJSHeapSize || 0,
      };
    });
    
    // Simulate scrolling through email logs
    for (let i = 0; i < 10; i++) {
      await page.keyboard.press('PageDown');
      await page.waitForTimeout(100);
    }
    
    // Get memory usage after scrolling
    const finalMetrics = await page.evaluate(() => {
      return {
        usedJSHeapSize: (performance as any).memory?.usedJSHeapSize || 0,
        totalJSHeapSize: (performance as any).memory?.totalJSHeapSize || 0,
      };
    });
    
    const memoryIncrease = finalMetrics.usedJSHeapSize - initialMetrics.usedJSHeapSize;
    const memoryIncreasePercent = (memoryIncrease / initialMetrics.usedJSHeapSize) * 100;
    
    // Memory increase should be reasonable (less than 50% increase)
    expect(memoryIncreasePercent).toBeLessThan(50);
    console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB (${memoryIncreasePercent.toFixed(2)}%)`);
  });

  test('network resource loading', async ({ page }) => {
    // Track network requests
    const requests: any[] = [];
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType(),
      });
    });
    
    const responses: any[] = [];
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status(),
        size: response.headers()['content-length'] || 0,
      });
    });
    
    await page.goto('/admin/emails');
    await page.waitForLoadState('networkidle');
    
    // Analyze network requests
    const apiRequests = requests.filter(req => req.url.includes('/api/'));
    const staticRequests = requests.filter(req => 
      req.resourceType === 'stylesheet' || 
      req.resourceType === 'script' || 
      req.resourceType === 'image'
    );
    
    console.log(`Total requests: ${requests.length}`);
    console.log(`API requests: ${apiRequests.length}`);
    console.log(`Static resource requests: ${staticRequests.length}`);
    
    // Check for failed requests
    const failedResponses = responses.filter(res => res.status >= 400);
    expect(failedResponses.length).toBe(0);
    
    // API requests should be minimal for initial load
    expect(apiRequests.length).toBeLessThan(10);
  });

  test('large dataset performance', async ({ page }) => {
    // This test would require setting up a large dataset
    // For now, we'll simulate by testing with maximum page size
    
    await page.goto('/admin/emails?limit=100');
    
    const startTime = Date.now();
    await page.waitForSelector('[data-testid="email-log-card"]');
    
    // Count loaded email cards
    const emailCards = await page.locator('[data-testid="email-log-card"]').count();
    const loadTime = Date.now() - startTime;
    
    console.log(`Loaded ${emailCards} email cards in ${loadTime}ms`);
    
    // Should handle large datasets efficiently
    if (emailCards > 50) {
      expect(loadTime).toBeLessThan(5000);
    }
  });

  test('concurrent user simulation', async ({ browser }) => {
    // Simulate multiple users accessing the email center
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext(),
      browser.newContext(),
    ]);
    
    const pages = await Promise.all(
      contexts.map(context => context.newPage())
    );
    
    const startTime = Date.now();
    
    // All users navigate to email center simultaneously
    await Promise.all(
      pages.map(page => page.goto('/admin/emails'))
    );
    
    // Wait for all pages to load
    await Promise.all(
      pages.map(page => page.waitForSelector('[data-testid="email-status-summary"]'))
    );
    
    const loadTime = Date.now() - startTime;
    
    console.log(`Concurrent load time for 3 users: ${loadTime}ms`);
    
    // Should handle concurrent users efficiently
    expect(loadTime).toBeLessThan(10000);
    
    // Cleanup
    await Promise.all(contexts.map(context => context.close()));
  });

  test('mobile performance', async ({ browser }) => {
    const context = await browser.newContext({
      ...require('@playwright/test').devices['iPhone 12'],
    });
    
    const page = await context.newPage();
    
    const startTime = Date.now();
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-status-summary"]');
    
    const mobileLoadTime = Date.now() - startTime;
    
    console.log(`Mobile load time: ${mobileLoadTime}ms`);
    
    // Mobile should load within reasonable time
    expect(mobileLoadTime).toBeLessThan(5000);
    
    await context.close();
  });

  test('refresh performance', async ({ page }) => {
    await page.goto('/admin/emails');
    await page.waitForSelector('[data-testid="email-log-card"]');
    
    // Test refresh button performance
    const refreshButton = page.locator('button:has-text("Refresh")');
    
    const startTime = Date.now();
    await refreshButton.click();
    await page.waitForSelector('[data-testid="email-log-card"]');
    
    const refreshTime = Date.now() - startTime;
    
    console.log(`Refresh time: ${refreshTime}ms`);
    
    // Refresh should be fast
    expect(refreshTime).toBeLessThan(3000);
  });
});
