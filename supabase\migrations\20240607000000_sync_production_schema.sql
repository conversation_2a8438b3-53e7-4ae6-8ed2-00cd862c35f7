-- IEPA Annual Meeting Registration - Sync Production Schema
-- This migration adds missing columns found in production to match the schema

-- Add missing columns to iepa_user_profiles
ALTER TABLE iepa_user_profiles 
ADD COLUMN IF NOT EXISTS country TEXT;

-- Add missing columns to iepa_organizations
ALTER TABLE iepa_organizations 
ADD COLUMN IF NOT EXISTS industry TEXT;

-- Add missing columns to iepa_historical_registrations
ALTER TABLE iepa_historical_registrations 
ADD COLUMN IF NOT EXISTS golf_cell_number TEXT;

-- Add missing columns to iepa_attendee_registrations
ALTER TABLE iepa_attendee_registrations 
ADD COLUMN IF NOT EXISTS night_one BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS night_two BOOLEAN DEFAULT FALSE;

-- Fix iepa_speaker_registrations full_name column to allow manual insertion
-- Drop the generated column and recreate as a regular column
ALTER TABLE iepa_speaker_registrations 
DROP COLUMN IF EXISTS full_name CASCADE;

ALTER TABLE iepa_speaker_registrations 
ADD COLUMN full_name TEXT;

-- Create a trigger to auto-populate full_name if not provided
CREATE OR REPLACE FUNCTION update_speaker_full_name()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update full_name if it's null or empty
    IF NEW.full_name IS NULL OR NEW.full_name = '' THEN
        NEW.full_name = NEW.first_name || ' ' || NEW.last_name;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_speaker_full_name_trigger
    BEFORE INSERT OR UPDATE ON iepa_speaker_registrations
    FOR EACH ROW EXECUTE FUNCTION update_speaker_full_name();

-- Fix iepa_attendee_registrations full_name column similarly
ALTER TABLE iepa_attendee_registrations 
DROP COLUMN IF EXISTS full_name CASCADE;

ALTER TABLE iepa_attendee_registrations 
ADD COLUMN full_name TEXT;

-- Create a trigger to auto-populate full_name if not provided
CREATE OR REPLACE FUNCTION update_attendee_full_name()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update full_name if it's null or empty
    IF NEW.full_name IS NULL OR NEW.full_name = '' THEN
        NEW.full_name = NEW.first_name || ' ' || NEW.last_name;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_attendee_full_name_trigger
    BEFORE INSERT OR UPDATE ON iepa_attendee_registrations
    FOR EACH ROW EXECUTE FUNCTION update_attendee_full_name();

-- Create iepa_golf_registrations table if it doesn't exist
CREATE TABLE IF NOT EXISTS iepa_golf_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    attendee_registration_id UUID REFERENCES iepa_attendee_registrations(id) ON DELETE CASCADE,
    golf_participation BOOLEAN DEFAULT TRUE,
    golf_club_rental BOOLEAN DEFAULT FALSE,
    golf_club_handedness TEXT DEFAULT '',
    golf_cell_number TEXT,
    golf_total DECIMAL(10,2) DEFAULT 200.00,
    golf_club_rental_total DECIMAL(10,2) DEFAULT 0,
    payment_status TEXT DEFAULT 'pending',
    payment_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS to golf registrations
ALTER TABLE iepa_golf_registrations ENABLE ROW LEVEL SECURITY;

-- Add updated_at trigger for golf registrations
CREATE TRIGGER update_iepa_golf_registrations_updated_at
    BEFORE UPDATE ON iepa_golf_registrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add any missing indexes for performance
CREATE INDEX IF NOT EXISTS idx_iepa_user_profiles_email ON iepa_user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_iepa_user_profiles_user_id ON iepa_user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_iepa_attendee_registrations_email ON iepa_attendee_registrations(email);
CREATE INDEX IF NOT EXISTS idx_iepa_attendee_registrations_user_id ON iepa_attendee_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_iepa_speaker_registrations_email ON iepa_speaker_registrations(email);
CREATE INDEX IF NOT EXISTS idx_iepa_speaker_registrations_user_id ON iepa_speaker_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_iepa_sponsor_registrations_user_id ON iepa_sponsor_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_iepa_payments_user_id ON iepa_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_iepa_payments_registration_id ON iepa_payments(registration_id);
CREATE INDEX IF NOT EXISTS idx_iepa_historical_registrations_user_id ON iepa_historical_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_iepa_golf_registrations_user_id ON iepa_golf_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_iepa_golf_registrations_attendee_id ON iepa_golf_registrations(attendee_registration_id);
