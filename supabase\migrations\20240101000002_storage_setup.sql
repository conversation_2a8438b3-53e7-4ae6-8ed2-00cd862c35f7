-- Storage setup for IEPA Annual Meeting Registration System

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public)
VALUES
    ('iepa-presentations', 'iepa-presentations', false),
    ('iepa-sponsor-assets', 'iepa-sponsor-assets', false),
    ('iepa-documents', 'iepa-documents', false)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for presentations
CREATE POLICY "Users can upload their own presentations"
    ON storage.objects FOR INSERT
    WITH CHECK (bucket_id = 'iepa-presentations' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own presentations"
    ON storage.objects FOR SELECT
    USING (bucket_id = 'iepa-presentations' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own presentations"
    ON storage.objects FOR DELETE
    USING (bucket_id = 'iepa-presentations' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update their own presentations"
    ON storage.objects FOR UPDATE
    USING (bucket_id = 'iepa-presentations' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for sponsor assets
CREATE POLICY "Users can upload their own sponsor assets"
    ON storage.objects FOR INSERT
    WITH CHECK (bucket_id = 'iepa-sponsor-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own sponsor assets"
    ON storage.objects FOR SELECT
    USING (bucket_id = 'iepa-sponsor-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own sponsor assets"
    ON storage.objects FOR DELETE
    USING (bucket_id = 'iepa-sponsor-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update their own sponsor assets"
    ON storage.objects FOR UPDATE
    USING (bucket_id = 'iepa-sponsor-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for PDF documents
CREATE POLICY "Users can view their own PDF documents"
    ON storage.objects FOR SELECT
    USING (bucket_id = 'iepa-documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own PDF documents"
    ON storage.objects FOR DELETE
    USING (bucket_id = 'iepa-documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update their own PDF documents"
    ON storage.objects FOR UPDATE
    USING (bucket_id = 'iepa-documents' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Admin can access all files
CREATE POLICY "Service role can manage all presentations"
    ON storage.objects FOR ALL
    USING (bucket_id = 'iepa-presentations' AND auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all sponsor assets"
    ON storage.objects FOR ALL
    USING (bucket_id = 'iepa-sponsor-assets' AND auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all PDF documents"
    ON storage.objects FOR ALL
    USING (bucket_id = 'iepa-documents' AND auth.jwt() ->> 'role' = 'service_role');
