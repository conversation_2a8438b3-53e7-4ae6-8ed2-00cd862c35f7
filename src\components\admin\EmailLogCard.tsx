'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from '@/components/ui';
import {
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiMail,
  FiUser,
  FiCalendar,
  FiExternalLink,
  FiCopy,
  <PERSON>Paper<PERSON><PERSON>,
  <PERSON>Eye,
  FiRefreshCw,
} from 'react-icons/fi';

interface EmailLog {
  id: string;
  recipient_email: string;
  recipient_name?: string;
  sender_email: string;
  sender_name?: string;
  subject: string;
  email_type: string;
  status: 'sent' | 'failed' | 'pending';
  sent_at?: string;
  created_at: string;
  updated_at?: string;
  registration_type?: string;
  registration_id?: string;
  user_id?: string;
  payment_id?: string;
  error_message?: string;
  content_preview?: string;
  has_attachments?: boolean;
  sendgrid_message_id?: string;
}

interface EmailLogCardProps {
  email: EmailLog;
  onCopySendGridId?: (id: string) => void;
  onViewContent?: (email: EmailLog) => void;
  onRetry?: (email: EmailLog) => void;
  className?: string;
}

export default function EmailLogCard({
  email,
  onCopySendGridId,
  onViewContent,
  onRetry,
  className = '',
}: EmailLogCardProps) {
  const [copying, setCopying] = useState(false);

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'sent':
        return {
          icon: FiCheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          label: 'Sent',
        };
      case 'failed':
        return {
          icon: FiXCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          label: 'Failed',
        };
      case 'pending':
        return {
          icon: FiClock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          label: 'Pending',
        };
      default:
        return {
          icon: FiMail,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          label: 'Unknown',
        };
    }
  };

  const statusConfig = getStatusConfig(email.status);
  const StatusIcon = statusConfig.icon;

  const handleCopySendGridId = async () => {
    if (!email.sendgrid_message_id || !onCopySendGridId) return;

    setCopying(true);
    try {
      await navigator.clipboard.writeText(email.sendgrid_message_id);
      onCopySendGridId(email.sendgrid_message_id);
    } catch (error) {
      console.error('Failed to copy SendGrid ID:', error);
    } finally {
      setCopying(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const formatEmailType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <Card
      className={`hover:bg-gray-50 transition-colors focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 ${className}`}
      role="article"
      aria-label={`Email: ${email.subject} to ${email.recipient_email}`}
    >
      <CardBody className="p-4">
        {/* Header Row */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 sm:gap-0 mb-3">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <StatusIcon className={`w-4 h-4 ${statusConfig.color} flex-shrink-0`} />
            <h3 className="font-medium text-sm text-gray-900 truncate" id={`email-subject-${email.id}`}>
              {email.subject}
            </h3>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0 flex-wrap">
            <Badge
              variant="outline"
              className={`text-xs ${statusConfig.color} ${statusConfig.bgColor} ${statusConfig.borderColor}`}
            >
              {statusConfig.label}
            </Badge>
            {email.has_attachments && (
              <Badge variant="secondary" className="text-xs">
                <FiPaperclip className="w-3 h-3 mr-1" />
                <span className="hidden sm:inline">Attachment</span>
                <span className="sm:hidden">📎</span>
              </Badge>
            )}
          </div>
        </div>

        {/* Email Details */}
        <div className="space-y-2 text-sm">
          {/* Recipient */}
          <div className="flex items-center gap-2">
            <FiUser className="w-3 h-3 text-gray-400 flex-shrink-0" />
            <span className="text-gray-600">To:</span>
            <span className="text-gray-900 truncate">
              {email.recipient_name ? (
                <>
                  {email.recipient_name} <span className="text-gray-500">({email.recipient_email})</span>
                </>
              ) : (
                email.recipient_email
              )}
            </span>
          </div>

          {/* Sender */}
          <div className="flex items-center gap-2">
            <FiMail className="w-3 h-3 text-gray-400 flex-shrink-0" />
            <span className="text-gray-600">From:</span>
            <span className="text-gray-900 truncate">
              {email.sender_name ? (
                <>
                  {email.sender_name} <span className="text-gray-500">({email.sender_email})</span>
                </>
              ) : (
                email.sender_email
              )}
            </span>
          </div>

          {/* Content Preview */}
          {email.content_preview && (
            <div className="text-gray-600 text-xs bg-gray-50 p-2 rounded border">
              <span className="font-medium">Preview:</span> {email.content_preview}
            </div>
          )}

          {/* Error Message */}
          {email.status === 'failed' && email.error_message && (
            <div className="text-red-600 text-xs bg-red-50 p-2 rounded border border-red-200">
              <span className="font-medium">Error:</span> {email.error_message}
            </div>
          )}
        </div>

        {/* Footer Row */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-2 mt-3 pt-3 border-t border-gray-100">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <FiCalendar className="w-3 h-3 flex-shrink-0" />
              <span className="truncate">
                {email.sent_at ? formatDate(email.sent_at) : formatDate(email.created_at)}
              </span>
            </div>
            <Badge variant="outline" className="text-xs self-start">
              {formatEmailType(email.email_type)}
            </Badge>
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            {/* SendGrid ID */}
            {email.sendgrid_message_id && (
              <div className="flex items-center gap-1">
                <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                  <FiExternalLink className="w-3 h-3 mr-1" />
                  <span className="hidden sm:inline">SendGrid</span>
                  <span className="sm:hidden">SG</span>
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopySendGridId}
                  disabled={copying}
                  className="h-8 w-8 sm:h-6 sm:w-auto sm:px-2 text-xs p-0 sm:p-1"
                  title={`Copy SendGrid ID: ${email.sendgrid_message_id}`}
                  aria-label={`Copy SendGrid ID ${email.sendgrid_message_id} to clipboard`}
                >
                  <FiCopy
                    className={`w-3 h-3 ${copying ? 'animate-pulse' : ''}`}
                    aria-hidden="true"
                  />
                  <span className="sr-only">Copy SendGrid ID</span>
                </Button>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center gap-1">
              {onViewContent && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewContent(email)}
                  className="h-8 w-8 sm:h-6 sm:w-auto sm:px-2 text-xs p-0 sm:p-1"
                  title="View full email content"
                  aria-label={`View full content of email: ${email.subject}`}
                >
                  <FiEye className="w-3 h-3" aria-hidden="true" />
                  <span className="sr-only">View content</span>
                </Button>
              )}

              {email.status === 'failed' && onRetry && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onRetry(email)}
                  className="h-8 w-8 sm:h-6 sm:w-auto sm:px-2 text-xs p-0 sm:p-1 text-orange-600 border-orange-200 hover:bg-orange-50"
                  title="Retry sending this email"
                  aria-label={`Retry sending failed email: ${email.subject}`}
                >
                  <FiRefreshCw className="w-3 h-3" aria-hidden="true" />
                  <span className="sr-only">Retry email</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
