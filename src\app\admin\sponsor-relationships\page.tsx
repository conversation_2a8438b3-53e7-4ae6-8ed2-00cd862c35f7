'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { supabase } from '@/lib/supabase';
import { FiUsers, FiStar, FiLink, FiX, FiSearch, FiRefreshCw } from 'react-icons/fi';


interface SponsorDomain {
  id: string;
  domain: string;
  sponsor_name: string;
  discount_value: number;
  discount_type: string;
  auto_discount_code: string;
  is_active: boolean;
  current_uses: number;
  max_uses: number | null;
  sponsor: {
    id: string;
    sponsor_name: string;
    payment_status: string;
  };
}

interface SponsorAttendee {
  id: string;
  full_name: string;
  email: string;
  organization: string;
  payment_status: string;
  sponsor_discount_code: string;
  sponsor: {
    id: string;
    sponsor_name: string;
  } | null;
}

export default function SponsorRelationshipsPage() {
  const [sponsorDomains, setSponsorDomains] = useState<SponsorDomain[]>([]);
  const [sponsorAttendees, setSponsorAttendees] = useState<SponsorAttendee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch sponsor domains and attendees
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch sponsor domains
      const { data: domains, error: domainsError } = await supabase
        .from('iepa_sponsor_domains')
        .select(`
          *,
          sponsor:iepa_sponsor_registrations!sponsor_id(
            id,
            sponsor_name,
            payment_status
          )
        `)
        .order('created_at', { ascending: false });

      if (domainsError) throw domainsError;

      // Fetch sponsor attendees
      const { data: attendees, error: attendeesError } = await supabase
        .from('iepa_attendee_registrations')
        .select(`
          id,
          full_name,
          email,
          organization,
          payment_status,
          sponsor_discount_code,
          sponsor:iepa_sponsor_registrations!sponsor_id(
            id,
            sponsor_name
          )
        `)
        .eq('is_sponsor_attendee', true)
        .order('created_at', { ascending: false });

      if (attendeesError) throw attendeesError;

      setSponsorDomains(domains || []);
      setSponsorAttendees((attendees || []) as unknown as SponsorAttendee[]);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Filter data based on search term
  const filteredDomains = sponsorDomains.filter(
    domain =>
      domain.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
      domain.sponsor_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAttendees = sponsorAttendees.filter(
    attendee =>
      attendee.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attendee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attendee.organization.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (attendee.sponsor?.sponsor_name || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Create new sponsor domain (currently unused)
  // const createSponsorDomain = async (sponsorId: string, autoCreate: boolean = true) => {
  //   try {
  //     const response = await fetch('/api/admin/sponsor-domains', {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({
  //         sponsorId,
  //         autoCreate,
  //       }),
  //     });

  //     const result = await response.json();

  //     if (result.success) {
  //       await fetchData(); // Refresh data
  //       alert('Sponsor domain created successfully!');
  //     } else {
  //       alert(`Error: ${result.error}`);
  //     }
  //   } catch (error) {
  //     console.error('Error creating sponsor domain:', error);
  //     alert('Failed to create sponsor domain');
  //   }
  // };

  // Toggle domain active status
  const toggleDomainStatus = async (domainId: string, isActive: boolean) => {
    try {
      const response = await fetch('/api/admin/sponsor-domains', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: domainId,
          isActive: !isActive,
        }),
      });

      const result = await response.json();

      if (result.success) {
        await fetchData(); // Refresh data
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Error updating domain status:', error);
      alert('Failed to update domain status');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FiRefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">Loading sponsor relationships...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Sponsor-Attendee Relationships</h1>
            <p className="text-gray-600 mt-1">
              Manage sponsor domains and track sponsor-linked attendee registrations
            </p>
          </div>
          <Button onClick={fetchData} variant="outline">
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}

        {/* Search */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-md">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search sponsors, domains, or attendees..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardBody className="flex items-center">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg mr-4">
                  <FiStar className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Sponsor Domains</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {sponsorDomains.filter(d => d.is_active).length}
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody className="flex items-center">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg mr-4">
                  <FiUsers className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Sponsor Attendees</p>
                  <p className="text-2xl font-bold text-gray-900">{sponsorAttendees.length}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody className="flex items-center">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg mr-4">
                  <FiLink className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Domain Uses</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {sponsorDomains.reduce((sum, d) => sum + d.current_uses, 0)}
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Sponsor Domains Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiStar className="w-5 h-5 mr-2" />
              Sponsor Domains ({filteredDomains.length})
            </CardTitle>
          </CardHeader>
          <CardBody className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Domain</TableHead>
                    <TableHead>Sponsor</TableHead>
                    <TableHead>Discount</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDomains.map((domain) => (
                    <TableRow key={domain.id}>
                      <TableCell className="font-medium">{domain.domain}</TableCell>
                      <TableCell>{domain.sponsor_name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {domain.discount_value}
                          {domain.discount_type === 'percentage' ? '%' : '$'} off
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {domain.current_uses}
                        {domain.max_uses && ` / ${domain.max_uses}`}
                      </TableCell>
                      <TableCell>
                        <Badge className={domain.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                          {domain.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => toggleDomainStatus(domain.id, domain.is_active)}
                        >
                          {domain.is_active ? <FiX className="w-4 h-4" /> : <FiLink className="w-4 h-4" />}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardBody>
        </Card>

        {/* Sponsor Attendees Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiUsers className="w-5 h-5 mr-2" />
              Sponsor Attendees ({filteredAttendees.length})
            </CardTitle>
          </CardHeader>
          <CardBody className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Organization</TableHead>
                    <TableHead>Sponsor</TableHead>
                    <TableHead>Discount Code</TableHead>
                    <TableHead>Payment Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAttendees.map((attendee) => (
                    <TableRow key={attendee.id}>
                      <TableCell className="font-medium">{attendee.full_name}</TableCell>
                      <TableCell>{attendee.email}</TableCell>
                      <TableCell>{attendee.organization}</TableCell>
                      <TableCell>{attendee.sponsor?.sponsor_name || 'N/A'}</TableCell>
                      <TableCell>
                        {attendee.sponsor_discount_code && (
                          <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                            {attendee.sponsor_discount_code}
                          </code>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={
                            attendee.payment_status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : attendee.payment_status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }
                        >
                          {attendee.payment_status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardBody>
        </Card>
      </div>
  );
}
