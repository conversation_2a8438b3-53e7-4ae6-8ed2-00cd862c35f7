/* Parallax Footer Styles */
.iepa-parallax-footer {
  position: relative;
  min-height: 600px;
  overflow: hidden;
  /* Reduced negative margin to prevent overlap with page content */
  margin-top: -100px;
  padding-top: 200px; /* Increased padding to ensure adequate space above trees */
}

/* Parallax Container */
.iepa-parallax-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* Parallax Layers */
.iepa-parallax-layer {
  position: absolute;
  left: 0;
  right: 0;
  height: 120%;
  will-change: transform, filter;
  /* Optimize rendering for blur effects */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* Background Layer (row3.svg) - Lightest trees, slowest movement, most blur */
.iepa-parallax-background {
  bottom: 5%; /* Reduced height to prevent overlap with content */
  z-index: -3; /* Negative z-index to stay behind all content */
  opacity: 0.6; /* Slightly reduced for better blending */
  filter: blur(6px);
  -webkit-filter: blur(6px);
  pointer-events: none; /* Allow clicks to pass through to footer content */
}

/* Middle Layer (row2.svg) - Medium trees, medium movement, medium blur */
.iepa-parallax-middle {
  bottom: 15%; /* Reduced height to prevent overlap with content */
  z-index: -2; /* Negative z-index to stay behind all content */
  opacity: 0.8; /* Slightly reduced for better blending */
  filter: blur(3px);
  -webkit-filter: blur(3px);
  pointer-events: none; /* Allow clicks to pass through to footer content */
}

/* Foreground Layer (row1.svg) - Darkest trees, fastest movement, NO blur (in focus) */
.iepa-parallax-foreground {
  bottom: 25%; /* Reduced height to prevent overlap with content */
  z-index: -1; /* Negative z-index to stay behind all content */
  opacity: 1; /* Full opacity for foreground trees */
  pointer-events: none; /* Allow clicks to pass through to footer content */
  /* No blur filter - foreground trees should be in sharp focus */
}

/* SVG Background Layers */
.iepa-parallax-layer {
  background-size: cover !important;
  background-position: bottom center !important;
  background-repeat: repeat-x !important;
}

/* Footer Content */
.iepa-footer-content {
  position: relative;
  z-index: 100; /* High z-index to ensure it's above all tree layers */
  background: rgba(
    13,
    41,
    92,
    0.92
  ); /* Slightly more transparent for better blending */
  -webkit-backdrop-filter: blur(
    12px
  ); /* Increased blur for better depth effect */
  backdrop-filter: blur(12px);
  margin-top: 200px; /* Reduced margin to provide more space above footer */
  padding: 4rem 0 3rem; /* Increased padding for better spacing */
  color: white;
  /* Add subtle gradient overlay for better integration */
  background-image: linear-gradient(
    to bottom,
    rgba(13, 41, 92, 0.85) 0%,
    rgba(13, 41, 92, 0.95) 100%
  );
}

/* Container */
.iepa-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Footer Grid */
.iepa-footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Brand Section */
.iepa-footer-brand {
  text-align: center;
}

.iepa-footer-logo-link {
  display: inline-block;
  margin-bottom: 1rem;
}

.iepa-footer-logo {
  filter: brightness(0) invert(1); /* Make logo white */
  transition: opacity 0.3s ease;
}

.iepa-footer-logo-link:hover .iepa-footer-logo {
  opacity: 0.8;
}

.iepa-footer-description {
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  opacity: 0.9;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.iepa-footer-conference {
  font-size: 1rem;
  font-weight: 600;
  color: #87ceeb; /* Light blue accent */
  margin-bottom: 0;
}

/* Footer Sections */
.iepa-footer-section {
  text-align: center;
}

.iepa-footer-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #87ceeb; /* Light blue accent */
}

.iepa-footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.iepa-footer-links li {
  margin-bottom: 0.5rem;
}

.iepa-footer-link {
  color: white;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.iepa-footer-link:hover {
  color: #87ceeb; /* Light blue on hover */
  text-decoration: underline;
}

/* Footer Bottom */
.iepa-footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 1.5rem;
}

.iepa-footer-bottom-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.iepa-footer-copyright {
  font-size: 0.85rem;
  opacity: 0.8;
  margin: 0;
}

.iepa-footer-bottom-links {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.iepa-footer-bottom-link {
  color: white;
  text-decoration: none;
  font-size: 0.85rem;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.iepa-footer-bottom-link:hover {
  opacity: 1;
  text-decoration: underline;
}

/* Screen Reader Only */
.iepa-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Tablet Styles */
@media (min-width: 640px) {
  .iepa-footer-grid {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
  }

  .iepa-footer-brand {
    text-align: left;
  }

  .iepa-footer-description {
    margin-left: 0;
    margin-right: 0;
  }

  .iepa-footer-section {
    text-align: left;
  }

  .iepa-footer-bottom-content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .iepa-parallax-footer {
    min-height: 700px;
    /* Reduced negative margin to prevent overlap */
    margin-top: -150px;
    padding-top: 300px; /* Increased padding for better spacing */
  }

  .iepa-footer-content {
    margin-top: 300px; /* Reduced margin to provide more space above footer */
    padding: 5rem 0 4rem; /* Increased padding for better desktop spacing */
  }

  .iepa-footer-grid {
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 4rem;
  }

  .iepa-container {
    padding: 0 2rem;
  }

  /* Enhanced desktop gradient fade-out */
  main::after {
    height: 200px; /* Larger fade area on desktop */
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .iepa-parallax-layer {
    will-change: auto;
    transform: none !important;
  }

  /* Reduce blur effects for users who prefer reduced motion */
  .iepa-parallax-background {
    filter: blur(2px) !important;
    -webkit-filter: blur(2px) !important;
  }

  .iepa-parallax-middle {
    filter: blur(1px) !important;
    -webkit-filter: blur(1px) !important;
  }

  .iepa-parallax-foreground {
    filter: none !important;
    -webkit-filter: none !important;
  }

  /* Disable gradient fade for reduced motion */
  main::after {
    opacity: 0.5;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .iepa-footer-content {
    background: rgba(0, 0, 0, 0.95);
  }

  .iepa-footer-link:hover {
    color: #ffffff;
    background-color: #000000;
  }
}
