# IEPA 2025 Conference Registration Application - Augment Ignore File

# This file excludes non-essential files from Augment's context engine

# to improve code context quality and relevance

# Dependencies and build artifacts

node_modules/
.next/
dist/
build/
.vercel/
out/

# Environment and configuration files

.env*
*.log
.DS_Store
.env.local
.env.development
.env.production
.env.test

# Version control and temporary files

.git/
.history/
history/
*.tmp
*.temp
*.bak
*.backup

# IDE and editor files

.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# Package manager files

package-lock.json
yarn.lock
pnpm-lock.yaml
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage and test artifacts

coverage/
.nyc_output/
*.lcov
.coverage
test-results/
junit.xml

# Documentation assets (images, icons, etc.)

*.png
*.jpg
*.jpeg
*.gif
*.svg
*.ico
*.webp
*.bmp
*.tiff

# Compiled TypeScript

*.tsbuildinfo
tsconfig.tsbuildinfo

# Cache directories

.cache/
.parcel-cache/
.eslintcache

# Runtime data

pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory

.npm

# Optional eslint cache

.eslintcache

# Microbundle cache

.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history

.node_repl_history

# Output of 'npm pack'

*.tgz

# Yarn Integrity file

.yarn-integrity

# Stores VSCode versions used for testing VSCode extensions

.vscode-test

# Husky

.husky/_

# Storybook build outputs

storybook-static

# Temporary folders

tmp/
temp/
