-- Add missing fields to iepa_conference_documents table for full CRUD functionality

-- Add display_order and is_public columns
ALTER TABLE iepa_conference_documents 
ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT TRUE;

-- Add index for better performance on ordering
CREATE INDEX IF NOT EXISTS idx_conference_documents_display_order 
ON iepa_conference_documents(display_order, created_at);

-- Add index for public documents filtering
CREATE INDEX IF NOT EXISTS idx_conference_documents_public_active 
ON iepa_conference_documents(is_public, is_active);

-- Update existing documents to have default values
UPDATE iepa_conference_documents 
SET display_order = 0 
WHERE display_order IS NULL;

UPDATE iepa_conference_documents 
SET is_public = TRUE 
WHERE is_public IS NULL;

-- Add trigger for updated_at timestamp
CREATE TRIGGER update_iepa_conference_documents_updated_at
    BEFORE UPDATE ON iepa_conference_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON COLUMN iepa_conference_documents.display_order IS 'Order for displaying documents (lower numbers first)';
COMMENT ON COLUMN iepa_conference_documents.is_public IS 'Whether document is visible to public users';
COMMENT ON COLUMN iepa_conference_documents.is_active IS 'Whether document is active and available';
COMMENT ON COLUMN iepa_conference_documents.file_size IS 'File size in bytes';
COMMENT ON COLUMN iepa_conference_documents.file_type IS 'MIME type of the uploaded file';
