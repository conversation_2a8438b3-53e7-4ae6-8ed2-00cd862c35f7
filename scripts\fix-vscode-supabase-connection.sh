#!/bin/bash

# IEPA Conference Registration - Fix VS Code Supabase Extension Connection
# This script fixes common issues with the VS Code Supabase extension

set -e

echo "🔧 Fixing VS Code Supabase Extension Connection..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Ensure we're in the project root
cd "$(dirname "$0")/.."

print_status "Current directory: $(pwd)"

# Step 1: Stop and restart Supabase cleanly
print_status "Step 1: Restarting Supabase cleanly..."
supabase stop || true
sleep 2
supabase start

# Step 2: Verify all services are running
print_status "Step 2: Verifying services..."
if supabase status > /dev/null 2>&1; then
    print_success "Supabase is running"
else
    print_error "Supabase failed to start"
    exit 1
fi

# Step 3: Test API connectivity
print_status "Step 3: Testing API connectivity..."
if curl -s http://127.0.0.1:54321/rest/v1/ -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" | grep -q "swagger"; then
    print_success "API is responding"
else
    print_error "API is not responding"
    exit 1
fi

# Step 4: Check required files
print_status "Step 4: Checking required files..."
if [ -f "supabase/config.toml" ]; then
    print_success "config.toml exists"
else
    print_error "config.toml missing"
    exit 1
fi

# Step 5: Create VS Code workspace file if it doesn't exist
print_status "Step 5: Creating workspace configuration..."
if [ ! -f "iepa-conf-reg.code-workspace" ]; then
    print_status "Creating workspace file..."
    cat > iepa-conf-reg.code-workspace << 'EOF'
{
  "folders": [
    {
      "path": "."
    }
  ],
  "settings": {
    "supabase.projectRef": "iepa-conf-reg",
    "supabase.localUrl": "http://127.0.0.1:54321",
    "supabase.studioUrl": "http://127.0.0.1:54323"
  }
}
EOF
    print_success "Workspace file created"
else
    print_success "Workspace file already exists"
fi

echo ""
print_success "🎉 Fix completed! Try these steps in VS Code:"
echo ""
echo "1. 📁 Open the workspace file: iepa-conf-reg.code-workspace"
echo "2. 🔄 Reload VS Code window (Cmd+Shift+P -> 'Developer: Reload Window')"
echo "3. 🔌 Open Supabase extension and try connecting again"
echo "4. 📂 If prompted, select this directory: $(pwd)"
echo ""
echo "🔗 Verify these URLs work in your browser:"
echo "   Studio: http://127.0.0.1:54323"
echo "   API: http://127.0.0.1:54321"
echo ""
echo "💡 If still having issues, try:"
echo "   - Restart VS Code completely"
echo "   - Disable and re-enable the Supabase extension"
echo "   - Check VS Code Developer Console for errors (Help -> Toggle Developer Tools)"
