// React Hook for Form Prefilling
// Provides automatic form prefilling from user profile data

import { useEffect, useState, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { userProfileUtils, UserProfile } from '@/lib/user-profile-utils';
import {
  transformProfileToFormData,
  mergeWithExistingData,
  isFormDataEmpty,
  createPrefillSummary,
  FormPrefillData,
  PrefillResult
} from '@/lib/form-prefill-utils';

export interface UseFormPrefillOptions {
  formType: 'attendee' | 'speaker';
  onPrefillComplete?: (fieldsPopulated: string[], summary: string) => void;
  onPrefillError?: (error: string) => void;
  enabled?: boolean;
}

export interface UseFormPrefillReturn {
  // State
  isLoading: boolean;
  profile: UserProfile | null;
  prefillData: FormPrefillData | null;
  error: string | null;
  
  // Actions
  prefillForm: (currentFormData: any) => Promise<{ 
    formData: any; 
    fieldsPopulated: string[]; 
    summary: string 
  } | null>;
  clearError: () => void;
  
  // Status
  hasPrefillData: boolean;
  canPrefill: boolean;
}

/**
 * Hook for automatic form prefilling from user profile data
 */
export function useFormPrefill(options: UseFormPrefillOptions): UseFormPrefillReturn {
  const {
    formType,
    onPrefillComplete,
    onPrefillError,
    enabled = true
  } = options;

  const { user } = useAuth();

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [prefillData, setPrefillData] = useState<FormPrefillData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Ref to track if profile has been loaded for this user
  const loadedUserIdRef = useRef<string | null>(null);

  // Load user profile when user is available
  useEffect(() => {
    if (!enabled || !user?.email) {
      setProfile(null);
      setPrefillData(null);
      loadedUserIdRef.current = null;
      return;
    }

    // Prevent multiple loads for the same user
    if (loadedUserIdRef.current === user.id) {
      return;
    }

    // Prevent multiple simultaneous loads
    if (isLoading) {
      return;
    }

    const loadProfile = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log(`🔄 Loading profile for prefill: ${user.email}`);

        // Try to get profile by user ID first, then by email
        let userProfile = await userProfileUtils.getUserProfile(user.id);

        if (!userProfile && user.email) {
          userProfile = await userProfileUtils.getUserProfileByEmail(user.email);
        }

        if (userProfile) {
          console.log(`✅ Profile found for prefill:`, userProfile);
          setProfile(userProfile);

          // Transform profile data for form prefilling
          const prefillResult = transformProfileToFormData(userProfile, formType);
          setPrefillData(prefillResult.data);

          console.log(`📝 Prefill data prepared:`, prefillResult);
        } else {
          console.log(`ℹ️ No profile found for user: ${user.email}`);
          setProfile(null);
          setPrefillData(null);
        }

        // Mark this user as loaded
        loadedUserIdRef.current = user.id;
      } catch (err) {
        console.error('Error loading profile for prefill:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to load profile data';
        setError(errorMessage);
        onPrefillError?.(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    loadProfile();
  }, [user?.id, user?.email, formType, enabled]); // Removed onPrefillError to prevent infinite loops

  // Prefill form function
  const prefillForm = useCallback(async (currentFormData: any) => {
    if (!prefillData || !profile) {
      console.log('⚠️ No prefill data available');
      return null;
    }

    try {
      console.log('🔄 Starting form prefill process');
      console.log('📋 Current form data:', currentFormData);
      console.log('📝 Available prefill data:', prefillData);

      // Check if form is mostly empty (to avoid overriding user input)
      const formIsEmpty = isFormDataEmpty(currentFormData);
      console.log('📊 Form is empty:', formIsEmpty);

      if (!formIsEmpty) {
        console.log('ℹ️ Form has existing data, merging carefully');
      }

      // Merge prefill data with existing form data
      const { mergedData, newFieldsPopulated } = mergeWithExistingData(
        currentFormData,
        prefillData
      );

      console.log('✅ Form prefill completed');
      console.log('📝 Merged data:', mergedData);
      console.log('🎯 Fields populated:', newFieldsPopulated);

      // Create summary message
      const summary = createPrefillSummary(newFieldsPopulated);
      console.log('📄 Prefill summary:', summary);

      // Notify completion
      if (newFieldsPopulated.length > 0) {
        onPrefillComplete?.(newFieldsPopulated, summary);
      }

      return {
        formData: mergedData,
        fieldsPopulated: newFieldsPopulated,
        summary
      };
    } catch (err) {
      console.error('Error during form prefill:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to prefill form';
      setError(errorMessage);
      onPrefillError?.(errorMessage);
      return null;
    }
  }, [prefillData, profile, onPrefillComplete, onPrefillError]);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Computed properties
  const hasPrefillData = Boolean(prefillData && Object.keys(prefillData).length > 0);
  const canPrefill = Boolean(enabled && user && profile && hasPrefillData && !isLoading);

  return {
    // State
    isLoading,
    profile,
    prefillData,
    error,
    
    // Actions
    prefillForm,
    clearError,
    
    // Status
    hasPrefillData,
    canPrefill
  };
}

/**
 * Simplified hook for basic prefill functionality
 */
export function useSimpleFormPrefill(
  formType: 'attendee' | 'speaker',
  onPrefillComplete?: (summary: string) => void
) {
  return useFormPrefill({
    formType,
    onPrefillComplete: (fields, summary) => onPrefillComplete?.(summary),
    enabled: true
  });
}
