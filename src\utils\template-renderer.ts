/**
 * Simple Mustache-like template renderer for email templates
 * Supports basic variable substitution and conditional blocks
 */

interface TemplateData {
  [key: string]: unknown;
}

export class TemplateRenderer {
  /**
   * Render a template with the provided data
   */
  static render(template: string, data: TemplateData): string {
    let result = template;
    // Fixed static method calls

    // Handle conditional blocks first ({{#variable}} ... {{/variable}})
    result = TemplateRenderer.renderConditionals(result, data);

    // Handle inverted conditional blocks ({{^variable}} ... {{/variable}})
    result = TemplateRenderer.renderInvertedConditionals(result, data);

    // Handle simple variable substitution ({{variable}})
    result = TemplateRenderer.renderVariables(result, data);

    return result;
  }

  /**
   * Render conditional blocks {{#variable}} ... {{/variable}}
   */
  private static renderConditionals(
    template: string,
    data: TemplateData
  ): string {
    const conditionalRegex = /\{\{#(\w+)\}\}([\s\S]*?)\{\{\/\1\}\}/g;

    return template.replace(conditionalRegex, (match, variable, content) => {
      const value = TemplateRenderer.getNestedValue(data, variable);

      // If value is truthy, render the content
      if (value) {
        // If value is an array, render content for each item
        if (Array.isArray(value)) {
          return value
            .map(item => {
              if (typeof item === 'object') {
                return TemplateRenderer.render(content, { ...data, ...item });
              } else {
                return TemplateRenderer.render(content, {
                  ...data,
                  [variable]: item,
                });
              }
            })
            .join('');
        }

        // If value is an object, merge it with data
        if (typeof value === 'object') {
          return TemplateRenderer.render(content, { ...data, ...value });
        }

        // For primitive values, just render the content
        return TemplateRenderer.render(content, data);
      }

      // If value is falsy, don't render anything
      return '';
    });
  }

  /**
   * Render inverted conditional blocks {{^variable}} ... {{/variable}}
   */
  private static renderInvertedConditionals(
    template: string,
    data: TemplateData
  ): string {
    const invertedConditionalRegex = /\{\{\^(\w+)\}\}([\s\S]*?)\{\{\/\1\}\}/g;

    return template.replace(
      invertedConditionalRegex,
      (match, variable, content) => {
        const value = TemplateRenderer.getNestedValue(data, variable);

        // If value is falsy, render the content
        if (!value || (Array.isArray(value) && value.length === 0)) {
          return TemplateRenderer.render(content, data);
        }

        // If value is truthy, don't render anything
        return '';
      }
    );
  }

  /**
   * Render simple variables {{variable}}
   */
  private static renderVariables(template: string, data: TemplateData): string {
    const variableRegex = /\{\{(\w+(?:\.\w+)*)\}\}/g;

    return template.replace(variableRegex, (match, variable) => {
      const value = TemplateRenderer.getNestedValue(data, variable);

      if (value === null || value === undefined) {
        return '';
      }

      return String(value);
    });
  }

  /**
   * Get nested value from object using dot notation
   */
  private static getNestedValue(
    obj: Record<string, unknown>,
    path: string
  ): unknown {
    return path.split('.').reduce((current: unknown, key: string) => {
      return current &&
        typeof current === 'object' &&
        current !== null &&
        (current as Record<string, unknown>)[key] !== undefined
        ? (current as Record<string, unknown>)[key]
        : undefined;
    }, obj);
  }

  /**
   * Extract variables from a template
   */
  static extractVariables(template: string): string[] {
    const variables = new Set<string>();

    // Extract from simple variables {{variable}}
    const variableRegex = /\{\{(\w+(?:\.\w+)*)\}\}/g;
    let match;
    while ((match = variableRegex.exec(template)) !== null) {
      variables.add(match[1]);
    }

    // Extract from conditional blocks {{#variable}} and {{^variable}}
    const conditionalRegex = /\{\{[#^](\w+)\}\}/g;
    while ((match = conditionalRegex.exec(template)) !== null) {
      variables.add(match[1]);
    }

    return Array.from(variables);
  }

  /**
   * Validate that a template has all required variables
   */
  static validateTemplate(
    template: string,
    requiredVariables: string[]
  ): string[] {
    const templateVariables = TemplateRenderer.extractVariables(template);
    const missing = requiredVariables.filter(
      variable => !templateVariables.includes(variable)
    );
    return missing;
  }

  /**
   * Preview a template with sample data
   */
  static preview(template: string, sampleData: TemplateData = {}): string {
    // Provide default sample data for common variables
    const defaultSampleData = {
      name: 'John Doe',
      email: '<EMAIL>',
      confirmationNumber: 'CONF-12345',
      registrationType: 'Attendee',
      organizationName: 'Sample Organization',
      amount: '$1,200.00',
      paymentDate: 'January 30, 2025',
      resetUrl: 'https://example.com/reset-password',
      isGolf: false,
      ...sampleData,
    };

    return TemplateRenderer.render(template, defaultSampleData);
  }
}

// Export convenience functions
export const renderTemplate = (template: string, data: TemplateData): string =>
  TemplateRenderer.render(template, data);
export const extractVariables = (template: string): string[] =>
  TemplateRenderer.extractVariables(template);
export const validateTemplate = (
  template: string,
  requiredVariables: string[]
): string[] => TemplateRenderer.validateTemplate(template, requiredVariables);
export const previewTemplate = (
  template: string,
  sampleData: TemplateData = {}
): string => TemplateRenderer.preview(template, sampleData);
