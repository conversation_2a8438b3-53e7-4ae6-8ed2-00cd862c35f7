#!/usr/bin/env node

/**
 * IEPA E2E Test Runner
 *
 * This script runs the IEPA member registration E2E test with proper setup.
 * It ensures the development server is running and executes the test.
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// Configuration
const CONFIG = {
  devServerPort: 6969,
  testTimeout: 300000, // 5 minutes
  retries: 1,
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, colors.cyan);
  log(`${message}`, colors.cyan);
  log(`${'='.repeat(60)}`, colors.cyan);
}

function logStep(step, message) {
  log(`\n${colors.bright}Step ${step}:${colors.reset} ${message}`, colors.blue);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️ ${message}`, colors.yellow);
}

async function checkDevServer() {
  return new Promise(resolve => {
    const http = require('http');
    const req = http.request(
      {
        hostname: 'localhost',
        port: CONFIG.devServerPort,
        path: '/health',
        timeout: 5000,
      },
      res => {
        resolve(res.statusCode === 200);
      }
    );

    req.on('error', () => resolve(false));
    req.on('timeout', () => resolve(false));
    req.end();
  });
}

async function startDevServer() {
  return new Promise((resolve, reject) => {
    logStep(1, 'Starting development server...');

    const devServer = spawn('npm', ['run', 'dev'], {
      cwd: projectRoot,
      stdio: 'pipe',
      env: { ...process.env, PORT: CONFIG.devServerPort.toString() },
    });

    let serverReady = false;

    devServer.stdout.on('data', data => {
      const output = data.toString();
      if (output.includes('Ready in') || output.includes('Local:')) {
        if (!serverReady) {
          serverReady = true;
          logSuccess('Development server started successfully');
          resolve(devServer);
        }
      }
    });

    devServer.stderr.on('data', data => {
      const error = data.toString();
      if (error.includes('EADDRINUSE')) {
        logWarning(
          'Port already in use, checking if server is already running...'
        );
        checkDevServer().then(isRunning => {
          if (isRunning) {
            logSuccess('Development server already running');
            resolve(null); // null indicates server was already running
          } else {
            reject(new Error('Port in use but server not responding'));
          }
        });
      }
    });

    devServer.on('error', error => {
      reject(error);
    });

    // Timeout after 2 minutes
    setTimeout(() => {
      if (!serverReady) {
        reject(new Error('Development server failed to start within timeout'));
      }
    }, 120000);
  });
}

async function runPlaywrightTest() {
  return new Promise((resolve, reject) => {
    logStep(2, 'Running Playwright E2E test...');

    const playwrightArgs = [
      'exec',
      'playwright',
      'test',
      'tests/iepa-member-registration-e2e.spec.js',
      '--project=chromium',
      '--reporter=list,html',
      `--timeout=${CONFIG.testTimeout}`,
      `--retries=${CONFIG.retries}`,
    ];

    const playwrightProcess = spawn('npx', playwrightArgs, {
      cwd: projectRoot,
      stdio: 'inherit',
      env: {
        ...process.env,
        PLAYWRIGHT_BASE_URL: `http://localhost:${CONFIG.devServerPort}`,
      },
    });

    playwrightProcess.on('close', code => {
      if (code === 0) {
        logSuccess('E2E test completed successfully');
        resolve();
      } else {
        reject(new Error(`Playwright test failed with exit code ${code}`));
      }
    });

    playwrightProcess.on('error', error => {
      reject(error);
    });
  });
}

async function main() {
  try {
    logHeader('IEPA Member Registration E2E Test Runner');

    // Check if dev server is already running
    const isServerRunning = await checkDevServer();
    let devServerProcess = null;

    if (isServerRunning) {
      logSuccess('Development server already running');
    } else {
      devServerProcess = await startDevServer();
    }

    // Wait a moment for server to be fully ready
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Run the E2E test
    await runPlaywrightTest();

    // Cleanup
    if (devServerProcess) {
      logStep(3, 'Stopping development server...');
      devServerProcess.kill('SIGTERM');
      logSuccess('Development server stopped');
    }

    logHeader('E2E Test Completed Successfully! 🎉');
    log('\nTest results available at:', colors.cyan);
    log('  - HTML Report: test-results/html-report/index.html', colors.cyan);
    log('  - JSON Results: test-results/results.json', colors.cyan);
    log('  - Screenshots: test-results/artifacts/', colors.cyan);
  } catch (error) {
    logError(`E2E test failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\nReceived SIGINT, cleaning up...', colors.yellow);
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('\nReceived SIGTERM, cleaning up...', colors.yellow);
  process.exit(0);
});

// Run the main function
main().catch(error => {
  logError(`Unexpected error: ${error.message}`);
  process.exit(1);
});
