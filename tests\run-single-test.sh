#!/bin/bash

# IEPA Registration Tests - Single Test Runner
# Usage: ./run-single-test.sh <test-name>

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
HEADED_MODE="--headed"
PROJECT="--project=chromium"
TIMEOUT="--timeout=120000"

# Available tests
declare -A TESTS
TESTS["iepa"]="tests/iepa-member-registration-complete-e2e.spec.js"
TESTS["non-iepa"]="tests/non-iepa-member-registration-e2e.spec.js"
TESTS["government"]="tests/government-registration-e2e.spec.js"
TESTS["speaker"]="tests/speaker-comped-registration-e2e.spec.js"
TESTS["sponsor-attendee"]="tests/sponsor-attendee-registration-e2e.spec.js"
TESTS["spouse"]="tests/spouse-registration-e2e.spec.js"
TESTS["child"]="tests/child-registration-e2e.spec.js"

# Function to show usage
show_usage() {
    echo "🧪 IEPA Registration Test Runner"
    echo "================================"
    echo ""
    echo "Usage: ./run-single-test.sh <test-name>"
    echo ""
    echo "Available tests:"
    echo "  iepa           - IEPA Member Registration ($2,369)"
    echo "  non-iepa       - Non-IEPA Member Registration ($2,730)"
    echo "  government     - Government Registration ($2,060)"
    echo "  speaker        - Speaker Comped Registration ($0)"
    echo "  sponsor-attendee - Sponsor Attendee Registration ($0)"
    echo "  spouse         - Spouse Registration ($500)"
    echo "  child          - Child Registration ($100)"
    echo ""
    echo "Examples:"
    echo "  ./run-single-test.sh iepa"
    echo "  ./run-single-test.sh speaker"
    echo "  ./run-single-test.sh government"
    echo ""
    echo "🌐 Browser: Chrome with visible UI and slow motion"
    echo "⏰ Timeout: 2 minutes per test"
}

# Check if test name provided
if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

TEST_NAME=$1
TEST_FILE=${TESTS[$TEST_NAME]}

# Check if test exists
if [ -z "$TEST_FILE" ]; then
    echo -e "${RED}❌ Unknown test: $TEST_NAME${NC}"
    echo ""
    show_usage
    exit 1
fi

# Check if test file exists
if [ ! -f "$TEST_FILE" ]; then
    echo -e "${RED}❌ Test file not found: $TEST_FILE${NC}"
    exit 1
fi

# Check if development server is running
echo "🔍 Checking if development server is running on port 6969..."
if curl -s http://localhost:6969 > /dev/null; then
    echo -e "${GREEN}✅ Development server is running${NC}"
else
    echo -e "${RED}❌ Development server not running. Please start with: npm run dev${NC}"
    echo "   Make sure the server is running on port 6969"
    exit 1
fi

echo ""
echo -e "${BLUE}🧪 Running: $TEST_NAME Registration Test${NC}"
echo "   File: $TEST_FILE"
echo "   Mode: Chrome browser with visible UI"
echo ""

# Run the test
echo "🚀 Starting test execution..."
echo ""

if npx playwright test "$TEST_FILE" $HEADED_MODE $PROJECT $TIMEOUT; then
    echo ""
    echo -e "${GREEN}🎉 TEST PASSED: $TEST_NAME Registration${NC}"
    echo ""
    echo "📁 Test artifacts saved to: test-results/"
    echo "📸 Screenshots: test-results/$TEST_NAME-*.png"
    echo ""
    echo "💡 To run with debugging:"
    echo "   npx playwright test $TEST_FILE --debug"
    echo ""
    exit 0
else
    echo ""
    echo -e "${RED}❌ TEST FAILED: $TEST_NAME Registration${NC}"
    echo ""
    echo "🔧 Debugging options:"
    echo "   1. Run with debugger: npx playwright test $TEST_FILE --debug"
    echo "   2. Check screenshots: test-results/$TEST_NAME-*.png"
    echo "   3. Verify development server is running: http://localhost:6969"
    echo "   4. Check browser console for errors"
    echo ""
    exit 1
fi
