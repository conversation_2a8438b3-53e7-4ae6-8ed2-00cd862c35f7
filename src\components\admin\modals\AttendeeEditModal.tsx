'use client';

import React, { useState } from 'react';
import { AttendeeRegistration } from '@/types/database';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button, Input, Label } from '@/components/ui';
import { PhoneInput } from '@/components/ui/phone-input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { supabase } from '@/lib/supabase';
import { FiSave, FiX } from 'react-icons/fi';

// Use AttendeeRegistration directly since all needed fields are already defined
type ExtendedAttendeeRegistration = AttendeeRegistration;

interface AttendeeEditModalProps {
  attendee: ExtendedAttendeeRegistration;
  open: boolean;
  onClose: () => void;
  onSave: () => void;
}

export default function AttendeeEditModal({
  attendee,
  open,
  onClose,
  onSave,
}: AttendeeEditModalProps) {
  const [formData, setFormData] = useState({
    full_name: attendee.full_name,
    first_name: attendee.first_name,
    last_name: attendee.last_name,
    name_on_badge: attendee.name_on_badge,
    email: attendee.email,
    phone_number: attendee.phone_number,
    street_address: attendee.street_address,
    city: attendee.city,
    state: attendee.state,
    zip_code: attendee.zip_code,
    organization: attendee.organization,
    job_title: attendee.job_title,
    gender: attendee.gender,
    registration_type: attendee.registration_type,
    attending_golf: attendee.attending_golf,
    golf_club_rental: attendee.golf_club_rental || false,
    golf_club_handedness: attendee.golf_club_handedness || '',
    dietary_needs: attendee.dietary_needs,
    payment_status: attendee.payment_status,
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      const { error: updateError } = await supabase
        .from('iepa_attendee_registrations')
        .update({
          ...formData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', attendee.id);

      if (updateError) throw updateError;

      onSave();
    } catch (err) {
      console.error('Error updating attendee:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to update attendee'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Attendee</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Personal Information</h3>

            <div>
              <Label htmlFor="full_name">Full Name</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={e => handleInputChange('full_name', e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="first_name">First Name</Label>
                <Input
                  id="first_name"
                  value={formData.first_name}
                  onChange={e =>
                    handleInputChange('first_name', e.target.value)
                  }
                />
              </div>
              <div>
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  value={formData.last_name}
                  onChange={e => handleInputChange('last_name', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="name_on_badge">Name on Badge</Label>
              <Input
                id="name_on_badge"
                value={formData.name_on_badge}
                onChange={e =>
                  handleInputChange('name_on_badge', e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="gender">Gender</Label>
              <Select
                value={formData.gender}
                onValueChange={value => handleInputChange('gender', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={e => handleInputChange('email', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="phone_number">Phone Number</Label>
              <PhoneInput
                id="phone_number"
                value={formData.phone_number}
                onChange={value =>
                  handleInputChange('phone_number', value)
                }
              />
            </div>
          </div>

          {/* Address and Professional */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Address & Professional</h3>

            <div>
              <Label htmlFor="street_address">Street Address</Label>
              <Input
                id="street_address"
                value={formData.street_address}
                onChange={e =>
                  handleInputChange('street_address', e.target.value)
                }
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={e => handleInputChange('city', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={e => handleInputChange('state', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="zip_code">ZIP Code</Label>
              <Input
                id="zip_code"
                value={formData.zip_code}
                onChange={e => handleInputChange('zip_code', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="organization">Organization</Label>
              <Input
                id="organization"
                value={formData.organization}
                onChange={e =>
                  handleInputChange('organization', e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="job_title">Job Title</Label>
              <Input
                id="job_title"
                value={formData.job_title}
                onChange={e => handleInputChange('job_title', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="registration_type">Registration Type</Label>
              <Select
                value={formData.registration_type}
                onValueChange={value =>
                  handleInputChange('registration_type', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="iepa-member">IEPA Member</SelectItem>
                  <SelectItem value="non-iepa-member">Non-IEPA Member</SelectItem>
                  <SelectItem value="day-use-iepa">Day Use - IEPA</SelectItem>
                  <SelectItem value="day-use-non-iepa">Day Use - Non-IEPA</SelectItem>
                  <SelectItem value="fed-state-government">Federal/State Government</SelectItem>
                  <SelectItem value="cca">California Community Choice Association</SelectItem>
                  <SelectItem value="comped-speaker">Comped Speaker</SelectItem>
                  <SelectItem value="full-meeting-speaker">Full Meeting Speaker</SelectItem>
                  <SelectItem value="iepa-staff">IEPA Staff</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="payment_status">Payment Status</Label>
              <Select
                value={formData.payment_status}
                onValueChange={value =>
                  handleInputChange('payment_status', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Golf and Dietary */}
          <div className="md:col-span-2 space-y-4">
            <h3 className="text-lg font-semibold">Golf & Dietary</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="attending_golf">Golf Participation</Label>
                <Select
                  value={formData.attending_golf ? 'yes' : 'no'}
                  onValueChange={value =>
                    handleInputChange('attending_golf', value === 'yes')
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.attending_golf && (
                <>
                  <div>
                    <Label htmlFor="golf_club_rental">Club Rental</Label>
                    <Select
                      value={formData.golf_club_rental ? 'yes' : 'no'}
                      onValueChange={value =>
                        handleInputChange('golf_club_rental', value === 'yes')
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yes">Yes</SelectItem>
                        <SelectItem value="no">No</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {formData.golf_club_rental && (
                    <div>
                      <Label htmlFor="golf_club_handedness">
                        Club Handedness
                      </Label>
                      <Select
                        value={formData.golf_club_handedness}
                        onValueChange={value =>
                          handleInputChange('golf_club_handedness', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="right">Right Handed</SelectItem>
                          <SelectItem value="left">Left Handed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </>
              )}
            </div>

            <div>
              <Label htmlFor="dietary_needs">Dietary Needs</Label>
              <Textarea
                id="dietary_needs"
                value={formData.dietary_needs}
                onChange={e =>
                  handleInputChange('dietary_needs', e.target.value)
                }
                placeholder="Any dietary restrictions or special needs..."
                rows={3}
              />
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            <FiX className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            <FiSave className="w-4 h-4 mr-2" />
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
