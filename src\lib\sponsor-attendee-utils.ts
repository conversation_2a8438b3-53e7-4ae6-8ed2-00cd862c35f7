// Sponsor-Attendee Linking Utilities for IEPA Conference Registration
// Handles automatic sponsor discounts, domain matching, and relationship management

import { createSupabaseAdmin } from '@/lib/supabase';
import { Database } from '@/types/database';

type SponsorRegistration = Database['public']['Tables']['iepa_sponsor_registrations']['Row'];
type AttendeeRegistration = Database['public']['Tables']['iepa_attendee_registrations']['Row'];
type SponsorDomain = Database['public']['Tables']['iepa_sponsor_domains']['Row'];

export interface SponsorDiscountResult {
  success: boolean;
  isEligible: boolean;
  sponsorId?: string;
  sponsorName?: string;
  discountCode?: string;
  discountAmount?: number;
  error?: string;
}

export interface CreateSponsorDomainResult {
  success: boolean;
  sponsorDomainId?: string;
  discountCode?: string;
  error?: string;
}

/**
 * Extract domain from email address
 */
export function extractEmailDomain(email: string): string {
  const domain = email.split('@')[1]?.toLowerCase();
  if (!domain) {
    throw new Error('Invalid email address');
  }
  return domain;
}

/**
 * Extract domain from URL
 */
export function extractUrlDomain(url: string): string {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
    return urlObj.hostname.replace('www.', '').toLowerCase();
  } catch (error) {
    throw new Error('Invalid URL format');
  }
}

/**
 * Generate automatic discount code for sponsor domain
 */
export function generateSponsorDiscountCode(sponsorName: string, domain: string): string {
  const cleanSponsorName = sponsorName
    .replace(/[^a-zA-Z0-9]/g, '')
    .substring(0, 10)
    .toUpperCase();
  const cleanDomain = domain.replace(/[^a-zA-Z0-9]/g, '').substring(0, 8).toUpperCase();
  return `SPONSOR_${cleanSponsorName}_${cleanDomain}`;
}

/**
 * Check if an email is eligible for sponsor discount
 */
export async function checkSponsorDiscount(email: string): Promise<SponsorDiscountResult> {
  try {
    const domain = extractEmailDomain(email);
    const supabaseAdmin = createSupabaseAdmin();

    // Look for active sponsor domain matching the email domain
    const { data: sponsorDomain, error: domainError } = await supabaseAdmin
      .from('iepa_sponsor_domains')
      .select(`
        *,
        sponsor:iepa_sponsor_registrations!sponsor_id(
          id,
          sponsor_name,
          payment_status
        )
      `)
      .eq('domain', domain)
      .eq('is_active', true)
      .single();

    if (domainError || !sponsorDomain) {
      return {
        success: true,
        isEligible: false,
      };
    }

    // Check if sponsor registration is completed
    const sponsor = sponsorDomain.sponsor as any;
    if (!sponsor || sponsor.payment_status !== 'completed') {
      return {
        success: true,
        isEligible: false,
        error: 'Sponsor registration not completed',
      };
    }

    // Check usage limits
    if (sponsorDomain.max_uses && sponsorDomain.current_uses >= sponsorDomain.max_uses) {
      return {
        success: true,
        isEligible: false,
        error: 'Sponsor discount usage limit reached',
      };
    }

    // Calculate discount amount (assuming 100% discount for sponsor attendees)
    const discountAmount = sponsorDomain.discount_type === 'percentage' 
      ? sponsorDomain.discount_value 
      : sponsorDomain.discount_value;

    return {
      success: true,
      isEligible: true,
      sponsorId: sponsor.id,
      sponsorName: sponsor.sponsor_name,
      discountCode: sponsorDomain.auto_discount_code,
      discountAmount,
    };
  } catch (error) {
    console.error('Error checking sponsor discount:', error);
    return {
      success: false,
      isEligible: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Create sponsor domain mapping with automatic discount code
 */
export async function createSponsorDomain(
  sponsorId: string,
  domain: string,
  sponsorName: string,
  discountValue: number = 100,
  discountType: 'percentage' | 'fixed_amount' = 'percentage',
  maxUses?: number,
  createdBy?: string
): Promise<CreateSponsorDomainResult> {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    // Generate automatic discount code
    const autoDiscountCode = generateSponsorDiscountCode(sponsorName, domain);

    // Check if domain already exists for this sponsor
    const { data: existingDomain } = await supabaseAdmin
      .from('iepa_sponsor_domains')
      .select('id')
      .eq('sponsor_id', sponsorId)
      .eq('domain', domain)
      .single();

    if (existingDomain) {
      return {
        success: false,
        error: 'Domain already exists for this sponsor',
      };
    }

    // Create sponsor domain record
    const { data: sponsorDomain, error: insertError } = await supabaseAdmin
      .from('iepa_sponsor_domains')
      .insert([
        {
          sponsor_id: sponsorId,
          domain: domain.toLowerCase(),
          sponsor_name: sponsorName,
          discount_type: discountType,
          discount_value: discountValue,
          auto_discount_code: autoDiscountCode,
          max_uses: maxUses || null,
          created_by: createdBy || null,
          updated_by: createdBy || null,
        },
      ])
      .select()
      .single();

    if (insertError) {
      throw new Error(`Failed to create sponsor domain: ${insertError.message}`);
    }

    return {
      success: true,
      sponsorDomainId: sponsorDomain.id,
      discountCode: autoDiscountCode,
    };
  } catch (error) {
    console.error('Error creating sponsor domain:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Link attendee registration to sponsor
 */
export async function linkAttendeeToSponsor(
  attendeeId: string,
  sponsorId: string,
  sponsorDiscountCode?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    const { error: updateError } = await supabaseAdmin
      .from('iepa_attendee_registrations')
      .update({
        sponsor_id: sponsorId,
        sponsor_discount_code: sponsorDiscountCode || null,
        is_sponsor_attendee: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', attendeeId);

    if (updateError) {
      throw new Error(`Failed to link attendee to sponsor: ${updateError.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error linking attendee to sponsor:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Get sponsor with linked attendees
 */
export async function getSponsorWithAttendees(sponsorId: string) {
  const supabaseAdmin = createSupabaseAdmin();

  const { data, error } = await supabaseAdmin
    .from('iepa_sponsor_registrations')
    .select(`
      *,
      linked_attendees:iepa_attendee_registrations!sponsor_id(*),
      sponsor_domains:iepa_sponsor_domains(*)
    `)
    .eq('id', sponsorId)
    .single();

  if (error) {
    throw new Error(`Failed to fetch sponsor data: ${error.message}`);
  }

  return data;
}

/**
 * Get attendee with sponsor information
 */
export async function getAttendeeWithSponsor(attendeeId: string) {
  const supabaseAdmin = createSupabaseAdmin();

  const { data, error } = await supabaseAdmin
    .from('iepa_attendee_registrations')
    .select(`
      *,
      sponsor:iepa_sponsor_registrations!sponsor_id(*)
    `)
    .eq('id', attendeeId)
    .single();

  if (error) {
    throw new Error(`Failed to fetch attendee data: ${error.message}`);
  }

  return data;
}

/**
 * Update sponsor domain usage count
 */
export async function incrementSponsorDomainUsage(sponsorDomainId: string): Promise<void> {
  const supabaseAdmin = createSupabaseAdmin();

  // First get the current usage count
  const { data: currentData, error: fetchError } = await supabaseAdmin
    .from('iepa_sponsor_domains')
    .select('current_uses')
    .eq('id', sponsorDomainId)
    .single();

  if (fetchError) {
    console.error('Error fetching current sponsor domain usage:', fetchError);
    throw new Error(`Failed to fetch sponsor domain usage: ${fetchError.message}`);
  }

  // Increment the usage count
  const { error } = await supabaseAdmin
    .from('iepa_sponsor_domains')
    .update({
      current_uses: (currentData.current_uses || 0) + 1,
      updated_at: new Date().toISOString(),
    })
    .eq('id', sponsorDomainId);

  if (error) {
    console.error('Error incrementing sponsor domain usage:', error);
    throw new Error(`Failed to update sponsor domain usage: ${error.message}`);
  }
}

/**
 * Auto-create sponsor domain from sponsor registration
 */
export async function autoCreateSponsorDomain(sponsorId: string): Promise<CreateSponsorDomainResult> {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    // Get sponsor registration details
    const { data: sponsor, error: sponsorError } = await supabaseAdmin
      .from('iepa_sponsor_registrations')
      .select('sponsor_name, sponsor_url, company_domain')
      .eq('id', sponsorId)
      .single();

    if (sponsorError || !sponsor) {
      return {
        success: false,
        error: 'Sponsor registration not found',
      };
    }

    // Extract domain from sponsor URL or use existing company_domain
    let domain: string;
    if (sponsor.company_domain) {
      domain = sponsor.company_domain;
    } else if (sponsor.sponsor_url) {
      try {
        domain = extractUrlDomain(sponsor.sponsor_url);
      } catch (error) {
        return {
          success: false,
          error: 'Could not extract domain from sponsor URL',
        };
      }
    } else {
      return {
        success: false,
        error: 'No domain information available for sponsor',
      };
    }

    // Create sponsor domain with 100% discount
    return await createSponsorDomain(
      sponsorId,
      domain,
      sponsor.sponsor_name,
      100, // 100% discount for sponsor attendees
      'percentage'
    );
  } catch (error) {
    console.error('Error auto-creating sponsor domain:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
