'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { FiUser, FiEye } from 'react-icons/fi';
import { Badge } from '@/components/ui/badge';
import { useFileUrl } from '@/hooks/useSignedUrl';

interface HeadshotThumbnailProps {
  headshotUrl: string | null;
  speakerName: string;
  className?: string;
  showViewButton?: boolean;
}

export function HeadshotThumbnail({
  headshotUrl,
  speakerName,
  className = '',
  showViewButton = true,
}: HeadshotThumbnailProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Use the useFileUrl hook to handle signed URL conversion
  const { signedUrl, loading, error } = useFileUrl(
    headshotUrl,
    'iepa-headshots'
  );

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  const handleViewFullSize = () => {
    if (signedUrl) {
      window.open(signedUrl, '_blank');
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center animate-pulse">
          <FiUser className="w-6 h-6 text-gray-400" />
        </div>
      </div>
    );
  }

  // Show error state or no headshot
  if (error || !signedUrl || imageError) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
          <FiUser className="w-6 h-6 text-gray-400" />
        </div>
        <Badge className="bg-red-100 text-red-800 text-xs">
          {error ? 'Error' : 'Missing'}
        </Badge>
      </div>
    );
  }

  // Show headshot thumbnail
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className="relative group">
        <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 border-2 border-gray-200">
          {imageLoading && (
            <div className="w-full h-full flex items-center justify-center animate-pulse">
              <FiUser className="w-6 h-6 text-gray-400" />
            </div>
          )}
          <Image
            src={signedUrl}
            alt={`${speakerName} headshot`}
            width={48}
            height={48}
            className={`w-full h-full object-cover transition-opacity duration-200 ${
              imageLoading ? 'opacity-0' : 'opacity-100'
            }`}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </div>

        {/* View button overlay on hover */}
        {showViewButton && !imageLoading && !imageError && (
          <button
            onClick={handleViewFullSize}
            className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            title="View full size"
          >
            <FiEye className="w-4 h-4 text-white" />
          </button>
        )}
      </div>

      <Badge className="bg-green-100 text-green-800 text-xs">Uploaded</Badge>
    </div>
  );
}
