'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@/components/ui';
import Link from 'next/link';
import {
  FaExclamationTriangle,
  FaArrowLeft,
  FaHome,
  FaQuestionCircle,
} from 'react-icons/fa';
import { CONFERENCE_YEAR } from '@/lib/conference-config';

function PaymentCancelContent() {
  const searchParams = useSearchParams();
  const registrationId = searchParams?.get('registration_id');

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-2xl mx-auto">
          {/* Cancel Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
              <FaExclamationTriangle className="w-8 h-8 text-yellow-600" />
            </div>
            <h1 className="iepa-heading-1 text-yellow-600 mb-2">
              Payment Cancelled
            </h1>
            <p className="iepa-body text-gray-600">
              Your payment for the IEPA {CONFERENCE_YEAR} Annual Conference was
              cancelled
            </p>
          </div>

          {/* Information Card */}
          <Card className="mb-6 border-yellow-200">
            <CardHeader>
              <h2 className="iepa-heading-2">What Happened?</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <p className="iepa-body">
                  You cancelled the payment process before completing your
                  registration. Your registration has not been confirmed and no
                  payment has been processed.
                </p>

                {registrationId && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="iepa-body-small">
                      <strong>Registration ID:</strong> {registrationId}
                    </p>
                    <p className="iepa-body-small text-gray-600 mt-1">
                      Your registration information has been saved. You can
                      complete the payment process later.
                    </p>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>

          {/* Next Steps */}
          <Card className="mb-6">
            <CardHeader>
              <h3 className="iepa-heading-3">What Can You Do Now?</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <FaArrowLeft className="w-5 h-5 text-blue-600 mt-1" />
                  <div>
                    <p className="iepa-body font-semibold">
                      Complete Your Registration
                    </p>
                    <p className="iepa-body-small text-gray-600">
                      Return to the registration form to complete your payment
                      and secure your spot.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <FaQuestionCircle className="w-5 h-5 text-blue-600 mt-1" />
                  <div>
                    <p className="iepa-body font-semibold">Need Help?</p>
                    <p className="iepa-body-small text-gray-600">
                      If you experienced any issues during payment, please
                      contact our support team.
                    </p>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button
              as={Link}
              href={
                registrationId
                  ? `/register/attendee?continue=${registrationId}`
                  : '/register'
              }
              color="primary"
              size="lg"
              className="flex items-center gap-2"
            >
              <FaArrowLeft className="w-4 h-4" />
              Complete Registration
            </Button>

            <Button
              as={Link}
              href="/"
              variant="bordered"
              size="lg"
              className="flex items-center gap-2"
            >
              <FaHome className="w-4 h-4" />
              Return to Homepage
            </Button>
          </div>

          {/* Help Section */}
          <Card className="bg-gray-50">
            <CardHeader>
              <h3 className="iepa-heading-3">Need Assistance?</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                <p className="iepa-body">
                  If you&apos;re having trouble with the payment process or have
                  questions about registration, we&apos;re here to help:
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="iepa-body-small font-semibold text-gray-600">
                      Email Support
                    </p>
                    <p className="iepa-body">
                      <a
                        href="mailto:<EMAIL>"
                        className="text-blue-600 hover:underline"
                      >
                        <EMAIL>
                      </a>
                    </p>
                  </div>

                  <div>
                    <p className="iepa-body-small font-semibold text-gray-600">
                      Phone Support
                    </p>
                    <p className="iepa-body">
                      <a
                        href="tel:+***********"
                        className="text-blue-600 hover:underline"
                      >
                        (*************
                      </a>
                    </p>
                  </div>
                </div>

                <div className="pt-3 border-t border-gray-200">
                  <p className="iepa-body-small text-gray-600">
                    <strong>Business Hours:</strong> Monday - Friday, 9:00 AM -
                    5:00 PM PST
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Common Issues */}
          <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
            <h4 className="iepa-heading-4 text-yellow-800 mb-2">
              Common Payment Issues
            </h4>
            <ul className="iepa-body-small text-yellow-700 space-y-1">
              <li>• Browser blocking pop-ups or redirects</li>
              <li>• Credit card information entered incorrectly</li>
              <li>• Insufficient funds or card declined by bank</li>
              <li>• Network connectivity issues</li>
              <li>• Session timeout due to inactivity</li>
            </ul>
            <p className="iepa-body-small text-yellow-700 mt-2">
              Most issues can be resolved by trying again or using a different
              payment method.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default function PaymentCancelPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentCancelContent />
    </Suspense>
  );
}
