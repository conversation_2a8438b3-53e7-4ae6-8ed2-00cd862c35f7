import { test, expect } from '@playwright/test';

/**
 * Registration Form Selectors Test
 * 
 * This test focuses on verifying that our form selectors are correct
 * by testing the registration form without authentication requirements.
 * This helps us debug selector issues separately from auth issues.
 */

test.describe('Registration Form Selectors Test', () => {
  test('should find and interact with registration form elements', async ({ page }) => {
    console.log('🧪 Testing registration form selectors...');

    // First login with working credentials
    console.log('🔐 Logging in with Golf Test User...');
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');

    // Fill email
    await page.fill('input[type="email"]', '<EMAIL>');

    // Switch to password mode
    await page.click('text=or use password');
    await page.waitForTimeout(1000);

    // Fill password
    await page.fill('input[type="password"]', 'GolfTest123!');

    // Submit
    await page.click('button:has-text("Sign In with Password")');
    await page.waitForLoadState('networkidle');

    // Navigate to registration page
    await page.goto('/register/attendee');
    await page.waitForLoadState('networkidle');
    
    // Take initial screenshot
    await page.screenshot({
      path: 'test-results/form-selectors-01-initial-page.png',
      fullPage: true,
    });
    
    console.log(`📍 Current URL: ${page.url()}`);
    console.log(`📄 Page title: ${await page.title()}`);
    
    // Test 1: Check if we can find the registration type field
    console.log('🔍 Looking for registration type field...');
    
    const registrationTypeSelectors = [
      '[data-testid="registration-type-field"]',
      '[data-testid*="registration-type"]',
      'input[name*="registration"]',
      'select[name*="registration"]',
      '[data-value*="iepa"]',
      '[data-value*="non-iepa"]'
    ];
    
    let foundRegistrationType = false;
    for (const selector of registrationTypeSelectors) {
      try {
        const element = await page.locator(selector).first();
        const isVisible = await element.isVisible();
        console.log(`  ${selector}: ${isVisible ? '✅ Found' : '❌ Not visible'}`);
        if (isVisible) {
          foundRegistrationType = true;
          
          // Take screenshot of found element
          await element.screenshot({
            path: `test-results/form-selectors-registration-type-${selector.replace(/[^a-zA-Z0-9]/g, '_')}.png`
          });
        }
      } catch (error) {
        console.log(`  ${selector}: ❌ Not found`);
      }
    }
    
    // Test 2: Check for personal information fields
    console.log('🔍 Looking for personal information fields...');
    
    const personalInfoSelectors = [
      '#first-name-input',
      '#last-name-input',
      '#email-input',
      '#name-on-badge-input',
      'input[placeholder*="first name"]',
      'input[placeholder*="last name"]',
      'input[placeholder*="email"]',
      '[data-testid*="first-name"]',
      '[data-testid*="last-name"]',
      '[data-testid*="email"]'
    ];
    
    for (const selector of personalInfoSelectors) {
      try {
        const element = await page.locator(selector).first();
        const isVisible = await element.isVisible();
        console.log(`  ${selector}: ${isVisible ? '✅ Found' : '❌ Not visible'}`);
      } catch (error) {
        console.log(`  ${selector}: ❌ Not found`);
      }
    }
    
    // Test 3: Check for contact information fields
    console.log('🔍 Looking for contact information fields...');
    
    const contactInfoSelectors = [
      'input[placeholder*="phone"]',
      'input[type="tel"]',
      'input[placeholder*="address"]',
      'input[placeholder*="city"]',
      'input[placeholder*="zip"]',
      'select[name*="state"]',
      '[data-testid*="phone"]',
      '[data-testid*="address"]'
    ];
    
    for (const selector of contactInfoSelectors) {
      try {
        const element = await page.locator(selector).first();
        const isVisible = await element.isVisible();
        console.log(`  ${selector}: ${isVisible ? '✅ Found' : '❌ Not visible'}`);
      } catch (error) {
        console.log(`  ${selector}: ❌ Not found`);
      }
    }
    
    // Test 4: Check for golf-related fields
    console.log('🔍 Looking for golf tournament fields...');
    
    const golfSelectors = [
      'input[type="checkbox"][id*="golf"]',
      'input[type="checkbox"][name*="golf"]',
      'label:has-text("Golf") input',
      '[data-testid*="golf"]',
      'input[value*="golf"]',
      'select[name*="handedness"]'
    ];
    
    for (const selector of golfSelectors) {
      try {
        const element = await page.locator(selector).first();
        const isVisible = await element.isVisible();
        console.log(`  ${selector}: ${isVisible ? '✅ Found' : '❌ Not visible'}`);
      } catch (error) {
        console.log(`  ${selector}: ❌ Not found`);
      }
    }
    
    // Test 5: Check for navigation buttons
    console.log('🔍 Looking for navigation buttons...');
    
    const buttonSelectors = [
      'button:has-text("Next")',
      'button:has-text("Previous")',
      'button:has-text("Submit")',
      'button:has-text("Complete")',
      'button[type="submit"]',
      '[data-testid*="next"]',
      '[data-testid*="submit"]'
    ];
    
    for (const selector of buttonSelectors) {
      try {
        const element = await page.locator(selector).first();
        const isVisible = await element.isVisible();
        console.log(`  ${selector}: ${isVisible ? '✅ Found' : '❌ Not visible'}`);
      } catch (error) {
        console.log(`  ${selector}: ❌ Not found`);
      }
    }
    
    // Test 6: Check page structure
    console.log('🔍 Analyzing page structure...');
    
    // Get all form elements
    const forms = await page.locator('form').count();
    const inputs = await page.locator('input').count();
    const buttons = await page.locator('button').count();
    const selects = await page.locator('select').count();
    
    console.log(`  Forms found: ${forms}`);
    console.log(`  Inputs found: ${inputs}`);
    console.log(`  Buttons found: ${buttons}`);
    console.log(`  Selects found: ${selects}`);
    
    // Check for authentication requirements
    const authIndicators = [
      'text=Sign In',
      'text=Login',
      'text=Authentication Required',
      'text=Please log in',
      '[data-testid*="auth"]',
      '[data-testid*="login"]'
    ];
    
    let authRequired = false;
    for (const selector of authIndicators) {
      try {
        const element = await page.locator(selector).first();
        const isVisible = await element.isVisible();
        if (isVisible) {
          console.log(`  🔐 Authentication indicator found: ${selector}`);
          authRequired = true;
        }
      } catch (error) {
        // Ignore
      }
    }
    
    if (authRequired) {
      console.log('⚠️ Page appears to require authentication');
    } else {
      console.log('✅ Page appears to be accessible without authentication');
    }
    
    // COMPREHENSIVE FIELD DISCOVERY
    console.log('🔍 DISCOVERING ALL ACTUAL FORM FIELDS...');

    // Get all input elements and their attributes
    const allInputs = await page.locator('input').all();
    console.log(`\n📋 FOUND ${allInputs.length} INPUT FIELDS:`);

    for (let i = 0; i < allInputs.length; i++) {
      const input = allInputs[i];
      try {
        const id = await input.getAttribute('id') || 'NO_ID';
        const name = await input.getAttribute('name') || 'NO_NAME';
        const placeholder = await input.getAttribute('placeholder') || 'NO_PLACEHOLDER';
        const type = await input.getAttribute('type') || 'text';
        const testId = await input.getAttribute('data-testid') || 'NO_TESTID';
        const isVisible = await input.isVisible();

        console.log(`  ${i + 1}. ${isVisible ? '✅' : '❌'} id="${id}" name="${name}" type="${type}" placeholder="${placeholder}" data-testid="${testId}"`);
      } catch (error) {
        console.log(`  ${i + 1}. ❌ Error reading input attributes`);
      }
    }

    // Get all select elements
    const allSelects = await page.locator('select').all();
    console.log(`\n📋 FOUND ${allSelects.length} SELECT FIELDS:`);

    for (let i = 0; i < allSelects.length; i++) {
      const select = allSelects[i];
      try {
        const id = await select.getAttribute('id') || 'NO_ID';
        const name = await select.getAttribute('name') || 'NO_NAME';
        const testId = await select.getAttribute('data-testid') || 'NO_TESTID';
        const isVisible = await select.isVisible();

        console.log(`  ${i + 1}. ${isVisible ? '✅' : '❌'} id="${id}" name="${name}" data-testid="${testId}"`);
      } catch (error) {
        console.log(`  ${i + 1}. ❌ Error reading select attributes`);
      }
    }

    // Get all buttons
    const allButtons = await page.locator('button').all();
    console.log(`\n📋 FOUND ${allButtons.length} BUTTONS:`);

    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      try {
        const text = await button.textContent() || 'NO_TEXT';
        const type = await button.getAttribute('type') || 'button';
        const testId = await button.getAttribute('data-testid') || 'NO_TESTID';
        const isVisible = await button.isVisible();

        console.log(`  ${i + 1}. ${isVisible ? '✅' : '❌'} type="${type}" text="${text.trim()}" data-testid="${testId}"`);
      } catch (error) {
        console.log(`  ${i + 1}. ❌ Error reading button attributes`);
      }
    }

    // Take final screenshot
    await page.screenshot({
      path: 'test-results/form-selectors-02-final-analysis.png',
      fullPage: true,
    });

    // ADDITIONAL COMPREHENSIVE ANALYSIS
    console.log('\n🔍 ANALYZING FORM SECTIONS AND FIELD RELATIONSHIPS...');

    // Try to identify which fields belong to which sections
    console.log('\n📋 PERSONAL INFORMATION FIELDS:');
    const personalFields = [
      '#first-name-input',
      '#last-name-input',
      'input[placeholder*="badge"]',
      'input[placeholder*="email"]',
      'input[placeholder*="organization"]',
      'input[placeholder*="job title"]'
    ];

    for (const selector of personalFields) {
      try {
        const element = page.locator(selector).first();
        const isVisible = await element.isVisible();
        const placeholder = await element.getAttribute('placeholder') || 'NO_PLACEHOLDER';
        console.log(`  ${isVisible ? '✅' : '❌'} ${selector} - "${placeholder}"`);
      } catch (error) {
        console.log(`  ❌ ${selector} - NOT FOUND`);
      }
    }

    console.log('\n📋 CONTACT INFORMATION FIELDS:');
    const contactFields = [
      'input[type="tel"]',
      'input[placeholder*="address"]',
      'input[placeholder="City"]',
      'input[placeholder*="12345"]',
      'input[placeholder*="Country"]',
      'button:has-text("Select state")'
    ];

    for (const selector of contactFields) {
      try {
        const element = page.locator(selector).first();
        const isVisible = await element.isVisible();
        const placeholder = await element.getAttribute('placeholder') || 'NO_PLACEHOLDER';
        console.log(`  ${isVisible ? '✅' : '❌'} ${selector} - "${placeholder}"`);
      } catch (error) {
        console.log(`  ❌ ${selector} - NOT FOUND`);
      }
    }

    console.log('\n📋 EVENT/GOLF CHECKBOXES:');
    const checkboxes = await page.locator('input[type="checkbox"]').all();
    for (let i = 0; i < checkboxes.length; i++) {
      try {
        const checkbox = checkboxes[i];
        const isVisible = await checkbox.isVisible();
        const id = await checkbox.getAttribute('id') || 'NO_ID';
        const name = await checkbox.getAttribute('name') || 'NO_NAME';

        // Try to find associated label text
        let labelText = 'NO_LABEL';
        try {
          const label = page.locator(`label[for="${id}"]`).first();
          labelText = await label.textContent() || 'NO_LABEL_TEXT';
        } catch (e) {
          // Try to find parent label
          try {
            const parentLabel = checkbox.locator('xpath=ancestor::label[1]');
            labelText = await parentLabel.textContent() || 'PARENT_LABEL';
          } catch (e2) {
            labelText = 'NO_ASSOCIATED_LABEL';
          }
        }

        console.log(`  ${i + 1}. ${isVisible ? '✅' : '❌'} id="${id}" name="${name}" label="${labelText.trim()}"`);
      } catch (error) {
        console.log(`  ${i + 1}. ❌ Error reading checkbox`);
      }
    }

    console.log('\n📋 EMERGENCY CONTACT FIELDS:');
    const emergencyFields = [
      'input[placeholder*="Contact person"]',
      'input[placeholder*="emergency"]',
      'input[placeholder*="relationship"]'
    ];

    for (const selector of emergencyFields) {
      try {
        const element = page.locator(selector).first();
        const isVisible = await element.isVisible();
        const placeholder = await element.getAttribute('placeholder') || 'NO_PLACEHOLDER';
        console.log(`  ${isVisible ? '✅' : '❌'} ${selector} - "${placeholder}"`);
      } catch (error) {
        console.log(`  ❌ ${selector} - NOT FOUND`);
      }
    }

    console.log('\n📋 SUBMIT/ACTION BUTTONS:');
    const actionButtons = [
      '[data-testid="submit-registration-button"]',
      'button:has-text("Complete Registration")',
      'button[type="submit"]',
      'button:has-text("Apply")',
      'button:has-text("Add")'
    ];

    for (const selector of actionButtons) {
      try {
        const element = page.locator(selector).first();
        const isVisible = await element.isVisible();
        const text = await element.textContent() || 'NO_TEXT';
        console.log(`  ${isVisible ? '✅' : '❌'} ${selector} - "${text.trim()}"`);
      } catch (error) {
        console.log(`  ❌ ${selector} - NOT FOUND`);
      }
    }

    console.log('\n🎯 COMPREHENSIVE FIELD DISCOVERY COMPLETE!');
    console.log('📝 Copy the ✅ selectors above to update your main test.');

    // The test passes if we can analyze the page structure
    expect(true).toBe(true);
  });
});
