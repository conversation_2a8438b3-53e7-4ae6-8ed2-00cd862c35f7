-- Add missing DELETE and UPDATE policies for storage objects
-- This fixes file upload/replacement functionality

-- Storage policies for presentations - DELETE
CREATE POLICY "Users can delete their own presentations"
    ON storage.objects FOR DELETE
    USING (bucket_id = 'iepa-presentations' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for presentations - UPDATE
CREATE POLICY "Users can update their own presentations"
    ON storage.objects FOR UPDATE
    USING (bucket_id = 'iepa-presentations' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for sponsor assets - DELETE
CREATE POLICY "Users can delete their own sponsor assets"
    ON storage.objects FOR DELETE
    USING (bucket_id = 'iepa-sponsor-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for sponsor assets - UPDATE
CREATE POLICY "Users can update their own sponsor assets"
    ON storage.objects FOR UPDATE
    USING (bucket_id = 'iepa-sponsor-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for PDF documents - DELETE
CREATE POLICY "Users can delete their own PDF documents"
    ON storage.objects FOR DELETE
    USING (bucket_id = 'iepa-documents' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for PDF documents - UPDATE
CREATE POLICY "Users can update their own PDF documents"
    ON storage.objects FOR UPDATE
    USING (bucket_id = 'iepa-documents' AND auth.uid()::text = (storage.foldername(name))[1]);
