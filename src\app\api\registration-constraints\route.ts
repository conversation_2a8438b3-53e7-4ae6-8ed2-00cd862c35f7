import { NextRequest, NextResponse } from 'next/server';
import { checkRegistrationConstraints } from '@/services/registrationConstraints';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const registrationType = searchParams.get('registrationType') as 'attendee' | 'speaker' | 'sponsor';
    const attendeeType = searchParams.get('attendeeType') as 'attendee' | 'spouse' | 'child' | undefined;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (!registrationType || !['attendee', 'speaker', 'sponsor'].includes(registrationType)) {
      return NextResponse.json(
        { error: 'Valid registration type is required (attendee, speaker, sponsor)' },
        { status: 400 }
      );
    }

    const result = await checkRegistrationConstraints(
      userId,
      registrationType,
      attendeeType || 'attendee'
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error checking registration constraints:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to check registration constraints',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, registrationType, attendeeType } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (!registrationType || !['attendee', 'speaker', 'sponsor'].includes(registrationType)) {
      return NextResponse.json(
        { error: 'Valid registration type is required (attendee, speaker, sponsor)' },
        { status: 400 }
      );
    }

    const result = await checkRegistrationConstraints(
      userId,
      registrationType,
      attendeeType || 'attendee'
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error checking registration constraints:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to check registration constraints',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
