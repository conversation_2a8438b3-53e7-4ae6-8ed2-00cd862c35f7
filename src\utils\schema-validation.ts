// Schema Validation System for IEPA 2025 Annual Meeting Registration
// Comprehensive validation utilities for all form schemas

import { MEAL_SCHEDULE } from '@/lib/conference-config';
import {
  REGISTRATION_PRICING,
  SPONSORSHIP_PACKAGES,
} from '@/lib/pricing-config';
import { schemaValidation } from './schema-utils';

// Import schema files
import attendeeSchema from '@/schemas/attendee-iepa-2025.json';
import speakerSchema from '@/schemas/speaker-iepa-2025.json';
import sponsorSchema from '@/schemas/sponsor-iepa-2025.json';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fieldErrors: Record<string, string[]>;
}

export interface SchemaValidationOptions {
  strict?: boolean; // Enable strict validation
  checkDependencies?: boolean; // Validate against config dependencies
  validateFiles?: boolean; // Validate file upload constraints
}

// Type definitions for form data
export interface AttendeeFormData {
  registrationType?: string;
  personalInfo?: {
    email?: string;
    firstName?: string;
    lastName?: string;
  };
  contactInfo?: {
    phoneNumber?: string;
    zipCode?: string;
  };
  eventOptions?: {
    meals?: string[];
  };
}

export interface SpeakerFormData {
  personalInfo?: {
    email?: string;
    firstName?: string;
    lastName?: string;
  };
  contactInfo?: {
    phoneNumber?: string;
  };
  presentationInfo?: {
    presentationTitle?: string;
    bio?: string;
    presentationAbstract?: string;
  };
}

export interface SponsorFormData {
  sponsorshipLevel?: string;
  sponsorInfo?: {
    sponsorName?: string;
    sponsorUrl?: string;
    sponsorDescription?: string;
  };
  contactInfo?: {
    contactEmail?: string;
    contactPhone?: string;
  };
}

/**
 * Validate attendee registration data against schema
 */
export const validateAttendeeData = (
  data: AttendeeFormData,
  options: SchemaValidationOptions = {}
): ValidationResult => {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    fieldErrors: {},
  };

  // Basic required field validation
  if (!data.registrationType) {
    result.errors.push('Registration type is required');
    result.fieldErrors.registrationType = ['Registration type is required'];
  }

  // Validate registration type against pricing config
  if (data.registrationType && options.checkDependencies !== false) {
    const validTypes = REGISTRATION_PRICING.map(r => r.id);
    if (!validTypes.includes(data.registrationType)) {
      result.errors.push(`Invalid registration type: ${data.registrationType}`);
      result.fieldErrors.registrationType = ['Invalid registration type'];
    }
  }

  // Validate personal info
  if (data.personalInfo) {
    const personalInfo = data.personalInfo;

    if (
      !personalInfo.email ||
      !schemaValidation.isValidEmail(personalInfo.email)
    ) {
      result.errors.push('Valid email address is required');
      result.fieldErrors['personalInfo.email'] = [
        'Valid email address is required',
      ];
    }

    if (!personalInfo.firstName || personalInfo.firstName.trim().length === 0) {
      result.errors.push('First name is required');
      result.fieldErrors['personalInfo.firstName'] = ['First name is required'];
    }

    if (!personalInfo.lastName || personalInfo.lastName.trim().length === 0) {
      result.errors.push('Last name is required');
      result.fieldErrors['personalInfo.lastName'] = ['Last name is required'];
    }
  }

  // Validate contact info
  if (data.contactInfo) {
    const contactInfo = data.contactInfo;

    if (
      !contactInfo.phoneNumber ||
      !schemaValidation.isValidPhoneNumber(contactInfo.phoneNumber)
    ) {
      result.errors.push('Valid phone number is required');
      result.fieldErrors['contactInfo.phoneNumber'] = [
        'Valid phone number is required',
      ];
    }

    if (
      !contactInfo.zipCode ||
      !schemaValidation.isValidZipCode(contactInfo.zipCode)
    ) {
      result.errors.push('Valid ZIP code is required');
      result.fieldErrors['contactInfo.zipCode'] = [
        'Valid ZIP code is required',
      ];
    }
  }

  // Validate meal selections
  if (data.eventOptions?.meals && options.checkDependencies !== false) {
    const validMealIds = MEAL_SCHEDULE.map(meal => meal.id);
    const invalidMeals = data.eventOptions.meals.filter(
      (mealId: string) => !validMealIds.includes(mealId)
    );

    if (invalidMeals.length > 0) {
      result.errors.push(`Invalid meal selections: ${invalidMeals.join(', ')}`);
      result.fieldErrors['eventOptions.meals'] = ['Invalid meal selections'];
    }
  }

  result.isValid = result.errors.length === 0;
  return result;
};

/**
 * Validate speaker registration data against schema
 */
export const validateSpeakerData = (
  data: SpeakerFormData
): ValidationResult => {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    fieldErrors: {},
  };

  // Validate personal info
  if (data.personalInfo) {
    const personalInfo = data.personalInfo;

    if (
      !personalInfo.email ||
      !schemaValidation.isValidEmail(personalInfo.email)
    ) {
      result.errors.push('Valid email address is required');
      result.fieldErrors['personalInfo.email'] = [
        'Valid email address is required',
      ];
    }

    if (!personalInfo.firstName || personalInfo.firstName.trim().length === 0) {
      result.errors.push('First name is required');
      result.fieldErrors['personalInfo.firstName'] = ['First name is required'];
    }

    if (!personalInfo.lastName || personalInfo.lastName.trim().length === 0) {
      result.errors.push('Last name is required');
      result.fieldErrors['personalInfo.lastName'] = ['Last name is required'];
    }
  }

  // Validate contact info
  if (data.contactInfo) {
    const contactInfo = data.contactInfo;

    if (
      !contactInfo.phoneNumber ||
      !schemaValidation.isValidPhoneNumber(contactInfo.phoneNumber)
    ) {
      result.errors.push('Valid phone number is required');
      result.fieldErrors['contactInfo.phoneNumber'] = [
        'Valid phone number is required',
      ];
    }
  }

  // Validate presentation info
  if (data.presentationInfo) {
    const presentationInfo = data.presentationInfo;

    if (
      !presentationInfo.presentationTitle ||
      presentationInfo.presentationTitle.trim().length < 5
    ) {
      result.errors.push('Presentation title must be at least 5 characters');
      result.fieldErrors['presentationInfo.presentationTitle'] = [
        'Presentation title too short',
      ];
    }

    if (!presentationInfo.bio || presentationInfo.bio.trim().length < 50) {
      result.errors.push('Biography must be at least 50 characters');
      result.fieldErrors['presentationInfo.bio'] = ['Biography too short'];
    }

    if (
      !presentationInfo.presentationAbstract ||
      presentationInfo.presentationAbstract.trim().length < 100
    ) {
      result.errors.push(
        'Presentation abstract must be at least 100 characters'
      );
      result.fieldErrors['presentationInfo.presentationAbstract'] = [
        'Abstract too short',
      ];
    }
  }

  result.isValid = result.errors.length === 0;
  return result;
};

/**
 * Validate sponsor registration data against schema
 */
export const validateSponsorData = (
  data: SponsorFormData,
  _options: SchemaValidationOptions = {}
): ValidationResult => {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    fieldErrors: {},
  };

  // Validate sponsorship level against pricing config
  if (data.sponsorshipLevel && _options.checkDependencies !== false) {
    const validLevels = SPONSORSHIP_PACKAGES.map(s => s.id);
    if (!validLevels.includes(data.sponsorshipLevel)) {
      result.errors.push(`Invalid sponsorship level: ${data.sponsorshipLevel}`);
      result.fieldErrors.sponsorshipLevel = ['Invalid sponsorship level'];
    }
  }

  // Validate sponsor info
  if (data.sponsorInfo) {
    const sponsorInfo = data.sponsorInfo;

    if (
      !sponsorInfo.sponsorName ||
      sponsorInfo.sponsorName.trim().length === 0
    ) {
      result.errors.push('Sponsor name is required');
      result.fieldErrors['sponsorInfo.sponsorName'] = [
        'Sponsor name is required',
      ];
    }

    if (
      !sponsorInfo.sponsorUrl ||
      !schemaValidation.isValidUrl(sponsorInfo.sponsorUrl)
    ) {
      result.errors.push('Valid sponsor URL is required');
      result.fieldErrors['sponsorInfo.sponsorUrl'] = ['Valid URL is required'];
    }

    if (
      !sponsorInfo.sponsorDescription ||
      sponsorInfo.sponsorDescription.trim().length < 50
    ) {
      result.errors.push('Sponsor description must be at least 50 characters');
      result.fieldErrors['sponsorInfo.sponsorDescription'] = [
        'Description too short',
      ];
    }
  }

  // Validate contact info
  if (data.contactInfo) {
    const contactInfo = data.contactInfo;

    if (
      !contactInfo.contactEmail ||
      !schemaValidation.isValidEmail(contactInfo.contactEmail)
    ) {
      result.errors.push('Valid contact email is required');
      result.fieldErrors['contactInfo.contactEmail'] = [
        'Valid email is required',
      ];
    }

    if (
      !contactInfo.contactPhone ||
      !schemaValidation.isValidPhoneNumber(contactInfo.contactPhone)
    ) {
      result.errors.push('Valid contact phone is required');
      result.fieldErrors['contactInfo.contactPhone'] = [
        'Valid phone is required',
      ];
    }
  }

  result.isValid = result.errors.length === 0;
  return result;
};

/**
 * Validate all schemas against their configuration dependencies
 */
export const validateSchemaIntegrity = (): ValidationResult => {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    fieldErrors: {},
  };

  // Check attendee schema registration types
  const attendeeRegTypes = attendeeSchema.properties.registrationType.enum;
  const pricingRegTypes = REGISTRATION_PRICING.map(r => r.id);

  const missingRegTypes = pricingRegTypes.filter(
    type => !attendeeRegTypes.includes(type)
  );
  const extraRegTypes = attendeeRegTypes.filter(
    type => !pricingRegTypes.includes(type)
  );

  if (missingRegTypes.length > 0) {
    result.errors.push(
      `Attendee schema missing registration types: ${missingRegTypes.join(', ')}`
    );
  }

  if (extraRegTypes.length > 0) {
    result.warnings.push(
      `Attendee schema has extra registration types: ${extraRegTypes.join(', ')}`
    );
  }

  // Check sponsor schema sponsorship levels
  const sponsorLevels = sponsorSchema.properties.sponsorshipLevel.enum;
  const pricingSponsorLevels = SPONSORSHIP_PACKAGES.map(s => s.id);

  const missingSponsorLevels = pricingSponsorLevels.filter(
    level => !sponsorLevels.includes(level)
  );
  const extraSponsorLevels = sponsorLevels.filter(
    level => !pricingSponsorLevels.includes(level)
  );

  if (missingSponsorLevels.length > 0) {
    result.errors.push(
      `Sponsor schema missing sponsorship levels: ${missingSponsorLevels.join(', ')}`
    );
  }

  if (extraSponsorLevels.length > 0) {
    result.warnings.push(
      `Sponsor schema has extra sponsorship levels: ${extraSponsorLevels.join(', ')}`
    );
  }

  // Check meal options
  const schemaMealIds =
    attendeeSchema.properties.eventOptions.properties.meals.items.enum;
  const configMealIds = MEAL_SCHEDULE.map(meal => meal.id);

  const missingMeals = configMealIds.filter(id => !schemaMealIds.includes(id));
  const extraMeals = schemaMealIds.filter(id => !configMealIds.includes(id));

  if (missingMeals.length > 0) {
    result.errors.push(
      `Attendee schema missing meal options: ${missingMeals.join(', ')}`
    );
  }

  if (extraMeals.length > 0) {
    result.warnings.push(
      `Attendee schema has extra meal options: ${extraMeals.join(', ')}`
    );
  }

  result.isValid = result.errors.length === 0;
  return result;
};

/**
 * Get schema validation summary
 */
export const getSchemaValidationSummary = () => {
  const integrity = validateSchemaIntegrity();

  return {
    schemas: {
      attendee: {
        version: attendeeSchema._metadata.version,
        lastUpdated: attendeeSchema._metadata.lastUpdated,
      },
      speaker: {
        version: speakerSchema._metadata.version,
        lastUpdated: speakerSchema._metadata.lastUpdated,
      },
      sponsor: {
        version: sponsorSchema._metadata.version,
        lastUpdated: sponsorSchema._metadata.lastUpdated,
      },
    },
    integrity,
    configurationStatus: {
      registrationTypes: REGISTRATION_PRICING.length,
      sponsorshipLevels: SPONSORSHIP_PACKAGES.length,
      mealOptions: MEAL_SCHEDULE.length,
    },
  };
};
