import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // Configure for both Webpack and Turbopack
  webpack: (config, { isServer }) => {
    // Exclude SendGrid and other server-only packages from client-side bundle
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };

      // Exclude server-only packages from client bundle
      config.externals = config.externals || [];
      config.externals.push({
        '@sendgrid/mail': 'commonjs @sendgrid/mail',
        '@sendgrid/helpers': 'commonjs @sendgrid/helpers',
      });
    }

    return config;
  },

  // Turbopack configuration (stable in Next.js 15+)
  turbopack: {
    // Configure module resolution for Turbopack
    resolveAlias: {
      // Ensure proper resolution of UI components and paths
      '@/components': './src/components',
      '@/lib': './src/lib',
      '@/hooks': './src/hooks',
      '@/contexts': './src/contexts',
      '@/styles': './src/styles',
      '@/types': './src/types',
      '@/app': './src/app',
    },
    // Configure external packages that should not be bundled
    resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.json', '.css'],
    // Rules for handling specific file types and modules
    rules: {
      // Handle server-only packages - equivalent to webpack externals
      '*.server.ts': {
        loaders: [],
        as: '*.js',
      },
      '*.server.js': {
        loaders: [],
        as: '*.js',
      },
    },
  },



  // Environment variables for both Webpack and Turbopack
  env: {
    // Ensure environment variables are available in both bundlers
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
  },

  // Transpile packages that might need it for both bundlers
  transpilePackages: [
    '@supabase/supabase-js',
    '@stripe/stripe-js',
    '@heroui/react',
    '@heroui/theme',
    'lucide-react',
  ],

  // Development server configuration
  devIndicators: {
    position: 'bottom-right',
  },

  // ESLint configuration for production builds
  eslint: {
    // Temporarily disable ESLint during builds to allow deployment
    // TODO: Fix ESLint errors and re-enable
    ignoreDuringBuilds: true,
  },

  // TypeScript configuration for production builds
  typescript: {
    // Temporarily disable TypeScript checking during builds to allow deployment
    // TODO: Fix TypeScript errors and re-enable
    ignoreBuildErrors: true,
  },

  // Images configuration for external domains
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/storage/v1/object/**',
      },
      {
        protocol: 'https',
        hostname: 'uffhyhpcuedjsisczocy.supabase.co',
        port: '',
        pathname: '/storage/v1/object/**',
      },
    ],
  },
};

export default nextConfig;
