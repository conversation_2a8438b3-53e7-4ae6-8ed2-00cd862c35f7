# One-Registration-Per-User Constraint Implementation

## Overview

This document describes the implementation of the one-registration-per-user constraint for the IEPA conference registration system. Each authenticated user account is limited to creating only one primary registration per registration type (attendee, speaker, sponsor).

## Key Features

### 1. Database-Level Constraints

**Attendee Registrations:**
- Unique constraint on `user_id` for primary attendees only (`attendee_type = 'attendee'`)
- Spouse/child registrations are allowed multiple per user (linked via `linked_attendee_email`)
- Index: `idx_unique_primary_attendee_per_user`

**Speaker Registrations:**
- Unique constraint on `user_id`: `unique_speaker_per_user`
- One speaker registration per user account

**Sponsor Registrations:**
- Unique constraint on `user_id`: `unique_sponsor_per_user`
- One sponsor registration per user account

### 2. Frontend Validation

**Registration Pages:**
- Check existing registrations before showing forms
- Display "registration already exists" notice if user has existing registration
- Redirect to existing registration details with edit options

**Registration Selector:**
- Visual indicators showing registration status
- Disabled buttons for registration types where user already has registration
- Status chips showing "Already Registered" state

### 3. Backend API Validation

**Registration Submission:**
- Pre-submission constraint checks in all registration endpoints
- Proper error messages for duplicate registration attempts
- Graceful handling of constraint violations

### 4. User Experience Features

**Existing Registration Notice:**
- Comprehensive display of existing registration details
- Options to view, edit, or contact support
- Clear messaging about the one-registration limit

**Registration Status Display:**
- Real-time status indicators on registration selector
- Clear visual feedback (green checkmarks, orange warning icons)
- Contextual button text ("Already Registered" vs "Register")

## Implementation Files

### Core Services
- `src/services/registrationConstraints.ts` - Core constraint checking logic
- `src/hooks/useRegistrationConstraints.ts` - React hooks for constraint checking
- `src/components/registration/ExistingRegistrationNotice.tsx` - UI component for existing registrations

### Database
- `src/lib/database-schema.sql` - Updated schema with unique constraints
- `supabase/migrations/20241201000000_add_unique_registration_constraints.sql` - Migration file

### API Endpoints
- `src/app/api/registration-constraints/route.ts` - API for checking constraints
- `src/app/api/test-constraints/route.ts` - Testing endpoint for constraint validation

### Updated Registration Pages
- `src/app/register/attendee/page.tsx` - Attendee registration with constraint checks
- `src/app/register/speaker/page.tsx` - Speaker registration with constraint checks
- `src/app/register/sponsor/page.tsx` - Sponsor registration with constraint checks

### UI Components
- `src/components/ui/registration-type-selector.tsx` - Updated with status indicators

## Database Schema Changes

```sql
-- For attendee registrations: only primary attendees (not spouse/child) can have one registration per user
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_unique_primary_attendee_per_user 
    ON iepa_attendee_registrations (user_id) 
    WHERE attendee_type = 'attendee';

-- For speaker registrations: one registration per user
ALTER TABLE iepa_speaker_registrations 
    ADD CONSTRAINT unique_speaker_per_user UNIQUE (user_id);

-- For sponsor registrations: one registration per user  
ALTER TABLE iepa_sponsor_registrations 
    ADD CONSTRAINT unique_sponsor_per_user UNIQUE (user_id);
```

## API Usage

### Check Registration Constraints

```typescript
import { checkRegistrationConstraints } from '@/services/registrationConstraints';

const result = await checkRegistrationConstraints(userId, 'attendee', 'attendee');
if (!result.canRegister) {
  // Handle existing registration
  console.log(result.message);
  console.log(result.existingRegistration);
}
```

### React Hook Usage

```typescript
import { useRegistrationConstraints } from '@/hooks/useRegistrationConstraints';

const { constraintCheck, loading, error } = useRegistrationConstraints({
  registrationType: 'attendee',
  attendeeType: 'attendee',
  autoCheck: true,
});

if (constraintCheck && !constraintCheck.canRegister) {
  // Show existing registration notice
}
```

## Edge Cases Handled

### 1. Spouse/Child Registrations
- Multiple spouse/child registrations allowed per user
- Linked to primary attendee via `linked_attendee_email`
- No constraint on spouse/child `attendee_type`

### 2. Failed Payments
- Registrations with failed payments still count toward constraint
- Users must complete or cancel existing registration before creating new one

### 3. Admin Override
- Service role can bypass constraints for administrative purposes
- Admin interface can manage registrations regardless of constraints

### 4. Partial Registrations
- Draft registrations count toward constraint
- Users must complete or delete draft before starting new registration

## Testing

### Manual Testing
- Visit `/test-constraints` page (development only)
- Test constraint application and duplicate prevention
- Verify hook functionality and UI updates

### Automated Testing
- Database constraint tests via `/api/test-constraints`
- Frontend validation tests
- API endpoint validation tests

## Error Handling

### Database Level
- Unique constraint violations return specific error codes
- Graceful handling of constraint errors in API endpoints

### Frontend Level
- User-friendly error messages for constraint violations
- Redirect to existing registration with edit options
- Clear guidance on next steps

### API Level
- Proper HTTP status codes for constraint violations
- Detailed error messages for debugging
- Consistent error response format

## Security Considerations

### Row Level Security
- All constraint checks respect RLS policies
- Users can only see their own registration data
- Admin users have appropriate elevated permissions

### Authentication
- All constraint checks require authenticated user
- User ID validation in all API endpoints
- Proper session management

## Performance Considerations

### Database Indexes
- Unique indexes provide fast constraint checking
- Optimized queries for registration status checks
- Minimal impact on registration form performance

### Frontend Optimization
- Lazy loading of constraint checks
- Caching of registration status
- Debounced API calls for real-time updates

## Future Enhancements

### Potential Improvements
1. Time-based registration editing restrictions
2. Registration transfer between users
3. Bulk registration management for organizations
4. Advanced constraint rules (e.g., role-based limits)

### Monitoring
1. Constraint violation tracking
2. User experience metrics for blocked registrations
3. Performance monitoring for constraint checks

---

**Document Information**
**Document Type**: Implementation Guide
**Last Updated**: December 2024
**Document Version**: 1.0
**System Version**: v0.1.0
**Implementation Status**: ✅ Active
**Prepared By**: Technical Team
