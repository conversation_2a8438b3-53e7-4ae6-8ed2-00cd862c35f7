@import 'tailwindcss';

:root {
  --font-geist-sans: '<PERSON>eist Sans', system-ui, sans-serif;
  --font-geist-mono: 'Geist Mono', monospace;

  /* IEPA Official Brand Colors */
  --iepa-primary-blue: #396da4;
  --iepa-primary-blue-light: #4d7fb5;
  --iepa-primary-blue-dark: #2d5a8a;
  --iepa-secondary-green: #5eae50;
  --iepa-secondary-green-light: #72c164;
  --iepa-secondary-green-dark: #4a9b3c;
  --iepa-accent-teal: #17a2b8;
  --iepa-accent-teal-light: #20c4dc;
  --iepa-accent-teal-dark: #138496;

  /* Neutral Colors */
  --iepa-gray-50: #f8f9fa;
  --iepa-gray-100: #e9ecef;
  --iepa-gray-200: #dee2e6;
  --iepa-gray-300: #ced4da;
  --iepa-gray-400: #adb5bd;
  --iepa-gray-500: #6c757d;
  --iepa-gray-600: #495057;
  --iepa-gray-700: #343a40;
  --iepa-gray-800: #212529;
  --iepa-gray-900: #1a1e21;
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

/* Dark mode only when explicitly set via class */
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

body {
  font-family: var(--font-geist-sans);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
  position: relative;
}

/* Seamless footer integration - gradient fade-out at bottom of main content */
main::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 150px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 30%,
    rgba(255, 255, 255, 0.7) 70%,
    rgba(255, 255, 255, 0.9) 100%
  );
  pointer-events: none;
  z-index: 5; /* Higher z-index to ensure it's above tree layers */
}

/* Ensure main content has adequate bottom padding to prevent overlap with footer trees */
main {
  position: relative;
  padding-bottom: 150px; /* Add bottom padding to prevent content overlap */
  min-height: calc(100vh - 200px); /* Ensure adequate height */
}

/* IEPA Conference Design System
 *
 * PADDING STRATEGY:
 * - Mobile (base): 1rem (16px) for containers, 0.75rem (12px) for interactive elements
 * - Tablet (640px+): 1.5rem (24px) for containers, enhanced spacing
 * - Desktop (1024px+): 2rem (32px) for containers, optimal readability
 * - Accessibility: 44px minimum touch targets, consistent spacing patterns
 * - All padding values use rem units for scalability and consistency
 */

/* Container System - Standardized responsive padding */
.iepa-container {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem; /* 16px - Standard mobile padding */
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .iepa-container {
    padding-left: 1.5rem; /* 24px - Standard tablet padding */
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .iepa-container {
    padding-left: 2rem; /* 32px - Standard desktop padding */
    padding-right: 2rem;
  }
}

/* Form System - Standardized responsive padding */
.iepa-form-section {
  padding: 1rem; /* 16px - Standard mobile padding */
  display: flex;
  flex-direction: column;
  gap: 1.5rem; /* Consistent gap spacing */
}

@media (min-width: 640px) {
  .iepa-form-section {
    padding: 1.5rem; /* 24px - Standard tablet padding */
    gap: 2rem; /* Enhanced tablet gap */
  }
}

@media (min-width: 1024px) {
  .iepa-form-section {
    padding: 2rem; /* 32px - Standard desktop padding */
    gap: 2.5rem; /* Enhanced desktop gap */
  }
}

/* Form Field Styling - Compact layout with maintained accessibility */
.iepa-form-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem; /* Further reduced gap for compact layout */
  width: 100%;
  margin-bottom: 0.75rem; /* Further reduced bottom margin for tighter vertical rhythm */
}

.iepa-form-field .nextui-input-wrapper,
.iepa-form-field .nextui-select-wrapper,
.iepa-form-field .nextui-radio-group-wrapper {
  width: 100%;
}

/* Ensure proper spacing for form elements */
.iepa-form-field .nextui-input,
.iepa-form-field .nextui-select,
.iepa-form-field .nextui-radio-group {
  margin-bottom: 0;
}

/* Radio group specific styling */
.iepa-form-field .nextui-radio-group {
  gap: 1rem; /* Increased gap for better spacing */
  padding: 0.5rem 0; /* Added vertical padding */
}

.iepa-form-field .nextui-radio {
  margin-right: 1.25rem; /* Slightly increased margin */
}

/* Form Grid System - Compact responsive layout */
.iepa-form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem; /* Reduced base gap for compact layout */
  width: 100%;
  align-items: start; /* Align items to start for consistent alignment */
}

@media (min-width: 640px) {
  .iepa-form-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem; /* Reduced gap for tablet */
    column-gap: 1.5rem; /* Reduced column gap for compact layout */
  }
}

@media (min-width: 1024px) {
  .iepa-form-grid {
    gap: 1.5rem; /* Reduced for desktop */
    column-gap: 2rem; /* More compact column spacing */
  }
}

.iepa-form-grid-3 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  width: 100%;
}

@media (min-width: 640px) {
  .iepa-form-grid-3 {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .iepa-form-grid-3 {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }
}

/* Typography System */
.iepa-heading-1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--iepa-primary-blue);
  line-height: 1.2;
}

@media (min-width: 640px) {
  .iepa-heading-1 {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .iepa-heading-1 {
    font-size: 2.25rem;
  }
}

.iepa-heading-2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--iepa-primary-blue);
  line-height: 1.3;
}

@media (min-width: 640px) {
  .iepa-heading-2 {
    font-size: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .iepa-heading-2 {
    font-size: 1.875rem;
  }
}

.iepa-heading-3 {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--iepa-gray-800);
  line-height: 1.4;
}

@media (min-width: 640px) {
  .iepa-heading-3 {
    font-size: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .iepa-heading-3 {
    font-size: 1.5rem;
  }
}

.iepa-body-large {
  font-size: 1rem;
  color: var(--iepa-gray-700);
  line-height: 1.6;
}

@media (min-width: 640px) {
  .iepa-body-large {
    font-size: 1.125rem;
  }
}

.iepa-body {
  font-size: 0.875rem;
  color: var(--iepa-gray-600);
  line-height: 1.5;
}

@media (min-width: 640px) {
  .iepa-body {
    font-size: 1rem;
  }
}

.iepa-body-small {
  font-size: 0.75rem;
  color: var(--iepa-gray-500);
  line-height: 1.4;
}

@media (min-width: 640px) {
  .iepa-body-small {
    font-size: 0.875rem;
  }
}

/* Spacing System - Balanced section spacing */
.iepa-section {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

@media (min-width: 640px) {
  .iepa-section {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .iepa-section {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

/* Button System Extensions */
.iepa-button-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .iepa-button-group {
    flex-direction: row;
    gap: 1rem;
  }
}

.iepa-button-group-center {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  justify-content: center;
  align-items: center;
}

@media (min-width: 640px) {
  .iepa-button-group-center {
    flex-direction: row;
    gap: 1rem;
  }
}

/* Status and Feedback */
.iepa-status-success {
  background-color: rgba(46, 139, 87, 0.1);
  border-left: 4px solid var(--iepa-secondary-green);
  color: var(--iepa-secondary-green-dark);
  border-radius: 0.5rem;
  padding: 1rem;
}

.iepa-status-error {
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 4px solid #dc3545;
  color: #991b1b;
  border-radius: 0.5rem;
  padding: 1rem;
}

.iepa-status-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 4px solid #ffc107;
  color: #92400e;
  border-radius: 0.5rem;
  padding: 1rem;
}

.iepa-status-info {
  background-color: rgba(23, 162, 184, 0.1);
  border-left: 4px solid var(--iepa-accent-teal);
  color: var(--iepa-accent-teal-dark);
  border-radius: 0.5rem;
  padding: 1rem;
}

/* Form Card Enhancements - Compact responsive padding */
.iepa-form-card {
  background: white;
  border: 1px solid var(--iepa-gray-200);
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 0.75rem; /* Further reduced margin for compact layout */
  overflow: hidden;
  padding: 0.375rem; /* 6px - More compact mobile padding */
}

@media (min-width: 640px) {
  .iepa-form-card {
    padding: 0.5rem; /* 8px - More compact tablet padding */
  }
}

@media (min-width: 1024px) {
  .iepa-form-card {
    padding: 0.75rem; /* 12px - More compact desktop padding */
  }
}

.iepa-form-card .nextui-card-header {
  background: var(--iepa-gray-50);
  border-bottom: 1px solid var(--iepa-gray-200);
  padding: 0.375rem 0.375rem 0.25rem; /* 6px horizontal, 4px bottom - More compact mobile */
}

.iepa-form-card .nextui-card-body {
  padding: 0.375rem; /* 6px - More compact mobile padding */
}

@media (min-width: 640px) {
  .iepa-form-card .nextui-card-header {
    padding: 0.5rem 0.5rem 0.375rem; /* 8px horizontal, 6px bottom - More compact tablet */
  }

  .iepa-form-card .nextui-card-body {
    padding: 0.5rem; /* 8px - More compact tablet padding */
  }
}

@media (min-width: 1024px) {
  .iepa-form-card .nextui-card-header {
    padding: 0.75rem 0.75rem 0.5rem; /* 12px horizontal, 8px bottom - More compact desktop */
  }

  .iepa-form-card .nextui-card-body {
    padding: 0.75rem; /* 12px - More compact desktop padding */
  }
}

/* General Card Component Padding - Balanced responsive padding */
.nextui-card-body {
  padding: 0.75rem !important; /* 12px - Balanced mobile padding */
}

.nextui-card-header {
  padding: 0.75rem 0.75rem 0.5rem !important; /* 12px horizontal, 8px bottom - Balanced mobile */
}

@media (min-width: 640px) {
  .nextui-card-body {
    padding: 1rem !important; /* 16px - Balanced tablet padding */
  }

  .nextui-card-header {
    padding: 1rem 1rem 0.75rem !important; /* 16px horizontal, 12px bottom - Balanced tablet */
  }
}

@media (min-width: 1024px) {
  .nextui-card-body {
    padding: 1.25rem !important; /* 20px - Balanced desktop padding */
  }

  .nextui-card-header {
    padding: 1.25rem 1.25rem 1rem !important; /* 20px horizontal, 16px bottom - Balanced desktop */
  }
}

/* HeroUI Component Overrides for Forms - Standardized padding and accessibility */
.iepa-form-field [data-slot='input-wrapper'] {
  border: 1px solid var(--iepa-gray-300) !important;
  transition: all 0.2s ease;
  min-height: 44px !important; /* Minimum accessibility touch target */
  padding: 0.75rem 1rem !important; /* 12px vertical, 16px horizontal - Standard padding */
}

.iepa-form-field [data-slot='input-wrapper']:hover {
  border-color: var(--iepa-primary-blue-light) !important;
}

.iepa-form-field [data-slot='input-wrapper'][data-focus='true'] {
  border-color: var(--iepa-primary-blue) !important;
  box-shadow: 0 0 0 3px rgba(27, 79, 114, 0.1) !important;
}

.iepa-form-field [data-slot='trigger'] {
  border: 1px solid var(--iepa-gray-300) !important;
  transition: all 0.2s ease;
  min-height: 44px !important; /* Minimum accessibility touch target */
  padding: 0.75rem 1rem !important; /* 12px vertical, 16px horizontal - Standard padding */
}

.iepa-form-field [data-slot='trigger']:hover {
  border-color: var(--iepa-primary-blue-light) !important;
}

.iepa-form-field [data-slot='trigger'][data-focus='true'] {
  border-color: var(--iepa-primary-blue) !important;
  box-shadow: 0 0 0 3px rgba(27, 79, 114, 0.1) !important;
}

/* Enhanced input field internal padding - Standardized */
.iepa-form-field [data-slot='input'] {
  padding: 0.75rem 1rem !important; /* 12px vertical, 16px horizontal - Standard padding */
  font-size: 1rem !important; /* Ensure readable font size */
  line-height: 1.5 !important; /* Better line height */
}

.iepa-form-field [data-slot='listbox'] {
  padding: 0.5rem 0 !important; /* 8px vertical padding for dropdown container */
}

.iepa-form-field [data-slot='listbox'] [role='option'] {
  padding: 0.75rem 1rem !important; /* 12px vertical, 16px horizontal - Standard option padding */
  min-height: 44px !important; /* Minimum accessibility touch target size */
}

/* Radio button styling improvements - Standardized padding */
.iepa-form-field [data-slot='wrapper'] {
  padding: 0.75rem 0; /* 12px vertical padding - Standard */
}

.iepa-form-field [data-slot='wrapper'] > div {
  margin-bottom: 0.5rem; /* 8px spacing between options */
  padding: 0.25rem 0; /* 4px vertical padding for individual options */
}

/* Individual radio/checkbox items - Standardized padding and accessibility */
.iepa-form-field [data-slot='base'] {
  padding: 0.75rem 1rem !important; /* 12px vertical, 16px horizontal - Standard padding */
  border-radius: 0.5rem; /* Rounded corners for better visual appeal */
  transition: background-color 0.2s ease;
  min-height: 44px !important; /* Minimum accessibility touch target size */
}

.iepa-form-field [data-slot='base']:hover {
  background-color: var(--iepa-gray-50) !important; /* Subtle hover effect */
}

.iepa-form-field [data-selected='true'] [data-slot='control'] {
  background-color: var(--iepa-primary-blue) !important;
  border-color: var(--iepa-primary-blue) !important;
}

/* Checkbox styling improvements */
.iepa-form-field [data-selected='true'] [data-slot='icon'] {
  background-color: var(--iepa-primary-blue) !important;
  border-color: var(--iepa-primary-blue) !important;
}

/* Enhanced checkbox/radio labels - Standardized spacing */
.iepa-form-field [data-slot='label-wrapper'] {
  padding-left: 0.75rem !important; /* 12px space between control and label - Standard */
}

/* Form labels and descriptions - Enhanced for better spacing and readability */
.iepa-form-field [data-slot='label'] {
  color: var(--iepa-gray-700) !important;
  font-weight: 500 !important;
  margin-bottom: 0.5rem;
  font-size: 0.95rem !important; /* Slightly larger for better readability */
  line-height: 1.4 !important; /* Better line height */
}

.iepa-form-field [data-slot='description'] {
  color: var(--iepa-gray-500) !important;
  font-size: 0.875rem !important;
  margin-top: 0.5rem; /* Increased margin for better separation */
  line-height: 1.4 !important; /* Better line height */
}

/* Prevent label collision with input content - Standardized */
.iepa-form-field [data-slot='input'] {
  padding-top: 1rem !important; /* 16px space for floating labels - Standard */
}

.iepa-form-field
  [data-slot='input'][data-has-value='true']
  + [data-slot='label'] {
  transform: translateY(-0.5rem) scale(0.85) !important; /* Better label positioning when input has value */
}

/* Multi-step Form Progress Indicator - Balanced spacing and visibility */
.iepa-progress-container {
  margin-bottom: 1.5rem; /* Balanced margin for better flow */
  padding: 0.5rem 0; /* Reduced padding for tighter spacing */
}

/* Sticky Progress Indicator - Phase 1 Implementation */
.iepa-sticky-progress {
  position: sticky;
  top: 0;
  background: white;
  z-index: 100;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--iepa-gray-200);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 0; /* Remove bottom margin when sticky */
}

/* Adjust progress container when in sticky mode */
.iepa-sticky-progress .iepa-progress-container {
  margin-bottom: 0;
  padding: 0.5rem 0;
}

/* Compact progress header for sticky mode */
.iepa-sticky-progress .iepa-progress-header {
  margin-bottom: 1rem; /* Reduced margin for compact sticky layout */
}

/* Responsive adjustments for sticky progress */
@media (min-width: 640px) {
  .iepa-sticky-progress {
    padding: 1rem 1.5rem;
  }
}

@media (min-width: 1024px) {
  .iepa-sticky-progress {
    padding: 1rem 2rem;
  }
}

/* Smooth scroll behavior for better UX with sticky elements */
html {
  scroll-behavior: smooth;
}

/* Ensure form content doesn't jump when progress becomes sticky */
.iepa-form-step {
  min-height: 400px;
  padding: 1rem 0;
  transition: padding-top 0.2s ease; /* Smooth transition for padding adjustments */
}

/* Add subtle backdrop blur effect for sticky progress on scroll */
.iepa-sticky-progress {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px); /* Safari support */
  transition: all 0.2s ease;
}

/* Enhanced shadow when sticky element is active (detected via scroll) */
.iepa-sticky-progress.scrolled {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Phase 2: Compact Typography and Spacing */

/* Compact heading styles for registration form */
.iepa-compact-heading-1 {
  font-size: 1.375rem; /* Reduced from 1.5rem */
  font-weight: 700;
  color: var(--iepa-primary-blue);
  line-height: 1.2;
  margin-bottom: 0.5rem; /* Reduced from default */
}

@media (min-width: 640px) {
  .iepa-compact-heading-1 {
    font-size: 1.625rem; /* Reduced from 1.875rem */
  }
}

@media (min-width: 1024px) {
  .iepa-compact-heading-1 {
    font-size: 1.875rem; /* Reduced from 2.25rem */
  }
}

.iepa-compact-heading-2 {
  font-size: 1.125rem; /* Reduced from 1.25rem */
  font-weight: 600;
  color: var(--iepa-primary-blue);
  line-height: 1.3;
  margin: 0.25em 0; /* Compact margins */
}

@media (min-width: 640px) {
  .iepa-compact-heading-2 {
    font-size: 1.25rem; /* Reduced from 1.5rem */
  }
}

/* Compact body text for registration form */
.iepa-compact-body {
  margin: 0.25em 0; /* Reduced margins */
  font-size: 0.9rem; /* Slightly smaller */
  line-height: 1.3; /* Tighter line height */
}

/* Compact section styling */
.iepa-compact-section {
  padding: 0.75rem 0; /* Reduced from default section padding */
}

@media (min-width: 640px) {
  .iepa-compact-section {
    padding: 1rem 0;
  }
}

@media (min-width: 1024px) {
  .iepa-compact-section {
    padding: 1.25rem 0;
  }
}

/* Phase 3: Two-Column Review Section */

/* Review grid layout for better space utilization */
.iepa-review-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .iepa-review-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Review section styling */
.iepa-review-summary {
  order: 1;
}

.iepa-review-details {
  order: 2;
}

@media (max-width: 768px) {
  .iepa-review-summary {
    order: 1;
  }

  .iepa-review-details {
    order: 2;
  }
}

.iepa-progress-header {
  text-align: center;
  margin-bottom: 1rem; /* Reduced margin for compact layout */
}

.iepa-progress-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin: 0 auto;
  max-width: 900px; /* Slightly increased max-width */
  padding: 0.75rem 0; /* Reduced vertical padding for compact layout */
}

.iepa-progress-bar::before {
  content: '';
  position: absolute;
  top: 28px; /* Adjusted to account for larger step circles */
  left: 0;
  right: 0;
  height: 3px; /* Slightly thicker line */
  background-color: var(--iepa-gray-300);
  z-index: 1;
}

.iepa-progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: white;
  padding: 0 1rem; /* Increased horizontal padding */
  min-width: 140px; /* Increased min-width for better spacing */
}

.iepa-progress-step-number {
  width: 48px; /* Increased size for better visibility */
  height: 48px;
  border-radius: 50%;
  background-color: var(--iepa-gray-300);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1rem; /* Slightly larger font */
  margin-bottom: 0.75rem; /* Increased margin for better spacing */
  transition: all 0.3s ease;
  border: 2px solid transparent; /* Added border for better definition */
}

.iepa-progress-step-title {
  font-size: 0.85rem; /* Slightly larger font for better readability */
  text-align: center;
  color: var(--iepa-gray-600);
  font-weight: 500;
  line-height: 1.3; /* Improved line height */
  max-width: 120px; /* Prevent text from becoming too wide */
  min-height: 2.2rem; /* Force consistent height for exactly 2 lines */
  height: 2.2rem; /* Fixed height to ensure uniformity */
  display: flex;
  align-items: center;
  justify-content: center;
  word-break: break-word; /* Allow words to break for better wrapping */
  -webkit-hyphens: auto; /* Safari compatibility */
  hyphens: auto; /* Enable hyphenation for better text flow */
}

.iepa-progress-step-active .iepa-progress-step-number {
  background-color: var(--iepa-primary-blue);
  border-color: var(--iepa-primary-blue-dark);
  box-shadow: 0 2px 8px rgba(27, 79, 114, 0.2); /* Added subtle shadow */
}

.iepa-progress-step-current .iepa-progress-step-number {
  background-color: var(--iepa-primary-blue);
  border-color: var(--iepa-primary-blue-dark);
  box-shadow: 0 0 0 4px rgba(27, 79, 114, 0.15); /* Enhanced current step indicator */
}

.iepa-progress-step-active .iepa-progress-step-title {
  color: var(--iepa-primary-blue);
  font-weight: 600;
}

/* Clickable progress steps */
.iepa-progress-step-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.iepa-progress-step-clickable:hover {
  transform: translateY(-2px);
}

.iepa-progress-step-clickable:hover .iepa-progress-step-number {
  box-shadow: 0 4px 12px rgba(27, 79, 114, 0.25);
  transform: scale(1.05);
}

.iepa-progress-step-clickable:hover .iepa-progress-step-title {
  color: var(--iepa-primary-blue);
}

.iepa-progress-step-clickable:focus {
  outline: 2px solid var(--iepa-primary-blue);
  outline-offset: 4px;
  border-radius: 8px;
}

.iepa-progress-step-clickable:active {
  transform: translateY(0);
}

.iepa-progress-step-clickable:active .iepa-progress-step-number {
  transform: scale(0.98);
}

/* Warning state for incomplete steps */
.iepa-progress-step-warning .iepa-progress-step-number {
  border-color: #f59e0b;
  background-color: #fef3c7;
}

.iepa-progress-step-warning .iepa-progress-step-title {
  color: #d97706;
}

.iepa-progress-step-warning:hover .iepa-progress-step-number {
  border-color: #d97706;
  background-color: #fed7aa;
}

/* Responsive progress bar */
@media (max-width: 768px) {
  .iepa-progress-bar {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .iepa-progress-bar::before {
    display: none;
  }

  .iepa-progress-step {
    min-width: auto;
    flex: 1;
  }

  .iepa-progress-step-title {
    font-size: 0.7rem;
    min-height: 1.8rem; /* Adjusted for smaller mobile font size */
    height: 1.8rem; /* Maintain consistent height on mobile */
    max-width: 100px; /* Slightly smaller max-width for mobile */
  }
}

/* Enhanced Form Styling - Balanced spacing and anchoring */
.iepa-form-step {
  min-height: 400px;
  padding: 1rem 0; /* Balanced padding for better flow */
}

.iepa-form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 3rem; /* Increased margin for better separation */
  padding: 2rem 0 1rem; /* Enhanced padding with bottom padding */
  border-top: 2px solid var(--iepa-gray-200); /* Slightly thicker border for better definition */
  background-color: var(
    --iepa-gray-50
  ); /* Subtle background to anchor the navigation */
  border-radius: 0.5rem; /* Rounded corners for better visual appeal */
}

.iepa-form-navigation-buttons {
  display: flex;
  gap: 1.25rem; /* Increased gap between buttons */
}

/* Error Styling - Enhanced for better visibility and spacing */
.iepa-form-field-error {
  border-color: #dc2626 !important;
}

.iepa-error-message {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.5rem; /* Increased margin for better separation */
  margin-bottom: 0.25rem; /* Added bottom margin for vertical rhythm */
  display: flex;
  align-items: center;
  gap: 0.375rem; /* Increased gap for better spacing */
  padding: 0.25rem 0; /* Added vertical padding */
}

.iepa-error-message::before {
  content: '⚠';
  font-size: 0.875rem; /* Increased icon size */
  flex-shrink: 0; /* Prevent icon from shrinking */
}

/* Enhanced Section Headers */
.iepa-form-card .iepa-heading-2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--iepa-primary-blue);
  margin-bottom: 0.5rem;
}

/* Better Button Styling - Standardized padding and accessibility */
.iepa-form-navigation .iepa-button-primary {
  background-color: var(--iepa-secondary-green);
  border-color: var(--iepa-secondary-green);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem; /* 12px vertical, 24px horizontal - Standard button padding */
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  min-height: 44px; /* Minimum accessibility touch target size */
}

.iepa-form-navigation .iepa-button-primary:hover {
  background-color: var(--iepa-secondary-green-dark);
  border-color: var(--iepa-secondary-green-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(94, 174, 80, 0.2);
}

.iepa-form-navigation .iepa-button-secondary {
  background-color: transparent;
  border: 2px solid var(--iepa-gray-300);
  color: var(--iepa-gray-700);
  font-weight: 600;
  padding: 0.75rem 1.5rem; /* 12px vertical, 24px horizontal - Standard button padding */
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  min-height: 44px; /* Minimum accessibility touch target size */
}

.iepa-form-navigation .iepa-button-secondary:hover {
  border-color: var(--iepa-primary-blue);
  color: var(--iepa-primary-blue);
  background-color: var(--iepa-gray-50);
}

/* Accessibility Enhancements */
.iepa-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip to content link for screen readers */
.iepa-skip-link {
  position: absolute;
  top: -10rem;
  left: 1.5rem;
  z-index: 50;
  background-color: var(--iepa-primary-blue);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s;
}

.iepa-skip-link:focus {
  top: 1.5rem;
  outline: none;
}

/* Enhanced Registration Card Animations */
.registration-card-label {
  /* Ensure smooth transitions for all interactive states */
  transition-property: transform, box-shadow, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); /* ease-out */
  transition-duration: 250ms;
}

.registration-card-label:hover {
  /* Enhanced hover shadow with IEPA brand color tint */
  box-shadow: 0 8px 25px rgba(57, 109, 164, 0.15);
}

.registration-card-label:focus-within {
  /* Enhanced focus shadow for better accessibility */
  box-shadow:
    0 8px 25px rgba(57, 109, 164, 0.15),
    0 0 0 3px rgba(57, 109, 164, 0.3);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  /* Disable transforms for reduced motion */
  .registration-card-label:hover,
  .registration-card-label:focus-within {
    transform: none !important;
  }
}

/* Removed problematic @theme and @apply directives for compatibility */

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
