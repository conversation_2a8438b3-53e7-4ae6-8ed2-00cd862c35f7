'use client';

import React, { useState } from 'react';
import { Card, CardBody, CardHeader } from '@/components/ui';
import { Button } from '@/components/ui/button';
import {
  PDFDownloadButton,
  PDFStatus,
} from '@/components/pdf/PDFDownloadButton';
import { usePDFStorageSetup } from '@/hooks/usePDFGeneration';
import { FiCheck, FiX, FiLoader, FiSettings } from 'react-icons/fi';

export default function TestPDFPage() {
  const [testResults, setTestResults] = useState<{
    success: boolean;
    message: string;
    details?: {
      bucketExists: boolean;
      canUpload: boolean;
      canDownload: boolean;
    };
  } | null>(null);
  const [storageInfo, setStorageInfo] = useState<{
    bucketName: string;
    bucketExists: boolean;
    folders: string[];
    maxFileSize: number;
    allowedMimeTypes: string[];
  } | null>(null);
  const {
    loading,
    error,
    setupComplete,
    initializeStorage,
    testStorage,
    getStorageInfo,
  } = usePDFStorageSetup();

  // Sample registration data for testing
  const sampleRegistration = {
    id: 'test-registration-123',
    type: 'attendee' as const,
    paymentMethod: 'Credit Card',
    transactionId: 'txn_test_123456',
  };

  const handleTestStorage = async () => {
    try {
      const success = await testStorage();
      // Since testStorage returns boolean, we need to create the proper result object
      if (success) {
        setTestResults({
          success: true,
          message: 'Storage test completed successfully',
          details: {
            bucketExists: true,
            canUpload: true,
            canDownload: true,
          },
        });
      } else {
        setTestResults({
          success: false,
          message: 'Storage test failed',
          details: {
            bucketExists: false,
            canUpload: false,
            canDownload: false,
          },
        });
      }
    } catch (error) {
      console.error('Error testing storage:', error);
      setTestResults({
        success: false,
        message: 'Failed to test storage',
        details: {
          bucketExists: false,
          canUpload: false,
          canDownload: false,
        },
      });
    }
  };

  const handleGetStorageInfo = async () => {
    const info = await getStorageInfo();
    setStorageInfo(info);
  };

  const handleInitializeStorage = async () => {
    await initializeStorage();
    // Refresh info after initialization
    await handleGetStorageInfo();
    await handleTestStorage();
  };

  const handleDirectDownload = async (documentType: 'receipt' | 'invoice') => {
    try {
      const response = await fetch('/api/pdf/test-generate-direct', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          registrationId: sampleRegistration.id,
          registrationType: sampleRegistration.type,
          documentType,
          paymentMethod: sampleRegistration.paymentMethod,
          transactionId: sampleRegistration.transactionId,
        }),
      });

      if (response.ok) {
        // Create blob from response
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        // Create download link
        const link = document.createElement('a');
        link.href = url;
        link.download = `${documentType}-${sampleRegistration.type}-${sampleRegistration.id.slice(-8)}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up
        window.URL.revokeObjectURL(url);

        console.log(`${documentType} downloaded successfully`);
      } else {
        const errorData = await response.json();
        console.error(`Error downloading ${documentType}:`, errorData.error);
      }
    } catch (error) {
      console.error(`Error downloading ${documentType}:`, error);
    }
  };

  const handleSimplePDFTest = async () => {
    try {
      console.log('Testing simple PDF generation...');
      const response = await fetch('/api/pdf/test-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Create blob from response
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        // Create download link
        const link = document.createElement('a');
        link.href = url;
        link.download = 'test-simple.pdf';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up
        window.URL.revokeObjectURL(url);

        console.log('✅ Simple PDF test successful!');
      } else {
        const errorData = await response.json();
        console.error('❌ Simple PDF test failed:', errorData);
      }
    } catch (error) {
      console.error('❌ Simple PDF test error:', error);
    }
  };

  const handleWorkingReceiptTest = async () => {
    try {
      console.log('Testing working receipt PDF generation...');
      const response = await fetch('/api/pdf/test-working-receipt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          registrationId: sampleRegistration.id,
          registrationType: sampleRegistration.type,
        }),
      });

      if (response.ok) {
        // Create blob from response
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        // Create download link
        const link = document.createElement('a');
        link.href = url;
        link.download = `working-receipt-${sampleRegistration.type}-${sampleRegistration.id.slice(-8)}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up
        window.URL.revokeObjectURL(url);

        console.log('✅ Working receipt test successful!');
      } else {
        const errorData = await response.json();
        console.error('❌ Working receipt test failed:', errorData);
      }
    } catch (error) {
      console.error('❌ Working receipt test error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            PDF Generation Test Page
          </h1>
          <p className="text-gray-600">
            Test PDF receipt and invoice generation functionality for IEPA
            conference registration.
          </p>
        </div>

        {/* Storage Setup Section */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-xl font-semibold flex items-center">
              <FiSettings className="mr-2" />
              Storage Setup
            </h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex gap-4">
                <Button
                  onClick={handleInitializeStorage}
                  disabled={loading}
                  variant="default"
                >
                  {loading ? <FiLoader className="animate-spin mr-2" /> : null}
                  Initialize Storage
                </Button>
                <Button
                  onClick={handleTestStorage}
                  disabled={loading}
                  variant="outline"
                >
                  Test Storage
                </Button>
                <Button
                  onClick={handleGetStorageInfo}
                  disabled={loading}
                  variant="outline"
                >
                  Get Storage Info
                </Button>
              </div>

              {error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-700">Error: {error}</p>
                </div>
              )}

              {setupComplete && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-700 flex items-center">
                    <FiCheck className="mr-2" />
                    Storage setup is complete and working
                  </p>
                </div>
              )}

              {storageInfo && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="font-semibold mb-2">Storage Information:</h3>
                  <ul className="text-sm space-y-1">
                    <li>Bucket: {storageInfo.bucketName}</li>
                    <li>Exists: {storageInfo.bucketExists ? 'Yes' : 'No'}</li>
                    <li>Folders: {storageInfo.folders.join(', ')}</li>
                    <li>
                      Max File Size:{' '}
                      {(storageInfo.maxFileSize / 1024 / 1024).toFixed(1)} MB
                    </li>
                    <li>
                      Allowed Types: {storageInfo.allowedMimeTypes.join(', ')}
                    </li>
                  </ul>
                </div>
              )}

              {testResults && (
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <h3 className="font-semibold mb-2">Test Results:</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      {testResults.details?.bucketExists ? (
                        <FiCheck className="text-green-500 mr-2" />
                      ) : (
                        <FiX className="text-red-500 mr-2" />
                      )}
                      Bucket Exists
                    </div>
                    <div className="flex items-center">
                      {testResults.details?.canUpload ? (
                        <FiCheck className="text-green-500 mr-2" />
                      ) : (
                        <FiX className="text-red-500 mr-2" />
                      )}
                      Can Upload
                    </div>
                    <div className="flex items-center">
                      {testResults.details?.canDownload ? (
                        <FiCheck className="text-green-500 mr-2" />
                      ) : (
                        <FiX className="text-red-500 mr-2" />
                      )}
                      Can Download
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardBody>
        </Card>

        {/* PDF Generation Test Section */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-xl font-semibold">PDF Generation Test</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">
                  Sample Registration Data
                </h3>
                <div className="bg-gray-100 p-4 rounded-lg">
                  <pre className="text-sm">
                    {JSON.stringify(sampleRegistration, null, 2)}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Generate PDFs</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-md font-medium mb-2">
                      With Storage (Supabase)
                    </h4>
                    <div className="flex gap-4">
                      <PDFDownloadButton
                        registrationId={sampleRegistration.id}
                        registrationType={sampleRegistration.type}
                        documentType="receipt"
                        paymentMethod={sampleRegistration.paymentMethod}
                        transactionId={sampleRegistration.transactionId}
                        variant="default"
                        testMode={true}
                        onSuccess={url =>
                          console.log('Receipt generated:', url)
                        }
                        onError={error =>
                          console.error('Receipt error:', error)
                        }
                      />
                      <PDFDownloadButton
                        registrationId={sampleRegistration.id}
                        registrationType={sampleRegistration.type}
                        documentType="invoice"
                        variant="outline"
                        testMode={true}
                        onSuccess={url =>
                          console.log('Invoice generated:', url)
                        }
                        onError={error =>
                          console.error('Invoice error:', error)
                        }
                      />
                    </div>
                  </div>

                  <div>
                    <h4 className="text-md font-medium mb-2">
                      Direct Download (No Storage)
                    </h4>
                    <div className="flex gap-4">
                      <Button
                        variant="secondary"
                        onClick={() => handleDirectDownload('receipt')}
                      >
                        Download Receipt PDF
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={() => handleDirectDownload('invoice')}
                      >
                        Download Invoice PDF
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-md font-medium mb-2">
                      Simple PDF Test
                    </h4>
                    <div className="flex gap-4">
                      <Button variant="ghost" onClick={handleSimplePDFTest}>
                        Test Simple PDF
                      </Button>
                      <Button
                        variant="ghost"
                        onClick={handleWorkingReceiptTest}
                      >
                        Test Working Receipt
                      </Button>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      Tests basic React-PDF functionality and professional
                      receipt template
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">
                  PDF Status Component
                </h3>
                <PDFStatus
                  registrationId={sampleRegistration.id}
                  registrationType={sampleRegistration.type}
                  showGenerateButtons={true}
                  paymentMethod={sampleRegistration.paymentMethod}
                  transactionId={sampleRegistration.transactionId}
                />
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Instructions</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4 text-sm">
              <div>
                <h3 className="font-medium">1. Initialize Storage</h3>
                <p className="text-gray-600">
                  Click &quot;Initialize Storage&quot; to create the PDF storage
                  bucket and set up policies.
                </p>
              </div>
              <div>
                <h3 className="font-medium">2. Test Storage</h3>
                <p className="text-gray-600">
                  Click &quot;Test Storage&quot; to verify that the storage
                  setup is working correctly.
                </p>
              </div>
              <div>
                <h3 className="font-medium">3. Generate PDFs</h3>
                <p className="text-gray-600">
                  Use the PDF generation buttons to create sample receipts and
                  invoices.
                </p>
              </div>
              <div>
                <h3 className="font-medium">Note</h3>
                <p className="text-gray-600">
                  This is a test page using sample data. In production, this
                  functionality would be integrated into the actual registration
                  forms and user dashboards.
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
