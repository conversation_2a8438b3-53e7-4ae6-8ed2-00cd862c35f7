'use client';

import React from 'react';
import { Card, CardBody, CardHeader } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatCurrency, formatDate } from '@/lib/pdf-generation/utils';
import {
  FiMic,
  FiUser,
  FiBriefcase,
  FiFile,
  FiDownload,
  FiEdit,
  FiEye,
  FiCalendar,
  FiCreditCard,
} from 'react-icons/fi';
import { PDFDownloadButton } from '@/components/pdf/PDFDownloadButton';
import type { UserRegistrationDetails } from '@/types/userRegistrations';

interface SpeakerProfileCardProps {
  speaker: UserRegistrationDetails;
  showActions?: boolean;
  onEdit?: () => void;
  onViewDetails?: () => void;
}

export function SpeakerProfileCard({
  speaker,
  showActions = true,
  onEdit,
  onViewDetails,
}: SpeakerProfileCardProps) {
  const getStatusColor = (status: string): "default" | "success" | "info" | "destructive" | "outline" | "secondary" => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'outline';
      case 'cancelled':
        return 'destructive';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPaymentStatusColor = (status: string): "default" | "success" | "info" | "destructive" | "outline" | "secondary" => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'outline';
      case 'failed':
        return 'destructive';
      case 'refunded':
        return 'secondary';
      default:
        return 'default';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full">
          <div>
            <h3 className="iepa-heading-3 mb-1 flex items-center gap-2">
              <FiMic className="w-5 h-5" />
              {speaker.speakerInfo?.presentationTitle || 'Speaker Registration'}
            </h3>
            <p className="iepa-body-small text-gray-600 mb-1">
              {`${speaker.personalInfo.firstName} ${speaker.personalInfo.lastName}`.trim()} • {speaker.personalInfo.organization}
            </p>
            <p className="iepa-body-small text-gray-500">
              Registration ID: {speaker.id.slice(0, 8)}...
            </p>
          </div>
          <div className="flex gap-2 mt-2 md:mt-0">
            <Badge variant={getStatusColor(speaker.status)}>
              {speaker.status.charAt(0).toUpperCase() + speaker.status.slice(1)}
            </Badge>
            <Badge variant={getPaymentStatusColor(speaker.paymentStatus)}>
              {speaker.paymentStatus.charAt(0).toUpperCase() +
                speaker.paymentStatus.slice(1)}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Personal Information */}
          <div>
            <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
              <FiUser className="w-4 h-4" />
              Speaker Information
            </h4>
            <div className="space-y-1 iepa-body-small">
              <p>
                <strong>Name:</strong> {`${speaker.personalInfo.firstName} ${speaker.personalInfo.lastName}`.trim()}
              </p>
              <p>
                <strong>Email:</strong> {speaker.personalInfo.email}
              </p>
              {speaker.personalInfo.phone && (
                <p>
                  <strong>Phone:</strong> {speaker.personalInfo.phone}
                </p>
              )}
              {speaker.personalInfo.organization && (
                <p>
                  <strong>Organization:</strong> {speaker.personalInfo.organization}
                </p>
              )}
              {speaker.personalInfo.title && (
                <p>
                  <strong>Title:</strong> {speaker.personalInfo.title}
                </p>
              )}
            </div>
          </div>

          {/* Presentation Information */}
          <div>
            <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
              <FiMic className="w-4 h-4" />
              Presentation
            </h4>
            <div className="space-y-1 iepa-body-small">
              {speaker.speakerInfo?.presentationTitle && (
                <p>
                  <strong>Title:</strong> {speaker.speakerInfo.presentationTitle}
                </p>
              )}
              {speaker.speakerInfo?.presentationDescription && (
                <p>
                  <strong>Description:</strong>{' '}
                  {speaker.speakerInfo.presentationDescription.length > 100
                    ? `${speaker.speakerInfo.presentationDescription.slice(0, 100)}...`
                    : speaker.speakerInfo.presentationDescription}
                </p>
              )}
              {speaker.speakerInfo?.presentationFileUrl && (
                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded">
                  <a
                    href={speaker.speakerInfo.presentationFileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-[var(--iepa-primary-blue)] hover:underline font-medium text-sm"
                  >
                    <FiFile className="w-4 h-4" />
                    <span>Download Presentation</span>
                    <FiDownload className="w-3 h-3" />
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Registration Details */}
          <div>
            <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
              <FiCalendar className="w-4 h-4" />
              Registration Details
            </h4>
            <div className="space-y-1 iepa-body-small">
              <p>
                <strong>Status:</strong> {speaker.status}
              </p>
              <p>
                <strong>Payment:</strong> {speaker.paymentStatus}
              </p>
              <p>
                <strong>Submitted:</strong>{' '}
                {formatDate(new Date(speaker.createdAt))}
              </p>
              <p>
                <strong>Amount:</strong> {formatCurrency(speaker.financial.grandTotal)}
              </p>
            </div>
          </div>
        </div>

        {/* Bio Section */}
        {speaker.speakerInfo?.bio && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
              <FiBriefcase className="w-4 h-4" />
              Speaker Bio
            </h4>
            <p className="iepa-body-small text-gray-700 whitespace-pre-wrap">
              {speaker.speakerInfo.bio.length > 300
                ? `${speaker.speakerInfo.bio.slice(0, 300)}...`
                : speaker.speakerInfo.bio}
            </p>
          </div>
        )}

        {/* Event Participation */}
        {speaker.eventParticipation?.attendingGolf && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
              ⛳ Event Participation
            </h4>
            <div className="space-y-1 iepa-body-small">
              <p>
                <strong>Golf Tournament:</strong>{' '}
                <span className="text-green-600">Participating</span>
              </p>
              {speaker.eventParticipation.golfClubRental && (
                <p>
                  <strong>Club Rental:</strong>{' '}
                  <span className="text-green-600">Requested</span>
                </p>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex flex-col sm:flex-row gap-3 mt-6">
            {onViewDetails && (
              <Button
                size="sm"
                variant="outline"
                onClick={onViewDetails}
                className="flex items-center gap-2"
              >
                <FiEye className="w-4 h-4" />
                View Details
              </Button>
            )}
            {onEdit && (
              <Button
                size="sm"
                variant="outline"
                onClick={onEdit}
                className="flex items-center gap-2"
              >
                <FiEdit className="w-4 h-4" />
                Edit Profile
              </Button>
            )}
            <PDFDownloadButton
              registrationId={speaker.id}
              registrationType="speaker"
              documentType="receipt"
              size="sm"
              variant="outline"
            />
          </div>
        )}
      </CardBody>
    </Card>
  );
}
