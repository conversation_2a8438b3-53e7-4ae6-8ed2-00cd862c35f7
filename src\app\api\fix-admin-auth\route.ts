// API to fix admin authentication by creating proper auth users
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function POST() {
  try {
    const supabaseAdmin = createSupabaseAdmin();
    
    console.log('🔧 Starting admin user authentication fix...');
    
    // Create the admin user using proper Supabase Auth API
    const adminEmail = '<EMAIL>';
    const adminPassword = 'AdminPass123!'; // Strong password for admin
    
    console.log(`📧 Creating auth user for: ${adminEmail}`);
    
    // Create the user using admin client
    const { data: authUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email: adminEmail,
      password: adminPassword,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        role: 'super_admin',
        created_by: 'system',
        created_at: new Date().toISOString()
      }
    });

    if (createError) {
      console.error('❌ Error creating auth user:', createError);

      // If user already exists, try to get the existing user
      if (createError.message.includes('already registered')) {
        console.log('👤 User already exists, trying to find and link...');

        // Try to get user by email using admin client
        try {
          const { data: existingUsers, error: listError } = await supabaseAdmin.auth.admin.listUsers();

          if (listError) {
            console.error('❌ Error listing users:', listError);
            return NextResponse.json({
              success: false,
              error: 'Failed to list existing users',
              details: listError.message
            });
          }

          console.log(`🔍 Searching for ${adminEmail} in ${existingUsers.users.length} users...`);
          const existingUser = existingUsers.users.find(u => u.email === adminEmail);

          if (existingUser) {
            console.log('✅ Found existing user:', existingUser.id);

            // Update the admin_users table to link to this user
            const { error: updateError } = await supabaseAdmin
              .from('iepa_admin_users')
              .update({
                user_id: existingUser.id,
                updated_at: new Date().toISOString()
              })
              .eq('email', adminEmail);

            if (updateError) {
              console.error('❌ Error updating admin user link:', updateError);
              return NextResponse.json({
                success: false,
                error: 'Found user but failed to link to admin record',
                details: updateError.message
              });
            } else {
              console.log('✅ Successfully linked existing user to admin record');
            }

            return NextResponse.json({
              success: true,
              message: 'Admin user already exists and has been linked',
              user: {
                id: existingUser.id,
                email: existingUser.email,
                created_at: existingUser.created_at
              },
              credentials: {
                email: adminEmail,
                note: 'User already exists. Try logging in with your existing password.'
              }
            });
          } else {
            console.log('❌ User not found in list despite "already registered" error');
            return NextResponse.json({
              success: false,
              error: 'User exists but could not be found',
              details: 'User is registered but not visible in user list'
            });
          }
        } catch (searchError) {
          console.error('❌ Error searching for existing user:', searchError);
          return NextResponse.json({
            success: false,
            error: 'Error searching for existing user',
            details: searchError instanceof Error ? searchError.message : 'Unknown error'
          });
        }
      }

      return NextResponse.json({
        success: false,
        error: 'Failed to create admin user',
        details: createError.message
      });
    }

    console.log('✅ Auth user created successfully:', authUser.user.id);

    // Update the admin_users table to link the new auth user
    const { error: updateError } = await supabaseAdmin
      .from('iepa_admin_users')
      .update({ 
        user_id: authUser.user.id,
        updated_at: new Date().toISOString()
      })
      .eq('email', adminEmail);

    if (updateError) {
      console.error('❌ Error updating admin user link:', updateError);
      return NextResponse.json({
        success: false,
        error: 'Created auth user but failed to link to admin record',
        details: updateError.message
      });
    }

    console.log('✅ Successfully linked auth user to admin record');

    // Test the authentication
    console.log('🧪 Testing authentication...');
    const { data: testAuth, error: testError } = await supabaseAdmin.auth.admin.getUserById(authUser.user.id);
    
    if (testError) {
      console.error('❌ Auth test failed:', testError);
    } else {
      console.log('✅ Auth test successful');
    }

    return NextResponse.json({
      success: true,
      message: 'Admin user created and linked successfully',
      user: {
        id: authUser.user.id,
        email: authUser.user.email,
        created_at: authUser.user.created_at
      },
      credentials: {
        email: adminEmail,
        password: adminPassword,
        note: 'Use these credentials to log in to the admin dashboard'
      }
    });

  } catch (error) {
    console.error('💥 Unexpected error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
