import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const role = searchParams.get('role') || 'all';
    const status = searchParams.get('status') || 'all';

    const supabaseAdmin = createSupabaseAdmin();

    // Build query
    let query = supabaseAdmin
      .from('iepa_admin_users')
      .select('*')
      .order('last_name', { ascending: true, nullsFirst: false })
      .order('first_name', { ascending: true, nullsFirst: false });

    // Apply filters
    if (search) {
      query = query.or(
        `email.ilike.%${search}%,first_name.ilike.%${search}%,last_name.ilike.%${search}%`
      );
    }

    if (role && role !== 'all') {
      query = query.eq('role', role);
    }

    if (status && status !== 'all') {
      const isActive = status === 'active';
      query = query.eq('is_active', isActive);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching admin users:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch admin users' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      users: data || [],
      count: data?.length || 0,
    });
  } catch (err) {
    console.error('Error in admin users API:', err);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, updates } = body;

    if (!userId || !updates) {
      return NextResponse.json(
        { success: false, error: 'User ID and updates are required' },
        { status: 400 }
      );
    }

    const supabaseAdmin = createSupabaseAdmin();

    const { data, error } = await supabaseAdmin
      .from('iepa_admin_users')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating admin user:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update admin user' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      user: data,
    });
  } catch (err) {
    console.error('Error in admin users update API:', err);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    const supabaseAdmin = createSupabaseAdmin();

    const { error } = await supabaseAdmin
      .from('iepa_admin_users')
      .delete()
      .eq('id', userId);

    if (error) {
      console.error('Error deleting admin user:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete admin user' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Admin user deleted successfully',
    });
  } catch (err) {
    console.error('Error in admin users delete API:', err);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
