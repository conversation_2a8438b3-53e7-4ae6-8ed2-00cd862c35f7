'use client';

import React, { useState } from 'react';
import {
  Card,
  CardHeader,
  CardBody,
  Input,
  Textarea,
  Button,
} from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  FiSend,
  FiUsers,
  FiMail,
  FiType,
  FiMessageSquare,
  FiCheck,
  FiAlertCircle,
} from 'react-icons/fi';
// import { useAdminSubmitButton } from '@/hooks/useSubmitButton';

interface SendEmailFormProps {
  onSendEmail: (emailData: EmailFormData) => Promise<void>;
  loading?: boolean;
  className?: string;
}

interface EmailFormData {
  recipients: string;
  subject: string;
  message: string;
  emailType: string;
}

const RECIPIENT_OPTIONS = [
  {
    value: 'all_attendees',
    label: 'All Attendees',
    description: 'Send to all registered attendees',
  },
  {
    value: 'paid_attendees',
    label: 'Paid Attendees Only',
    description: 'Send to attendees with completed payments',
  },
  {
    value: 'speakers',
    label: 'All Speakers',
    description: 'Send to all registered speakers',
  },
  {
    value: 'sponsors',
    label: 'All Sponsors',
    description: 'Send to all sponsor contacts',
  },
  {
    value: 'unpaid_attendees',
    label: 'Unpaid Attendees',
    description: 'Send to attendees with pending payments',
  },
  {
    value: 'failed_payments',
    label: 'Failed Payments',
    description: 'Send to attendees with failed payment attempts',
  },
  {
    value: 'custom',
    label: 'Custom Recipients',
    description: 'Enter specific email addresses',
  },
];

const EMAIL_TYPE_OPTIONS = [
  { value: 'announcement', label: 'Announcement' },
  { value: 'reminder', label: 'Reminder' },
  { value: 'update', label: 'Update' },
  { value: 'notification', label: 'Notification' },
  { value: 'custom', label: 'Custom' },
];

export default function SendEmailForm({
  onSendEmail,
  className = '',
}: SendEmailFormProps) {
  const [formData, setFormData] = useState<EmailFormData>({
    recipients: '',
    subject: '',
    message: '',
    emailType: 'announcement',
  });

  const [customEmails, setCustomEmails] = useState('');
  const [showPreview, setShowPreview] = useState(false);

  // Submit button state management
  // const { isSubmitting, handleSubmit: handleSubmitButton } = useAdminSubmitButton();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.recipients || !formData.subject || !formData.message) {
      return;
    }

    setIsSubmitting(true);
    try {
      const emailData = {
        ...formData,
        recipients:
          formData.recipients === 'custom' ? customEmails : formData.recipients,
      };

      await onSendEmail(emailData);
      // Reset form on success
      setFormData({
        recipients: '',
        subject: '',
        message: '',
        emailType: 'announcement',
      });
      setCustomEmails('');
      setShowPreview(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedRecipient = RECIPIENT_OPTIONS.find(
    opt => opt.value === formData.recipients
  );
  const isFormValid =
    formData.recipients && formData.subject && formData.message;

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <FiSend className="w-5 h-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">Send Email</h2>
        </div>
        <p className="text-sm text-gray-600">
          Send emails to attendees, speakers, or sponsors
        </p>
      </CardHeader>

      <CardBody className="pt-0">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Recipients */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              <FiUsers className="w-4 h-4 inline mr-2" />
              Recipients *
            </label>
            <Select
              value={formData.recipients}
              onValueChange={value =>
                setFormData(prev => ({ ...prev, recipients: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select recipients" />
              </SelectTrigger>
              <SelectContent>
                {RECIPIENT_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-gray-500">
                        {option.description}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {selectedRecipient && (
              <div className="text-xs text-gray-600 bg-blue-50 p-2 rounded border border-blue-200">
                <FiCheck className="w-3 h-3 inline mr-1 text-blue-600" />
                {selectedRecipient.description}
              </div>
            )}
          </div>

          {/* Custom Email Addresses */}
          {formData.recipients === 'custom' && (
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                <FiMail className="w-4 h-4 inline mr-2" />
                Email Addresses
              </label>
              <Textarea
                value={customEmails}
                onChange={e => setCustomEmails(e.target.value)}
                placeholder="Enter email addresses separated by commas or new lines..."
                rows={3}
                className="text-sm"
              />
              <p className="text-xs text-gray-500">
                Separate multiple email addresses with commas or new lines
              </p>
            </div>
          )}

          {/* Email Type */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              <FiType className="w-4 h-4 inline mr-2" />
              Email Type
            </label>
            <Select
              value={formData.emailType}
              onValueChange={value =>
                setFormData(prev => ({ ...prev, emailType: value }))
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {EMAIL_TYPE_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Subject */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              <FiMail className="w-4 h-4 inline mr-2" />
              Subject *
            </label>
            <Input
              type="text"
              value={formData.subject}
              onChange={e =>
                setFormData(prev => ({ ...prev, subject: e.target.value }))
              }
              placeholder="Enter email subject..."
              required
            />
          </div>

          {/* Message */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              <FiMessageSquare className="w-4 h-4 inline mr-2" />
              Message *
            </label>
            <Textarea
              value={formData.message}
              onChange={e =>
                setFormData(prev => ({ ...prev, message: e.target.value }))
              }
              placeholder="Enter your message..."
              rows={6}
              required
            />
            <p className="text-xs text-gray-500">
              HTML formatting is supported. Use standard HTML tags for styling.
            </p>
          </div>

          {/* Preview Toggle */}
          {formData.message && (
            <div className="space-y-2">
              <button
                type="button"
                onClick={() => setShowPreview(!showPreview)}
                className="text-sm text-blue-600 hover:text-blue-800 underline"
              >
                {showPreview ? 'Hide Preview' : 'Show Preview'}
              </button>

              {showPreview && (
                <div className="border border-gray-200 rounded p-3 bg-gray-50">
                  <div className="text-xs text-gray-500 mb-2">Preview:</div>
                  <div
                    className="text-sm prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: formData.message }}
                  />
                </div>
              )}
            </div>
          )}

          {/* Warning for large recipient groups */}
          {formData.recipients &&
            [
              'all_attendees',
              'paid_attendees',
              'speakers',
              'sponsors',
            ].includes(formData.recipients) && (
              <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <FiAlertCircle className="w-4 h-4 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <div className="font-medium">Bulk Email Warning</div>
                  <div>
                    This will send emails to multiple recipients. Please review
                    your message carefully before sending.
                  </div>
                </div>
              </div>
            )}

          {/* Submit Button */}
          <div className="pt-4 border-t border-gray-100">
            <Button
              type="submit"
              disabled={!isFormValid || isSubmitting}
              className="w-full"
              size="lg"
              data-testid="send-email-button"
            >
              <FiSend className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Sending Email...' : 'Send Email'}
            </Button>
          </div>
        </form>
      </CardBody>
    </Card>
  );
}
