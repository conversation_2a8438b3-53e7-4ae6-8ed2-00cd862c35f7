'use client';

import { Button, RegistrationTypeSelector } from '@/components/ui';
import { ProtectedRegistrationPage } from '@/components/auth/ProtectedRoute';
import Link from 'next/link';
import { CONFERENCE_DATES, CONFERENCE_YEAR } from '@/lib/conference-config';
export default function RegisterPage() {
  return (
    <ProtectedRegistrationPage>
      <div className="iepa-container">
        {/* Header Section */}
        <section className="iepa-section text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="iepa-heading-1 mb-4">
              IEPA {CONFERENCE_YEAR} Registration
            </h1>
            <p className="iepa-body-large mb-2">
              {CONFERENCE_DATES.startDate.displayDate} -{' '}
              {CONFERENCE_DATES.endDate.displayDate}
            </p>
            <p className="iepa-body mb-8 max-w-2xl mx-auto">
              Choose your registration type below to get started with the IEPA{' '}
              {CONFERENCE_YEAR} Annual Conference.
            </p>
          </div>
        </section>

        {/* Registration Options */}
        <RegistrationTypeSelector showHeader={false} showAuthButtons={false} />

        {/* Additional Information */}
        <section
          className="iepa-section text-center"
          style={{ backgroundColor: 'var(--iepa-gray-50)' }}
        >
          <div className="max-w-4xl mx-auto">
            <h2 className="iepa-heading-2 mb-4">Need Help?</h2>
            <p className="iepa-body mb-6">
              If you have questions about registration or need assistance,
              please contact us.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button as={Link} href="/contact" variant="bordered">
                Contact Support
              </Button>
              <Button as={Link} href="/about" variant="light">
                Conference Information
              </Button>
            </div>
          </div>
        </section>
      </div>
    </ProtectedRegistrationPage>
  );
}
