#!/usr/bin/env node

/**
 * Create Speaker Test User Script
 * Creates a speaker test user through the proper Supabase auth API
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  console.error('NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.error('SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

// Create admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createSpeakerTestUser() {
  console.log('🚀 Creating speaker test user through Supabase Auth API...');
  
  const email = '<EMAIL>';
  const password = 'TestPass123!';
  
  try {
    // Create new user through proper auth API
    console.log('👤 Creating new speaker test user...');
    const { data, error } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true,
      user_metadata: {
        full_name: 'Speaker Test User',
        first_name: 'Speaker',
        last_name: 'Tester'
      }
    });
    
    if (error) {
      console.error('❌ Error creating user:', error);
      return;
    }
    
    console.log('✅ User created successfully!');
    console.log('📧 Email:', data.user.email);
    console.log('🆔 ID:', data.user.id);
    console.log('✉️ Email Confirmed:', !!data.user.email_confirmed_at);
    
    // Test the login
    console.log('\n🔐 Testing login...');
    
    // Create a regular client for testing login
    const testClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    
    const { data: loginData, error: loginError } = await testClient.auth.signInWithPassword({
      email: email,
      password: password
    });
    
    if (loginError) {
      console.error('❌ Login test failed:', loginError);
    } else {
      console.log('✅ Login test successful!');
      console.log('👤 User ID:', loginData.user.id);
      console.log('📧 Email:', loginData.user.email);
      
      // Sign out
      await testClient.auth.signOut();
    }
    
    // Create a sample speaker registration for this user
    console.log('\n📝 Creating sample speaker registration...');
    
    const { data: speakerData, error: speakerError } = await supabase
      .from('iepa_speaker_registrations')
      .insert({
        user_id: data.user.id,
        email: email,
        full_name: 'Speaker Test User',
        first_name: 'Speaker',
        last_name: 'Tester',
        organization_name: 'Test Organization',
        job_title: 'Senior Test Engineer',
        bio: 'This is a test speaker bio for testing the speaker profile functionality. The speaker has extensive experience in testing and quality assurance.',
        presentation_title: 'Testing Best Practices for Energy Applications',
        presentation_description: 'A comprehensive overview of testing methodologies and best practices specifically tailored for energy sector applications.',
        presentation_duration: '45 minutes',
        target_audience: 'Engineers and QA professionals',
        learning_objectives: 'Participants will learn modern testing approaches and tools for energy applications.',
        speaker_experience: 'Experienced',
        previous_speaking: 'Multiple conferences and workshops',
        equipment_needs: 'Projector, microphone, laptop connection',
        special_requests: 'None',
        dietary_needs: 'Vegetarian',
        attending_golf: false,
        golf_club_rental: false,
        meal_selections: {
          'breakfast_day1': true,
          'lunch_day1': true,
          'dinner_day1': true
        },
        registration_fee: 0,
        payment_status: 'completed',
        status: 'confirmed'
      })
      .select()
      .single();
    
    if (speakerError) {
      console.error('❌ Error creating speaker registration:', speakerError);
    } else {
      console.log('✅ Speaker registration created successfully!');
      console.log('🎤 Registration ID:', speakerData.id);
    }
    
  } catch (error) {
    console.error('💥 Script error:', error);
  }
}

// Run the script
createSpeakerTestUser().then(() => {
  console.log('\n🎯 Speaker test user creation complete!');
  console.log('📝 Credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: TestPass123!');
  console.log('\n🔗 Test URLs:');
  console.log('   Login: http://localhost:6969/auth/login');
  console.log('   Speaker Profile: http://localhost:6969/my-speaker-profile');
  console.log('   Admin View: http://localhost:6969/admin/speakers?testAdmin=true');
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
