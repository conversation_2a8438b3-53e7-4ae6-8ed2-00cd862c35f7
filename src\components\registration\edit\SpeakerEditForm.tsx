'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Input,
  Textarea,
  Label,
  FileUpload,
} from '@/components/ui';
import { PhoneInput } from '@/components/ui/phone-input';
import type { SpeakerRegistration } from '@/types/database';
import { FiSave, FiUser, FiMic, FiFileText } from 'react-icons/fi';

interface SpeakerEditFormProps {
  registration: SpeakerRegistration;
  onSave: (data: Partial<SpeakerRegistration>) => Promise<void>;
  saving: boolean;
  error: string | null;
}

export function SpeakerEditForm({
  registration,
  onSave,
  saving,
  error,
}: SpeakerEditFormProps) {
  const [formData, setFormData] = useState({
    first_name: registration.first_name || '',
    last_name: registration.last_name || '',
    email: registration.email || '',
    phone_number: registration.phone_number || '',
    organization_name: registration.organization_name || '',
    job_title: registration.job_title || '',
    bio: registration.bio || '',
    presentation_file_url: registration.presentation_file_url || '',
    headshot_url: registration.headshot_url || '',
  });

  const handleInputChange = (field: string, value: string) => {
    console.log(`[SPEAKER-EDIT] Updating field ${field} to:`, value);
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value,
      };
      console.log('[SPEAKER-EDIT] New form data:', newData);
      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody>
            <p className="text-red-600 text-sm">{error}</p>
          </CardBody>
        </Card>
      )}

      {/* Personal Information */}
      <Card>
        <CardHeader>
          <h2 className="iepa-heading-2 flex items-center gap-2">
            <FiUser className="w-5 h-5" />
            Personal Information
          </h2>
        </CardHeader>
        <CardBody>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="first_name">First Name *</Label>
              <Input
                id="first_name"
                value={formData.first_name}
                onChange={e => handleInputChange('first_name', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="last_name">Last Name *</Label>
              <Input
                id="last_name"
                value={formData.last_name}
                onChange={e => handleInputChange('last_name', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={e => handleInputChange('email', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="phone_number">Phone Number</Label>
              <PhoneInput
                value={formData.phone_number}
                onChange={value => handleInputChange('phone_number', value)}
              />
            </div>
            <div>
              <Label htmlFor="organization_name">Organization</Label>
              <Input
                id="organization_name"
                value={formData.organization_name}
                onChange={e =>
                  handleInputChange('organization_name', e.target.value)
                }
              />
            </div>
            <div>
              <Label htmlFor="job_title">Job Title</Label>
              <Input
                id="job_title"
                value={formData.job_title}
                onChange={e => handleInputChange('job_title', e.target.value)}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Speaker Information */}
      <Card>
        <CardHeader>
          <h2 className="iepa-heading-2 flex items-center gap-2">
            <FiMic className="w-5 h-5" />
            Speaker Information
          </h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <div>
              <Label htmlFor="bio">Speaker Bio *</Label>
              <Textarea
                id="bio"
                value={formData.bio}
                onChange={e => handleInputChange('bio', e.target.value)}
                placeholder="Please provide a brief bio for the conference program..."
                rows={4}
                required
              />
              <p className="text-sm text-gray-600 mt-1">
                This will be used in the conference program and marketing
                materials.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Files */}
      <Card>
        <CardHeader>
          <h2 className="iepa-heading-2 flex items-center gap-2">
            <FiFileText className="w-5 h-5" />
            Files
          </h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-6">
            <div>
              <FileUpload
                label="Presentation File"
                description="Upload your presentation (PDF, PPT, PPTX, DOC, DOCX - max 50MB)"
                bucket="iepa-presentations"
                folder="speaker-presentations"
                maxSize={52428800} // 50MB
                allowedTypes={[
                  'application/pdf',
                  'application/vnd.ms-powerpoint',
                  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                  'application/msword',
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                ]}
                accept=".pdf,.ppt,.pptx,.doc,.docx"
                onFileUpload={url => {
                  if (url) {
                    handleInputChange('presentation_file_url', url);
                  }
                }}
                placeholder="Upload your presentation file"
                existingFileUrl={formData.presentation_file_url}
                existingFileName={formData.presentation_file_url ?
                  formData.presentation_file_url.split('/').pop() : undefined}
                allowDownload={true}
                allowView={true}
                onRemoveExisting={() => handleInputChange('presentation_file_url', '')}
              />
            </div>

            <div>
              <FileUpload
                label="Professional Headshot"
                description="Upload your professional headshot (JPG, PNG, WebP - max 5MB)"
                bucket="iepa-presentations"
                folder="speaker-headshots"
                maxSize={5242880} // 5MB
                allowedTypes={['image/jpeg', 'image/png', 'image/webp']}
                accept=".jpg,.jpeg,.png,.webp"
                onFileUpload={url => {
                  if (url) {
                    handleInputChange('headshot_url', url);
                  }
                }}
                placeholder="Upload your professional headshot"
                existingFileUrl={formData.headshot_url}
                existingFileName={formData.headshot_url ?
                  formData.headshot_url.split('/').pop() : undefined}
                showPreview={true}
                allowDownload={true}
                allowView={true}
                onRemoveExisting={() => handleInputChange('headshot_url', '')}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={saving}
          className="flex items-center gap-2"
        >
          <FiSave className="w-4 h-4" />
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </form>
  );
}
