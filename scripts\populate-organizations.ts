import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface Organization {
  name: string;
  normalized_name: string;
  usage_count: number;
  last_used_at: string;
}

async function populateOrganizations() {
  console.log('[ORG-POPULATE] Starting organization population...');

  try {
    // Get unique organizations from all sources
    const organizations = new Map<string, Organization>();

    // 1. Get organizations from iepa_attendee_registrations
    console.log('[ORG-POPULATE] Fetching from attendee registrations...');
    const { data: attendeeOrgs, error: attendeeError } = await supabase
      .from('iepa_attendee_registrations')
      .select('organization, created_at')
      .not('organization', 'is', null)
      .neq('organization', '');

    if (attendeeError) {
      console.error('[ORG-POPULATE] Error fetching attendee organizations:', attendeeError);
      throw attendeeError;
    }

    // Process attendee organizations
    attendeeOrgs?.forEach(record => {
      if (record.organization && record.organization.trim()) {
        const orgName = record.organization.trim();
        const normalizedName = orgName.toLowerCase();
        
        if (!organizations.has(normalizedName)) {
          organizations.set(normalizedName, {
            name: orgName,
            normalized_name: normalizedName,
            usage_count: 1,
            last_used_at: record.created_at
          });
        } else {
          const existing = organizations.get(normalizedName)!;
          existing.usage_count++;
          // Keep the latest date
          if (new Date(record.created_at) > new Date(existing.last_used_at)) {
            existing.last_used_at = record.created_at;
          }
        }
      }
    });

    // 2. Get organizations from iepa_speaker_registrations
    console.log('[ORG-POPULATE] Fetching from speaker registrations...');
    const { data: speakerOrgs, error: speakerError } = await supabase
      .from('iepa_speaker_registrations')
      .select('organization_name, created_at')
      .not('organization_name', 'is', null)
      .neq('organization_name', '');

    if (speakerError) {
      console.error('[ORG-POPULATE] Error fetching speaker organizations:', speakerError);
      throw speakerError;
    }

    // Process speaker organizations
    speakerOrgs?.forEach(record => {
      if (record.organization_name && record.organization_name.trim()) {
        const orgName = record.organization_name.trim();
        const normalizedName = orgName.toLowerCase();
        
        if (!organizations.has(normalizedName)) {
          organizations.set(normalizedName, {
            name: orgName,
            normalized_name: normalizedName,
            usage_count: 1,
            last_used_at: record.created_at
          });
        } else {
          const existing = organizations.get(normalizedName)!;
          existing.usage_count++;
          // Keep the latest date
          if (new Date(record.created_at) > new Date(existing.last_used_at)) {
            existing.last_used_at = record.created_at;
          }
        }
      }
    });

    // 3. Get organizations from iepa_historical_registrations (if any exist)
    console.log('[ORG-POPULATE] Fetching from historical registrations...');
    const { data: historicalOrgs, error: historicalError } = await supabase
      .from('iepa_historical_registrations')
      .select('organization, created_at')
      .not('organization', 'is', null)
      .neq('organization', '');

    if (historicalError && historicalError.code !== 'PGRST116') {
      console.error('[ORG-POPULATE] Error fetching historical organizations:', historicalError);
    } else if (historicalOrgs) {
      // Process historical organizations
      historicalOrgs.forEach(record => {
        if (record.organization && record.organization.trim()) {
          const orgName = record.organization.trim();
          const normalizedName = orgName.toLowerCase();
          
          if (!organizations.has(normalizedName)) {
            organizations.set(normalizedName, {
              name: orgName,
              normalized_name: normalizedName,
              usage_count: 1,
              last_used_at: record.created_at
            });
          } else {
            const existing = organizations.get(normalizedName)!;
            existing.usage_count++;
            // Keep the latest date
            if (new Date(record.created_at) > new Date(existing.last_used_at)) {
              existing.last_used_at = record.created_at;
            }
          }
        }
      });
    }

    // 4. Get organizations from iepa_user_profiles
    console.log('[ORG-POPULATE] Fetching from user profiles...');
    const { data: profileOrgs, error: profileError } = await supabase
      .from('iepa_user_profiles')
      .select('organization, created_at')
      .not('organization', 'is', null)
      .neq('organization', '');

    if (profileError) {
      console.error('[ORG-POPULATE] Error fetching profile organizations:', profileError);
    } else if (profileOrgs) {
      // Process profile organizations
      profileOrgs.forEach(record => {
        if (record.organization && record.organization.trim()) {
          const orgName = record.organization.trim();
          const normalizedName = orgName.toLowerCase();
          
          if (!organizations.has(normalizedName)) {
            organizations.set(normalizedName, {
              name: orgName,
              normalized_name: normalizedName,
              usage_count: 1,
              last_used_at: record.created_at
            });
          } else {
            const existing = organizations.get(normalizedName)!;
            existing.usage_count++;
            // Keep the latest date
            if (new Date(record.created_at) > new Date(existing.last_used_at)) {
              existing.last_used_at = record.created_at;
            }
          }
        }
      });
    }

    console.log(`[ORG-POPULATE] Found ${organizations.size} unique organizations`);

    // Convert to array and sort by usage count
    const orgArray = Array.from(organizations.values()).sort((a, b) => b.usage_count - a.usage_count);

    // Insert organizations in batches
    const batchSize = 100;
    let inserted = 0;
    
    for (let i = 0; i < orgArray.length; i += batchSize) {
      const batch = orgArray.slice(i, i + batchSize);
      
      console.log(`[ORG-POPULATE] Inserting batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(orgArray.length / batchSize)} (${batch.length} organizations)...`);
      
      const { data, error } = await supabase
        .from('iepa_organizations')
        .upsert(batch, { 
          onConflict: 'normalized_name',
          ignoreDuplicates: false 
        })
        .select('id, name');

      if (error) {
        console.error('[ORG-POPULATE] Error inserting batch:', error);
        throw error;
      }

      inserted += data?.length || 0;
    }

    console.log(`[ORG-POPULATE] Successfully populated ${inserted} organizations`);

    // Show top 10 most used organizations
    console.log('\n[ORG-POPULATE] Top 10 most used organizations:');
    orgArray.slice(0, 10).forEach((org, index) => {
      console.log(`${index + 1}. ${org.name} (used ${org.usage_count} times)`);
    });

    return {
      success: true,
      totalFound: organizations.size,
      totalInserted: inserted
    };

  } catch (error) {
    console.error('[ORG-POPULATE] Failed to populate organizations:', error);
    throw error;
  }
}

// Run the script
if (require.main === module) {
  populateOrganizations()
    .then(result => {
      console.log('[ORG-POPULATE] Population completed successfully:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('[ORG-POPULATE] Population failed:', error);
      process.exit(1);
    });
}

export default populateOrganizations; 