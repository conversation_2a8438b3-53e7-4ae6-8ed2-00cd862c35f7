#!/usr/bin/env node

// <PERSON>rip<PERSON> to set a password for the <NAME_EMAIL>
// This allows for easier testing without waiting for magic links

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

// Create admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setTestUserPassword() {
  console.log('🔑 Setting password for <NAME_EMAIL>...');
  
  const email = '<EMAIL>';
  const password = 'TestPass123!'; // Strong test password
  
  try {
    // First, find the user in auth.users
    console.log('🔍 Looking up user in auth.users...');
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('❌ Error fetching auth users:', authError);
      return;
    }
    
    const authUser = authUsers.users.find(user => user.email === email);
    
    if (!authUser) {
      console.error(`❌ User not found in auth.users with email: ${email}`);
      return;
    }
    
    console.log('✅ Found auth user:');
    console.log('📧 Email:', authUser.email);
    console.log('🆔 ID:', authUser.id);
    
    // Update the user's password
    console.log('🔑 Setting new password...');
    const { data: updateData, error: updateError } = await supabase.auth.admin.updateUserById(
      authUser.id,
      {
        password: password,
        email_confirm: true // Ensure email is confirmed
      }
    );
    
    if (updateError) {
      console.error('❌ Error setting password:', updateError);
      return;
    }
    
    console.log('✅ Password set successfully!');
    console.log('📋 Updated user:', {
      id: updateData.user.id,
      email: updateData.user.email,
      emailConfirmed: !!updateData.user.email_confirmed_at
    });
    
    console.log('\n🎯 Test Credentials Ready!');
    console.log('📝 Login Details:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: TestPass123!');
    console.log('\n🔗 Test URLs:');
    console.log('   Login: http://localhost:6969/auth/login');
    console.log('   Direct to Attendee Form: http://localhost:6969/register/attendee');
    console.log('   Direct to Speaker Form: http://localhost:6969/register/speaker');
    console.log('\n📋 Expected Profile Data:');
    console.log('   Name: Test User');
    console.log('   Organization: Noteware Digital');
    console.log('   Job Title: Software Developer');
    console.log('   Phone: (*************');
    console.log('   Address: San Francisco, CA');
    
    console.log('\n🧪 Testing Steps:');
    console.log('1. Go to http://localhost:6969/auth/login');
    console.log('2. Click "or use password"');
    console.log('3. Enter email: <EMAIL>');
    console.log('4. Enter password: TestPass123!');
    console.log('5. Click "Sign In with Password"');
    console.log('6. Navigate to registration forms to test prefilling');
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Run the script
setTestUserPassword().catch(console.error);
