'use client';

// Golf Add-On Form Component
// Form for adding golf tournament and club rental to existing registration

import { useState, useEffect } from 'react';
import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Checkbox,
  Label,
  Alert,
  AlertDescription
} from '@/components/ui';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Loader2, DollarSign, CheckCircle, Target } from 'lucide-react';
import { formatCurrency } from '@/lib/pdf-generation/utils';
import type {
  GolfAddOnFormData,
  GolfAddOnEligibility,
  GolfAddOnPricing,
} from '@/types/golfAddOn';
import { GOLF_ADDON_CONSTANTS } from '@/types/golfAddOn';

interface GolfAddOnFormProps {
  eligibility: GolfAddOnEligibility;
  onSubmit: (formData: GolfAddOnFormData) => Promise<void>;
  loading?: boolean;
  error?: string | null;
}

export function GolfAddOnForm({
  eligibility,
  onSubmit,
  loading = false,
  error = null,
}: GolfAddOnFormProps) {
  const [formData, setFormData] = useState<GolfAddOnFormData>({
    golfTournament: false,
    golfClubRental: false,
    golfClubHandedness: '',
  });

  const [pricing, setPricing] = useState<GolfAddOnPricing>({
    golfTournamentFee: 0,
    golfClubRentalFee: 0,
    total: 0,
  });

  // Calculate pricing when form data changes
  useEffect(() => {
    let golfTournamentFee = 0;
    let golfClubRentalFee = 0;

    // Only charge for golf tournament if user doesn't already have it
    if (formData.golfTournament && eligibility.canAddGolf) {
      golfTournamentFee = GOLF_ADDON_CONSTANTS.GOLF_TOURNAMENT_FEE;
    }

    // Only charge for club rental if user is adding it
    if (formData.golfClubRental && (eligibility.canAddGolf || eligibility.canAddClubRental)) {
      golfClubRentalFee = GOLF_ADDON_CONSTANTS.GOLF_CLUB_RENTAL_FEE;
    }

    const total = golfTournamentFee + golfClubRentalFee;

    setPricing({
      golfTournamentFee,
      golfClubRentalFee,
      total,
    });
  }, [formData, eligibility]);

  const handleInputChange = (field: keyof GolfAddOnFormData, value: boolean | string) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };

      // Reset club rental options if golf tournament is unchecked
      if (field === 'golfTournament' && !value) {
        updated.golfClubRental = false;
        updated.golfClubHandedness = '';
      }

      // Reset handedness if club rental is unchecked
      if (field === 'golfClubRental' && !value) {
        updated.golfClubHandedness = '';
      }

      return updated;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (pricing.total <= 0) {
      return;
    }

    await onSubmit(formData);
  };

  const isFormValid = pricing.total > 0 && (!formData.golfClubRental || formData.golfClubHandedness);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Golf Tournament Option */}
      {eligibility.canAddGolf && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="golf-tournament"
                checked={formData.golfTournament}
                onCheckedChange={(checked) => handleInputChange('golfTournament', checked)}
                disabled={loading}
              />
              <div className="flex-1">
                <Label htmlFor="golf-tournament" className="text-base font-medium cursor-pointer">
                  Golf Tournament (+{formatCurrency(GOLF_ADDON_CONSTANTS.GOLF_TOURNAMENT_FEE)})
                </Label>
                <p className="text-sm text-gray-600 mt-1">
                  Join the golf tournament on September 16, 2025
                </p>
              </div>
              <Target className="h-5 w-5 text-green-600 mt-1" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Golf Club Rental Options */}
      {(formData.golfTournament || eligibility.hasExistingGolf) && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="golf-club-rental"
                  checked={formData.golfClubRental}
                  onCheckedChange={(checked) => handleInputChange('golfClubRental', checked)}
                  disabled={loading}
                />
                <div className="flex-1">
                  <Label htmlFor="golf-club-rental" className="text-base font-medium cursor-pointer">
                    Golf Club Rental (+{formatCurrency(GOLF_ADDON_CONSTANTS.GOLF_CLUB_RENTAL_FEE)})
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">
                    Callaway Rogues, includes 6 Callaway Golf Balls
                  </p>
                </div>
              </div>

              {/* Handedness Selection */}
              {formData.golfClubRental && (
                <div className="ml-8 p-4 bg-gray-50 rounded-lg border border-gray-100">
                  <Label className="text-sm font-medium mb-3 block">
                    Club Handedness *
                  </Label>
                  <RadioGroup
                    value={formData.golfClubHandedness}
                    onValueChange={(value) => handleInputChange('golfClubHandedness', value)}
                    disabled={loading}
                  >
                    {GOLF_ADDON_CONSTANTS.GOLF_CLUB_OPTIONS.map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} id={`handedness-${option.value}`} />
                        <Label htmlFor={`handedness-${option.value}`} className="cursor-pointer">
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pricing Summary */}
      {pricing.total > 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <DollarSign className="h-5 w-5" />
              Golf Add-On Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {pricing.golfTournamentFee > 0 && (
                <div className="flex justify-between">
                  <span>Golf Tournament:</span>
                  <span className="font-medium">{formatCurrency(pricing.golfTournamentFee)}</span>
                </div>
              )}
              {pricing.golfClubRentalFee > 0 && (
                <div className="flex justify-between">
                  <span>Golf Club Rental ({formData.golfClubHandedness} handed):</span>
                  <span className="font-medium">{formatCurrency(pricing.golfClubRentalFee)}</span>
                </div>
              )}
              <div className="border-t pt-2 flex justify-between text-lg font-bold text-blue-900">
                <span>Total Add-On Cost:</span>
                <span>{formatCurrency(pricing.total)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Submit Button */}
      <div className="flex justify-end space-x-3">
        <Button
          type="submit"
          disabled={!isFormValid || loading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <CheckCircle className="mr-2 h-4 w-4" />
              Add Golf to Registration ({formatCurrency(pricing.total)})
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
