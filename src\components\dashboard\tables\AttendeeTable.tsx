'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardBody, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  FiSearch,
  FiDownload,
  FiEye,
  FiEdit,
  FiMail,
  FiPhone,
  FiUser,
  FiCalendar,
} from 'react-icons/fi';
import type { EnhancedAttendeeRegistration } from '@/types/dashboard';

interface AttendeeTableProps {
  attendees: EnhancedAttendeeRegistration[];
  isLoading?: boolean;
  onViewDetails?: (attendee: EnhancedAttendeeRegistration) => void;
  onEditAttendee?: (attendee: EnhancedAttendeeRegistration) => void;
  onExport?: () => void;
}

interface TableFilters {
  search: string;
  registrationType: string;
  paymentStatus: string;
  golfParticipation: string;
}

const AttendeeTable: React.FC<AttendeeTableProps> = ({
  attendees,
  isLoading = false,
  onViewDetails,
  onEditAttendee,
  onExport,
}) => {
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    registrationType: '',
    paymentStatus: '',
    golfParticipation: '',
  });
  const [sortField, setSortField] =
    useState<keyof EnhancedAttendeeRegistration>('last_name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Filter and sort attendees
  const filteredAndSortedAttendees = useMemo(() => {
    const filtered = attendees.filter(attendee => {
      const matchesSearch =
        !filters.search ||
        `${attendee.last_name}, ${attendee.first_name}`
          .toLowerCase()
          .includes(filters.search.toLowerCase()) ||
        attendee.email.toLowerCase().includes(filters.search.toLowerCase()) ||
        attendee.organization
          .toLowerCase()
          .includes(filters.search.toLowerCase());

      const matchesRegistrationType =
        !filters.registrationType ||
        attendee.registration_type === filters.registrationType;

      const matchesPaymentStatus =
        !filters.paymentStatus ||
        attendee.payment_status === filters.paymentStatus;

      const matchesGolfParticipation =
        !filters.golfParticipation ||
        (filters.golfParticipation === 'yes' && attendee.attending_golf) ||
        (filters.golfParticipation === 'no' && !attendee.attending_golf);

      return (
        matchesSearch &&
        matchesRegistrationType &&
        matchesPaymentStatus &&
        matchesGolfParticipation
      );
    });

    // Sort - implement name-based sorting with last name first, then first name
    filtered.sort((a, b) => {
      // Special handling for name-based sorting
      if (sortField === 'last_name' || sortField === 'first_name' || sortField === 'full_name') {
        const aLastName = (a.last_name || '').toLowerCase();
        const bLastName = (b.last_name || '').toLowerCase();
        const aFirstName = (a.first_name || '').toLowerCase();
        const bFirstName = (b.first_name || '').toLowerCase();

        // Primary sort by last name
        const lastNameCompare = aLastName.localeCompare(bLastName);
        if (lastNameCompare !== 0) {
          return sortDirection === 'asc' ? lastNameCompare : -lastNameCompare;
        }

        // Secondary sort by first name
        const firstNameCompare = aFirstName.localeCompare(bFirstName);
        return sortDirection === 'asc' ? firstNameCompare : -firstNameCompare;
      }

      // Default sorting for other fields
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue, undefined, { sensitivity: 'base' })
          : bValue.localeCompare(aValue, undefined, { sensitivity: 'base' });
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    return filtered;
  }, [attendees, filters, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedAttendees.length / pageSize);
  const paginatedAttendees = filteredAndSortedAttendees.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const handleSort = (field: keyof EnhancedAttendeeRegistration) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      confirmed: 'success',
      pending: 'secondary',
      cancelled: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPaymentStatusBadge = (status: string) => {
    const variants = {
      completed: 'success',
      pending: 'secondary',
      failed: 'destructive',
      refunded: 'outline',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded animate-pulse w-1/4"></div>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="h-16 bg-gray-200 rounded animate-pulse"
              ></div>
            ))}
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Attendee Registrations
            </h3>
            <p className="text-sm text-gray-600">
              {filteredAndSortedAttendees.length} of {attendees.length}{' '}
              attendees
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onExport}
              className="flex items-center gap-2"
            >
              <FiDownload className="w-4 h-4" />
              Export
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search attendees..."
              value={filters.search}
              onChange={e =>
                setFilters(prev => ({ ...prev, search: e.target.value }))
              }
              className="pl-10"
            />
          </div>

          <select
            value={filters.registrationType}
            onChange={e =>
              setFilters(prev => ({
                ...prev,
                registrationType: e.target.value,
              }))
            }
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Registration Types</option>
            <option value="iepa-member">IEPA Member</option>
            <option value="non-iepa-member">Non-IEPA Member</option>
            <option value="day-use-iepa">Day Use - IEPA</option>
            <option value="day-use-non-iepa">Day Use - Non-IEPA</option>
            <option value="fed-state-government">Government</option>
            <option value="cca">CCA</option>
          </select>

          <select
            value={filters.paymentStatus}
            onChange={e =>
              setFilters(prev => ({ ...prev, paymentStatus: e.target.value }))
            }
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Payment Status</option>
            <option value="completed">Completed</option>
            <option value="pending">Pending</option>
            <option value="failed">Failed</option>
            <option value="refunded">Refunded</option>
          </select>

          <select
            value={filters.golfParticipation}
            onChange={e =>
              setFilters(prev => ({
                ...prev,
                golfParticipation: e.target.value,
              }))
            }
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Golf Participation</option>
            <option value="yes">Participating</option>
            <option value="no">Not Participating</option>
          </select>
        </div>
      </CardHeader>

      <CardBody>
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th
                  className="text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:bg-gray-50"
                  onClick={() => handleSort('full_name')}
                >
                  <div className="flex items-center gap-2">
                    <FiUser className="w-4 h-4" />
                    Attendee
                    {sortField === 'full_name' && (
                      <span className="text-xs">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">
                  Contact
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">
                  Organization
                </th>
                <th
                  className="text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:bg-gray-50"
                  onClick={() => handleSort('registration_type')}
                >
                  Registration Type
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">
                  Payment
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">
                  Status
                </th>
                <th
                  className="text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:bg-gray-50"
                  onClick={() => handleSort('created_at')}
                >
                  Registered
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {paginatedAttendees.map(attendee => (
                <tr
                  key={attendee.id}
                  className="border-b border-gray-100 hover:bg-gray-50"
                >
                  <td className="py-4 px-4">
                    <div>
                      <p className="font-medium text-gray-900">
                        {`${attendee.last_name}, ${attendee.first_name}`.trim()}
                      </p>
                      <p className="text-sm text-gray-600">
                        {attendee.name_on_badge}
                      </p>
                      {attendee.attending_golf && (
                        <Badge variant="outline" className="mt-1 text-xs">
                          Golf
                        </Badge>
                      )}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <FiMail className="w-3 h-3" />
                        {attendee.email}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <FiPhone className="w-3 h-3" />
                        {attendee.phone_number}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div>
                      <p className="font-medium text-gray-900">
                        {attendee.organization}
                      </p>
                      <p className="text-sm text-gray-600">
                        {attendee.job_title}
                      </p>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <Badge variant="outline" className="text-xs">
                      {attendee.registration_type
                        .replace(/-/g, ' ')
                        .toUpperCase()}
                    </Badge>
                  </td>
                  <td className="py-4 px-4">
                    <div>
                      <p className="font-medium text-gray-900">
                        {formatCurrency(attendee.grand_total)}
                      </p>
                      {getPaymentStatusBadge(
                        attendee.payment_status || 'pending'
                      )}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    {getStatusBadge(attendee.status)}
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <FiCalendar className="w-3 h-3" />
                      {formatDate(attendee.created_at)}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onViewDetails?.(attendee)}
                        className="p-1"
                      >
                        <FiEye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEditAttendee?.(attendee)}
                        className="p-1"
                      >
                        <FiEdit className="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <p className="text-sm text-gray-600">
              Showing {(currentPage - 1) * pageSize + 1} to{' '}
              {Math.min(
                currentPage * pageSize,
                filteredAndSortedAttendees.length
              )}{' '}
              of {filteredAndSortedAttendees.length} results
            </p>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage(prev => Math.min(totalPages, prev + 1))
                }
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default AttendeeTable;
