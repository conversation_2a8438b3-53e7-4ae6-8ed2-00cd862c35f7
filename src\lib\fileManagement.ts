import { supabase } from '@/lib/supabase';

export interface FileUpdateResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface FileDeleteResult {
  success: boolean;
  error?: string;
}

/**
 * Delete a file from Supabase storage
 */
export async function deleteFileFromStorage(
  bucket: string,
  filePath: string
): Promise<FileDeleteResult> {
  try {
    const { error } = await supabase.storage.from(bucket).remove([filePath]);

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Error deleting file:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Extract file path from Supabase URL
 */
export function extractFilePathFromUrl(url: string, bucket: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    const bucketIndex = pathParts.findIndex(part => part === bucket);

    if (bucketIndex === -1 || bucketIndex === pathParts.length - 1) {
      return null;
    }

    return pathParts.slice(bucketIndex + 1).join('/');
  } catch (error) {
    console.error('Error extracting file path from URL:', error);
    return null;
  }
}

/**
 * Generate a signed URL for a file in a private bucket
 */
export async function getSignedFileUrl(
  bucket: string,
  filePath: string,
  expiresIn: number = 3600 // 1 hour default
): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, expiresIn);

    if (error) {
      throw error;
    }

    return {
      success: true,
      url: data.signedUrl,
    };
  } catch (error) {
    console.error('Error creating signed URL:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Convert a public URL to a signed URL for private bucket access
 */
export async function convertToSignedUrl(
  publicUrl: string,
  bucket: string,
  expiresIn: number = 3600
): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    const filePath = extractFilePathFromUrl(publicUrl, bucket);
    if (!filePath) {
      throw new Error('Could not extract file path from URL');
    }

    return await getSignedFileUrl(bucket, filePath, expiresIn);
  } catch (error) {
    console.error('Error converting to signed URL:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Replace an existing file with a new one
 */
export async function replaceFile(
  bucket: string,
  oldFileUrl: string | null,
  newFile: File,
  folder?: string
): Promise<FileUpdateResult> {
  try {
    // Get current user for folder structure
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      throw new Error('User not authenticated. Please log in and try again.');
    }

    // Delete old file if it exists
    if (oldFileUrl) {
      const oldFilePath = extractFilePathFromUrl(oldFileUrl, bucket);
      if (oldFilePath) {
        await deleteFileFromStorage(bucket, oldFilePath);
      }
    }

    // Upload new file
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = newFile.name.split('.').pop();
    const fileName = `${timestamp}-${randomString}.${fileExtension}`;

    // Create file path with user ID as first folder (required by RLS policies)
    const filePath = folder ? `${user.id}/${folder}/${fileName}` : `${user.id}/${fileName}`;

    console.log('Replacing file at path:', filePath);

    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, newFile, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) {
      console.error('Storage upload error:', error);
      throw error;
    }

    // Get signed URL for private bucket (expires in 1 hour)
    const { data: signedUrlData, error: signedUrlError } = await supabase.storage
      .from(bucket)
      .createSignedUrl(data.path, 3600); // 1 hour expiry

    if (signedUrlError) {
      console.error('Error creating signed URL:', signedUrlError);
      throw signedUrlError;
    }

    return {
      success: true,
      url: signedUrlData.signedUrl,
    };
  } catch (error) {
    console.error('Error replacing file:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Update speaker files (presentation and/or headshot)
 */
export async function updateSpeakerFiles(
  speakerId: string,
  files: {
    presentationFile?: File;
    headshot?: File;
  },
  currentUrls: {
    presentationFileUrl?: string | null;
    headshotUrl?: string | null;
  }
): Promise<{
  success: boolean;
  updatedUrls?: {
    presentationFileUrl?: string;
    headshotUrl?: string;
  };
  error?: string;
}> {
  try {
    const updatedUrls: {
      presentationFileUrl?: string;
      headshotUrl?: string;
    } = {};

    // Update presentation file if provided
    if (files.presentationFile) {
      const result = await replaceFile(
        'iepa-presentations',
        currentUrls.presentationFileUrl || null,
        files.presentationFile,
        'speaker-presentations'
      );

      if (!result.success) {
        throw new Error(`Failed to update presentation file: ${result.error}`);
      }

      updatedUrls.presentationFileUrl = result.url;
    }

    // Update headshot if provided
    if (files.headshot) {
      const result = await replaceFile(
        'iepa-presentations',
        currentUrls.headshotUrl || null,
        files.headshot,
        'speaker-headshots'
      );

      if (!result.success) {
        throw new Error(`Failed to update headshot: ${result.error}`);
      }

      updatedUrls.headshotUrl = result.url;
    }

    // Update database record
    const updateData: any = {};
    if (updatedUrls.presentationFileUrl) {
      updateData.presentation_file_url = updatedUrls.presentationFileUrl;
    }
    if (updatedUrls.headshotUrl) {
      updateData.headshot_url = updatedUrls.headshotUrl;
    }

    if (Object.keys(updateData).length > 0) {
      updateData.updated_at = new Date().toISOString();

      const { error: dbError } = await supabase
        .from('iepa_speaker_registrations')
        .update(updateData)
        .eq('id', speakerId);

      if (dbError) {
        throw dbError;
      }
    }

    return {
      success: true,
      updatedUrls,
    };
  } catch (error) {
    console.error('Error updating speaker files:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
