-- Migration: Remove fullName field dependency and make it computed
-- This migration handles the transition from manual fullName to computed fullName

-- Step 1: For iepa_attendee_registrations table
-- First, ensure all records have first_name and last_name populated
-- If any records have full_name but missing first/last names, we'll try to split them

DO $$
DECLARE
    rec RECORD;
    name_parts TEXT[];
BEGIN
    -- Update records where first_name or last_name is missing but full_name exists
    FOR rec IN 
        SELECT id, full_name 
        FROM iepa_attendee_registrations 
        WHERE (first_name IS NULL OR first_name = '' OR last_name IS NULL OR last_name = '')
        AND full_name IS NOT NULL AND full_name != ''
    LOOP
        -- Split full_name into parts
        name_parts := string_to_array(trim(rec.full_name), ' ');
        
        -- Update first_name and last_name based on the split
        IF array_length(name_parts, 1) >= 2 THEN
            UPDATE iepa_attendee_registrations 
            SET 
                first_name = COALESCE(NULLIF(first_name, ''), name_parts[1]),
                last_name = COALESCE(NULLIF(last_name, ''), array_to_string(name_parts[2:], ' '))
            WHERE id = rec.id;
        ELSIF array_length(name_parts, 1) = 1 THEN
            UPDATE iepa_attendee_registrations 
            SET 
                first_name = COALESCE(NULLIF(first_name, ''), name_parts[1]),
                last_name = COALESCE(NULLIF(last_name, ''), '')
            WHERE id = rec.id;
        END IF;
    END LOOP;
END $$;

-- Step 2: Drop the existing full_name column and recreate as computed
ALTER TABLE iepa_attendee_registrations DROP COLUMN IF EXISTS full_name;
ALTER TABLE iepa_attendee_registrations ADD COLUMN full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED;

-- Step 3: For iepa_speaker_registrations table
-- Same process for speakers
DO $$
DECLARE
    rec RECORD;
    name_parts TEXT[];
BEGIN
    -- Update records where first_name or last_name is missing but full_name exists
    FOR rec IN 
        SELECT id, full_name 
        FROM iepa_speaker_registrations 
        WHERE (first_name IS NULL OR first_name = '' OR last_name IS NULL OR last_name = '')
        AND full_name IS NOT NULL AND full_name != ''
    LOOP
        -- Split full_name into parts
        name_parts := string_to_array(trim(rec.full_name), ' ');
        
        -- Update first_name and last_name based on the split
        IF array_length(name_parts, 1) >= 2 THEN
            UPDATE iepa_speaker_registrations 
            SET 
                first_name = COALESCE(NULLIF(first_name, ''), name_parts[1]),
                last_name = COALESCE(NULLIF(last_name, ''), array_to_string(name_parts[2:], ' '))
            WHERE id = rec.id;
        ELSIF array_length(name_parts, 1) = 1 THEN
            UPDATE iepa_speaker_registrations 
            SET 
                first_name = COALESCE(NULLIF(first_name, ''), name_parts[1]),
                last_name = COALESCE(NULLIF(last_name, ''), '')
            WHERE id = rec.id;
        END IF;
    END LOOP;
END $$;

-- Step 4: Drop the existing full_name column and recreate as computed for speakers
ALTER TABLE iepa_speaker_registrations DROP COLUMN IF EXISTS full_name;
ALTER TABLE iepa_speaker_registrations ADD COLUMN full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED;

-- Step 5: Update any views or indexes that might depend on full_name
-- (Add any specific view updates here if needed)

-- Step 6: Verify the migration worked
-- This will show any records that might have issues
SELECT 
    'attendee' as table_name,
    id,
    first_name,
    last_name,
    full_name,
    CASE 
        WHEN first_name IS NULL OR first_name = '' THEN 'Missing first_name'
        WHEN last_name IS NULL OR last_name = '' THEN 'Missing last_name'
        ELSE 'OK'
    END as status
FROM iepa_attendee_registrations
WHERE first_name IS NULL OR first_name = '' OR last_name IS NULL OR last_name = ''

UNION ALL

SELECT 
    'speaker' as table_name,
    id,
    first_name,
    last_name,
    full_name,
    CASE 
        WHEN first_name IS NULL OR first_name = '' THEN 'Missing first_name'
        WHEN last_name IS NULL OR last_name = '' THEN 'Missing last_name'
        ELSE 'OK'
    END as status
FROM iepa_speaker_registrations
WHERE first_name IS NULL OR first_name = '' OR last_name IS NULL OR last_name = '';
