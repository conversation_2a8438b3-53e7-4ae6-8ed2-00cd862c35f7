-- IEPA Annual Meeting Registration - Add Remaining Missing Columns
-- This migration adds all remaining columns found in production

-- Add missing columns to iepa_user_profiles
ALTER TABLE iepa_user_profiles 
ADD COLUMN IF NOT EXISTS gender TEXT;

-- Add missing columns to iepa_organizations
ALTER TABLE iepa_organizations 
ADD COLUMN IF NOT EXISTS last_used_at TIMESTAMP WITH TIME ZONE;

-- Add missing columns to iepa_historical_registrations
ALTER TABLE iepa_historical_registrations 
ADD COLUMN IF NOT EXISTS golf_total DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS golf_club_rental_total DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS meal_total DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS grand_total DECIMAL(10,2) DEFAULT 0;
