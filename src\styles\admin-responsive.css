/* IEPA Admin Dashboard Responsive Styles */

/* Mobile-first responsive utilities for admin dashboard */

/* Container adjustments for admin pages */
.admin-container {
  padding: 1rem;
  max-width: 100%;
}

@media (min-width: 640px) {
  .admin-container {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .admin-container {
    padding: 2rem;
    margin-left: 16rem; /* Account for sidebar */
  }
}

/* Admin card responsive spacing */
.admin-card {
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .admin-card {
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .admin-card {
    margin-bottom: 2rem;
  }
}

/* Responsive grid for admin dashboard */
.admin-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .admin-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .admin-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1280px) {
  .admin-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Admin table responsive utilities */
.admin-table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Hide scrollbar on mobile for cleaner look */
@media (max-width: 1023px) {
  .admin-table-container::-webkit-scrollbar {
    display: none;
  }
  
  .admin-table-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Admin filter responsive layout */
.admin-filters {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

@media (min-width: 640px) {
  .admin-filters {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .admin-filters {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Admin action buttons responsive layout */
.admin-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .admin-actions {
    flex-direction: row;
    justify-content: flex-end;
    gap: 0.75rem;
  }
}

/* Admin sidebar responsive behavior */
.admin-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 16rem;
  background: white;
  border-right: 1px solid #e5e7eb;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  z-index: 50;
}

@media (min-width: 1024px) {
  .admin-sidebar {
    transform: translateX(0);
    position: fixed;
  }
}

.admin-sidebar.open {
  transform: translateX(0);
}

/* Admin main content responsive spacing */
.admin-main {
  min-height: 100vh;
  padding: 1rem;
  transition: margin-left 0.3s ease-in-out;
}

@media (min-width: 640px) {
  .admin-main {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .admin-main {
    margin-left: 16rem;
    padding: 2rem;
  }
}

/* Admin header responsive layout */
.admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 40;
}

@media (min-width: 640px) {
  .admin-header {
    padding: 1.5rem;
  }
}

/* Mobile menu toggle */
.admin-mobile-toggle {
  display: block;
}

@media (min-width: 1024px) {
  .admin-mobile-toggle {
    display: none;
  }
}

/* Admin page title responsive sizing */
.admin-page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

@media (min-width: 640px) {
  .admin-page-title {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .admin-page-title {
    font-size: 2.25rem;
  }
}

/* Admin stats cards responsive layout */
.admin-stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (min-width: 640px) {
  .admin-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .admin-stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Admin form responsive layout */
.admin-form {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .admin-form {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .admin-form {
    gap: 2rem;
  }
}

/* Admin modal responsive sizing */
.admin-modal {
  width: 95vw;
  max-width: 95vw;
  max-height: 90vh;
  overflow-y: auto;
}

@media (min-width: 640px) {
  .admin-modal {
    width: 80vw;
    max-width: 80vw;
  }
}

@media (min-width: 1024px) {
  .admin-modal {
    width: 60vw;
    max-width: 60vw;
  }
}

@media (min-width: 1280px) {
  .admin-modal {
    width: 50vw;
    max-width: 50vw;
  }
}

/* Admin pagination responsive layout */
.admin-pagination {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

@media (min-width: 640px) {
  .admin-pagination {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* Admin breadcrumb responsive behavior */
.admin-breadcrumb {
  display: none;
}

@media (min-width: 768px) {
  .admin-breadcrumb {
    display: block;
  }
}

/* Admin search responsive layout */
.admin-search {
  width: 100%;
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .admin-search {
    width: auto;
    min-width: 20rem;
    margin-bottom: 0;
  }
}

/* Admin badge responsive sizing */
.admin-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

@media (min-width: 640px) {
  .admin-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }
}

/* Admin button responsive sizing */
.admin-button-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

@media (min-width: 640px) {
  .admin-button-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Responsive text truncation utilities */
.admin-truncate-sm {
  max-width: 8rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@media (min-width: 640px) {
  .admin-truncate-sm {
    max-width: 12rem;
  }
}

@media (min-width: 1024px) {
  .admin-truncate-sm {
    max-width: 16rem;
  }
}

.admin-truncate-md {
  max-width: 12rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@media (min-width: 640px) {
  .admin-truncate-md {
    max-width: 20rem;
  }
}

@media (min-width: 1024px) {
  .admin-truncate-md {
    max-width: 24rem;
  }
}

/* Admin overlay for mobile menu */
.admin-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
  display: block;
}

@media (min-width: 1024px) {
  .admin-overlay {
    display: none;
  }
}

/* Focus states for accessibility */
.admin-focus:focus {
  outline: 2px solid var(--iepa-primary-blue);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .admin-card {
    border: 2px solid #000;
  }
  
  .admin-button {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .admin-sidebar,
  .admin-main {
    transition: none;
  }
}
