#!/usr/bin/env node

/**
 * Test script for Stripe MCP setup
 *
 * This script verifies that the Stripe MCP integration is properly configured
 * and can connect to the Stripe API.
 */

import { StripeAgentToolkit } from '@stripe/agent-toolkit/modelcontextprotocol';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function testStripeConnection() {
  console.log('🧪 Testing Stripe MCP Setup...\n');

  // Check environment variables
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
  const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

  console.log('📋 Environment Check:');
  console.log(
    `✅ STRIPE_SECRET_KEY: ${stripeSecretKey ? 'Set' : '❌ Missing'}`
  );
  console.log(
    `✅ STRIPE_PUBLISHABLE_KEY: ${stripePublishableKey ? 'Set' : '❌ Missing'}`
  );

  if (!stripeSecretKey) {
    console.error('\n❌ Error: STRIPE_SECRET_KEY is required');
    console.error('Please set it in your .env.local file');
    process.exit(1);
  }

  // Test key format
  const isTestKey = stripeSecretKey.startsWith('sk_test_');
  const isLiveKey = stripeSecretKey.startsWith('sk_live_');

  console.log(
    `🔑 Key Type: ${isTestKey ? '🧪 Test Key (Safe)' : isLiveKey ? '🚨 Live Key (Production)' : '❓ Unknown Format'}`
  );

  if (isLiveKey) {
    console.warn(
      "⚠️  Warning: You're using a live Stripe key. Be careful with testing!"
    );
  }

  try {
    // Initialize the Stripe Agent Toolkit
    console.log('\n🔧 Initializing Stripe Agent Toolkit...');

    const toolkit = new StripeAgentToolkit({
      secretKey: stripeSecretKey,
      configuration: {
        actions: {
          customers: {
            read: true,
          },
          balance: {
            read: true,
          },
        },
      },
    });

    console.log('✅ Stripe Agent Toolkit initialized successfully');

    // Test basic Stripe API connectivity using the regular Stripe SDK
    console.log('\n🌐 Testing Stripe API connectivity...');

    const stripe = require('stripe')(stripeSecretKey);

    // Test with a simple balance retrieval
    const balance = await stripe.balance.retrieve();
    console.log('✅ Successfully connected to Stripe API');
    console.log(
      `💰 Account Balance: ${balance.available[0]?.amount || 0} ${balance.available[0]?.currency || 'USD'} available`
    );

    // List recent customers (limit to 3 for testing)
    console.log('\n👥 Testing customer listing...');
    const customers = await stripe.customers.list({ limit: 3 });
    console.log(`✅ Found ${customers.data.length} customers in account`);

    if (customers.data.length > 0) {
      console.log('📝 Recent customers:');
      customers.data.forEach((customer: any, index: number) => {
        console.log(
          `   ${index + 1}. ${customer.email || customer.id} (${customer.created ? new Date(customer.created * 1000).toLocaleDateString() : 'Unknown date'})`
        );
      });
    }

    console.log(
      '\n🎉 All tests passed! Your Stripe MCP setup is ready to use.'
    );
    console.log('\n📚 Next steps:');
    console.log('   1. Start the MCP server: npm run stripe:mcp');
    console.log('   2. Or use npx: npm run stripe:mcp:npx');
    console.log('   3. Configure your AI assistant to use the MCP server');
    console.log(
      "   4. Test with AI assistant commands like 'create a customer' or 'list recent customers'"
    );
  } catch (error: any) {
    console.error('\n❌ Error testing Stripe connection:');

    if (error.type === 'StripeAuthenticationError') {
      console.error('🔐 Authentication failed - check your API key');
    } else if (error.type === 'StripePermissionError') {
      console.error(
        '🚫 Permission denied - your API key may not have sufficient permissions'
      );
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      console.error('🌐 Network error - check your internet connection');
    } else {
      console.error(`💥 ${error.message}`);
    }

    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Verify your STRIPE_SECRET_KEY in .env.local');
    console.error('   2. Ensure you have internet connectivity');
    console.error('   3. Check that your Stripe account is active');
    console.error('   4. Verify API key permissions in Stripe Dashboard');

    process.exit(1);
  }
}

// Run the test
testStripeConnection().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
