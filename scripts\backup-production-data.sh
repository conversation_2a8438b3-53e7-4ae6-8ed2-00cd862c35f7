#!/bin/bash

# IEPA Conference Registration - Production Data Backup
# This script creates a backup of production data for local development

set -e

echo "📦 IEPA Production Data Backup"
echo "=============================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Ensure we're in the project root
cd "$(dirname "$0")/.."

# Create backup directory
BACKUP_DIR=".docs/production-backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_PATH="$BACKUP_DIR/backup_$TIMESTAMP"

mkdir -p "$BACKUP_PATH"

print_status "Creating production data backup..."
print_status "Backup location: $BACKUP_PATH"

# Production database connection
PROD_DB_URL="*********************************************************************************************/postgres"

# Check if pg_dump is available
if ! command -v pg_dump &> /dev/null; then
    print_error "pg_dump not found. Please install PostgreSQL client tools."
    print_status "On macOS: brew install postgresql"
    print_status "On Ubuntu: sudo apt-get install postgresql-client"
    exit 1
fi

# IEPA tables to backup
TABLES=(
    "iepa_user_profiles"
    "iepa_organizations"
    "iepa_historical_registrations"
    "iepa_attendee_registrations"
    "iepa_speaker_registrations"
    "iepa_sponsor_registrations"
    "iepa_golf_registrations"
    "iepa_payments"
    "iepa_email_log"
)

print_status "Backing up ${#TABLES[@]} tables..."

# Backup each table
for table in "${TABLES[@]}"; do
    print_status "Backing up $table..."
    
    # Create data-only dump
    if pg_dump "$PROD_DB_URL" \
        --table="public.$table" \
        --data-only \
        --column-inserts \
        --no-owner \
        --no-privileges \
        --file="$BACKUP_PATH/${table}.sql" 2>/dev/null; then
        
        # Get row count
        ROW_COUNT=$(grep -c "INSERT INTO" "$BACKUP_PATH/${table}.sql" 2>/dev/null || echo "0")
        print_success "✅ $table: $ROW_COUNT rows"
    else
        print_warning "⚠️  $table: backup failed or table empty"
        rm -f "$BACKUP_PATH/${table}.sql"
    fi
done

# Create a combined backup file
print_status "Creating combined backup file..."
cat > "$BACKUP_PATH/restore_all.sql" << 'EOF'
-- IEPA Production Data Restore Script
-- Generated automatically - do not edit manually

-- Disable triggers and constraints for faster import
SET session_replication_role = replica;

-- Clear existing data (uncomment if needed)
-- TRUNCATE iepa_email_log, iepa_payments, iepa_golf_registrations, 
--          iepa_sponsor_registrations, iepa_speaker_registrations, 
--          iepa_attendee_registrations, iepa_historical_registrations, 
--          iepa_organizations, iepa_user_profiles CASCADE;

EOF

# Append each table's data in dependency order
for table in "${TABLES[@]}"; do
    if [ -f "$BACKUP_PATH/${table}.sql" ]; then
        echo "" >> "$BACKUP_PATH/restore_all.sql"
        echo "-- Restoring $table" >> "$BACKUP_PATH/restore_all.sql"
        cat "$BACKUP_PATH/${table}.sql" >> "$BACKUP_PATH/restore_all.sql"
    fi
done

cat >> "$BACKUP_PATH/restore_all.sql" << 'EOF'

-- Re-enable triggers and constraints
SET session_replication_role = DEFAULT;

-- Update sequences (if needed)
SELECT setval(pg_get_serial_sequence('iepa_user_profiles', 'id'), COALESCE(max(id), 1)) FROM iepa_user_profiles;
-- Add more sequence updates as needed

EOF

# Create restore script
cat > "$BACKUP_PATH/restore.sh" << 'EOF'
#!/bin/bash

# IEPA Production Data Restore Script
# This script restores the backed up data to your local Supabase

set -e

echo "🔄 Restoring IEPA production data to local Supabase..."

# Local database connection
LOCAL_DB_URL="postgresql://postgres:postgres@127.0.0.1:54322/postgres"

# Check if local Supabase is running
if ! curl -s http://127.0.0.1:54321/rest/v1/ > /dev/null 2>&1; then
    echo "❌ Local Supabase is not running"
    echo "Please run: supabase start"
    exit 1
fi

echo "✅ Local Supabase is running"

# Restore data
echo "📥 Restoring data..."
if psql "$LOCAL_DB_URL" -f restore_all.sql; then
    echo "✅ Data restored successfully!"
    echo ""
    echo "🔗 Access your data:"
    echo "   Studio: http://127.0.0.1:54323"
    echo "   API: http://127.0.0.1:54321"
else
    echo "❌ Restore failed"
    exit 1
fi
EOF

chmod +x "$BACKUP_PATH/restore.sh"

# Create summary
TOTAL_FILES=$(find "$BACKUP_PATH" -name "*.sql" -not -name "restore_all.sql" | wc -l)
TOTAL_SIZE=$(du -sh "$BACKUP_PATH" | cut -f1)

cat > "$BACKUP_PATH/README.md" << EOF
# IEPA Production Data Backup

**Created:** $(date)
**Tables:** $TOTAL_FILES
**Size:** $TOTAL_SIZE

## Files

- \`restore_all.sql\` - Combined restore script
- \`restore.sh\` - Automated restore script
- Individual table files: \`*.sql\`

## Usage

### Quick Restore (Recommended)
\`\`\`bash
cd $BACKUP_PATH
./restore.sh
\`\`\`

### Manual Restore
\`\`\`bash
# Make sure local Supabase is running
supabase start

# Restore data
psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -f restore_all.sql
\`\`\`

### TypeScript Sync (Alternative)
\`\`\`bash
# From project root
npm run sync-production-data
\`\`\`

## Tables Backed Up

$(for table in "${TABLES[@]}"; do
    if [ -f "$BACKUP_PATH/${table}.sql" ]; then
        ROW_COUNT=$(grep -c "INSERT INTO" "$BACKUP_PATH/${table}.sql" 2>/dev/null || echo "0")
        echo "- $table ($ROW_COUNT rows)"
    fi
done)
EOF

print_success "🎉 Backup completed!"
echo ""
echo "📁 Backup location: $BACKUP_PATH"
echo "📄 Files created: $TOTAL_FILES table files + restore scripts"
echo "💾 Total size: $TOTAL_SIZE"
echo ""
echo "🔄 To restore to local development:"
echo "   cd $BACKUP_PATH && ./restore.sh"
echo ""
echo "📖 See README.md in backup folder for more options"
