import { supabase } from '@/lib/supabase';
import {
  AgendaEvent,
  AgendaDayWithEvents,
  LegacyAgendaDay,
  convertToLegacyFormat
} from '@/lib/types/agenda';

/**
 * Fetch all agenda days with their events from Supabase
 */
export async function fetchAgendaData(): Promise<AgendaDayWithEvents[]> {
  try {
    // Fetch agenda days
    const { data: agendaDays, error: daysError } = await supabase
      .from('iepa_agenda_days')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (daysError) {
      console.error('Error fetching agenda days:', daysError);
      throw new Error(`Failed to fetch agenda days: ${daysError.message}`);
    }

    if (!agendaDays || agendaDays.length === 0) {
      console.warn('No agenda days found in database');
      return [];
    }

    // Fetch all events for all days
    const { data: agendaEvents, error: eventsError } = await supabase
      .from('iepa_agenda_events')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (eventsError) {
      console.error('Error fetching agenda events:', eventsError);
      throw new Error(`Failed to fetch agenda events: ${eventsError.message}`);
    }

    // Group events by agenda_day_id
    const eventsByDayId = (agendaEvents || []).reduce((acc, event) => {
      if (!acc[event.agenda_day_id]) {
        acc[event.agenda_day_id] = [];
      }
      acc[event.agenda_day_id].push(event);
      return acc;
    }, {} as Record<string, AgendaEvent[]>);

    // Combine days with their events
    const agendaDaysWithEvents: AgendaDayWithEvents[] = agendaDays.map(day => ({
      ...day,
      events: eventsByDayId[day.id] || [],
    }));

    console.log(`✅ Successfully fetched ${agendaDaysWithEvents.length} agenda days with events`);
    return agendaDaysWithEvents;

  } catch (error) {
    console.error('Error in fetchAgendaData:', error);
    throw error;
  }
}

/**
 * Fetch agenda data in legacy format for backward compatibility
 */
export async function fetchAgendaDataLegacy(): Promise<LegacyAgendaDay[]> {
  try {
    const agendaData = await fetchAgendaData();
    return convertToLegacyFormat(agendaData);
  } catch (error) {
    console.error('Error fetching agenda data in legacy format:', error);
    throw error;
  }
}

/**
 * Fallback agenda data (same as current hardcoded data)
 * Used when database is unavailable or empty
 */
export function getFallbackAgendaData(): LegacyAgendaDay[] {
  return [
    {
      dayNumber: 1,
      title: 'Day 1 - September 15, 2025',
      date: '2025-09-15',
      color: 'var(--iepa-primary-blue)',
      events: [
        {
          time: '3:00 PM',
          title: 'Registration & Check-in',
          description: 'Welcome reception begins',
        },
        {
          time: '4:00 PM',
          title: 'Opening Keynote',
          description: 'Industry outlook and emerging trends',
        },
        {
          time: '6:00 PM',
          title: 'Welcome Dinner',
          description: 'Networking and dinner reception',
        },
        {
          time: '11:00 AM',
          title: 'Golf Tournament',
          description: 'Optional golf at South Lake Tahoe Golf Course',
        },
      ],
    },
    {
      dayNumber: 2,
      title: 'Day 2 - Tuesday, September 16, 2025',
      date: '2025-09-16',
      color: 'var(--iepa-secondary-green)',
      events: [
        {
          time: '8:00 AM',
          title: 'Breakfast',
          description: 'Continental breakfast and networking',
        },
        {
          time: '9:00 AM',
          title: 'Technical Sessions',
          description: 'Concurrent breakout sessions and workshops',
        },
        {
          time: '12:00 PM',
          title: 'Lunch & Keynote',
          description: 'Networking lunch with featured speaker',
        },
        {
          time: '2:00 PM',
          title: 'Exhibition',
          description: 'Technology showcase and poster sessions',
        },
        {
          time: '6:30 PM',
          title: 'Networking Dinner',
          description: 'Reception and plated dinner',
        },
      ],
    },
    {
      dayNumber: 3,
      title: 'Day 3 - September 17, 2025',
      date: '2025-09-17',
      color: 'var(--iepa-accent-teal)',
      events: [
        {
          time: '8:00 AM',
          title: 'Breakfast',
          description: 'Final networking breakfast',
        },
        {
          time: '9:00 AM',
          title: 'Final Sessions',
          description: 'Wrap-up presentations and panel discussions',
        },
        {
          time: '12:00 PM',
          title: 'Closing Lunch',
          description: 'Conference conclusion and departures',
        },
        {
          time: '1:00 PM',
          title: 'Departure',
          description: 'Check-out and safe travels',
        },
      ],
    },
  ];
}

/**
 * Get agenda data with fallback to hardcoded data
 * This ensures the app continues to work even if database is unavailable
 */
export async function getAgendaDataWithFallback(): Promise<LegacyAgendaDay[]> {
  try {
    console.log('🔄 Attempting to fetch agenda data from Supabase...');
    const agendaData = await fetchAgendaDataLegacy();
    
    if (agendaData && agendaData.length > 0) {
      console.log('✅ Successfully loaded agenda data from Supabase');
      return agendaData;
    } else {
      console.warn('⚠️ No agenda data found in Supabase, using fallback data');
      return getFallbackAgendaData();
    }
  } catch (error) {
    console.error('❌ Error fetching agenda data from Supabase, using fallback:', error);
    return getFallbackAgendaData();
  }
}
