'use client';

import { useState, useEffect, useCallback } from 'react';
import { Check, ChevronsUpDown, Plus, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button, Input, Popover, PopoverTrigger, PopoverContent } from '@/components/ui';

interface Organization {
  id: string;
  name: string;
  usage_count?: number;
}

interface OrganizationComboboxProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  label?: string;
  isRequired?: boolean;
  error?: string;
}

export default function OrganizationCombobox({
  value = '',
  onChange,
  placeholder = 'Select or enter organization...',
  className,
  disabled = false,
  label,
  isRequired = false,
  error
}: OrganizationComboboxProps) {
  const [open, setOpen] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [inputValue, setInputValue] = useState(value);

  // Debounce search function
  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // Fetch organizations from API
  const fetchOrganizations = useCallback(async (search?: string) => {
    setLoading(true);
    try {
      const url = new URL('/api/organizations', window.location.origin);
      if (search && search.trim()) {
        url.searchParams.set('search', search.trim());
      }
      url.searchParams.set('limit', '50');

      const response = await fetch(url.toString());
      const data = await response.json();

      if (data.success) {
        setOrganizations(data.organizations || []);
      } else {
        console.error('[ORG-COMBOBOX] Failed to fetch organizations:', data.error);
      }
    } catch (error) {
      console.error('[ORG-COMBOBOX] Error fetching organizations:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Debounced search
  const debouncedSearch = useCallback(
    debounce((search: string) => {
      setSearchTerm(search);
      fetchOrganizations(search);
    }, 300),
    [fetchOrganizations]
  );

  // Initial load
  useEffect(() => {
    fetchOrganizations();
  }, [fetchOrganizations]);

  // Handle search input changes
  const handleSearchChange = (search: string) => {
    setSearchTerm(search);
    debouncedSearch(search);
  };

  // Handle organization selection
  const handleSelect = async (selectedValue: string) => {
    const trimmedValue = selectedValue.trim();
    setInputValue(trimmedValue);
    onChange(trimmedValue);
    setOpen(false);

    // Update usage count if organization exists
    if (trimmedValue) {
      try {
        await fetch('/api/organizations', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            organizationName: trimmedValue
          }),
        });
      } catch (error) {
        console.error('[ORG-COMBOBOX] Error updating usage count:', error);
      }
    }
  };

  // Handle creating new organization
  const handleCreateNew = async () => {
    const trimmedValue = searchTerm.trim();
    if (!trimmedValue) return;

    try {
      const response = await fetch('/api/organizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: trimmedValue
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        // Add the new organization to the list
        setOrganizations(prev => [data.organization, ...prev]);
        // Select the new organization
        handleSelect(trimmedValue);
      } else {
        console.error('[ORG-COMBOBOX] Failed to create organization:', data.error);
      }
    } catch (error) {
      console.error('[ORG-COMBOBOX] Error creating organization:', error);
    }
  };

  // Handle manual input change
  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    onChange(newValue);
    handleSearchChange(newValue);
  };

  // Sync with external value changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Filter organizations based on current search
  const filteredOrganizations = organizations.filter(org =>
    org.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Check if current search matches any existing organization
  const exactMatch = organizations.find(org => 
    org.name.toLowerCase() === searchTerm.toLowerCase()
  );

  const showCreateOption = searchTerm.trim() && !exactMatch && !loading;

  return (
    <div className={cn('w-full', className)}>
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-2 block">
          {label}
          {isRequired && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <Popover isOpen={open} onOpenChange={setOpen} placement="bottom-start">
        <PopoverTrigger>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              'w-full justify-between text-left font-normal h-auto p-0',
              !inputValue && 'text-muted-foreground',
              error && 'border-red-500 ring-red-500',
              disabled && 'opacity-50 cursor-not-allowed'
            )}
            disabled={disabled}
          >
            <Input
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              placeholder={placeholder}
              className="border-0 focus:ring-0 focus:outline-none bg-transparent"
              disabled={disabled}
            />
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-full p-0 max-h-60 overflow-auto bg-white">
          <div className="p-2">
            <div className="relative mb-2">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search organizations..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <div className="max-h-40 overflow-auto">
              {loading ? (
                <div className="p-2 text-sm text-gray-500">Loading organizations...</div>
              ) : (
                <>
                  {filteredOrganizations.length === 0 && !showCreateOption ? (
                    <div className="p-2 text-sm text-gray-500">No organizations found.</div>
                  ) : (
                    <div className="space-y-1">
                      {filteredOrganizations.map((org) => (
                        <div
                          key={org.id}
                          className={cn(
                            'flex items-center space-x-2 p-2 rounded cursor-pointer hover:bg-gray-100',
                            inputValue === org.name && 'bg-blue-50'
                          )}
                          onClick={() => handleSelect(org.name)}
                        >
                          <Check
                            className={cn(
                              'h-4 w-4',
                              inputValue === org.name ? 'opacity-100' : 'opacity-0'
                            )}
                          />
                          <div className="flex-1">
                            <div className="font-medium text-sm">{org.name}</div>
                            {org.usage_count && org.usage_count > 1 && (
                              <div className="text-xs text-gray-500">
                                Used {org.usage_count} times
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                      
                      {showCreateOption && (
                        <div
                          className="flex items-center space-x-2 p-2 rounded cursor-pointer hover:bg-blue-50 text-blue-600"
                          onClick={handleCreateNew}
                        >
                          <Plus className="h-4 w-4" />
                          <span className="text-sm">Create "{searchTerm}"</span>
                        </div>
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {error && (
        <div className="mt-1 text-sm text-red-600" role="alert">
          {error}
        </div>
      )}
    </div>
  );
} 