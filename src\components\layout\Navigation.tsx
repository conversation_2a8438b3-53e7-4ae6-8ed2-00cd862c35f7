'use client';

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  Button,
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import {
  UserDropdown,
  CompactUserDropdown,
} from '@/components/auth/UserDropdown';
import { AuthStatusIndicator } from '@/components/auth/AuthStatusIndicator';
import { WelcomeBar } from '@/components/layout/WelcomeBar';
import { Breadcrumbs } from '@/components/layout/Breadcrumbs';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import {
  FiMenu,
  FiX,
  FiHome,
  FiInfo,
  FiUserPlus,
  FiMail,
  FiUser,
} from 'react-icons/fi';

export function Navigation() {
  const { user } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const menuItems = [
    { name: 'Home', href: '/', icon: FiHome },
    { name: 'Annual Meeting Info', href: '/about', icon: FiInfo },
    { name: 'Register', href: '/register', icon: FiUserPlus },
    { name: 'Contact', href: '/contact', icon: FiMail },
  ];

  const registerItems = [
    { name: 'Register - Attendee', href: '/register/attendee' },
    { name: 'Register - Speaker', href: '/register/speaker' },
    { name: 'Register - Sponsor', href: '/register/sponsor' },
    // Note: Sponsor Attendee registration is only accessible via email links
  ];

  const meetingInfoItems = [
    { name: 'Annual Meeting Info', href: '/about' },
    { name: 'Annual Meeting Agenda', href: '/agenda' },
    { name: 'Resources', href: '/about#resources' },
  ];

  return (
    <>
      {/* Welcome Bar - Only shown for authenticated users */}
      <WelcomeBar />

      <header
        id="main-navigation"
        className="sticky top-0 z-50 w-full border-b border-iepa-primary-light bg-white shadow-sm dark:bg-iepa-primary-dark dark:border-iepa-primary"
        role="banner"
        data-testid="main-navigation"
      >
        {/* Main Navigation Container */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Top Navigation Bar - Single Flex Container */}
          <div className="flex items-center justify-between h-16 gap-4">
            {/* Left: Mobile Menu + Logo */}
            <div
              id="navigation-brand-section"
              className="flex items-center gap-3"
              data-testid="navigation-brand-section"
            >
              {/* Mobile Menu Toggle - Enhanced for Accessibility */}
              <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                <SheetTrigger asChild>
                  <Button
                    id="mobile-menu-toggle"
                    variant="ghost"
                    size="sm"
                    className="md:hidden flex-shrink-0 h-10 w-10 p-2 text-iepa-primary-dark hover:text-white hover:bg-iepa-primary focus:ring-2 focus:ring-iepa-primary focus:ring-offset-2 focus:ring-offset-white focus:outline-none transition-all duration-200 rounded-md"
                    aria-label={
                      isMenuOpen
                        ? 'Close navigation menu'
                        : 'Open navigation menu'
                    }
                    aria-expanded={isMenuOpen}
                    aria-controls="mobile-navigation-menu"
                    data-testid="mobile-menu-toggle"
                  >
                    {isMenuOpen ? (
                      <FiX className="h-5 w-5" aria-hidden="true" />
                    ) : (
                      <FiMenu className="h-5 w-5" aria-hidden="true" />
                    )}
                  </Button>
                </SheetTrigger>
                <SheetContent
                  id="mobile-navigation-menu"
                  side="left"
                  className="w-80 max-w-[85vw] bg-iepa-primary-dark border-iepa-primary shadow-2xl"
                  data-testid="mobile-navigation-menu"
                  aria-labelledby="mobile-menu-title"
                >
                  <SheetHeader className="pb-4">
                    <SheetTitle
                      id="mobile-menu-title"
                      className="text-white text-left text-lg font-semibold"
                      data-testid="mobile-menu-title"
                    >
                      Navigation Menu
                    </SheetTitle>
                  </SheetHeader>
                  <nav
                    id="mobile-navigation-links"
                    className="mt-2 space-y-1"
                    role="navigation"
                    aria-label="Mobile navigation"
                    data-testid="mobile-navigation-links"
                  >
                    {menuItems.map(item => (
                      <Link
                        key={item.name}
                        href={item.href}
                        onClick={() => setIsMenuOpen(false)}
                        className="mobile-nav-link flex items-center gap-3 w-full min-h-[44px] text-white hover:text-white hover:bg-white/20 active:bg-white/30 py-3 px-4 rounded-md font-medium transition-all duration-200 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-iepa-primary-dark focus:outline-none"
                        data-testid={`mobile-nav-${item.name.toLowerCase().replace(/\s+/g, '-')}`}
                        tabIndex={0}
                      >
                        <item.icon
                          className="h-5 w-5 flex-shrink-0"
                          aria-hidden="true"
                        />
                        <span className="text-base">{item.name}</span>
                      </Link>
                    ))}

                    {/* Meeting Info Submenu in Mobile */}
                    <div
                      id="mobile-meeting-submenu"
                      className="py-3 border-t border-white/20 mt-3"
                      data-testid="mobile-meeting-submenu"
                      role="group"
                      aria-labelledby="mobile-meeting-heading"
                    >
                      <h3
                        id="mobile-meeting-heading"
                        className="text-sm font-semibold mb-3 text-white/90 px-4 uppercase tracking-wide"
                        data-testid="mobile-meeting-heading"
                      >
                        Annual Meeting
                      </h3>
                      <div
                        id="mobile-meeting-links"
                        className="pl-4 space-y-1"
                        data-testid="mobile-meeting-links"
                      >
                        {meetingInfoItems.map(item => (
                          <Link
                            key={item.name}
                            href={item.href}
                            onClick={() => setIsMenuOpen(false)}
                            className="mobile-meeting-link block min-h-[44px] text-white hover:text-white hover:bg-white/20 active:bg-white/30 text-sm py-3 px-3 rounded-md transition-all duration-200 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-iepa-primary-dark focus:outline-none"
                            data-testid={`mobile-meeting-${item.name.toLowerCase().replace(/\s+/g, '-')}`}
                            tabIndex={0}
                          >
                            <span className="flex items-center h-full">
                              {item.name}
                            </span>
                          </Link>
                        ))}
                      </div>
                    </div>

                    {/* Register Submenu in Mobile - Enhanced Accessibility */}
                    <div
                      id="mobile-register-submenu"
                      className="py-3 border-t border-white/20 mt-3"
                      data-testid="mobile-register-submenu"
                      role="group"
                      aria-labelledby="mobile-register-heading"
                    >
                      <h3
                        id="mobile-register-heading"
                        className="text-sm font-semibold mb-3 text-white/90 px-4 uppercase tracking-wide"
                        data-testid="mobile-register-heading"
                      >
                        Registration Options
                      </h3>
                      <div
                        id="mobile-register-links"
                        className="pl-4 space-y-1"
                        data-testid="mobile-register-links"
                      >
                        {registerItems.map(item => (
                          <Link
                            key={item.name}
                            href={item.href}
                            onClick={() => setIsMenuOpen(false)}
                            className="mobile-register-link block min-h-[44px] text-white hover:text-white hover:bg-white/20 active:bg-white/30 text-sm py-3 px-3 rounded-md transition-all duration-200 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-iepa-primary-dark focus:outline-none"
                            data-testid={`mobile-register-${item.name.toLowerCase().replace(/\s+/g, '-')}`}
                            tabIndex={0}
                          >
                            <span className="flex items-center h-full">
                              {item.name}
                            </span>
                          </Link>
                        ))}
                      </div>
                    </div>

                    {/* User Info in Mobile - Enhanced Accessibility */}
                    {user ? (
                      <div
                        id="mobile-user-info"
                        className="border-t border-white/20 pt-4 mt-4"
                        data-testid="mobile-user-info"
                        role="group"
                        aria-label="User information"
                      >
                        <div className="px-4 py-3 bg-white/10 rounded-md mb-3">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 bg-white/20 rounded-full flex items-center justify-center">
                              <span className="text-white font-medium text-sm">
                                {user.email?.charAt(0).toUpperCase() || 'U'}
                              </span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-white font-medium text-sm truncate">
                                {user.user_metadata?.full_name ||
                                  user.email?.split('@')[0] ||
                                  'User'}
                              </p>
                              <p className="text-white/70 text-xs truncate">
                                {user.email}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <Link
                            href="/my-registrations"
                            onClick={() => setIsMenuOpen(false)}
                            className="flex items-center gap-3 w-full min-h-[44px] text-white hover:text-white hover:bg-white/20 active:bg-white/30 py-3 px-4 rounded-md transition-all duration-200 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-iepa-primary-dark focus:outline-none"
                            data-testid="mobile-user-registrations"
                            tabIndex={0}
                          >
                            <FiUser
                              className="h-5 w-5 flex-shrink-0"
                              aria-hidden="true"
                            />
                            <span className="text-base">My Registrations</span>
                          </Link>
                        </div>
                      </div>
                    ) : (
                      <div
                        id="mobile-auth-links"
                        className="border-t border-white/20 pt-4 mt-4 space-y-2"
                        data-testid="mobile-auth-links"
                        role="group"
                        aria-label="Authentication options"
                      >
                        <Link
                          href="/auth/magic-link"
                          onClick={() => setIsMenuOpen(false)}
                          className="mobile-auth-login flex items-center gap-3 w-full min-h-[44px] text-white hover:text-white hover:bg-white/20 active:bg-white/30 py-3 px-4 rounded-md font-medium transition-all duration-200 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-iepa-primary-dark focus:outline-none"
                          data-testid="mobile-auth-login"
                          tabIndex={0}
                        >
                          <FiUser
                            className="h-5 w-5 flex-shrink-0"
                            aria-hidden="true"
                          />
                          <span className="text-base">Sign In</span>
                        </Link>
                        <Link
                          href="/auth/signup"
                          onClick={() => setIsMenuOpen(false)}
                          className="mobile-auth-signup flex items-center gap-3 w-full min-h-[44px] bg-white text-iepa-primary hover:bg-iepa-gray-100 active:bg-iepa-gray-200 font-semibold py-3 px-4 rounded-md transition-all duration-200 focus:ring-2 focus:ring-iepa-primary focus:ring-offset-2 focus:ring-offset-white focus:outline-none shadow-sm"
                          data-testid="mobile-auth-signup"
                          tabIndex={0}
                        >
                          <FiUserPlus
                            className="h-5 w-5 flex-shrink-0"
                            aria-hidden="true"
                          />
                          <span className="text-base">Create Account</span>
                        </Link>
                      </div>
                    )}
                  </nav>
                </SheetContent>
              </Sheet>

              {/* Logo */}
              <Link
                id="iepa-logo-link"
                href="/"
                className="font-bold flex items-center gap-2 text-iepa-primary-dark hover:text-iepa-primary focus:ring-2 focus:ring-iepa-primary focus:ring-offset-2 focus:ring-offset-white transition-all rounded-md px-1 py-1 flex-shrink-0"
                data-testid="iepa-logo-link"
              >
                <Image
                  id="iepa-logo-image"
                  src="/iepa_svg_logo.svg"
                  alt="IEPA Logo"
                  width={100}
                  height={28}
                  className="h-7 w-auto"
                  priority
                  data-testid="iepa-logo-image"
                />
                <span
                  id="conference-title-full"
                  className="hidden xl:block text-lg font-semibold text-iepa-primary-dark"
                  data-testid="conference-title-full"
                >
                  {CONFERENCE_YEAR} Annual Meeting
                </span>
                <span
                  id="conference-title-short"
                  className="hidden lg:block xl:hidden text-xs font-semibold text-iepa-primary-dark"
                  data-testid="conference-title-short"
                >
                  {CONFERENCE_YEAR}
                </span>
              </Link>
            </div>

            {/* Center: Desktop Navigation */}
            <div
              id="desktop-navigation-section"
              className="hidden md:flex items-center"
              data-testid="desktop-navigation-section"
            >
              <NavigationMenu>
                <NavigationMenuList
                  id="desktop-navigation-list"
                  className="gap-6"
                  data-testid="desktop-navigation-list"
                >
                  <NavigationMenuItem>
                    <NavigationMenuTrigger
                      id="nav-meeting-trigger"
                      className="text-iepa-primary-dark transition-all duration-200 font-medium bg-transparent data-[state=open]:bg-iepa-primary data-[state=open]:text-white"
                      data-testid="nav-meeting-trigger"
                    >
                      <FiInfo className="mr-2 h-4 w-4" />
                      Annual Meeting Info
                    </NavigationMenuTrigger>
                    <NavigationMenuContent
                      id="nav-meeting-dropdown"
                      className="bg-white border border-gray-200 shadow-lg rounded-md"
                      data-testid="nav-meeting-dropdown"
                    >
                      <div
                        id="nav-meeting-options"
                        className="grid gap-3 p-4 w-80 bg-white"
                        data-testid="nav-meeting-options"
                      >
                        {meetingInfoItems.map(item => (
                          <NavigationMenuLink key={item.name} asChild>
                            <Link
                              href={item.href}
                              className="desktop-meeting-link block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-all duration-200 hover:bg-[var(--iepa-primary-blue-dark)] hover:text-white focus:bg-[var(--iepa-primary-blue-dark)] focus:text-white"
                              data-testid={`nav-meeting-${item.name.toLowerCase().replace(/\s+/g, '-')}`}
                            >
                              <div className="text-sm font-medium leading-none">
                                {item.name}
                              </div>
                            </Link>
                          </NavigationMenuLink>
                        ))}
                      </div>
                    </NavigationMenuContent>
                  </NavigationMenuItem>

                  <NavigationMenuItem>
                    <NavigationMenuTrigger
                      id="nav-register-trigger"
                      className="text-iepa-primary-dark transition-all duration-200 font-medium bg-transparent data-[state=open]:bg-iepa-primary data-[state=open]:text-white"
                      data-testid="nav-register-trigger"
                    >
                      <FiUserPlus className="mr-2 h-4 w-4" />
                      Register
                    </NavigationMenuTrigger>
                    <NavigationMenuContent
                      id="nav-register-dropdown"
                      className="bg-white border border-gray-200 shadow-lg rounded-md"
                      data-testid="nav-register-dropdown"
                    >
                      <div
                        id="nav-register-options"
                        className="grid gap-3 p-4 w-80 bg-white"
                        data-testid="nav-register-options"
                      >
                        {registerItems.map(item => (
                          <NavigationMenuLink key={item.name} asChild>
                            <Link
                              href={item.href}
                              className="desktop-register-link block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-all duration-200 hover:bg-[var(--iepa-primary-blue-dark)] hover:text-white focus:bg-[var(--iepa-primary-blue-dark)] focus:text-white"
                              data-testid={`nav-register-${item.name.toLowerCase().replace(/\s+/g, '-')}`}
                            >
                              <div className="text-sm font-medium leading-none">
                                {item.name}
                              </div>
                            </Link>
                          </NavigationMenuLink>
                        ))}
                      </div>
                    </NavigationMenuContent>
                  </NavigationMenuItem>

                  <NavigationMenuItem className="mb-3">
                    <NavigationMenuLink asChild>
                      <Link
                        id="nav-contact-link"
                        href="/contact"
                        className="text-iepa-primary-dark transition-all duration-200 font-medium bg-transparent inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2"
                        data-testid="nav-contact-link"
                      >
                        <FiMail className="mr-2 h-4 w-4" />
                        Contact
                      </Link>
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                </NavigationMenuList>
              </NavigationMenu>
            </div>

            {/* Right: User Menu */}
            <div
              id="user-menu-section"
              className="flex items-center justify-end flex-shrink-0"
              data-testid="user-menu-section"
            >
              {user ? (
                <div
                  id="authenticated-user-menu"
                  data-testid="authenticated-user-menu"
                >
                  {/* Desktop User Section */}
                  <div
                    id="desktop-user-section"
                    className="hidden md:flex items-center gap-2 bg-iepa-gray-50 rounded-lg px-3 py-2 border border-iepa-gray-200 shadow-sm"
                    data-testid="desktop-user-section"
                  >
                    <AuthStatusIndicator
                      variant="badge"
                      className="hidden lg:block"
                    />
                    <UserDropdown />
                  </div>

                  {/* Mobile User Dropdown */}
                  <div
                    id="mobile-user-section"
                    className="md:hidden"
                    data-testid="mobile-user-section"
                  >
                    <CompactUserDropdown />
                  </div>
                </div>
              ) : (
                <div
                  id="guest-auth-section"
                  className="flex items-center gap-1 sm:gap-2 bg-iepa-gray-50 rounded-lg px-1 sm:px-2 py-1.5 border border-iepa-gray-200 shadow-sm"
                  data-testid="guest-auth-section"
                >
                  <Button
                    asChild
                    variant="ghost"
                    size="sm"
                    className="iepa-nav-login-btn px-2 sm:px-3 py-1.5 text-xs sm:text-sm"
                    data-testid="nav-login-button"
                  >
                    <Link
                      href="/auth/magic-link"
                      className="flex items-center gap-1 sm:gap-1.5"
                    >
                      <FiUser className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Sign In</span>
                      <span className="sm:hidden">Login</span>
                    </Link>
                  </Button>
                  <Button
                    asChild
                    size="sm"
                    className="iepa-nav-signup-btn px-2 sm:px-3 py-1.5 text-xs sm:text-sm"
                    data-testid="nav-signup-button"
                  >
                    <Link
                      href="/auth/signup"
                      className="flex items-center gap-1 sm:gap-1.5"
                    >
                      <FiUserPlus className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Create Account</span>
                      <span className="sm:hidden">Sign Up</span>
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Breadcrumbs - Separate row with proper spacing */}
          <div className="border-t border-iepa-gray-100 mt-1 pt-3 pb-1">
            <Breadcrumbs
              className="iepa-nav-breadcrumbs"
              showIcons={false}
              compact={true}
            />
          </div>
        </div>
      </header>
    </>
  );
}
