import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

export async function POST() {
  try {
    console.log('[CREATE-EMAIL-LOG-TABLE] Creating email log table...');

    // SQL to create the email log table
    const createTableSQL = `
      -- Create email log table
      CREATE TABLE IF NOT EXISTS public.iepa_email_log (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        
        -- Email details
        recipient_email VARCHAR(255) NOT NULL,
        recipient_name VARCHAR(255),
        sender_email VARCHAR(255) NOT NULL,
        sender_name <PERSON><PERSON><PERSON><PERSON>(255),
        subject VARCHAR(500) NOT NULL,
        
        -- Email classification
        email_type VARCHAR(50) NOT NULL,
        template_used VARCHAR(100),
        
        -- Content reference
        content_preview TEXT,
        has_attachments BOOLEAN DEFAULT FALSE,
        
        -- Delivery status
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        sendgrid_message_id VARCHAR(255),
        error_message TEXT,
        
        -- Related records
        user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
        registration_id UUID,
        registration_type VARCHAR(20),
        payment_id UUID,
        
        -- Timestamps
        sent_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // Create indexes
    const createIndexesSQL = `
      CREATE INDEX IF NOT EXISTS idx_iepa_email_log_recipient ON public.iepa_email_log(recipient_email);
      CREATE INDEX IF NOT EXISTS idx_iepa_email_log_type ON public.iepa_email_log(email_type);
      CREATE INDEX IF NOT EXISTS idx_iepa_email_log_status ON public.iepa_email_log(status);
      CREATE INDEX IF NOT EXISTS idx_iepa_email_log_user_id ON public.iepa_email_log(user_id);
      CREATE INDEX IF NOT EXISTS idx_iepa_email_log_registration ON public.iepa_email_log(registration_id, registration_type);
      CREATE INDEX IF NOT EXISTS idx_iepa_email_log_created_at ON public.iepa_email_log(created_at);
    `;

    // Create trigger function
    const createTriggerSQL = `
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql';

      DROP TRIGGER IF EXISTS update_iepa_email_log_updated_at ON public.iepa_email_log;
      CREATE TRIGGER update_iepa_email_log_updated_at 
        BEFORE UPDATE ON public.iepa_email_log 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `;

    // Enable RLS and create policies
    const createPoliciesSQL = `
      ALTER TABLE public.iepa_email_log ENABLE ROW LEVEL SECURITY;

      DROP POLICY IF EXISTS "Service role full access" ON public.iepa_email_log;
      DROP POLICY IF EXISTS "Admins can view all email logs" ON public.iepa_email_log;
      DROP POLICY IF EXISTS "Admins can insert email logs" ON public.iepa_email_log;
      DROP POLICY IF EXISTS "Users can view their own email logs" ON public.iepa_email_log;

      CREATE POLICY "Service role full access" 
        ON public.iepa_email_log FOR ALL 
        TO service_role 
        USING (true) 
        WITH CHECK (true);

      CREATE POLICY "Admins can view all email logs" 
        ON public.iepa_email_log FOR SELECT 
        TO authenticated 
        USING (
          EXISTS (
            SELECT 1 FROM public.iepa_admin_users 
            WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
            AND is_active = true
          )
        );

      CREATE POLICY "Admins can insert email logs" 
        ON public.iepa_email_log FOR INSERT 
        TO authenticated 
        WITH CHECK (
          EXISTS (
            SELECT 1 FROM public.iepa_admin_users 
            WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
            AND is_active = true
          )
        );

      CREATE POLICY "Users can view their own email logs" 
        ON public.iepa_email_log FOR SELECT 
        TO authenticated 
        USING (user_id = auth.uid());
    `;

    // Execute SQL statements using the SQL editor approach
    console.log('[CREATE-EMAIL-LOG-TABLE] Creating table and setup...');

    const fullSQL = `
      ${createTableSQL}
      ${createIndexesSQL}
      ${createTriggerSQL}
      ${createPoliciesSQL}
    `;

    // Use a direct SQL execution approach
    const { error: sqlError } = await supabase
      .from('iepa_email_log')
      .select('id')
      .limit(1);

    // If the table doesn't exist, we need to create it manually
    if (sqlError && sqlError.message.includes('does not exist')) {
      return NextResponse.json({
        success: false,
        error: 'Email log table needs to be created manually',
        instructions: {
          step1: 'Go to your Supabase project dashboard',
          step2: 'Navigate to SQL Editor',
          step3: 'Copy and paste the SQL from the response below',
          step4: 'Execute the SQL to create the table',
          step5: 'Test again using the setup endpoint'
        },
        sql: fullSQL,
        note: 'This is a one-time setup. After running the SQL, the email logging will work automatically.'
      }, { status: 200 });
    }

    // Test the table by inserting and deleting a test record
    console.log('[CREATE-EMAIL-LOG-TABLE] Testing table...');
    const testRecord = {
      recipient_email: '<EMAIL>',
      sender_email: '<EMAIL>',
      subject: 'Email Log Table Setup Test',
      email_type: 'setup_test',
      content_preview: 'This is a test to verify the email log table was created successfully.',
      status: 'sent'
    };

    const { data: insertData, error: insertError } = await supabase
      .from('iepa_email_log')
      .insert([testRecord])
      .select();

    if (insertError) {
      console.error('[CREATE-EMAIL-LOG-TABLE] Test insert failed:', insertError);
      throw insertError;
    }

    // Clean up test record
    if (insertData && insertData[0]) {
      await supabase
        .from('iepa_email_log')
        .delete()
        .eq('id', insertData[0].id);
    }

    console.log('[CREATE-EMAIL-LOG-TABLE] Email log table created successfully!');

    return NextResponse.json({
      success: true,
      message: 'Email log table created successfully',
      tableCreated: true,
      indexesCreated: true,
      triggersCreated: true,
      policiesCreated: true,
      testPassed: true,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CREATE-EMAIL-LOG-TABLE] Failed to create table:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create email log table',
      details: 'Check server logs for more information',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
