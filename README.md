# IEPA Annual Meeting Registration System

A comprehensive annual meeting registration and management system built for the Independent Energy Producers Association (IEPA) Annual Meeting. This Next.js application provides registration forms, payment processing, administrative dashboards, and AI-powered assistance through Stripe MCP integration.

## Features

- **Multi-step Registration Forms**: Attendee, Speaker, and Sponsor registration workflows
- **Payment Processing**: Integrated Stripe payment system with invoice generation
- **Administrative Dashboard**: Complete management interface for conference organizers
- **AI Assistant Integration**: Stripe MCP (Model Context Protocol) for AI-powered payment operations
- **Responsive Design**: Mobile-first design with IEPA branding
- **Database Integration**: Supabase backend with real-time capabilities

## Getting Started

### Prerequisites

- Node.js 18+
- Docker Desktop
- Supabase CLI (`brew install supabase/tap/supabase`)
- Stripe account (test/live API keys) - optional for basic development

### Quick Start (Automated Setup)

The project includes automated Docker and Supabase setup for immediate development:

```bash
# Complete project initialization (first time setup)
./scripts/init-project.sh

# Start the complete development environment
npm run dev:full
```

This will:
- ✅ Start Docker if needed
- ✅ Stop any conflicting Supabase projects
- ✅ Start the IEPA Supabase environment with all migrations
- ✅ Start Next.js development server on port 6969

**Development URLs:**
- 🚀 **App**: http://localhost:6969
- 📊 **Supabase Studio**: http://127.0.0.1:54323
- 🔗 **API**: http://127.0.0.1:54321
- 📧 **Email Testing**: http://127.0.0.1:54324

### Manual Setup (Alternative)

If you prefer manual setup or need remote Supabase:

1. Copy the environment template:
   ```bash
   cp .env.example .env.local
   ```

2. Configure your environment variables in `.env.local`:
   ```env
   # Database (for remote Supabase)
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # Payment Processing
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
   ```

3. Install dependencies and start:
   ```bash
   npm install
   npm run dev
   ```

## Stripe MCP Integration

This project includes Stripe MCP (Model Context Protocol) integration for AI assistant interactions with Stripe APIs.

### Quick Test

Test your Stripe MCP setup:

```bash
npm run stripe:test
```

### Start MCP Server

Option 1 - Custom server (recommended):

```bash
npm run stripe:mcp
```

Option 2 - Using npx:

```bash
npm run stripe:mcp:npx
```

For detailed setup instructions, see [STRIPE_MCP_SETUP.md](./STRIPE_MCP_SETUP.md).

## Available Scripts

### Development
- `npm run dev:full` - **Complete setup**: Start Supabase + Next.js (recommended)
- `npm run dev` - Start Next.js development server (port 6969)
- `npm run env:check` - Check development environment status

### Supabase Management
- `npm run supabase:setup` - Setup and start local Supabase environment
- `npm run supabase:start` - Start Supabase
- `npm run supabase:stop` - Stop Supabase
- `npm run supabase:restart` - Restart Supabase
- `npm run supabase:status` - Check Supabase status
- `npm run supabase:studio` - Open Supabase Studio

### Build & Quality
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run check` - Run all quality checks (lint, format, type check)

### Stripe MCP
- `npm run stripe:test` - Test Stripe MCP setup
- `npm run stripe:mcp` - Start custom Stripe MCP server
- `npm run stripe:mcp:npx` - Start Stripe MCP server via npx

### Testing
- `npm run test:e2e` - Run end-to-end tests
- `npm run test:e2e:local` - Run E2E tests against local server
- `npm run test:e2e:production` - Run E2E tests against production

## Project Structure

```
src/
├── app/                 # Next.js app router pages
├── components/          # Reusable UI components
├── lib/                # Utility libraries and configurations
├── services/           # API services and external integrations
├── types/              # TypeScript type definitions
└── utils/              # Helper functions

scripts/
├── stripe-mcp-server.ts    # Custom Stripe MCP server
└── test-stripe-mcp.ts      # Stripe MCP test script
```

## Documentation

### User Documentation
- **[Complete User Guide](./docs/IEPA_Conference_Registration_User_Documentation.md)** - Comprehensive user documentation with screenshots
- **[Executive Summary](./docs/IEPA_System_Executive_Summary.md)** - Business overview and ROI analysis
- **[Technical Troubleshooting](./docs/IEPA_Technical_Troubleshooting_Guide.md)** - Admin and developer troubleshooting guide
- **[Documentation Index](./docs/README.md)** - Complete documentation directory

### Setup Guides
- [Docker & Supabase Setup](./.docs/01-setup-config/docker-supabase-setup.md) - **Automated development environment**
- [Supabase Setup](./SUPABASE_SETUP.md) - Manual database configuration
- [Stripe MCP Setup](./STRIPE_MCP_SETUP.md) - AI assistant integration
- [Magic Link Authentication](./docs/MAGIC_LINK_AUTHENTICATION.md) - Authentication implementation details

### External Documentation
- [Next.js Documentation](https://nextjs.org/docs) - Framework documentation
- [Stripe Documentation](https://docs.stripe.com) - Payment processing
- [Supabase Documentation](https://supabase.com/docs) - Database and auth

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Payments**: Stripe
- **AI Integration**: Stripe MCP (Model Context Protocol)
- **Deployment**: Vercel (recommended)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run quality checks: `npm run check`
5. Submit a pull request

## License

This project is proprietary software for the Independent Energy Producers Association (IEPA).
