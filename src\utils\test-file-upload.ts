// Test utility for file upload functionality
import { supabase } from '@/lib/supabase';

export interface FileUploadTestResult {
  success: boolean;
  message: string;
  url?: string;
  error?: string;
}

export async function testFileUpload(): Promise<FileUploadTestResult> {
  try {
    // Create a test file
    const testContent =
      'This is a test file for IEPA speaker presentation upload.';
    const blob = new Blob([testContent], { type: 'text/plain' });
    const testFile = new File([blob], 'test-presentation.txt', {
      type: 'text/plain',
    });

    // Generate unique filename
    const timestamp = Date.now();
    const fileName = `test-${timestamp}.txt`;
    const filePath = `speaker-presentations/${fileName}`;

    console.log('Testing file upload to:', filePath);

    // Upload to Supabase
    const { data, error } = await supabase.storage
      .from('iepa-presentations')
      .upload(filePath, testFile, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) {
      throw error;
    }

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from('iepa-presentations').getPublicUrl(data.path);

    console.log('File uploaded successfully:', publicUrl);

    return {
      success: true,
      message: 'File upload test successful',
      url: publicUrl,
    };
  } catch (error) {
    console.error('File upload test failed:', error);
    return {
      success: false,
      message: 'File upload test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function testStorageBuckets(): Promise<{
  bucketsExist: boolean;
  buckets: string[];
  error?: string;
}> {
  try {
    const { data, error } = await supabase.storage.listBuckets();

    if (error) {
      throw error;
    }

    const bucketNames = data.map(bucket => bucket.name);
    const requiredBuckets = ['iepa-presentations', 'iepa-sponsor-assets'];
    const bucketsExist = requiredBuckets.every(bucket =>
      bucketNames.includes(bucket)
    );

    return {
      bucketsExist,
      buckets: bucketNames,
    };
  } catch (error) {
    return {
      bucketsExist: false,
      buckets: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function testDatabaseConnection(): Promise<{
  connected: boolean;
  tableExists: boolean;
  error?: string;
}> {
  try {
    // Test basic connection
    const { error } = await supabase
      .from('iepa_speaker_registrations')
      .select('count(*)', { count: 'exact', head: true });

    if (error) {
      throw error;
    }

    return {
      connected: true,
      tableExists: true,
    };
  } catch (error) {
    return {
      connected: false,
      tableExists: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Comprehensive test function
export async function runAllTests(): Promise<{
  fileUpload: FileUploadTestResult;
  storage: Awaited<ReturnType<typeof testStorageBuckets>>;
  database: Awaited<ReturnType<typeof testDatabaseConnection>>;
}> {
  console.log('Running comprehensive file upload tests...');

  const [fileUploadResult, storageResult, databaseResult] = await Promise.all([
    testFileUpload(),
    testStorageBuckets(),
    testDatabaseConnection(),
  ]);

  console.log('Test Results:', {
    fileUpload: fileUploadResult,
    storage: storageResult,
    database: databaseResult,
  });

  return {
    fileUpload: fileUploadResult,
    storage: storageResult,
    database: databaseResult,
  };
}

// Make tests available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as unknown as Record<string, unknown>).testFileUpload = {
    testFileUpload,
    testStorageBuckets,
    testDatabaseConnection,
    runAllTests,
  };
}
