import { useState, useCallback } from 'react';

interface UseSubmitButtonOptions {
  onSubmit: () => Promise<void> | void;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  resetAfterMs?: number;
}

interface UseSubmitButtonReturn {
  isSubmitting: boolean;
  isSuccess: boolean;
  isError: boolean;
  error: Error | null;
  handleSubmit: () => Promise<void>;
  reset: () => void;
}

/**
 * Hook for managing submit button state with loading, success, and error states
 * Provides comprehensive submit button state management with accessibility compliance
 */
export function useSubmitButton({
  onSubmit,
  onSuccess,
  onError,
  resetAfterMs = 3000,
}: UseSubmitButtonOptions): UseSubmitButtonReturn {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const reset = useCallback(() => {
    setIsSubmitting(false);
    setIsSuccess(false);
    setIsError(false);
    setError(null);
  }, []);

  const handleSubmit = useCallback(async () => {
    if (isSubmitting) return; // Prevent double submission

    try {
      setIsSubmitting(true);
      setIsError(false);
      setError(null);

      await onSubmit();

      setIsSuccess(true);
      onSuccess?.();

      // Auto-reset after specified time
      if (resetAfterMs > 0) {
        setTimeout(() => {
          setIsSuccess(false);
          setIsSubmitting(false);
        }, resetAfterMs);
      } else {
        setIsSubmitting(false);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An error occurred');
      setError(error);
      setIsError(true);
      setIsSubmitting(false);
      onError?.(error);
    }
  }, [isSubmitting, onSubmit, onSuccess, onError, resetAfterMs]);

  return {
    isSubmitting,
    isSuccess,
    isError,
    error,
    handleSubmit,
    reset,
  };
}

interface UseAdminSubmitButtonOptions {
  onSubmit: () => Promise<void> | void;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  resetAfterMs?: number;
  requireConfirmation?: boolean;
  confirmationMessage?: string;
}

interface UseAdminSubmitButtonReturn extends UseSubmitButtonReturn {
  handleSubmitWithConfirmation: () => Promise<void>;
}

/**
 * Enhanced submit button hook for admin forms with confirmation dialogs
 */
export function useAdminSubmitButton({
  onSubmit,
  onSuccess,
  onError,
  resetAfterMs = 3000,
  requireConfirmation = false,
  confirmationMessage = 'Are you sure you want to proceed?',
}: UseAdminSubmitButtonOptions): UseAdminSubmitButtonReturn {
  const baseHook = useSubmitButton({
    onSubmit,
    onSuccess,
    onError,
    resetAfterMs,
  });

  const handleSubmitWithConfirmation = useCallback(async () => {
    if (requireConfirmation) {
      const confirmed = window.confirm(confirmationMessage);
      if (!confirmed) return;
    }

    await baseHook.handleSubmit();
  }, [requireConfirmation, confirmationMessage, baseHook.handleSubmit]);

  return {
    ...baseHook,
    handleSubmitWithConfirmation,
  };
}

// Default export for backward compatibility
export default useSubmitButton;
