'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@/components/ui';
import { stripeUtils, STRIPE_TEST_CARDS } from '@/lib/stripe-client';
import {
  FaCheckCircle,
  FaExclamationTriangle,
  FaCreditCard,
  FaSpinner,
} from 'react-icons/fa';
import { showError } from '@/utils/notifications';

interface StripeConfigTest {
  success: boolean;
  timestamp: string;
  configuration: {
    isValid: boolean;
    errors: string[];
    status: {
      hasPublishableKey: boolean;
      hasWebhookSecret: boolean;
      environment: string;
      isTestMode: boolean;
      currency: string;
      paymentMethodTypes: string[];
    };
  };
  connection: {
    success: boolean;
    error?: string;
  };
  recommendations: string[];
}

export default function StripeTestPage() {
  const [configTest, setConfigTest] = useState<StripeConfigTest | null>(null);
  const [loading, setLoading] = useState(true);
  const [testingPayment, setTestingPayment] = useState(false);

  useEffect(() => {
    testStripeConfiguration();
  }, []);

  const testStripeConfiguration = async () => {
    try {
      const response = await fetch('/api/stripe/test-config');
      const data = await response.json();
      setConfigTest(data);
    } catch (error) {
      console.error('Error testing Stripe configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  const testPaymentFlow = async () => {
    setTestingPayment(true);

    try {
      const result = await stripeUtils.processPayment({
        registrationId: 'test-registration-' + Date.now(),
        registrationType: 'attendee',
        customerEmail: '<EMAIL>',
        customerName: 'Test User',
        totalAmount: 100, // $100 test amount
        lineItems: [
          {
            name: 'Test IEPA Conference Registration',
            description: 'Test registration for Stripe integration',
            price: 100,
            quantity: 1,
          },
        ],
        metadata: {
          test: 'true',
          source: 'stripe-test-page',
        },
      });

      if (!result.success) {
        showError('Payment Test Failed', `Error: ${result.error}`);
      }
      // If successful, user will be redirected to Stripe checkout
    } catch (error) {
      showError('Payment Test Error', `Error: ${error}`);
    } finally {
      setTestingPayment(false);
    }
  };

  const clientValidation = stripeUtils.validateConfig();

  if (loading) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-4xl mx-auto text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="iepa-body">Testing Stripe configuration...</p>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="iepa-heading-1 mb-4">Stripe Configuration Test</h1>
            <p className="iepa-body text-gray-600">
              Test and verify Stripe integration for IEPA conference payments
            </p>
          </div>

          {/* Client-side Validation */}
          <Card className="mb-6">
            <CardHeader>
              <h2 className="iepa-heading-2 flex items-center gap-2">
                {clientValidation.isValid ? (
                  <FaCheckCircle className="text-green-600" />
                ) : (
                  <FaExclamationTriangle className="text-red-600" />
                )}
                Client-side Configuration
              </h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="iepa-body-small font-semibold">Status</p>
                    <p
                      className={`iepa-body ${clientValidation.isValid ? 'text-green-600' : 'text-red-600'}`}
                    >
                      {clientValidation.isValid ? 'Valid' : 'Invalid'}
                    </p>
                  </div>
                  <div>
                    <p className="iepa-body-small font-semibold">Mode</p>
                    <p className="iepa-body">
                      {clientValidation.isTestMode
                        ? 'Test Mode'
                        : 'Production Mode'}
                    </p>
                  </div>
                </div>

                {clientValidation.errors.length > 0 && (
                  <div className="mt-4">
                    <p className="iepa-body-small font-semibold text-red-600">
                      Errors:
                    </p>
                    <ul className="list-disc list-inside space-y-1">
                      {clientValidation.errors.map(
                        (error: string, index: number) => (
                          <li
                            key={index}
                            className="iepa-body-small text-red-600"
                          >
                            {error}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>

          {/* Server-side Test Results */}
          {configTest && (
            <Card className="mb-6">
              <CardHeader>
                <h2 className="iepa-heading-2 flex items-center gap-2">
                  {configTest.success ? (
                    <FaCheckCircle className="text-green-600" />
                  ) : (
                    <FaExclamationTriangle className="text-red-600" />
                  )}
                  Server-side Configuration
                </h2>
              </CardHeader>
              <CardBody>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="iepa-body-small font-semibold">
                        Overall Status
                      </p>
                      <p
                        className={`iepa-body ${configTest.success ? 'text-green-600' : 'text-red-600'}`}
                      >
                        {configTest.success ? 'Healthy' : 'Issues Found'}
                      </p>
                    </div>
                    <div>
                      <p className="iepa-body-small font-semibold">
                        Environment
                      </p>
                      <p className="iepa-body">
                        {configTest.configuration.status.environment}
                      </p>
                    </div>
                    <div>
                      <p className="iepa-body-small font-semibold">
                        API Connection
                      </p>
                      <p
                        className={`iepa-body ${configTest.connection.success ? 'text-green-600' : 'text-red-600'}`}
                      >
                        {configTest.connection.success ? 'Connected' : 'Failed'}
                      </p>
                    </div>
                  </div>

                  {configTest.configuration.errors.length > 0 && (
                    <div>
                      <p className="iepa-body-small font-semibold text-red-600">
                        Configuration Errors:
                      </p>
                      <ul className="list-disc list-inside space-y-1">
                        {configTest.configuration.errors.map((error, index) => (
                          <li
                            key={index}
                            className="iepa-body-small text-red-600"
                          >
                            {error}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {configTest.recommendations.length > 0 && (
                    <div>
                      <p className="iepa-body-small font-semibold text-blue-600">
                        Recommendations:
                      </p>
                      <ul className="list-disc list-inside space-y-1">
                        {configTest.recommendations.map((rec, index) => (
                          <li
                            key={index}
                            className="iepa-body-small text-blue-600"
                          >
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </CardBody>
            </Card>
          )}

          {/* Test Payment Flow */}
          <Card className="mb-6">
            <CardHeader>
              <h2 className="iepa-heading-2 flex items-center gap-2">
                <FaCreditCard className="text-blue-600" />
                Test Payment Flow
              </h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <p className="iepa-body">
                  Test the complete payment flow with a $100 test transaction.
                  This will redirect you to Stripe&apos;s test checkout page.
                </p>

                <div className="flex justify-center">
                  <Button
                    onClick={testPaymentFlow}
                    disabled={testingPayment || !configTest?.success}
                    color="primary"
                    size="lg"
                    className="flex items-center gap-2"
                  >
                    {testingPayment ? (
                      <FaSpinner className="animate-spin" />
                    ) : (
                      <FaCreditCard />
                    )}
                    {testingPayment
                      ? 'Creating Test Payment...'
                      : 'Test Payment Flow ($100)'}
                  </Button>
                </div>

                {!configTest?.success && (
                  <p className="iepa-body-small text-red-600 text-center">
                    Fix configuration issues before testing payment flow
                  </p>
                )}
              </div>
            </CardBody>
          </Card>

          {/* Test Cards Information */}
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2">Test Card Numbers</h2>
            </CardHeader>
            <CardBody>
              <p className="iepa-body mb-4">
                Use these test card numbers when testing payments:
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="iepa-body-small font-semibold">
                    Successful Payments
                  </p>
                  <ul className="space-y-1">
                    <li className="iepa-body-small font-mono">
                      {STRIPE_TEST_CARDS.visa} (Visa)
                    </li>
                    <li className="iepa-body-small font-mono">
                      {STRIPE_TEST_CARDS.mastercard} (Mastercard)
                    </li>
                    <li className="iepa-body-small font-mono">
                      {STRIPE_TEST_CARDS.amex} (Amex)
                    </li>
                  </ul>
                </div>

                <div>
                  <p className="iepa-body-small font-semibold">
                    Test Scenarios
                  </p>
                  <ul className="space-y-1">
                    <li className="iepa-body-small font-mono">
                      {STRIPE_TEST_CARDS.declined} (Declined)
                    </li>
                    <li className="iepa-body-small font-mono">
                      {STRIPE_TEST_CARDS.insufficientFunds} (Insufficient funds)
                    </li>
                    <li className="iepa-body-small font-mono">
                      {STRIPE_TEST_CARDS.expired} (Expired)
                    </li>
                  </ul>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="iepa-body-small text-blue-700">
                  <strong>For all test cards:</strong> Use any future expiry
                  date (e.g., 12/25), any 3-digit CVC (4 digits for Amex), and
                  any valid ZIP code.
                </p>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>
    </div>
  );
}
