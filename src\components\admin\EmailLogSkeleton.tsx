'use client';

import React from 'react';
import { Card, CardBody, Skeleton } from '@/components/ui';

interface EmailLogSkeletonProps {
  count?: number;
  className?: string;
}

function EmailLogCardSkeleton() {
  return (
    <Card className="hover:bg-gray-50 transition-colors">
      <CardBody className="p-4">
        {/* Header Row */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 sm:gap-0 mb-3">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Skeleton className="w-4 h-4 rounded-full" />
            <Skeleton className="h-4 flex-1 max-w-xs" />
          </div>
          <div className="flex items-center gap-2 flex-shrink-0 flex-wrap">
            <Skeleton className="h-5 w-12 rounded-full" />
            <Skeleton className="h-5 w-16 rounded-full" />
          </div>
        </div>

        {/* Email Details */}
        <div className="space-y-2 text-sm">
          {/* Recipient */}
          <div className="flex items-center gap-2">
            <Skeleton className="w-3 h-3 rounded-full" />
            <Skeleton className="h-3 w-8" />
            <Skeleton className="h-3 flex-1 max-w-sm" />
          </div>

          {/* Sender */}
          <div className="flex items-center gap-2">
            <Skeleton className="w-3 h-3 rounded-full" />
            <Skeleton className="h-3 w-10" />
            <Skeleton className="h-3 flex-1 max-w-xs" />
          </div>

          {/* Content Preview */}
          <div className="bg-gray-50 p-2 rounded border">
            <div className="flex items-start gap-2">
              <Skeleton className="h-3 w-12" />
              <div className="flex-1 space-y-1">
                <Skeleton className="h-3 w-full" />
                <Skeleton className="h-3 w-3/4" />
              </div>
            </div>
          </div>
        </div>

        {/* Footer Row */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-2 mt-3 pt-3 border-t border-gray-100">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-1">
              <Skeleton className="w-3 h-3 rounded-full" />
              <Skeleton className="h-3 w-24" />
            </div>
            <Skeleton className="h-4 w-20 rounded-full self-start" />
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            <div className="flex items-center gap-1">
              <Skeleton className="h-4 w-16 rounded-full" />
              <Skeleton className="h-6 w-6 sm:w-8 rounded" />
            </div>
            <div className="flex items-center gap-1">
              <Skeleton className="h-6 w-6 sm:w-8 rounded" />
              <Skeleton className="h-6 w-6 sm:w-8 rounded" />
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}

export default function EmailLogSkeleton({ 
  count = 5, 
  className = '' 
}: EmailLogSkeletonProps) {
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: count }, (_, index) => (
        <EmailLogCardSkeleton key={index} />
      ))}
    </div>
  );
}

// Export individual skeleton for reuse
export { EmailLogCardSkeleton };
