'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardBody, CardHeader, Button, Input } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import { useAdminAccess } from '@/hooks/useAdminAccess';
// import { useAdminSubmitButton } from '@/hooks/useSubmitButton'; // TODO: Use when component is created

interface EmailConfigFormData {
  sender_email: string;
  support_email: string;
  noreply_email: string;
  from_name: string;
  reply_to_email: string;
  test_bcc_email: string;
}

interface EmailConfigRecord {
  id: string;
  config_key: string;
  config_value: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface EmailConfigLogEntry {
  id: string;
  config_key: string;
  old_value: string | null;
  new_value: string;
  changed_by: string;
  change_reason: string;
  created_at: string;
}

export default function EmailConfigPage() {
  const { user } = useAuth();
  const { isAdmin, isLoading: adminLoading } = useAdminAccess();
  const [, setConfigs] = useState<EmailConfigRecord[]>([]);
  const [formData, setFormData] = useState<EmailConfigFormData>({
    sender_email: '',
    support_email: '',
    noreply_email: '',
    from_name: '',
    reply_to_email: '',
    test_bcc_email: '',
  });
  const [history, setHistory] = useState<EmailConfigLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [testing, setTesting] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [showHistory, setShowHistory] = useState(false);
  const [message, setMessage] = useState<{
    type: 'success' | 'error';
    text: string;
  } | null>(null);

  // Submit button state management
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isAdmin && !adminLoading) {
      loadConfigurations();
      loadHistory();
    }
  }, [isAdmin, adminLoading, loadConfigurations]);

  const loadConfigurations = useCallback(async () => {
    try {
      // Try the new API first
      let response = await fetch('/api/admin/email-config');
      let result = await response.json();

      if (result.success && result.configurations) {
        setConfigs(result.configurations);

        // Populate form with current values
        const configMap: Partial<EmailConfigFormData> = {};
        result.configurations.forEach((config: EmailConfigRecord) => {
          if (config.config_key in formData) {
            configMap[config.config_key as keyof EmailConfigFormData] =
              config.config_value;
          }
        });
        setFormData(prev => ({ ...prev, ...configMap }));
      } else {
        // Fallback to setup API
        response = await fetch('/api/admin/setup-email-config');
        result = await response.json();

        if (result.setup && result.configurations) {
          setConfigs(result.configurations);

          const configMap: Partial<EmailConfigFormData> = {};
          result.configurations.forEach((config: EmailConfigRecord) => {
            if (config.config_key in formData) {
              configMap[config.config_key as keyof EmailConfigFormData] =
                config.config_value;
            }
          });
          setFormData(prev => ({ ...prev, ...configMap }));
        } else {
          setMessage({ type: 'error', text: 'Email configuration not set up' });
        }
      }
    } catch (error) {
      console.error('Failed to load configurations:', error);
      setMessage({
        type: 'error',
        text: 'Failed to load email configurations',
      });
    } finally {
      setLoading(false);
    }
  }, [formData]);

  const loadHistory = async () => {
    try {
      // For now, just set empty history since we don't have the API endpoint yet
      setHistory([]);
    } catch (error) {
      console.error('Failed to load history:', error);
    }
  };

  const handleInputChange = (key: keyof EmailConfigFormData, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = async () => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);

      if (!user?.email) {
        throw new Error('User not authenticated');
      }

      const response = await fetch('/api/admin/email-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          configurations: formData,
          changedBy: user.email,
          changeReason: 'Updated via admin interface',
        }),
      });

      const result = await response.json();

      if (result.success) {
        setMessage({
          type: 'success',
          text: result.message || 'Email configuration updated successfully',
        });
        await loadConfigurations();
        await loadHistory();
      } else {
        throw new Error(result.error || 'Failed to save configuration');
      }
    } catch (error: unknown) {
      setMessage({
        type: 'error',
        text: (error as Error).message || 'Failed to save configuration',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTestEmail = async () => {
    if (!testEmail.trim()) {
      setMessage({ type: 'error', text: 'Please enter a test email address' });
      return;
    }

    setTesting(true);
    try {
      const response = await fetch('/api/test-email-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ testEmail: testEmail.trim() }),
      });

      const result = await response.json();

      if (result.success && result.emailSent) {
        setMessage({
          type: 'success',
          text: `Test email sent successfully to ${testEmail}`,
        });
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'Test email failed to send',
        });
      }
    } catch (error: unknown) {
      console.error('Test email failed:', error);
      setMessage({
        type: 'error',
        text: (error as Error).message || 'Test email failed',
      });
    } finally {
      setTesting(false);
    }
  };

  const handleRefreshConfig = async () => {
    try {
      // Call the cache refresh API
      const response = await fetch('/api/admin/email-config', {
        method: 'PUT',
      });

      const result = await response.json();

      if (result.success) {
        await loadConfigurations();
        setMessage({
          type: 'success',
          text: 'Email configuration cache refreshed successfully',
        });
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'Failed to refresh configuration',
        });
      }
    } catch (error: unknown) {
      console.error('Failed to refresh config:', error);
      setMessage({
        type: 'error',
        text: (error as Error).message || 'Failed to refresh configuration',
      });
    }
  };

  if (adminLoading || loading) {
    return (
      <div className="iepa-container">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-iepa-blue mx-auto mb-4"></div>
          <p className="iepa-body">Loading email configuration...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="iepa-container">
        <div className="text-center py-12">
          <h1 className="iepa-heading-1 text-red-600 mb-4">Access Denied</h1>
          <p className="iepa-body">
            You must be an administrator to access this page.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      {/* Header */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          {/* Message Display */}
          {message && (
            <div
              className={`mb-6 p-4 rounded-lg ${
                message.type === 'success'
                  ? 'bg-green-100 text-green-800 border border-green-200'
                  : 'bg-red-100 text-red-800 border border-red-200'
              }`}
            >
              <div className="flex justify-between items-center">
                <span>{message.text}</span>
                <button
                  onClick={() => setMessage(null)}
                  className="ml-4 text-lg font-bold hover:opacity-70"
                >
                  ×
                </button>
              </div>
            </div>
          )}

          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="iepa-heading-1 mb-2">Email Configuration</h1>
              <p className="iepa-body text-gray-600">
                Manage global email settings for the IEPA conference
                registration system
              </p>
            </div>
            <div className="flex gap-3">
              <Button onClick={handleRefreshConfig} variant="outline" size="sm">
                🔄 Refresh Cache
              </Button>
              <Button
                onClick={() => setShowHistory(!showHistory)}
                variant="outline"
                size="sm"
              >
                📋 {showHistory ? 'Hide' : 'Show'} History
              </Button>
            </div>
          </div>

          {/* Configuration Form */}
          <Card className="mb-6">
            <CardHeader>
              <h2 className="iepa-heading-2">Email Settings</h2>
              <p className="iepa-body-small text-gray-600">
                Configure email addresses and display names for the system
              </p>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Sender Email *
                  </label>
                  <Input
                    type="email"
                    value={formData.sender_email}
                    onChange={e =>
                      handleInputChange('sender_email', e.target.value)
                    }
                    placeholder="<EMAIL>"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Primary email address for outgoing emails
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Support Email *
                  </label>
                  <Input
                    type="email"
                    value={formData.support_email}
                    onChange={e =>
                      handleInputChange('support_email', e.target.value)
                    }
                    placeholder="<EMAIL>"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Email for customer support and replies
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    No-Reply Email *
                  </label>
                  <Input
                    type="email"
                    value={formData.noreply_email}
                    onChange={e =>
                      handleInputChange('noreply_email', e.target.value)
                    }
                    placeholder="<EMAIL>"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Email for automated notifications
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    From Name *
                  </label>
                  <Input
                    type="text"
                    value={formData.from_name}
                    onChange={e =>
                      handleInputChange('from_name', e.target.value)
                    }
                    placeholder="IEPA Conference 2025"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Display name in email &quot;From&quot; field
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reply-To Email
                  </label>
                  <Input
                    type="email"
                    value={formData.reply_to_email}
                    onChange={e =>
                      handleInputChange('reply_to_email', e.target.value)
                    }
                    placeholder="<EMAIL>"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Email address for replies
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Test BCC Email
                  </label>
                  <Input
                    type="email"
                    value={formData.test_bcc_email}
                    onChange={e =>
                      handleInputChange('test_bcc_email', e.target.value)
                    }
                    placeholder="<EMAIL>"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    BCC for development testing (optional)
                  </p>
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <Button
                  onClick={handleSave}
                  disabled={isSubmitting}
                  className="bg-white border-2 border-iepa-blue text-iepa-blue hover:bg-iepa-blue hover:text-white px-6 py-2 rounded-lg font-medium transition-colors"
                  data-testid="save-email-config-button"
                >
                  {isSubmitting
                    ? 'Saving Configuration...'
                    : '💾 Save Configuration'}
                </Button>
              </div>
            </CardBody>
          </Card>

          {/* Test Email */}
          <Card className="mb-6">
            <CardHeader>
              <h2 className="iepa-heading-2">Test Email Configuration</h2>
              <p className="iepa-body-small text-gray-600">
                Send a test email to verify the current configuration
              </p>
            </CardHeader>
            <CardBody>
              <div className="flex gap-3">
                <Input
                  type="email"
                  value={testEmail}
                  onChange={e => setTestEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="flex-1"
                />
                <Button
                  onClick={handleTestEmail}
                  disabled={testing || !testEmail.trim()}
                  variant="outline"
                >
                  {testing ? 'Sending...' : 'Send Test Email'}
                </Button>
              </div>
            </CardBody>
          </Card>

          {/* Configuration History */}
          {showHistory && (
            <Card>
              <CardHeader>
                <h2 className="iepa-heading-2">Configuration History</h2>
                <p className="iepa-body-small text-gray-600">
                  Recent changes to email configuration settings
                </p>
              </CardHeader>
              <CardBody>
                {history.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">
                    No configuration changes found
                  </p>
                ) : (
                  <div className="space-y-3">
                    {history.slice(0, 10).map(entry => (
                      <div
                        key={entry.id}
                        className="border-l-4 border-iepa-blue pl-4 py-2"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-sm">
                              {entry.config_key}: {entry.old_value || 'null'} →{' '}
                              {entry.new_value}
                            </p>
                            <p className="text-xs text-gray-500">
                              Changed by {entry.changed_by} •{' '}
                              {entry.change_reason}
                            </p>
                          </div>
                          <span className="text-xs text-gray-400">
                            {new Date(entry.created_at).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardBody>
            </Card>
          )}
        </div>
      </section>
    </div>
  );
}
