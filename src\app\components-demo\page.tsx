'use client';

import { useState } from 'react';
import {
  Button,
  Input,
  Card,
  CardBody,
  CardHeader,
  Textarea,
  Checkbox,
  RadioGroupWithOptions,
  Divider,
  Spacer,
  Chip,
  Badge,
  Progress,
  Spinner,
} from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { dateUtils } from '@/lib/conference-config';
import { pricingUtils } from '@/lib/pricing-config';

export default function ComponentsDemo() {
  const [inputValue, setInputValue] = useState('');
  const [selectValue, setSelectValue] = useState('');
  const [textareaValue, setTextareaValue] = useState('');
  const [checkboxValue, setCheckboxValue] = useState(false);
  const [checkboxGroupValue, setCheckboxGroupValue] = useState<string[]>([]);
  const [radioValue, setRadioValue] = useState('');

  const selectOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4 (Disabled)', disabled: true },
  ];

  // Import meal options from centralized configuration
  const { getMealOptions } = dateUtils;
  const mealOptions = getMealOptions();

  const checkboxOptions = [
    { value: mealOptions[1].id, label: mealOptions[1].label },
    { value: mealOptions[2].id, label: mealOptions[2].label },
    { value: mealOptions[3].id, label: mealOptions[3].label },
  ];

  // Import registration options from centralized pricing configuration
  const registrationOptions = pricingUtils.getRegistrationOptions();

  const radioOptions = [
    {
      value: 'iepa-member',
      label: 'IEPA Member',
      description: `${registrationOptions.find(r => r.id === 'iepa-member')?.formattedPrice} registration fee`,
    },
    {
      value: 'non-iepa-member',
      label: 'Non-IEPA Member',
      description: `${registrationOptions.find(r => r.id === 'non-iepa-member')?.formattedPrice} registration fee`,
    },
    {
      value: 'fed-state-government',
      label: 'Fed/State Government',
      description: `${registrationOptions.find(r => r.id === 'fed-state-government')?.formattedPrice} registration fee`,
    },
    {
      value: 'cca',
      label: 'CCA Member',
      description: `${registrationOptions.find(r => r.id === 'cca')?.formattedPrice} registration fee`,
    },
  ];

  return (
    <div className="iepa-container iepa-section">
      {/* Skip to content link for accessibility */}
      <a href="#main-content" className="iepa-skip-link">
        Skip to main content
      </a>

      <main id="main-content">
        <div className="iepa-form-section">
          <h1 className="iepa-heading-1 mb-8">
            IEPA 2025 UI Component Library Demo
          </h1>
          <p className="iepa-body-large mb-8">
            This page showcases all the UI components used in the IEPA 2025
            Conference Registration Application. All components are built with
            Hero UI and follow WCAG 2.2 AA accessibility standards.
          </p>

          {/* Buttons Section */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="iepa-heading-2">Buttons</h2>
            </CardHeader>
            <CardBody>
              <div className="iepa-form-grid">
                <div className="space-y-4">
                  <h3 className="iepa-heading-3">Variants</h3>
                  <div className="iepa-button-group">
                    <Button color="primary" variant="solid">
                      Primary Solid
                    </Button>
                    <Button color="primary" variant="bordered">
                      Primary Bordered
                    </Button>
                    <Button color="primary" variant="light">
                      Primary Light
                    </Button>
                    <Button color="primary" variant="flat">
                      Primary Flat
                    </Button>
                  </div>
                </div>
                <div className="space-y-4">
                  <h3 className="iepa-heading-3">Sizes</h3>
                  <div className="iepa-button-group">
                    <Button size="sm">Small</Button>
                    <Button size="md">Medium</Button>
                    <Button size="lg">Large</Button>
                  </div>
                </div>
              </div>
              <Divider className="my-6" />
              <div className="space-y-4">
                <h3 className="iepa-heading-3">Colors</h3>
                <div className="iepa-button-group">
                  <Button color="default">Default</Button>
                  <Button color="primary">Primary</Button>
                  <Button color="secondary">Secondary</Button>
                  <Button color="success">Success</Button>
                  <Button color="warning">Warning</Button>
                  <Button color="danger">Danger</Button>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Form Inputs Section */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="iepa-heading-2">Form Inputs</h2>
            </CardHeader>
            <CardBody>
              <div className="iepa-form-grid">
                <div className="iepa-form-field">
                  <Input
                    label="Full Name"
                    placeholder="Enter your full name"
                    value={inputValue}
                    onChange={e => setInputValue(e.target.value)}
                    description="This will appear on your conference badge"
                    isRequired
                  />
                </div>
                <div className="iepa-form-field">
                  <Input
                    label="Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    variant="bordered"
                    isRequired
                  />
                </div>
                <div className="iepa-form-field">
                  <label className="text-sm font-medium text-[var(--iepa-gray-700)] mb-2 block">
                    Registration Type *
                  </label>
                  <Select value={selectValue} onValueChange={setSelectValue}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your registration type" />
                    </SelectTrigger>
                    <SelectContent>
                      {selectOptions.map(option => (
                        <SelectItem
                          key={option.value}
                          value={option.value}
                          disabled={option.disabled}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="iepa-form-field">
                  <Input
                    label="Phone Number"
                    type="tel"
                    placeholder="(*************"
                    variant="underlined"
                  />
                </div>
                <div className="iepa-form-field-full">
                  <label className="text-sm font-medium text-[var(--iepa-gray-700)] mb-2 block">
                    Special Dietary Needs
                  </label>
                  <Textarea
                    placeholder="Please describe any dietary restrictions or allergies"
                    value={textareaValue}
                    onChange={e => setTextareaValue(e.target.value)}
                    rows={3}
                  />
                  <p className="text-sm text-[var(--iepa-gray-600)] mt-1">
                    Put &apos;none&apos; if not applicable
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Selection Controls Section */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="iepa-heading-2">Selection Controls</h2>
            </CardHeader>
            <CardBody>
              <div className="iepa-form-grid">
                <div className="space-y-6">
                  <div>
                    <h3 className="iepa-heading-3 mb-4">Single Checkbox</h3>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="terms-checkbox"
                        checked={checkboxValue}
                        onCheckedChange={checked =>
                          setCheckboxValue(checked === true)
                        }
                      />
                      <label
                        htmlFor="terms-checkbox"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        I agree to the terms and conditions
                      </label>
                    </div>
                  </div>

                  <div>
                    <h3 className="iepa-heading-3 mb-4">Checkbox Group</h3>
                    <div className="space-y-3">
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-[var(--iepa-gray-700)]">
                          Meal Selections
                        </label>
                        <p className="text-sm text-[var(--iepa-gray-600)]">
                          Select which meals you plan to attend
                        </p>
                      </div>
                      <div className="space-y-2">
                        {checkboxOptions.map(option => (
                          <div
                            key={option.value}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={option.value}
                              checked={checkboxGroupValue.includes(
                                option.value
                              )}
                              onCheckedChange={checked => {
                                if (checked) {
                                  setCheckboxGroupValue([
                                    ...checkboxGroupValue,
                                    option.value,
                                  ]);
                                } else {
                                  setCheckboxGroupValue(
                                    checkboxGroupValue.filter(
                                      v => v !== option.value
                                    )
                                  );
                                }
                              }}
                            />
                            <label
                              htmlFor={option.value}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {option.label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="iepa-heading-3 mb-4">Radio Group</h3>
                  <RadioGroupWithOptions
                    label="Registration Type"
                    description="Choose your registration category"
                    options={radioOptions}
                    value={radioValue}
                    onValueChange={setRadioValue}
                    orientation="vertical"
                    isRequired
                  />
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Status and Feedback Section */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="iepa-heading-2">Status & Feedback</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-6">
                <div>
                  <h3 className="iepa-heading-3 mb-4">Status Messages</h3>
                  <div className="space-y-4">
                    <div className="iepa-status-success">
                      <strong>Success:</strong> Your registration has been
                      submitted successfully!
                    </div>
                    <div className="iepa-status-error">
                      <strong>Error:</strong> Please correct the errors below
                      and try again.
                    </div>
                    <div className="iepa-status-warning">
                      <strong>Warning:</strong> Your session will expire in 5
                      minutes.
                    </div>
                    <div className="iepa-status-info">
                      <strong>Info:</strong> Early bird pricing ends on March
                      1st, 2025.
                    </div>
                  </div>
                </div>

                <Divider />

                <div>
                  <h3 className="iepa-heading-3 mb-4">Progress & Loading</h3>
                  <div className="space-y-4">
                    <div className="max-w-md">
                      <div className="flex justify-between text-sm mb-2">
                        <span>Registration Progress</span>
                        <span>65%</span>
                      </div>
                      <Progress value={65} className="w-full" />
                    </div>
                    <div className="flex items-center gap-4">
                      <Spinner size="sm" />
                      <Spinner size="md" />
                      <Spinner size="lg" />
                    </div>
                  </div>
                </div>

                <Divider />

                <div>
                  <h3 className="iepa-heading-3 mb-4">Chips & Badges</h3>
                  <div className="flex flex-wrap gap-2 mb-4">
                    <Chip color="primary" variant="solid">
                      Attendee
                    </Chip>
                    <Chip color="secondary" variant="bordered">
                      Speaker
                    </Chip>
                    <Chip color="success" variant="flat">
                      Sponsor
                    </Chip>
                    <Chip color="warning" variant="dot">
                      VIP
                    </Chip>
                  </div>
                  <div className="flex gap-4">
                    <Badge content="5" color="danger">
                      <Button variant="bordered">Notifications</Button>
                    </Badge>
                    <Badge content="New" color="success">
                      <Button variant="bordered">Features</Button>
                    </Badge>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Typography Section */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="iepa-heading-2">Typography System</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <h1 className="iepa-heading-1">Heading 1 - Main Page Title</h1>
                <h2 className="iepa-heading-2">Heading 2 - Section Title</h2>
                <h3 className="iepa-heading-3">Heading 3 - Subsection Title</h3>
                <p className="iepa-body-large">
                  Large body text - Used for important descriptions and
                  introductory content.
                </p>
                <p className="iepa-body">
                  Regular body text - The standard text size for most content,
                  form labels, and general information.
                </p>
                <p className="iepa-body-small">
                  Small body text - Used for helper text, captions, and
                  secondary information.
                </p>
              </div>
            </CardBody>
          </Card>

          {/* Responsive Demo */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="iepa-heading-2">Responsive Design Demo</h2>
            </CardHeader>
            <CardBody>
              <p className="iepa-body mb-6">
                Resize your browser window to see how components adapt to
                different screen sizes.
              </p>
              <div className="iepa-form-grid-3">
                <Card className="iepa-card-flat">
                  <CardBody>
                    <h3 className="iepa-heading-3 mb-2">Mobile First</h3>
                    <p className="iepa-body">
                      All components are designed mobile-first and scale up
                      beautifully.
                    </p>
                  </CardBody>
                </Card>
                <Card className="iepa-card-flat">
                  <CardBody>
                    <h3 className="iepa-heading-3 mb-2">Tablet Optimized</h3>
                    <p className="iepa-body">
                      Touch-friendly interfaces with appropriate spacing and
                      sizing.
                    </p>
                  </CardBody>
                </Card>
                <Card className="iepa-card-flat">
                  <CardBody>
                    <h3 className="iepa-heading-3 mb-2">Desktop Enhanced</h3>
                    <p className="iepa-body">
                      Full-featured experience with optimal use of screen real
                      estate.
                    </p>
                  </CardBody>
                </Card>
              </div>
            </CardBody>
          </Card>

          <Spacer y={8} />

          <div className="text-center">
            <p className="iepa-body-small">
              All components follow WCAG 2.2 AA accessibility standards and
              support keyboard navigation, screen readers, and high contrast
              mode.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
