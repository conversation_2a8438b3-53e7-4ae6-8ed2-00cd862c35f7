#!/usr/bin/env tsx

/**
 * IEPA Conference Registration - Safe Production Data Sync
 * 
 * This script safely syncs production data by handling foreign key dependencies
 * and creating missing auth.users records as needed.
 */

import { createClient } from '@supabase/supabase-js';

// Production Supabase credentials
const PRODUCTION_CONFIG = {
  url: 'https://uffhyhpcuedjsisczocy.supabase.co',
  serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVmZmh5aHBjdWVkanNpc2N6b2N5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NDkxNywiZXhwIjoyMDY0MTQwOTE3fQ.bBd3qf8TicnfVhCKjC4aYNbe9xLQI7tEyIjWME43MQA'
};

// Local Supabase credentials
const LOCAL_CONFIG = {
  url: 'http://127.0.0.1:54321',
  serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'
};

// Create Supabase clients
const productionClient = createClient(PRODUCTION_CONFIG.url, PRODUCTION_CONFIG.serviceRoleKey);
const localClient = createClient(LOCAL_CONFIG.url, LOCAL_CONFIG.serviceRoleKey);

const log = (message: string) => console.log(`[INFO] ${message}`);
const success = (message: string) => console.log(`[SUCCESS] ${message}`);
const warn = (message: string) => console.log(`[WARN] ${message}`);
const error = (message: string) => console.log(`[ERROR] ${message}`);

// Create missing auth users
const createMissingUsers = async (): Promise<void> => {
  log('Creating missing auth.users records...');
  
  // Get all unique user_ids from production tables
  const userIds = new Set<string>();
  
  const tables = [
    'iepa_user_profiles',
    'iepa_attendee_registrations', 
    'iepa_speaker_registrations',
    'iepa_sponsor_registrations',
    'iepa_payments'
  ];
  
  for (const table of tables) {
    const { data } = await productionClient
      .from(table)
      .select('user_id')
      .not('user_id', 'is', null);
    
    if (data) {
      data.forEach(row => {
        if (row.user_id) userIds.add(row.user_id);
      });
    }
  }
  
  log(`Found ${userIds.size} unique user IDs`);
  
  // Create auth.users records for each user_id
  let created = 0;
  for (const userId of userIds) {
    try {
      // Check if user already exists
      const { data: existingUser } = await localClient.auth.admin.getUserById(userId);
      
      if (!existingUser.user) {
        // Create a dummy user record
        const { error: createError } = await localClient.auth.admin.createUser({
          email: `user-${userId}@iepa-import.local`,
          password: 'temp-password-123',
          email_confirm: true
        });
        
        if (!createError) {
          created++;
        }
      }
    } catch (err) {
      // User creation failed, but continue
    }
  }
  
  success(`Created ${created} auth.users records`);
};

// Sync data without foreign key constraints
const syncDataSafe = async (): Promise<void> => {
  log('Starting safe data sync...');
  
  // First, create missing users
  await createMissingUsers();
  
  // Sync tables in dependency order, but skip foreign key validation
  const tables = [
    'iepa_user_profiles',
    'iepa_organizations',
    'iepa_historical_registrations',
    'iepa_attendee_registrations',
    'iepa_speaker_registrations', 
    'iepa_sponsor_registrations',
    'iepa_payments'
  ];
  
  for (const table of tables) {
    try {
      log(`Syncing ${table}...`);
      
      // Get production data
      const { data: productionData, error: fetchError } = await productionClient
        .from(table)
        .select('*');
      
      if (fetchError) {
        error(`Failed to fetch ${table}: ${fetchError.message}`);
        continue;
      }
      
      if (!productionData || productionData.length === 0) {
        warn(`No data in ${table}`);
        continue;
      }
      
      log(`Found ${productionData.length} rows in ${table}`);
      
      // Insert data with upsert to handle conflicts
      const { error: insertError } = await localClient
        .from(table)
        .upsert(productionData, { 
          onConflict: 'id',
          ignoreDuplicates: false 
        });
      
      if (insertError) {
        error(`Failed to sync ${table}: ${insertError.message}`);
      } else {
        success(`✅ Synced ${table}: ${productionData.length} rows`);
      }
      
    } catch (err) {
      error(`Error syncing ${table}: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }
};

// Main execution
const main = async () => {
  console.log('🔄 IEPA Safe Production Data Sync');
  console.log('==================================');
  console.log('');
  
  try {
    // Check local Supabase
    const { data } = await localClient.from('iepa_user_profiles').select('count', { count: 'exact', head: true });
    success('Local Supabase is running');
    
    await syncDataSafe();
    
    console.log('');
    success('🎉 Safe sync completed!');
    console.log('');
    console.log('🔗 Check your data:');
    console.log('   Studio: http://127.0.0.1:54323');
    console.log('   API: http://127.0.0.1:54321');
    
  } catch (err) {
    error(`Sync failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
    process.exit(1);
  }
};

if (require.main === module) {
  main();
}
