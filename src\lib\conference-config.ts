// IEPA 2025 Conference Date Configuration
// Centralized configuration for all conference dates, meals, and events

/**
 * Conference Date Configuration
 * Update these dates for the 2025 IEPA Conference
 *
 * NOTE: These are placeholder dates based on previous year's schedule.
 * Update with actual 2025 conference dates when confirmed.
 */

export interface ConferenceDate {
  date: string; // ISO date string (YYYY-MM-DD)
  displayDate: string; // Human-readable date
  dayOfWeek: string;
}

export interface MealEvent {
  id: string;
  name: string;
  date: string; // ISO date string
  time: string; // Time in HH:MM format
  displayName: string; // Full display name with date
  description?: string;
}

export interface ConferenceEvent {
  id: string;
  name: string;
  date: string;
  startTime: string;
  endTime: string;
  location?: string;
  description?: string;
}

// IEPA 2025 Conference Dates
// Updated with confirmed 2025 conference dates from IEPA website
export const CONFERENCE_DATES: {
  startDate: ConferenceDate;
  endDate: ConferenceDate;
  golfTournament: ConferenceDate;
} = {
  startDate: {
    date: '2025-09-15',
    displayDate: 'September 15, 2025',
    dayOfWeek: 'Monday',
  },
  endDate: {
    date: '2025-09-17',
    displayDate: 'September 17, 2025',
    dayOfWeek: 'Wednesday',
  },
  golfTournament: {
    date: '2025-09-15',
    displayDate: 'September 15, 2025',
    dayOfWeek: 'Monday',
  },
};

// Conference Meal Schedule
export const MEAL_SCHEDULE: MealEvent[] = [
  {
    id: 'dinner-day1',
    name: 'Welcome Reception & Dinner',
    date: '2025-09-15',
    time: '18:00',
    displayName: 'Welcome Reception & Dinner - September 15, 2025',
    description: 'Welcome networking reception and dinner',
  },
  {
    id: 'breakfast-day2',
    name: 'Breakfast',
    date: '2025-09-16',
    time: '07:30',
    displayName: 'Breakfast - September 16, 2025',
    description:
      'Continental breakfast before morning sessions (Complimentary)',
  },
  {
    id: 'lunch-day2',
    name: 'Lunch',
    date: '2025-09-16',
    time: '12:00',
    displayName: 'Lunch - September 16, 2025',
    description: 'Networking lunch with keynote presentation (Complimentary)',
  },
  {
    id: 'dinner-day2',
    name: 'Reception & Dinner',
    date: '2025-09-16',
    time: '18:00',
    displayName: 'Reception & Dinner - September 16, 2025',
    description: 'Networking reception and dinner',
  },
  {
    id: 'breakfast-day3',
    name: 'Breakfast',
    date: '2025-09-17',
    time: '07:30',
    displayName: 'Breakfast - September 17, 2025',
    description: 'Continental breakfast before final sessions (Complimentary)',
  },
  {
    id: 'lunch-day3',
    name: 'Closing Lunch',
    date: '2025-09-17',
    time: '12:00',
    displayName: 'Closing Lunch - September 17, 2025',
    description: 'Closing lunch and farewell',
  },
];

// Main Conference Events
export const CONFERENCE_EVENTS: ConferenceEvent[] = [
  {
    id: 'golf-tournament',
    name: 'Golf Tournament',
    date: '2025-09-15',
    startTime: '11:00',
    endTime: '16:00',
    location: 'TBD Golf Course',
    description: 'Annual IEPA Golf Tournament - $200 fee',
  },
  {
    id: 'registration-opening',
    name: 'Registration & Check-in',
    date: '2025-09-15',
    startTime: '15:00',
    endTime: '18:00',
    location: 'Annual Meeting Center Lobby',
    description: 'Annual meeting registration and check-in',
  },
  {
    id: 'welcome-reception-dinner',
    name: 'Welcome Reception & Dinner',
    date: '2025-09-15',
    startTime: '18:00',
    endTime: '22:00',
    location: 'Annual Meeting Center',
    description: 'Welcome networking reception and dinner',
  },
  {
    id: 'day2-breakfast',
    name: 'Breakfast',
    date: '2025-09-16',
    startTime: '07:30',
    endTime: '08:30',
    location: 'Conference Center',
    description: 'Continental breakfast before morning sessions',
  },
  {
    id: 'day2-sessions-morning',
    name: 'Sessions',
    date: '2025-09-16',
    startTime: '08:30',
    endTime: '12:00',
    location: 'Conference Center',
    description: 'Morning conference sessions',
  },
  {
    id: 'day2-lunch-keynote',
    name: 'Lunch & Keynote',
    date: '2025-09-16',
    startTime: '12:00',
    endTime: '13:00',
    location: 'Conference Center',
    description: 'Networking lunch with keynote presentation',
  },
  {
    id: 'day2-sessions-afternoon',
    name: 'Sessions',
    date: '2025-09-16',
    startTime: '13:00',
    endTime: '18:00',
    location: 'Conference Center',
    description: 'Afternoon conference sessions',
  },
  {
    id: 'day2-reception-dinner',
    name: 'Reception & Dinner',
    date: '2025-09-16',
    startTime: '18:00',
    endTime: '22:00',
    location: 'Conference Center',
    description: 'Networking reception and dinner',
  },
  {
    id: 'day3-breakfast',
    name: 'Breakfast',
    date: '2025-09-17',
    startTime: '07:30',
    endTime: '08:30',
    location: 'Conference Center',
    description: 'Continental breakfast before final sessions',
  },
  {
    id: 'day3-sessions',
    name: 'Sessions',
    date: '2025-09-17',
    startTime: '08:30',
    endTime: '11:45',
    location: 'Conference Center',
    description: 'Final conference sessions',
  },
  {
    id: 'day3-final-thoughts',
    name: 'Final Thoughts and Conclusion',
    date: '2025-09-17',
    startTime: '11:45',
    endTime: '12:00',
    location: 'Conference Center',
    description: 'Conference wrap-up and closing remarks',
  },
  {
    id: 'day3-closing-lunch',
    name: 'Closing Lunch',
    date: '2025-09-17',
    startTime: '12:00',
    endTime: '13:00',
    location: 'Conference Center',
    description: 'Closing lunch and farewell',
  },
  {
    id: 'day3-departure',
    name: 'Departure',
    date: '2025-09-17',
    startTime: '13:00',
    endTime: '14:00',
    location: 'Conference Center',
    description: 'Conference departure',
  },
];

// Utility Functions
export const dateUtils = {
  /**
   * Format date for display in forms and UI
   */
  formatDisplayDate: (date: string): string => {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  },

  /**
   * Format date for short display (e.g., "Sep 22")
   */
  formatShortDate: (date: string): string => {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  },

  /**
   * Format date for mobile display (e.g., "Mon, Sep 15")
   */
  formatMobileDate: (date: string): string => {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  },

  /**
   * Format time for display
   */
  formatTime: (time: string): string => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  },

  /**
   * Get meal options for forms (returns array of meal objects)
   */
  getMealOptions: () => {
    return MEAL_SCHEDULE.map((meal, index) => ({
      id: meal.id,
      value: meal.id,
      label: `${index + 1}. ${meal.displayName}`,
      shortLabel: meal.name,
      date: meal.date,
      time: meal.time,
      description: meal.description,
    }));
  },

  /**
   * Check if a date is within the conference period
   */
  isConferenceDate: (date: string): boolean => {
    const checkDate = new Date(date);
    const startDate = new Date(CONFERENCE_DATES.startDate.date);
    const endDate = new Date(CONFERENCE_DATES.endDate.date);
    return checkDate >= startDate && checkDate <= endDate;
  },

  /**
   * Get conference duration in days
   */
  getConferenceDuration: (): number => {
    const startDate = new Date(CONFERENCE_DATES.startDate.date);
    const endDate = new Date(CONFERENCE_DATES.endDate.date);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  },

  /**
   * Map database field names to human-readable date displays
   */
  mapDatabaseFieldToDate: (
    fieldName: string,
    options?: { mobile?: boolean }
  ): string => {
    const mobile = options?.mobile || false;

    switch (fieldName) {
      case 'night_one':
      case 'nightOne':
        return mobile ? 'Mon, Sep 15-16' : 'Monday, September 15-16, 2025';
      case 'night_two':
      case 'nightTwo':
        return mobile ? 'Tue, Sep 16-17' : 'Tuesday, September 16-17, 2025';
      case 'day_one':
      case 'dayOne':
        return mobile ? 'Mon, Sep 15' : 'Monday, September 15, 2025';
      case 'day_two':
      case 'dayTwo':
        return mobile ? 'Tue, Sep 16' : 'Tuesday, September 16, 2025';
      case 'day_three':
      case 'dayThree':
        return mobile ? 'Wed, Sep 17' : 'Wednesday, September 17, 2025';
      default:
        return fieldName;
    }
  },

  /**
   * Get lodging night display information
   */
  getLodgingNightInfo: (
    nightField: 'night_one' | 'night_two' | 'nightOne' | 'nightTwo'
  ) => {
    const nightMap = {
      night_one: {
        date: '2025-09-15',
        displayDate: 'Monday, September 15, 2025',
        shortDate: 'Sep 15-16',
        mobileDate: 'Mon, Sep 15-16',
        description: 'Arrival day with welcome reception and networking dinner',
        checkIn: 'September 15',
        checkOut: 'September 16',
      },
      nightOne: {
        date: '2025-09-15',
        displayDate: 'Monday, September 15, 2025',
        shortDate: 'Sep 15-16',
        mobileDate: 'Mon, Sep 15-16',
        description: 'Arrival day and welcome reception',
        checkIn: 'September 15',
        checkOut: 'September 16',
      },
      night_two: {
        date: '2025-09-16',
        displayDate: 'Tuesday, September 16, 2025',
        shortDate: 'Sep 16-17',
        mobileDate: 'Tue, Sep 16-17',
        description: 'Main conference day and networking dinner',
        checkIn: 'September 16',
        checkOut: 'September 17',
      },
      nightTwo: {
        date: '2025-09-16',
        displayDate: 'Tuesday, September 16, 2025',
        shortDate: 'Sep 16-17',
        mobileDate: 'Tue, Sep 16-17',
        description: 'Main conference day and networking dinner',
        checkIn: 'September 16',
        checkOut: 'September 17',
      },
    };

    return nightMap[nightField] || null;
  },
};

// Utility functions for formatting dates and times
export const formatConferenceDateRange = (
  startDate: ConferenceDate,
  endDate: ConferenceDate
): string => {
  return `${startDate.dayOfWeek}, ${startDate.displayDate} through ${endDate.dayOfWeek}, ${endDate.displayDate}`;
};

export const formatEventTime = (time: string): string => {
  // Convert 24-hour format to 12-hour format
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
  return `${displayHour}:${minutes} ${ampm}`;
};

export const formatMealTime = (mealEvent: MealEvent): string => {
  return formatEventTime(mealEvent.time);
};

// Export commonly used values
export const CONFERENCE_YEAR = 2025;
export const GOLF_TOURNAMENT_FEE = 200;

// Configuration metadata
export const CONFIG_METADATA = {
  lastUpdated: '2025-01-29',
  version: '1.0.0',
  notes:
    'Initial configuration with placeholder dates. Update with actual 2025 annual meeting dates when confirmed.',
};
