'use client';

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  FiBarChart,
  FiRefreshCw,
  FiDownload,
  FiUsers,
  FiDollarSign,
  FiTrendingUp,
  FiPieChart,
} from 'react-icons/fi';
import { formatCurrency } from '@/lib/pdf-generation/utils';

interface ReportData {
  registrationStats: {
    totalAttendees: number;
    totalSpeakers: number;
    totalSponsors: number;
    totalRevenue: number;
    registrationsByType: Record<string, number>;
    registrationsByDate: Array<{ date: string; count: number }>;
  };
  paymentStats: {
    completedPayments: number;
    pendingPayments: number;
    failedPayments: number;
    refundedPayments: number;
    revenueByType: Record<string, number>;
    dailyRevenue: Array<{ date: string; amount: number }>;
  };
  eventStats: {
    golfParticipants: number;
    mealSelections: Record<string, number>;
    dietaryRestrictions: Record<string, number>;
    accessibilityNeeds: number;
  };
}

export default function ReportsPage() {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportType, setReportType] = useState('overview');
  const [dateRange, setDateRange] = useState('all');

  // Fetch report data
  const fetchReportData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch attendees data
      const { data: attendees, error: attendeesError } = await supabase
        .from('iepa_attendee_registrations')
        .select('*');

      if (attendeesError) throw attendeesError;

      // Fetch speakers data
      const { data: speakers, error: speakersError } = await supabase
        .from('iepa_speaker_registrations')
        .select('*');

      if (speakersError) throw speakersError;

      // Fetch sponsors data
      const { data: sponsors, error: sponsorsError } = await supabase
        .from('iepa_sponsor_registrations')
        .select('*');

      if (sponsorsError) throw sponsorsError;

      // Fetch payment records
      const { data: payments, error: paymentsError } = await supabase
        .from('iepa_payments')
        .select('*');

      if (paymentsError) throw paymentsError;

      // Process data into report format
      const processedData: ReportData = {
        registrationStats: {
          totalAttendees: attendees?.length || 0,
          totalSpeakers: speakers?.length || 0,
          totalSponsors: sponsors?.length || 0,
          totalRevenue:
            payments?.reduce((sum, p) => sum + (p.amount || 0), 0) || 0,
          registrationsByType: {
            attendee: attendees?.length || 0,
            speaker: speakers?.length || 0,
            sponsor: sponsors?.length || 0,
          },
          registrationsByDate: [], // TODO: Process date-based data
        },
        paymentStats: {
          completedPayments:
            payments?.filter(p => p.status === 'completed').length || 0,
          pendingPayments:
            payments?.filter(p => p.status === 'pending').length || 0,
          failedPayments:
            payments?.filter(p => p.status === 'failed').length || 0,
          refundedPayments:
            payments?.filter(p => p.status === 'refunded').length || 0,
          revenueByType: {
            attendee:
              payments
                ?.filter(p => p.registration_type === 'attendee')
                .reduce((sum, p) => sum + p.amount, 0) || 0,
            speaker:
              payments
                ?.filter(p => p.registration_type === 'speaker')
                .reduce((sum, p) => sum + p.amount, 0) || 0,
            sponsor:
              payments
                ?.filter(p => p.registration_type === 'sponsor')
                .reduce((sum, p) => sum + p.amount, 0) || 0,
          },
          dailyRevenue: [], // TODO: Process daily revenue data
        },
        eventStats: {
          golfParticipants:
            (attendees?.filter(a => a.attending_golf).length || 0) +
            (speakers?.filter(s => s.attending_golf).length || 0) +
            (sponsors?.filter(s => s.attending_golf).length || 0),
          mealSelections: {}, // TODO: Process meal selection data
          dietaryRestrictions: {}, // TODO: Process dietary restrictions
          accessibilityNeeds:
            (attendees?.filter(a => a.accessibility_needs).length || 0) +
            (speakers?.filter(s => s.accessibility_needs).length || 0) +
            (sponsors?.filter(s => s.accessibility_needs).length || 0),
        },
      };

      setReportData(processedData);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to fetch report data'
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReportData();
  }, [reportType, dateRange]);

  // Export report
  const handleExportReport = async () => {
    try {
      if (!reportData) return;

      const reportContent = `
IEPA Conference Report
Generated: ${new Date().toISOString()}

REGISTRATION STATISTICS
Total Attendees: ${reportData.registrationStats.totalAttendees}
Total Speakers: ${reportData.registrationStats.totalSpeakers}
Total Sponsors: ${reportData.registrationStats.totalSponsors}
Total Revenue: ${formatCurrency(reportData.registrationStats.totalRevenue)}

PAYMENT STATISTICS
Completed Payments: ${reportData.paymentStats.completedPayments}
Pending Payments: ${reportData.paymentStats.pendingPayments}
Failed Payments: ${reportData.paymentStats.failedPayments}
Refunded Payments: ${reportData.paymentStats.refundedPayments}

EVENT STATISTICS
Golf Participants: ${reportData.eventStats.golfParticipants}
Accessibility Needs: ${reportData.eventStats.accessibilityNeeds}
      `;

      const blob = new Blob([reportContent], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `iepa-conference-report-${new Date().toISOString().split('T')[0]}.txt`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export report');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <FiBarChart className="w-6 h-6 mr-3 text-[var(--iepa-primary-blue)]" />
            Reports & Analytics
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Conference statistics and performance metrics
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <Button
            onClick={handleExportReport}
            variant="outline"
            className="flex items-center"
            disabled={!reportData}
          >
            <FiDownload className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button
            onClick={fetchReportData}
            variant="outline"
            className="flex items-center"
          >
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger>
                <SelectValue placeholder="Report Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="overview">Overview</SelectItem>
                <SelectItem value="registrations">Registrations</SelectItem>
                <SelectItem value="payments">Payments</SelectItem>
                <SelectItem value="events">Events</SelectItem>
              </SelectContent>
            </Select>
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger>
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">Last 7 Days</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Error Display */}
      {error && (
        <Card>
          <CardBody className="p-4">
            <div className="text-red-600 text-sm">{error}</div>
          </CardBody>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card>
          <CardBody className="p-8 text-center">
            <FiRefreshCw className="w-8 h-8 animate-spin mx-auto text-gray-400" />
            <p className="mt-2 text-gray-500">Loading report data...</p>
          </CardBody>
        </Card>
      )}

      {/* Report Content */}
      {!loading && reportData && (
        <>
          {/* Overview Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardBody className="p-6">
                <div className="flex items-center">
                  <FiUsers className="w-8 h-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">
                      Total Registrations
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {reportData.registrationStats.totalAttendees +
                        reportData.registrationStats.totalSpeakers +
                        reportData.registrationStats.totalSponsors}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-6">
                <div className="flex items-center">
                  <FiDollarSign className="w-8 h-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">
                      Total Revenue
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(
                        reportData.registrationStats.totalRevenue
                      )}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-6">
                <div className="flex items-center">
                  <FiTrendingUp className="w-8 h-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">
                      Golf Participants
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {reportData.eventStats.golfParticipants}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-6">
                <div className="flex items-center">
                  <FiPieChart className="w-8 h-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">
                      Completed Payments
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {reportData.paymentStats.completedPayments}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Detailed Reports */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Registration Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Registration Breakdown</CardTitle>
              </CardHeader>
              <CardBody className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Attendees</span>
                    <span className="text-sm text-gray-500">
                      {reportData.registrationStats.totalAttendees}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Speakers</span>
                    <span className="text-sm text-gray-500">
                      {reportData.registrationStats.totalSpeakers}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Sponsors</span>
                    <span className="text-sm text-gray-500">
                      {reportData.registrationStats.totalSponsors}
                    </span>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Payment Status */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Status</CardTitle>
              </CardHeader>
              <CardBody className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-green-600">
                      Completed
                    </span>
                    <span className="text-sm text-gray-500">
                      {reportData.paymentStats.completedPayments}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-yellow-600">
                      Pending
                    </span>
                    <span className="text-sm text-gray-500">
                      {reportData.paymentStats.pendingPayments}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-red-600">
                      Failed
                    </span>
                    <span className="text-sm text-gray-500">
                      {reportData.paymentStats.failedPayments}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-600">
                      Refunded
                    </span>
                    <span className="text-sm text-gray-500">
                      {reportData.paymentStats.refundedPayments}
                    </span>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
