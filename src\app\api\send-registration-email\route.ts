import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const {
      email,
      fullName,
      type,
      confirmationNumber,
      userId,
      speakerPricingType,
      sponsorshipLevel,
      registrationId,
    } = await request.json();

    // Validate required fields
    if (!email || !fullName || !type) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    console.log('[EMAIL-API] Sending welcome email:', {
      email,
      fullName,
      type,
      confirmationNumber,
      userId,
      speakerPricingType,
      sponsorshipLevel,
      registrationId,
    });

    // Try to send welcome email using the email service
    try {
      const { emailService } = await import('@/services/email');

      // Get registration data to check for lodging and golf options
      const { createSupabaseAdmin } = await import('@/lib/supabase');
      const supabaseAdmin = createSupabaseAdmin();

      let hasLodging = false;
      let hasGolf = false;

      if (registrationId && type === 'attendee') {
        const { data: registration } = await supabaseAdmin
          .from('iepa_attendee_registrations')
          .select('night_one, night_two, attending_golf')
          .eq('id', registrationId)
          .single();

        if (registration) {
          hasLodging =
            registration.night_one || registration.night_two || false;
          hasGolf = registration.attending_golf || false;
        }
      }

      // Use different email methods based on registration type
      if (type === 'sponsor') {
        // For sponsors, get the sponsor organization name from the database
        const { createSupabaseAdmin } = await import('@/lib/supabase');
        const supabaseAdmin = createSupabaseAdmin();

        const { data: sponsorData } = await supabaseAdmin
          .from('iepa_sponsor_registrations')
          .select('sponsor_name, sponsorship_amount, created_at')
          .eq('id', confirmationNumber)
          .single();

        // For sponsors, use the sponsor confirmation email template
        await emailService.sendSponsorConfirmationEmail(email, fullName, {
          organizationName: sponsorData?.sponsor_name || fullName,
          sponsorshipLevel: sponsorshipLevel || 'Standard',
          confirmationNumber: confirmationNumber || 'N/A',
          registrationDate: sponsorData?.created_at
            ? new Date(sponsorData.created_at).toLocaleDateString()
            : new Date().toLocaleDateString(),
          paymentAmount: sponsorData?.sponsorship_amount
            ? `$${sponsorData.sponsorship_amount.toLocaleString()}`
            : undefined,
          userId,
        });
      } else {
        // For attendees and speakers, use the welcome email
        await emailService.sendWelcomeEmail(email, fullName, {
          type,
          confirmationNumber,
          userId,
          hasLodging,
          hasGolf,
        });
      }

      console.log('[EMAIL-API] Welcome email sent successfully');
      return NextResponse.json({ success: true });
    } catch (emailError) {
      console.error('[EMAIL-API-ERROR] Failed to send email:', emailError);

      // Return success even if email fails to not block registration
      return NextResponse.json({
        success: true,
        warning: 'Registration successful but email notification failed',
      });
    }
  } catch (error) {
    console.error('[EMAIL-API-ERROR] API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
