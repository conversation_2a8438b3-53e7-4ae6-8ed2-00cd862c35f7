'use client';

import { CONFERENCE_YEAR } from '@/lib/conference-config';
import { ArrowLeft } from 'lucide-react';
import { useEffect, useState } from 'react';
import { getAgendaDataWithFallback } from '@/lib/services/agenda';
import { LegacyAgendaDay } from '@/lib/types/agenda';
import Link from 'next/link';



export default function PrintAgendaPage() {
  const [agendaDays, setAgendaDays] = useState<LegacyAgendaDay[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadAgendaData() {
      try {
        setLoading(true);
        const data = await getAgendaDataWithFallback();
        setAgendaDays(data);
      } catch (err) {
        console.error('Error loading agenda data for print:', err);
        // If there's an error, the fallback data will be used
      } finally {
        setLoading(false);
      }
    }

    loadAgendaData();
  }, []);

  if (loading) {
    return (
      <>
        <Link href="/agenda" className="print-back-link">
          <ArrowLeft size={16} />
          Back to Agenda
        </Link>
        <div className="print-agenda-container">
          <div className="print-header">
            <h1 className="print-title">IEPA {CONFERENCE_YEAR} Conference Agenda</h1>
            <p className="print-subtitle">Loading...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Link href="/agenda" className="print-back-link">
        <ArrowLeft size={16} />
        Back to Agenda
      </Link>
      <div className="print-agenda-container">
      <style jsx global>{`
        @media print {
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }
          @page {
            margin: 0.15in;
            size: letter;
          }
          body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            font-size: 8px;
            line-height: 1.1;
            color: #000;
          }
          .print-agenda-container {
            max-width: 100%;
            margin: 0;
            padding: 0;
            font-size: 8px;
            line-height: 1.1;
            page-break-inside: avoid;
          }
          .print-header {
            text-align: center;
            margin-bottom: 0.08in;
            border-bottom: 1px solid #000;
            padding-bottom: 0.03in;
          }
          .print-title {
            font-size: 12px;
            font-weight: bold;
            color: #000;
            margin: 0 0 0.02in 0;
          }
          .print-subtitle {
            font-size: 8px;
            color: #333;
            margin: 0;
          }
          .print-day {
            margin-bottom: 0.06in;
            break-inside: avoid;
            page-break-inside: avoid;
          }
          .print-day-header {
            background: #f0f0f0;
            padding: 0.02in 0.03in;
            border-left: 2px solid #000;
            font-weight: bold;
            font-size: 8px;
            margin-bottom: 0.02in;
          }
          .print-event {
            display: flex;
            margin-bottom: 0.01in;
            padding-left: 0.03in;
            break-inside: avoid;
          }
          .print-time {
            width: 0.5in;
            font-weight: bold;
            color: #000;
            flex-shrink: 0;
            font-size: 7px;
          }
          .print-event-details {
            flex: 1;
            font-size: 7px;
          }
          .print-event-title {
            font-weight: bold;
            display: inline;
          }
          .print-event-desc {
            color: #333;
            font-size: 7px;
            display: inline;
          }
          .print-notes {
            margin-top: 0.06in;
            padding: 0.03in;
            background: #f8f8f8;
            border: 1px solid #ccc;
            font-size: 7px;
            break-inside: avoid;
          }
          .print-notes-title {
            font-weight: bold;
            margin-bottom: 0.02in;
            font-size: 8px;
          }
          .print-notes ul {
            margin: 0;
            padding-left: 0.1in;
            list-style-type: disc;
          }
          .print-notes li {
            margin-bottom: 0.005in;
            font-size: 7px;
          }
          /* Hide all navigation and non-essential elements */
          nav, header, footer, .banner, .navigation, .breadcrumb {
            display: none !important;
          }
          /* Hide back link in print */
          .print-back-link {
            display: none !important;
          }
        }
        
        @media screen {
          .print-back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            margin-bottom: 1rem;
            background: var(--iepa-primary-blue);
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s ease;
          }
          .print-back-link:hover {
            background: var(--iepa-primary-blue-dark);
            color: white;
            text-decoration: none;
          }
          .print-agenda-container {
            max-width: 8.5in;
            margin: 1rem auto;
            padding: 0.5in;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 11in;
            font-size: 11px;
            line-height: 1.3;
          }
          .print-header {
            text-align: center;
            margin-bottom: 1rem;
            border-bottom: 1px solid #1e40af;
            padding-bottom: 0.5rem;
          }
          .print-title {
            font-size: 18px;
            font-weight: bold;
            color: #1e40af;
            margin: 0 0 0.25rem 0;
          }
          .print-subtitle {
            font-size: 12px;
            color: #6b7280;
            margin: 0;
          }
          .print-day {
            margin-bottom: 0.75rem;
          }
          .print-day-header {
            background: #f3f4f6;
            padding: 0.25rem 0.5rem;
            border-left: 3px solid #1e40af;
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 0.25rem;
          }
          .print-event {
            display: flex;
            margin-bottom: 0.25rem;
            padding-left: 0.5rem;
          }
          .print-time {
            width: 70px;
            font-weight: bold;
            color: #1e40af;
            flex-shrink: 0;
            font-size: 10px;
          }
          .print-event-details {
            flex: 1;
            font-size: 10px;
          }
          .print-event-title {
            font-weight: bold;
            display: inline;
          }
          .print-event-desc {
            color: #6b7280;
            font-size: 10px;
            display: inline;
          }
          .print-notes {
            margin-top: 1rem;
            padding: 0.5rem;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
          }
          .print-notes-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #1e40af;
            font-size: 11px;
          }
          .print-notes ul {
            margin: 0;
            padding-left: 1rem;
          }
          .print-notes li {
            margin-bottom: 0.1rem;
            font-size: 10px;
          }
        }
      `}</style>

      <div className="print-header">
        <h1 className="print-title">IEPA {CONFERENCE_YEAR} Conference Agenda</h1>
        <p className="print-subtitle">
          {CONFERENCE_DATES.startDate.displayDate} - {CONFERENCE_DATES.endDate.displayDate} | Stanford Sierra Conference Center, Lake Tahoe
        </p>
      </div>

      <div className="print-content">
        {agendaDays.map((day) => (
          <div key={day.dayNumber} className="print-day">
            <div className="print-day-header">
              {day.title}
            </div>
            {day.events.map((event, index) => (
              <div key={index} className="print-event">
                <div className="print-time">{event.time}</div>
                <div className="print-event-details">
                  <span className="print-event-title">{event.title}</span>
                  {event.description && (
                    <>
                      <span> - </span>
                      <span className="print-event-desc">{event.description}</span>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>

      <div className="print-notes">
        <div className="print-notes-title">Important Notes</div>
        <ul>
          <li>All meals complimentary • Golf tournament $200 fee • Business casual dress</li>
          <li>Schedule subject to change • Comfortable shoes recommended</li>
        </ul>
      </div>
    </div>
    </>
  );
}
