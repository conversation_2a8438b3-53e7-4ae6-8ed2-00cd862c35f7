// User Registration Data Service for IEPA 2025 Conference Registration
// Service functions for fetching user-specific registration data

import { supabase } from '@/lib/supabase';
import type {
  UserRegistrationsResponse,
  UserRegistrationDetails,
  UserPaymentRecord,
  UserRegistrationSummary,
  AttendeeRegistration,
  SpeakerRegistration,
  SponsorRegistration,
  PaymentRecord,
  UpdateRegistrationRequest,
  UpdateRegistrationResponse,
} from '@/types/userRegistrations';
import { RegistrationStatus, PaymentStatus } from '@/types/userRegistrations';

/**
 * Fetch all registrations for a specific user
 */
export async function fetchUserRegistrations(
  userId: string
): Promise<UserRegistrationsResponse> {
  try {
    // Get the authenticated user's email for email-based matching
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    const userEmail = user?.email;

    // Fetch all registration types in parallel
    // Use order + limit(1) to get the most recent registration if multiple exist
    // Query by both user_id and email to handle cases where admin creates registration for user
    const [attendee<PERSON><PERSON>ult, speakerResult, sponsorResult, paymentsResult] =
      await Promise.all([
        supabase
          .from('iepa_attendee_registrations')
          .select('*')
          .or(`user_id.eq.${userId}${userEmail ? `,email.eq.${userEmail}` : ''}`)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle(),
        supabase
          .from('iepa_speaker_registrations')
          .select('*')
          .or(`user_id.eq.${userId}${userEmail ? `,email.eq.${userEmail}` : ''}`)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle(),
        supabase
          .from('iepa_sponsor_registrations')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle(),
        supabase
          .from('iepa_payments')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false }),
      ]);

    // Check for errors
    if (attendeeResult.error) throw attendeeResult.error;
    if (speakerResult.error) throw speakerResult.error;
    if (sponsorResult.error) throw sponsorResult.error;
    if (paymentsResult.error) throw paymentsResult.error;

    // Transform data to user-friendly format
    const attendee = attendeeResult.data
      ? transformAttendeeRegistration(attendeeResult.data)
      : undefined;
    const speaker = speakerResult.data
      ? transformSpeakerRegistration(speakerResult.data)
      : undefined;
    const sponsor = sponsorResult.data
      ? transformSponsorRegistration(sponsorResult.data)
      : undefined;
    const payments = paymentsResult.data?.map(transformPaymentRecord) || [];

    // Generate summary
    const summary = generateRegistrationSummary({ attendee, speaker, sponsor });

    return {
      attendee,
      speaker,
      sponsor,
      payments,
      summary,
    };
  } catch (error) {
    console.error('Error fetching user registrations:', error);
    throw error;
  }
}

/**
 * Fetch attendee registration for a specific user
 */
export async function fetchUserAttendeeRegistration(
  userId: string
): Promise<UserRegistrationDetails | null> {
  try {
    // Get the authenticated user's email for email-based matching
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    const userEmail = user?.email;

    const { data, error } = await supabase
      .from('iepa_attendee_registrations')
      .select('*')
      .or(`user_id.eq.${userId}${userEmail ? `,email.eq.${userEmail}` : ''}`)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) throw error;
    return data ? transformAttendeeRegistration(data) : null;
  } catch (error) {
    console.error('Error fetching attendee registration:', error);
    throw error;
  }
}

/**
 * Fetch speaker registration for a specific user
 */
export async function fetchUserSpeakerRegistration(
  userId: string
): Promise<UserRegistrationDetails | null> {
  try {
    // Get the authenticated user's email for email-based matching
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    const userEmail = user?.email;

    const { data, error } = await supabase
      .from('iepa_speaker_registrations')
      .select('*')
      .or(`user_id.eq.${userId}${userEmail ? `,email.eq.${userEmail}` : ''}`)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) throw error;
    return data ? transformSpeakerRegistration(data) : null;
  } catch (error) {
    console.error('Error fetching speaker registration:', error);
    throw error;
  }
}

/**
 * Fetch sponsor registration for a specific user
 */
export async function fetchUserSponsorRegistration(
  userId: string
): Promise<UserRegistrationDetails | null> {
  try {
    const { data, error } = await supabase
      .from('iepa_sponsor_registrations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) throw error;
    return data ? transformSponsorRegistration(data) : null;
  } catch (error) {
    console.error('Error fetching sponsor registration:', error);
    throw error;
  }
}

/**
 * Fetch payment history for a specific user
 */
export async function fetchUserPaymentHistory(
  userId: string
): Promise<UserPaymentRecord[]> {
  try {
    const { data, error } = await supabase
      .from('iepa_payments')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data?.map(transformPaymentRecord) || [];
  } catch (error) {
    console.error('Error fetching payment history:', error);
    throw error;
  }
}

/**
 * Update user registration
 */
export async function updateUserRegistration(
  request: UpdateRegistrationRequest
): Promise<UpdateRegistrationResponse> {
  try {
    const { registrationId, registrationType, updates } = request;

    let tableName: string;
    switch (registrationType) {
      case 'attendee':
        tableName = 'iepa_attendee_registrations';
        break;
      case 'speaker':
        tableName = 'iepa_speaker_registrations';
        break;
      case 'sponsor':
        tableName = 'iepa_sponsor_registrations';
        break;
      default:
        throw new Error(`Invalid registration type: ${registrationType}`);
    }

    const { data, error } = await supabase
      .from(tableName)
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', registrationId)
      .select()
      .single();

    if (error) throw error;

    // Transform the updated data
    let transformedData: UserRegistrationDetails;
    switch (registrationType) {
      case 'attendee':
        transformedData = transformAttendeeRegistration(
          data as AttendeeRegistration
        );
        break;
      case 'speaker':
        transformedData = transformSpeakerRegistration(
          data as SpeakerRegistration
        );
        break;
      case 'sponsor':
        transformedData = transformSponsorRegistration(
          data as SponsorRegistration
        );
        break;
      default:
        throw new Error(`Invalid registration type: ${registrationType}`);
    }

    return {
      success: true,
      data: transformedData,
      message: 'Registration updated successfully',
    };
  } catch (error) {
    console.error('Error updating registration:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Transform functions
function transformAttendeeRegistration(
  data: AttendeeRegistration
): UserRegistrationDetails {
  // Derive status from payment status since there's no status field in the database
  const derivedStatus =
    data.payment_status === 'completed'
      ? RegistrationStatus.CONFIRMED
      : RegistrationStatus.PENDING;

  return {
    id: data.id,
    userId: data.user_id,
    type: 'attendee',
    status: derivedStatus,
    paymentStatus:
      (data.payment_status as PaymentStatus) || PaymentStatus.PENDING,
    createdAt: data.created_at,
    updatedAt: data.updated_at,

    // Personal information
    personalInfo: {
      firstName: data.first_name || '',
      lastName: data.last_name || '',
      email: data.email || '',
      phone: data.phone_number || undefined,
      organization: data.organization || undefined,
      title: data.job_title || undefined,
      badgeName: data.name_on_badge || undefined,
      gender: data.gender || undefined,
    },

    // Registration-specific details
    registrationDetails: {
      registrationType: data.registration_type || undefined,
      membershipType: undefined, // Not available in database
      specialRequests: undefined, // Not available in database
      dietaryRestrictions: data.dietary_needs || undefined,
      accessibilityNeeds: undefined, // Not available in database
    },

    // Event participation
    eventParticipation: {
      attendingGolf: data.attending_golf || false,
      golfClubRental: data.golf_club_rental || undefined,
      golfClubHandedness: data.golf_club_handedness || undefined,
      mealSelections: {}, // meals field is string[] in database, not object
    },

    // Financial information
    financial: {
      registrationFee: data.registration_total || 0,
      golfFee: data.golf_total || undefined,
      golfClubRentalFee: data.golf_club_rental_total || undefined,
      mealTotal: data.meal_total || undefined,
      subtotal:
        data.registration_total +
        (data.golf_total || 0) +
        (data.golf_club_rental_total || 0) +
        (data.meal_total || 0),
      tax: 0, // No tax currently
      grandTotal: data.grand_total || 0,
    },

    // Document URLs
    documents: {
      receiptUrl: data.receipt_url || undefined,
      invoiceUrl: data.invoice_url || undefined,
      receiptGeneratedAt: data.receipt_generated_at || undefined,
      invoiceGeneratedAt: data.invoice_generated_at || undefined,
    },
  };
}

function transformSpeakerRegistration(
  data: SpeakerRegistration
): UserRegistrationDetails {
  // Speakers are typically confirmed once they submit their registration
  const derivedStatus = RegistrationStatus.CONFIRMED;

  return {
    id: data.id,
    userId: data.user_id,
    type: 'speaker',
    status: derivedStatus,
    paymentStatus: PaymentStatus.COMPLETED, // Speakers typically don't pay
    createdAt: data.created_at,
    updatedAt: data.updated_at,

    // Personal information
    personalInfo: {
      firstName: data.first_name || '',
      lastName: data.last_name || '',
      email: data.email || '',
      phone: data.phone_number || undefined,
      organization: data.organization_name || undefined,
      title: data.job_title || undefined,
    },

    // Registration-specific details
    registrationDetails: {
      specialRequests: data.special_requests || undefined,
      dietaryRestrictions: undefined, // Not available in speaker database
      accessibilityNeeds: undefined, // Not available in speaker database
    },

    // Event participation (minimal for speakers)
    eventParticipation: {
      attendingGolf: false,
      mealSelections: {}, // Not available in speaker database
    },

    // Financial information (typically $0 for speakers)
    financial: {
      registrationFee: 0,
      subtotal: 0,
      grandTotal: 0,
    },

    // Speaker-specific information
    speakerInfo: {
      bio: data.bio || '',
      presentationTitle: data.presentation_title || '',
      presentationDescription: data.presentation_description || '',
      presentationFileUrl: data.presentation_file_url || undefined,
      headshotUrl: data.headshot_url || undefined,
    },

    // Document URLs
    documents: {
      receiptUrl: data.receipt_url || undefined,
      invoiceUrl: data.invoice_url || undefined,
      receiptGeneratedAt: data.receipt_generated_at || undefined,
      invoiceGeneratedAt: data.invoice_generated_at || undefined,
    },
  };
}

function transformSponsorRegistration(
  data: SponsorRegistration
): UserRegistrationDetails {
  // Derive status from payment status since there's no status field in the database
  const derivedStatus =
    data.payment_status === 'completed'
      ? RegistrationStatus.CONFIRMED
      : RegistrationStatus.PENDING;

  return {
    id: data.id,
    userId: data.user_id,
    type: 'sponsor',
    status: derivedStatus,
    paymentStatus:
      (data.payment_status as PaymentStatus) || PaymentStatus.PENDING,
    createdAt: data.created_at,
    updatedAt: data.updated_at,

    // Personal information (contact person) - sponsors don't have separate first/last names
    personalInfo: {
      firstName: '', // Not available in sponsor registration
      lastName: '', // Not available in sponsor registration
      email: data.user_id || '', // Use user_id as fallback for email
      phone: undefined,
      organization: data.sponsor_name || undefined,
      title: undefined,
    },

    // Registration-specific details
    registrationDetails: {
      specialRequests: undefined, // Not available in sponsor database
    },

    // Event participation (minimal for sponsors)
    eventParticipation: {
      attendingGolf: false,
      mealSelections: {},
    },

    // Financial information
    financial: {
      registrationFee: 0, // Not available in sponsor database
      subtotal: 0, // Not available in sponsor database
      grandTotal: 0, // Not available in sponsor database
    },

    // Sponsor-specific information
    sponsorInfo: {
      organizationName: data.sponsor_name || '',
      sponsorshipLevel: 'Standard', // Default since sponsorship_level doesn't exist in database
      packageDetails: {},
      logoUrl: data.sponsor_image_url || undefined,
      websiteUrl: data.sponsor_url || undefined,
      description: data.sponsor_description || undefined,
    },

    // Document URLs
    documents: {
      receiptUrl: data.receipt_url || undefined,
      invoiceUrl: data.invoice_url || undefined,
      receiptGeneratedAt: data.receipt_generated_at || undefined,
      invoiceGeneratedAt: data.invoice_generated_at || undefined,
    },
  };
}

function transformPaymentRecord(data: PaymentRecord): UserPaymentRecord {
  return {
    id: data.id,
    registrationId: data.registration_id,
    registrationType: data.registration_type,
    amount: data.amount || 0,
    currency: data.currency || 'USD',
    status: (data.status as PaymentStatus) || PaymentStatus.PENDING,
    paymentMethod: 'card', // Default since payment_method field doesn't exist in database
    stripePaymentIntentId: data.stripe_payment_intent_id || undefined,
    transactionId: data.stripe_payment_intent_id || '',
    createdAt: data.created_at,
    updatedAt: data.updated_at,
    description: `Payment for ${data.registration_type} registration`,
    metadata: {},
  };
}

function generateRegistrationSummary(registrations: {
  attendee?: UserRegistrationDetails;
  speaker?: UserRegistrationDetails;
  sponsor?: UserRegistrationDetails;
}): UserRegistrationSummary[] {
  const summary: UserRegistrationSummary[] = [];

  if (registrations.attendee) {
    const reg = registrations.attendee;
    summary.push({
      id: reg.id,
      type: 'attendee',
      status: reg.status,
      amount: reg.financial.grandTotal,
      paymentStatus: reg.paymentStatus,
      createdAt: reg.createdAt,
      updatedAt: reg.updatedAt,
      title: 'Conference Attendee Registration',
      description:
        `Registration for ${reg.personalInfo?.firstName} ${reg.personalInfo?.lastName}`.trim() ||
        'attendee' +
          ` from ${reg.personalInfo?.organization || 'organization'}`,
      canEdit:
        reg.status === RegistrationStatus.DRAFT ||
        reg.status === RegistrationStatus.PENDING,
      hasReceipt: reg.paymentStatus === PaymentStatus.COMPLETED,
      hasInvoice:
        reg.paymentStatus === PaymentStatus.COMPLETED ||
        reg.paymentStatus === PaymentStatus.PENDING,
    });
  }

  if (registrations.speaker) {
    const reg = registrations.speaker;
    summary.push({
      id: reg.id,
      type: 'speaker',
      status: reg.status,
      amount: reg.financial.grandTotal,
      paymentStatus: reg.paymentStatus,
      createdAt: reg.createdAt,
      updatedAt: reg.updatedAt,
      title: 'Speaker Registration',
      description: `Speaker proposal: ${reg.speakerInfo?.presentationTitle || 'Presentation'}`,
      canEdit:
        reg.status === RegistrationStatus.DRAFT ||
        reg.status === RegistrationStatus.PENDING,
      hasReceipt: reg.paymentStatus === PaymentStatus.COMPLETED,
      hasInvoice:
        reg.paymentStatus === PaymentStatus.COMPLETED ||
        reg.paymentStatus === PaymentStatus.PENDING,
    });
  }

  if (registrations.sponsor) {
    const reg = registrations.sponsor;
    summary.push({
      id: reg.id,
      type: 'sponsor',
      status: reg.status,
      amount: reg.financial.grandTotal,
      paymentStatus: reg.paymentStatus,
      createdAt: reg.createdAt,
      updatedAt: reg.updatedAt,
      title: 'Sponsorship Registration',
      description: `${reg.sponsorInfo?.sponsorshipLevel || 'Sponsor'} level sponsorship for ${reg.sponsorInfo?.organizationName || 'organization'}`,
      canEdit:
        reg.status === RegistrationStatus.DRAFT ||
        reg.status === RegistrationStatus.PENDING,
      hasReceipt: reg.paymentStatus === PaymentStatus.COMPLETED,
      hasInvoice:
        reg.paymentStatus === PaymentStatus.COMPLETED ||
        reg.paymentStatus === PaymentStatus.PENDING,
    });
  }

  return summary;
}
