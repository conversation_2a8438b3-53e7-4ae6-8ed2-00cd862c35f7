import { NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

/**
 * API endpoint to fix the RLS policy for iepa_admin_users table
 * This fixes the circular dependency causing 406 errors
 */

export async function POST() {
  try {
    console.log('[FIX-RLS] Starting RLS policy fix for iepa_admin_users...');

    const supabaseAdmin = createSupabaseAdmin();

    // SQL to fix the RLS policy
    const fixRLSSQL = `
      -- Drop existing problematic policy
      DROP POLICY IF EXISTS "Admin users can view all admin records" ON iepa_admin_users;

      -- Create new policy that allows users to check their own admin status
      CREATE POLICY "Users can check their own admin status"
          ON iepa_admin_users FOR SELECT
          USING (
              -- Allow users to see their own admin record based on email
              email = auth.jwt() ->> 'email'
              OR
              -- Allow service role to see all records
              auth.jwt() ->> 'role' = 'service_role'
          );

      -- Create policy for super admins to view all records
      CREATE POLICY "Super admins can view all admin records"
          ON iepa_admin_users FOR SELECT
          USING (
              -- Service role can see everything
              auth.jwt() ->> 'role' = 'service_role'
              OR
              -- Super admins can see all admin records
              EXISTS (
                  SELECT 1 FROM iepa_admin_users admin
                  WHERE admin.email = auth.jwt() ->> 'email'
                  AND admin.role = 'super_admin'
                  AND admin.is_active = true
              )
          );
    `;

    console.log('[FIX-RLS] Executing RLS policy fix...');

    // Execute the SQL
    const { error } = await supabaseAdmin.rpc('exec_sql', {
      sql: fixRLSSQL,
    });

    if (error) {
      // If RPC doesn't work, try direct SQL execution
      console.log('[FIX-RLS] RPC failed, trying direct SQL execution...');

      // Try executing each statement separately
      const statements = [
        'DROP POLICY IF EXISTS "Admin users can view all admin records" ON iepa_admin_users;',
        `CREATE POLICY "Users can check their own admin status"
            ON iepa_admin_users FOR SELECT
            USING (
                email = auth.jwt() ->> 'email'
                OR
                auth.jwt() ->> 'role' = 'service_role'
            );`,
        `CREATE POLICY "Super admins can view all admin records"
            ON iepa_admin_users FOR SELECT
            USING (
                auth.jwt() ->> 'role' = 'service_role'
                OR
                EXISTS (
                    SELECT 1 FROM iepa_admin_users admin
                    WHERE admin.email = auth.jwt() ->> 'email'
                    AND admin.role = 'super_admin'
                    AND admin.is_active = true
                )
            );`,
      ];

      for (const statement of statements) {
        const { error: stmtError } = await supabaseAdmin.rpc('exec_sql', {
          sql: statement,
        });

        if (stmtError) {
          console.error(
            '[FIX-RLS] Failed to execute statement:',
            statement,
            stmtError
          );
          throw stmtError;
        }
      }
    }

    console.log('[FIX-RLS] RLS policy fix completed successfully');

    // Test the fix by trying to query the admin table
    const { data: testData, error: testError } = await supabaseAdmin
      .from('iepa_admin_users')
      .select('email, role, is_active')
      .limit(1);

    if (testError) {
      console.warn('[FIX-RLS] Test query failed:', testError);
    } else {
      console.log(
        '[FIX-RLS] Test query successful, found',
        testData?.length || 0,
        'admin records'
      );
    }

    return NextResponse.json({
      success: true,
      message: 'RLS policy fixed successfully',
      details: {
        policyFixed: true,
        testQueryWorked: !testError,
        adminRecordsFound: testData?.length || 0,
      },
    });
  } catch (error) {
    console.error('[FIX-RLS] Error fixing RLS policy:', error);

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to fix RLS policy',
        details: {
          policyFixed: false,
          errorType:
            error instanceof Error ? error.constructor.name : 'Unknown',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check current RLS policy status
 */
export async function GET() {
  try {
    console.log('[FIX-RLS] Checking current RLS policy status...');

    const supabaseAdmin = createSupabaseAdmin();

    // Query to check current policies
    const { data: policies, error: policiesError } = await supabaseAdmin.rpc(
      'exec_sql',
      {
        sql: `
          SELECT policyname, cmd, qual 
          FROM pg_policies 
          WHERE tablename = 'iepa_admin_users' 
          AND schemaname = 'public';
        `,
      }
    );

    if (policiesError) {
      console.warn('[FIX-RLS] Could not fetch policies:', policiesError);
    }

    // Test admin table access
    const { data: testData, error: testError } = await supabaseAdmin
      .from('iepa_admin_users')
      .select('email, role, is_active')
      .limit(5);

    return NextResponse.json({
      success: true,
      message: 'RLS policy status checked',
      details: {
        policies: policies || [],
        policiesError: policiesError?.message || null,
        testQueryWorked: !testError,
        testError: testError?.message || null,
        adminRecordsFound: testData?.length || 0,
        sampleAdmins:
          testData?.map(admin => ({
            email: admin.email,
            role: admin.role,
            is_active: admin.is_active,
          })) || [],
      },
    });
  } catch (error) {
    console.error('[FIX-RLS] Error checking RLS policy status:', error);

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to check RLS policy status',
      },
      { status: 500 }
    );
  }
}
