// Email Log Utilities for IEPA Admin Interface
// Utility functions and types for email log management

export interface EmailLog {
  id: string;
  recipient_email: string;
  recipient_name?: string;
  sender_email: string;
  sender_name?: string;
  subject: string;
  email_type: string;
  status: 'sent' | 'failed' | 'pending';
  sent_at?: string;
  created_at: string;
  updated_at?: string;
  registration_type?: string;
  registration_id?: string;
  user_id?: string;
  payment_id?: string;
  error_message?: string;
  content_preview?: string;
  has_attachments?: boolean;
  sendgrid_message_id?: string;
}

export interface EmailStats {
  totalSent: number;
  totalFailed: number;
  totalPending: number;
  totalEmails: number;
}

export interface EmailLogFilters {
  status: string;
  emailType: string;
  search: string;
  page: number;
  limit: number;
}

// Email status utilities
export const getEmailStatusColor = (status: string): string => {
  switch (status) {
    case 'sent':
      return 'text-green-600';
    case 'failed':
      return 'text-red-600';
    case 'pending':
      return 'text-yellow-600';
    default:
      return 'text-gray-600';
  }
};

export const getEmailStatusBgColor = (status: string): string => {
  switch (status) {
    case 'sent':
      return 'bg-green-50';
    case 'failed':
      return 'bg-red-50';
    case 'pending':
      return 'bg-yellow-50';
    default:
      return 'bg-gray-50';
  }
};

export const getEmailStatusBorderColor = (status: string): string => {
  switch (status) {
    case 'sent':
      return 'border-green-200';
    case 'failed':
      return 'border-red-200';
    case 'pending':
      return 'border-yellow-200';
    default:
      return 'border-gray-200';
  }
};

// Date formatting utilities
export const formatEmailDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
};

export const formatRelativeDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
  } else if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
  } else {
    return formatEmailDate(dateString);
  }
};

// Content utilities
export const truncateContent = (
  content: string,
  maxLength: number = 150
): string => {
  if (!content || content.length <= maxLength) {
    return content;
  }
  return content.substring(0, maxLength).trim() + '...';
};

export const stripHtmlTags = (html: string): string => {
  return html.replace(/<[^>]*>/g, '').trim();
};

export const createContentPreview = (
  htmlContent: string,
  maxLength: number = 150
): string => {
  const textContent = stripHtmlTags(htmlContent);
  return truncateContent(textContent, maxLength);
};

// Email type utilities
export const formatEmailType = (type: string): string => {
  return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

export const getEmailTypeColor = (type: string): string => {
  const typeColors: Record<string, string> = {
    registration_confirmation: 'text-blue-600',
    sponsor_confirmation: 'text-purple-600',
    payment_confirmation: 'text-green-600',
    golf_addon: 'text-purple-600',
    password_reset: 'text-orange-600',
    welcome: 'text-indigo-600',
    welcome_email: 'text-indigo-600',
    invoice: 'text-red-600',
    announcement: 'text-blue-600',
    reminder: 'text-yellow-600',
    update: 'text-teal-600',
    notification: 'text-gray-600',
    custom: 'text-gray-600',
  };
  return typeColors[type] || 'text-gray-600';
};

// SendGrid utilities
export const isValidSendGridId = (id: string): boolean => {
  // SendGrid message IDs are typically alphanumeric strings
  return /^[a-zA-Z0-9._-]+$/.test(id) && id.length > 10;
};

export const formatSendGridId = (id: string): string => {
  if (!id) return '';
  // Truncate long IDs for display
  return id.length > 20 ? `${id.substring(0, 20)}...` : id;
};

// Statistics utilities
export const calculateSuccessRate = (stats: EmailStats): number => {
  if (stats.totalEmails === 0) return 0;
  return Math.round((stats.totalSent / stats.totalEmails) * 100);
};

export const getStatusSummary = (stats: EmailStats): string => {
  const successRate = calculateSuccessRate(stats);

  if (stats.totalEmails === 0) {
    return 'No emails sent yet';
  }

  if (stats.totalFailed > 0) {
    return `${successRate}% success rate (${stats.totalFailed} failed)`;
  }

  if (stats.totalPending > 0) {
    return `${stats.totalPending} emails pending delivery`;
  }

  return `All ${stats.totalSent} emails delivered successfully`;
};

// Filter utilities
export const buildEmailLogQuery = (
  filters: EmailLogFilters
): URLSearchParams => {
  const params = new URLSearchParams();

  if (filters.status && filters.status !== 'all') {
    params.append('status', filters.status);
  }

  if (filters.emailType && filters.emailType !== 'all') {
    params.append('emailType', filters.emailType);
  }

  if (filters.search && filters.search.trim()) {
    params.append('search', filters.search.trim());
  }

  params.append('page', filters.page.toString());
  params.append('limit', filters.limit.toString());

  return params;
};

export const getDefaultFilters = (): EmailLogFilters => ({
  status: 'all',
  emailType: 'all',
  search: '',
  page: 1,
  limit: 50,
});

// Export utilities
export const generateEmailLogCSV = (logs: EmailLog[]): string => {
  const headers = [
    'ID',
    'Recipient Email',
    'Recipient Name',
    'Sender Email',
    'Subject',
    'Email Type',
    'Status',
    'Sent At',
    'Created At',
    'SendGrid ID',
    'Error Message',
  ];

  const rows = logs.map(log => [
    log.id,
    log.recipient_email,
    log.recipient_name || '',
    log.sender_email,
    log.subject,
    log.email_type,
    log.status,
    log.sent_at || '',
    log.created_at,
    log.sendgrid_message_id || '',
    log.error_message || '',
  ]);

  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n');

  return csvContent;
};

export const downloadCSV = (csvContent: string, filename: string): void => {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Validation utilities
export const validateEmailAddress = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const parseEmailList = (emailString: string): string[] => {
  return emailString
    .split(/[,\n\r]+/)
    .map(email => email.trim())
    .filter(email => email && validateEmailAddress(email));
};

// Clipboard utilities
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};
