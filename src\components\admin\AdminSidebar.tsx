'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { AdminUser } from '@/hooks/useAdminAccess';

import { Badge } from '@/components/ui/badge';
import {
  FiGrid,
  FiUsers,
  FiMic,
  FiStar,
  FiCreditCard,
  FiFile,
  FiMail,
  FiBarChart,
  FiDownload,
  FiDatabase,
  FiSettings,
  FiShield,
  FiX,
  FiCheck,
} from 'react-icons/fi';

interface AdminSidebarProps {
  adminUser: AdminUser | null;
  isOpen: boolean;
  onClose: () => void;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  permission?: string;
}

const navItems: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: FiGrid,
  },
  {
    name: 'Attendees',
    href: '/admin/attendees',
    icon: FiUsers,
  },
  {
    name: 'Speakers',
    href: '/admin/speakers',
    icon: FiMic,
  },
  {
    name: 'Sponsors',
    href: '/admin/sponsors',
    icon: FiStar,
  },
  {
    name: 'Payments',
    href: '/admin/payments',
    icon: FiCreditCard,
  },
  {
    name: 'Receipts',
    href: '/admin/receipts',
    icon: FiCheck,
  },
  {
    name: 'Documents',
    href: '/admin/documents',
    icon: FiFile,
  },
  {
    name: 'Email Center',
    href: '/admin/emails',
    icon: FiMail,
  },
  {
    name: 'Email Templates',
    href: '/admin/email-templates',
    icon: FiMail,
  },
  {
    name: 'Reports',
    href: '/admin/reports',
    icon: FiBarChart,
  },
  {
    name: 'Data Export',
    href: '/admin/export',
    icon: FiDownload,
  },
  {
    name: 'Database',
    href: '/admin/database',
    icon: FiDatabase,
    permission: 'database',
  },
  {
    name: 'Admin Users',
    href: '/admin/users',
    icon: FiShield,
    permission: 'users',
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: FiSettings,
    permission: 'settings',
  },
];

export default function AdminSidebar({
  adminUser,
  isOpen,
  onClose,
}: AdminSidebarProps) {
  const pathname = usePathname();

  const hasPermission = (permission?: string) => {
    if (!permission || !adminUser) return true;
    return adminUser.permissions[permission] === true;
  };

  const filteredNavItems = navItems.filter(item =>
    hasPermission(item.permission)
  );

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">IEPA</span>
              </div>
              <span className="font-semibold text-gray-900">Admin</span>
            </div>
            <button
              onClick={onClose}
              className="lg:hidden p-1 rounded-md hover:bg-gray-100"
            >
              <FiX className="w-5 h-5" />
            </button>
          </div>

          {/* Admin User Info */}
          {adminUser && (
            <div className="p-4 border-b">
              <div className="text-sm text-gray-600">Signed in as</div>
              <div className="font-medium text-gray-900">{adminUser.email}</div>
              <Badge
                variant={
                  adminUser.role === 'super_admin' ? 'default' : 'secondary'
                }
                className="mt-1"
              >
                {adminUser.role === 'super_admin' ? 'Super Admin' : 'Admin'}
              </Badge>
            </div>
          )}

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
            {filteredNavItems.map(item => {
              const isActive = pathname === item.href;
              const Icon = item.icon;

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={onClose}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.name}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="ml-auto">
                      {item.badge}
                    </Badge>
                  )}
                </Link>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t">
            <div className="text-xs text-gray-500">
              IEPA Conference Registration
            </div>
            <div className="text-xs text-gray-400">Admin Dashboard v1.0</div>
          </div>
        </div>
      </div>
    </>
  );
}
