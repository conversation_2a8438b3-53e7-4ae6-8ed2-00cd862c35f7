# Adding Stripe MCP to Augment Code

This guide explains how to add the Stripe MCP (Model Context Protocol) server to your Augment Code configuration.

## Quick Setup

### 1. Copy the MCP Configuration

**Option A: Secure Configuration (Recommended)**

Use this configuration that loads API keys from the project's .env.local file:

```json
{
  "mcpServers": {
    "stripe-iepa-custom": {
      "command": "npx",
      "args": ["tsx", "scripts/stripe-mcp-server.ts"],
      "cwd": "/Users/<USER>/Documents/iepa-conf-reg"
    },
    "stripe-iepa-official": {
      "command": "bash",
      "args": ["scripts/stripe-mcp-wrapper.sh"],
      "cwd": "/Users/<USER>/Documents/iepa-conf-reg"
    }
  }
}
```

**Option B: Direct Configuration (Less Secure)**

If environment variable substitution doesn't work in your Augment Code setup:

```json
{
  "mcpServers": {
    "stripe-iepa-custom": {
      "command": "npx",
      "args": ["tsx", "scripts/stripe-mcp-server.ts"],
      "cwd": "/Users/<USER>/Documents/iepa-conf-reg"
    },
    "stripe-iepa-official": {
      "command": "npx",
      "args": [
        "-y",
        "@stripe/mcp",
        "--tools=all",
        "--api-key=sk_test_zfudQUituJM6jCNko5vfOsxQ00zQCxoN3Y"
      ]
    }
  }
}
```

### 2. Verify API Key Configuration

The API key is automatically loaded from your project's `.env.local` file. Verify it's set correctly:

```bash
cd /Users/<USER>/Documents/iepa-conf-reg
grep STRIPE_SECRET_KEY .env.local
```

Should show:

```
STRIPE_SECRET_KEY=sk_test_zfudQUituJM6jCNko5vfOsxQ00zQCxoN3Y
```

**Note**: The custom server and wrapper script automatically load this from `.env.local`, so no additional environment setup is needed.

## Configuration Options

### Option 1: Custom Server (Recommended)

**Server Name**: `stripe-iepa-custom`

- **Pros**:
  - Comprehensive API coverage
  - Better error handling
  - Project-specific configuration
  - Graceful shutdown handling
- **Cons**:
  - Requires project directory access
  - Slightly more complex setup

### Option 2: Official Server

**Server Name**: `stripe-iepa-official`

- **Pros**:
  - Simpler setup
  - Always up-to-date
  - No project dependency
- **Cons**:
  - Less customization
  - Basic error handling

## Available Stripe Operations

Once configured, you can ask Augment Code to perform Stripe operations like:

### Customer Management

- "Create a new customer for the IEPA conference"
- "List all customers"
- "Update customer information"
- "Find customers by email"

### Payment Processing

- "Create a payment link for conference registration"
- "Generate a payment intent for $500"
- "List recent payments"
- "Check payment status"

### Product & Pricing

- "Create a product for conference tickets"
- "Set up pricing for different registration types"
- "List all products and prices"

### Subscriptions

- "Create a subscription for annual membership"
- "Cancel a subscription"
- "Update subscription pricing"

### Invoicing

- "Create an invoice for conference registration"
- "List unpaid invoices"
- "Mark invoice as paid"

### Financial Operations

- "Process a refund for $100"
- "Check account balance"
- "List recent refunds"

### Disputes & Support

- "List open disputes"
- "Update dispute information"

## Testing the Setup

### 1. Verify Configuration

Run the test script to ensure everything is working:

```bash
cd /Users/<USER>/Documents/iepa-conf-reg
npm run stripe:test
```

### 2. Test with Augment Code

Try asking Augment Code:

> "Can you check my Stripe account balance?"

> "Create a customer named 'Test User' with email '<EMAIL>'"

> "List my recent customers"

## Troubleshooting

### Common Issues

1. **"Command not found" error**:

   - Ensure `npx` is available in your PATH
   - Verify Node.js is installed

2. **"STRIPE_SECRET_KEY not found" error**:

   - Check environment variable is set: `echo $STRIPE_SECRET_KEY`
   - Restart Augment Code after setting the variable

3. **"Permission denied" error**:

   - Verify the project directory path is correct
   - Ensure you have read access to the project files

4. **"Invalid API key" error**:
   - Verify your Stripe API key is correct
   - Check if you're using test vs live keys appropriately

### Debug Steps

1. **Test the MCP server manually**:

   ```bash
   cd /Users/<USER>/Documents/iepa-conf-reg
   npm run stripe:mcp
   ```

2. **Check environment variables**:

   ```bash
   env | grep STRIPE
   ```

3. **Verify project dependencies**:
   ```bash
   cd /Users/<USER>/Documents/iepa-conf-reg
   npm list @stripe/agent-toolkit
   ```

## Security Notes

- **Use Test Keys**: Always use test API keys (`sk_test_...`) during development
- **Environment Variables**: Never hardcode API keys in configuration files
- **Access Control**: The MCP server has read/write access to your Stripe account
- **Rate Limits**: Be aware of Stripe's API rate limits when making frequent requests

## Advanced Configuration

### Custom Environment Variables

You can add additional environment variables to the configuration:

```json
{
  "env": {
    "STRIPE_SECRET_KEY": "${STRIPE_SECRET_KEY}",
    "STRIPE_WEBHOOK_SECRET": "${STRIPE_WEBHOOK_SECRET}",
    "NODE_ENV": "development"
  }
}
```

### Multiple Stripe Accounts

To work with multiple Stripe accounts, create separate server configurations:

```json
{
  "mcpServers": {
    "stripe-iepa-test": {
      "command": "npx",
      "args": ["tsx", "scripts/stripe-mcp-server.ts"],
      "cwd": "/Users/<USER>/Documents/iepa-conf-reg",
      "env": {
        "STRIPE_SECRET_KEY": "${STRIPE_TEST_KEY}"
      }
    },
    "stripe-iepa-live": {
      "command": "npx",
      "args": ["tsx", "scripts/stripe-mcp-server.ts"],
      "cwd": "/Users/<USER>/Documents/iepa-conf-reg",
      "env": {
        "STRIPE_SECRET_KEY": "${STRIPE_LIVE_KEY}"
      }
    }
  }
}
```

## Next Steps

1. **Add the configuration** to your Augment Code MCP settings
2. **Set your environment variable** with your Stripe API key
3. **Test the integration** with simple commands
4. **Explore advanced features** like subscription management and invoicing
5. **Set up webhooks** for real-time event handling (separate from MCP)

For more detailed information, see:

- `STRIPE_MCP_SETUP.md` - Complete setup guide
- `scripts/stripe-mcp-server.ts` - Custom server implementation
- `scripts/test-stripe-mcp.ts` - Testing utilities
