#!/usr/bin/env tsx

/**
 * Clean Test Registrations Script
 * 
 * This script safely removes test registrations from the database while preserving
 * production data. It identifies test data by common patterns in email addresses
 * and names.
 * 
 * Usage: npm run clean-test-data
 */

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test data patterns to identify and remove
const TEST_PATTERNS = {
  emails: [
    'test@',
    'demo@',
    'example@',
    'fake@',
    'sample@',
    '@test.',
    '@example.',
    '@demo.',
    'john.tester',
    'jane.test',
    'test.user',
    'demo.user',
    'playwright',
    'e2e.test',
    'iepa-test.com',
    'notewaredigital.com'
  ],
  names: [
    'test',
    'demo',
    'example',
    'fake',
    'sample',
    'john tester',
    'jane test',
    'test user',
    'demo user',
    'playwright',
    'e2e test'
  ],
  organizations: [
    'test',
    'demo',
    'example',
    'fake',
    'sample',
    'test company',
    'demo corp',
    'example inc',
    'playwright'
  ]
};

interface CleanupStats {
  attendees: number;
  speakers: number;
  sponsors: number;
  userProfiles: number;
  emailLogs: number;
  payments: number;
}

/**
 * Check if a value matches any test patterns
 */
function isTestData(value: string, patterns: string[]): boolean {
  if (!value) return false;
  const lowerValue = value.toLowerCase();
  return patterns.some(pattern => lowerValue.includes(pattern.toLowerCase()));
}

/**
 * Check if registration data appears to be test data
 */
function isTestRegistration(data: any): boolean {
  // Check email patterns
  if (data.email && isTestData(data.email, TEST_PATTERNS.emails)) {
    return true;
  }
  
  // Check contact email for sponsors
  if (data.contact_email && isTestData(data.contact_email, TEST_PATTERNS.emails)) {
    return true;
  }
  
  // Check name patterns
  const fullName = `${data.first_name || ''} ${data.last_name || ''}`.trim();
  const contactName = data.contact_name || '';
  
  if (isTestData(fullName, TEST_PATTERNS.names) || isTestData(contactName, TEST_PATTERNS.names)) {
    return true;
  }
  
  // Check organization patterns (if field exists)
  if (data.organization && isTestData(data.organization, TEST_PATTERNS.organizations)) {
    return true;
  }
  
  return false;
}

/**
 * Clean attendee registrations
 */
async function cleanAttendeeRegistrations(): Promise<number> {
  console.log('🔍 Scanning attendee registrations...');
  
  const { data: attendees, error } = await supabase
    .from('iepa_attendee_registrations')
    .select('*');
  
  if (error) {
    console.error('❌ Error fetching attendees:', error);
    return 0;
  }
  
  const testAttendees = attendees?.filter(isTestRegistration) || [];
  
  if (testAttendees.length === 0) {
    console.log('✅ No test attendee registrations found');
    return 0;
  }
  
  console.log(`📋 Found ${testAttendees.length} test attendee registrations:`);
  testAttendees.forEach(attendee => {
    console.log(`   - ${attendee.first_name} ${attendee.last_name} (${attendee.email})`);
  });
  
  const attendeeIds = testAttendees.map(a => a.id);
  
  const { error: deleteError } = await supabase
    .from('iepa_attendee_registrations')
    .delete()
    .in('id', attendeeIds);
  
  if (deleteError) {
    console.error('❌ Error deleting attendees:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${testAttendees.length} test attendee registrations`);
  return testAttendees.length;
}

/**
 * Clean speaker registrations
 */
async function cleanSpeakerRegistrations(): Promise<number> {
  console.log('🔍 Scanning speaker registrations...');
  
  const { data: speakers, error } = await supabase
    .from('iepa_speaker_registrations')
    .select('*');
  
  if (error) {
    console.error('❌ Error fetching speakers:', error);
    return 0;
  }
  
  const testSpeakers = speakers?.filter(isTestRegistration) || [];
  
  if (testSpeakers.length === 0) {
    console.log('✅ No test speaker registrations found');
    return 0;
  }
  
  console.log(`📋 Found ${testSpeakers.length} test speaker registrations:`);
  testSpeakers.forEach(speaker => {
    console.log(`   - ${speaker.first_name} ${speaker.last_name} (${speaker.email})`);
  });
  
  const speakerIds = testSpeakers.map(s => s.id);
  
  const { error: deleteError } = await supabase
    .from('iepa_speaker_registrations')
    .delete()
    .in('id', speakerIds);
  
  if (deleteError) {
    console.error('❌ Error deleting speakers:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${testSpeakers.length} test speaker registrations`);
  return testSpeakers.length;
}

/**
 * Clean sponsor registrations
 */
async function cleanSponsorRegistrations(): Promise<number> {
  console.log('🔍 Scanning sponsor registrations...');
  
  const { data: sponsors, error } = await supabase
    .from('iepa_sponsor_registrations')
    .select('*');
  
  if (error) {
    console.error('❌ Error fetching sponsors:', error);
    return 0;
  }
  
  const testSponsors = sponsors?.filter(isTestRegistration) || [];
  
  if (testSponsors.length === 0) {
    console.log('✅ No test sponsor registrations found');
    return 0;
  }
  
  console.log(`📋 Found ${testSponsors.length} test sponsor registrations:`);
  testSponsors.forEach(sponsor => {
    console.log(`   - ${sponsor.organization} (${sponsor.contact_email})`);
  });
  
  const sponsorIds = testSponsors.map(s => s.id);
  
  const { error: deleteError } = await supabase
    .from('iepa_sponsor_registrations')
    .delete()
    .in('id', sponsorIds);
  
  if (deleteError) {
    console.error('❌ Error deleting sponsors:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${testSponsors.length} test sponsor registrations`);
  return testSponsors.length;
}

/**
 * Clean user profiles for test users
 */
async function cleanUserProfiles(): Promise<number> {
  console.log('🔍 Scanning user profiles...');
  
  const { data: profiles, error } = await supabase
    .from('iepa_user_profiles')
    .select('*');
  
  if (error) {
    console.error('❌ Error fetching user profiles:', error);
    return 0;
  }
  
  const testProfiles = profiles?.filter(profile => 
    isTestData(profile.email || '', TEST_PATTERNS.emails)
  ) || [];
  
  if (testProfiles.length === 0) {
    console.log('✅ No test user profiles found');
    return 0;
  }
  
  console.log(`📋 Found ${testProfiles.length} test user profiles:`);
  testProfiles.forEach(profile => {
    console.log(`   - ${profile.first_name} ${profile.last_name} (${profile.email})`);
  });
  
  const profileIds = testProfiles.map(p => p.id);
  
  const { error: deleteError } = await supabase
    .from('iepa_user_profiles')
    .delete()
    .in('id', profileIds);
  
  if (deleteError) {
    console.error('❌ Error deleting user profiles:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${testProfiles.length} test user profiles`);
  return testProfiles.length;
}

/**
 * Clean test email logs
 */
async function cleanEmailLogs(): Promise<number> {
  console.log('🔍 Scanning email logs...');
  
  const { data: emailLogs, error } = await supabase
    .from('iepa_email_logs')
    .select('*')
    .or(`recipient_email.ilike.%test%,recipient_email.ilike.%demo%,recipient_email.ilike.%example%,recipient_email.ilike.%playwright%,recipient_email.ilike.%iepa-test%`);
  
  if (error) {
    console.error('❌ Error fetching email logs:', error);
    return 0;
  }
  
  if (!emailLogs || emailLogs.length === 0) {
    console.log('✅ No test email logs found');
    return 0;
  }
  
  console.log(`📋 Found ${emailLogs.length} test email logs`);
  
  const emailLogIds = emailLogs.map(log => log.id);
  
  const { error: deleteError } = await supabase
    .from('iepa_email_logs')
    .delete()
    .in('id', emailLogIds);
  
  if (deleteError) {
    console.error('❌ Error deleting email logs:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${emailLogs.length} test email logs`);
  return emailLogs.length;
}

/**
 * Main cleanup function
 */
async function cleanTestRegistrations(): Promise<void> {
  console.log('🧹 Starting test registration cleanup...\n');
  
  const stats: CleanupStats = {
    attendees: 0,
    speakers: 0,
    sponsors: 0,
    userProfiles: 0,
    emailLogs: 0,
    payments: 0
  };
  
  try {
    // Clean registrations
    stats.attendees = await cleanAttendeeRegistrations();
    stats.speakers = await cleanSpeakerRegistrations();
    stats.sponsors = await cleanSponsorRegistrations();
    
    // Clean related data
    stats.userProfiles = await cleanUserProfiles();
    stats.emailLogs = await cleanEmailLogs();
    
    // Summary
    console.log('\n📊 Cleanup Summary:');
    console.log(`   Attendees: ${stats.attendees} deleted`);
    console.log(`   Speakers: ${stats.speakers} deleted`);
    console.log(`   Sponsors: ${stats.sponsors} deleted`);
    console.log(`   User Profiles: ${stats.userProfiles} deleted`);
    console.log(`   Email Logs: ${stats.emailLogs} deleted`);
    
    const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
    
    if (total > 0) {
      console.log(`\n✅ Successfully cleaned ${total} test records from the database`);
      console.log('🎯 Database is now ready for fresh testing!');
    } else {
      console.log('\n✅ Database was already clean - no test data found');
    }
    
  } catch (error) {
    console.error('\n❌ Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the cleanup
if (require.main === module) {
  cleanTestRegistrations();
}

export { cleanTestRegistrations };
