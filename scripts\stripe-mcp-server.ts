#!/usr/bin/env node

/**
 * Stripe MCP (Model Context Protocol) Server
 *
 * This script sets up a Stripe MCP server that can be used with AI assistants
 * to interact with the Stripe API through function calling.
 *
 * Usage:
 * 1. Set your STRIPE_SECRET_KEY environment variable
 * 2. Run: npx tsx scripts/stripe-mcp-server.ts
 *
 * Or use the pre-built version:
 * npx -y @stripe/mcp --tools=all --api-key=YOUR_STRIPE_SECRET_KEY
 */

import { StripeAgentToolkit } from '@stripe/agent-toolkit/modelcontextprotocol';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function main() {
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

  if (!stripeSecretKey) {
    console.error('Error: STRIPE_SECRET_KEY environment variable is required');
    console.error(
      'Please set it in your .env.local file or as an environment variable'
    );
    process.exit(1);
  }

  // Initialize the Stripe Agent Toolkit with comprehensive configuration
  const server = new StripeAgentToolkit({
    secretKey: stripeSecretKey,
    configuration: {
      actions: {
        // Customer management
        customers: {
          create: true,
          read: true,
          update: true,
        },

        // Product and pricing
        products: {
          create: true,
          read: true,
          update: true,
        },
        prices: {
          create: true,
          read: true,
          update: true,
        },

        // Payment processing
        paymentIntents: {
          create: true,
          read: true,
          update: true,
        },
        paymentLinks: {
          create: true,
          read: true,
          update: true,
        },

        // Subscriptions
        subscriptions: {
          create: true,
          read: true,
          update: true,
        },

        // Invoicing
        invoices: {
          create: true,
          read: true,
          update: true,
        },
        invoiceItems: {
          create: true,
          read: true,
          update: true,
        },

        // Coupons and discounts
        coupons: {
          create: true,
          read: true,
          update: true,
        },

        // Financial operations
        refunds: {
          create: true,
          read: true,
          update: true,
        },

        // Account information
        balance: {
          read: true,
        },

        // Disputes
        disputes: {
          read: true,
          update: true,
        },
      },

      // Context for connected accounts (if needed)
      context: {
        // Uncomment and set if you need to work with connected accounts
        // account: "acct_123"
      },
    },
  });

  try {
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error('Stripe MCP Server running on stdio');
    console.error('Available Stripe API operations:');
    console.error('- Customer management (create, list, retrieve, update)');
    console.error('- Product and pricing management');
    console.error('- Payment processing (PaymentIntents, PaymentLinks)');
    console.error('- Subscription management');
    console.error('- Invoice and billing operations');
    console.error('- Refunds and disputes');
    console.error('- Account balance retrieval');
    console.error('- Coupon management');
  } catch (error) {
    console.error('Fatal error starting Stripe MCP Server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.error('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.error('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Start the server
main().catch(error => {
  console.error('Fatal error in main():', error);
  process.exit(1);
});
