import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { emailService } from '@/services/email';

export async function POST(request: NextRequest) {
  try {
    // Initialize Supabase admin client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Missing Supabase configuration' },
        { status: 500 }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    const { recipients, customEmails, subject, message, emailType } = await request.json();

    // Validate required fields
    if (!subject || !message) {
      return NextResponse.json(
        { error: 'Subject and message are required' },
        { status: 400 }
      );
    }

    console.log('[BULK-EMAIL] Processing bulk email request:', {
      recipients,
      subject,
      emailType,
      hasCustomEmails: !!customEmails
    });

    let recipientList: Array<{ email: string; name: string }> = [];

    // Get recipient emails based on selection
    if (recipients === 'custom') {
      if (!customEmails) {
        return NextResponse.json(
          { error: 'Custom emails are required when recipients is set to custom' },
          { status: 400 }
        );
      }

      // Parse custom email list
      const emails = customEmails
        .split(',')
        .map((email: string) => email.trim())
        .filter((email: string) => email.length > 0);

      recipientList = emails.map((email: string) => ({
        email,
        name: 'Conference Participant'
      }));

    } else {
      // Get emails from database based on recipient type
      let tableName = '';
      let nameFields = { first: 'first_name', last: 'last_name' };

      switch (recipients) {
        case 'all-attendees':
          tableName = 'iepa_attendee_registrations';
          break;
        case 'all-speakers':
          tableName = 'iepa_speaker_registrations';
          nameFields = { first: 'first_name', last: 'last_name' };
          break;
        case 'all-sponsors':
          tableName = 'iepa_sponsor_registrations';
          nameFields = { first: 'contact_name', last: '' }; // Sponsors might not have separate first/last names
          break;
        default:
          return NextResponse.json(
            { error: 'Invalid recipient type' },
            { status: 400 }
          );
      }

      console.log('[BULK-EMAIL] Fetching recipients from table:', tableName);

      // Fetch recipients from database
      const { data: registrations, error: fetchError } = await supabase
        .from(tableName)
        .select('email, first_name, last_name, contact_name, sponsor_name')
        .not('email', 'is', null);

      if (fetchError) {
        console.error('[BULK-EMAIL] Error fetching recipients:', fetchError);
        return NextResponse.json(
          { error: 'Failed to fetch recipients from database' },
          { status: 500 }
        );
      }

      // Format recipient list
      recipientList = registrations.map((reg: any) => {
        let name = 'Conference Participant';
        
        if (recipients === 'all-sponsors') {
          name = reg.contact_name || reg.sponsor_name || 'Sponsor Contact';
        } else {
          const firstName = reg.first_name || '';
          const lastName = reg.last_name || '';
          name = `${firstName} ${lastName}`.trim() || 'Conference Participant';
        }

        return {
          email: reg.email,
          name
        };
      });
    }

    if (recipientList.length === 0) {
      return NextResponse.json(
        { error: 'No recipients found' },
        { status: 400 }
      );
    }

    console.log('[BULK-EMAIL] Sending to', recipientList.length, 'recipients');

    // Send emails to all recipients
    const emailPromises = recipientList.map(async (recipient) => {
      try {
        // Create HTML email with IEPA branding
        const html = `
          <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
            <!-- IEPA Header -->
            <div style="background: #3A6CA5; padding: 20px; text-align: center;">
              <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Independent Energy Producers Association</h1>
              <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">2025 Annual Meeting</p>
            </div>
            
            <hr style="color: #3A6CA5; border: 2px solid #3A6CA5; margin: 0;" />
            
            <div style="padding: 30px;">
              <p>Dear ${recipient.name},</p>
              
              <div style="white-space: pre-wrap; line-height: 1.6;">${message}</div>
              
              <p style="margin-top: 30px;">Best regards,<br>
              <strong>IEPA Conference Team</strong><br>
              Independent Energy Producers Association</p>
            </div>
            
            <!-- Footer -->
            <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; color: #6c757d; font-size: 14px;">
                Independent Energy Producers Association<br>
                California's oldest nonprofit trade association representing independent energy facilities
              </p>
            </div>
          </div>
        `;

        const text = `
          Dear ${recipient.name},
          
          ${message}
          
          Best regards,
          IEPA Conference Team
          Independent Energy Producers Association
        `;

        return await emailService.sendEmail({
          to: recipient.email,
          subject,
          html,
          text,
        }, {
          emailType: emailType || 'custom',
          registrationType: recipients.replace('all-', '').replace('s', '') as 'attendee' | 'speaker' | 'sponsor',
        });

      } catch (error) {
        console.error('[BULK-EMAIL] Error sending to', recipient.email, ':', error);
        return false;
      }
    });

    // Wait for all emails to be sent
    const results = await Promise.all(emailPromises);
    const successCount = results.filter(result => result === true).length;
    const failureCount = results.length - successCount;

    console.log('[BULK-EMAIL] Bulk email completed:', {
      total: results.length,
      successful: successCount,
      failed: failureCount
    });

    return NextResponse.json({
      success: true,
      recipientCount: successCount,
      totalRecipients: results.length,
      failedCount: failureCount,
      message: `Email sent successfully to ${successCount} out of ${results.length} recipients.`
    });

  } catch (error) {
    console.error('[BULK-EMAIL] Error in bulk email API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
