-- Add missing fields to iepa_sponsor_registrations table
-- This migration adds contact information, sponsorship details, billing info, and other missing fields

-- Add contact information fields
ALTER TABLE iepa_sponsor_registrations 
ADD COLUMN IF NOT EXISTS contact_name TEXT,
ADD COLUMN IF NOT EXISTS contact_email TEXT,
ADD COLUMN IF NOT EXISTS contact_phone TEXT,
ADD COLUMN IF NOT EXISTS contact_title TEXT;

-- Add sponsorship details
ALTER TABLE iepa_sponsor_registrations 
ADD COLUMN IF NOT EXISTS sponsorship_level TEXT,
ADD COLUMN IF NOT EXISTS sponsorship_amount DECIMAL(10,2);

-- Add billing information
ALTER TABLE iepa_sponsor_registrations 
ADD COLUMN IF NOT EXISTS billing_address TEXT,
ADD COLUMN IF NOT EXISTS billing_city TEXT,
ADD COLUMN IF NOT EXISTS billing_state TEXT,
ADD COLUMN IF NOT EXISTS billing_zip TEXT,
ADD COLUMN IF NOT EXISTS billing_country TEXT DEFAULT 'United States';

-- Add additional information fields
ALTER TABLE iepa_sponsor_registrations 
ADD COLUMN IF NOT EXISTS marketing_goals TEXT,
ADD COLUMN IF NOT EXISTS exhibit_requirements TEXT,
ADD COLUMN IF NOT EXISTS special_requests TEXT,
ADD COLUMN IF NOT EXISTS attendee_count TEXT,
ADD COLUMN IF NOT EXISTS attendee_names TEXT;

-- Add golf participation
ALTER TABLE iepa_sponsor_registrations 
ADD COLUMN IF NOT EXISTS attending_golf BOOLEAN DEFAULT FALSE;

-- Update existing records to have default values where appropriate
UPDATE iepa_sponsor_registrations 
SET 
    billing_country = 'United States' 
WHERE billing_country IS NULL;

UPDATE iepa_sponsor_registrations 
SET 
    attending_golf = FALSE 
WHERE attending_golf IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN iepa_sponsor_registrations.contact_name IS 'Primary contact person name';
COMMENT ON COLUMN iepa_sponsor_registrations.contact_email IS 'Primary contact email address';
COMMENT ON COLUMN iepa_sponsor_registrations.contact_phone IS 'Primary contact phone number';
COMMENT ON COLUMN iepa_sponsor_registrations.contact_title IS 'Primary contact job title';
COMMENT ON COLUMN iepa_sponsor_registrations.sponsorship_level IS 'Sponsorship package level (bronze, silver, gold, platinum, diamond)';
COMMENT ON COLUMN iepa_sponsor_registrations.sponsorship_amount IS 'Sponsorship package amount in USD';
COMMENT ON COLUMN iepa_sponsor_registrations.attending_golf IS 'Whether sponsor representatives are attending golf tournament';
