// User Registration Hooks for IEPA 2025 Conference Registration
// React hooks for user registration data management

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  fetchUserRegistrations,
  fetchUserAttendeeRegistration,
  fetchUserSpeakerRegistration,
  fetchUserSponsorRegistration,
  fetchUserPaymentHistory,
  updateUserRegistration,
} from '@/services/userRegistrations';
import type {
  UserRegistrationsResponse,
  UserRegistrationDetails,
  UserPaymentRecord,
  UserRegistrationsState,
  UpdateRegistrationRequest,
  UpdateRegistrationResponse,
} from '@/types/userRegistrations';

/**
 * Main hook for all user registrations
 */
export function useUserRegistrations() {
  const { user } = useAuth();
  const [state, setState] = useState<UserRegistrationsState>({
    registrations: null,
    loading: true,
    error: null,
    lastFetched: null,
  });

  const fetchData = useCallback(async () => {
    if (!user?.id) {
      setState(prev => ({ ...prev, loading: false, error: 'User not authenticated' }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const data = await fetchUserRegistrations(user.id);
      setState({
        registrations: data,
        loading: false,
        error: null,
        lastFetched: new Date(),
      });
    } catch (error) {
      console.error('Error fetching user registrations:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch registrations',
      }));
    }
  }, [user?.id]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Computed values
  const hasRegistrations = useMemo(() => {
    if (!state.registrations) return false;
    return !!(state.registrations.attendee || state.registrations.speaker || state.registrations.sponsor);
  }, [state.registrations]);

  const registrationCount = useMemo(() => {
    if (!state.registrations) return 0;
    let count = 0;
    if (state.registrations.attendee) count++;
    if (state.registrations.speaker) count++;
    if (state.registrations.sponsor) count++;
    return count;
  }, [state.registrations]);

  const totalAmount = useMemo(() => {
    if (!state.registrations) return 0;
    let total = 0;
    if (state.registrations.attendee) total += state.registrations.attendee.financial.grandTotal;
    if (state.registrations.speaker) total += state.registrations.speaker.financial.grandTotal;
    if (state.registrations.sponsor) total += state.registrations.sponsor.financial.grandTotal;
    return total;
  }, [state.registrations]);

  return {
    registrations: state.registrations,
    loading: state.loading,
    error: state.error,
    lastFetched: state.lastFetched,
    hasRegistrations,
    registrationCount,
    totalAmount,
    refresh: fetchData,
  };
}

/**
 * Hook for specific registration type details
 */
export function useUserRegistrationDetails(type: 'attendee' | 'speaker' | 'sponsor') {
  const { user } = useAuth();
  const [registration, setRegistration] = useState<UserRegistrationDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!user?.id) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      let data: UserRegistrationDetails | null = null;
      switch (type) {
        case 'attendee':
          data = await fetchUserAttendeeRegistration(user.id);
          break;
        case 'speaker':
          data = await fetchUserSpeakerRegistration(user.id);
          break;
        case 'sponsor':
          data = await fetchUserSponsorRegistration(user.id);
          break;
      }

      setRegistration(data);
    } catch (err) {
      console.error(`Error fetching ${type} registration:`, err);
      setError(err instanceof Error ? err.message : `Failed to fetch ${type} registration`);
    } finally {
      setLoading(false);
    }
  }, [user?.id, type]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    registration,
    loading,
    error,
    refresh: fetchData,
  };
}

/**
 * Hook for user payment history
 */
export function useUserPaymentHistory() {
  const { user } = useAuth();
  const [payments, setPayments] = useState<UserPaymentRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!user?.id) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const data = await fetchUserPaymentHistory(user.id);
      setPayments(data);
    } catch (err) {
      console.error('Error fetching payment history:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch payment history');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    payments,
    loading,
    error,
    refresh: fetchData,
  };
}

/**
 * Hook for updating user registrations
 */
export function useUpdateUserRegistration() {
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateRegistration = useCallback(async (
    request: UpdateRegistrationRequest
  ): Promise<UpdateRegistrationResponse> => {
    try {
      setUpdating(true);
      setError(null);
      const result = await updateUserRegistration(request);
      
      if (!result.success) {
        setError(result.error || 'Update failed');
      }
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Update failed';
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setUpdating(false);
    }
  }, []);

  return {
    updateRegistration,
    updating,
    error,
  };
}

/**
 * Hook for registration summary data
 */
export function useRegistrationSummary() {
  const { registrations, loading, error } = useUserRegistrations();

  const summary = useMemo(() => {
    if (!registrations) return null;
    return registrations.summary;
  }, [registrations]);

  return {
    summary,
    loading,
    error,
  };
}

/**
 * Hook for user registration permissions
 */
export function useRegistrationPermissions() {
  const { user } = useAuth();
  const { registrations } = useUserRegistrations();

  const permissions = useMemo(() => {
    if (!user) {
      return {
        canRegisterAttendee: false,
        canRegisterSpeaker: false,
        canRegisterSponsor: false,
        canEditRegistrations: false,
        canViewPayments: false,
      };
    }

    return {
      canRegisterAttendee: !registrations?.attendee,
      canRegisterSpeaker: !registrations?.speaker,
      canRegisterSponsor: !registrations?.sponsor,
      canEditRegistrations: true,
      canViewPayments: true,
    };
  }, [user, registrations]);

  return permissions;
}
