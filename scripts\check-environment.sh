#!/bin/bash

# IEPA Conference Registration - Environment Check Script
# Verifies that all required services are running correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

echo "🔍 IEPA Conference Registration - Environment Check"
echo "=================================================="
echo ""

# Check Docker
print_status "Checking Docker..."
if docker info > /dev/null 2>&1; then
    print_success "Docker is running"
else
    print_error "Docker is not running"
    exit 1
fi

# Check Supabase CLI
print_status "Checking Supabase CLI..."
if command -v supabase &> /dev/null; then
    SUPABASE_VERSION=$(supabase --version)
    print_success "Supabase CLI installed: $SUPABASE_VERSION"
else
    print_error "Supabase CLI not found"
    exit 1
fi

# Check Supabase containers
print_status "Checking Supabase containers..."
if docker ps --format "table {{.Names}}" | grep -q "supabase_.*iepa-conf-reg"; then
    print_success "IEPA Supabase containers are running"
    
    # List running containers
    echo ""
    echo "Running IEPA containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}" | grep "iepa-conf-reg" | while read line; do
        echo "  $line"
    done
else
    print_warning "IEPA Supabase containers not found"
    echo "  Run: npm run supabase:setup"
fi

# Check ports
print_status "Checking port availability..."

check_port() {
    local port=$1
    local service=$2
    if lsof -i :$port > /dev/null 2>&1; then
        print_success "$service (port $port) is active"
    else
        print_warning "$service (port $port) is not active"
    fi
}

check_port 54321 "Supabase API"
check_port 54322 "Supabase Database"
check_port 54323 "Supabase Studio"
check_port 54324 "Email Testing"

# Check if port 6969 is available for Next.js
print_status "Checking Next.js port (6969)..."
if lsof -i :6969 > /dev/null 2>&1; then
    print_warning "Port 6969 is in use (Next.js may already be running)"
else
    print_success "Port 6969 is available for Next.js"
fi

# Check Node.js and npm
print_status "Checking Node.js environment..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "Node.js: $NODE_VERSION"
else
    print_error "Node.js not found"
fi

if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_success "npm: $NPM_VERSION"
else
    print_error "npm not found"
fi

# Check if dependencies are installed
print_status "Checking project dependencies..."
if [ -d "node_modules" ]; then
    print_success "Node modules are installed"
else
    print_warning "Node modules not found. Run: npm install"
fi

# Check environment files
print_status "Checking environment configuration..."
if [ -f ".env.local" ]; then
    print_success ".env.local exists"
else
    print_warning ".env.local not found (may use remote Supabase)"
fi

# Test Supabase connection
print_status "Testing Supabase connection..."
if curl -s http://127.0.0.1:54321/health > /dev/null 2>&1; then
    print_success "Supabase API is responding"
else
    print_warning "Supabase API not responding"
fi

echo ""
echo "🎯 Quick Start Commands:"
echo "  npm run supabase:setup  # Setup Supabase environment"
echo "  npm run dev             # Start Next.js development server"
echo "  npm run dev:full        # Setup Supabase + start Next.js"
echo ""
echo "🔗 Development URLs:"
echo "  App:      http://localhost:6969"
echo "  Studio:   http://127.0.0.1:54323"
echo "  API:      http://127.0.0.1:54321"
echo "  Email:    http://127.0.0.1:54324"
echo ""
print_success "Environment check completed!"
