'use client';

import { useState, useEffect } from 'react';
import { getAdminSettings, isProfileEditAllowed, type AdminSettings } from '@/services/adminSettings';

interface UseAdminSettingsReturn {
  settings: AdminSettings;
  isLoading: boolean;
  error: string | null;
  allowProfileEdits: boolean;
  refresh: () => Promise<void>;
}

/**
 * Hook to access admin settings
 */
export function useAdminSettings(): UseAdminSettingsReturn {
  const [settings, setSettings] = useState<AdminSettings>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [allowProfileEdits, setAllowProfileEdits] = useState(true);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [adminSettings, profileEditAllowed] = await Promise.all([
        getAdminSettings(),
        isProfileEditAllowed(),
      ]);

      setSettings(adminSettings);
      setAllowProfileEdits(profileEditAllowed);
    } catch (err) {
      console.error('Error loading admin settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load settings');
      // Set defaults on error
      setSettings({
        allow_profile_edits: true,
        registration_open: true,
        maintenance_mode: false,
      });
      setAllowProfileEdits(true);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const refresh = async () => {
    await loadSettings();
  };

  return {
    settings,
    isLoading,
    error,
    allowProfileEdits,
    refresh,
  };
}

/**
 * Simple hook to just check if profile edits are allowed
 */
export function useProfileEditPermission(): { allowProfileEdits: boolean; isLoading: boolean } {
  const [allowProfileEdits, setAllowProfileEdits] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        const allowed = await isProfileEditAllowed();
        setAllowProfileEdits(allowed);
      } catch (error) {
        console.error('Error checking profile edit permission:', error);
        setAllowProfileEdits(true); // Default to allowing edits
      } finally {
        setIsLoading(false);
      }
    };

    checkPermission();
  }, []);

  return { allowProfileEdits, isLoading };
}
