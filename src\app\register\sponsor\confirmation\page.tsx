'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@/components/ui';
import Link from 'next/link';
import { FaCheckCircle, FaHome, FaReceipt, FaEnvelope, FaFileInvoiceDollar } from 'react-icons/fa';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import { SPONSORSHIP_PACKAGES } from '@/lib/pricing-config';
import { IEPA_COMPANY_INFO } from '@/lib/pdf-generation/config';

interface SponsorRegistration {
  id: string;
  sponsor_name: string;
  sponsorship_level: string;
  sponsorship_amount: number;
  contact_name: string;
  contact_email: string;
  contact_phone: string;
  contact_title: string;
  billing_address: string;
  billing_city: string;
  billing_state: string;
  billing_zip: string;
  billing_country: string;
  created_at: string;
  payment_status: string;
}

function SponsorConfirmationContent() {
  const searchParams = useSearchParams();
  const [registration, setRegistration] = useState<SponsorRegistration | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const registrationId = searchParams?.get('id');

  useEffect(() => {
    if (!registrationId) {
      setError('No registration ID provided');
      setLoading(false);
      return;
    }

    const fetchRegistration = async () => {
      try {
        const { supabase } = await import('@/lib/supabase');
        
        const { data, error } = await supabase
          .from('iepa_sponsor_registrations')
          .select('*')
          .eq('id', registrationId)
          .single();

        if (error) {
          console.error('Error fetching registration:', error);
          setError('Registration not found');
          return;
        }

        setRegistration(data);
      } catch (err) {
        console.error('Error:', err);
        setError('Failed to load registration details');
      } finally {
        setLoading(false);
      }
    };

    fetchRegistration();
  }, [registrationId]);

  if (loading) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-2xl mx-auto text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="iepa-body mt-4">Loading confirmation details...</p>
          </div>
        </section>
      </div>
    );
  }

  if (error || !registration) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-2xl mx-auto">
            <Card className="border-red-200">
              <CardHeader className="text-center">
                <h1 className="iepa-heading-1 text-red-600">Error</h1>
              </CardHeader>
              <CardBody className="text-center">
                <p className="iepa-body mb-6">{error || 'Registration not found'}</p>
                <Button as={Link} href="/register" color="primary">
                  Return to Registration
                </Button>
              </CardBody>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  // Get sponsorship package details
  const sponsorshipPackage = SPONSORSHIP_PACKAGES.find(
    pkg => pkg.id === registration.sponsorship_level
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          {/* Success Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <FaCheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h1 className="iepa-heading-1 text-green-600 mb-2">
              Sponsorship Application Submitted!
            </h1>
            <p className="iepa-body text-gray-600">
              Thank you for your sponsorship of the IEPA {CONFERENCE_YEAR} Annual Conference
            </p>
          </div>

          {/* Registration Details */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="iepa-heading-2">Sponsorship Details</h2>
            </CardHeader>
            <CardBody className="space-y-6">
              {/* Organization Information */}
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="iepa-heading-3 mb-4">Organization Information</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="font-semibold">Organization:</span>
                      <span className="ml-2">{registration.sponsor_name}</span>
                    </div>
                    <div>
                      <span className="font-semibold">Sponsorship Level:</span>
                      <span className="ml-2">{sponsorshipPackage?.name || registration.sponsorship_level}</span>
                    </div>
                    <div>
                      <span className="font-semibold">Sponsorship Amount:</span>
                      <span className="ml-2 text-lg font-bold text-green-600">
                        {formatCurrency(registration.sponsorship_amount)}
                      </span>
                    </div>
                    <div>
                      <span className="font-semibold">Confirmation Number:</span>
                      <span className="ml-2 font-mono">{registration.id}</span>
                    </div>
                    <div>
                      <span className="font-semibold">Registration Date:</span>
                      <span className="ml-2">{formatDate(registration.created_at)}</span>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div>
                  <h3 className="iepa-heading-3 mb-4">Primary Contact</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="font-semibold">Name:</span>
                      <span className="ml-2">{registration.contact_name}</span>
                    </div>
                    <div>
                      <span className="font-semibold">Title:</span>
                      <span className="ml-2">{registration.contact_title}</span>
                    </div>
                    <div>
                      <span className="font-semibold">Email:</span>
                      <span className="ml-2">{registration.contact_email}</span>
                    </div>
                    <div>
                      <span className="font-semibold">Phone:</span>
                      <span className="ml-2">{registration.contact_phone}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Billing Information */}
              <div>
                <h3 className="iepa-heading-3 mb-4">Billing Information</h3>
                <div className="space-y-1">
                  <div>{registration.billing_address}</div>
                  <div>
                    {registration.billing_city}, {registration.billing_state} {registration.billing_zip}
                  </div>
                  <div>{registration.billing_country}</div>
                </div>
              </div>

              {/* Sponsorship Benefits */}
              {sponsorshipPackage && (
                <div>
                  <h3 className="iepa-heading-3 mb-4">Your Sponsorship Benefits</h3>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-semibold text-blue-800 mb-2">Registration Benefits</h4>
                        <ul className="space-y-1 text-sm text-blue-700">
                          {sponsorshipPackage.benefits.map((benefit, index) => (
                            <li key={index}>• {benefit}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold text-blue-800 mb-2">Marketing Benefits</h4>
                        <ul className="space-y-1 text-sm text-blue-700">
                          {sponsorshipPackage.marketingBenefits.map((benefit, index) => (
                            <li key={index}>• {benefit}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardBody>
          </Card>

          {/* Next Steps */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="iepa-heading-2">Next Steps</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <FaEnvelope className="w-5 h-5 text-blue-600 mt-1" />
                  <div>
                    <h4 className="font-semibold">Confirmation Email</h4>
                    <p className="text-sm text-gray-600">
                      A confirmation email with your invoice has been sent to {registration.contact_email}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <FaFileInvoiceDollar className="w-5 h-5 text-green-600 mt-1" />
                  <div>
                    <h4 className="font-semibold">Payment Instructions</h4>
                    <p className="text-sm text-gray-600 mb-2">
                      Please send your payment by check to:
                    </p>
                    <div className="bg-gray-50 p-3 rounded text-sm">
                      <div><strong>Pay to:</strong> {IEPA_COMPANY_INFO.name}</div>
                      <div><strong>Mail to:</strong></div>
                      <div>{IEPA_COMPANY_INFO.address.street}</div>
                      <div>{IEPA_COMPANY_INFO.address.city}, {IEPA_COMPANY_INFO.address.state} {IEPA_COMPANY_INFO.address.zipCode}</div>
                      <div>{IEPA_COMPANY_INFO.address.country}</div>
                      <div className="mt-2"><strong>Include:</strong> Confirmation Number {registration.id}</div>
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <FaReceipt className="w-5 h-5 text-purple-600 mt-1" />
                  <div>
                    <h4 className="font-semibold">Attendee Registration</h4>
                    <p className="text-sm text-gray-600">
                      After payment is received, you will receive discount codes for your complimentary attendee registrations.
                    </p>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              as={Link}
              href="/my-registrations"
              color="primary"
              size="lg"
              className="flex items-center gap-2"
            >
              <FaReceipt className="w-4 h-4" />
              View My Registrations
            </Button>

            <Button
              as={Link}
              href="/"
              variant="bordered"
              size="lg"
              className="flex items-center gap-2"
            >
              <FaHome className="w-4 h-4" />
              Return to Homepage
            </Button>
          </div>

          {/* Additional Information */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="iepa-heading-4 text-blue-800 mb-2">
              Important Information
            </h4>
            <ul className="iepa-body-small text-blue-700 space-y-1">
              <li>• Keep this confirmation for your records</li>
              <li>• Attendee discount codes will be sent after payment is received</li>
              <li>• Conference details will be sent closer to the event date</li>
              <li>• For questions, contact <NAME_EMAIL></li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  );
}

export default function SponsorConfirmationPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SponsorConfirmationContent />
    </Suspense>
  );
}
