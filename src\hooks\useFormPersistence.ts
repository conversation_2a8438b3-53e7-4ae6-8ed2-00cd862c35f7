// React Hook for Form Persistence
// Provides seamless integration with React forms for auto-save/restore functionality

import { useEffect, useRef, useState, useCallback } from 'react';
import {
  createFormPersistence,
  FormPersistenceConfig,
  FormPersistenceHook,
  formatDataAge,
} from '@/lib/form-persistence';

export interface UseFormPersistenceOptions extends FormPersistenceConfig {
  onDataRestored?: (data: Record<string, unknown>) => void;
  onDataSaved?: () => void;
  onDataCleared?: () => void;
  autoRestore?: boolean;
}

export interface UseFormPersistenceReturn {
  // Core functionality
  saveFormData: (data: Record<string, unknown>, step?: number) => void;
  loadFormData: () => Record<string, unknown> | null;
  clearFormData: () => void;

  // State information
  hasPersistedData: boolean;
  isDataExpired: boolean;
  dataAge: string | null;

  // UI state
  showRestorePrompt: boolean;
  setShowRestorePrompt: (show: boolean) => void;
  isAutoSaving: boolean;
  lastSavedAt: Date | null;

  // Actions
  restoreData: () => void;
  startFresh: () => void;
  dismissPrompt: () => void;
}

export function useFormPersistence(
  formData: Record<string, unknown>,
  setFormData: (data: Record<string, unknown>) => void,
  options: UseFormPersistenceOptions
): UseFormPersistenceReturn {
  const {
    onDataRestored,
    onDataSaved,
    onDataCleared,
    autoRestore = false,
    ...persistenceConfig
  } = options;

  // Create persistence instance
  const persistenceRef = useRef<FormPersistenceHook | null>(null);
  if (!persistenceRef.current) {
    persistenceRef.current = createFormPersistence(persistenceConfig);
  }
  const persistence = persistenceRef.current;

  // State for UI feedback
  const [hasPersistedData, setHasPersistedData] = useState(false);
  const [isDataExpired, setIsDataExpired] = useState(false);
  const [dataAge, setDataAge] = useState<string | null>(null);
  const [showRestorePrompt, setShowRestorePrompt] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSavedAt, setLastSavedAt] = useState<Date | null>(null);
  const [userDismissedPrompt, setUserDismissedPrompt] = useState(false);

  // Track if component is mounted to avoid state updates after unmount
  const isMountedRef = useRef(true);
  const lastSavedDataRef = useRef<Record<string, unknown> | null>(null);
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Check for persisted data on mount
  useEffect(() => {
    const checkPersistedData = () => {
      const hasPersisted = persistence.hasPersistedData();
      const expired = persistence.isDataExpired();
      const age = persistence.getDataAge();

      console.log('Checking persisted data:', { hasPersisted, expired, age });

      if (isMountedRef.current) {
        setHasPersistedData(hasPersisted);
        setIsDataExpired(expired);
        setDataAge(age ? formatDataAge(age) : null);

        // Show restore prompt if there's valid data, auto-restore is disabled, and user hasn't dismissed it
        if (hasPersisted && !expired && !autoRestore && !userDismissedPrompt) {
          console.log('Showing restore prompt');
          setShowRestorePrompt(true);
        }

        // Auto-restore if enabled and data is valid
        if (hasPersisted && !expired && autoRestore) {
          console.log('Auto-restoring data');
          const restoredData = persistence.loadFormData();
          if (restoredData) {
            console.log('Restored data:', restoredData);
            setFormData(restoredData);
            onDataRestored?.(restoredData);
          }
        }
      }
    };

    // Add a small delay to ensure the component is fully mounted
    const timer = setTimeout(checkPersistedData, 100);
    return () => clearTimeout(timer);
  }, [
    persistence,
    autoRestore,
    setFormData,
    onDataRestored,
    userDismissedPrompt,
  ]);

  // Auto-save form data when it changes (with debouncing)
  useEffect(() => {
    if (formData && Object.keys(formData).length > 0) {
      // Create a stable string representation for comparison
      const formDataString = JSON.stringify(formData);
      const lastSavedString = lastSavedDataRef.current
        ? JSON.stringify(lastSavedDataRef.current)
        : null;

      // Only save if data has actually changed
      if (formDataString !== lastSavedString) {
        console.log('🔄 Form data changed, triggering auto-save');
        setIsAutoSaving(true);

        // Debounce the save operation
        const timer = setTimeout(() => {
          if (isMountedRef.current) {
            // Create a deep copy to avoid reference issues
            const formDataCopy = JSON.parse(JSON.stringify(formData));
            persistence.saveFormData(formDataCopy);
            lastSavedDataRef.current = formDataCopy;

            setIsAutoSaving(false);
            setLastSavedAt(new Date());
            onDataSaved?.();
            console.log('✅ Auto-save completed');
          }
        }, 300); // Reduced debounce time

        return () => clearTimeout(timer);
      } else {
        console.log('🔄 Form data unchanged, skipping auto-save');
      }
    }
  }, [formData, persistence, onDataSaved]);

  // Listen for storage events from other tabs
  useEffect(() => {
    const handleStorageEvent = (e: StorageEvent) => {
      if (e.key === persistenceConfig.formKey) {
        const hasPersisted = persistence.hasPersistedData();
        const expired = persistence.isDataExpired();
        const age = persistence.getDataAge();

        if (isMountedRef.current) {
          setHasPersistedData(hasPersisted);
          setIsDataExpired(expired);
          setDataAge(age ? formatDataAge(age) : null);
        }
      }
    };

    window.addEventListener('storage', handleStorageEvent);
    return () => window.removeEventListener('storage', handleStorageEvent);
  }, [persistence, persistenceConfig.formKey]);

  // Listen for custom events from the persistence utility
  useEffect(() => {
    const handleFormDataSaved = (e: CustomEvent) => {
      if (
        e.detail.formKey === persistenceConfig.formKey &&
        isMountedRef.current
      ) {
        setLastSavedAt(new Date());
        onDataSaved?.();
      }
    };

    const handleFormDataCleared = (e: CustomEvent) => {
      if (
        e.detail.formKey === persistenceConfig.formKey &&
        isMountedRef.current
      ) {
        setHasPersistedData(false);
        setShowRestorePrompt(false);
        setDataAge(null);
        onDataCleared?.();
      }
    };

    window.addEventListener(
      'formDataSaved',
      handleFormDataSaved as EventListener
    );
    window.addEventListener(
      'formDataCleared',
      handleFormDataCleared as EventListener
    );

    return () => {
      window.removeEventListener(
        'formDataSaved',
        handleFormDataSaved as EventListener
      );
      window.removeEventListener(
        'formDataCleared',
        handleFormDataCleared as EventListener
      );
    };
  }, [persistenceConfig.formKey, onDataSaved, onDataCleared]);

  // Action handlers
  const restoreData = useCallback(() => {
    const restoredData = persistence.loadFormData();
    if (restoredData) {
      setFormData(restoredData);
      setShowRestorePrompt(false);
      setUserDismissedPrompt(true);
      onDataRestored?.(restoredData);
    }
  }, [persistence, setFormData, onDataRestored]);

  const startFresh = useCallback(() => {
    persistence.clearFormData();
    setShowRestorePrompt(false);
    setUserDismissedPrompt(true);
  }, [persistence]);

  const saveFormData = useCallback(
    (data: Record<string, unknown>, step?: number) => {
      persistence.saveFormData(data, step);
    },
    [persistence]
  );

  const loadFormData = useCallback(() => {
    return persistence.loadFormData();
  }, [persistence]);

  const clearFormData = useCallback(() => {
    persistence.clearFormData();
  }, [persistence]);

  const dismissPrompt = useCallback(() => {
    setShowRestorePrompt(false);
    setUserDismissedPrompt(true);
  }, []);

  return {
    // Core functionality
    saveFormData,
    loadFormData,
    clearFormData,

    // State information
    hasPersistedData,
    isDataExpired,
    dataAge,

    // UI state
    showRestorePrompt,
    setShowRestorePrompt,
    isAutoSaving,
    lastSavedAt,

    // Actions
    restoreData,
    startFresh,
    dismissPrompt,
  };
}

// Hook for multi-step forms (like attendee registration)
export function useMultiStepFormPersistence(
  formData: Record<string, unknown>,
  setFormData: (data: Record<string, unknown>) => void,
  currentStep: number,
  options: UseFormPersistenceOptions
): UseFormPersistenceReturn & {
  saveCurrentStep: () => void;
  restoreStep: (step: number) => void;
} {
  const persistence = useFormPersistence(formData, setFormData, options);

  const saveCurrentStep = useCallback(() => {
    persistence.saveFormData(formData, currentStep);
  }, [persistence, formData, currentStep]);

  const restoreStep = useCallback(() => {
    const restoredData = persistence.loadFormData();
    if (restoredData) {
      setFormData(restoredData);
    }
  }, [persistence, setFormData]);

  return {
    ...persistence,
    saveCurrentStep,
    restoreStep,
  };
}
