'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Check, ChevronRight } from 'lucide-react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  Badge,
  Separator,
} from '@/components/ui';

export default function StyleGuidePage() {
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const CopyButton = ({ text, label }: { text: string; label: string }) => (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => copyToClipboard(text, label)}
      className="h-8 w-8 p-0"
    >
      {copiedText === label ? (
        <Check className="h-4 w-4 text-green-600" />
      ) : (
        <Copy className="h-4 w-4" />
      )}
    </Button>
  );

  const ColorSwatch = ({
    name,
    value,
    cssVar,
    rgb,
    description
  }: {
    name: string;
    value: string;
    cssVar: string;
    rgb?: string;
    description?: string;
  }) => (
    <div className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
      <div
        className="w-12 h-12 rounded-lg border shadow-sm flex-shrink-0"
        style={{ backgroundColor: value }}
      />
      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm">{name}</div>
        <div className="text-xs text-gray-600 font-mono">{value}</div>
        {rgb && <div className="text-xs text-gray-600 font-mono">{rgb}</div>}
        <div className="text-xs text-gray-500 font-mono">{cssVar}</div>
        {description && <div className="text-xs text-gray-500 mt-1">{description}</div>}
      </div>
      <CopyButton text={cssVar} label={`${name}-css`} />
    </div>
  );

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="border-b bg-white sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold text-[var(--iepa-primary-blue)]">
                IEPA Style Guide
              </h1>
              <Badge variant="secondary">v2025.1</Badge>
            </div>
            <nav className="hidden md:flex items-center gap-6 text-sm">
              <a href="#typography" className="hover:text-[var(--iepa-primary-blue)] transition-colors">Typography</a>
              <a href="#colors" className="hover:text-[var(--iepa-primary-blue)] transition-colors">Colors</a>
              <a href="#buttons" className="hover:text-[var(--iepa-primary-blue)] transition-colors">Buttons</a>
              <a href="#forms" className="hover:text-[var(--iepa-primary-blue)] transition-colors">Forms</a>
              <a href="#layout" className="hover:text-[var(--iepa-primary-blue)] transition-colors">Layout</a>
              <a href="#components" className="hover:text-[var(--iepa-primary-blue)] transition-colors">Components</a>
            </nav>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Introduction */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Design System Documentation</h2>
          <p className="text-lg text-gray-600 max-w-3xl">
            This style guide documents the core design elements used throughout the IEPA 2025 Conference Registration application. 
            All components follow WCAG 2.2 AA accessibility standards and are built with shadcn/ui and Tailwind CSS.
          </p>
        </div>

        {/* Quick Navigation */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ChevronRight className="h-5 w-5" />
              Quick Navigation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {[
                { name: 'Typography', href: '#typography' },
                { name: 'Colors', href: '#colors' },
                { name: 'Buttons', href: '#buttons' },
                { name: 'Forms', href: '#forms' },
                { name: 'Layout', href: '#layout' },
                { name: 'Components', href: '#components' },
              ].map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="flex items-center justify-center p-3 border rounded-lg hover:bg-[var(--iepa-gray-50)] hover:border-[var(--iepa-primary-blue)] transition-all text-sm font-medium"
                >
                  {item.name}
                </a>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Typography Section */}
        <section id="typography" className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Typography</h2>
          
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Font Families</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Primary Font - Geist Sans</h4>
                  <p className="font-sans text-lg mb-2">The quick brown fox jumps over the lazy dog</p>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">font-family: &apos;Geist Sans&apos;, system-ui, sans-serif</code>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Monospace Font - Geist Mono</h4>
                  <p className="font-mono text-lg mb-2">The quick brown fox jumps over the lazy dog</p>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">font-family: &apos;Geist Mono&apos;, monospace</code>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Typography Scale</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-6">
                {/* Heading 1 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h1 className="iepa-heading-1">Heading 1 - Main Page Title</h1>
                    <CopyButton text="iepa-heading-1" label="h1-class" />
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>Mobile: 1.5rem (24px) • Tablet: 1.875rem (30px) • Desktop: 2.25rem (36px)</div>
                    <div>Font Weight: 700 • Line Height: 1.2 • Color: var(--iepa-primary-blue)</div>
                  </div>
                </div>

                {/* Heading 2 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h2 className="iepa-heading-2">Heading 2 - Section Title</h2>
                    <CopyButton text="iepa-heading-2" label="h2-class" />
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>Mobile: 1.25rem (20px) • Tablet: 1.5rem (24px) • Desktop: 1.875rem (30px)</div>
                    <div>Font Weight: 600 • Line Height: 1.3 • Color: var(--iepa-primary-blue)</div>
                  </div>
                </div>

                {/* Heading 3 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="iepa-heading-3">Heading 3 - Subsection Title</h3>
                    <CopyButton text="iepa-heading-3" label="h3-class" />
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>Mobile: 1.125rem (18px) • Tablet: 1.25rem (20px) • Desktop: 1.5rem (24px)</div>
                    <div>Font Weight: 500 • Line Height: 1.4 • Color: var(--iepa-gray-800)</div>
                  </div>
                </div>

                {/* Body Large */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <p className="iepa-body-large">Large body text - Important descriptions and introductory content</p>
                    <CopyButton text="iepa-body-large" label="body-large-class" />
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>Mobile: 1rem (16px) • Tablet+: 1.125rem (18px)</div>
                    <div>Font Weight: 400 • Line Height: 1.6 • Color: var(--iepa-gray-700)</div>
                  </div>
                </div>

                {/* Body Regular */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <p className="iepa-body">Regular body text - Standard content, form labels, and general information</p>
                    <CopyButton text="iepa-body" label="body-class" />
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>All screens: 0.875rem (14px)</div>
                    <div>Font Weight: 400 • Line Height: 1.5 • Color: var(--iepa-gray-600)</div>
                  </div>
                </div>

                {/* Body Small */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <p className="iepa-body-small">Small body text - Helper text, captions, and secondary information</p>
                    <CopyButton text="iepa-body-small" label="body-small-class" />
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>All screens: 0.75rem (12px)</div>
                    <div>Font Weight: 400 • Line Height: 1.4 • Color: var(--iepa-gray-500)</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Compact Typography Variants */}
          <Card>
            <CardHeader>
              <CardTitle>Compact Typography (Forms)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <h1 className="iepa-compact-heading-1">Compact Heading 1</h1>
                  <CopyButton text="iepa-compact-heading-1" label="compact-h1-class" />
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <h2 className="iepa-compact-heading-2">Compact Heading 2</h2>
                  <CopyButton text="iepa-compact-heading-2" label="compact-h2-class" />
                </div>
              </div>
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> Compact variants are used in registration forms and dense layouts to save vertical space.
                </p>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Colors Section */}
        <section id="colors" className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Color Palette</h2>
          
          <div className="grid lg:grid-cols-2 gap-6">
            {/* Brand Colors */}
            <Card>
              <CardHeader>
                <CardTitle>IEPA Brand Colors</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <ColorSwatch
                  name="Primary Blue"
                  value="#396da4"
                  rgb="rgb(57, 109, 164)"
                  cssVar="var(--iepa-primary-blue)"
                  description="Main brand color for headers, links, and primary actions"
                />
                <ColorSwatch
                  name="Primary Blue Light"
                  value="#4d7fb5"
                  rgb="rgb(77, 127, 181)"
                  cssVar="var(--iepa-primary-blue-light)"
                  description="Lighter variant for hover states"
                />
                <ColorSwatch
                  name="Primary Blue Dark"
                  value="#2d5a8a"
                  rgb="rgb(45, 90, 138)"
                  cssVar="var(--iepa-primary-blue-dark)"
                  description="Darker variant for active states"
                />
                <ColorSwatch
                  name="Secondary Green"
                  value="#5eae50"
                  rgb="rgb(94, 174, 80)"
                  cssVar="var(--iepa-secondary-green)"
                  description="CTA buttons and success states"
                />
                <ColorSwatch
                  name="Secondary Green Light"
                  value="#72c164"
                  rgb="rgb(114, 193, 100)"
                  cssVar="var(--iepa-secondary-green-light)"
                  description="Lighter green variant"
                />
                <ColorSwatch
                  name="Secondary Green Dark"
                  value="#4a9b3c"
                  rgb="rgb(74, 155, 60)"
                  cssVar="var(--iepa-secondary-green-dark)"
                  description="Darker green variant"
                />
                <ColorSwatch
                  name="Accent Teal"
                  value="#17a2b8"
                  rgb="rgb(23, 162, 184)"
                  cssVar="var(--iepa-accent-teal)"
                  description="Accent color for highlights and info states"
                />
              </CardContent>
            </Card>

            {/* Neutral Colors */}
            <Card>
              <CardHeader>
                <CardTitle>Neutral Colors</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <ColorSwatch
                  name="Gray 50"
                  value="#f8f9fa"
                  rgb="rgb(248, 249, 250)"
                  cssVar="var(--iepa-gray-50)"
                  description="Light background color"
                />
                <ColorSwatch
                  name="Gray 100"
                  value="#e9ecef"
                  rgb="rgb(233, 236, 239)"
                  cssVar="var(--iepa-gray-100)"
                  description="Very light gray for subtle backgrounds"
                />
                <ColorSwatch
                  name="Gray 200"
                  value="#dee2e6"
                  rgb="rgb(222, 226, 230)"
                  cssVar="var(--iepa-gray-200)"
                  description="Border color for form elements"
                />
                <ColorSwatch
                  name="Gray 300"
                  value="#ced4da"
                  rgb="rgb(206, 212, 218)"
                  cssVar="var(--iepa-gray-300)"
                  description="Default border color"
                />
                <ColorSwatch
                  name="Gray 400"
                  value="#adb5bd"
                  rgb="rgb(173, 181, 189)"
                  cssVar="var(--iepa-gray-400)"
                  description="Placeholder text color"
                />
                <ColorSwatch
                  name="Gray 500"
                  value="#6c757d"
                  rgb="rgb(108, 117, 125)"
                  cssVar="var(--iepa-gray-500)"
                  description="Muted text color"
                />
                <ColorSwatch
                  name="Gray 600"
                  value="#495057"
                  rgb="rgb(73, 80, 87)"
                  cssVar="var(--iepa-gray-600)"
                  description="Secondary text color"
                />
                <ColorSwatch
                  name="Gray 700"
                  value="#343a40"
                  rgb="rgb(52, 58, 64)"
                  cssVar="var(--iepa-gray-700)"
                  description="Dark text color"
                />
                <ColorSwatch
                  name="Gray 800"
                  value="#212529"
                  rgb="rgb(33, 37, 41)"
                  cssVar="var(--iepa-gray-800)"
                  description="Primary text color"
                />
                <ColorSwatch
                  name="Gray 900"
                  value="#1a1e21"
                  rgb="rgb(26, 30, 33)"
                  cssVar="var(--iepa-gray-900)"
                  description="Darkest gray"
                />
              </CardContent>
            </Card>
            {/* Status Colors */}
            <Card>
              <CardHeader>
                <CardTitle>Status Colors</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <ColorSwatch
                  name="Success"
                  value="#28a745"
                  rgb="rgb(40, 167, 69)"
                  cssVar="var(--iepa-success)"
                  description="Success messages and positive actions"
                />
                <ColorSwatch
                  name="Warning"
                  value="#ffc107"
                  rgb="rgb(255, 193, 7)"
                  cssVar="var(--iepa-warning)"
                  description="Warning messages and caution states"
                />
                <ColorSwatch
                  name="Danger"
                  value="#dc3545"
                  rgb="rgb(220, 53, 69)"
                  cssVar="var(--iepa-danger)"
                  description="Error messages and destructive actions"
                />
                <ColorSwatch
                  name="Info"
                  value="#17a2b8"
                  rgb="rgb(23, 162, 184)"
                  cssVar="var(--iepa-info)"
                  description="Informational messages (same as accent teal)"
                />
              </CardContent>
            </Card>

            {/* Background & Text Colors */}
            <Card>
              <CardHeader>
                <CardTitle>Background & Text Colors</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <ColorSwatch
                  name="Primary Background"
                  value="#ffffff"
                  rgb="rgb(255, 255, 255)"
                  cssVar="var(--iepa-bg-primary)"
                  description="Main background color"
                />
                <ColorSwatch
                  name="Secondary Background"
                  value="#f8f9fa"
                  rgb="rgb(248, 249, 250)"
                  cssVar="var(--iepa-bg-secondary)"
                  description="Alternate background color"
                />
                <ColorSwatch
                  name="Primary Text"
                  value="#212529"
                  rgb="rgb(33, 37, 41)"
                  cssVar="var(--iepa-text-primary)"
                  description="Main text color"
                />
                <ColorSwatch
                  name="Secondary Text"
                  value="#495057"
                  rgb="rgb(73, 80, 87)"
                  cssVar="var(--iepa-text-secondary)"
                  description="Secondary text color"
                />
                <ColorSwatch
                  name="Muted Text"
                  value="#6c757d"
                  rgb="rgb(108, 117, 125)"
                  cssVar="var(--iepa-text-muted)"
                  description="Muted text color"
                />
                <ColorSwatch
                  name="White Text"
                  value="#ffffff"
                  rgb="rgb(255, 255, 255)"
                  cssVar="var(--iepa-text-white)"
                  description="White text for dark backgrounds"
                />
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Buttons Section */}
        <section id="buttons" className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Buttons</h2>

          <div className="grid lg:grid-cols-2 gap-6">
            {/* Button Variants */}
            <Card>
              <CardHeader>
                <CardTitle>Button Variants</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Button variant="default">Default Button</Button>
                      <CopyButton text='<Button variant="default">Default</Button>' label="btn-default" />
                    </div>
                    <p className="text-sm text-gray-600">Primary action button with IEPA blue background</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Button variant="secondary">Secondary Button</Button>
                      <CopyButton text='<Button variant="secondary">Secondary</Button>' label="btn-secondary" />
                    </div>
                    <p className="text-sm text-gray-600">Secondary actions with light background</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Button variant="destructive">Destructive Button</Button>
                      <CopyButton text='<Button variant="destructive">Destructive</Button>' label="btn-destructive" />
                    </div>
                    <p className="text-sm text-gray-600">Dangerous actions like delete or cancel</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Button variant="outline">Outline Button</Button>
                      <CopyButton text='<Button variant="outline">Outline</Button>' label="btn-outline" />
                    </div>
                    <p className="text-sm text-gray-600">Subtle actions with border only</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Button variant="ghost">Ghost Button</Button>
                      <CopyButton text='<Button variant="ghost">Ghost</Button>' label="btn-ghost" />
                    </div>
                    <p className="text-sm text-gray-600">Minimal styling for tertiary actions</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Button Sizes */}
            <Card>
              <CardHeader>
                <CardTitle>Button Sizes</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Button size="sm">Small Button</Button>
                      <CopyButton text='<Button size="sm">Small</Button>' label="btn-sm" />
                    </div>
                    <p className="text-sm text-gray-600">Compact size for tight spaces • Height: 32px • Padding: 8px 12px</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Button size="default">Default Size</Button>
                      <CopyButton text='<Button size="default">Default</Button>' label="btn-default-size" />
                    </div>
                    <p className="text-sm text-gray-600">Standard size for most use cases • Height: 40px • Padding: 10px 16px</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Button size="lg">Large Button</Button>
                      <CopyButton text='<Button size="lg">Large</Button>' label="btn-lg" />
                    </div>
                    <p className="text-sm text-gray-600">Prominent size for primary CTAs • Height: 44px • Padding: 12px 24px</p>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                  <p className="text-sm text-blue-800">
                    <strong>Accessibility:</strong> All buttons meet the minimum 44px touch target requirement for mobile devices.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Button States */}
            <Card>
              <CardHeader>
                <CardTitle>Button States</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Button>Normal State</Button>
                    <span className="text-sm text-gray-500">Default appearance</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <Button className="hover:bg-[var(--iepa-primary-blue-dark)]">Hover State</Button>
                    <span className="text-sm text-gray-500">On mouse hover</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <Button disabled>Disabled State</Button>
                    <CopyButton text='<Button disabled>Disabled</Button>' label="btn-disabled" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* IEPA Specific Buttons */}
            <Card>
              <CardHeader>
                <CardTitle>IEPA Specific Styles</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Button className="bg-[var(--iepa-secondary-green)] hover:bg-[var(--iepa-secondary-green-dark)]">
                      CTA Button
                    </Button>
                    <CopyButton text='className="bg-[var(--iepa-secondary-green)]"' label="btn-cta" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Button className="bg-[var(--iepa-primary-blue)] hover:bg-[var(--iepa-primary-blue-dark)]">
                      Primary Action
                    </Button>
                    <CopyButton text='className="bg-[var(--iepa-primary-blue)]"' label="btn-primary" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Button variant="outline" className="border-[var(--iepa-accent-teal)] text-[var(--iepa-accent-teal)]">
                      Accent Button
                    </Button>
                    <CopyButton text='className="border-[var(--iepa-accent-teal)]"' label="btn-accent" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Forms Section */}
        <section id="forms" className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Form Elements</h2>

          <div className="grid lg:grid-cols-2 gap-6">
            {/* Input Fields */}
            <Card>
              <CardHeader>
                <CardTitle>Input Fields</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Default Input</label>
                    <input
                      type="text"
                      placeholder="Enter text here..."
                      className="w-full px-3 py-2 border border-[var(--iepa-gray-300)] rounded-md focus:border-[var(--iepa-primary-blue)] focus:ring-2 focus:ring-[var(--iepa-primary-blue)]/20 outline-none"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Email Input</label>
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      className="w-full px-3 py-2 border border-[var(--iepa-gray-300)] rounded-md focus:border-[var(--iepa-primary-blue)] focus:ring-2 focus:ring-[var(--iepa-primary-blue)]/20 outline-none"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Phone Input</label>
                    <input
                      type="tel"
                      placeholder="(*************"
                      className="w-full px-3 py-2 border border-[var(--iepa-gray-300)] rounded-md focus:border-[var(--iepa-primary-blue)] focus:ring-2 focus:ring-[var(--iepa-primary-blue)]/20 outline-none"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Disabled Input</label>
                    <input
                      type="text"
                      placeholder="Disabled field"
                      disabled
                      className="w-full px-3 py-2 border border-[var(--iepa-gray-300)] rounded-md bg-gray-50 text-gray-500 cursor-not-allowed"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Textarea */}
            <Card>
              <CardHeader>
                <CardTitle>Textarea</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Message</label>
                  <textarea
                    placeholder="Enter your message here..."
                    rows={4}
                    className="w-full px-3 py-2 border border-[var(--iepa-gray-300)] rounded-md focus:border-[var(--iepa-primary-blue)] focus:ring-2 focus:ring-[var(--iepa-primary-blue)]/20 outline-none resize-vertical"
                  />
                  <p className="text-sm text-[var(--iepa-gray-600)] mt-1">Helper text for additional context</p>
                </div>
              </CardContent>
            </Card>

            {/* Checkboxes */}
            <Card>
              <CardHeader>
                <CardTitle>Checkboxes</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="checkbox1"
                      className="w-4 h-4 text-[var(--iepa-primary-blue)] border-[var(--iepa-gray-300)] rounded focus:ring-[var(--iepa-primary-blue)] focus:ring-2"
                    />
                    <label htmlFor="checkbox1" className="text-sm font-medium">Default checkbox</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="checkbox2"
                      defaultChecked
                      className="w-4 h-4 text-[var(--iepa-primary-blue)] border-[var(--iepa-gray-300)] rounded focus:ring-[var(--iepa-primary-blue)] focus:ring-2"
                    />
                    <label htmlFor="checkbox2" className="text-sm font-medium">Checked checkbox</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="checkbox3"
                      disabled
                      className="w-4 h-4 text-[var(--iepa-primary-blue)] border-[var(--iepa-gray-300)] rounded focus:ring-[var(--iepa-primary-blue)] focus:ring-2 opacity-50 cursor-not-allowed"
                    />
                    <label htmlFor="checkbox3" className="text-sm font-medium text-gray-500">Disabled checkbox</label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Radio Buttons */}
            <Card>
              <CardHeader>
                <CardTitle>Radio Buttons</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="radio1"
                      name="radio-group"
                      className="w-4 h-4 text-[var(--iepa-primary-blue)] border-[var(--iepa-gray-300)] focus:ring-[var(--iepa-primary-blue)] focus:ring-2"
                    />
                    <label htmlFor="radio1" className="text-sm font-medium">Option 1</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="radio2"
                      name="radio-group"
                      defaultChecked
                      className="w-4 h-4 text-[var(--iepa-primary-blue)] border-[var(--iepa-gray-300)] focus:ring-[var(--iepa-primary-blue)] focus:ring-2"
                    />
                    <label htmlFor="radio2" className="text-sm font-medium">Option 2 (Selected)</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="radio3"
                      name="radio-group"
                      className="w-4 h-4 text-[var(--iepa-primary-blue)] border-[var(--iepa-gray-300)] focus:ring-[var(--iepa-primary-blue)] focus:ring-2"
                    />
                    <label htmlFor="radio3" className="text-sm font-medium">Option 3</label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Select Dropdowns */}
            <Card>
              <CardHeader>
                <CardTitle>Select Dropdowns</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Standard Select</label>
                    <select className="w-full px-3 py-2 border border-[var(--iepa-gray-300)] rounded-md focus:border-[var(--iepa-primary-blue)] focus:ring-2 focus:ring-[var(--iepa-primary-blue)]/20 outline-none bg-white">
                      <option>Choose an option...</option>
                      <option>Option 1</option>
                      <option>Option 2</option>
                      <option>Option 3</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Disabled Select</label>
                    <select disabled className="w-full px-3 py-2 border border-[var(--iepa-gray-300)] rounded-md bg-gray-50 text-gray-500 cursor-not-allowed">
                      <option>Disabled dropdown</option>
                    </select>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                  <p className="text-sm text-green-800">
                    <strong>Note:</strong> For complex dropdowns, use the shadcn/ui Select component for better styling and accessibility.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Form States */}
            <Card>
              <CardHeader>
                <CardTitle>Form States & Validation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Success State</label>
                    <input
                      type="text"
                      value="Valid input"
                      className="w-full px-3 py-2 border border-green-500 rounded-md focus:border-green-500 focus:ring-2 focus:ring-green-500/20 outline-none"
                    />
                    <p className="text-sm text-green-600 mt-1">✓ This field is valid</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Error State</label>
                    <input
                      type="text"
                      value="Invalid input"
                      className="w-full px-3 py-2 border border-red-500 rounded-md focus:border-red-500 focus:ring-2 focus:ring-red-500/20 outline-none"
                    />
                    <p className="text-sm text-red-600 mt-1">⚠ This field has an error</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Required Field</label>
                    <input
                      type="text"
                      placeholder="This field is required"
                      className="w-full px-3 py-2 border border-[var(--iepa-gray-300)] rounded-md focus:border-[var(--iepa-primary-blue)] focus:ring-2 focus:ring-[var(--iepa-primary-blue)]/20 outline-none"
                    />
                    <p className="text-sm text-gray-600 mt-1">* Required field</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Layout and Spacing Section */}
        <section id="layout" className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Layout & Spacing</h2>

          <div className="grid lg:grid-cols-2 gap-6">
            {/* Responsive Padding */}
            <Card>
              <CardHeader>
                <CardTitle>Responsive Padding Standards</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="p-4 bg-[var(--iepa-gray-50)] border rounded">
                    <div className="bg-white p-4 border-2 border-dashed border-[var(--iepa-primary-blue)]">
                      <p className="text-sm font-medium">Mobile: 1rem (16px)</p>
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded mt-1 inline-block">p-4</code>
                    </div>
                  </div>
                  <div className="p-6 bg-[var(--iepa-gray-50)] border rounded">
                    <div className="bg-white p-6 border-2 border-dashed border-[var(--iepa-secondary-green)]">
                      <p className="text-sm font-medium">Tablet: 1.5rem (24px)</p>
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded mt-1 inline-block">sm:p-6</code>
                    </div>
                  </div>
                  <div className="p-8 bg-[var(--iepa-gray-50)] border rounded">
                    <div className="bg-white p-8 border-2 border-dashed border-[var(--iepa-accent-teal)]">
                      <p className="text-sm font-medium">Desktop: 2rem (32px)</p>
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded mt-1 inline-block">lg:p-8</code>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Breakpoints */}
            <Card>
              <CardHeader>
                <CardTitle>Breakpoints</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-[var(--iepa-gray-50)] rounded">
                    <span className="font-medium">Mobile</span>
                    <code className="text-sm bg-white px-2 py-1 rounded">0px - 639px</code>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-[var(--iepa-gray-50)] rounded">
                    <span className="font-medium">Tablet</span>
                    <code className="text-sm bg-white px-2 py-1 rounded">640px - 1023px</code>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-[var(--iepa-gray-50)] rounded">
                    <span className="font-medium">Desktop</span>
                    <code className="text-sm bg-white px-2 py-1 rounded">1024px+</code>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                  <p className="text-sm text-blue-800">
                    <strong>Note:</strong> All components are designed mobile-first and scale up progressively.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Grid System */}
            <Card>
              <CardHeader>
                <CardTitle>Grid System</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium mb-2">Single Column (Mobile)</p>
                    <div className="grid grid-cols-1 gap-2">
                      <div className="bg-[var(--iepa-primary-blue)] text-white p-2 rounded text-center text-sm">Col 1</div>
                    </div>
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded mt-1 inline-block">grid-cols-1</code>
                  </div>
                  <div>
                    <p className="text-sm font-medium mb-2">Two Columns (Tablet)</p>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-[var(--iepa-secondary-green)] text-white p-2 rounded text-center text-sm">Col 1</div>
                      <div className="bg-[var(--iepa-secondary-green)] text-white p-2 rounded text-center text-sm">Col 2</div>
                    </div>
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded mt-1 inline-block">md:grid-cols-2</code>
                  </div>
                  <div>
                    <p className="text-sm font-medium mb-2">Three Columns (Desktop)</p>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="bg-[var(--iepa-accent-teal)] text-white p-2 rounded text-center text-sm">Col 1</div>
                      <div className="bg-[var(--iepa-accent-teal)] text-white p-2 rounded text-center text-sm">Col 2</div>
                      <div className="bg-[var(--iepa-accent-teal)] text-white p-2 rounded text-center text-sm">Col 3</div>
                    </div>
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded mt-1 inline-block">lg:grid-cols-3</code>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Spacing Scale */}
            <Card>
              <CardHeader>
                <CardTitle>Spacing Scale</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  {[
                    { name: 'xs', value: '0.25rem', class: 'space-1' },
                    { name: 'sm', value: '0.5rem', class: 'space-2' },
                    { name: 'md', value: '1rem', class: 'space-4' },
                    { name: 'lg', value: '1.5rem', class: 'space-6' },
                    { name: 'xl', value: '2rem', class: 'space-8' },
                    { name: '2xl', value: '3rem', class: 'space-12' },
                  ].map((space) => (
                    <div key={space.name} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-3">
                        <div
                          className="bg-[var(--iepa-primary-blue)] rounded"
                          style={{ width: space.value, height: '1rem' }}
                        />
                        <span className="text-sm font-medium">{space.name}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm">{space.value}</div>
                        <code className="text-xs bg-gray-100 px-1 rounded">{space.class}</code>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Components Section */}
        <section id="components" className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">UI Components</h2>

          <div className="grid lg:grid-cols-2 gap-6">
            {/* Cards */}
            <Card>
              <CardHeader>
                <CardTitle>Cards</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <Card className="p-4">
                    <h4 className="font-semibold mb-2">Basic Card</h4>
                    <p className="text-sm text-gray-600">Simple card with content and padding.</p>
                  </Card>
                  <Card className="border-[var(--iepa-primary-blue)] border-2">
                    <CardHeader>
                      <CardTitle>Card with Header</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600">Card with structured header and content areas.</p>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>

            {/* Badges */}
            <Card>
              <CardHeader>
                <CardTitle>Badges</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge variant="default">Default</Badge>
                  <Badge variant="secondary">Secondary</Badge>
                  <Badge variant="destructive">Destructive</Badge>
                  <Badge variant="outline">Outline</Badge>
                  <Badge className="bg-[var(--iepa-secondary-green)] text-white">Success</Badge>
                  <Badge className="bg-[var(--iepa-accent-teal)] text-white">Info</Badge>
                </div>
                <div className="mt-4">
                  <CopyButton text='<Badge variant="default">Badge</Badge>' label="badge-code" />
                </div>
              </CardContent>
            </Card>

            {/* Icons */}
            <Card>
              <CardHeader>
                <CardTitle>Icons (Lucide)</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-4 gap-4">
                  <div className="flex flex-col items-center gap-2">
                    <Copy className="h-6 w-6 text-[var(--iepa-primary-blue)]" />
                    <span className="text-xs">Copy</span>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <Check className="h-6 w-6 text-[var(--iepa-secondary-green)]" />
                    <span className="text-xs">Check</span>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <ChevronRight className="h-6 w-6 text-[var(--iepa-accent-teal)]" />
                    <span className="text-xs">Chevron</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-4">
                  Icons are from Lucide React. Use consistent sizing: h-4 w-4 (16px), h-5 w-5 (20px), h-6 w-6 (24px).
                </p>
              </CardContent>
            </Card>

            {/* Alerts & Status Messages */}
            <Card>
              <CardHeader>
                <CardTitle>Alerts & Status Messages</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="iepa-status-success">
                    <strong>Success:</strong> Your registration has been submitted successfully!
                  </div>
                  <div className="iepa-status-warning">
                    <strong>Warning:</strong> Your session will expire in 5 minutes.
                  </div>
                  <div className="iepa-status-error">
                    <strong>Error:</strong> Please correct the errors below and try again.
                  </div>
                  <div className="iepa-status-info">
                    <strong>Info:</strong> Early bird pricing ends on March 1st, 2025.
                  </div>
                </div>
                <div className="mt-4">
                  <CopyButton text="iepa-status-success" label="status-classes" />
                </div>
              </CardContent>
            </Card>

            {/* Separators */}
            <Card>
              <CardHeader>
                <CardTitle>Separators & Dividers</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium mb-2">Horizontal Separator</p>
                    <Separator />
                    <CopyButton text="<Separator />" label="separator-horizontal" />
                  </div>
                  <div>
                    <p className="text-sm font-medium mb-2">Vertical Separator</p>
                    <div className="flex items-center gap-4 h-8">
                      <span className="text-sm">Item 1</span>
                      <Separator orientation="vertical" />
                      <span className="text-sm">Item 2</span>
                      <Separator orientation="vertical" />
                      <span className="text-sm">Item 3</span>
                    </div>
                    <div className="mt-2">
                      <CopyButton text='<Separator orientation="vertical" />' label="separator-vertical" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Custom IEPA Components */}
            <Card>
              <CardHeader>
                <CardTitle>Custom IEPA Components</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Registration Card Radio</h4>
                    <div className="p-4 border border-[var(--iepa-primary-blue)] rounded-lg bg-[var(--iepa-primary-blue)]/5">
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="font-medium">IEPA Member</h5>
                          <p className="text-sm text-gray-600">$450 registration fee</p>
                        </div>
                        <div className="w-4 h-4 rounded-full border-2 border-[var(--iepa-primary-blue)] bg-[var(--iepa-primary-blue)]"></div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">Used for registration type selection with visual feedback</p>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Sponsor Card Radio</h4>
                    <div className="p-4 border border-[var(--iepa-secondary-green)] rounded-lg bg-[var(--iepa-secondary-green)]/5">
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="font-medium">Gold Sponsor</h5>
                          <p className="text-sm text-gray-600">Premium sponsorship package</p>
                        </div>
                        <Badge className="bg-[var(--iepa-secondary-green)] text-white">Selected</Badge>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">Used for sponsorship level selection with enhanced styling</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Footer */}
        <div className="border-t pt-8 mt-12">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">IEPA 2025 Design System</h3>
            <p className="text-sm text-gray-600 mb-4">
              Built with shadcn/ui, Tailwind CSS, and accessibility in mind.
            </p>
            <div className="flex justify-center gap-4 text-sm text-gray-500">
              <span>WCAG 2.2 AA Compliant</span>
              <Separator orientation="vertical" className="h-4" />
              <span>Mobile-First Design</span>
              <Separator orientation="vertical" className="h-4" />
              <span>Version 2025.1</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
