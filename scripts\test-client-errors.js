#!/usr/bin/env node

/**
 * Client-side error testing script
 * This script tests key pages for JavaScript runtime errors
 * to catch issues like circular dependencies before committing
 */

const { chromium } = require('playwright');

const TEST_PAGES = [
  {
    name: 'Admin Attendees List',
    url: 'http://localhost:6969/admin/attendees?testAdmin=true',
    waitFor: 'text=Attendees',
  },
  {
    name: 'Admin Attendee Detail',
    url: 'http://localhost:6969/admin/attendees/view?id=9fba13b9-fd63-4cfe-b6e5-d79a1a5c6750&testAdmin=true',
    waitFor: 'text=Attendee Details',
  },
  {
    name: 'Admin Attendees with Meal Filter',
    url: 'http://localhost:6969/admin/attendees?meal=day1-breakfast&testAdmin=true',
    waitFor: 'text=Showing attendees registered for',
  },
  {
    name: 'Admin Speakers',
    url: 'http://localhost:6969/admin/speakers?testAdmin=true',
    waitFor: 'text=Speakers',
  },
  {
    name: 'Admin Dashboard',
    url: 'http://localhost:6969/admin?testAdmin=true',
    waitFor: 'text=Dashboard',
  },
];

async function testPage(browser, pageConfig) {
  const page = await browser.newPage();
  const errors = [];

  // Collect console errors (filter out non-critical ones)
  page.on('console', msg => {
    if (msg.type() === 'error') {
      const text = msg.text();
      // Filter out known non-critical errors
      if (
        text.includes(
          'Failed to load resource: the server responded with a status of 406'
        ) ||
        text.includes('In HTML,') ||
        text.includes('cannot contain a nested') ||
        text.includes('This will cause a hydration error')
      ) {
        return; // Skip these non-critical errors
      }
      errors.push(`Console Error: ${text}`);
    }
  });

  // Collect page errors (these are critical)
  page.on('pageerror', error => {
    const message = error.message;
    errors.push(`Page Error: ${message}`);

    // Flag critical errors that should fail the build
    if (
      message.includes('Cannot access') &&
      message.includes('before initialization')
    ) {
      errors.push(`CRITICAL: Circular dependency detected - ${message}`);
    }
  });

  try {
    console.log(`Testing: ${pageConfig.name}`);

    // Navigate to page
    await page.goto(pageConfig.url, {
      waitUntil: 'networkidle',
      timeout: 30000,
    });

    // Wait for specific content to ensure page loaded
    if (pageConfig.waitFor) {
      await page
        .waitForSelector(`text=${pageConfig.waitFor}`, {
          timeout: 15000,
        })
        .catch(() => {
          // If specific text not found, just wait a bit more
          return page.waitForTimeout(3000);
        });
    }

    // Wait a bit more for any async operations
    await page.waitForTimeout(2000);

    console.log(`✅ ${pageConfig.name} - No critical errors`);
    return { success: true, errors };
  } catch (error) {
    errors.push(`Navigation Error: ${error.message}`);
    console.log(`❌ ${pageConfig.name} - Failed: ${error.message}`);
    return { success: false, errors };
  } finally {
    await page.close();
  }
}

async function runTests() {
  console.log('🧪 Starting client-side error testing...\n');

  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  let allPassed = true;
  const allErrors = [];

  for (const pageConfig of TEST_PAGES) {
    const result = await testPage(browser, pageConfig);

    if (!result.success) {
      allPassed = false;
    }

    if (result.errors.length > 0) {
      allErrors.push({
        page: pageConfig.name,
        errors: result.errors,
      });
    }
  }

  await browser.close();

  console.log('\n📊 Test Results:');

  if (allErrors.length > 0) {
    console.log('\n⚠️  Errors found:');
    allErrors.forEach(({ page, errors }) => {
      console.log(`\n${page}:`);
      errors.forEach(error => console.log(`  - ${error}`));
    });
  }

  if (allPassed) {
    console.log('✅ All pages loaded successfully without critical errors');
    process.exit(0);
  } else {
    console.log('❌ Some pages failed to load properly');
    process.exit(1);
  }
}

// Check if server is running
async function checkServer() {
  try {
    // Just check if anything responds on port 6969
    const response = await fetch('http://localhost:6969').catch(() => null);
    if (!response) {
      throw new Error('Server not responding');
    }
  } catch (error) {
    console.error('❌ Development server is not running on port 6969');
    console.error('Please start the server with: npm run dev');
    process.exit(1);
  }
}

async function main() {
  await checkServer();
  await runTests();
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}
