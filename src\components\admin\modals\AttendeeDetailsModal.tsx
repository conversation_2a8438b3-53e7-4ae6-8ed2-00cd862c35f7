'use client';

import React, { useState, useEffect } from 'react';
import { AttendeeRegistration } from '@/types/database';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { formatCurrency, formatDate } from '@/lib/pdf-generation/utils';
import { formatMealsForDisplay } from '@/lib/meal-utils';
import {
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiBriefcase,
  FiCreditCard,
  FiCalendar,
  FiTarget,
  FiCoffee,
} from 'react-icons/fi';

// Use AttendeeRegistration directly since all needed fields are already defined
type ExtendedAttendeeRegistration = AttendeeRegistration;

interface AttendeeDetailsModalProps {
  attendee: ExtendedAttendeeRegistration;
  open: boolean;
  onClose: () => void;
}

export default function AttendeeDetailsModal({
  attendee,
  open,
  onClose,
}: AttendeeDetailsModalProps) {
  const [formattedMeals, setFormattedMeals] = useState<string>('Loading...');

  // Load formatted meals when attendee changes
  useEffect(() => {
    const loadFormattedMeals = async () => {
      try {
        const formatted = await formatMealsForDisplay(attendee.meals);
        setFormattedMeals(formatted);
      } catch (error) {
        console.error('Error formatting meals:', error);
        setFormattedMeals(attendee.meals?.join(', ') || 'None selected');
      }
    };

    loadFormattedMeals();
  }, [attendee.meals]);

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FiUser className="w-5 h-5" />
            <span>Attendee Details</span>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Personal Information</CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Full Name
                </label>
                <p className="text-gray-900">{`${attendee.last_name}, ${attendee.first_name}`.trim()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Badge Name
                </label>
                <p className="text-gray-900">{attendee.name_on_badge}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Gender
                </label>
                <p className="text-gray-900">{attendee.gender}</p>
              </div>
              <div className="flex items-center space-x-2">
                <FiMail className="w-4 h-4 text-gray-400" />
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Email
                  </label>
                  <p className="text-gray-900">{attendee.email}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <FiPhone className="w-4 h-4 text-gray-400" />
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Phone
                  </label>
                  <p className="text-gray-900">{attendee.phone_number}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <FiMapPin className="w-5 h-5 mr-2" />
                Address
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Street Address
                </label>
                <p className="text-gray-900">{attendee.street_address}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    City
                  </label>
                  <p className="text-gray-900">{attendee.city}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    State
                  </label>
                  <p className="text-gray-900">{attendee.state}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  ZIP Code
                </label>
                <p className="text-gray-900">{attendee.zip_code}</p>
              </div>
            </CardBody>
          </Card>

          {/* Professional Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <FiBriefcase className="w-5 h-5 mr-2" />
                Professional
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Organization
                </label>
                <p className="text-gray-900">{attendee.organization}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Job Title
                </label>
                <p className="text-gray-900">{attendee.job_title}</p>
              </div>
            </CardBody>
          </Card>

          {/* Registration Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Registration Details</CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Registration Type
                </label>
                <div className="mt-1">
                  <Badge variant="outline">{attendee.registration_type}</Badge>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <FiCalendar className="w-4 h-4 text-gray-400" />
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Registered
                  </label>
                  <p className="text-gray-900">
                    {formatDate(new Date(attendee.created_at))}
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Golf Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <FiTarget className="w-5 h-5 mr-2" />
                Golf Tournament
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Participating
                </label>
                <div className="mt-1">
                  {attendee.attending_golf ? (
                    <Badge className="bg-green-100 text-green-800">Yes</Badge>
                  ) : (
                    <Badge variant="secondary">No</Badge>
                  )}
                </div>
              </div>
              {attendee.attending_golf && (
                <>
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Club Rental
                    </label>
                    <div className="mt-1">
                      {attendee.golf_club_rental ? (
                        <Badge className="bg-blue-100 text-blue-800">
                          Yes ({attendee.golf_club_handedness})
                        </Badge>
                      ) : (
                        <Badge variant="secondary">No</Badge>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Golf Total
                    </label>
                    <p className="text-gray-900">
                      {formatCurrency(attendee.golf_total)}
                    </p>
                  </div>
                  {attendee.golf_club_rental && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Club Rental Total
                      </label>
                      <p className="text-gray-900">
                        {formatCurrency(attendee.golf_club_rental_total || 0)}
                      </p>
                    </div>
                  )}
                </>
              )}
            </CardBody>
          </Card>

          {/* Meal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <FiCoffee className="w-5 h-5 mr-2" />
                Meals
              </CardTitle>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Selected Meals
                </label>
                <p className="text-gray-900">{formattedMeals}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Dietary Needs
                </label>
                <p className="text-gray-900">
                  {attendee.dietary_needs || 'None specified'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Meal Total
                </label>
                <p className="text-gray-900">
                  {formatCurrency(attendee.meal_total || 0)}
                </p>
              </div>
            </CardBody>
          </Card>

          {/* Payment Information */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <FiCreditCard className="w-5 h-5 mr-2" />
                Payment Information
              </CardTitle>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Status
                  </label>
                  <div className="mt-1">
                    {getPaymentStatusBadge(attendee.payment_status)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Registration Total
                  </label>
                  <p className="text-gray-900 font-semibold">
                    {formatCurrency(attendee.registration_total)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Grand Total
                  </label>
                  <p className="text-gray-900 font-bold text-lg">
                    {formatCurrency(attendee.grand_total)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Payment ID
                  </label>
                  <p className="text-gray-900 text-sm font-mono">
                    {attendee.payment_id || 'N/A'}
                  </p>
                </div>
              </div>

              {(attendee.receipt_url || attendee.invoice_url) && (
                <div className="mt-6 pt-6 border-t">
                  <label className="text-sm font-medium text-gray-500 mb-3 block">
                    Documents
                  </label>
                  <div className="flex space-x-4">
                    {attendee.receipt_url && (
                      <a
                        href={attendee.receipt_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-[var(--iepa-primary-blue)] hover:underline text-sm"
                      >
                        View Receipt
                      </a>
                    )}
                    {attendee.invoice_url && (
                      <a
                        href={attendee.invoice_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-[var(--iepa-primary-blue)] hover:underline text-sm"
                      >
                        View Invoice
                      </a>
                    )}
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
