'use client';

import React from 'react';
import { Badge } from '@/components/ui';
import {
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiMail,
  FiRefreshCw,
} from 'react-icons/fi';

interface EmailStats {
  totalSent: number;
  totalFailed: number;
  totalPending: number;
  totalEmails: number;
}

interface EmailStatusSummaryProps {
  stats: EmailStats;
  loading?: boolean;
  onRefresh?: () => void;
  className?: string;
}

export default function EmailStatusSummary({
  stats,
  loading = false,
  onRefresh,
  className = '',
}: EmailStatusSummaryProps) {
  const statusItems = [
    {
      label: 'Sent',
      value: stats.totalSent,
      icon: FiCheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
    },
    {
      label: 'Failed',
      value: stats.totalFailed,
      icon: FiXCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
    },
    {
      label: 'Pending',
      value: stats.totalPending,
      icon: FiClock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
    },
    {
      label: 'Total',
      value: stats.totalEmails,
      icon: FiMail,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
    },
  ];

  return (
    <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Status Items */}
      <div className="flex flex-wrap items-center gap-2 sm:gap-4">
        {statusItems.map((item) => {
          const Icon = item.icon;
          return (
            <div
              key={item.label}
              className={`flex items-center gap-2 px-3 py-2 rounded-md border ${item.bgColor} ${item.borderColor} min-w-0 flex-shrink-0`}
            >
              <Icon className={`w-4 h-4 ${item.color} flex-shrink-0`} />
              <span className={`font-medium text-sm ${item.color} hidden sm:inline`}>
                {item.label}:
              </span>
              <span className={`font-medium text-sm ${item.color} sm:hidden`}>
                {item.label.charAt(0)}:
              </span>
              <Badge
                variant="secondary"
                className={`${item.color} ${item.bgColor} border-0 font-semibold text-xs sm:text-sm`}
              >
                {loading ? '...' : item.value.toLocaleString()}
              </Badge>
            </div>
          );
        })}
      </div>

      {/* Refresh Button */}
      {onRefresh && (
        <button
          onClick={onRefresh}
          disabled={loading}
          className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors min-h-[44px] min-w-[44px] justify-center sm:justify-start"
          title="Refresh email statistics"
        >
          <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''} flex-shrink-0`} />
          <span className="hidden sm:inline">Refresh</span>
        </button>
      )}
    </div>
  );
}

// Helper function to calculate success rate
export function calculateSuccessRate(stats: EmailStats): number {
  if (stats.totalEmails === 0) return 0;
  return Math.round((stats.totalSent / stats.totalEmails) * 100);
}

// Helper function to get status summary text
export function getStatusSummary(stats: EmailStats): string {
  const successRate = calculateSuccessRate(stats);
  
  if (stats.totalEmails === 0) {
    return 'No emails sent yet';
  }
  
  if (stats.totalFailed > 0) {
    return `${successRate}% success rate (${stats.totalFailed} failed)`;
  }
  
  if (stats.totalPending > 0) {
    return `${stats.totalPending} emails pending delivery`;
  }
  
  return `All ${stats.totalSent} emails delivered successfully`;
}
