import * as React from 'react';
import { cn } from '@/lib/utils';

interface SpacerProps extends React.HTMLAttributes<HTMLDivElement> {
  x?: number | string;
  y?: number | string;
}

const Spacer = React.forwardRef<HTMLDivElement, SpacerProps>(
  ({ className, x, y, style, ...props }, ref) => {
    const spacerStyle = {
      ...style,
      ...(x && { width: typeof x === 'number' ? `${x}rem` : x }),
      ...(y && { height: typeof y === 'number' ? `${y}rem` : y }),
    };

    return (
      <div
        ref={ref}
        className={cn('flex-shrink-0', className)}
        style={spacerStyle}
        {...props}
      />
    );
  }
);
Spacer.displayName = 'Spacer';

export { Spacer };
