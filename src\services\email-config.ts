// Email Configuration Service
// Manages global email configuration from database with caching and fallbacks

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface EmailConfig {
  sender_email: string;
  support_email: string;
  noreply_email: string;
  from_name: string;
  reply_to_email: string;
  test_bcc_email?: string;
}

export interface EmailConfigRecord {
  id: string;
  config_key: string;
  config_value: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EmailConfigLogEntry {
  id: string;
  config_key: string;
  old_value: string | null;
  new_value: string;
  changed_by: string;
  change_reason: string;
  created_at: string;
}

class EmailConfigService {
  private cache: EmailConfig | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get email configuration with caching
   */
  async getEmailConfig(): Promise<EmailConfig> {
    // Check if cache is valid
    if (this.cache && Date.now() < this.cacheExpiry) {
      return this.cache;
    }

    try {
      // Fetch from database
      const { data, error } = await supabase
        .from('iepa_email_config')
        .select('config_key, config_value')
        .eq('is_active', true);

      if (error) {
        console.error('[EMAIL-CONFIG] Database error:', error);
        return this.getFallbackConfig();
      }

      if (!data || data.length === 0) {
        console.warn('[EMAIL-CONFIG] No active config found, using fallback');
        return this.getFallbackConfig();
      }

      // Convert array to config object
      const config: Partial<EmailConfig> = {};
      data.forEach(item => {
        config[item.config_key as keyof EmailConfig] = item.config_value;
      });

      // Ensure all required fields are present
      const emailConfig: EmailConfig = {
        sender_email: config.sender_email || this.getFallbackConfig().sender_email,
        support_email: config.support_email || this.getFallbackConfig().support_email,
        noreply_email: config.noreply_email || this.getFallbackConfig().noreply_email,
        from_name: config.from_name || this.getFallbackConfig().from_name,
        reply_to_email: config.reply_to_email || this.getFallbackConfig().reply_to_email,
        test_bcc_email: config.test_bcc_email,
      };

      // Update cache
      this.cache = emailConfig;
      this.cacheExpiry = Date.now() + this.CACHE_DURATION;

      console.log('[EMAIL-CONFIG] Configuration loaded from database');
      return emailConfig;

    } catch (error) {
      console.error('[EMAIL-CONFIG] Failed to fetch config:', error);
      return this.getFallbackConfig();
    }
  }

  /**
   * Get fallback configuration from environment variables
   */
  private getFallbackConfig(): EmailConfig {
    return {
      sender_email: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      support_email: process.env.SENDGRID_SUPPORT_EMAIL || '<EMAIL>',
      noreply_email: process.env.SENDGRID_NOREPLY_EMAIL || '<EMAIL>',
      from_name: process.env.SENDGRID_FROM_NAME || 'IEPA Conference 2025',
      reply_to_email: process.env.SENDGRID_SUPPORT_EMAIL || '<EMAIL>',
      test_bcc_email: process.env.NODE_ENV === 'development' ? '<EMAIL>' : undefined,
    };
  }

  /**
   * Refresh cache - force reload from database
   */
  async refreshConfig(): Promise<EmailConfig> {
    this.cache = null;
    this.cacheExpiry = 0;
    return this.getEmailConfig();
  }

  /**
   * Get all email configuration records (for admin interface)
   */
  async getAllConfigs(): Promise<EmailConfigRecord[]> {
    const { data, error } = await supabase
      .from('iepa_email_config')
      .select('*')
      .order('config_key');

    if (error) {
      console.error('[EMAIL-CONFIG] Failed to fetch all configs:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Update email configuration
   */
  async updateConfig(
    configKey: string, 
    configValue: string, 
    changedBy: string,
    changeReason?: string
  ): Promise<void> {
    // Validate email format for email fields
    if (configKey.includes('email') && !this.isValidEmail(configValue)) {
      throw new Error(`Invalid email format: ${configValue}`);
    }

    const { error } = await supabase
      .from('iepa_email_config')
      .update({ 
        config_value: configValue,
        updated_at: new Date().toISOString()
      })
      .eq('config_key', configKey);

    if (error) {
      console.error('[EMAIL-CONFIG] Failed to update config:', error);
      throw error;
    }

    // Clear cache to force reload
    this.cache = null;
    this.cacheExpiry = 0;

    console.log(`[EMAIL-CONFIG] Updated ${configKey} to ${configValue}`);
  }

  /**
   * Get configuration change history
   */
  async getConfigHistory(configKey?: string): Promise<EmailConfigLogEntry[]> {
    let query = supabase
      .from('iepa_email_config_log')
      .select('*')
      .order('created_at', { ascending: false });

    if (configKey) {
      query = query.eq('config_key', configKey);
    }

    const { data, error } = await query.limit(100);

    if (error) {
      console.error('[EMAIL-CONFIG] Failed to fetch config history:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Test email configuration by sending a test email
   */
  async testConfiguration(testEmail: string, configOverride?: Partial<EmailConfig>): Promise<boolean> {
    try {
      const config = configOverride || await this.getEmailConfig();
      
      // Import email service dynamically to avoid circular dependency
      const { EmailService } = await import('./email');
      const emailService = new EmailService();

      // Temporarily override config for testing
      const originalGetConfig = emailService.getConfig;
      emailService.getConfig = () => Promise.resolve({
        fromEmail: config.sender_email || '<EMAIL>',
        fromName: config.from_name || 'IEPA Conference 2025',
        supportEmail: config.support_email || '<EMAIL>',
        noreplyEmail: config.noreply_email || '<EMAIL>',
        testBcc: config.test_bcc_email || null,
        isConfigured: true
      });

      const result = await emailService.sendEmail({
        to: testEmail,
        subject: 'IEPA Email Configuration Test',
        html: `
          <h2>Email Configuration Test</h2>
          <p>This is a test email to verify the email configuration.</p>
          <p><strong>Sender:</strong> ${config.sender_email}</p>
          <p><strong>From Name:</strong> ${config.from_name}</p>
          <p><strong>Support Email:</strong> ${config.support_email}</p>
          <p><strong>Test Time:</strong> ${new Date().toISOString()}</p>
        `,
        text: `Email Configuration Test - Sender: ${config.sender_email}, From: ${config.from_name}`
      }, {
        emailType: 'configuration_test',
        userId: 'admin-test'
      });

      // Restore original config method
      emailService.getConfig = originalGetConfig;

      return result;
    } catch (error) {
      console.error('[EMAIL-CONFIG] Test email failed:', error);
      return false;
    }
  }

  /**
   * Validate email address format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Create or update a configuration setting
   */
  async upsertConfig(
    configKey: string,
    configValue: string,
    description: string,
    changedBy: string
  ): Promise<void> {
    // Validate email format for email fields
    if (configKey.includes('email') && !this.isValidEmail(configValue)) {
      throw new Error(`Invalid email format: ${configValue}`);
    }

    const { error } = await supabase
      .from('iepa_email_config')
      .upsert({
        config_key: configKey,
        config_value: configValue,
        description: description,
        is_active: true,
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('[EMAIL-CONFIG] Failed to upsert config:', error);
      throw error;
    }

    // Clear cache
    this.cache = null;
    this.cacheExpiry = 0;
  }

  /**
   * Toggle configuration active status
   */
  async toggleConfig(configKey: string, isActive: boolean): Promise<void> {
    const { error } = await supabase
      .from('iepa_email_config')
      .update({ 
        is_active: isActive,
        updated_at: new Date().toISOString()
      })
      .eq('config_key', configKey);

    if (error) {
      console.error('[EMAIL-CONFIG] Failed to toggle config:', error);
      throw error;
    }

    // Clear cache
    this.cache = null;
    this.cacheExpiry = 0;
  }
}

// Export singleton instance
export const emailConfigService = new EmailConfigService();
export default emailConfigService;
