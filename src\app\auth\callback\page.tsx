'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

function AuthCallbackContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const handleAuthCallback = async () => {
      const code = searchParams?.get('code');
      const next = searchParams?.get('next') || '/my-registrations';

      // Enhanced debugging for URL and domain issues
      const currentUrl = window.location.href;
      const currentDomain = window.location.hostname;

      console.log('🔗 Client-side auth callback - ENHANCED DEBUG:', {
        code: !!code,
        next,
        allParams: searchParams
          ? Object.fromEntries(searchParams.entries())
          : {},
        currentUrl,
        currentDomain,
        expectedDomain: 'reg.iepa.com',
        domainMatch: currentDomain === 'reg.iepa.com',
        urlHash: window.location.hash,
        urlSearch: window.location.search,
        urlPathname: window.location.pathname,
        referrer: document.referrer,
        userAgent: navigator.userAgent.substring(0, 100),
        timestamp: new Date().toISOString(),
      });

      // Check if user is already authenticated (magic link might have worked)
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      console.log('🔍 Current session check:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        sessionError: sessionError?.message,
      });

      if (session && session.user) {
        console.log('✅ User already authenticated via magic link!');
        setSuccess(true);
        setIsLoading(false);

        setTimeout(() => {
          console.log('🚀 Redirecting authenticated user to:', next);
          router.push(next);
        }, 2000);
        return;
      }

      if (!code) {
        // Check for various issues
        if (
          currentUrl.includes('sendgrid.net') ||
          currentUrl.includes('ct.sendgrid.net')
        ) {
          const errorMsg =
            'SendGrid click tracking detected. Please check Supabase email configuration to disable click tracking for authentication emails.';
          setError(errorMsg);
          console.error(
            '❌ SendGrid click tracking detected in URL:',
            currentUrl
          );
        } else if (currentDomain === 'iepa.vercel.app') {
          const errorMsg = `DOMAIN MISMATCH: You're on iepa.vercel.app but the production app is deployed at reg.iepa.com. Please update your Supabase Site URL configuration or visit the correct domain.`;
          setError(errorMsg);
          console.error('❌ Domain mismatch detected:', {
            currentDomain,
            expectedDomain: 'reg.iepa.com',
            currentUrl,
            suggestedUrl: currentUrl.replace('iepa.vercel.app', 'reg.iepa.com'),
          });
        } else {
          const errorMsg = `Missing authentication code in callback URL. Current domain: ${currentDomain}`;
          setError(errorMsg);
          console.error('❌ No code parameter found:', {
            currentDomain,
            currentUrl,
            allParams: searchParams
              ? Object.fromEntries(searchParams.entries())
              : {},
          });
        }
        setIsLoading(false);
        return;
      }

      try {
        console.log('🔄 Exchanging code for session on client...');

        const { data, error: exchangeError } =
          await supabase.auth.exchangeCodeForSession(code);

        console.log('📊 Client exchange result:', {
          hasSession: !!data.session,
          hasUser: !!data.user,
          hasError: !!exchangeError,
          errorMessage: exchangeError?.message,
        });

        if (exchangeError) {
          throw exchangeError;
        }

        if (data.session) {
          console.log('✅ Session established on client successfully');
          console.log('👤 User authenticated:', {
            id: data.user?.id,
            email: data.user?.email,
          });

          setSuccess(true);
          setIsLoading(false);

          // Small delay to show success message, then redirect
          setTimeout(() => {
            console.log('🚀 Redirecting to:', next);
            router.push(next);
          }, 2000);
        } else {
          throw new Error('No session received after code exchange');
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error occurred';
        console.error('💥 Client callback error:', errorMessage);
        setError(errorMessage);
        setIsLoading(false);
      }
    };

    handleAuthCallback();
  }, [searchParams, router]);

  if (isLoading) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-center">Signing you in...</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="iepa-body">
                    Please wait while we complete your magic link
                    authentication...
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  if (error) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-center text-red-600">
                  Authentication Failed
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Alert className="mb-4">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>

                <div className="space-y-3">
                  <Button asChild className="w-full">
                    <Link href="/auth/login">Try Magic Link Again</Link>
                  </Button>

                  <Button variant="outline" asChild className="w-full">
                    <Link href="/auth/login?usePassword=true">
                      Sign In with Password
                    </Link>
                  </Button>
                </div>

                {/* SendGrid-specific error guidance */}
                {error?.includes('SendGrid') && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded text-sm">
                    <p className="font-medium text-blue-800">
                      For Administrators:
                    </p>
                    <p className="text-blue-700 mt-1">
                      This error occurs when SendGrid click tracking interferes
                      with magic links. Please check the Supabase email
                      configuration to disable click tracking for authentication
                      emails.
                    </p>
                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                      className="mt-2"
                    >
                      <Link
                        href="/api/admin/check-supabase-config"
                        target="_blank"
                      >
                        Check Configuration
                      </Link>
                    </Button>
                  </div>
                )}

                {/* Domain mismatch error guidance */}
                {error?.includes('DOMAIN MISMATCH') && (
                  <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded text-sm">
                    <p className="font-medium text-orange-800">
                      Domain Configuration Issue:
                    </p>
                    <p className="text-orange-700 mt-1">
                      You&apos;re accessing the wrong domain. The production app
                      is deployed at reg.iepa.com, but Supabase is redirecting
                      to iepa.vercel.app.
                    </p>
                    <div className="mt-3 space-y-2">
                      <Button
                        asChild
                        variant="outline"
                        size="sm"
                        className="w-full"
                      >
                        <Link
                          href={`https://iepa.vercel.app${window.location.pathname}${window.location.search}`}
                        >
                          Go to Correct Domain
                        </Link>
                      </Button>
                      <Button
                        asChild
                        variant="outline"
                        size="sm"
                        className="w-full"
                      >
                        <Link href="/admin/sendgrid-fix" target="_blank">
                          Fix Supabase Configuration
                        </Link>
                      </Button>
                    </div>
                  </div>
                )}

                {/* Debug Information (Development Only) */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded text-xs">
                    <strong>Debug Info:</strong>
                    <pre className="mt-1 whitespace-pre-wrap">
                      {JSON.stringify(
                        {
                          error,
                          searchParams: searchParams
                            ? Object.fromEntries(searchParams.entries())
                            : {},
                        },
                        null,
                        2
                      )}
                    </pre>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  if (success) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-center text-green-600">
                  Successfully Signed In!
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center space-y-4">
                  <div className="text-green-600 text-4xl mb-4">✓</div>

                  <p className="iepa-body">
                    Your magic link authentication was successful. You will be
                    redirected shortly...
                  </p>

                  <div className="pt-4">
                    <Button asChild className="w-full">
                      <Link href="/my-registrations">
                        Continue to My Registrations
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  return null;
}

export default function AuthCallbackPage() {
  return (
    <Suspense
      fallback={
        <div className="iepa-container">
          <section className="iepa-section">
            <div className="max-w-md mx-auto">
              <div className="text-center">
                <h1 className="iepa-heading-1 mb-4">Loading...</h1>
                <p className="iepa-body">
                  Please wait while we process your authentication...
                </p>
              </div>
            </div>
          </section>
        </div>
      }
    >
      <AuthCallbackContent />
    </Suspense>
  );
}
