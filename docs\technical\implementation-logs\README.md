# IEPA Conference Registration Application Documentation

This documentation folder contains comprehensive information about the IEPA (Independent Energy Producers Association) conference registration application. The documentation is organized into logical categories for easy navigation and maintenance.

## 📁 Folder Structure

### 01-setup-config/

**Setup and Configuration Documentation**

- Authentication setup and troubleshooting guides
- Database configuration and connection setup
- Application configuration guides
- Environment setup instructions
- Password reset implementation

### 02-testing/

**Testing Procedures and Test Data**

- Test user credentials for different registration types
- Test procedure verification documents
- Testing workflows and validation steps
- Quality assurance documentation

### 03-api-integrations/

**API Documentation and External Integrations**

- Stripe payment integration setup and configuration
- Webhook configuration and implementation
- External service integrations
- API troubleshooting guides
- CloudFront and CDN configuration

### 04-implementation-logs/

**Task Tracking and Implementation Logs**

- Detailed fix logs for specific implementations
- Feature development tracking
- Migration logs (HeroUI to shadcn/ui)
- Component implementation records
- Dashboard and admin interface development

### 05-ui-ux/

**UI/UX Documentation and Design Guidelines**

- UI design specifications and guidelines
- Component styling and theming
- Responsive design implementation
- Accessibility compliance documentation
- Brand compliance and IEPA styling standards

### 06-audit-reports/

**Audit Reports and Quality Assurance**

- Comprehensive application audits
- Accessibility audit reports
- Performance and security audits
- Dependency cleanup reports
- Code quality assessments

### 07-reference-data/

**Reference Materials and External Data**

- IEPA 2025 pricing data and configuration
- External template references (Materialize)
- Configuration guides and data structures
- Business logic documentation

### 08-legacy-backups/

**Previous Application Versions and Backups**

- Legacy application data and screenshots
- Previous implementation references
- Backup files and historical data
- Migration reference materials

## 🔍 Quick Reference

### For Developers

- **Getting Started**: Check `01-setup-config/` for initial setup
- **Testing**: Use credentials in `02-testing/test-users.md`
- **API Integration**: Reference `03-api-integrations/` for external services
- **UI Guidelines**: Follow standards in `05-ui-ux/`

### For Project Managers

- **Implementation Status**: Review logs in `04-implementation-logs/`
- **Quality Reports**: Check `06-audit-reports/` for project health
- **Testing Progress**: Monitor `02-testing/` for QA status

### For Designers

- **Brand Guidelines**: Reference `05-ui-ux/` for IEPA styling
- **Component Library**: Check implementation logs for component status

## 📋 Documentation Standards

1. **Naming Convention**: Use descriptive, kebab-case filenames
2. **Status Tracking**: Include completion status and dates
3. **Cross-References**: Link related documents when applicable
4. **Version Control**: Track changes and implementation dates
5. **Testing Evidence**: Include screenshots and verification steps

## 🔄 Maintenance

This documentation structure should be maintained as the project evolves:

- Add new implementation logs to `04-implementation-logs/`
- Update test procedures in `02-testing/`
- Document new integrations in `03-api-integrations/`
- Keep audit reports current in `06-audit-reports/`

## 📞 Support

For questions about this documentation structure or specific implementations, refer to the relevant folder's content or check the implementation logs for detailed development history.

---

_Last Updated: January 2025_
_IEPA Conference Registration Application Documentation_
