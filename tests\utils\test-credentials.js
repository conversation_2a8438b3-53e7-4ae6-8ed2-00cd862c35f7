/**
 * IEPA Test Credentials and Utilities
 * 
 * Centralized test credentials and helper functions based on
 * docs/testing/procedures/test-logins.md
 */

// Test account credentials from test-logins.md
export const TEST_ACCOUNTS = {
  // Admin account
  admin: {
    email: '<EMAIL>',
    password: '[Contact admin]', // Password not included in docs
    role: 'Administrator',
    purpose: 'Admin dashboard testing, user management, system configuration'
  },

  // Golf test user (recommended for golf add-on testing)
  golf: {
    email: '<EMAIL>',
    password: 'GolfTest123!',
    userId: '4ea60bd3-28ae-4e23-916d-46a6310a9c24',
    purpose: 'Golf tournament registration, club rental, payment processing'
  },

  // Regular attendee test user
  attendee: {
    email: '<EMAIL>',
    password: 'TestPass123!',
    purpose: 'Regular attendee registration flow, pricing options, meal selections'
  },

  // Speaker test user
  speaker: {
    email: '<EMAIL>',
    password: 'TestPass123!',
    purpose: 'Speaker registration, presentation uploads, speaker-specific features'
  },

  // Sponsor test user
  sponsor: {
    email: '<EMAIL>',
    password: 'TestPass123!',
    purpose: 'Sponsor registration, sponsorship levels, sponsor-specific features'
  },

  // E2E test user
  e2e: {
    email: '<EMAIL>',
    password: 'TestPass123!',
    purpose: 'Automated testing, Playwright testing, comprehensive flow testing'
  }
};

// Registration flow specific test emails
export const REGISTRATION_TEST_EMAILS = {
  iepaMember: '<EMAIL>',
  nonMember: '<EMAIL>',
  compedSpeaker: '<EMAIL>',
  paidSpeaker: '<EMAIL>',
  spouse: '<EMAIL>',
  child: '<EMAIL>',
  sponsorAttendee: '<EMAIL>',
  paymentFail: '<EMAIL>'
};

// Stripe test card information
export const STRIPE_TEST_CARDS = {
  success: {
    number: '****************',
    expiry: '12/25',
    cvc: '123',
    zip: '12345',
    description: 'Success card - payment will succeed'
  },
  decline: {
    number: '****************',
    expiry: '12/25',
    cvc: '123',
    zip: '12345',
    description: 'Decline card - payment will be declined'
  },
  insufficientFunds: {
    number: '****************',
    expiry: '12/25',
    cvc: '123',
    zip: '12345',
    description: 'Insufficient funds - payment will fail'
  },
  expired: {
    number: '****************',
    expiry: '12/25',
    cvc: '123',
    zip: '12345',
    description: 'Expired card - payment will fail'
  },
  cvcFail: {
    number: '****************',
    expiry: '12/25',
    cvc: '123',
    zip: '12345',
    description: 'CVC check fail - payment will fail'
  }
};

// Test promo codes
export const PROMO_CODES = {
  test: 'TEST', // 100% discount for testing
  golf: 'GOLF50', // Example golf discount
  speaker: 'SPEAKER25' // Example speaker discount
};

/**
 * Login helper function for Playwright tests
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {Object} credentials - Login credentials {email, password}
 * @param {Object} options - Additional options
 */
export async function loginWithCredentials(page, credentials, options = {}) {
  const { 
    loginUrl = '/login',
    timeout = 10000,
    waitForSelector = '[data-testid="user-menu"], .user-avatar, text=My Account'
  } = options;

  console.log(`🔐 Logging in with: ${credentials.email}`);
  
  // Navigate to login page
  await page.goto(loginUrl);
  await page.waitForLoadState('networkidle');
  
  // Fill login form
  await page.fill('input[type="email"]', credentials.email);
  await page.fill('input[type="password"]', credentials.password);
  
  // Submit login
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  
  // Wait for successful login
  try {
    await page.waitForSelector(waitForSelector, { timeout });
    console.log('✅ Successfully logged in');
    return true;
  } catch (error) {
    console.log('⚠️ Login verification unclear, continuing...');
    return false;
  }
}

/**
 * Fill Stripe payment form with test card
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {Object} card - Card information from STRIPE_TEST_CARDS
 * @param {Object} billingInfo - Billing information
 */
export async function fillStripePaymentForm(page, card = STRIPE_TEST_CARDS.success, billingInfo = {}) {
  const {
    name = 'Test User',
    email = '<EMAIL>',
    phone = '(*************'
  } = billingInfo;

  console.log(`💳 Filling Stripe form with: ${card.description}`);
  
  try {
    // Wait for Stripe form to load
    await page.waitForSelector('input[name="cardNumber"], input[placeholder*="card"]', { timeout: 15000 });
    
    // Fill email if not pre-filled
    const emailInput = page.locator('input[name="email"], input[type="email"]');
    const emailValue = await emailInput.inputValue();
    if (!emailValue) {
      await emailInput.fill(email);
    }
    
    // Add phone number if required
    try {
      await page.fill('input[name="phone"], input[placeholder*="phone"]', phone);
    } catch (e) {
      console.log('ℹ️ Phone number field not found or not required');
    }
    
    // Fill card information
    await page.fill('input[name="cardNumber"], input[placeholder*="card"]', card.number);
    await page.fill('input[name="cardExpiry"], input[placeholder*="expiry"]', card.expiry);
    await page.fill('input[name="cardCvc"], input[placeholder*="cvc"]', card.cvc);
    
    // Fill billing details
    await page.fill('input[name="billingName"], input[placeholder*="name"]', name);
    
    console.log('✅ Stripe payment form filled');
    return true;
  } catch (error) {
    console.log('⚠️ Error filling Stripe form:', error.message);
    return false;
  }
}

/**
 * Apply promo code during checkout
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} promoCode - Promo code to apply
 */
export async function applyPromoCode(page, promoCode = PROMO_CODES.test) {
  console.log(`🎫 Applying promo code: ${promoCode}`);
  
  try {
    // Click "Have a discount code?" button
    await page.click('text=Have a discount code?, text=Promo code, text=Discount code');
    await page.waitForTimeout(1000);

    // Wait for discount code input to appear
    await page.waitForSelector('input[placeholder*="code"], input[placeholder*="discount"], input[placeholder*="promo"]', {
      timeout: 5000
    });

    // Enter promo code
    await page.fill('input[placeholder*="code"], input[placeholder*="discount"], input[placeholder*="promo"]', promoCode);

    // Apply the code
    await page.click('button:has-text("Apply"), button:has-text("Submit")');

    // Wait for discount to be applied
    await page.waitForTimeout(2000);

    console.log('✅ Promo code applied successfully');
    return true;
  } catch (error) {
    console.log('⚠️ Could not apply promo code:', error.message);
    return false;
  }
}

/**
 * Take a screenshot with consistent naming
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} testName - Name of the test
 * @param {string} stepName - Name of the step
 */
export async function takeTestScreenshot(page, testName, stepName) {
  const filename = `test-results/${testName}-${stepName}.png`;
  await page.screenshot({
    path: filename,
    fullPage: true,
  });
  console.log(`📸 Screenshot saved: ${filename}`);
}

/**
 * Wait for page to be stable (no loading indicators)
 * @param {import('@playwright/test').Page} page - Playwright page object
 */
export async function waitForPageStable(page) {
  // Wait for network to be idle
  await page.waitForLoadState('networkidle');
  
  // Wait for any loading spinners to disappear
  try {
    await page.waitForSelector('.loading, .spinner, text=Loading', { 
      state: 'hidden', 
      timeout: 5000 
    });
  } catch (e) {
    // Loading indicators might not exist, which is fine
  }
  
  await page.waitForTimeout(1000);
}

// Export default configuration
export const DEFAULT_TEST_CONFIG = {
  timeouts: {
    navigation: 30000,
    formFill: 5000,
    payment: 60000,
    email: 30000,
    stripeCheckout: 45000,
  },
  delays: {
    typing: 100,
    formStep: 1000,
    pageLoad: 2000,
  },
  baseUrl: 'http://localhost:6969',
  testDataPrefix: 'test-',
};
