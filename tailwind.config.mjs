import { heroui } from '@heroui/react';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        // IEPA Brand Colors
        'iepa-primary': {
          DEFAULT: 'var(--iepa-primary-blue)',
          light: 'var(--iepa-primary-blue-light)',
          dark: 'var(--iepa-primary-blue-dark)',
        },
        'iepa-secondary': {
          DEFAULT: 'var(--iepa-secondary-green)',
          light: 'var(--iepa-secondary-green-light)',
          dark: 'var(--iepa-secondary-green-dark)',
        },
        'iepa-accent': {
          DEFAULT: 'var(--iepa-accent-teal)',
          light: 'var(--iepa-accent-teal-light)',
          lighter: 'var(--iepa-accent-teal-lighter)',
          dark: 'var(--iepa-accent-teal-dark)',
        },
        'iepa-gray': {
          50: 'var(--iepa-gray-50)',
          100: 'var(--iepa-gray-100)',
          200: 'var(--iepa-gray-200)',
          300: 'var(--iepa-gray-300)',
          400: 'var(--iepa-gray-400)',
          500: 'var(--iepa-gray-500)',
          600: 'var(--iepa-gray-600)',
          700: 'var(--iepa-gray-700)',
          800: 'var(--iepa-gray-800)',
          900: 'var(--iepa-gray-900)',
        },
        'iepa-light': '#95D5B2', // Light green for text on dark backgrounds
      },
      fontFamily: {
        sans: ['var(--font-geist-sans)', 'Arial', 'Helvetica', 'sans-serif'],
        mono: ['var(--font-geist-mono)', 'monospace'],
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  darkMode: 'class',
  plugins: [
    heroui(),
    function ({ addUtilities }) {
      addUtilities({
        '.iepa-card': {
          '@apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700':
            {},
        },
        '.iepa-card-elevated': {
          '@apply iepa-card shadow-lg': {},
        },
        '.iepa-card-flat': {
          '@apply iepa-card shadow-none border-0 bg-gray-50 dark:bg-gray-900':
            {},
        },
        '.iepa-card-hover': {
          '@apply iepa-card transition-all duration-200 hover:shadow-lg hover:-translate-y-1':
            {},
        },
      });
    },
  ],
};
