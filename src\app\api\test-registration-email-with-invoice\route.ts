import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const {
      email = '<EMAIL>',
      fullName = 'Test User',
      type = 'attendee',
      confirmationNumber = 'TEST-12345',
      userId = 'test-user-id',
      registrationId,
      speakerPricingType,
      sponsorshipLevel
    } = await request.json();

    // Validate required fields
    if (!email || !fullName || !type) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!registrationId) {
      return NextResponse.json(
        { error: 'Registration ID is required for PDF invoice generation' },
        { status: 400 }
      );
    }

    console.log('[EMAIL-TEST] Testing registration confirmation email with PDF invoice:', {
      email,
      fullName,
      type,
      confirmationNumber,
      userId,
      registrationId,
      speakerPricingType,
      sponsorshipLevel
    });

    // Try to send email using the email service
    try {
      const { emailService } = await import('@/services/email');
      await emailService.sendRegistrationConfirmation(
        email,
        fullName,
        {
          type,
          confirmationNumber,
          userId,
          speakerPricingType,
          sponsorshipLevel,
          registrationId,
        }
      );

      console.log('[EMAIL-TEST] Registration confirmation email with PDF invoice sent successfully');
      return NextResponse.json({ 
        success: true,
        message: 'Registration confirmation email with PDF invoice sent successfully',
        details: {
          email,
          fullName,
          type,
          confirmationNumber,
          registrationId
        }
      });
    } catch (emailError) {
      console.error('[EMAIL-TEST-ERROR] Failed to send email:', emailError);

      return NextResponse.json({
        success: false,
        error: 'Failed to send registration confirmation email',
        details: emailError instanceof Error ? emailError.message : 'Unknown error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('[EMAIL-TEST-ERROR] API route error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET method to show usage instructions
export async function GET() {
  return NextResponse.json({
    message: 'Test Registration Email with PDF Invoice',
    usage: 'POST to this endpoint with registration details',
    requiredFields: ['registrationId'],
    optionalFields: ['email', 'fullName', 'type', 'confirmationNumber', 'userId', 'speakerPricingType', 'sponsorshipLevel'],
    example: {
      registrationId: 'your-registration-id-here',
      email: '<EMAIL>',
      fullName: 'Test User',
      type: 'attendee',
      confirmationNumber: 'CONF-12345'
    }
  });
}
