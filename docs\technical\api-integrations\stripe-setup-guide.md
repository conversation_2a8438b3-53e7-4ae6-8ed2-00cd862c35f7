# Stripe Test API Keys Setup Guide

## Overview

This guide walks through setting up Stripe test API keys for the IEPA 2025 Conference Registration Application. This setup enables secure payment processing for attendee and sponsor registrations.

## Prerequisites

- Stripe account (free to create at [stripe.com](https://stripe.com))
- Access to the IEPA conference application codebase
- Development environment running

## Step 1: Create/Access Stripe Account

### Option A: Create New Stripe Account

1. Go to [stripe.com](https://stripe.com)
2. Click "Start now" or "Sign up"
3. Fill in business information:
   - **Business Name**: Independent Energy Producers Association
   - **Business Type**: Non-profit organization
   - **Country**: United States
   - **Email**: Use your development email
4. Complete account verification (may require additional documentation)

### Option B: Use Existing Stripe Account

1. Log in to your existing Stripe Dashboard
2. Ensure you're in "Test mode" (toggle in top-left corner)

## Step 2: Get Test API Keys

### Access API Keys

1. In Stripe Dashboard, ensure you're in **Test mode**
2. Navigate to **Developers** > **API keys**
3. You'll see two types of keys:

#### Publishable Key (Safe for client-side)

- **Format**: `pk_test_...`
- **Usage**: Client-side JavaScript, forms, checkout
- **Security**: Safe to expose in frontend code

#### Secret Key (Server-side only)

- **Format**: `sk_test_...`
- **Usage**: Server-side API calls, payment processing
- **Security**: ⚠️ **NEVER expose in frontend code**

### Copy Your Test Keys

1. **Publishable key**: Click "Reveal test key" and copy
2. **Secret key**: Click "Reveal test key" and copy
3. Keep these keys secure and ready for configuration

## Step 3: Configure Environment Variables

### Update `.env.local`

Add your Stripe test keys to the existing environment file:

```bash
# Payment Processing - Stripe Test Keys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### Environment Variable Validation

The application will validate these keys on startup. Ensure:

- Publishable key starts with `pk_test_`
- Secret key starts with `sk_test_`
- Both keys are from the same Stripe account

## Step 4: Set Up Webhooks (Optional for Testing)

Webhooks allow Stripe to notify your application about payment events.

### Create Webhook Endpoint

1. In Stripe Dashboard: **Developers** > **Webhooks**
2. Click **Add endpoint**
3. **Endpoint URL**: `https://your-domain.com/api/stripe/webhook`
   - For local testing: Use ngrok or similar tunneling service
   - For development: `http://localhost:3000/api/stripe/webhook`
4. **Events to send**:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `checkout.session.completed`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

### Get Webhook Secret

1. After creating the webhook, click on it
2. Copy the **Signing secret** (starts with `whsec_`)
3. Add to your `.env.local` file

## Step 5: Test Configuration

### Verify Installation

Run the development server and check for any Stripe-related errors:

```bash
npm run dev
```

### Test API Keys

The application includes built-in validation that will:

- ✅ Verify API keys are properly formatted
- ✅ Test connection to Stripe API
- ✅ Confirm test mode is active

## Step 6: Stripe Test Cards

Use these test card numbers for testing payments:

### Successful Payments

- **Visa**: `****************`
- **Visa (debit)**: `****************`
- **Mastercard**: `****************`
- **American Express**: `***************`

### Test Scenarios

- **Declined**: `****************`
- **Insufficient funds**: `****************`
- **Expired card**: `****************`
- **Incorrect CVC**: `****************`

### Test Details for All Cards

- **Expiry**: Any future date (e.g., 12/25)
- **CVC**: Any 3-digit number (4 digits for Amex)
- **ZIP**: Any valid ZIP code

## Security Best Practices

### ✅ Do's

- Always use test keys in development
- Keep secret keys secure and server-side only
- Use HTTPS in production
- Validate webhook signatures
- Log payment events for debugging

### ❌ Don'ts

- Never commit API keys to version control
- Never use production keys in development
- Never expose secret keys in client-side code
- Never skip webhook signature validation

## Integration Points

### Registration Forms

The Stripe integration will be added to:

- **Attendee Registration**: `/register/attendee` (Step 6: Review & Payment)
- **Sponsor Registration**: `/register/sponsor` (Payment step)

### Payment Flow

1. User completes registration form
2. Application calculates total cost
3. Stripe Checkout session created
4. User redirected to Stripe-hosted payment page
5. Payment processed securely by Stripe
6. User redirected back to confirmation page
7. Webhook confirms payment status
8. Registration marked as paid in database

## Troubleshooting

### Common Issues

1. **Invalid API key**: Ensure keys are copied correctly and from test mode
2. **CORS errors**: Verify publishable key is properly configured
3. **Webhook failures**: Check endpoint URL and signing secret
4. **Payment failures**: Use correct test card numbers

### Support Resources

- **Stripe Documentation**: [stripe.com/docs](https://stripe.com/docs)
- **Test Cards**: [stripe.com/docs/testing](https://stripe.com/docs/testing)
- **Webhook Testing**: [stripe.com/docs/webhooks/test](https://stripe.com/docs/webhooks/test)

## Step 7: Test Your Setup

### Access Test Page

1. Start your development server: `npm run dev`
2. Navigate to: `http://localhost:3000/test-stripe`
3. Review configuration status and test results
4. Use the "Test Payment Flow" button to verify end-to-end functionality

### Verify Integration

- ✅ Configuration validation passes
- ✅ API connection successful
- ✅ Test payment redirects to Stripe checkout
- ✅ Payment success/cancel pages work correctly

## Quick Setup Checklist

- [ ] **Stripe Account**: Created or accessed existing account
- [ ] **Test Mode**: Confirmed you're in test mode
- [ ] **API Keys**: Copied publishable and secret test keys
- [ ] **Environment Variables**: Updated `.env.local` with your keys
- [ ] **Webhook Setup**: Created webhook endpoint (optional for basic testing)
- [ ] **Test Configuration**: Visited `/test-stripe` page and verified setup
- [ ] **Test Payment**: Successfully completed a test payment flow

---

**Status**: ✅ Complete Stripe integration configured and tested
**Configuration**: ✅ Test API keys added and verified
**Test Results**: ✅ All configuration tests passing

**Next Steps**:

1. ✅ ~~Add your actual Stripe test API keys to `.env.local`~~ **COMPLETED**
2. ✅ ~~Test the integration at `/test-stripe`~~ **COMPLETED**
3. Integrate payment flow with registration forms
4. Set up webhook endpoint for production use

**Last Updated**: January 2025
