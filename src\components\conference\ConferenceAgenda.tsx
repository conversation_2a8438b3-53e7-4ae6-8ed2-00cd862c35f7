'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui';
import { getAgendaDataWithFallback } from '@/lib/services/agenda';
import { LegacyAgendaDay } from '@/lib/types/agenda';
import { Clock, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConferenceAgendaProps {
  className?: string;
  showTitle?: boolean;
  title?: string;
  compact?: boolean;
}

export function ConferenceAgenda({
  className,
  showTitle = true,
  title = 'Conference Agenda',
  compact = false,
}: ConferenceAgendaProps) {
  const [agendaData, setAgendaData] = useState<LegacyAgendaDay[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadAgenda() {
      try {
        setLoading(true);
        setError(null);
        const data = await getAgendaDataWithFallback();
        setAgendaData(data);
      } catch (err) {
        console.error('Error loading agenda:', err);
        setError('Failed to load agenda data');
      } finally {
        setLoading(false);
      }
    }

    loadAgenda();
  }, []);

  if (loading) {
    return (
      <section className={cn('iepa-section', className)}>
        <div className="max-w-4xl mx-auto">
          {showTitle && (
            <h2 className="iepa-heading-2 text-center mb-8">{title}</h2>
          )}
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--iepa-primary-blue)] mx-auto"></div>
            <p className="mt-4 text-[var(--iepa-gray-600)]">
              Loading agenda...
            </p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className={cn('iepa-section', className)}>
        <div className="max-w-4xl mx-auto">
          {showTitle && (
            <h2 className="iepa-heading-2 text-center mb-8">{title}</h2>
          )}
          <div className="text-center py-8">
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={cn('iepa-section', className)}>
      <div className="max-w-4xl mx-auto">
        {showTitle && (
          <h2 className="iepa-heading-2 text-center mb-8">{title}</h2>
        )}

        <div className="space-y-6">
          {agendaData.map(day => (
            <Card key={day.dayNumber} className="overflow-hidden">
              <div
                className="px-6 py-4 text-white"
                style={{ backgroundColor: day.color }}
              >
                <div className="flex items-center gap-3">
                  <Calendar className="w-5 h-5" />
                  <div>
                    <h3 className="font-semibold text-lg">{day.title}</h3>
                    <p className="text-sm opacity-90">{day.date}</p>
                  </div>
                </div>
              </div>

              <CardContent className="p-0">
                <div className="space-y-0">
                  {day.events.map((event, eventIndex) => (
                    <div
                      key={eventIndex}
                      className={cn(
                        'px-6 py-4 border-b border-gray-100 last:border-b-0',
                        compact && 'py-3'
                      )}
                    >
                      <div className="flex gap-4">
                        <div className="flex items-center gap-2 text-[var(--iepa-primary-blue)] min-w-0 flex-shrink-0">
                          <Clock className="w-4 h-4" />
                          <span
                            className={cn(
                              'font-medium',
                              compact ? 'text-sm' : 'text-base'
                            )}
                          >
                            {event.time}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4
                            className={cn(
                              'font-semibold text-[var(--iepa-gray-900)] mb-1',
                              compact ? 'text-sm' : 'text-base'
                            )}
                          >
                            {event.title}
                          </h4>
                          {event.description && (
                            <p
                              className={cn(
                                'text-[var(--iepa-gray-600)]',
                                compact ? 'text-xs' : 'text-sm'
                              )}
                            >
                              {event.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {agendaData.length === 0 && (
          <div className="text-center py-8">
            <p className="text-[var(--iepa-gray-600)]">
              No agenda items available at this time.
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
