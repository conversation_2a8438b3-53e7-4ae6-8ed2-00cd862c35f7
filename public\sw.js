// Service Worker for IEPA Email Center
// Provides offline support and caching for better performance

const CACHE_NAME = 'iepa-email-center-v1';
const STATIC_CACHE_NAME = 'iepa-static-v1';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/admin',
  '/admin/emails',
  '/manifest.json',
  // Add other critical assets here
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /\/api\/admin\/email-logs/,
  /\/api\/admin\/email-content/,
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Static assets cached successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[SW] Failed to cache static assets:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE_NAME) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Service worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle requests with caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle static assets and pages
  event.respondWith(handleStaticRequest(request));
});

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
  const url = new URL(request.url);
  
  // Check if this API should be cached
  const shouldCache = API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname));
  
  if (!shouldCache) {
    // For non-cacheable APIs, just fetch normally
    return fetch(request);
  }

  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
      return networkResponse;
    }
    
    throw new Error('Network response not ok');
  } catch (error) {
    console.log('[SW] Network failed, trying cache for:', request.url);
    
    // Fallback to cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline fallback for email logs
    if (url.pathname.includes('/email-logs')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Offline - cached data not available',
        logs: [],
        statistics: {
          totalSent: 0,
          totalFailed: 0,
          totalPending: 0,
          totalEmails: 0
        },
        pagination: {
          page: 1,
          limit: 50,
          total: 0,
          totalPages: 0
        },
        offline: true
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    throw error;
  }
}

// Handle static requests with cache-first strategy
async function handleStaticRequest(request) {
  try {
    // Try cache first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fallback to network
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache the response
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('[SW] Failed to fetch static resource:', request.url);
    
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      const offlineResponse = await caches.match('/offline.html');
      if (offlineResponse) {
        return offlineResponse;
      }
    }
    
    throw error;
  }
}

// Handle background sync for failed requests
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'email-retry') {
    event.waitUntil(retryFailedEmails());
  }
});

// Retry failed email operations when back online
async function retryFailedEmails() {
  try {
    console.log('[SW] Retrying failed email operations...');
    
    // Get failed operations from IndexedDB or localStorage
    // This would need to be implemented based on your offline storage strategy
    
    // For now, just log that we would retry
    console.log('[SW] Email retry functionality would be implemented here');
  } catch (error) {
    console.error('[SW] Failed to retry email operations:', error);
  }
}

// Handle push notifications (if needed in the future)
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received');
  
  const options = {
    body: 'New email status update available',
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    tag: 'email-update',
    requireInteraction: false,
    actions: [
      {
        action: 'view',
        title: 'View Updates'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('IEPA Email Center', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event.action);
  
  event.notification.close();
  
  if (event.action === 'view') {
    event.waitUntil(
      clients.openWindow('/admin/emails')
    );
  }
});

// Periodic background sync (if supported)
self.addEventListener('periodicsync', (event) => {
  console.log('[SW] Periodic sync triggered:', event.tag);
  
  if (event.tag === 'email-status-check') {
    event.waitUntil(checkEmailStatusUpdates());
  }
});

async function checkEmailStatusUpdates() {
  try {
    console.log('[SW] Checking for email status updates...');
    
    const response = await fetch('/api/admin/email-status-updates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        since: new Date(Date.now() - 30 * 60 * 1000).toISOString() // Last 30 minutes
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      
      if (data.success && data.changes?.length > 0) {
        // Show notification about updates
        self.registration.showNotification('IEPA Email Center', {
          body: `${data.changes.length} email status updates available`,
          icon: '/icon-192x192.png',
          tag: 'status-update'
        });
      }
    }
  } catch (error) {
    console.error('[SW] Failed to check email status updates:', error);
  }
}
