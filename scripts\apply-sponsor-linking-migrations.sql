-- Apply sponsor-attendee linking database migrations
-- Run this script in your Supabase SQL editor to add the new fields and table

-- Add sponsor linking fields to iepa_attendee_registrations table
ALTER TABLE iepa_attendee_registrations 
ADD COLUMN IF NOT EXISTS sponsor_id UUID REFERENCES iepa_sponsor_registrations(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS sponsor_discount_code TEXT,
ADD COLUMN IF NOT EXISTS is_sponsor_attendee BOOLEAN DEFAULT FALSE;

-- Add attendee linking fields to iepa_sponsor_registrations table
ALTER TABLE iepa_sponsor_registrations 
ADD COLUMN IF NOT EXISTS linked_attendee_email TEXT,
ADD COLUMN IF NOT EXISTS company_domain TEXT;

-- Create iepa_sponsor_domains table for email domain-based automatic discounts
CREATE TABLE IF NOT EXISTS iepa_sponsor_domains (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sponsor_id UUID REFERENCES iepa_sponsor_registrations(id) ON DELETE CASCADE,
    domain TEXT NOT NULL, -- e.g., 'acme.com'
    sponsor_name TEXT NOT NULL, -- Company name for reference
    -- Discount configuration
    discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')) DEFAULT 'percentage',
    discount_value DECIMAL(10,2) NOT NULL DEFAULT 100, -- Default 100% discount for sponsor attendees
    -- Auto-generated discount code
    auto_discount_code TEXT UNIQUE, -- Automatically generated code for this domain
    stripe_coupon_id TEXT, -- Associated Stripe coupon
    -- Status and limits
    is_active BOOLEAN DEFAULT TRUE,
    max_uses INTEGER, -- Optional limit on domain-based discounts
    current_uses INTEGER DEFAULT 0,
    -- Admin tracking
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Constraints
    UNIQUE(domain, sponsor_id)
);

-- Add trigger for updated_at
CREATE TRIGGER update_iepa_sponsor_domains_updated_at
    BEFORE UPDATE ON iepa_sponsor_domains
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE iepa_sponsor_domains ENABLE ROW LEVEL SECURITY;

-- RLS Policies for iepa_sponsor_domains
CREATE POLICY "Anyone can view active sponsor domains"
    ON iepa_sponsor_domains FOR SELECT
    USING (is_active = true);

CREATE POLICY "Only admins can manage sponsor domains"
    ON iepa_sponsor_domains FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

CREATE POLICY "Service role can manage all sponsor domains"
    ON iepa_sponsor_domains FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sponsor_domains_domain ON iepa_sponsor_domains(domain);
CREATE INDEX IF NOT EXISTS idx_sponsor_domains_sponsor_id ON iepa_sponsor_domains(sponsor_id);
CREATE INDEX IF NOT EXISTS idx_sponsor_domains_active ON iepa_sponsor_domains(is_active);
CREATE INDEX IF NOT EXISTS idx_attendee_registrations_sponsor_id ON iepa_attendee_registrations(sponsor_id);
CREATE INDEX IF NOT EXISTS idx_attendee_registrations_is_sponsor_attendee ON iepa_attendee_registrations(is_sponsor_attendee);

-- Insert sample data for testing (optional - remove in production)
-- This creates a test sponsor domain for testing purposes
-- INSERT INTO iepa_sponsor_domains (
--     sponsor_id, 
--     domain, 
--     sponsor_name, 
--     auto_discount_code,
--     discount_type,
--     discount_value
-- ) VALUES (
--     (SELECT id FROM iepa_sponsor_registrations LIMIT 1), -- Use first sponsor for testing
--     'example.com',
--     'Example Corp',
--     'SPONSOR_EXAMPLE_EXAMPLECOM',
--     'percentage',
--     100
-- );

-- Verify the changes
SELECT 'Migration completed successfully. New columns and table created.' as status;
