'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { IconType } from 'react-icons';

export interface ScrollspySection {
  id: string;
  title: string;
  description?: string;
  icon?: IconType;
}

export interface ScrollspyNavigationProps {
  sections: ScrollspySection[];
  activeSection: string;
  onSectionClick: (sectionId: string) => void;
  className?: string;
  variant?: 'desktop' | 'mobile';
}

export default function ScrollspyNavigation({
  sections,
  activeSection,
  onSectionClick,
  className,
  variant = 'desktop'
}: ScrollspyNavigationProps) {
  const handleSectionClick = (sectionId: string) => {
    onSectionClick(sectionId);
    
    // Smooth scroll to section
    const element = document.getElementById(sectionId);
    if (element) {
      const offset = variant === 'mobile' ? 80 : 100; // Account for sticky navigation
      const elementPosition = element.offsetTop - offset;
      
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
    }
  };

  if (variant === 'mobile') {
    return (
      <div className={cn(
        'iepa-scrollspy-mobile',
        'sticky top-0 z-40 bg-white border-b border-[var(--iepa-gray-200)] shadow-sm',
        'lg:hidden', // Hide on desktop
        className
      )}>
        <div className="iepa-container">
          <div className="flex overflow-x-auto scrollbar-hide py-3 px-4 gap-2">
            {sections.map((section) => {
              const IconComponent = section.icon;
              const isActive = activeSection === section.id;
              
              return (
                <button
                  key={section.id}
                  onClick={() => handleSectionClick(section.id)}
                  className={cn(
                    'iepa-scrollspy-mobile-item',
                    'flex items-center gap-2 px-3 py-2 rounded-lg whitespace-nowrap',
                    'text-sm font-medium transition-all duration-200 ease-out',
                    'border border-transparent',
                    isActive
                      ? 'bg-[var(--iepa-primary-blue)] text-white border-[var(--iepa-primary-blue)]'
                      : 'text-[var(--iepa-gray-600)] hover:text-[var(--iepa-primary-blue)] hover:bg-[var(--iepa-gray-50)]'
                  )}
                  aria-current={isActive ? 'true' : 'false'}
                >
                  {IconComponent && (
                    <IconComponent className={cn(
                      'w-4 h-4 flex-shrink-0',
                      isActive ? 'text-white' : 'text-[var(--iepa-gray-500)]'
                    )} />
                  )}
                  <span className="truncate">{section.title}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>
    );
  }

  // Desktop variant
  return (
    <div className={cn(
      'iepa-scrollspy-desktop',
      'fixed left-6 top-1/2 transform -translate-y-1/2 z-30',
      'hidden lg:block', // Show only on desktop
      'w-64 max-h-[80vh] overflow-y-auto',
      className
    )}>
      <div className="iepa-scrollspy-desktop-container bg-[var(--iepa-gray-800)] rounded-lg shadow-lg p-4">
        <h3 className="text-white font-semibold text-sm mb-4 px-2">
          Registration Progress
        </h3>
        
        <nav className="space-y-1">
          {sections.map((section, index) => {
            const IconComponent = section.icon;
            const isActive = activeSection === section.id;
            
            return (
              <button
                key={section.id}
                onClick={() => handleSectionClick(section.id)}
                className={cn(
                  'iepa-scrollspy-desktop-item',
                  'w-full flex items-start gap-3 p-3 rounded-lg text-left',
                  'transition-all duration-200 ease-out',
                  'group hover:transform hover:-translate-y-1',
                  isActive
                    ? 'bg-[var(--iepa-primary-blue)] text-white shadow-md'
                    : 'text-[var(--iepa-gray-300)] hover:bg-[var(--iepa-gray-700)] hover:text-white'
                )}
                aria-current={isActive ? 'true' : 'false'}
              >
                <div className={cn(
                  'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold',
                  'transition-colors duration-200',
                  isActive
                    ? 'bg-white text-[var(--iepa-primary-blue)]'
                    : 'bg-[var(--iepa-gray-600)] text-[var(--iepa-gray-300)] group-hover:bg-[var(--iepa-primary-blue)] group-hover:text-white'
                )}>
                  {IconComponent ? (
                    <IconComponent className="w-4 h-4" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className={cn(
                    'font-medium text-sm leading-tight',
                    isActive ? 'text-white' : 'group-hover:text-white'
                  )}>
                    {section.title}
                  </div>
                  {section.description && (
                    <div className={cn(
                      'text-xs mt-1 leading-tight',
                      isActive 
                        ? 'text-blue-100' 
                        : 'text-[var(--iepa-gray-400)] group-hover:text-[var(--iepa-gray-200)]'
                    )}>
                      {section.description}
                    </div>
                  )}
                </div>
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
}
