'use client';

import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { useRouter } from 'next/navigation';
import {
  FiSettings,
  FiLogOut,
  FiGrid,
  FiFileText,
  FiShield,
  FiMail,
  FiCheck,
  FiAlertTriangle,
} from 'react-icons/fi';
import Link from 'next/link';

interface UserDropdownProps {
  className?: string;
  showAdminBadge?: boolean;
}

export function UserDropdown({
  className = '',
  showAdminBadge = false,
}: UserDropdownProps) {
  const { user, signOut } = useAuth();
  const { isAdmin } = useAdminAccess();
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);

  if (!user) return null;

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setIsSigningOut(false);
    }
  };

  const getInitials = (email: string) => {
    return email.charAt(0).toUpperCase();
  };

  const isEmailConfirmed = user.email_confirmed_at;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          id="user-dropdown-trigger"
          variant="ghost"
          className={`relative h-10 w-10 rounded-full bg-iepa-primary hover:bg-iepa-primary-dark focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-iepa-primary transition-all ${className}`}
          style={{
            backgroundColor: 'var(--iepa-primary-blue, #396DA4)',
          }}
          data-testid="user-dropdown-trigger"
        >
          <Avatar
            id="user-avatar"
            className="h-10 w-10"
            data-testid="user-avatar"
          >
            <AvatarImage
              src={user.user_metadata?.avatar_url}
              alt={user.email || ''}
              data-testid="user-avatar-image"
            />
            <AvatarFallback
              id="user-avatar-fallback"
              className="bg-iepa-primary text-white font-medium"
              data-testid="user-avatar-fallback"
            >
              {getInitials(user.email || 'U')}
            </AvatarFallback>
          </Avatar>

          {/* Status indicator dot */}
          <div
            id="user-status-indicator"
            className="absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full border-2 border-white"
            data-testid="user-status-indicator"
          >
            {isEmailConfirmed ? (
              <div
                id="email-confirmed-indicator"
                className="h-full w-full rounded-full bg-green-500"
                title="Email confirmed"
                data-testid="email-confirmed-indicator"
              />
            ) : (
              <div
                id="email-unconfirmed-indicator"
                className="h-full w-full rounded-full bg-amber-500"
                title="Email not confirmed"
                data-testid="email-unconfirmed-indicator"
              />
            )}
          </div>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        id="user-dropdown-menu"
        align="end"
        className="w-64 iepa-user-dropdown-content"
        data-testid="user-dropdown-menu"
      >
        {/* User Info Header */}
        <DropdownMenuLabel
          id="user-dropdown-header"
          className="p-4"
          data-testid="user-dropdown-header"
        >
          <div
            id="user-info-section"
            className="flex items-start gap-3"
            data-testid="user-info-section"
          >
            <Avatar
              id="user-dropdown-avatar"
              className="h-12 w-12"
              data-testid="user-dropdown-avatar"
            >
              <AvatarImage
                src={user.user_metadata?.avatar_url}
                alt={user.email || ''}
                data-testid="user-dropdown-avatar-image"
              />
              <AvatarFallback
                id="user-dropdown-avatar-fallback"
                className="bg-iepa-primary text-white font-medium text-lg"
                data-testid="user-dropdown-avatar-fallback"
              >
                {getInitials(user.email || 'U')}
              </AvatarFallback>
            </Avatar>

            <div
              id="user-details-section"
              className="flex-1 min-w-0"
              data-testid="user-details-section"
            >
              <div className="flex items-center gap-2 mb-1">
                <p
                  id="user-display-name"
                  className="font-semibold text-sm text-gray-900 truncate"
                  data-testid="user-display-name"
                >
                  {user.user_metadata?.full_name ||
                    user.email?.split('@')[0] ||
                    'User'}
                </p>
                {showAdminBadge && (
                  <Badge
                    id="admin-badge"
                    variant="secondary"
                    className="text-xs"
                    data-testid="admin-badge"
                  >
                    <FiShield className="w-3 h-3 mr-1" />
                    Admin
                  </Badge>
                )}
              </div>

              <p
                id="user-email-display"
                className="text-xs text-gray-600 truncate mb-2"
                data-testid="user-email-display"
              >
                {user.email}
              </p>

              <div
                id="user-verification-status"
                className="flex items-center gap-1"
                data-testid="user-verification-status"
              >
                {isEmailConfirmed ? (
                  <Badge
                    id="verified-badge"
                    variant="default"
                    className="text-xs bg-green-100 text-green-800 border-green-200"
                    data-testid="verified-badge"
                  >
                    <FiCheck className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                ) : (
                  <Badge
                    id="unverified-badge"
                    variant="outline"
                    className="text-xs bg-amber-50 text-amber-700 border-amber-200"
                    data-testid="unverified-badge"
                  >
                    <FiAlertTriangle className="w-3 h-3 mr-1" />
                    Unverified
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator className="iepa-user-dropdown-separator" />

        {/* Navigation Items */}
        <DropdownMenuItem asChild>
          <Link
            href="/my-registrations"
            className="flex items-center gap-2 cursor-pointer iepa-user-dropdown-item"
          >
            <FiFileText className="w-4 h-4" />
            My Registrations
          </Link>
        </DropdownMenuItem>

        {isAdmin && (
          <DropdownMenuItem asChild>
            <Link
              href="/admin"
              className="flex items-center gap-2 cursor-pointer iepa-user-dropdown-item"
            >
              <FiGrid className="w-4 h-4" />
              Admin Dashboard
            </Link>
          </DropdownMenuItem>
        )}

        <DropdownMenuItem asChild>
          <Link
            href="/settings"
            className="flex items-center gap-2 cursor-pointer iepa-user-dropdown-item"
          >
            <FiSettings className="w-4 h-4" />
            Account Settings
          </Link>
        </DropdownMenuItem>

        {!isEmailConfirmed && (
          <>
            <DropdownMenuSeparator className="iepa-user-dropdown-separator" />
            <DropdownMenuItem className="text-amber-700 focus:text-amber-800 focus:bg-amber-50">
              <FiMail className="w-4 h-4 mr-2" />
              Verify Email Address
            </DropdownMenuItem>
          </>
        )}

        <DropdownMenuSeparator className="iepa-user-dropdown-separator" />

        {/* Sign Out */}
        <DropdownMenuItem
          onClick={handleSignOut}
          disabled={isSigningOut}
          className="text-red-600 focus:text-red-700 focus:bg-red-50 cursor-pointer"
        >
          <FiLogOut className="w-4 h-4 mr-2" />
          {isSigningOut ? 'Signing out...' : 'Sign Out'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact version for mobile or smaller spaces
export function CompactUserDropdown({
  className = '',
}: {
  className?: string;
}) {
  const { user, signOut } = useAuth();
  const { isAdmin } = useAdminAccess();
  const [isSigningOut, setIsSigningOut] = useState(false);

  if (!user) return null;

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setIsSigningOut(false);
    }
  };

  const getInitials = (email: string) => {
    return email.charAt(0).toUpperCase();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`flex items-center gap-2 h-8 px-2 text-white hover:bg-iepa-primary-dark bg-iepa-primary ${className}`}
          style={{
            backgroundColor: 'var(--iepa-primary-blue, #396DA4)',
          }}
        >
          <Avatar className="h-6 w-6">
            <AvatarFallback className="bg-white/20 text-white text-xs">
              {getInitials(user.email || 'U')}
            </AvatarFallback>
          </Avatar>
          <span className="text-sm font-medium truncate max-w-24">
            {user.email?.split('@')[0] || 'User'}
          </span>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        className="w-48 iepa-user-dropdown-content"
      >
        <DropdownMenuLabel className="text-xs iepa-user-dropdown-label">
          {user.email}
        </DropdownMenuLabel>

        <DropdownMenuSeparator className="iepa-user-dropdown-separator" />

        <DropdownMenuItem asChild>
          <Link
            href="/my-registrations"
            className="text-sm iepa-user-dropdown-item"
          >
            My Registrations
          </Link>
        </DropdownMenuItem>

        {isAdmin && (
          <DropdownMenuItem asChild>
            <Link href="/admin" className="text-sm iepa-user-dropdown-item">
              Admin Dashboard
            </Link>
          </DropdownMenuItem>
        )}

        <DropdownMenuSeparator className="iepa-user-dropdown-separator" />

        <DropdownMenuItem
          onClick={handleSignOut}
          disabled={isSigningOut}
          className="text-red-600 focus:text-red-700 text-sm"
        >
          {isSigningOut ? 'Signing out...' : 'Sign Out'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
