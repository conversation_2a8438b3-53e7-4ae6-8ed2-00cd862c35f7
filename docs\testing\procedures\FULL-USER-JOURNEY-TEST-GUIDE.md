# IEPA Conference Registration - Full User Journey Testing Guide

## Overview

This guide covers running comprehensive end-to-end tests that simulate a complete user journey from initial signup through payment completion. The test creates new users dynamically and tests the entire registration flow.

## Prerequisites

### 1. Development Environment Setup

```bash
# Ensure development server is running
npm run dev
# Server should be accessible at http://localhost:6969
```

### 2. Database & Services

- ✅ Supabase connection active (NDS project)
- ✅ Stripe test keys configured
- ✅ Email system configured (<EMAIL>)
- ✅ Authentication disabled email confirmation for testing

### 3. Test Dependencies

```bash
# Install Playwright if not already installed
npm install @playwright/test

# Install browsers
npx playwright install
```

## Running the Tests

### Option 1: Use the Test Runner Script (Recommended)

```bash
# Run the comprehensive test suite
node scripts/run-full-user-journey-test.js
```

This script will:
- Check prerequisites (server running, files exist)
- Create necessary directories
- Run all user journey tests
- Generate a summary report
- Open browser for visual debugging

### Option 2: Direct Playwright Command

```bash
# Run with browser visible (for debugging)
npx playwright test tests/full-user-journey-e2e.spec.js --headed

# Run headless (for CI/automation)
npx playwright test tests/full-user-journey-e2e.spec.js

# Run specific test
npx playwright test tests/full-user-journey-e2e.spec.js -g "complete full user journey"
```

## Test Scenarios Covered

### 1. Complete New User Journey
- **Duration:** ~2 minutes
- **Steps:**
  1. Navigate to homepage
  2. Start registration process
  3. Create new user account (unique email each run)
  4. Fill complete attendee registration form
  5. Select registration type and options
  6. Process payment with Stripe test card
  7. Verify success confirmation
  8. Navigate to user dashboard

### 2. Existing User Journey
- **Duration:** ~1 minute
- **Steps:**
  1. Login with existing test account
  2. Quick registration form completion
  3. Submit and verify processing

### 3. Form Validation Testing
- **Duration:** ~30 seconds
- **Steps:**
  1. Attempt form submission with missing data
  2. Verify validation errors display
  3. Confirm error handling works

## Test Data

### New User (Generated Dynamically)
```javascript
{
  firstName: 'Sarah',
  lastName: 'Johnson',
  email: `new-user-${Date.now()}@iepa-test.com`, // Unique each run
  password: 'NewUser123!',
  organization: 'Renewable Energy Solutions',
  // ... complete profile data
}
```

### Stripe Test Card
```javascript
{
  number: '****************', // Always succeeds
  expiry: '12/25',
  cvc: '123',
  zip: '12345'
}
```

### Existing Test Account
- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`

## Expected Results

### Screenshots Generated
- `journey-01-homepage.png` - Initial homepage
- `journey-03-attendee-selection.png` - Registration type selection
- `journey-05-after-signup.png` - After user authentication
- `journey-09-emergency-contact-filled.png` - Completed form
- `journey-13-stripe-payment-filled.png` - Stripe checkout
- `journey-15-success-confirmation.png` - Success page
- `journey-16-user-dashboard.png` - User dashboard

### Database Records Created
- New user in `auth.users` table
- User profile in `iepa_user_profiles` table
- Registration record in `iepa_attendee_registrations` table
- Payment record in `iepa_payments` table
- Email log in `iepa_email_logs` table

### Email Notifications
- Registration confirmation email sent
- PDF invoice attached
- BCC copy to `<EMAIL>` (testing)

## Troubleshooting

### Common Issues

1. **Server Not Running**
   ```bash
   # Error: Development server is not running on port 6969
   # Solution: Start the dev server
   npm run dev
   ```

2. **Authentication Failures**
   - Check Supabase configuration
   - Verify email confirmation is disabled
   - Check test account credentials

3. **Payment Processing Issues**
   - Verify Stripe test keys are configured
   - Check webhook endpoints are accessible
   - Confirm test card numbers are correct

4. **Form Validation Errors**
   - Review form field selectors in test
   - Check for UI changes that affect element selection
   - Verify form submission flow

### Debug Mode

Run tests with additional debugging:

```bash
# Run with debug output
DEBUG=pw:api npx playwright test tests/full-user-journey-e2e.spec.js --headed

# Run with trace recording
npx playwright test tests/full-user-journey-e2e.spec.js --trace=on
```

## Test Maintenance

### Updating Test Data
- Modify user data in `tests/full-user-journey-e2e.spec.js`
- Update selectors if UI changes
- Adjust timeouts if needed

### Adding New Scenarios
- Add new test cases to the existing describe block
- Follow the same pattern: setup → action → verification
- Include screenshots for debugging

### Cleanup
After testing, clean up test data:
- Remove test users from Supabase
- Clear test registrations
- Remove test payment records

## Integration with CI/CD

For automated testing:

```bash
# Headless mode for CI
npx playwright test tests/full-user-journey-e2e.spec.js --reporter=json

# With specific configuration
npx playwright test tests/full-user-journey-e2e.spec.js --config=playwright.ci.config.js
```

## Next Steps

1. **Run the test** and review all generated screenshots
2. **Verify email notifications** were sent correctly
3. **Check database records** for completeness
4. **Test payment variations** (declined cards, different amounts)
5. **Expand to other registration types** (Speaker, Sponsor)

---

**Note:** This test creates real database records and sends real emails (in test mode). Ensure proper cleanup after testing.
