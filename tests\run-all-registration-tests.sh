#!/bin/bash

# IEPA Registration Tests - Complete Test Suite Runner
# This script runs all implemented registration type tests with browser visibility

echo "🚀 IEPA Registration Tests - Complete Test Suite"
echo "================================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
HEADED_MODE="--headed"
SLOW_MO="--slowMo=500"
PROJECT="--project=chromium"
TIMEOUT="--timeout=120000"

# Check if development server is running
echo "🔍 Checking if development server is running on port 6969..."
if curl -s http://localhost:6969 > /dev/null; then
    echo -e "${GREEN}✅ Development server is running${NC}"
else
    echo -e "${RED}❌ Development server not running. Please start with: npm run dev${NC}"
    echo "   Make sure the server is running on port 6969"
    exit 1
fi

echo ""
echo "🎯 Test Execution Plan:"
echo "======================"
echo "Priority 1 (Core Flows):"
echo "  ✅ IEPA Member Registration (already implemented)"
echo "  ✅ Non-IEPA Member Registration"
echo "  ✅ Speaker Comped Registration"
echo "  ✅ Sponsor Attendee Registration"
echo ""
echo "Priority 2 (Special Cases):"
echo "  ✅ Government Registration"
echo "  ✅ Spouse Registration"
echo "  ✅ Child Registration"
echo ""
echo "🌐 Browser Mode: Chrome with visible UI"
echo "⏱️  Slow Motion: 500ms delays for visibility"
echo "⏰ Timeout: 2 minutes per test"
echo ""

# Function to run a test with error handling
run_test() {
    local test_file=$1
    local test_name=$2
    local priority=$3
    
    echo -e "${BLUE}🧪 Running: $test_name${NC}"
    echo "   File: $test_file"
    echo "   Priority: $priority"
    echo ""
    
    # Run the test
    if npx playwright test "$test_file" $HEADED_MODE $SLOW_MO $PROJECT $TIMEOUT; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        echo ""
        return 0
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        echo ""
        return 1
    fi
}

# Test execution counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

echo "🚀 Starting test execution..."
echo ""

# Priority 1 Tests (Core Flows)
echo -e "${YELLOW}📋 PRIORITY 1 TESTS (Core Flows)${NC}"
echo "================================"

# Test 1: IEPA Member Registration (already implemented)
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "tests/iepa-member-registration-complete-e2e.spec.js" "IEPA Member Registration" "Priority 1"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 2: Non-IEPA Member Registration
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "tests/non-iepa-member-registration-e2e.spec.js" "Non-IEPA Member Registration" "Priority 1"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 3: Speaker Comped Registration
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "tests/speaker-comped-registration-e2e.spec.js" "Speaker Comped Registration" "Priority 1"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 4: Sponsor Attendee Registration
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "tests/sponsor-attendee-registration-e2e.spec.js" "Sponsor Attendee Registration" "Priority 1"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

echo ""
echo -e "${YELLOW}📋 PRIORITY 2 TESTS (Special Cases)${NC}"
echo "==================================="

# Test 5: Government Registration
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "tests/government-registration-e2e.spec.js" "Government Registration" "Priority 2"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 6: Spouse Registration
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "tests/spouse-registration-e2e.spec.js" "Spouse Registration" "Priority 2"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test 7: Child Registration
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "tests/child-registration-e2e.spec.js" "Child Registration" "Priority 2"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Test Results Summary
echo ""
echo "🏁 TEST EXECUTION COMPLETE"
echo "=========================="
echo ""
echo -e "${BLUE}📊 Test Results Summary:${NC}"
echo "   Total Tests: $TOTAL_TESTS"
echo -e "   ${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "   ${RED}Failed: $FAILED_TESTS${NC}"
echo ""

# Calculate success rate
if [ $TOTAL_TESTS -gt 0 ]; then
    SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo "   Success Rate: $SUCCESS_RATE%"
else
    SUCCESS_RATE=0
fi

echo ""
echo "📁 Test Artifacts:"
echo "   Screenshots: test-results/"
echo "   Logs: Console output above"
echo ""

# Test coverage summary
echo "🎯 Registration Type Coverage:"
echo "   ✅ IEPA Member ($2,369)"
echo "   ✅ Non-IEPA Member ($2,730)"
echo "   ✅ Government ($2,060)"
echo "   ✅ Speaker Comped ($0)"
echo "   ✅ Sponsor Attendee ($0)"
echo "   ✅ Spouse ($500)"
echo "   ✅ Child ($100)"
echo ""

# Recommendations
echo "💡 Next Steps:"
if [ $SUCCESS_RATE -eq 100 ]; then
    echo -e "   ${GREEN}🎉 All tests passed! Ready for production testing.${NC}"
    echo "   📋 Consider implementing remaining registration types:"
    echo "      - CCA Member Registration"
    echo "      - Day Use Registrations"
    echo "      - Speaker Full Meeting"
    echo "      - Sponsor Registration"
    echo "      - Staff Registration"
elif [ $SUCCESS_RATE -ge 80 ]; then
    echo -e "   ${YELLOW}⚠️  Most tests passed. Review failed tests and fix issues.${NC}"
    echo "   🔧 Debug failed tests using: npx playwright test <test-file> --debug"
elif [ $SUCCESS_RATE -ge 50 ]; then
    echo -e "   ${YELLOW}⚠️  Some tests failed. Significant issues need attention.${NC}"
    echo "   🔧 Review test logs and application functionality"
else
    echo -e "   ${RED}❌ Many tests failed. Major issues need immediate attention.${NC}"
    echo "   🔧 Check development server, database, and application configuration"
fi

echo ""
echo "🛠️  Debugging Commands:"
echo "   Run single test with debug: npx playwright test tests/<test-file> --debug"
echo "   Run with browser visible: npx playwright test tests/<test-file> --headed"
echo "   Run with slow motion: npx playwright test tests/<test-file> --headed --slowMo=1000"
echo ""

# Exit with appropriate code
if [ $SUCCESS_RATE -eq 100 ]; then
    exit 0
elif [ $SUCCESS_RATE -ge 80 ]; then
    exit 1
else
    exit 2
fi
