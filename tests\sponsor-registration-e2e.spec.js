const { test, expect } = require('@playwright/test');

// Test configuration for production environment
const TEST_SPONSOR = {
  companyName: 'Solar Dynamics Corporation',
  contactFirstName: '<PERSON>',
  contactLastName: 'Chen',
  contactEmail: '<EMAIL>',
  password: 'TestPass123!',
  contactTitle: 'Marketing Director',
  phone: '(*************',
  website: 'https://solardynamics.com',
  streetAddress: '789 Energy Boulevard',
  city: 'Los Angeles',
  state: 'CA',
  zipCode: '90210',
  description: 'Solar Dynamics Corporation is a leading provider of innovative solar energy solutions for commercial and residential applications.',
  sponsorshipLevel: 'Gold',
  boothPreferences: 'Corner booth preferred, near main entrance',
  additionalServices: 'Logo on conference materials, speaking opportunity'
};

test.describe('Sponsor Registration Flow - Production E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set viewport for consistent testing
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Navigate to sponsor registration
    await page.goto('/register/sponsor');
    await page.waitForLoadState('networkidle');
  });

  test('should display sponsor registration page with tier selection', async ({ page }) => {
    console.log('🧪 Testing sponsor registration page layout...');
    
    // Check page title and main elements
    await expect(page).toHaveTitle(/IEPA.*Conference.*Registration/);
    await expect(page.locator('h1:has-text("Sponsor Registration")')).toBeVisible();
    
    // Take screenshot of full page
    await page.screenshot({ path: 'test-results/sponsor-registration-page.png', fullPage: true });
    
    console.log('✅ Sponsor registration page layout verified');
  });

  test('should display sponsor tier selection with card-based radio components', async ({ page }) => {
    console.log('🧪 Testing sponsor tier selection...');
    
    // Look for sponsor tier options
    const tierOptions = page.locator('text=Platinum, text=Gold, text=Silver, text=Bronze');
    const tierCount = await tierOptions.count();
    
    if (tierCount > 0) {
      console.log(`✅ Found ${tierCount} sponsor tier option(s)`);
    }
    
    // Look for tier icons (trophy, star, medal)
    const tierIcons = page.locator('[class*="trophy"], [class*="star"], [class*="medal"], svg');
    const iconCount = await tierIcons.count();
    
    if (iconCount > 0) {
      console.log(`✅ Found ${iconCount} tier icon(s)`);
    }
    
    // Look for card-based selection components
    const cardElements = page.locator('[class*="card"], [class*="border"], [class*="rounded"]');
    const cardCount = await cardElements.count();
    
    if (cardCount > 0) {
      console.log(`✅ Found ${cardCount} card-style element(s)`);
    }
    
    // Take screenshot of tier selection
    await page.screenshot({ path: 'test-results/sponsor-tier-selection.png', fullPage: true });
    
    console.log('✅ Sponsor tier selection verified');
  });

  test('should test sponsor-attendee linking functionality', async ({ page }) => {
    console.log('🧪 Testing sponsor-attendee linking...');
    
    // Look for attendee linking elements
    const linkingElements = page.locator('text=Link Attendee, text=Attendee Email, text=Discount Code, input[name*="attendee"], input[name*="link"]');
    const linkingCount = await linkingElements.count();
    
    if (linkingCount > 0) {
      console.log(`✅ Found ${linkingCount} attendee linking element(s)`);
    }
    
    // Look for email domain matching elements
    const domainElements = page.locator('text=Domain, text=Email Domain, text=Company Email');
    const domainCount = await domainElements.count();
    
    if (domainCount > 0) {
      console.log(`✅ Found ${domainCount} domain matching element(s)`);
    }
    
    // Look for discount code elements
    const discountElements = page.locator('text=Discount, text=Coupon, text=Code, input[name*="discount"], input[name*="coupon"]');
    const discountCount = await discountElements.count();
    
    if (discountCount > 0) {
      console.log(`✅ Found ${discountCount} discount code element(s)`);
    }
    
    await page.screenshot({ path: 'test-results/sponsor-attendee-linking.png', fullPage: true });
    
    console.log('✅ Sponsor-attendee linking testing completed');
  });

  test('should test automatic 100% discount application', async ({ page }) => {
    console.log('🧪 Testing automatic discount application...');
    
    // Fill sponsor email with company domain
    const emailField = page.locator('input[placeholder*="email"], input[name="email"], input[name="contactEmail"]');
    if (await emailField.isVisible()) {
      await emailField.fill(TEST_SPONSOR.contactEmail);
      await page.waitForTimeout(1000);
      
      // Look for discount application indicators
      const discountIndicators = page.locator('text=100%, text=Discount Applied, text=Free, text=$0');
      const discountCount = await discountIndicators.count();
      
      if (discountCount > 0) {
        console.log(`✅ Found ${discountCount} discount indicator(s)`);
        await page.screenshot({ path: 'test-results/sponsor-discount-applied.png', fullPage: true });
      }
    }
    
    console.log('✅ Automatic discount testing completed');
  });

  test('should fill out sponsor company information', async ({ page }) => {
    console.log('🧪 Testing sponsor company information form...');
    
    // Fill company information
    await page.fill('input[placeholder*="company"], input[name="companyName"]', TEST_SPONSOR.companyName);
    await page.fill('input[placeholder*="first name"], input[name="contactFirstName"]', TEST_SPONSOR.contactFirstName);
    await page.fill('input[placeholder*="last name"], input[name="contactLastName"]', TEST_SPONSOR.contactLastName);
    await page.fill('input[placeholder*="email"], input[name="contactEmail"]', TEST_SPONSOR.contactEmail);
    await page.fill('input[placeholder*="title"], input[name="contactTitle"]', TEST_SPONSOR.contactTitle);
    
    await page.screenshot({ path: 'test-results/sponsor-company-info-filled.png', fullPage: true });
    
    // Fill contact information
    await page.fill('input[placeholder*="phone"], input[name="phone"]', TEST_SPONSOR.phone);
    await page.fill('input[placeholder*="website"], input[name="website"]', TEST_SPONSOR.website);
    await page.fill('input[placeholder*="address"], input[name="streetAddress"]', TEST_SPONSOR.streetAddress);
    await page.fill('input[placeholder*="city"], input[name="city"]', TEST_SPONSOR.city);
    
    // Select state
    const stateSelect = page.locator('select[name="state"]');
    if (await stateSelect.isVisible()) {
      await stateSelect.selectOption(TEST_SPONSOR.state);
    }
    
    await page.fill('input[placeholder*="zip"], input[name="zipCode"]', TEST_SPONSOR.zipCode);
    
    // Fill company description
    const descField = page.locator('textarea[placeholder*="description"], textarea[name="description"], textarea[placeholder*="company"]');
    if (await descField.isVisible()) {
      await descField.fill(TEST_SPONSOR.description);
    }
    
    await page.screenshot({ path: 'test-results/sponsor-contact-info-filled.png', fullPage: true });
    
    console.log('✅ Sponsor company information completed');
  });

  test('should test logo and marketing material uploads', async ({ page }) => {
    console.log('🧪 Testing logo and marketing material uploads...');
    
    // Look for file upload elements
    const fileInputs = page.locator('input[type="file"]');
    const uploadButtons = page.locator('button:has-text("Upload"), button:has-text("Choose File"), text=Upload Logo');
    const logoElements = page.locator('text=Logo, text=Image, text=Marketing Material');
    
    const fileInputCount = await fileInputs.count();
    const uploadButtonCount = await uploadButtons.count();
    const logoElementCount = await logoElements.count();
    
    console.log(`✅ Found ${fileInputCount} file input(s)`);
    console.log(`✅ Found ${uploadButtonCount} upload button(s)`);
    console.log(`✅ Found ${logoElementCount} logo/marketing element(s)`);
    
    // Check file type acceptance
    const fileInput = fileInputs.first();
    if (await fileInput.isVisible()) {
      const acceptAttr = await fileInput.getAttribute('accept');
      if (acceptAttr) {
        console.log(`✅ File input accepts: ${acceptAttr}`);
      }
    }
    
    await page.screenshot({ path: 'test-results/sponsor-file-uploads.png', fullPage: true });
    
    console.log('✅ Logo and marketing material upload testing completed');
  });

  test('should test booth preferences and additional services', async ({ page }) => {
    console.log('🧪 Testing booth preferences and additional services...');
    
    // Fill booth preferences
    const boothField = page.locator('textarea[placeholder*="booth"], textarea[name="boothPreferences"], input[placeholder*="booth"]');
    if (await boothField.isVisible()) {
      await boothField.fill(TEST_SPONSOR.boothPreferences);
      console.log('✅ Filled booth preferences');
    }
    
    // Fill additional services
    const servicesField = page.locator('textarea[placeholder*="services"], textarea[name="additionalServices"], textarea[placeholder*="additional"]');
    if (await servicesField.isVisible()) {
      await servicesField.fill(TEST_SPONSOR.additionalServices);
      console.log('✅ Filled additional services');
    }
    
    // Look for service checkboxes or options
    const serviceOptions = page.locator('input[type="checkbox"], text=Speaking Opportunity, text=Logo Placement, text=Booth Setup');
    const serviceCount = await serviceOptions.count();
    
    if (serviceCount > 0) {
      console.log(`✅ Found ${serviceCount} service option(s)`);
    }
    
    await page.screenshot({ path: 'test-results/sponsor-booth-services.png', fullPage: true });
    
    console.log('✅ Booth preferences and services testing completed');
  });

  test('should test sponsor discount code functionality', async ({ page }) => {
    console.log('🧪 Testing sponsor discount code functionality...');
    
    // Look for discount code input
    const discountField = page.locator('input[placeholder*="discount"], input[name="discountCode"], input[placeholder*="coupon"]');
    
    if (await discountField.isVisible()) {
      // Test entering a discount code
      await discountField.fill('SPONSOR2025');
      await page.waitForTimeout(1000);
      
      // Look for apply button
      const applyButton = page.locator('button:has-text("Apply"), button:has-text("Validate")');
      if (await applyButton.isVisible()) {
        await applyButton.click();
        await page.waitForTimeout(1000);
        
        // Look for discount feedback
        const feedback = page.locator('text=Applied, text=Valid, text=Invalid, text=Error');
        const feedbackCount = await feedback.count();
        
        if (feedbackCount > 0) {
          console.log(`✅ Found ${feedbackCount} discount feedback element(s)`);
        }
      }
    }
    
    await page.screenshot({ path: 'test-results/sponsor-discount-code.png', fullPage: true });
    
    console.log('✅ Sponsor discount code testing completed');
  });

  test('should test form validation for sponsor registration', async ({ page }) => {
    console.log('🧪 Testing sponsor form validation...');
    
    // Try to submit without required fields
    const submitButton = page.locator('button[type="submit"], button:has-text("Submit"), button:has-text("Register")');
    
    if (await submitButton.isVisible()) {
      await submitButton.click();
      await page.waitForTimeout(1000);
      
      // Look for validation errors
      const errorElements = page.locator('[class*="error"], [class*="invalid"], .text-red-500, text=required, text=Required');
      const errorCount = await errorElements.count();
      
      if (errorCount > 0) {
        console.log(`✅ Found ${errorCount} validation error(s)`);
        await page.screenshot({ path: 'test-results/sponsor-validation-errors.png', fullPage: true });
      }
    }
    
    console.log('✅ Sponsor form validation tested');
  });

  test('should test responsive design for sponsor registration', async ({ page }) => {
    console.log('🧪 Testing sponsor registration responsive design...');
    
    // Test mobile viewport (375px)
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/sponsor-mobile-375px.png', fullPage: true });
    
    // Test tablet viewport (768px)
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/sponsor-tablet-768px.png', fullPage: true });
    
    // Test desktop viewport (1200px)
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/sponsor-desktop-1200px.png', fullPage: true });
    
    console.log('✅ Sponsor responsive design testing completed');
  });

  test('should verify shadcn/ui card components with tier icons', async ({ page }) => {
    console.log('🧪 Testing shadcn/ui components and tier icons...');
    
    // Look for shadcn/ui card components
    const cardComponents = page.locator('[class*="card"], [class*="border"], [class*="rounded"], [class*="shadow"]');
    const cardCount = await cardComponents.count();
    
    if (cardCount > 0) {
      console.log(`✅ Found ${cardCount} card-style component(s)`);
    }
    
    // Look for tier icons (trophy, star, medal)
    const trophyIcons = page.locator('[class*="trophy"], svg[class*="trophy"]');
    const starIcons = page.locator('[class*="star"], svg[class*="star"]');
    const medalIcons = page.locator('[class*="medal"], svg[class*="medal"]');
    
    const trophyCount = await trophyIcons.count();
    const starCount = await starIcons.count();
    const medalCount = await medalIcons.count();
    
    console.log(`✅ Found ${trophyCount} trophy icon(s)`);
    console.log(`✅ Found ${starCount} star icon(s)`);
    console.log(`✅ Found ${medalCount} medal icon(s)`);
    
    // Test card interactivity (hover effects)
    const firstCard = cardComponents.first();
    if (await firstCard.isVisible()) {
      await firstCard.hover();
      await page.waitForTimeout(500);
      console.log('✅ Tested card hover interaction');
    }
    
    await page.screenshot({ path: 'test-results/sponsor-ui-components.png', fullPage: true });
    
    console.log('✅ UI components and tier icons verified');
  });
});

test.describe('Sponsor Registration - Profile Linking', () => {
  test('should test profile linking between sponsor and attendee registrations', async ({ page }) => {
    console.log('🧪 Testing sponsor-attendee profile linking...');
    
    // Look for profile linking elements
    const linkingElements = page.locator('text=Link Profile, text=Attendee Profile, text=Connect Attendee');
    const linkingCount = await linkingElements.count();
    
    if (linkingCount > 0) {
      console.log(`✅ Found ${linkingCount} profile linking element(s)`);
    }
    
    // Test email domain matching
    const emailField = page.locator('input[name="contactEmail"], input[placeholder*="email"]');
    if (await emailField.isVisible()) {
      await emailField.fill('<EMAIL>');
      await page.waitForTimeout(1000);
      
      // Look for domain matching indicators
      const domainMatch = page.locator('text=Domain Match, text=Company Match, text=Automatic Link');
      const matchCount = await domainMatch.count();
      
      if (matchCount > 0) {
        console.log(`✅ Found ${matchCount} domain matching element(s)`);
      }
    }
    
    await page.screenshot({ path: 'test-results/sponsor-profile-linking.png', fullPage: true });
    
    console.log('✅ Profile linking testing completed');
  });
});
