'use client';

import { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function DomainRedirectPage() {
  useEffect(() => {
    // Auto-redirect to correct domain after 5 seconds
    const timer = setTimeout(() => {
      const currentUrl = window.location.href;
      const correctUrl = currentUrl.replace('reg.iepa.com', 'iepa.vercel.app');
      window.location.href = correctUrl;
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  const handleRedirect = () => {
    const currentUrl = window.location.href;
    const correctUrl = currentUrl.replace('reg.iepa.com', 'iepa.vercel.app');
    window.location.href = correctUrl;
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-center text-orange-600">
                Domain Redirect Required
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center space-y-4">
                <div className="text-orange-600 text-4xl mb-4">🔄</div>

                <p className="iepa-body">
                  You&apos;re on <strong>reg.iepa.com</strong> but the IEPA
                  conference registration app is now deployed at{' '}
                  <strong>iepa.vercel.app</strong>.
                </p>

                <p className="text-sm text-gray-600">
                  You will be automatically redirected in 5 seconds, or click
                  the button below.
                </p>

                <div className="pt-4 space-y-3">
                  <Button onClick={handleRedirect} className="w-full">
                    Go to iepa.vercel.app
                  </Button>

                  <Button asChild variant="outline" className="w-full">
                    <Link href="https://iepa.vercel.app/auth/login">
                      Login at Correct Domain
                    </Link>
                  </Button>
                </div>

                <div className="mt-6 p-3 bg-blue-50 border border-blue-200 rounded text-xs">
                  <p className="font-medium text-blue-800 mb-1">
                    For Administrators:
                  </p>
                  <p className="text-blue-700">
                    Update the Supabase Site URL configuration to point to
                    iepa.vercel.app instead of reg.iepa.com to fix this redirect
                    issue.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
