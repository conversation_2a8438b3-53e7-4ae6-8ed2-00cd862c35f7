#!/usr/bin/env node

/**
 * IEPA Conference Registration - Full User Journey Test Runner
 *
 * This script runs comprehensive end-to-end tests covering:
 * 1. New user signup and authentication
 * 2. Complete registration form filling
 * 3. Payment processing with Stripe
 * 4. Success confirmation and user dashboard
 *
 * Usage: node scripts/run-full-user-journey-test.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const TEST_CONFIG = {
  testFile: 'tests/full-user-journey-e2e.spec.js',
  outputDir: 'test-results',
  browsers: ['chromium'], // Start with chromium, can add 'firefox', 'webkit'
  headless: false, // Set to true for CI/automated runs
  timeout: 120000, // 2 minutes per test
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function createDirectories() {
  const dirs = [
    TEST_CONFIG.outputDir,
    'test-results/screenshots',
    'test-results/videos',
    'test-results/reports',
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      log(`📁 Created directory: ${dir}`, 'cyan');
    }
  });
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');

  // Check if development server is running
  try {
    execSync('curl -f http://localhost:6969 > /dev/null 2>&1', {
      stdio: 'ignore',
    });
    log('✅ Development server is running on port 6969', 'green');
  } catch (error) {
    log('❌ Development server is not running on port 6969', 'red');
    log('💡 Please start the development server with: npm run dev', 'yellow');
    process.exit(1);
  }

  // Check if test file exists
  if (!fs.existsSync(TEST_CONFIG.testFile)) {
    log(`❌ Test file not found: ${TEST_CONFIG.testFile}`, 'red');
    process.exit(1);
  }

  log('✅ All prerequisites met', 'green');
}

function runPlaywrightTest() {
  log('🚀 Starting full user journey tests...', 'bright');
  log(`📁 Test file: ${TEST_CONFIG.testFile}`, 'cyan');
  log(`🌐 Target URL: http://localhost:6969`, 'cyan');
  log(`⏱️  Timeout: ${TEST_CONFIG.timeout / 1000} seconds per test`, 'cyan');

  const playwrightCommand = [
    'npx playwright test',
    TEST_CONFIG.testFile,
    '--headed', // Show browser for debugging
    `--timeout=${TEST_CONFIG.timeout}`,
    '--reporter=list,html',
    '--output=test-results',
    '--screenshot=only-on-failure',
    '--video=retain-on-failure',
    '--trace=retain-on-failure',
  ].join(' ');

  log(`🔧 Command: ${playwrightCommand}`, 'cyan');

  try {
    execSync(playwrightCommand, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });

    log('✅ Full user journey tests completed successfully!', 'green');
    return true;
  } catch (error) {
    log('❌ Tests failed or encountered errors', 'red');
    log('📊 Check the HTML report for detailed results', 'yellow');
    return false;
  }
}

function generateSummaryReport() {
  log('📊 Generating test summary...', 'blue');

  const timestamp = new Date().toISOString();
  const reportPath = path.join(
    TEST_CONFIG.outputDir,
    'full-journey-test-summary.md'
  );

  const summary = `# Full User Journey Test Summary

**Test Run Date:** ${timestamp}
**Test Environment:** Development (http://localhost:6969)
**Test File:** ${TEST_CONFIG.testFile}

## Test Scenarios Covered

### 1. Complete New User Journey
- ✅ Homepage navigation
- ✅ Registration type selection (Attendee)
- ✅ New user authentication/signup
- ✅ Complete registration form filling
- ✅ Payment processing with Stripe
- ✅ Success confirmation
- ✅ User dashboard navigation

### 2. Existing User Journey
- ✅ Login with existing test account
- ✅ Quick registration form completion
- ✅ Form submission and processing

### 3. Form Validation Testing
- ✅ Incomplete form submission
- ✅ Validation error display
- ✅ Error handling

## Test Results

Screenshots and detailed results are available in the \`${TEST_CONFIG.outputDir}\` directory.

### Key Files Generated
- \`journey-01-homepage.png\` - Initial homepage
- \`journey-05-after-signup.png\` - After user signup
- \`journey-13-stripe-payment-filled.png\` - Stripe payment form
- \`journey-15-success-confirmation.png\` - Success page
- \`journey-16-user-dashboard.png\` - User dashboard

## Next Steps

1. Review all generated screenshots for UI/UX issues
2. Verify email notifications were sent (check email logs)
3. Confirm database records were created correctly
4. Test with different registration types (Speaker, Sponsor)
5. Test with different payment scenarios (declined cards, etc.)

## Notes

- Test uses unique email addresses to avoid conflicts
- Stripe test cards are used for payment processing
- All test data should be cleaned up after testing
`;

  fs.writeFileSync(reportPath, summary);
  log(`📄 Summary report generated: ${reportPath}`, 'green');
}

function main() {
  log(
    '🎯 IEPA Conference Registration - Full User Journey Test Runner',
    'bright'
  );
  log('='.repeat(60), 'cyan');

  try {
    createDirectories();
    checkPrerequisites();

    const success = runPlaywrightTest();

    generateSummaryReport();

    if (success) {
      log('🎉 All tests completed successfully!', 'green');
      log(
        '📊 Open playwright-report/index.html to view detailed results',
        'cyan'
      );
    } else {
      log('⚠️  Some tests failed - check the reports for details', 'yellow');
    }
  } catch (error) {
    log(`💥 Script error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  runPlaywrightTest,
  TEST_CONFIG,
};
