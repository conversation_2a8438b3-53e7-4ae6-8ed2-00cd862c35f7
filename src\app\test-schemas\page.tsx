'use client';

import { useEffect, useState } from 'react';
import { Card, CardBody, CardHeader } from '@/components/ui';
import {
  validateSchemaIntegrity,
  getSchemaValidationSummary,
  type ValidationResult,
} from '@/utils/schema-validation';

interface SchemaValidationSummary {
  schemas: {
    attendee: { version: string; lastUpdated: string };
    speaker: { version: string; lastUpdated: string };
    sponsor: { version: string; lastUpdated: string };
  };
  integrity: ValidationResult;
  configurationStatus: {
    registrationTypes: number;
    sponsorshipLevels: number;
    mealOptions: number;
  };
}

export default function TestSchemasPage() {
  const [integrityResult, setIntegrityResult] =
    useState<ValidationResult | null>(null);
  const [summary, setSummary] = useState<SchemaValidationSummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const runTests = async () => {
      try {
        const integrity = validateSchemaIntegrity();
        const validationSummary = getSchemaValidationSummary();

        setIntegrityResult(integrity);
        setSummary(validationSummary);
      } catch (error) {
        console.error('Error running schema tests:', error);
      } finally {
        setLoading(false);
      }
    };

    runTests();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Schema Validation Tests</h1>
        <p>Loading tests...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold mb-6">Schema Validation Tests</h1>

      {/* Schema Integrity */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Schema Integrity Check</h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <span
                className={`text-lg font-medium ${
                  integrityResult?.isValid ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {integrityResult?.isValid ? '✅ PASSED' : '❌ FAILED'}
              </span>
              <span>Schema integrity validation</span>
            </div>

            {integrityResult?.errors && integrityResult.errors.length > 0 && (
              <div>
                <h3 className="font-medium text-red-600 mb-2">Errors:</h3>
                <ul className="list-disc list-inside space-y-1">
                  {integrityResult.errors.map(
                    (error: string, index: number) => (
                      <li key={index} className="text-red-600">
                        {error}
                      </li>
                    )
                  )}
                </ul>
              </div>
            )}

            {integrityResult?.warnings &&
              integrityResult.warnings.length > 0 && (
                <div>
                  <h3 className="font-medium text-yellow-600 mb-2">
                    Warnings:
                  </h3>
                  <ul className="list-disc list-inside space-y-1">
                    {integrityResult.warnings.map(
                      (warning: string, index: number) => (
                        <li key={index} className="text-yellow-600">
                          {warning}
                        </li>
                      )
                    )}
                  </ul>
                </div>
              )}
          </div>
        </CardBody>
      </Card>

      {/* Schema Summary */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Schema Summary</h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Attendee Schema */}
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">Attendee Schema</h3>
              <p className="text-sm text-gray-600">
                Version: {summary?.schemas?.attendee?.version}
              </p>
              <p className="text-sm text-gray-600">
                Updated: {summary?.schemas?.attendee?.lastUpdated}
              </p>
            </div>

            {/* Speaker Schema */}
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">Speaker Schema</h3>
              <p className="text-sm text-gray-600">
                Version: {summary?.schemas?.speaker?.version}
              </p>
              <p className="text-sm text-gray-600">
                Updated: {summary?.schemas?.speaker?.lastUpdated}
              </p>
            </div>

            {/* Sponsor Schema */}
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">Sponsor Schema</h3>
              <p className="text-sm text-gray-600">
                Version: {summary?.schemas?.sponsor?.version}
              </p>
              <p className="text-sm text-gray-600">
                Updated: {summary?.schemas?.sponsor?.lastUpdated}
              </p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">Configuration Status</h3>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium">Registration Types:</span>{' '}
                {summary?.configurationStatus?.registrationTypes}
              </div>
              <div>
                <span className="font-medium">Sponsorship Levels:</span>{' '}
                {summary?.configurationStatus?.sponsorshipLevels}
              </div>
              <div>
                <span className="font-medium">Meal Options:</span>{' '}
                {summary?.configurationStatus?.mealOptions}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Task 2.3 Status */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Task 2.3 Completion Status</h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span>Updated attendee-iepa-2025.json schema</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span>Updated speaker-iepa-2025.json schema</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span>Updated sponsor-iepa-2025.json schema</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span>Validated all form field requirements</span>
            </div>
            <div className="flex items-center gap-2">
              <span
                className={
                  integrityResult?.isValid ? 'text-green-600' : 'text-red-600'
                }
              >
                {integrityResult?.isValid ? '✅' : '❌'}
              </span>
              <span>Tested schema validation</span>
            </div>
          </div>

          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">Task 2.3 Summary</h3>
            <p className="text-blue-700 text-sm">
              All schema files have been successfully updated for 2025
              requirements. Schemas now integrate with pricing and conference
              configurations, include enhanced validation rules, and support
              improved file upload constraints.
            </p>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
