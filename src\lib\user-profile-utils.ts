import { supabase } from './supabase';

// Types for user profile data
export interface UserProfile {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  phone_number?: string;
  organization?: string;
  job_title?: string;
  street_address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  gender?: string;
  preferred_name_on_badge?: string;
  imported_from_2024: boolean;
  import_date?: string;
  created_at: string;
  updated_at: string;
}

export interface HistoricalRegistration {
  id: string;
  user_id: string;
  profile_id: string;
  event_year: number;
  event_name: string;
  registration_date?: string;
  attendee_type: string;
  attendee_type_iepa?: string;
  status: string;
  name_on_badge?: string;
  nights_staying?: string;
  meals?: string[];
  special_dietary_needs?: string;
  golf_tournament: boolean;
  golf_club_rental?: string;
  golf_cell_number?: string;
  golf_total: number;
  grand_total: number;
  payment_status: string;
  imported_from_source?: string;
  original_data?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface UserProfileWithHistory {
  profile: UserProfile;
  historicalRegistrations: HistoricalRegistration[];
  isReturningAttendee: boolean;
  lastAttendance?: HistoricalRegistration;
}

// Utility functions for working with user profiles
export const userProfileUtils = {
  // Get user profile by user ID
  getUserProfile: async (userId: string): Promise<UserProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('iepa_user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // Not found
          return null;
        }
        throw error;
      }

      return data as UserProfile;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  },

  // Get user profile by email
  getUserProfileByEmail: async (email: string): Promise<UserProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('iepa_user_profiles')
        .select('*')
        .eq('email', email.toLowerCase())
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // Not found
          return null;
        }
        throw error;
      }

      return data as UserProfile;
    } catch (error) {
      console.error('Error fetching user profile by email:', error);
      return null;
    }
  },

  // Get historical registrations for a user
  getHistoricalRegistrations: async (
    userId: string
  ): Promise<HistoricalRegistration[]> => {
    try {
      const { data, error } = await supabase
        .from('iepa_historical_registrations')
        .select('*')
        .eq('user_id', userId)
        .order('event_year', { ascending: false });

      if (error) {
        throw error;
      }

      return data as HistoricalRegistration[];
    } catch (error) {
      console.error('Error fetching historical registrations:', error);
      return [];
    }
  },

  // Get complete user profile with history
  getUserProfileWithHistory: async (
    userId: string
  ): Promise<UserProfileWithHistory | null> => {
    try {
      const profile = await userProfileUtils.getUserProfile(userId);
      if (!profile) {
        return null;
      }

      const historicalRegistrations =
        await userProfileUtils.getHistoricalRegistrations(userId);
      const isReturningAttendee = historicalRegistrations.length > 0;
      const lastAttendance = historicalRegistrations[0]; // Most recent (ordered by year desc)

      return {
        profile,
        historicalRegistrations,
        isReturningAttendee,
        lastAttendance,
      };
    } catch (error) {
      console.error('Error fetching user profile with history:', error);
      return null;
    }
  },

  // Create or update user profile
  upsertUserProfile: async (
    userId: string,
    profileData: Partial<UserProfile>
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const { error } = await supabase.from('iepa_user_profiles').upsert(
        {
          user_id: userId,
          ...profileData,
          updated_at: new Date().toISOString(),
        },
        {
          onConflict: 'user_id',
        }
      );

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error upserting user profile:', error);
      return { success: false, error: 'Failed to save user profile' };
    }
  },

  // Pre-populate form data from user profile
  getFormDefaults: (profileWithHistory: UserProfileWithHistory | null) => {
    if (!profileWithHistory) {
      return {};
    }

    const { profile, lastAttendance } = profileWithHistory;

    return {
      // Personal Information
      firstName: profile.first_name || '',
      lastName: profile.last_name || '',
      email: profile.email || '',
      phoneNumber: profile.phone_number || '',
      nameOnBadge: profile.preferred_name_on_badge || profile.first_name || '',
      gender: profile.gender || '',

      // Professional Information
      organization: profile.organization || '',
      jobTitle: profile.job_title || '',

      // Address Information
      streetAddress: profile.street_address || '',
      city: profile.city || '',
      state: profile.state || '',
      zipCode: profile.zip_code || '',
      country: profile.country || 'United States',

      // Preferences from last attendance (if available)
      specialDietaryNeeds: lastAttendance?.special_dietary_needs || '',
      golfCellNumber:
        lastAttendance?.golf_cell_number || profile.phone_number || '',

      // Suggested defaults based on history
      suggestedMeals: lastAttendance?.meals || [],
      previouslyPlayedGolf: lastAttendance?.golf_tournament || false,
      previousGolfClubRental: lastAttendance?.golf_club_rental || '',
      previousNightsStaying: lastAttendance?.nights_staying || '',
    };
  },

  // Check if user is a returning attendee
  isReturningAttendee: async (userId: string): Promise<boolean> => {
    try {
      const { count, error } = await supabase
        .from('iepa_historical_registrations')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (error) {
        throw error;
      }

      return (count || 0) > 0;
    } catch (error) {
      console.error('Error checking returning attendee status:', error);
      return false;
    }
  },

  // Get attendee statistics
  getAttendeeStats: async () => {
    try {
      // Total imported users
      const { count: totalImported } = await supabase
        .from('iepa_user_profiles')
        .select('*', { count: 'exact', head: true })
        .eq('imported_from_2024', true);

      // Total historical registrations
      const { count: totalHistorical } = await supabase
        .from('iepa_historical_registrations')
        .select('*', { count: 'exact', head: true });

      // Current year registrations
      const { count: currentRegistrations } = await supabase
        .from('iepa_attendee_registrations')
        .select('*', { count: 'exact', head: true });

      return {
        totalImportedUsers: totalImported || 0,
        totalHistoricalRegistrations: totalHistorical || 0,
        currentYearRegistrations: currentRegistrations || 0,
      };
    } catch (error) {
      console.error('Error fetching attendee stats:', error);
      return {
        totalImportedUsers: 0,
        totalHistoricalRegistrations: 0,
        currentYearRegistrations: 0,
      };
    }
  },

  // Search users by name or email
  searchUsers: async (
    query: string,
    limit: number = 10
  ): Promise<UserProfile[]> => {
    try {
      const { data, error } = await supabase
        .from('iepa_user_profiles')
        .select('*')
        .or(
          `full_name.ilike.%${query}%,email.ilike.%${query}%,organization.ilike.%${query}%`
        )
        .limit(limit)
        .order('full_name');

      if (error) {
        throw error;
      }

      return data as UserProfile[];
    } catch (error) {
      console.error('Error searching users:', error);
      return [];
    }
  },
};
