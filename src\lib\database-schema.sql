-- IEPA 2025 Conference Registration Database Schema
-- Run this in your Supabase SQL editor to set up the database

-- Create iepa_user_profiles table for general user information
CREATE TABLE IF NOT EXISTS iepa_user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    email TEXT NOT NULL,
    phone_number TEXT,
    organization TEXT,
    job_title TEXT,
    street_address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    country TEXT DEFAULT 'United States',
    gender TEXT,
    preferred_name_on_badge TEXT,
    -- Import tracking
    imported_from_2024 BOOLEAN DEFAULT FALSE,
    import_date TIMESTAMP WITH TIME ZONE,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create organizations table for managing organization data
CREATE TABLE IF NOT EXISTS iepa_organizations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    normalized_name TEXT NOT NULL, -- Lowercase, trimmed for searching
    industry TEXT,
    description TEXT,
    website_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0, -- Track how many times this org has been used
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_iepa_organizations_normalized_name ON iepa_organizations(normalized_name);
CREATE INDEX IF NOT EXISTS idx_iepa_organizations_usage_count ON iepa_organizations(usage_count DESC);

-- Create iepa_historical_registrations table for past event data
CREATE TABLE IF NOT EXISTS iepa_historical_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    profile_id UUID REFERENCES iepa_user_profiles(id) ON DELETE CASCADE,
    -- Event Information
    event_year INTEGER NOT NULL,
    event_name TEXT DEFAULT 'IEPA Annual Meeting',
    registration_date TIMESTAMP WITH TIME ZONE,
    -- Registration Details
    attendee_type TEXT NOT NULL,
    attendee_type_iepa TEXT,
    status TEXT NOT NULL,
    name_on_badge TEXT,
    -- Organization information (ADDED)
    organization TEXT,
    job_title TEXT,
    -- Lodging and Meals
    nights_staying TEXT,
    meals TEXT[],
    special_dietary_needs TEXT,
    -- Golf Information
    golf_tournament BOOLEAN DEFAULT FALSE,
    golf_club_rental TEXT,
    golf_cell_number TEXT,
    golf_total DECIMAL(10,2) DEFAULT 0,
    -- Financial Information
    grand_total DECIMAL(10,2) DEFAULT 0,
    payment_status TEXT DEFAULT 'completed',
    -- Import tracking
    imported_from_source TEXT,
    original_data JSONB,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_attendee_registrations table
CREATE TABLE IF NOT EXISTS iepa_attendee_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    registration_type TEXT NOT NULL,
    -- Attendee type and linking for spouse/child registrations
    attendee_type TEXT NOT NULL DEFAULT 'attendee' CHECK (attendee_type IN ('attendee', 'spouse', 'child')),
    linked_attendee_email TEXT, -- Email of primary attendee for spouse/child registrations
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    email TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    name_on_badge TEXT NOT NULL,
    gender TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    street_address TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    zip_code TEXT NOT NULL,
    organization TEXT NOT NULL,
    job_title TEXT NOT NULL,
    attending_golf BOOLEAN DEFAULT FALSE,
    golf_club_rental BOOLEAN DEFAULT FALSE,
    golf_club_handedness TEXT DEFAULT '',
    meals TEXT[] DEFAULT '{}',
    dietary_needs TEXT DEFAULT '',
    registration_total DECIMAL(10,2) DEFAULT 0,
    golf_total DECIMAL(10,2) DEFAULT 0,
    golf_club_rental_total DECIMAL(10,2) DEFAULT 0,
    meal_total DECIMAL(10,2) DEFAULT 0,
    grand_total DECIMAL(10,2) DEFAULT 0,
    -- Discount tracking
    discount_code TEXT,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    original_total DECIMAL(10,2) DEFAULT 0,
    payment_status TEXT DEFAULT 'pending',
    payment_id TEXT,
    receipt_url TEXT,
    receipt_generated_at TIMESTAMP WITH TIME ZONE,
    invoice_url TEXT,
    invoice_generated_at TIMESTAMP WITH TIME ZONE,
    -- Speaker linking fields
    speaker_registration_id UUID REFERENCES iepa_speaker_registrations(id) ON DELETE SET NULL,
    is_speaker BOOLEAN DEFAULT FALSE,
    speaker_pricing_type TEXT, -- 'comped-speaker' or 'full-meeting-speaker'
    -- Sponsor linking fields
    sponsor_id UUID REFERENCES iepa_sponsor_registrations(id) ON DELETE SET NULL,
    sponsor_discount_code TEXT, -- Track which sponsor discount code was used
    is_sponsor_attendee BOOLEAN DEFAULT FALSE, -- Flag for sponsor-linked attendees
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_speaker_registrations table
CREATE TABLE IF NOT EXISTS iepa_speaker_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    -- Personal Information
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT NOT NULL,
    -- Contact Information
    phone_number TEXT,
    preferred_contact_method TEXT DEFAULT 'email',
    -- Professional Information
    organization_name TEXT NOT NULL,
    job_title TEXT NOT NULL,
    bio TEXT NOT NULL,
    -- Presentation Information
    presentation_title TEXT,
    presentation_description TEXT,
    presentation_duration TEXT,
    target_audience TEXT,
    learning_objectives TEXT,
    -- Speaking Experience
    speaker_experience TEXT,
    previous_speaking TEXT,
    -- Technical Requirements
    equipment_needs TEXT,
    special_requests TEXT,
    -- File Uploads
    presentation_file_url TEXT,
    headshot_url TEXT,
    -- Attendee registration linking
    attendee_registration_id UUID REFERENCES iepa_attendee_registrations(id) ON DELETE SET NULL,
    speaker_pricing_type TEXT DEFAULT 'comped-speaker', -- 'comped-speaker' or 'full-meeting-speaker'
    -- Registration status for partial registrations
    registration_status TEXT DEFAULT 'complete' CHECK (registration_status IN ('partial', 'complete')),
    -- PDF Documents
    receipt_url TEXT,
    receipt_generated_at TIMESTAMP WITH TIME ZONE,
    invoice_url TEXT,
    invoice_generated_at TIMESTAMP WITH TIME ZONE,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_sponsor_registrations table
CREATE TABLE IF NOT EXISTS iepa_sponsor_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    sponsor_name TEXT NOT NULL,
    sponsor_url TEXT NOT NULL,
    sponsor_video TEXT,
    sponsor_image_url TEXT,
    sponsor_description TEXT NOT NULL,
    -- Contact information
    contact_name TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    contact_title TEXT,
    -- Sponsorship details
    sponsorship_level TEXT,
    sponsorship_amount DECIMAL(10,2),
    -- Billing information
    billing_address TEXT,
    billing_city TEXT,
    billing_state TEXT,
    billing_zip TEXT,
    billing_country TEXT DEFAULT 'United States',
    -- Additional information
    marketing_goals TEXT,
    exhibit_requirements TEXT,
    special_requests TEXT,
    attendee_count TEXT,
    attendee_names TEXT,
    -- Golf participation
    attending_golf BOOLEAN DEFAULT FALSE,
    -- Attendee linking field
    linked_attendee_email TEXT, -- Email of sponsor's attendee registration
    -- Company domain for automatic discounts
    company_domain TEXT, -- Extracted from sponsor_url or manually set
    payment_status TEXT DEFAULT 'pending',
    payment_id TEXT,
    receipt_url TEXT,
    receipt_generated_at TIMESTAMP WITH TIME ZONE,
    invoice_url TEXT,
    invoice_generated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_payments table
CREATE TABLE IF NOT EXISTS iepa_payments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    registration_id UUID NOT NULL,
    registration_type TEXT NOT NULL CHECK (registration_type IN ('attendee', 'sponsor', 'speaker')),
    stripe_payment_intent_id TEXT NOT NULL UNIQUE,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'usd',
    status TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_admin_users table for admin access control
CREATE TABLE IF NOT EXISTS iepa_admin_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL UNIQUE,
    role TEXT NOT NULL CHECK (role IN ('admin', 'super_admin')) DEFAULT 'admin',
    permissions JSONB NOT NULL DEFAULT '{"dashboard": true, "reports": true}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_discount_codes table for managing discount codes
CREATE TABLE IF NOT EXISTS iepa_discount_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    description TEXT,
    -- Discount configuration
    discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')) DEFAULT 'percentage',
    discount_value DECIMAL(10,2) NOT NULL CHECK (discount_value > 0),
    -- Stripe integration
    stripe_coupon_id TEXT UNIQUE,
    -- Usage limits
    max_uses INTEGER,
    max_uses_per_user INTEGER DEFAULT 1,
    current_uses INTEGER DEFAULT 0,
    -- Validity period
    valid_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE,
    -- Restrictions
    minimum_amount DECIMAL(10,2),
    applicable_registration_types TEXT[], -- ['attendee', 'sponsor'] or specific types
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    -- Admin tracking
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_discount_usage table for tracking discount code usage
CREATE TABLE IF NOT EXISTS iepa_discount_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    discount_code_id UUID REFERENCES iepa_discount_codes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    registration_id UUID, -- Can reference any registration table
    registration_type TEXT NOT NULL CHECK (registration_type IN ('attendee', 'sponsor', 'speaker')),
    -- Usage details
    original_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    final_amount DECIMAL(10,2) NOT NULL,
    -- Stripe tracking
    stripe_session_id TEXT,
    stripe_payment_intent_id TEXT,
    -- Timestamps
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_sponsor_domains table for email domain-based automatic discounts
CREATE TABLE IF NOT EXISTS iepa_sponsor_domains (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sponsor_id UUID REFERENCES iepa_sponsor_registrations(id) ON DELETE CASCADE,
    domain TEXT NOT NULL, -- e.g., 'acme.com'
    sponsor_name TEXT NOT NULL, -- Company name for reference
    -- Discount configuration
    discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')) DEFAULT 'percentage',
    discount_value DECIMAL(10,2) NOT NULL DEFAULT 100, -- Default 100% discount for sponsor attendees
    -- Auto-generated discount code
    auto_discount_code TEXT UNIQUE, -- Automatically generated code for this domain
    stripe_coupon_id TEXT, -- Associated Stripe coupon
    -- Status and limits
    is_active BOOLEAN DEFAULT TRUE,
    max_uses INTEGER, -- Optional limit on domain-based discounts
    current_uses INTEGER DEFAULT 0,
    -- Admin tracking
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Constraints
    UNIQUE(domain, sponsor_id)
);

-- Create iepa_conference_documents table for managing conference documents
CREATE TABLE IF NOT EXISTS iepa_conference_documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    file_url TEXT NOT NULL,
    file_size INTEGER,
    file_type TEXT,
    uploaded_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_iepa_user_profiles_updated_at
    BEFORE UPDATE ON iepa_user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_historical_registrations_updated_at
    BEFORE UPDATE ON iepa_historical_registrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_attendee_registrations_updated_at
    BEFORE UPDATE ON iepa_attendee_registrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_speaker_registrations_updated_at
    BEFORE UPDATE ON iepa_speaker_registrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_sponsor_registrations_updated_at
    BEFORE UPDATE ON iepa_sponsor_registrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_payments_updated_at
    BEFORE UPDATE ON iepa_payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_admin_users_updated_at
    BEFORE UPDATE ON iepa_admin_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_organizations_updated_at
    BEFORE UPDATE ON iepa_organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_sponsor_domains_updated_at
    BEFORE UPDATE ON iepa_sponsor_domains
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_conference_documents_updated_at
    BEFORE UPDATE ON iepa_conference_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE iepa_user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_historical_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_attendee_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_speaker_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_sponsor_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_sponsor_domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_conference_documents ENABLE ROW LEVEL SECURITY;

-- RLS Policies for iepa_user_profiles
CREATE POLICY "Users can view their own profile"
    ON iepa_user_profiles FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile"
    ON iepa_user_profiles FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile"
    ON iepa_user_profiles FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_historical_registrations
CREATE POLICY "Users can view their own historical registrations"
    ON iepa_historical_registrations FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own historical registrations"
    ON iepa_historical_registrations FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own historical registrations"
    ON iepa_historical_registrations FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_attendee_registrations
CREATE POLICY "Users can view their own attendee registrations"
    ON iepa_attendee_registrations FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own attendee registrations"
    ON iepa_attendee_registrations FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own attendee registrations"
    ON iepa_attendee_registrations FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_speaker_registrations
CREATE POLICY "Users can view their own speaker registrations"
    ON iepa_speaker_registrations FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own speaker registrations"
    ON iepa_speaker_registrations FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own speaker registrations"
    ON iepa_speaker_registrations FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_sponsor_registrations
CREATE POLICY "Users can view their own sponsor registrations"
    ON iepa_sponsor_registrations FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sponsor registrations"
    ON iepa_sponsor_registrations FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sponsor registrations"
    ON iepa_sponsor_registrations FOR UPDATE
    USING (auth.uid() = user_id);

-- RLS Policies for iepa_payments
CREATE POLICY "Users can view their own payments"
    ON iepa_payments FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own payments"
    ON iepa_payments FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- RLS Policies for iepa_admin_users
CREATE POLICY "Admin users can view all admin records"
    ON iepa_admin_users FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

CREATE POLICY "Super admins can manage admin users"
    ON iepa_admin_users FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.role = 'super_admin'
            AND admin.is_active = true
        )
    );

-- Admin policies (for service role)
CREATE POLICY "Service role can manage all user profiles"
    ON iepa_user_profiles FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all historical registrations"
    ON iepa_historical_registrations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all attendee registrations"
    ON iepa_attendee_registrations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all speaker registrations"
    ON iepa_speaker_registrations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all sponsor registrations"
    ON iepa_sponsor_registrations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all payments"
    ON iepa_payments FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all admin users"
    ON iepa_admin_users FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

-- RLS Policies for iepa_organizations (all authenticated users can read, only admins can write)
CREATE POLICY "Anyone can view active organizations"
    ON iepa_organizations FOR SELECT
    USING (is_active = true);

CREATE POLICY "Only admins can manage organizations"
    ON iepa_organizations FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

CREATE POLICY "Service role can manage all organizations"
    ON iepa_organizations FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

-- RLS Policies for iepa_sponsor_domains
CREATE POLICY "Anyone can view active sponsor domains"
    ON iepa_sponsor_domains FOR SELECT
    USING (is_active = true);

CREATE POLICY "Only admins can manage sponsor domains"
    ON iepa_sponsor_domains FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

CREATE POLICY "Service role can manage all sponsor domains"
    ON iepa_sponsor_domains FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

-- RLS Policies for iepa_conference_documents
CREATE POLICY "Only admins can view conference documents"
    ON iepa_conference_documents FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

CREATE POLICY "Only admins can manage conference documents"
    ON iepa_conference_documents FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users admin
            WHERE admin.email = auth.jwt() ->> 'email'
            AND admin.is_active = true
        )
    );

CREATE POLICY "Service role can manage all conference documents"
    ON iepa_conference_documents FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

-- Add unique constraints for one-registration-per-user
-- For attendee registrations: only primary attendees (not spouse/child) can have one registration per user
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_unique_primary_attendee_per_user
    ON iepa_attendee_registrations (user_id)
    WHERE attendee_type = 'attendee';

-- For speaker registrations: one registration per user
ALTER TABLE iepa_speaker_registrations
    ADD CONSTRAINT unique_speaker_per_user UNIQUE (user_id);

-- For sponsor registrations: one registration per user
ALTER TABLE iepa_sponsor_registrations
    ADD CONSTRAINT unique_sponsor_per_user UNIQUE (user_id);

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public)
VALUES
    ('iepa-presentations', 'iepa-presentations', false),
    ('iepa-sponsor-assets', 'iepa-sponsor-assets', false),
    ('iepa-documents', 'iepa-documents', false)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for presentations
CREATE POLICY "Users can upload their own presentations"
    ON storage.objects FOR INSERT
    WITH CHECK (bucket_id = 'iepa-presentations' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own presentations"
    ON storage.objects FOR SELECT
    USING (bucket_id = 'iepa-presentations' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for sponsor assets
CREATE POLICY "Users can upload their own sponsor assets"
    ON storage.objects FOR INSERT
    WITH CHECK (bucket_id = 'iepa-sponsor-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own sponsor assets"
    ON storage.objects FOR SELECT
    USING (bucket_id = 'iepa-sponsor-assets' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Storage policies for PDF documents
CREATE POLICY "Users can view their own PDF documents"
    ON storage.objects FOR SELECT
    USING (bucket_id = 'iepa-documents' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Admin can access all files
CREATE POLICY "Service role can manage all presentations"
    ON storage.objects FOR ALL
    USING (bucket_id = 'iepa-presentations' AND auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all sponsor assets"
    ON storage.objects FOR ALL
    USING (bucket_id = 'iepa-sponsor-assets' AND auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all PDF documents"
    ON storage.objects FOR ALL
    USING (bucket_id = 'iepa-documents' AND auth.jwt() ->> 'role' = 'service_role');
