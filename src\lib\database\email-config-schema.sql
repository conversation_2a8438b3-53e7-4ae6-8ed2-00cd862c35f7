-- IEPA Email Configuration Schema
-- Database schema for global email configuration management
-- Created: 2025-01-30

-- Create the email configuration table
CREATE TABLE IF NOT EXISTS iepa_email_config (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_iepa_email_config_key ON iepa_email_config(config_key);
CREATE INDEX IF NOT EXISTS idx_iepa_email_config_active ON iepa_email_config(is_active);

-- <PERSON><PERSON> updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_iepa_email_config_updated_at 
    BEFORE UPDATE ON iepa_email_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default email configuration values
INSERT INTO iepa_email_config (config_key, config_value, description, is_active) VALUES
    ('sender_email', '<EMAIL>', 'Primary sender email address for all outgoing emails', true),
    ('support_email', '<EMAIL>', 'Support email address for customer inquiries and replies', true),
    ('noreply_email', '<EMAIL>', 'No-reply email address for automated notifications', true),
    ('from_name', 'IEPA Annual Meeting 2025', 'Display name shown in the "From" field of emails', true),
    ('reply_to_email', '<EMAIL>', 'Reply-to email address for email responses', true),
    ('test_bcc_email', '<EMAIL>', 'BCC email for testing purposes (development only)', true)
ON CONFLICT (config_key) DO UPDATE SET
    config_value = EXCLUDED.config_value,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Create email configuration change log table for audit trail
CREATE TABLE IF NOT EXISTS iepa_email_config_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL,
    old_value VARCHAR(255),
    new_value VARCHAR(255) NOT NULL,
    changed_by VARCHAR(255), -- User email or system identifier
    change_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for audit log
CREATE INDEX IF NOT EXISTS idx_iepa_email_config_log_key ON iepa_email_config_log(config_key);
CREATE INDEX IF NOT EXISTS idx_iepa_email_config_log_date ON iepa_email_config_log(created_at);

-- Row Level Security (RLS) policies
ALTER TABLE iepa_email_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_email_config_log ENABLE ROW LEVEL SECURITY;

-- Policy: Only authenticated users can read email config
CREATE POLICY "Allow authenticated users to read email config" ON iepa_email_config
    FOR SELECT USING (auth.role() = 'authenticated');

-- Policy: Only admin users can modify email config
CREATE POLICY "Allow admin users to modify email config" ON iepa_email_config
    FOR ALL USING (
        auth.jwt() ->> 'email' IN (
            SELECT email FROM iepa_users WHERE role = 'admin'
        )
    );

-- Policy: Only authenticated users can read config log
CREATE POLICY "Allow authenticated users to read config log" ON iepa_email_config_log
    FOR SELECT USING (auth.role() = 'authenticated');

-- Policy: Only admin users can insert into config log
CREATE POLICY "Allow admin users to insert config log" ON iepa_email_config_log
    FOR INSERT WITH CHECK (
        auth.jwt() ->> 'email' IN (
            SELECT email FROM iepa_users WHERE role = 'admin'
        )
    );

-- Function to log email configuration changes
CREATE OR REPLACE FUNCTION log_email_config_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Log the change when config is updated
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO iepa_email_config_log (
            config_key, 
            old_value, 
            new_value, 
            changed_by,
            change_reason
        ) VALUES (
            NEW.config_key,
            OLD.config_value,
            NEW.config_value,
            COALESCE(auth.jwt() ->> 'email', 'system'),
            'Configuration updated via admin interface'
        );
        RETURN NEW;
    END IF;
    
    -- Log the change when config is inserted
    IF TG_OP = 'INSERT' THEN
        INSERT INTO iepa_email_config_log (
            config_key, 
            old_value, 
            new_value, 
            changed_by,
            change_reason
        ) VALUES (
            NEW.config_key,
            NULL,
            NEW.config_value,
            COALESCE(auth.jwt() ->> 'email', 'system'),
            'Configuration created'
        );
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for logging changes
CREATE TRIGGER email_config_change_log_trigger
    AFTER INSERT OR UPDATE ON iepa_email_config
    FOR EACH ROW EXECUTE FUNCTION log_email_config_change();

-- Grant necessary permissions
GRANT SELECT ON iepa_email_config TO authenticated;
GRANT SELECT ON iepa_email_config_log TO authenticated;
GRANT ALL ON iepa_email_config TO service_role;
GRANT ALL ON iepa_email_config_log TO service_role;

-- Comments for documentation
COMMENT ON TABLE iepa_email_config IS 'Global email configuration settings for IEPA conference registration system';
COMMENT ON COLUMN iepa_email_config.config_key IS 'Unique identifier for the configuration setting';
COMMENT ON COLUMN iepa_email_config.config_value IS 'The email address or configuration value';
COMMENT ON COLUMN iepa_email_config.description IS 'Human-readable description of the configuration purpose';
COMMENT ON COLUMN iepa_email_config.is_active IS 'Whether this configuration is currently active';

-- Create email templates table for managing transactional email templates
CREATE TABLE IF NOT EXISTS iepa_email_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_key VARCHAR(100) NOT NULL UNIQUE,
    template_name VARCHAR(255) NOT NULL,
    description TEXT,
    subject_template TEXT NOT NULL,
    html_template TEXT NOT NULL,
    text_template TEXT,
    variables JSONB DEFAULT '[]'::jsonb, -- Array of variable names used in template
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for email templates
CREATE INDEX IF NOT EXISTS idx_iepa_email_templates_key ON iepa_email_templates(template_key);
CREATE INDEX IF NOT EXISTS idx_iepa_email_templates_active ON iepa_email_templates(is_active);

-- Create email template change log table for audit trail
CREATE TABLE IF NOT EXISTS iepa_email_template_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_id UUID REFERENCES iepa_email_templates(id) ON DELETE CASCADE,
    template_key VARCHAR(100) NOT NULL,
    field_changed VARCHAR(50) NOT NULL, -- 'subject_template', 'html_template', 'text_template', etc.
    old_value TEXT,
    new_value TEXT NOT NULL,
    changed_by VARCHAR(255), -- User email or system identifier
    change_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for template change log
CREATE INDEX IF NOT EXISTS idx_iepa_email_template_log_template_id ON iepa_email_template_log(template_id);
CREATE INDEX IF NOT EXISTS idx_iepa_email_template_log_created_at ON iepa_email_template_log(created_at);

-- Insert default email templates
INSERT INTO iepa_email_templates (template_key, template_name, description, subject_template, html_template, text_template, variables) VALUES
(
    'registration_confirmation',
    'Registration Confirmation',
    'Email sent to confirm successful registration for the IEPA annual meeting',
    'Welcome to IEPA''s 2025 Annual Meeting - Registration Confirmed!',
    '<div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
        <div style="background: #3A6CA5; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Independent Energy Producers Association</h1>
            <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">2025 Annual Meeting Registration Confirmed</p>
        </div>
        <hr style="color: #3A6CA5; border: 2px solid #3A6CA5; margin: 0;" />
        <div style="padding: 30px;">
            <h2 style="color: #3A6CA5; margin-top: 0;">Dear {{name}},</h2>
            <p>Thank you for registering for IEPA''s 2025 Annual Meeting as a <strong>{{registrationType}}</strong>. We are putting together another outstanding program and look forward to your participation.</p>
            {{#confirmationNumber}}
            <div style="background: #f8f9fa; border-left: 4px solid #3A6CA5; padding: 15px; margin: 20px 0;">
                <p style="margin: 0;"><strong>Confirmation Number:</strong> {{confirmationNumber}}</p>
            </div>
            {{/confirmationNumber}}
            {{roleSpecificContent}}
        </div>
        <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                Independent Energy Producers Association<br>
                California''s oldest nonprofit trade association representing independent energy facilities
            </p>
        </div>
    </div>',
    'Dear {{name}},

Thank you for registering for IEPA''s 2025 Annual Meeting as a {{registrationType}}. We are putting together another outstanding program and look forward to your participation.

{{#confirmationNumber}}
Confirmation Number: {{confirmationNumber}}
{{/confirmationNumber}}

Best regards,
The IEPA Annual Meeting Team

Independent Energy Producers Association
California''s oldest nonprofit trade association representing independent energy facilities',
    '["name", "registrationType", "confirmationNumber", "roleSpecificContent"]'::jsonb
),
(
    'payment_confirmation',
    'Payment Confirmation',
    'Email sent to confirm payment for conference registration or golf tournament',
    '{{#isGolf}}⛳ Golf Tournament Registration{{/isGolf}}{{^isGolf}}💳 Payment Confirmation{{/isGolf}} - IEPA 2025',
    '<div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
        <div style="background: {{#isGolf}}#059669{{/isGolf}}{{^isGolf}}#3A6CA5{{/isGolf}}; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">
                {{#isGolf}}⛳ Golf Tournament Registration{{/isGolf}}{{^isGolf}}💳 Payment Confirmation{{/isGolf}}
            </h1>
            <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">IEPA 2025 Annual Meeting</p>
        </div>
        <hr style="color: {{#isGolf}}#059669{{/isGolf}}{{^isGolf}}#3A6CA5{{/isGolf}}; border: 2px solid {{#isGolf}}#059669{{/isGolf}}{{^isGolf}}#3A6CA5{{/isGolf}}; margin: 0;" />
        <div style="padding: 30px;">
            <h2 style="color: {{#isGolf}}#059669{{/isGolf}}{{^isGolf}}#3A6CA5{{/isGolf}}; margin-top: 0;">Dear {{name}},</h2>
            <p>{{paymentMessage}}</p>
            {{paymentDetails}}
        </div>
        <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                Independent Energy Producers Association<br>
                California''s oldest nonprofit trade association representing independent energy facilities
            </p>
        </div>
    </div>',
    'Dear {{name}},

{{paymentMessage}}

{{paymentDetailsText}}

Best regards,
The IEPA Annual Meeting Team

Independent Energy Producers Association
California''s oldest nonprofit trade association representing independent energy facilities',
    '["name", "isGolf", "paymentMessage", "paymentDetails", "paymentDetailsText"]'::jsonb
),
(
    'welcome_email',
    'Welcome Email',
    'Comprehensive welcome email with annual meeting information and attachments',
    'Welcome to IEPA''s 2025 Annual Meeting!',
    '<div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">
        <div style="background: #3A6CA5; padding: 20px; text-align: center;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px;">Independent Energy Producers Association</h1>
            <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">2025 Annual Meeting</p>
        </div>
        <hr style="color: #3A6CA5; border: 2px solid #3A6CA5; margin: 0;" />
        <div style="padding: 30px;">
            <h2 style="color: #3A6CA5; margin-top: 0;">Dear {{name}},</h2>
            <p>Welcome to IEPA''s 2025 Annual Meeting! We''re excited to have you join us for this important event.</p>
            {{welcomeContent}}
        </div>
        <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                Independent Energy Producers Association<br>
                California''s oldest nonprofit trade association representing independent energy facilities
            </p>
        </div>
    </div>',
    'Dear {{name}},

Welcome to IEPA''s 2025 Annual Meeting! We''re excited to have you join us for this important event.

{{welcomeContentText}}

Best regards,
The IEPA Annual Meeting Team

Independent Energy Producers Association
California''s oldest nonprofit trade association representing independent energy facilities',
    '["name", "welcomeContent", "welcomeContentText"]'::jsonb
),
(
    'password_reset',
    'Password Reset',
    'Email sent when user requests password reset',
    'Reset Your Password - IEPA Conference 2025',
    '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Password Reset Request</h2>
        <p>You requested to reset your password for your IEPA Conference account.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{resetUrl}}"
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Reset Password
            </a>
        </div>

        <p>This link will expire in 24 hours for security reasons.</p>
        <p>If you didn''t request this reset, you can safely ignore this email.</p>

        <p>Best regards,<br>
        The IEPA Conference Team</p>
    </div>',
    'Password Reset Request

You requested to reset your password for your IEPA Conference account.

Reset your password by visiting: {{resetUrl}}

This link will expire in 24 hours for security reasons.
If you didn''t request this reset, you can safely ignore this email.

Best regards,
The IEPA Conference Team',
    '["resetUrl"]'::jsonb
)
ON CONFLICT (template_key) DO UPDATE SET
    template_name = EXCLUDED.template_name,
    description = EXCLUDED.description,
    subject_template = EXCLUDED.subject_template,
    html_template = EXCLUDED.html_template,
    text_template = EXCLUDED.text_template,
    variables = EXCLUDED.variables,
    updated_at = NOW();

-- Comments for email templates documentation
COMMENT ON TABLE iepa_email_templates IS 'Email templates for transactional emails in the IEPA conference registration system';
COMMENT ON COLUMN iepa_email_templates.template_key IS 'Unique identifier for the template (e.g., registration_confirmation, payment_confirmation)';
COMMENT ON COLUMN iepa_email_templates.variables IS 'JSON array of variable names that can be used in the template (e.g., ["name", "confirmationNumber"])';
COMMENT ON COLUMN iepa_email_templates.subject_template IS 'Email subject template with variable placeholders';
COMMENT ON COLUMN iepa_email_templates.html_template IS 'HTML email template with variable placeholders using Mustache syntax';
COMMENT ON COLUMN iepa_email_templates.text_template IS 'Plain text email template with variable placeholders';

COMMENT ON TABLE iepa_email_config_log IS 'Audit log for email configuration changes';
COMMENT ON COLUMN iepa_email_config_log.changed_by IS 'Email of user who made the change or system identifier';
COMMENT ON COLUMN iepa_email_config_log.change_reason IS 'Reason for the configuration change';
