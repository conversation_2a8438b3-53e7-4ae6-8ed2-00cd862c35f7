# Production URL Configuration Fix

## Issue
Magic links generated from `reg.iepa.com` were redirecting to `localhost:6969` instead of the production domain.

## Root Cause
The `NEXT_PUBLIC_APP_URL` environment variable was set to `http://localhost:6969` in production, causing server-side magic link generation to use localhost URLs.

## Solution Applied

### 1. Enhanced URL Detection Logic
Updated `src/lib/port-utils.ts` with improved production URL detection:

- **`getDynamicAppUrl()`**: Now detects Vercel production environment and uses appropriate URLs
- **`getProductionAppUrl()`**: New function specifically for production URL detection
- **`getAuthRedirectUrl()`**: Enhanced to always use production URLs in production environment

### 2. Environment Variable Configuration

For production deployment, you need to set the correct environment variables:

#### Option A: Set Production URL Environment Variable
```bash
NEXT_PUBLIC_APP_URL=https://reg.iepa.com
```

#### Option B: Use Vercel Environment Variables (Automatic)
Vercel automatically sets `VERCEL_URL` which the code now detects and uses.

#### Option C: Set Custom Production URL
```bash
NEXT_PUBLIC_PRODUCTION_URL=https://reg.iepa.com
```

## Deployment Steps

### For Vercel Deployment:

1. **Update Environment Variables in Vercel Dashboard:**
   - Go to your Vercel project settings
   - Navigate to Environment Variables
   - Update `NEXT_PUBLIC_APP_URL` to `https://reg.iepa.com`
   - Or add `NEXT_PUBLIC_PRODUCTION_URL=https://reg.iepa.com`

2. **Redeploy the Application:**
   ```bash
   # Trigger a new deployment
   git push origin main
   ```

### For Local Development:
Keep the current localhost setting in `.env.local`:
```bash
NEXT_PUBLIC_APP_URL=http://localhost:6969
```

## Testing the Fix

### 1. Test Magic Link Generation
1. Go to `https://reg.iepa.com/auth/login`
2. Enter email and click "Sign in with magic link"
3. Check the email - the magic link should now point to `https://reg.iepa.com/auth/callback`

### 2. Test Password Reset
1. Go to `https://reg.iepa.com/auth/forgot-password`
2. Enter email and request password reset
3. Check the email - the reset link should point to `https://reg.iepa.com/auth/reset-password`

## Code Changes Made

### Enhanced `getDynamicAppUrl()` Function:
```typescript
export const getDynamicAppUrl = (): string => {
  // In browser environment, use current location
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }

  // In server environment, try environment variable first
  if (process.env.NEXT_PUBLIC_APP_URL) {
    const envUrl = process.env.NEXT_PUBLIC_APP_URL;
    
    // If we're in production (Vercel) and env var is localhost, use Vercel URL
    if (process.env.VERCEL_URL && envUrl.includes('localhost')) {
      return `https://${process.env.VERCEL_URL}`;
    }
    
    // Production domain detection
    if (process.env.NODE_ENV === 'production') {
      if (process.env.VERCEL_URL?.includes('iepa') || process.env.VERCEL_URL?.includes('reg.iepa.com')) {
        return `https://${process.env.VERCEL_URL}`;
      }
      if (process.env.VERCEL_URL === 'reg.iepa.com') {
        return 'https://reg.iepa.com';
      }
    }
    
    return envUrl;
  }

  return 'http://localhost:3000';
};
```

### New `getProductionAppUrl()` Function:
```typescript
export const getProductionAppUrl = (): string => {
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  
  if (process.env.NEXT_PUBLIC_PRODUCTION_URL) {
    return process.env.NEXT_PUBLIC_PRODUCTION_URL;
  }
  
  if (process.env.NODE_ENV === 'production') {
    return 'https://reg.iepa.com';
  }
  
  return getDynamicAppUrl();
};
```

### Enhanced `getAuthRedirectUrl()` Function:
```typescript
export const getAuthRedirectUrl = (path: string): string => {
  // In production, always use the production URL for auth redirects
  if (process.env.NODE_ENV === 'production') {
    return `${getProductionAppUrl()}${path}`;
  }
  
  // In development, use dynamic URL detection
  return `${getDynamicAppUrl()}${path}`;
};
```

## Verification

After deployment, verify the fix by:

1. **Check Console Logs**: Look for URL generation logs in browser dev tools
2. **Test Magic Links**: Ensure they point to production domain
3. **Test Password Reset**: Ensure reset links point to production domain
4. **Test Authentication Flow**: Complete end-to-end auth flow

## Fallback Behavior

The code now has multiple fallback mechanisms:
1. Browser environment: Uses `window.location.origin`
2. Vercel environment: Uses `VERCEL_URL`
3. Custom production URL: Uses `NEXT_PUBLIC_PRODUCTION_URL`
4. Production default: Uses `https://reg.iepa.com`
5. Development: Uses `NEXT_PUBLIC_APP_URL` or localhost

This ensures magic links will always use the correct production domain in production environments.
