import { supabase } from '@/lib/supabase';

export interface AdminSettings {
  allow_profile_edits?: boolean;
  registration_open?: boolean;
  maintenance_mode?: boolean;
  [key: string]: any;
}

/**
 * Get admin settings from the database
 */
export async function getAdminSettings(): Promise<AdminSettings> {
  try {
    // For now, we'll use default values since the settings table might not exist
    // In a real implementation, you would fetch from a settings table
    const defaultSettings: AdminSettings = {
      allow_profile_edits: false, // Temporarily set to false to test the functionality
      registration_open: true,
      maintenance_mode: false,
    };

    // TODO: Implement actual database fetch when settings table is created
    // const { data, error } = await supabase
    //   .from('iepa_admin_settings')
    //   .select('*')
    //   .single();

    // if (error) {
    //   console.error('Error fetching admin settings:', error);
    //   return defaultSettings;
    // }

    return defaultSettings;
  } catch (error) {
    console.error('Error in getAdminSettings:', error);
    return {
      allow_profile_edits: false, // Temporarily set to false to test the functionality
      registration_open: true,
      maintenance_mode: false,
    };
  }
}

/**
 * Get a specific admin setting
 */
export async function getAdminSetting(key: string): Promise<any> {
  try {
    const settings = await getAdminSettings();
    return settings[key];
  } catch (error) {
    console.error(`Error getting admin setting ${key}:`, error);
    return null;
  }
}

/**
 * Check if profile edits are allowed
 */
export async function isProfileEditAllowed(): Promise<boolean> {
  try {
    const allowEdits = await getAdminSetting('allow_profile_edits');
    return allowEdits !== false; // Default to true if not set
  } catch (error) {
    console.error('Error checking profile edit permission:', error);
    return true; // Default to allowing edits if there's an error
  }
}

/**
 * Update admin settings (admin only)
 */
export async function updateAdminSettings(settings: Partial<AdminSettings>): Promise<{ success: boolean; error?: string }> {
  try {
    // Check if user is admin
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        success: false,
        error: 'Authentication required',
      };
    }

    // TODO: Implement actual database update when settings table is created
    // const { error } = await supabase
    //   .from('iepa_admin_settings')
    //   .upsert(settings);

    // if (error) {
    //   return {
    //     success: false,
    //     error: error.message,
    //   };
    // }

    console.log('Admin settings would be updated:', settings);
    
    return {
      success: true,
    };
  } catch (error) {
    console.error('Error updating admin settings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
