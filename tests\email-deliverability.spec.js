/**
 * Email Deliverability Tests for IEPA Conference Registration
 * Tests email delivery, content validation, and link functionality
 */

const { test, expect } = require('@playwright/test');
const {
  waitForEmail,
  validateEmailContent,
  extractLinksFromEmail,
} = require('./utils/email-testing');
const {
  getEmailTestAccount,
  shouldUseInbucket,
  getEmailValidationRules,
  generateTestEmail,
  testEmailConfiguration,
} = require('./config/email-test-config');

// Test data for email testing
const TEST_USER = {
  firstName: 'Email',
  lastName: 'Tester',
  email: generateTestEmail('email-test'),
  organization: 'Test Organization',
  jobTitle: 'QA Engineer',
  phone: '(*************',
};

test.describe('Email Deliverability Tests', () => {
  test.beforeAll(async () => {
    console.log('🧪 Starting Email Deliverability Tests');
    console.log(`📧 Test email: ${TEST_USER.email}`);

    // Test email configuration
    const isConfigured = await testEmailConfiguration();
    if (!isConfigured && !shouldUseInbucket()) {
      console.warn(
        '⚠️ Email testing may not work properly - check configuration'
      );
    }
  });

  test('should verify email service configuration', async ({ page }) => {
    console.log('🧪 Testing email service configuration...');

    // Test the email configuration endpoint
    await page.goto('/api/test-email');

    const response = await page.textContent('body');
    const config = JSON.parse(response);

    expect(config.configured).toBe(true);
    expect(config.config.fromEmail).toBeTruthy();
    expect(config.config.fromName).toContain('IEPA');

    console.log('✅ Email service configuration verified');
  });

  test('should send and receive registration confirmation email', async ({
    page,
  }) => {
    console.log('🧪 Testing registration confirmation email...');

    // Navigate to registration page
    await page.goto('/register/attendee');
    await page.waitForLoadState('networkidle');

    // Fill out registration form (simplified for email testing)
    await page.fill('input[name="firstName"]', TEST_USER.firstName);
    await page.fill('input[name="lastName"]', TEST_USER.lastName);
    await page.fill('input[name="email"]', TEST_USER.email);
    await page.fill('input[name="organization"]', TEST_USER.organization);
    await page.fill('input[name="jobTitle"]', TEST_USER.jobTitle);
    await page.fill('input[name="phone"]', TEST_USER.phone);

    // Submit registration (this should trigger email)
    await page.click('button[type="submit"]');

    // Wait for email to be sent
    console.log('📧 Waiting for registration confirmation email...');

    const emailAccount = getEmailTestAccount('primary');
    const email = await waitForEmail({
      email: emailAccount?.email,
      password: emailAccount?.password,
      subjectMatch: 'Registration Confirmation',
      toMatch: TEST_USER.email,
      useInbucket: shouldUseInbucket(),
      timeout: 45000,
    });

    // Validate email content
    const validationRules = getEmailValidationRules(
      'registration_confirmation'
    );
    const validation = validateEmailContent(email, validationRules);

    expect(validation.isValid).toBe(true);
    if (validation.errors.length > 0) {
      console.error('❌ Email validation errors:', validation.errors);
    }

    // Check for confirmation links
    const links = extractLinksFromEmail(email.html || email.text);
    expect(links.length).toBeGreaterThan(0);

    console.log('✅ Registration confirmation email received and validated');
  });

  test('should send welcome email after successful registration', async ({
    page,
  }) => {
    console.log('🧪 Testing welcome email delivery...');

    // This test assumes a complete registration flow
    // You may need to adapt based on your actual registration process

    const welcomeEmail = generateTestEmail('welcome-test');

    // Trigger welcome email via API (if you have such endpoint)
    const response = await page.request.post('/api/test-email', {
      data: {
        testEmail: welcomeEmail,
        testType: 'welcome',
      },
    });

    expect(response.ok()).toBe(true);

    // Wait for welcome email
    console.log('📧 Waiting for welcome email...');

    const emailAccount = getEmailTestAccount('primary');
    const email = await waitForEmail({
      email: emailAccount?.email,
      password: emailAccount?.password,
      subjectMatch: 'Welcome',
      toMatch: welcomeEmail,
      useInbucket: shouldUseInbucket(),
      timeout: 30000,
    });

    // Validate welcome email content
    const validationRules = getEmailValidationRules('welcome');
    const validation = validateEmailContent(email, validationRules);

    expect(validation.isValid).toBe(true);

    console.log('✅ Welcome email received and validated');
  });

  test('should send payment confirmation email with PDF attachment', async ({
    page,
  }) => {
    console.log('🧪 Testing payment confirmation email...');

    const paymentEmail = generateTestEmail('payment-test');

    // Trigger payment confirmation email via API
    const response = await page.request.post('/api/test-email', {
      data: {
        testEmail: paymentEmail,
        testType: 'payment',
      },
    });

    expect(response.ok()).toBe(true);

    // Wait for payment confirmation email
    console.log('📧 Waiting for payment confirmation email...');

    const emailAccount = getEmailTestAccount('primary');
    const email = await waitForEmail({
      email: emailAccount?.email,
      password: emailAccount?.password,
      subjectMatch: 'Payment Confirmation',
      toMatch: paymentEmail,
      useInbucket: shouldUseInbucket(),
      timeout: 30000,
    });

    // Validate payment email content
    const validationRules = getEmailValidationRules('payment_confirmation');
    const validation = validateEmailContent(email, validationRules);

    expect(validation.isValid).toBe(true);

    // Check for PDF attachment (receipt)
    if (email.attachments && email.attachments.length > 0) {
      const pdfAttachment = email.attachments.find(
        att =>
          att.contentType === 'application/pdf' ||
          att.filename?.endsWith('.pdf')
      );
      expect(pdfAttachment).toBeTruthy();
      console.log('✅ PDF receipt attachment found');
    } else {
      console.warn('⚠️ No attachments found in payment confirmation email');
    }

    console.log('✅ Payment confirmation email received and validated');
  });

  test('should validate email links are functional', async ({ page }) => {
    console.log('🧪 Testing email link functionality...');

    const linkTestEmail = generateTestEmail('link-test');

    // Send a test email with links
    const response = await page.request.post('/api/test-email', {
      data: {
        testEmail: linkTestEmail,
        testType: 'registration',
      },
    });

    expect(response.ok()).toBe(true);

    // Wait for email
    const emailAccount = getEmailTestAccount('primary');
    const email = await waitForEmail({
      email: emailAccount?.email,
      password: emailAccount?.password,
      subjectMatch: 'Registration',
      toMatch: linkTestEmail,
      useInbucket: shouldUseInbucket(),
      timeout: 30000,
    });

    // Extract and test links
    const links = extractLinksFromEmail(email.html || email.text);
    expect(links.length).toBeGreaterThan(0);

    // Test each link
    for (const link of links.slice(0, 3)) {
      // Test first 3 links to avoid timeout
      console.log(`🔗 Testing link: ${link}`);

      try {
        const linkResponse = await page.request.get(link);
        expect(linkResponse.status()).toBeLessThan(500); // Should not be server error
        console.log(`✅ Link accessible: ${link} (${linkResponse.status()})`);
      } catch (error) {
        console.warn(`⚠️ Link test failed: ${link} - ${error.message}`);
      }
    }

    console.log('✅ Email link functionality tested');
  });

  test('should verify email logging in database', async ({ page }) => {
    console.log('🧪 Testing email logging functionality...');

    const logTestEmail = generateTestEmail('log-test');

    // Send test email
    const response = await page.request.post('/api/test-email', {
      data: {
        testEmail: logTestEmail,
        testType: 'basic',
      },
    });

    expect(response.ok()).toBe(true);

    // Check if email was logged (you may need to create an API endpoint for this)
    // This is a placeholder - implement based on your logging system
    console.log('📊 Email logging verification would go here');
    console.log(
      'ℹ️ Consider implementing /api/admin/email-logs endpoint for testing'
    );

    console.log('✅ Email logging test completed');
  });

  test('should handle email delivery failures gracefully', async ({ page }) => {
    console.log('🧪 Testing email delivery failure handling...');

    // Test with invalid email address
    const response = await page.request.post('/api/test-email', {
      data: {
        testEmail: 'invalid-email-address',
        testType: 'basic',
      },
    });

    const result = await response.json();

    // Should handle invalid email gracefully
    expect(result.success).toBe(false);
    expect(result.error).toBeTruthy();

    console.log('✅ Email delivery failure handling verified');
  });

  test.afterAll(async () => {
    console.log('🧹 Cleaning up email tests...');

    // Clean up test emails if using real email account
    const emailAccount = getEmailTestAccount('primary');
    if (emailAccount && !shouldUseInbucket()) {
      const { cleanupTestEmails } = require('./config/email-test-config');
      await cleanupTestEmails(emailAccount, [
        'Registration Confirmation',
        'Welcome',
        'Payment Confirmation',
      ]);
    }

    console.log('✅ Email deliverability tests completed');
  });
});
