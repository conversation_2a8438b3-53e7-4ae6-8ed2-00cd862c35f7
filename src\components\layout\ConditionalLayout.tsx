'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { Navigation } from '@/components/layout/Navigation';
import { ParallaxFooter } from '@/components/layout/ParallaxFooter';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

/**
 * ConditionalLayout component that renders different layouts based on the current route
 *
 * - Admin routes (/admin/*): Clean layout without main navigation or footer
 * - Print routes (ending with /print): Clean layout without main navigation or footer
 * - All other routes: Full layout with navigation (including breadcrumbs) and footer
 */
export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();

  // Check if current route is an admin route
  const isAdminRoute = pathname?.startsWith('/admin') || false;

  // Check if current route is a print-friendly page
  const isPrintRoute = pathname?.endsWith('/print') || false;

  // For admin routes and print routes, render only the children (clean layout)
  if (isAdminRoute || isPrintRoute) {
    return <main>{children}</main>;
  }

  // For all other routes, render the full layout with navigation and footer
  return (
    <>
      <Navigation />
      <main>{children}</main>
      <ParallaxFooter />
    </>
  );
}
