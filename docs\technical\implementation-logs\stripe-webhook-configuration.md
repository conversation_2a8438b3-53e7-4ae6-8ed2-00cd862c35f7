# Stripe Webhook Configuration

**Generated**: 2025-06-13T20:21:28.157Z
**Script**: configure-stripe-webhooks.ts

## Configured Webhooks

### PRODUCTION Environment

- **Webhook ID**: `we_1RZe6OIOwAzWO5br0RIHUtoi`
- **URL**: `https://iepa.com/api/stripe/webhook`
- **Secret**: `whsec_QcARBBPRyZKu4fQRgLp5Y139vm5ml2Vl`
- **Events**:
  - `checkout.session.completed`
  - `payment_intent.succeeded`
  - `payment_intent.payment_failed`

## Environment Variables

### Development (.env.local)

```env
STRIPE_WEBHOOK_SECRET=NOT_CONFIGURED
```

### Production

```env
STRIPE_WEBHOOK_SECRET=whsec_QcARBBPRyZKu4fQRgLp5Y139vm5ml2Vl
```

## Testing Webhooks

### Development Testing

```bash
# Test webhook delivery
curl -X POST http://localhost:3000/api/stripe/webhook \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: test" \
  -d '{"type": "checkout.session.completed"}'
```

### Using Stripe CLI

```bash
# Forward events to local development
stripe listen --forward-to localhost:3000/api/stripe/webhook

# Test specific events
stripe trigger checkout.session.completed
stripe trigger payment_intent.succeeded
stripe trigger payment_intent.payment_failed
```

## Webhook Handler

The webhook handler is implemented at:

- **File**: `src/app/api/stripe/webhook/route.ts`
- **Endpoint**: `/api/stripe/webhook`

## Security Notes

1. **Webhook Secrets**: Keep webhook secrets secure and never commit to version control
2. **Signature Verification**: All webhooks are verified using Stripe's signature verification
3. **Environment Separation**: Development and production use separate webhook endpoints
4. **HTTPS Required**: Production webhooks must use HTTPS endpoints

## Troubleshooting

1. **Webhook Not Receiving Events**: Check Stripe dashboard webhook logs
2. **Signature Verification Failed**: Ensure webhook secret matches environment variable
3. **404 Errors**: Verify webhook URL is accessible and endpoint exists
4. **Database Updates Not Working**: Check webhook handler logs and database permissions
