// Stripe Live Webhook Handler
// POST /api/stripe/webhook/live

import { NextRequest, NextResponse } from 'next/server';
import { stripe, STRIPE_WEBHOOK_EVENTS } from '@/lib/stripe';
import { createSupabaseAdmin } from '@/lib/supabase';
import { emailService } from '@/services/email';
import <PERSON><PERSON> from 'stripe';

// Live environment webhook secret (to be set when going live)
const LIVE_WEBHOOK_SECRET = process.env.STRIPE_LIVE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      console.error('[LIVE-WEBHOOK] Missing Stripe signature');
      return NextResponse.json(
        { error: 'Missing Stripe signature' },
        { status: 400 }
      );
    }

    if (!LIVE_WEBHOOK_SECRET) {
      console.error('[LIVE-WEBHOOK] Missing webhook secret');
      return NextResponse.json(
        { error: 'Live webhook secret not configured' },
        { status: 500 }
      );
    }

    // Verify webhook signature
    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        LIVE_WEBHOOK_SECRET
      );
    } catch (err) {
      console.error(
        '[LIVE-WEBHOOK] Webhook signature verification failed:',
        err
      );
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    console.log(`[LIVE-WEBHOOK] Received webhook event: ${event.type}`);

    // Handle different event types
    switch (event.type) {
      case STRIPE_WEBHOOK_EVENTS.CHECKOUT_SESSION_COMPLETED:
        await handleCheckoutSessionCompleted(
          event.data.object as Stripe.Checkout.Session
        );
        break;

      case STRIPE_WEBHOOK_EVENTS.PAYMENT_INTENT_SUCCEEDED:
        await handlePaymentIntentSucceeded(
          event.data.object as Stripe.PaymentIntent
        );
        break;

      case STRIPE_WEBHOOK_EVENTS.PAYMENT_INTENT_PAYMENT_FAILED:
        await handlePaymentIntentFailed(
          event.data.object as Stripe.PaymentIntent
        );
        break;

      default:
        console.log(`[LIVE-WEBHOOK] Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true, environment: 'live' });
  } catch (error) {
    console.error('[LIVE-WEBHOOK] Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

// Live environment handlers (same logic as test but with live logging)
async function handleCheckoutSessionCompleted(
  session: Stripe.Checkout.Session
) {
  try {
    console.log(
      '[LIVE-WEBHOOK] Processing checkout session completed:',
      session.id
    );

    const { registrationId, registrationType, userId } = session.metadata || {};

    if (!registrationId || !registrationType) {
      console.error(
        '[LIVE-WEBHOOK] Missing metadata in checkout session:',
        session.id
      );
      return;
    }

    console.log('[LIVE-WEBHOOK] Processing registration:', {
      registrationId,
      registrationType,
      userId,
      amount: (session.amount_total || 0) / 100,
      customerEmail: session.customer_details?.email || session.customer_email,
    });

    // Map registration type to payment table categories
    const paymentRegistrationType =
      registrationType === 'sponsor' ? 'sponsor' : 'attendee';

    // Create payment record
    const paymentData = {
      user_id: userId || null,
      registration_id: registrationId,
      registration_type: paymentRegistrationType as 'attendee' | 'sponsor',
      stripe_payment_intent_id: session.payment_intent as string,
      amount: (session.amount_total || 0) / 100,
      currency: session.currency || 'usd',
      status: 'completed',
    };

    const supabaseAdmin = createSupabaseAdmin();
    const { error: paymentError } = await supabaseAdmin
      .from('iepa_payments')
      .insert([paymentData]);

    if (paymentError) {
      console.error(
        '[LIVE-WEBHOOK] Error creating payment record:',
        paymentError
      );
      return;
    }

    // Update registration status
    const tableName = `iepa_${paymentRegistrationType}_registrations`;
    const { error: updateError } = await supabaseAdmin
      .from(tableName)
      .update({
        payment_status: 'completed',
        payment_id: session.payment_intent,
        updated_at: new Date().toISOString(),
      })
      .eq('id', registrationId);

    if (updateError) {
      console.error(
        '[LIVE-WEBHOOK] Error updating registration status:',
        updateError
      );
      return;
    }

    console.log(
      `[LIVE-WEBHOOK] Successfully processed payment for ${registrationType} registration:`,
      registrationId
    );

    // 📧 SEND PAYMENT CONFIRMATION EMAIL
    await sendPaymentConfirmationEmail(
      session,
      registrationType,
      registrationId,
      userId
    );

    // 📧 SEND WELCOME EMAIL WITH CONFERENCE DETAILS (includes PDF invoice)
    await sendWelcomeEmail(
      session,
      paymentRegistrationType,
      registrationId,
      userId
    );
  } catch (error) {
    console.error(
      '[LIVE-WEBHOOK] Error handling checkout session completed:',
      error
    );
  }
}

async function handlePaymentIntentSucceeded(
  paymentIntent: Stripe.PaymentIntent
) {
  try {
    console.log(
      '[LIVE-WEBHOOK] Processing payment intent succeeded:',
      paymentIntent.id
    );

    const supabaseAdmin = createSupabaseAdmin();
    const { error } = await supabaseAdmin
      .from('iepa_payments')
      .update({
        status: 'completed',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_payment_intent_id', paymentIntent.id);

    if (error) {
      console.error('[LIVE-WEBHOOK] Error updating payment status:', error);
    } else {
      console.log('[LIVE-WEBHOOK] Payment status updated successfully');
    }
  } catch (error) {
    console.error(
      '[LIVE-WEBHOOK] Error handling payment intent succeeded:',
      error
    );
  }
}

// Send payment confirmation email after successful payment
async function sendPaymentConfirmationEmail(
  session: Stripe.Checkout.Session,
  registrationType: string,
  registrationId: string,
  userId?: string
) {
  try {
    console.log(
      '[LIVE-WEBHOOK] [EMAIL-DEBUG] Sending payment confirmation email',
      {
        sessionId: session.id,
        registrationType,
        registrationId,
        customerEmail: session.customer_details?.email,
      }
    );

    const customerEmail =
      session.customer_details?.email || session.customer_email;
    const customerName =
      session.customer_details?.name ||
      session.metadata?.customerName ||
      'Conference Attendee';

    if (!customerEmail) {
      console.error(
        '[LIVE-WEBHOOK] [EMAIL-ERROR] No customer email found in session:',
        session.id
      );
      return;
    }

    // Create payment details object with metadata
    const paymentDetailsWithMetadata = {
      amount: (session.amount_total || 0) / 100,
      paymentId: session.payment_intent as string,
      registrationType: `${registrationType.charAt(0).toUpperCase() + registrationType.slice(1)} Registration`,
      userId: userId,
      registrationId: registrationId,
    };

    // Send email
    const success = await emailService.sendPaymentConfirmation(
      customerEmail,
      customerName,
      paymentDetailsWithMetadata
    );

    if (success) {
      console.log(
        '[LIVE-WEBHOOK] [EMAIL-DEBUG] Payment confirmation email sent successfully',
        {
          to: customerEmail,
          registrationId,
        }
      );
    } else {
      console.error(
        '[LIVE-WEBHOOK] [EMAIL-ERROR] Failed to send payment confirmation email',
        {
          to: customerEmail,
          registrationId,
        }
      );
    }
  } catch (error) {
    console.error(
      '[LIVE-WEBHOOK] [EMAIL-ERROR] Error sending payment confirmation email:',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        sessionId: session.id,
        registrationId,
      }
    );
  }
}

// Send welcome email with conference details
async function sendWelcomeEmail(
  session: Stripe.Checkout.Session,
  registrationType: string,
  registrationId: string,
  userId?: string
) {
  try {
    console.log('[LIVE-WEBHOOK] [EMAIL-DEBUG] Sending welcome email', {
      sessionId: session.id,
      registrationType,
      registrationId,
      customerEmail: session.customer_details?.email,
    });

    const customerEmail =
      session.customer_details?.email || session.customer_email;
    const customerName =
      session.customer_details?.name ||
      session.metadata?.customerName ||
      'Conference Attendee';

    if (!customerEmail) {
      console.error(
        '[LIVE-WEBHOOK] [EMAIL-ERROR] No customer email found for welcome email:',
        session.id
      );
      return;
    }

    // Get registration details for welcome email
    const supabaseAdmin = createSupabaseAdmin();
    const tableName = `iepa_${registrationType}_registrations`;
    const { data: registration } = await supabaseAdmin
      .from(tableName)
      .select('*')
      .eq('id', registrationId)
      .single();

    // Send welcome email with conference details
    const success = await emailService.sendWelcomeEmail(
      customerEmail,
      customerName,
      {
        type: registrationType as 'attendee' | 'speaker' | 'sponsor',
        confirmationNumber: registrationId,
        userId: userId,
        hasLodging: registration?.night_one || registration?.night_two || false,
        hasGolf: registration?.attending_golf || false,
      }
    );

    if (success) {
      console.log(
        '[LIVE-WEBHOOK] [EMAIL-DEBUG] Welcome email sent successfully',
        {
          to: customerEmail,
          registrationId,
        }
      );
    } else {
      console.error(
        '[LIVE-WEBHOOK] [EMAIL-ERROR] Failed to send welcome email',
        {
          to: customerEmail,
          registrationId,
        }
      );
    }
  } catch (error) {
    console.error('[LIVE-WEBHOOK] [EMAIL-ERROR] Error sending welcome email:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      sessionId: session.id,
      registrationId,
    });
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log(
      '[LIVE-WEBHOOK] Processing payment intent failed:',
      paymentIntent.id
    );

    const supabaseAdmin = createSupabaseAdmin();
    const { error } = await supabaseAdmin
      .from('iepa_payments')
      .update({
        status: 'failed',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_payment_intent_id', paymentIntent.id);

    if (error) {
      console.error('[LIVE-WEBHOOK] Error updating payment status:', error);
    } else {
      console.log('[LIVE-WEBHOOK] Payment status updated to failed');
    }
  } catch (error) {
    console.error(
      '[LIVE-WEBHOOK] Error handling payment intent failed:',
      error
    );
  }
}
