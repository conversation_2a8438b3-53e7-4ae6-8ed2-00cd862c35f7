'use client';

import React from 'react';
import { Card, CardBody } from '@/components/ui';
import { cn } from '@/lib/utils';
import { pricingUtils } from '@/lib/pricing-config';
import type { RegistrationPrice, AdditionalOption } from '@/lib/pricing-config';

interface RegistrationRatesTableProps {
  registrationPricing: RegistrationPrice[];
  additionalOptions: AdditionalOption[];
  className?: string;
}

interface RateCardProps {
  name: string;
  basePrice: number;
  groupDiscountPrice?: number;
  isEven: boolean;
}

function RateCard({
  name,
  basePrice,
  groupDiscountPrice, // eslint-disable-line @typescript-eslint/no-unused-vars
  isEven,
}: RateCardProps) {
  return (
    <Card
      className={cn(
        'border border-gray-200',
        isEven ? 'bg-gray-50' : 'bg-white'
      )}
    >
      <CardBody className="p-4">
        <div className="space-y-3">
          {/* Registration Type */}
          <div>
            <h4 className="font-semibold text-gray-900 iepa-body">{name}</h4>
          </div>

          {/* Regular Rate */}
          <div className="flex justify-between items-center">
            <span className="text-gray-600 iepa-body-small">Regular Rate:</span>
            <span className="font-semibold iepa-body text-right">
              {pricingUtils.formatPrice(basePrice)}
            </span>
          </div>

          {/* 3rd Person Discount - Hidden per user request */}
          {/* <div className="flex justify-between items-start">
            <span className="text-gray-600 iepa-body-small">
              3rd Person Discount:
            </span>
            <div className="text-right">
              {groupDiscountPrice ? (
                <div className="space-y-1">
                  <div className="font-semibold iepa-body">
                    {pricingUtils.formatPrice(groupDiscountPrice)}
                  </div>
                  <div className="text-green-600 text-xs">
                    (${basePrice - groupDiscountPrice} savings)
                  </div>
                </div>
              ) : (
                <span className="iepa-body">—</span>
              )}
            </div>
          </div> */}
        </div>
      </CardBody>
    </Card>
  );
}

export function RegistrationRatesTable({
  registrationPricing,
  additionalOptions,
  className,
}: RegistrationRatesTableProps) {
  // Combine all rates for consistent indexing
  const allRates = [
    ...registrationPricing.map(rate => ({
      name: rate.name,
      basePrice: rate.basePrice,
      groupDiscountPrice: rate.groupDiscountPrice,
    })),
    ...additionalOptions.map(option => ({
      name: option.name,
      basePrice: option.price,
      groupDiscountPrice: undefined,
    })),
  ];

  return (
    <div className={cn('w-full', className)}>
      {/* Desktop Table View */}
      <div className="hidden md:block overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr
              className="border-b-2"
              style={{ borderColor: 'var(--iepa-primary-blue)' }}
            >
              <th className="text-left py-3 px-4 iepa-body font-semibold">
                Registration Type
              </th>
              <th className="text-right py-3 px-4 iepa-body font-semibold">
                Regular Rate
              </th>
              {/* 3rd Person Discount column hidden per user request */}
              {/* <th className="text-right py-3 px-4 iepa-body font-semibold">
                3rd Person Discount
              </th> */}
            </tr>
          </thead>
          <tbody>
            {registrationPricing.map((rate, index) => (
              <tr key={rate.id} className={index % 2 === 0 ? 'bg-gray-50' : ''}>
                <td className="py-3 px-4 iepa-body">{rate.name}</td>
                <td className="py-3 px-4 iepa-body text-right font-semibold">
                  {pricingUtils.formatPrice(rate.basePrice)}
                </td>
                {/* 3rd Person Discount cell hidden per user request */}
                {/* <td className="py-3 px-4 iepa-body text-right">
                  {rate.groupDiscountPrice ? (
                    <span>
                      {pricingUtils.formatPrice(rate.groupDiscountPrice)}
                      <span className="text-green-600 ml-2">
                        ($
                        {pricingUtils.calculateGroupSavings(rate.id)} savings)
                      </span>
                    </span>
                  ) : (
                    '—'
                  )}
                </td> */}
              </tr>
            ))}
            {/* Additional Options */}
            {additionalOptions.map((option, index) => (
              <tr
                key={option.id}
                className={
                  registrationPricing.length % 2 === index % 2
                    ? 'bg-gray-50'
                    : ''
                }
              >
                <td className="py-3 px-4 iepa-body">{option.name}</td>
                <td className="py-3 px-4 iepa-body text-right font-semibold">
                  {pricingUtils.formatPrice(option.price)}
                </td>
                {/* 3rd Person Discount cell hidden per user request */}
                {/* <td className="py-3 px-4 iepa-body text-right">—</td> */}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-3">
        {allRates.map((rate, index) => (
          <RateCard
            key={`${rate.name}-${index}`}
            name={rate.name}
            basePrice={rate.basePrice}
            groupDiscountPrice={rate.groupDiscountPrice}
            isEven={index % 2 === 0}
          />
        ))}
      </div>
    </div>
  );
}
