'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import { showError } from '@/utils/notifications';
import { FiDownload, FiFile, FiFileText, FiImage } from 'react-icons/fi';

interface ConferenceDocument {
  id: string;
  name: string;
  description: string;
  file_url: string;
  file_size: number;
  file_type: string;
  display_order: number;
  is_public: boolean;
  is_active: boolean;
  created_at: string;
}

const getFileIcon = (fileType: string) => {
  if (fileType?.includes('pdf')) return FiFileText;
  if (fileType?.includes('image')) return FiImage;
  return FiFile;
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export function ConferenceResources() {
  const [documents, setDocuments] = useState<ConferenceDocument[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    try {
      const { data, error } = await supabase
        .from('iepa_conference_documents')
        .select('*')
        .eq('is_active', true)
        .eq('is_public', true)
        .order('display_order', { ascending: true })
        .order('created_at', { ascending: false });

      if (error) throw error;
      setDocuments(data || []);
    } catch (error) {
      console.error('Error loading conference documents:', error);
      showError('Error', 'Failed to load conference documents');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (doc: ConferenceDocument) => {
    try {
      // Create a temporary link to download the file
      const link = document.createElement('a');
      link.href = doc.file_url;
      link.download = doc.name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading document:', error);
      showError('Error', 'Failed to download document');
    }
  };

  if (loading) {
    return (
      <section id="resources" className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="iepa-heading-2 mb-4">Conference Resources</h2>
            <p className="iepa-body text-[var(--iepa-gray-600)]">
              Download important conference documents and materials
            </p>
          </div>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--iepa-primary-blue)] mx-auto mb-4"></div>
            <p className="text-[var(--iepa-gray-600)]">Loading resources...</p>
          </div>
        </div>
      </section>
    );
  }

  if (documents.length === 0) {
    return (
      <section id="resources" className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="iepa-heading-2 mb-4">Conference Resources</h2>
            <p className="iepa-body text-[var(--iepa-gray-600)]">
              Download important conference documents and materials
            </p>
          </div>
          <div className="text-center py-8 text-[var(--iepa-gray-500)]">
            <FiFile className="mx-auto h-12 w-12 mb-4" />
            <p>Conference resources will be available soon.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="resources" className="iepa-section">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="iepa-heading-2 mb-4">Conference Resources</h2>
          <p className="iepa-body text-[var(--iepa-gray-600)] mb-6">
            Download important conference documents and materials
          </p>
        </div>

        <div className="grid grid-cols-1 gap-2">
          {documents.map((document) => {
            const FileIcon = getFileIcon(document.file_type);

            return (
              <Card
                key={document.id}
                className="hover:shadow-sm transition-all duration-200 hover:scale-[1.002] border-[var(--iepa-gray-200)] hover:border-[var(--iepa-primary-blue)]/30 bg-white"
              >
                <CardContent className="p-3">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-lg flex items-center justify-center bg-gradient-to-br from-[var(--iepa-primary-blue)] to-[var(--iepa-accent-teal)]">
                        <FileIcon className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-sm text-[var(--iepa-primary-blue)]">
                        {document.name}
                      </h3>
                    </div>
                    <div className="flex-shrink-0">
                      <Button
                        onClick={() => handleDownload(document)}
                        size="sm"
                        className="bg-[var(--iepa-secondary-green)] hover:bg-[var(--iepa-secondary-green-dark)] text-white text-xs h-6 px-2"
                      >
                        <FiDownload className="w-3 h-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}
