'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SponsorCard } from '@/components/ui/sponsor-card-radio';
import { SPONSORSHIP_PACKAGES } from '@/lib/pricing-config';
import Link from 'next/link';
import { FaArrowRight } from 'react-icons/fa';

interface SponsorShowcaseProps {
  title?: string;
  description?: string;
  showCTA?: boolean;
  ctaText?: string;
  ctaHref?: string;
  className?: string;
  layout?: 'grid' | 'carousel' | 'list';
  maxItems?: number;
}

export function SponsorShowcase({
  title = 'Sponsorship Opportunities',
  description = 'Partner with IEPA to showcase your organization and connect with environmental professionals.',
  showCTA = true,
  ctaText = 'Become a Sponsor',
  ctaHref = '/register/sponsor',
  className = '',
  layout = 'grid',
  maxItems,
}: SponsorShowcaseProps) {
  const packages = maxItems 
    ? SPONSORSHIP_PACKAGES.slice(0, maxItems)
    : SPONSORSHIP_PACKAGES;

  const sortedPackages = [...packages].sort((a, b) => b.price - a.price);

  return (
    <section className={`iepa-section ${className}`}>
      <div className="iepa-container">
        {/* Header */}
        <div className="text-center mb-8 lg:mb-12">
          <h2 className="iepa-heading-1 mb-4">{title}</h2>
          <p className="iepa-body text-[var(--iepa-gray-600)] max-w-3xl mx-auto">
            {description}
          </p>
        </div>

        {/* Sponsorship Packages */}
        <div className={`
          ${layout === 'grid'
            ? 'space-y-6'
            : layout === 'list'
            ? 'space-y-6'
            : 'space-y-6'
          }
        `}>
          {sortedPackages.map((pkg) => (
            <SponsorCard
              key={pkg.id}
              package={pkg}
              showAsCard={true}
              className=""
            />
          ))}
        </div>

        {/* Call to Action */}
        {showCTA && (
          <div className="text-center mt-8 lg:mt-12">
            <Button
              as={Link}
              href={ctaHref}
              size="lg"
              className="iepa-btn-primary"
            >
              {ctaText}
              <FaArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <p className="text-sm text-[var(--iepa-gray-600)] mt-3">
              Questions about sponsorship? <Link href="/contact" className="text-[var(--iepa-primary-blue)] hover:underline">Contact us</Link>
            </p>
          </div>
        )}
      </div>
    </section>
  );
}

interface SponsorBenefitsComparisonProps {
  className?: string;
}

export function SponsorBenefitsComparison({ className = '' }: SponsorBenefitsComparisonProps) {
  const sortedPackages = [...SPONSORSHIP_PACKAGES].sort((a, b) => b.price - a.price);

  return (
    <section className={`iepa-section ${className}`}>
      <div className="iepa-container">
        <div className="text-center mb-8">
          <h2 className="iepa-heading-1 mb-4">Compare Sponsorship Benefits</h2>
          <p className="iepa-body text-[var(--iepa-gray-600)]">
            Choose the sponsorship level that best aligns with your marketing goals and budget.
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-white rounded-lg shadow-sm">
            <thead>
              <tr className="bg-[var(--iepa-gray-50)]">
                <th className="text-left p-4 font-semibold text-[var(--iepa-gray-700)]">
                  Benefits
                </th>
                {sortedPackages.map((pkg) => (
                  <th key={pkg.id} className="text-center p-4 min-w-[150px]">
                    <div className="space-y-2">
                      <Badge className={`
                        ${pkg.level === 'diamond' ? 'bg-purple-100 text-purple-800' :
                          pkg.level === 'platinum' ? 'bg-gray-100 text-gray-800' :
                          pkg.level === 'gold' ? 'bg-yellow-100 text-yellow-800' :
                          pkg.level === 'silver' ? 'bg-slate-100 text-slate-800' :
                          'bg-orange-100 text-orange-800'
                        }
                      `}>
                        {pkg.level.charAt(0).toUpperCase() + pkg.level.slice(1)}
                      </Badge>
                      <div className="font-bold text-[var(--iepa-primary-blue)]">
                        ${pkg.price.toLocaleString()}
                      </div>
                      <div className="text-xs text-[var(--iepa-gray-600)]">
                        {pkg.includedRegistrations} registration{pkg.includedRegistrations > 1 ? 's' : ''}
                      </div>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {/* Registration Benefits */}
              <tr className="border-t">
                <td className="p-4 font-medium text-[var(--iepa-gray-700)] bg-[var(--iepa-gray-50)]" colSpan={sortedPackages.length + 1}>
                  Registration Benefits
                </td>
              </tr>
              {sortedPackages[0].benefits.map((benefit, index) => (
                <tr key={`benefit-${index}`} className="border-t">
                  <td className="p-4 text-[var(--iepa-gray-700)]">{benefit}</td>
                  {sortedPackages.map((pkg) => (
                    <td key={pkg.id} className="p-4 text-center">
                      {pkg.benefits.includes(benefit) ? (
                        <span className="text-[var(--iepa-secondary-green)] text-lg">✓</span>
                      ) : (
                        <span className="text-[var(--iepa-gray-400)]">—</span>
                      )}
                    </td>
                  ))}
                </tr>
              ))}

              {/* Marketing Benefits */}
              <tr className="border-t">
                <td className="p-4 font-medium text-[var(--iepa-gray-700)] bg-[var(--iepa-gray-50)]" colSpan={sortedPackages.length + 1}>
                  Marketing Benefits
                </td>
              </tr>
              {sortedPackages[0].marketingBenefits.map((benefit, index) => (
                <tr key={`marketing-${index}`} className="border-t">
                  <td className="p-4 text-[var(--iepa-gray-700)]">{benefit}</td>
                  {sortedPackages.map((pkg) => (
                    <td key={pkg.id} className="p-4 text-center">
                      {pkg.marketingBenefits.includes(benefit) ? (
                        <span className="text-[var(--iepa-accent-teal)] text-lg">✓</span>
                      ) : (
                        <span className="text-[var(--iepa-gray-400)]">—</span>
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="text-center mt-8">
          <Button
            as={Link}
            href="/register/sponsor"
            size="lg"
            className="iepa-btn-primary"
          >
            Register as Sponsor
            <FaArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
}

interface SponsorHighlightProps {
  packageId: string;
  featured?: boolean;
  className?: string;
}

export function SponsorHighlight({ 
  packageId, 
  featured = false, 
  className = '' 
}: SponsorHighlightProps) {
  const pkg = SPONSORSHIP_PACKAGES.find(p => p.id === packageId);
  
  if (!pkg) return null;

  return (
    <Card className={`
      ${featured ? 'border-2 border-[var(--iepa-primary-blue)] shadow-lg' : ''}
      ${className}
    `}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl">{pkg.name}</CardTitle>
          {featured && (
            <Badge className="bg-[var(--iepa-primary-blue)] text-white">
              Most Popular
            </Badge>
          )}
        </div>
        <div className="text-3xl font-bold text-[var(--iepa-primary-blue)]">
          ${pkg.price.toLocaleString()}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-[var(--iepa-gray-600)] mb-4">
          Includes {pkg.includedRegistrations} registration{pkg.includedRegistrations > 1 ? 's' : ''}
        </p>
        <Button
          as={Link}
          href="/register/sponsor"
          className="w-full iepa-btn-primary"
        >
          Choose {pkg.level.charAt(0).toUpperCase() + pkg.level.slice(1)}
        </Button>
      </CardContent>
    </Card>
  );
}
