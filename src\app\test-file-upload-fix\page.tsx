'use client';

import { useState } from 'react';
import { SimpleFileUpload } from '@/components/ui/SimpleFileUpload';

export default function TestFileUploadPage() {
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);

  const handleFileUpload = (url: string | null) => {
    console.log('File uploaded:', url);
    setUploadedUrl(url);
  };

  return (
    <div className="container mx-auto p-8 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">File Upload Test</h1>

      <div className="space-y-6">
        <div>
          <h2 className="text-lg font-semibold mb-4">Test Image Upload</h2>
          <SimpleFileUpload
            label="Test Image"
            description="Upload a test image (JPG, PNG, WebP - max 5MB)"
            bucket="iepa-presentations"
            folder="test-uploads"
            maxSize={5242880} // 5MB
            allowedTypes={['image/jpeg', 'image/png', 'image/webp']}
            accept=".jpg,.jpeg,.png,.webp"
            onFileUpload={handleFileUpload}
            placeholder="Upload your test image"
          />
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-4">Test Document Upload</h2>
          <SimpleFileUpload
            label="Test Document"
            description="Upload a test document (PDF, DOC, DOCX - max 10MB)"
            bucket="iepa-presentations"
            folder="test-documents"
            maxSize={10485760} // 10MB
            allowedTypes={[
              'application/pdf',
              'application/msword',
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            ]}
            accept=".pdf,.doc,.docx"
            onFileUpload={url => console.log('Document uploaded:', url)}
            placeholder="Upload your test document"
          />
        </div>

        {uploadedUrl && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              Upload Successful!
            </h3>
            <p className="text-green-700">File URL: {uploadedUrl}</p>
          </div>
        )}
      </div>
    </div>
  );
}
