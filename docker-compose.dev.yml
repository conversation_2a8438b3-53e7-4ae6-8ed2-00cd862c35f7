# IEPA Conference Registration - Development Docker Compose
# This file provides an alternative way to manage the development environment

version: '3.8'

services:
  # This is a placeholder service that ensures <PERSON><PERSON> is running
  # The actual Supabase containers are managed by the Supabase CLI
  setup:
    image: alpine:latest
    container_name: iepa-dev-setup
    command: |
      sh -c "
        echo '🚀 IEPA Conference Registration Development Environment'
        echo '📊 Supabase Studio: http://127.0.0.1:54323'
        echo '🔗 API URL: http://127.0.0.1:54321'
        echo '📧 Email Testing: http://127.0.0.1:54324'
        echo '🗄️  Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres'
        echo ''
        echo '💡 Run the following commands to start development:'
        echo '   ./scripts/setup-local-supabase.sh'
        echo '   npm run dev'
        echo ''
        echo 'Keeping container alive for reference...'
        tail -f /dev/null
      "
    restart: unless-stopped
    labels:
      - "com.iepa.project=conference-registration"
      - "com.iepa.environment=development"

networks:
  default:
    name: iepa-dev-network
