'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardBody, CardHeader, CardTitle, Button } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { useDashboardData } from '@/hooks/useDashboardData';
import { CONFERENCE_DATES } from '@/lib/conference-config';
import RegistrationStats from '@/components/dashboard/stats/RegistrationStats';
import {
  FiRefreshCw,
  FiUsers,
  FiMic,
  FiStar,
  FiCreditCard,
  FiFileText,
  FiMail,
  FiBarChart,
  FiCalendar,
  FiDollarSign,
  FiActivity,
} from 'react-icons/fi';
import Link from 'next/link';

export default function AdminDashboardPage() {
  const { data, loading, refreshData } = useDashboardData({
    autoRefresh: true,
    refreshInterval: 60000, // 1 minute
  });

  const [isRefreshing, setIsRefreshing] = useState(false);

  // Memoize default stats to prevent unnecessary re-renders
  const defaultStats = useMemo(
    () => ({
      totalAttendees: 0,
      totalSpeakers: 0,
      totalSponsors: 0,
      totalRevenue: 0,
      pendingPayments: 0,
      completedPayments: 0,
      golfParticipants: 0,
      mealSelections: {},
    }),
    []
  );

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
    } finally {
      setIsRefreshing(false);
    }
  };

  const quickActions = [
    {
      title: 'Manage Attendees',
      description: 'View and edit attendee registrations',
      href: '/admin/attendees',
      icon: FiUsers,
      color: 'bg-blue-500',
      count: data?.stats.totalAttendees || 0,
    },
    {
      title: 'Manage Speakers',
      description: 'Review speaker applications',
      href: '/admin/speakers',
      icon: FiMic,
      color: 'bg-green-500',
      count: data?.stats.totalSpeakers || 0,
    },
    {
      title: 'Manage Sponsors',
      description: 'Handle sponsor registrations',
      href: '/admin/sponsors',
      icon: FiStar,
      color: 'bg-purple-500',
      count: data?.stats.totalSponsors || 0,
    },
    {
      title: 'Payment Records',
      description: 'View payment transactions',
      href: '/admin/payments',
      icon: FiCreditCard,
      color: 'bg-orange-500',
      count: data?.stats.completedPayments || 0,
    },
    {
      title: 'Email Center',
      description: 'Send notifications and updates',
      href: '/admin/emails',
      icon: FiMail,
      color: 'bg-indigo-500',
      count: 0, // TODO: Add email count
    },
    {
      title: 'Email Templates',
      description: 'Manage email templates',
      href: '/admin/email-templates',
      icon: FiMail,
      color: 'bg-purple-500',
      count: 0, // TODO: Add template count
    },
  ];

  const recentActivity = [
    {
      type: 'registration',
      message: 'New attendee registration',
      time: '2 minutes ago',
      icon: FiUsers,
    },
    {
      type: 'payment',
      message: 'Payment completed',
      time: '5 minutes ago',
      icon: FiDollarSign,
    },
    {
      type: 'speaker',
      message: 'Speaker application submitted',
      time: '10 minutes ago',
      icon: FiMic,
    },
    {
      type: 'system',
      message: 'Receipt generated',
      time: '15 minutes ago',
      icon: FiFileText,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Action Bar */}
      <div className="flex items-center justify-end space-x-3">
        <Badge variant="outline" className="text-sm">
          <FiCalendar className="w-4 h-4 mr-2" />
          {CONFERENCE_DATES.startDate.displayDate} -{' '}
          {CONFERENCE_DATES.endDate.displayDate}
        </Badge>
        <Button
          onClick={handleRefresh}
          disabled={isRefreshing}
          variant="outline"
          size="sm"
        >
          <FiRefreshCw
            className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`}
          />
          Refresh
        </Button>
      </div>

      {/* Statistics */}
      <div key="registration-stats">
        <RegistrationStats
          stats={data?.stats || defaultStats}
          isLoading={loading.isLoading}
        />
      </div>

      {/* Quick Actions Grid */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quickActions.map(action => {
            const Icon = action.icon;
            return (
              <Link key={action.href} href={action.href}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardBody className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div
                        className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center`}
                      >
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <Badge
                        variant="secondary"
                        className="text-lg font-semibold"
                      >
                        {action.count}
                      </Badge>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {action.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {action.description}
                    </p>
                  </CardBody>
                </Card>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Recent Activity and System Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiActivity className="w-5 h-5 mr-2" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => {
                const Icon = activity.icon;
                return (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <Icon className="w-4 h-4 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.message}
                      </p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="mt-4 pt-4 border-t">
              <Link
                href="/admin/audit"
                className="text-sm text-[var(--iepa-primary-blue)] hover:underline"
              >
                View all activity →
              </Link>
            </div>
          </CardBody>
        </Card>

        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiBarChart className="w-5 h-5 mr-2" />
              System Status
            </CardTitle>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Database Status
                </span>
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800"
                >
                  Healthy
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Payment Processing
                </span>
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800"
                >
                  Active
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Email Service
                </span>
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800"
                >
                  Operational
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  File Storage
                </span>
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800"
                >
                  Available
                </Badge>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <Link
                href="/admin/settings"
                className="text-sm text-[var(--iepa-primary-blue)] hover:underline"
              >
                System settings →
              </Link>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
