import { NextRequest, NextResponse } from 'next/server';
import { fixStoragePolicies, testStorageAccess } from '@/utils/fix-storage-policies';

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    if (action === 'fix-policies') {
      const result = await fixStoragePolicies();
      return NextResponse.json(result);
    }

    if (action === 'test-access') {
      const result = await testStorageAccess();
      return NextResponse.json(result);
    }

    return NextResponse.json(
      { success: false, message: 'Invalid action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Storage fix API error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const result = await testStorageAccess();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Storage test API error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
