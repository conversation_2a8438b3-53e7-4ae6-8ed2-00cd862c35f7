-- Add check tracking fields to iepa_sponsor_registrations table
-- This migration adds fields to track when checks are received and processed

-- Add check tracking fields
ALTER TABLE iepa_sponsor_registrations 
ADD COLUMN IF NOT EXISTS check_received BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS check_received_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS check_number TEXT,
ADD COLUMN IF NOT EXISTS check_notes TEXT;

-- Add comment for documentation
COMMENT ON COLUMN iepa_sponsor_registrations.check_received IS 'Whether the physical check has been received by IEPA';
COMMENT ON COLUMN iepa_sponsor_registrations.check_received_date IS 'Date when the check was physically received';
COMMENT ON COLUMN iepa_sponsor_registrations.check_number IS 'The check number from the physical check';
COMMENT ON COLUMN iepa_sponsor_registrations.check_notes IS 'Additional notes about the check (e.g., memo line, special instructions)';
