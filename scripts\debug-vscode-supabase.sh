#!/bin/bash

# IEPA Conference Registration - Debug VS Code Supabase Extension
# This script provides comprehensive debugging for the VS Code Supabase extension

set -e

echo "🔍 Debugging VS Code Supabase Extension Connection..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Ensure we're in the project root
cd "$(dirname "$0")/.."

echo "=================================================="
echo "  VS Code Supabase Extension Debug Report"
echo "=================================================="
echo ""

# 1. Check current directory and project structure
print_status "1. Project Structure Check"
echo "   Current directory: $(pwd)"
echo "   Project name: $(basename $(pwd))"

if [ -f "supabase/config.toml" ]; then
    print_success "   ✅ supabase/config.toml exists"
    PROJECT_ID=$(grep "project_id" supabase/config.toml | cut -d'"' -f2)
    echo "   Project ID: $PROJECT_ID"
else
    print_error "   ❌ supabase/config.toml missing"
fi

# 2. Check Supabase CLI status
print_status "2. Supabase CLI Status"
if command -v supabase &> /dev/null; then
    print_success "   ✅ Supabase CLI installed"
    echo "   Version: $(supabase --version)"
    
    if supabase status > /dev/null 2>&1; then
        print_success "   ✅ Supabase is running"
        echo "   Services status:"
        supabase status | grep -E "(API URL|Studio URL|DB URL)" | sed 's/^/     /'
    else
        print_error "   ❌ Supabase is not running"
        echo "   Run: supabase start"
    fi
else
    print_error "   ❌ Supabase CLI not installed"
fi

# 3. Check Docker containers
print_status "3. Docker Containers Check"
if command -v docker &> /dev/null; then
    print_success "   ✅ Docker is available"
    
    SUPABASE_CONTAINERS=$(docker ps --filter "name=supabase" --format "table {{.Names}}\t{{.Status}}" | grep -v NAMES | wc -l)
    if [ "$SUPABASE_CONTAINERS" -gt 0 ]; then
        print_success "   ✅ $SUPABASE_CONTAINERS Supabase containers running"
        docker ps --filter "name=supabase" --format "     {{.Names}}: {{.Status}}"
    else
        print_warning "   ⚠️  No Supabase containers running"
    fi
else
    print_error "   ❌ Docker not available"
fi

# 4. Check network connectivity
print_status "4. Network Connectivity Check"
if curl -s http://127.0.0.1:54321/rest/v1/ > /dev/null 2>&1; then
    print_success "   ✅ API endpoint responding (http://127.0.0.1:54321)"
else
    print_error "   ❌ API endpoint not responding"
fi

if curl -s http://127.0.0.1:54323 > /dev/null 2>&1; then
    print_success "   ✅ Studio endpoint responding (http://127.0.0.1:54323)"
else
    print_error "   ❌ Studio endpoint not responding"
fi

# 5. Check VS Code configuration files
print_status "5. VS Code Configuration Check"
if [ -f ".vscode/settings.json" ]; then
    print_success "   ✅ .vscode/settings.json exists"
    if grep -q "supabase" .vscode/settings.json; then
        print_success "   ✅ Supabase settings found in VS Code config"
    else
        print_warning "   ⚠️  No Supabase settings in VS Code config"
    fi
else
    print_warning "   ⚠️  .vscode/settings.json missing"
fi

if [ -f "iepa-conf-reg.code-workspace" ]; then
    print_success "   ✅ Workspace file exists"
else
    print_warning "   ⚠️  Workspace file missing"
fi

# 6. Check environment files
print_status "6. Environment Files Check"
if [ -f ".env.local" ]; then
    print_success "   ✅ .env.local exists"
    if grep -q "SUPABASE" .env.local; then
        print_success "   ✅ Supabase environment variables found"
    else
        print_warning "   ⚠️  No Supabase variables in .env.local"
    fi
else
    print_warning "   ⚠️  .env.local missing"
fi

if [ -f "supabase/.env" ]; then
    print_success "   ✅ supabase/.env exists"
else
    print_warning "   ⚠️  supabase/.env missing"
fi

echo ""
echo "=================================================="
echo "  Troubleshooting Recommendations"
echo "=================================================="
echo ""

print_status "🔧 Try these solutions in order:"
echo ""
echo "1. 🔄 Restart VS Code Extension:"
echo "   - Open VS Code Command Palette (Cmd+Shift+P)"
echo "   - Run: 'Developer: Reload Window'"
echo "   - Disable Supabase extension, restart VS Code, re-enable"
echo ""
echo "2. 📁 Use Workspace File:"
echo "   - File → Open Workspace from File"
echo "   - Select: iepa-conf-reg.code-workspace"
echo ""
echo "3. 🔌 Manual Connection:"
echo "   - Open Supabase extension"
echo "   - Click 'Connect to project'"
echo "   - Choose 'Local project'"
echo "   - Select this directory: $(pwd)"
echo ""
echo "4. 🛠️ Alternative: Use Supabase Studio directly:"
echo "   - Open: http://127.0.0.1:54323"
echo "   - This gives you full database access"
echo ""
echo "5. 🔍 Check VS Code Developer Console:"
echo "   - Help → Toggle Developer Tools"
echo "   - Look for Supabase extension errors"
echo ""

print_status "🌐 Direct Access URLs:"
echo "   Studio: http://127.0.0.1:54323"
echo "   API: http://127.0.0.1:54321"
echo "   Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres"
