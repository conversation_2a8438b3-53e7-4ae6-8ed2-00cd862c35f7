'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardBody, CardHeader, CardTitle, Button } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  FiActivity,
  FiRefreshCw,
  FiSearch,
  FiCalendar,
  FiUser,
  FiDatabase,
  FiEdit,
  FiTrash2,
  FiPlus,
  FiEye,
  FiShield,
  FiAlertTriangle,
  FiInfo,
  FiCheckCircle,
} from 'react-icons/fi';
import { createSupabaseAdmin } from '@/lib/supabase';

interface AuditLogEntry {
  id: string;
  user_id: string;
  user_email: string;
  action: string;
  resource_type: string;
  resource_id: string;
  details: Record<string, any>;
  ip_address: string;
  user_agent: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  created_at: string;
}

interface Filters {
  search: string;
  action: string;
  resource_type: string;
  severity: string;
  date_range: string;
}

export default function AdminAuditPage() {
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<Filters>({
    search: '',
    action: 'all',
    resource_type: 'all',
    severity: 'all',
    date_range: 'all',
  });

  const supabase = createSupabaseAdmin();

  // Mock data for demonstration since audit table might not exist
  const mockAuditData: AuditLogEntry[] = [
    {
      id: '1',
      user_id: 'user-1',
      user_email: '<EMAIL>',
      action: 'CREATE',
      resource_type: 'attendee_registration',
      resource_id: 'reg-123',
      details: { registration_type: 'member', amount: 450 },
      ip_address: '*************',
      user_agent: 'Mozilla/5.0...',
      severity: 'info',
      created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    },
    {
      id: '2',
      user_id: 'user-2',
      user_email: '<EMAIL>',
      action: 'UPDATE',
      resource_type: 'sponsor_registration',
      resource_id: 'sponsor-456',
      details: { field_changed: 'sponsorship_level', old_value: 'silver', new_value: 'gold' },
      ip_address: '*************',
      user_agent: 'Mozilla/5.0...',
      severity: 'info',
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    },
    {
      id: '3',
      user_id: 'user-1',
      user_email: '<EMAIL>',
      action: 'DELETE',
      resource_type: 'payment_record',
      resource_id: 'pay-789',
      details: { reason: 'duplicate_payment', amount: 200 },
      ip_address: '*************',
      user_agent: 'Mozilla/5.0...',
      severity: 'warning',
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
    },
    {
      id: '4',
      user_id: 'system',
      user_email: '<EMAIL>',
      action: 'BACKUP',
      resource_type: 'database',
      resource_id: 'backup-001',
      details: { backup_size: '2.5GB', duration: '45s' },
      ip_address: '127.0.0.1',
      user_agent: 'System/1.0',
      severity: 'info',
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
    },
    {
      id: '5',
      user_id: 'user-3',
      user_email: '<EMAIL>',
      action: 'LOGIN_FAILED',
      resource_type: 'authentication',
      resource_id: 'auth-failed',
      details: { reason: 'invalid_credentials', attempts: 3 },
      ip_address: '***********',
      user_agent: 'Mozilla/5.0...',
      severity: 'warning',
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 8).toISOString(), // 8 hours ago
    },
  ];

  // Fetch audit logs
  const fetchAuditLogs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // For now, use mock data since audit table might not exist
      // In a real implementation, you would query the audit table:
      // const { data, error } = await supabase
      //   .from('iepa_audit_logs')
      //   .select('*')
      //   .order('created_at', { ascending: false });

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      let filteredData = [...mockAuditData];

      // Apply filters
      if (filters.search) {
        filteredData = filteredData.filter(log =>
          log.user_email.toLowerCase().includes(filters.search.toLowerCase()) ||
          log.action.toLowerCase().includes(filters.search.toLowerCase()) ||
          log.resource_type.toLowerCase().includes(filters.search.toLowerCase())
        );
      }

      if (filters.action && filters.action !== 'all') {
        filteredData = filteredData.filter(log => log.action === filters.action);
      }

      if (filters.resource_type && filters.resource_type !== 'all') {
        filteredData = filteredData.filter(log => log.resource_type === filters.resource_type);
      }

      if (filters.severity && filters.severity !== 'all') {
        filteredData = filteredData.filter(log => log.severity === filters.severity);
      }

      setAuditLogs(filteredData);
    } catch (err) {
      console.error('Error fetching audit logs:', err);
      setError('Failed to fetch audit logs');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchAuditLogs();
  }, [fetchAuditLogs]);

  const getActionBadge = (action: string) => {
    switch (action) {
      case 'CREATE':
        return <Badge className="bg-green-100 text-green-800">Create</Badge>;
      case 'UPDATE':
        return <Badge className="bg-blue-100 text-blue-800">Update</Badge>;
      case 'DELETE':
        return <Badge className="bg-red-100 text-red-800">Delete</Badge>;
      case 'LOGIN':
        return <Badge className="bg-purple-100 text-purple-800">Login</Badge>;
      case 'LOGIN_FAILED':
        return <Badge className="bg-red-100 text-red-800">Login Failed</Badge>;
      case 'BACKUP':
        return <Badge className="bg-gray-100 text-gray-800">Backup</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{action}</Badge>;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'info':
        return <Badge className="bg-blue-100 text-blue-800 flex items-center"><FiInfo className="w-3 h-3 mr-1" />Info</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800 flex items-center"><FiAlertTriangle className="w-3 h-3 mr-1" />Warning</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800 flex items-center"><FiAlertTriangle className="w-3 h-3 mr-1" />Error</Badge>;
      case 'critical':
        return <Badge className="bg-red-200 text-red-900 flex items-center"><FiAlertTriangle className="w-3 h-3 mr-1" />Critical</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{severity}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const getResourceIcon = (resourceType: string) => {
    switch (resourceType) {
      case 'attendee_registration':
        return <FiUser className="w-4 h-4" />;
      case 'sponsor_registration':
        return <FiShield className="w-4 h-4" />;
      case 'payment_record':
        return <FiDatabase className="w-4 h-4" />;
      case 'database':
        return <FiDatabase className="w-4 h-4" />;
      case 'authentication':
        return <FiShield className="w-4 h-4" />;
      default:
        return <FiActivity className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <FiActivity className="w-8 h-8 mr-3 text-[var(--iepa-primary-blue)]" />
            Audit Log
          </h1>
          <p className="text-gray-600 mt-1">
            System activity and security audit trail
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button onClick={fetchAuditLogs} variant="outline" size="sm" disabled={loading}>
            <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Warning Banner */}
      <Card className="border-blue-200 bg-blue-50">
        <CardBody className="p-4">
          <div className="flex items-center">
            <FiInfo className="w-5 h-5 text-blue-600 mr-3" />
            <div>
              <h3 className="font-medium text-blue-800">Demo Data</h3>
              <p className="text-sm text-blue-700">
                Showing sample audit log entries. In production, this would display real system activity from the audit log database.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Search and Filters */}
      <Card>
        <CardBody>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search by user, action, or resource..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  className="pl-10"
                />
              </div>
            </div>
            <Select
              value={filters.action}
              onValueChange={(value) => setFilters({ ...filters, action: value })}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                <SelectItem value="CREATE">Create</SelectItem>
                <SelectItem value="UPDATE">Update</SelectItem>
                <SelectItem value="DELETE">Delete</SelectItem>
                <SelectItem value="LOGIN">Login</SelectItem>
                <SelectItem value="LOGIN_FAILED">Login Failed</SelectItem>
                <SelectItem value="BACKUP">Backup</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.severity}
              onValueChange={(value) => setFilters({ ...filters, severity: value })}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by severity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severities</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="warning">Warning</SelectItem>
                <SelectItem value="error">Error</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center">
              <FiActivity className="w-8 h-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Events</p>
                <p className="text-2xl font-bold text-gray-900">{auditLogs.length}</p>
              </div>
            </div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center">
              <FiCheckCircle className="w-8 h-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Info Events</p>
                <p className="text-2xl font-bold text-gray-900">
                  {auditLogs.filter(log => log.severity === 'info').length}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center">
              <FiAlertTriangle className="w-8 h-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Warnings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {auditLogs.filter(log => log.severity === 'warning').length}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center">
              <FiAlertTriangle className="w-8 h-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Errors</p>
                <p className="text-2xl font-bold text-gray-900">
                  {auditLogs.filter(log => log.severity === 'error' || log.severity === 'critical').length}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Audit Log Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FiActivity className="w-5 h-5 mr-2" />
            Audit Log Entries ({auditLogs.length})
          </CardTitle>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Loading audit logs...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-2">{error}</div>
              <Button onClick={fetchAuditLogs} variant="outline" size="sm">
                <FiRefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            </div>
          ) : auditLogs.length === 0 ? (
            <div className="text-center py-8">
              <FiActivity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No audit logs found</h3>
              <p className="text-gray-500 mb-4">
                {filters.search || filters.action !== 'all' || filters.severity !== 'all'
                  ? 'No audit logs match your current filters.'
                  : 'No audit logs have been recorded yet.'}
              </p>
              {(filters.search || filters.action !== 'all' || filters.severity !== 'all') && (
                <Button
                  onClick={() => setFilters({ search: '', action: 'all', resource_type: 'all', severity: 'all', date_range: 'all' })}
                  variant="outline"
                  size="sm"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Resource</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditLogs.map(log => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="flex items-center text-sm text-gray-600">
                          <FiCalendar className="w-4 h-4 mr-2" />
                          {formatDate(log.created_at)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                            <FiUser className="w-4 h-4 text-gray-600" />
                          </div>
                          <div>
                            <div className="font-medium text-sm">{log.user_email}</div>
                            <div className="text-xs text-gray-500">ID: {log.user_id.slice(0, 8)}...</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getActionBadge(log.action)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {getResourceIcon(log.resource_type)}
                          <div className="ml-2">
                            <div className="font-medium text-sm">{log.resource_type}</div>
                            <div className="text-xs text-gray-500">{log.resource_id}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getSeverityBadge(log.severity)}</TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-600">{log.ip_address}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-600 max-w-xs truncate">
                          {JSON.stringify(log.details)}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
