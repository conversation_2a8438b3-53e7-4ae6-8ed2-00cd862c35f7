// Database types for agenda data
export interface AgendaDay {
  id: string;
  day_number: number;
  title: string;
  date: string;
  color: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AgendaEvent {
  id: string;
  agenda_day_id: string;
  time: string;
  title: string;
  description: string | null;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Combined type for frontend use
export interface AgendaDayWithEvents extends AgendaDay {
  events: AgendaEvent[];
}

// Legacy types for backward compatibility with existing components
export interface LegacyAgendaEvent {
  time: string;
  title: string;
  description: string;
}

export interface LegacyAgendaDay {
  dayNumber: number;
  title: string;
  date: string;
  color: string;
  events: LegacyAgendaEvent[];
}

// Conversion functions
export function convertToLegacyFormat(agendaDays: AgendaDayWithEvents[]): LegacyAgendaDay[] {
  return agendaDays.map(day => ({
    dayNumber: day.day_number,
    title: day.title,
    date: day.date,
    color: day.color,
    events: day.events.map(event => ({
      time: event.time,
      title: event.title,
      description: event.description || '',
    })),
  }));
}

export function convertFromLegacyFormat(legacyDays: LegacyAgendaDay[]): Omit<AgendaDayWithEvents, 'id' | 'created_at' | 'updated_at'>[] {
  return legacyDays.map((day, dayIndex) => ({
    day_number: day.dayNumber,
    title: day.title,
    date: day.date,
    color: day.color,
    sort_order: dayIndex + 1,
    is_active: true,
    events: day.events.map((event, eventIndex) => ({
      id: '', // Will be generated by database
      agenda_day_id: '', // Will be set by database
      time: event.time,
      title: event.title,
      description: event.description,
      sort_order: eventIndex + 1,
      is_active: true,
      created_at: '',
      updated_at: '',
    })),
  }));
}
