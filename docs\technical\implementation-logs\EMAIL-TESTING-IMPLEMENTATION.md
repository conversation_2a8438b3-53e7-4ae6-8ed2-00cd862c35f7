# Email Testing Implementation for IEPA Conference Registration

## 🎯 Overview

I've implemented a comprehensive email deliverability testing system for your IEPA conference registration application using Playwright. This system supports both local development testing (using Supabase Inbucket) and production-like testing (using real email services like Gmail or Mailtrap).

## 📦 What's Been Implemented

### 1. **Core Email Testing Utilities**

- **`tests/utils/email-testing.js`** - Main email testing functions
  - `waitForEmail()` - Waits for and retrieves emails via IMAP or Inbucket
  - `validateEmailContent()` - Validates email content against expected criteria
  - `extractLinksFromEmail()` - Extracts and validates links from emails
  - Support for both Gmail/Mailtrap (IMAP) and Supabase Inbucket

### 2. **Configuration System**

- **`tests/config/email-test-config.js`** - Email testing configuration
  - Test account management
  - Environment detection (local vs production)
  - Email validation rules for different email types
  - Automatic cleanup utilities

### 3. **Test Specifications**

- **`tests/email-deliverability.spec.js`** - Comprehensive email delivery tests
- **`tests/email-integration.spec.js`** - End-to-end registration flow with email verification
- **Updated `tests/attendee-registration-e2e.spec.js`** - Added email verification to existing tests

### 4. **Setup and Configuration**

- **`scripts/test-email-setup.js`** - Email configuration verification script
- **`.env.email-testing.example`** - Template for email testing credentials
- **`.docs/testing/email-testing-guide.md`** - Comprehensive documentation

### 5. **Package Dependencies**

- Added `imap-simple` and `mailparser` for IMAP email access
- Updated npm scripts for email testing

## 🚀 Quick Start

### 1. **Local Development (Recommended)**

```bash
# Start Supabase (includes Inbucket email testing)
npm run supabase:start

# Start development server
npm run dev

# Test email configuration
npm run test:email

# Run email deliverability tests
npm run test:email:deliverability

# View captured emails
open http://127.0.0.1:54324
```

### 2. **Production-like Testing**

```bash
# Copy email testing configuration
cp .env.email-testing.example .env.email-testing.local

# Edit .env.email-testing.local with your Gmail app-specific password
# or Mailtrap credentials

# Run comprehensive email tests
npm run test:email:all
```

## 📧 Email Testing Approaches

### Local Development (Supabase Inbucket)

- **Pros**: Fast, reliable, no external dependencies, visual email inspection
- **Cons**: Doesn't test actual email delivery
- **Use Case**: Development and CI/CD testing

### Production-like Testing (Gmail/Mailtrap)

- **Pros**: Tests real email delivery, validates formatting, tests spam filtering
- **Cons**: Requires setup, potential rate limits, network dependencies
- **Use Case**: Pre-deployment validation, comprehensive testing

## 🧪 Test Coverage

### Email Types Tested

1. **Registration Confirmation** - Sent after successful registration
2. **Welcome Email** - Detailed welcome message for attendees
3. **Payment Confirmation** - Receipt with PDF attachment
4. **Speaker Confirmation** - Speaker-specific confirmation
5. **Sponsor Instructions** - Payment by check instructions

### Test Scenarios

- ✅ Email delivery speed and reliability
- ✅ Email content validation (IEPA branding, correct information)
- ✅ Link functionality (confirmation links work)
- ✅ Attachment handling (PDF receipts)
- ✅ Email formatting (HTML and text versions)
- ✅ Database logging verification
- ✅ Error handling (invalid email addresses)

## 🔧 Configuration Options

### Environment Variables

```env
# Primary test email account (Gmail with app-specific password)
TEST_EMAIL_ADDRESS=<EMAIL>
TEST_EMAIL_PASSWORD=your-app-specific-password

# Alternative: Mailtrap credentials
MAILTRAP_EMAIL=your-mailtrap-username
MAILTRAP_PASSWORD=your-mailtrap-password

# Testing preferences
USE_INBUCKET_FOR_LOCAL=true
EMAIL_TEST_TIMEOUT=30000
MARK_TEST_EMAILS_AS_READ=true
```

### NPM Scripts Added

```json
{
  "test:email": "node scripts/test-email-setup.js",
  "test:email:deliverability": "npx playwright test tests/email-deliverability.spec.js",
  "test:email:integration": "npx playwright test tests/email-integration.spec.js",
  "test:email:all": "npx playwright test tests/email-*.spec.js"
}
```

## 🛠 Setup Instructions

### For Gmail Testing

1. Create a dedicated Gmail account for testing
2. Enable 2-Factor Authentication
3. Generate App-Specific Password:
   - Google Account → Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
4. Configure in `.env.email-testing.local`

### For Mailtrap Testing

1. Sign up at [Mailtrap.io](https://mailtrap.io/)
2. Create a new inbox
3. Get IMAP credentials from inbox settings
4. Configure in `.env.email-testing.local`

## 🔍 Debugging and Troubleshooting

### Common Issues

- **Emails not received**: Check spam folders, verify credentials
- **Authentication failures**: Use app-specific passwords for Gmail
- **Timeout issues**: Increase timeout values, check network connectivity

### Debugging Tools

```bash
# Test email configuration
npm run test:email

# View Inbucket emails (local)
open http://127.0.0.1:54324

# Check Supabase email logs
npm run supabase:studio
```

## 📊 Integration with Existing Tests

The email testing system integrates seamlessly with your existing Playwright tests:

```javascript
// Example: Add email verification to any registration test
const email = await waitForEmail({
  subjectMatch: 'Registration Confirmation',
  toMatch: testUser.email,
  useInbucket: shouldUseInbucket(),
});

const validation = validateEmailContent(email, {
  subjectContains: ['registration', 'confirmation'],
  bodyContains: ['iepa', 'conference'],
});

expect(validation.isValid).toBe(true);
```

## 🔒 Security Considerations

- ✅ Never commit real email credentials to version control
- ✅ Use app-specific passwords for Gmail (not main account password)
- ✅ Use dedicated test email accounts
- ✅ Regularly rotate test account passwords
- ✅ Monitor for unauthorized access to test accounts

## 🎉 Benefits

1. **Comprehensive Coverage**: Tests all email types in your registration system
2. **Flexible Testing**: Supports both local and production-like testing
3. **Easy Setup**: Works out-of-the-box with Supabase Inbucket
4. **Detailed Validation**: Validates email content, links, and attachments
5. **CI/CD Ready**: Designed for automated testing pipelines
6. **Well Documented**: Comprehensive guides and examples

## 🚀 Next Steps

1. **Test the Setup**: Run `npm run test:email` to verify configuration
2. **Run Local Tests**: Use `npm run test:email:deliverability` for quick testing
3. **Configure Production Testing**: Set up Gmail or Mailtrap for comprehensive testing
4. **Integrate with CI/CD**: Add email tests to your deployment pipeline
5. **Monitor Email Delivery**: Use the logging system to track email performance

The email testing system is now ready to ensure reliable email delivery for your IEPA conference registration system! 🎯
