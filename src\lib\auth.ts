import { supabase } from './supabase';
import { getDynamicAppUrl, getAuthRedirectUrl } from './port-utils';
import type { User, Session } from '@supabase/supabase-js';

export type AuthUser = User;

export type AuthSession = Session;

// Re-export for backward compatibility
export const getAppUrl = getDynamicAppUrl;

// Authentication helper functions
export const auth = {
  // Sign up with email and password
  signUp: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });
    return { data, error };
  },

  // Sign in with email and password
  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  },

  // Sign in with magic link
  signInWithMagicLink: async (email: string, redirectTo?: string) => {
    const { getAuthRedirectUrl } = await import('@/lib/port-utils');
    const finalRedirectTo = redirectTo || '/my-registrations';
    const authRedirectUrl = getAuthRedirectUrl(
      `/auth/callback?next=${encodeURIComponent(finalRedirectTo)}`
    );

    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: authRedirectUrl,
      },
    });
    return { data, error };
  },

  // Sign out
  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  // Reset password
  resetPassword: async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: getAuthRedirectUrl('/auth/reset-password'),
    });
    return { data, error };
  },

  // Update password
  updatePassword: async (password: string) => {
    const { data, error } = await supabase.auth.updateUser({
      password,
    });
    return { data, error };
  },

  // Get current session
  getSession: async () => {
    const { data, error } = await supabase.auth.getSession();
    return { data, error };
  },

  // Get current user
  getUser: async () => {
    const { data, error } = await supabase.auth.getUser();
    return { data, error };
  },

  // Listen to auth state changes
  onAuthStateChange: (
    callback: (event: string, session: Session | null) => void
  ) => {
    return supabase.auth.onAuthStateChange(callback);
  },
};

// Helper to check if user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  const { data } = await auth.getSession();
  return !!data.session;
};

// Helper to get authenticated user
export const getAuthenticatedUser = async (): Promise<AuthUser | null> => {
  const { data } = await auth.getUser();
  return data.user as AuthUser | null;
};
