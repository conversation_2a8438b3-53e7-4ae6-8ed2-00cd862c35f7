// Test API to verify admin client works
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

export async function GET() {
  try {
    const supabaseAdmin = createSupabaseAdmin();
    
    // Test query to get payment records to see what payment intents exist
    const { data, error } = await supabaseAdmin
      .from('iepa_payments')
      .select('*')
      .limit(3);

    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
        details: error
      });
    }

    return NextResponse.json({
      success: true,
      count: data?.length || 0,
      data: data
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
