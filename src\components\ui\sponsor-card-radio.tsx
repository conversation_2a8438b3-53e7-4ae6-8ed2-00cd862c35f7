'use client';

import React, { useState, useEffect } from 'react';
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  FaGem,
  FaTrophy,
  FaMedal,
  FaAward,
  FaStar,
  FaCheck,
} from 'react-icons/fa';
import Link from 'next/link';
import type { SponsorshipPackage } from '@/lib/pricing-config';

// Hook to detect mobile viewport
function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 640); // 640px is our mobile breakpoint
    };

    // Check on mount
    checkIsMobile();

    // Add event listener for resize
    window.addEventListener('resize', checkIsMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
}

// Icon mapping for different sponsorship levels
const getSponsorshipIcon = (level: string) => {
  const iconMap = {
    diamond: FaGem,
    platinum: FaTrophy,
    gold: FaMedal,
    silver: FaAward,
    bronze: FaStar,
  };

  return iconMap[level as keyof typeof iconMap] || FaStar;
};

// Color mapping for different sponsorship levels
const getSponsorshipColors = (level: string) => {
  const colorMap = {
    diamond: {
      border: 'border-purple-300',
      bg: 'bg-purple-50',
      icon: 'text-purple-600',
      badge: 'bg-purple-100 text-purple-800',
      selected: 'border-purple-500 bg-purple-100',
    },
    platinum: {
      border: 'border-gray-400',
      bg: 'bg-gray-50',
      icon: 'text-gray-600',
      badge: 'bg-gray-100 text-gray-800',
      selected: 'border-gray-600 bg-gray-100',
    },
    gold: {
      border: 'border-yellow-300',
      bg: 'bg-yellow-50',
      icon: 'text-yellow-600',
      badge: 'bg-yellow-100 text-yellow-800',
      selected: 'border-yellow-500 bg-yellow-100',
    },
    silver: {
      border: 'border-slate-300',
      bg: 'bg-slate-50',
      icon: 'text-slate-600',
      badge: 'bg-slate-100 text-slate-800',
      selected: 'border-slate-500 bg-slate-100',
    },
    bronze: {
      border: 'border-orange-300',
      bg: 'bg-orange-50',
      icon: 'text-orange-600',
      badge: 'bg-orange-100 text-orange-800',
      selected: 'border-orange-500 bg-orange-100',
    },
  };

  return colorMap[level as keyof typeof colorMap] || colorMap.bronze;
};

interface SponsorCardProps {
  package: SponsorshipPackage;
  isSelected?: boolean;
  onSelect?: (packageId: string) => void;
  className?: string;
  showAsCard?: boolean;
}

export function SponsorCard({
  package: pkg,
  isSelected = false,
  onSelect,
  className,
  showAsCard = false,
}: SponsorCardProps) {
  const IconComponent = getSponsorshipIcon(pkg.level);
  const colors = getSponsorshipColors(pkg.level);
  const isMobile = useIsMobile();

  // Mobile accordion content
  const mobileAccordionContent = (
    <div className="space-y-4">
      {/* Price Display */}
      <div className="text-center">
        <div className="text-2xl font-bold text-[var(--iepa-gray-800)]">
          ${pkg.price.toLocaleString()}
        </div>
        <div className="text-sm text-[var(--iepa-gray-600)]">
          {pkg.includedRegistrations} registration
          {pkg.includedRegistrations > 1 ? 's' : ''} included
        </div>
      </div>

      {/* Benefits */}
      <div>
        <h4 className="font-medium text-[var(--iepa-gray-800)] mb-3">
          Package Benefits
        </h4>
        <ul className="space-y-2">
          {pkg.benefits.map((benefit, index) => (
            <li key={index} className="flex items-start space-x-2">
              <FaCheck className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-[var(--iepa-gray-700)]">
                {benefit}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* Marketing Benefits */}
      {pkg.marketingBenefits && pkg.marketingBenefits.length > 0 && (
        <div>
          <h4 className="font-medium text-[var(--iepa-gray-800)] mb-3">
            Marketing Benefits
          </h4>
          <ul className="space-y-2">
            {pkg.marketingBenefits.map((benefit, index) => (
              <li key={index} className="flex items-start space-x-2">
                <FaCheck className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-[var(--iepa-gray-700)]">
                  {benefit}
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );

  // Mobile accordion header
  const mobileAccordionHeader = (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center space-x-3">
        <div className={cn('p-2 rounded-lg', colors.bg)}>
          <IconComponent className={cn('h-6 w-6', colors.icon)} />
        </div>
        <div>
          <h3 className="font-semibold text-lg text-[var(--iepa-gray-800)]">
            {pkg.name}
          </h3>
          <Badge className={cn('text-xs', colors.badge)}>
            {pkg.level.charAt(0).toUpperCase() + pkg.level.slice(1)} Level
          </Badge>
        </div>
      </div>
      <div className="text-right">
        <div className="text-lg font-bold text-[var(--iepa-gray-800)]">
          ${pkg.price.toLocaleString()}
        </div>
        {isSelected && (
          <div className="text-xs text-[var(--iepa-primary-blue)] font-medium">
            Selected
          </div>
        )}
      </div>
    </div>
  );

  // Desktop card content (original layout)
  const desktopCardContent = (
    <div className="flex items-start space-x-6">
      {/* Left Section: Icon, Title, Price */}
      <div className="flex-shrink-0 space-y-4">
        {/* Header with Icon and Level */}
        <div className="flex items-center space-x-3">
          <div className={cn('p-3 rounded-lg', colors.bg)}>
            <IconComponent className={cn('h-8 w-8', colors.icon)} />
          </div>
          <div>
            <h3 className="font-semibold text-xl text-[var(--iepa-gray-800)]">
              {pkg.name}
            </h3>
            <Badge className={cn('text-xs mt-1', colors.badge)}>
              {pkg.level.charAt(0).toUpperCase() + pkg.level.slice(1)} Level
            </Badge>
          </div>
        </div>

        {/* Price */}
        <div className="text-center py-3 px-4 rounded-lg bg-[var(--iepa-gray-50)] border border-[var(--iepa-gray-200)]">
          <div className="text-3xl font-bold text-[var(--iepa-primary-blue)]">
            ${pkg.price.toLocaleString()}
          </div>
          <div className="text-sm text-[var(--iepa-gray-600)] mt-1">
            Includes {pkg.includedRegistrations} registration
            {pkg.includedRegistrations > 1 ? 's' : ''}
          </div>
        </div>
      </div>

      {/* Right Section: Benefits */}
      <div className="flex-1 space-y-4">
        <div className="grid md:grid-cols-2 gap-4">
          {/* Registration Benefits */}
          <div>
            <h4 className="font-medium text-[var(--iepa-gray-700)] mb-3">
              Registration Benefits
            </h4>
            <ul className="space-y-2">
              {pkg.benefits.slice(0, 4).map((benefit, index) => (
                <li key={index} className="flex items-start space-x-2 text-sm">
                  <FaCheck className="h-3 w-3 text-[var(--iepa-secondary-green)] mt-0.5 flex-shrink-0" />
                  <span className="text-[var(--iepa-gray-700)]">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Marketing Benefits */}
          {pkg.marketingBenefits && pkg.marketingBenefits.length > 0 && (
            <div>
              <h4 className="font-medium text-[var(--iepa-gray-700)] mb-3">
                Marketing Benefits
              </h4>
              <ul className="space-y-2">
                {pkg.marketingBenefits.slice(0, 3).map((benefit, index) => (
                  <li
                    key={index}
                    className="flex items-start space-x-2 text-sm"
                  >
                    <FaCheck className="h-3 w-3 text-[var(--iepa-accent-teal)] mt-0.5 flex-shrink-0" />
                    <span className="text-[var(--iepa-gray-700)]">
                      {benefit}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-[var(--iepa-primary-blue)] text-white">
          <FaCheck className="h-4 w-4" />
        </div>
      )}
    </div>
  );

  // Mobile accordion rendering
  if (isMobile) {
    return (
      <div
        className={cn('relative', className)}
        data-testid={`sponsor-card-${pkg.id}`}
      >
        <RadioGroupPrimitive.Item
          value={pkg.id}
          id={`sponsor-radio-${pkg.id}`}
          className="sr-only"
          onClick={() => onSelect?.(pkg.id)}
          data-testid={`sponsor-radio-${pkg.id}`}
        />
        <label
          htmlFor={`sponsor-radio-${pkg.id}`}
          className={cn(
            'sponsor-card-label block cursor-pointer transition-all duration-200',
            'focus-within:ring-2 focus-within:ring-[var(--iepa-primary-blue)]/20 focus-within:ring-offset-2'
          )}
        >
          <Card
            className={cn(
              'transition-all duration-200 border-2',
              colors.border,
              isSelected
                ? cn(
                    colors.selected,
                    'shadow-lg ring-2 ring-[var(--iepa-primary-blue)]/20'
                  )
                : cn(colors.bg, 'hover:shadow-md'),
              'hover:border-[var(--iepa-primary-blue)]/30'
            )}
          >
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value={pkg.id} className="border-none">
                <AccordionTrigger className="hover:no-underline px-4 py-4 min-h-[44px]">
                  {mobileAccordionHeader}
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  {mobileAccordionContent}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </Card>
        </label>
      </div>
    );
  }

  // Desktop rendering (original layout)
  if (showAsCard) {
    return (
      <Card
        className={cn(
          'transition-all duration-200',
          colors.border,
          isSelected ? colors.selected : colors.bg,
          className
        )}
      >
        <CardContent className="p-6">{desktopCardContent}</CardContent>
      </Card>
    );
  }

  return (
    <div
      className={cn('relative', className)}
      data-testid={`sponsor-card-${pkg.id}`}
    >
      <RadioGroupPrimitive.Item
        value={pkg.id}
        id={`sponsor-radio-${pkg.id}`}
        className="sr-only"
        onClick={() => onSelect?.(pkg.id)}
        data-testid={`sponsor-radio-${pkg.id}`}
      />
      <label
        htmlFor={`sponsor-radio-${pkg.id}`}
        className={cn(
          'sponsor-card-label block cursor-pointer transition-all duration-200',
          'hover:scale-[1.02] focus-within:scale-[1.02]',
          'focus-within:ring-2 focus-within:ring-[var(--iepa-primary-blue)]/20 focus-within:ring-offset-2'
        )}
      >
        <Card
          className={cn(
            'transition-all duration-200 border-2',
            colors.border,
            isSelected
              ? cn(
                  colors.selected,
                  'shadow-lg ring-2 ring-[var(--iepa-primary-blue)]/20'
                )
              : cn(colors.bg, 'hover:shadow-md'),
            'hover:border-[var(--iepa-primary-blue)]/30'
          )}
        >
          <CardContent className="p-6">{desktopCardContent}</CardContent>
        </Card>
      </label>
    </div>
  );
}

interface SponsorCardRadioProps {
  packages: SponsorshipPackage[];
  value?: string;
  onValueChange?: (value: string) => void;
  name?: string;
  required?: boolean;
  className?: string;
  'aria-describedby'?: string;
}

export function SponsorCardRadio({
  packages,
  value,
  onValueChange,
  name,
  required,
  className,
  'aria-describedby': ariaDescribedBy,
}: SponsorCardRadioProps) {
  return (
    <RadioGroupPrimitive.Root
      id="sponsorship-level-selector"
      value={value}
      onValueChange={onValueChange}
      name={name}
      required={required}
      className={cn('sponsor-card-radio-group space-y-4', className)}
      aria-describedby={ariaDescribedBy}
      data-testid="sponsorship-level-selector"
    >
      {/* Single column layout for horizontal sponsor cards */}
      <div
        id="sponsor-cards-container"
        className="space-y-4"
        data-testid="sponsor-cards-container"
      >
        {packages.map(pkg => (
          <SponsorCard
            key={pkg.id}
            package={pkg}
            isSelected={value === pkg.id}
            onSelect={onValueChange}
          />
        ))}
      </div>
    </RadioGroupPrimitive.Root>
  );
}

interface ClickableSponsorCardProps {
  package: SponsorshipPackage;
  className?: string;
  href?: string;
}

export function ClickableSponsorCard({
  package: pkg,
  className,
  href = `/register/sponsor?level=${pkg.id}`,
}: ClickableSponsorCardProps) {
  const IconComponent = getSponsorshipIcon(pkg.level);
  const colors = getSponsorshipColors(pkg.level);
  const isMobile = useIsMobile();

  // Mobile card content (stacked layout)
  const mobileCardContent = (
    <div className="space-y-4">
      {/* Header with Icon, Title, and Price */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={cn('p-2 rounded-lg', colors.bg)}>
            <IconComponent className={cn('h-6 w-6', colors.icon)} />
          </div>
          <div>
            <h3 className="font-semibold text-lg text-[var(--iepa-gray-800)]">
              {pkg.name}
            </h3>
            <Badge className={cn('text-xs', colors.badge)}>
              {pkg.level.charAt(0).toUpperCase() + pkg.level.slice(1)} Level
            </Badge>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xl font-bold text-[var(--iepa-primary-blue)]">
            ${pkg.price.toLocaleString()}
          </div>
          <div className="text-xs text-[var(--iepa-gray-600)]">
            {pkg.includedRegistrations} registration
            {pkg.includedRegistrations > 1 ? 's' : ''}
          </div>
        </div>
      </div>

      {/* Benefits */}
      <div>
        <h4 className="font-medium text-[var(--iepa-gray-800)] mb-3">
          Package Benefits
        </h4>
        <ul className="space-y-2">
          {pkg.benefits.map((benefit, index) => (
            <li key={index} className="flex items-start space-x-2">
              <FaCheck className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-[var(--iepa-gray-700)]">
                {benefit}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* Call to Action */}
      <div className="pt-2 border-t border-gray-200">
        <div className="text-sm text-[var(--iepa-gray-600)] text-center">
          Click to register with this sponsorship level
        </div>
      </div>
    </div>
  );

  // Desktop card content (original horizontal layout)
  const desktopCardContent = (
    <div className="flex items-start space-x-6">
      {/* Left Section: Icon, Title, Price */}
      <div className="flex-shrink-0 space-y-4">
        {/* Header with Icon and Level */}
        <div className="flex items-center space-x-3">
          <div className={cn('p-3 rounded-lg', colors.bg)}>
            <IconComponent className={cn('h-8 w-8', colors.icon)} />
          </div>
          <div>
            <h3 className="font-semibold text-xl text-[var(--iepa-gray-800)]">
              {pkg.name}
            </h3>
            <Badge className={cn('text-xs mt-1', colors.badge)}>
              {pkg.level.charAt(0).toUpperCase() + pkg.level.slice(1)} Level
            </Badge>
          </div>
        </div>

        {/* Price */}
        <div className="text-center">
          <div className="text-3xl font-bold text-[var(--iepa-primary-blue)]">
            ${pkg.price.toLocaleString()}
          </div>
          <div className="text-sm text-[var(--iepa-gray-600)] mt-1">
            Includes {pkg.includedRegistrations} registration
            {pkg.includedRegistrations > 1 ? 's' : ''}
          </div>
        </div>
      </div>

      {/* Right Section: Benefits */}
      <div className="flex-1 space-y-4">
        <div>
          <h4 className="font-medium text-[var(--iepa-gray-800)] mb-3">
            Package Benefits
          </h4>
          <ul className="space-y-2">
            {pkg.benefits.map((benefit, index) => (
              <li key={index} className="flex items-start space-x-2">
                <FaCheck className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-[var(--iepa-gray-700)]">
                  {benefit}
                </span>
              </li>
            ))}
          </ul>
        </div>

        {/* Call to Action */}
        <div className="pt-4">
          <div className="text-sm text-[var(--iepa-gray-600)] mb-2">
            Click to register with this sponsorship level
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <Link href={href} className="block">
      <Card
        className={cn(
          'transition-all duration-200 cursor-pointer',
          'hover:-translate-y-1 hover:shadow-lg hover:border-[var(--iepa-primary-blue)]/50',
          'focus-within:ring-2 focus-within:ring-[var(--iepa-primary-blue)]/20 focus-within:ring-offset-2',
          colors.border,
          colors.bg,
          className
        )}
      >
        <CardContent className={cn('p-4 sm:p-6')}>
          {isMobile ? mobileCardContent : desktopCardContent}
        </CardContent>
      </Card>
    </Link>
  );
}
