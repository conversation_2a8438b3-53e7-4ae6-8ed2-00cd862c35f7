'use client';

import { useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Chip } from '@/components/ui';
import { ProtectedPageBanner } from '@/components/auth/AuthStatusIndicator';
import { useAuth } from '@/contexts/AuthContext';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import { useUserRegistrationDetails } from '@/hooks/useUserRegistrations';
import { PDFDownloadButton } from '@/components/pdf/PDFDownloadButton';
import { formatCurrency, formatDate } from '@/lib/pdf-generation/utils';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  FiEdit,
  FiEye,
  FiCreditCard,
  FiCalendar,
  FiUser,
  FiMic,
  FiFile,
  FiDownload,
  FiBriefcase,
  FiTarget,
  FiCoffee,
} from 'react-icons/fi';
import { SpeakerProfileCard } from '@/components/user/speaker/SpeakerProfileCard';

export default function MySpeakerProfilePage() {
  const { user } = useAuth();
  const router = useRouter();
  const {
    registration: speakerRegistration,
    loading,
    error,
    refresh,
  } = useUserRegistrationDetails('speaker');

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
    }
  }, [user, router]);

  if (!user) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'danger';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      case 'refunded':
        return 'secondary';
      default:
        return 'default';
    }
  };

  // Handle error state
  if (error) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="iepa-heading-1 mb-4 text-red-600">Error</h1>
            <p className="iepa-body mb-4">{error}</p>
            <Button onClick={refresh} color="primary">
              Try Again
            </Button>
          </div>
        </section>
      </div>
    );
  }

  // Handle loading state
  if (loading) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-4xl mx-auto text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h1 className="iepa-heading-1 mb-4">Loading...</h1>
            <p className="iepa-body">Fetching your speaker profile...</p>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div>
      {/* Auth Status Banner */}
      <ProtectedPageBanner />

      <div className="iepa-container">
        {/* Header */}
        <section className="iepa-section">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
              <div>
                <h1 className="iepa-heading-1 mb-2 flex items-center gap-3">
                  <FiMic className="w-8 h-8 text-[var(--iepa-primary-blue)]" />
                  My Speaker Profile
                </h1>
                <p className="iepa-body">
                  View and manage your IEPA {CONFERENCE_YEAR} speaker registration
                </p>
              </div>
              <div className="mt-4 md:mt-0 flex gap-2">
                {speakerRegistration?.speakerInfo?.presentationFileUrl && (
                  <Button
                    as="a"
                    href={speakerRegistration.speakerInfo.presentationFileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    color="primary"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <FiDownload className="w-4 h-4" />
                    Download Presentation
                  </Button>
                )}
                <Button onClick={refresh} variant="bordered" size="sm">
                  Refresh
                </Button>
                <Button as={Link} href="/my-registrations" variant="bordered" size="sm">
                  All Registrations
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Speaker Profile Content */}
        <section className="iepa-section">
          <div className="max-w-6xl mx-auto">
            {!speakerRegistration ? (
              <Card>
                <CardBody>
                  <div className="text-center py-12">
                    <div className="text-6xl mb-6">🎤</div>
                    <h2 className="iepa-heading-2 mb-4">
                      No Speaker Registration Found
                    </h2>
                    <p className="iepa-body mb-8 max-w-2xl mx-auto">
                      You haven&apos;t registered as a speaker for the IEPA {CONFERENCE_YEAR}{' '}
                      conference yet. Register as a speaker to share your expertise with the community!
                    </p>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button
                        as={Link}
                        href="/register/speaker"
                        color="primary"
                        className="flex items-center gap-2"
                      >
                        <FiMic className="w-4 h-4" />
                        Register - Speaker
                      </Button>
                      <Button as={Link} href="/about" variant="bordered">
                        Learn More About Speaking
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            ) : (
              <div className="space-y-6">
                {/* Speaker Registration Overview */}
                <SpeakerProfileCard
                  speaker={speakerRegistration}
                  showActions={true}
                  onEdit={() => {
                    router.push('/my-speaker-profile/edit');
                  }}
                  onViewDetails={() => {
                    // TODO: Implement view details functionality
                    console.log('View speaker details');
                  }}
                />

                {/* Support Information */}
                <Card>
                  <CardHeader>
                    <h3 className="iepa-heading-3">Need Help?</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="iepa-body font-semibold mb-2">Speaker Support</h4>
                        <p className="iepa-body-small mb-4">
                          Have questions about your presentation or need to make changes to your speaker profile?
                        </p>
                        <Button
                          as={Link}
                          href="/contact"
                          variant="bordered"
                          size="sm"
                        >
                          Contact Support
                        </Button>
                      </div>
                      <div>
                        <h4 className="iepa-body font-semibold mb-2">Conference Information</h4>
                        <p className="iepa-body-small mb-4">
                          Get details about the conference schedule, venue, and speaker guidelines.
                        </p>
                        <Button as={Link} href="/about" variant="bordered" size="sm">
                          Conference Details
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}
          </div>
        </section>
      </div>
    </div>
  );
}
