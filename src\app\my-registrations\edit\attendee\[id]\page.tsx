'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button, Card, CardBody } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { FiArrowLeft } from 'react-icons/fi';
import { showSuccess, showError } from '@/utils/notifications';
import { AttendeeEditForm } from '@/components/registration/edit/AttendeeEditForm';
import type { AttendeeRegistration } from '@/types/database';

export default function EditAttendeeRegistrationPage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const [registration, setRegistration] = useState<AttendeeRegistration | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const registrationId = params?.id as string;

  const fetchRegistration = useCallback(async () => {
    if (!user?.id || !registrationId) {
      setError('Missing user or registration ID');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('iepa_attendee_registrations')
        .select('*')
        .eq('id', registrationId)
        .eq('user_id', user.id) // Ensure user can only edit their own registration
        .single();

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      if (!data) {
        throw new Error('Registration not found or access denied');
      }

      setRegistration(data);
    } catch (err) {
      console.error('Error fetching registration:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch registration'
      );
    } finally {
      setLoading(false);
    }
  }, [user?.id, registrationId]);

  useEffect(() => {
    fetchRegistration();
  }, [fetchRegistration]);

  const handleSave = async (updatedData: Partial<AttendeeRegistration>) => {
    if (!registration) return;

    try {
      setSaving(true);
      setError(null);

      const { error: updateError } = await supabase
        .from('iepa_attendee_registrations')
        .update({
          ...updatedData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', registration.id)
        .eq('user_id', user?.id); // Double-check user ownership

      if (updateError) {
        throw new Error(updateError.message);
      }

      showSuccess(
        'Registration Updated',
        'Your registration has been updated successfully!'
      );
      router.push('/my-registrations');
    } catch (err) {
      console.error('Error updating registration:', err);
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to update registration';
      setError(errorMessage);
      showError('Update Failed', errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    router.push('/my-registrations');
  };

  if (loading) {
    return (
      <div className="iepa-container">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--iepa-primary-blue)] mx-auto mb-4"></div>
            <p className="iepa-body">Loading registration...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !registration) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardBody>
                <div className="text-center py-8">
                  <p className="iepa-body text-red-600 mb-4">
                    {error || 'Registration not found'}
                  </p>
                  <Button onClick={handleBack} variant="bordered">
                    Back to My Registrations
                  </Button>
                </div>
              </CardBody>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      {/* Header */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center gap-4 mb-8">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBack}
              className="flex items-center gap-2"
            >
              <FiArrowLeft className="w-4 h-4" />
              Back to My Registrations
            </Button>
            <div>
              <h1 className="iepa-heading-1">Edit Attendee Registration</h1>
              <p className="iepa-body text-gray-600">
                Update your conference registration details
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Edit Form */}
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <AttendeeEditForm
            registration={registration}
            onSave={handleSave}
            saving={saving}
            error={error}
          />
        </div>
      </section>
    </div>
  );
}
