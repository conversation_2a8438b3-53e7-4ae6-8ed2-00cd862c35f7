// Form Persistence Utility for IEPA Conference Registration
// Provides auto-save and restore functionality using localStorage

export interface FormPersistenceConfig {
  formKey: string;
  debounceMs?: number;
  expirationDays?: number;
  excludeFields?: string[];
}

export interface PersistedFormData {
  data: any;
  timestamp: number;
  version: string;
  step?: number;
}

export interface FormPersistenceHook {
  saveFormData: (data: any, step?: number) => void;
  loadFormData: () => any | null;
  clearFormData: () => void;
  hasPersistedData: () => boolean;
  getDataAge: () => number | null;
  isDataExpired: () => boolean;
}

// Storage keys for different forms
export const FORM_STORAGE_KEYS = {
  ATTENDEE: 'iepa_attendee_form_data',
  SPEAKER: 'iepa_speaker_form_data',
  SPONSOR: 'iepa_sponsor_form_data',
  SPONSOR_ATTENDEE_REGISTRATION: 'iepa_sponsor_attendee_form_data',
} as const;

// Default configuration
const DEFAULT_CONFIG: Required<Omit<FormPersistenceConfig, 'formKey'>> = {
  debounceMs: 1000,
  expirationDays: 7,
  excludeFields: ['presentationFile', 'headshot', 'logoFile'], // Exclude file fields
};

// Version for data format compatibility
const PERSISTENCE_VERSION = '1.0.0';

/**
 * Debounce utility function
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Check if localStorage is available
 */
function isLocalStorageAvailable(): boolean {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

/**
 * Safely get item from localStorage
 */
function safeGetItem(key: string): string | null {
  try {
    if (!isLocalStorageAvailable()) return null;
    return localStorage.getItem(key);
  } catch (error) {
    console.warn('Failed to read from localStorage:', error);
    return null;
  }
}

/**
 * Safely set item in localStorage
 */
function safeSetItem(key: string, value: string): boolean {
  try {
    console.log(`🔄 Attempting to save to localStorage: ${key}`, value.substring(0, 100) + '...');

    if (!isLocalStorageAvailable()) {
      console.warn(`❌ localStorage not available for key: ${key}`);
      return false;
    }

    localStorage.setItem(key, value);
    console.log(`✅ Successfully saved to localStorage: ${key}`);

    // Verify the save worked
    const retrieved = localStorage.getItem(key);
    if (retrieved === value) {
      console.log(`✅ Verified save successful for: ${key}`);
      return true;
    } else {
      console.warn(`❌ Save verification failed for: ${key}`);
      return false;
    }
  } catch (error) {
    console.warn(`❌ Failed to write to localStorage for key: ${key}`, error);
    return false;
  }
}

/**
 * Safely remove item from localStorage
 */
function safeRemoveItem(key: string): boolean {
  try {
    if (!isLocalStorageAvailable()) return false;
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.warn('Failed to remove from localStorage:', error);
    return false;
  }
}

/**
 * Filter out excluded fields from form data
 */
function filterFormData(data: any, excludeFields: string[]): any {
  if (!data || typeof data !== 'object') return data;
  
  const filtered = { ...data };
  excludeFields.forEach(field => {
    delete filtered[field];
  });
  
  return filtered;
}

// Cache for persistence instances to avoid multiple instances
const persistenceInstances = new Map<string, FormPersistenceHook>();

/**
 * Create form persistence hook (with instance caching)
 */
export function createFormPersistence(config: FormPersistenceConfig): FormPersistenceHook {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  const { formKey, debounceMs, expirationDays, excludeFields } = fullConfig;

  // Return cached instance if it exists
  if (persistenceInstances.has(formKey)) {
    console.log(`Returning cached persistence instance for ${formKey}`);
    return persistenceInstances.get(formKey)!;
  }

  console.log(`Creating new persistence instance for ${formKey}`);

  // Create debounced save function
  const debouncedSave = debounce((data: any, step?: number) => {
    console.log(`🔄 Debounced save triggered for ${formKey}:`, data);

    const filteredData = filterFormData(data, excludeFields);
    console.log(`🔄 Filtered data for ${formKey}:`, filteredData);

    const persistedData: PersistedFormData = {
      data: filteredData,
      timestamp: Date.now(),
      version: PERSISTENCE_VERSION,
      step,
    };

    console.log(`🔄 Saving form data for ${formKey}:`, persistedData);
    const success = safeSetItem(formKey, JSON.stringify(persistedData));
    console.log(`🔄 Save success for ${formKey}:`, success);

    if (success) {
      console.log(`✅ Save successful, dispatching event for ${formKey}`);
      // Dispatch custom event for UI feedback
      window.dispatchEvent(new CustomEvent('formDataSaved', {
        detail: { formKey, step }
      }));
    } else {
      console.warn(`❌ Save failed for ${formKey}`);
    }
  }, debounceMs);

  const instance: FormPersistenceHook = {
    saveFormData: (data: any, step?: number) => {
      debouncedSave(data, step);
    },

    loadFormData: (): any | null => {
      const stored = safeGetItem(formKey);
      console.log(`Loading form data for ${formKey}:`, stored ? 'found' : 'not found');

      if (!stored) return null;

      try {
        const parsed: PersistedFormData = JSON.parse(stored);
        console.log(`Parsed form data for ${formKey}:`, parsed);

        // Check version compatibility
        if (parsed.version !== PERSISTENCE_VERSION) {
          console.warn('Form data version mismatch, clearing stored data');
          safeRemoveItem(formKey);
          return null;
        }

        // Check expiration
        const ageMs = Date.now() - parsed.timestamp;
        const maxAgeMs = expirationDays * 24 * 60 * 60 * 1000;

        if (ageMs > maxAgeMs) {
          console.info('Form data expired, clearing stored data');
          safeRemoveItem(formKey);
          return null;
        }

        console.log(`Returning form data for ${formKey}:`, parsed.data);
        return parsed.data;
      } catch (error) {
        console.warn('Failed to parse stored form data:', error);
        safeRemoveItem(formKey);
        return null;
      }
    },

    clearFormData: () => {
      safeRemoveItem(formKey);
      window.dispatchEvent(new CustomEvent('formDataCleared', { 
        detail: { formKey } 
      }));
    },

    hasPersistedData: (): boolean => {
      const stored = safeGetItem(formKey);
      console.log(`hasPersistedData for ${formKey}:`, stored ? 'found' : 'not found', stored?.substring(0, 100));
      return stored !== null;
    },

    getDataAge: (): number | null => {
      const stored = safeGetItem(formKey);
      if (!stored) return null;

      try {
        const parsed: PersistedFormData = JSON.parse(stored);
        return Date.now() - parsed.timestamp;
      } catch {
        return null;
      }
    },

    isDataExpired: (): boolean => {
      const stored = safeGetItem(formKey);
      console.log(`isDataExpired for ${formKey}:`, stored ? 'found' : 'not found');

      if (!stored) return true;

      try {
        const parsed: PersistedFormData = JSON.parse(stored);
        const age = Date.now() - parsed.timestamp;
        const maxAgeMs = expirationDays * 24 * 60 * 60 * 1000;
        const expired = age > maxAgeMs;
        console.log(`Data age for ${formKey}:`, age, 'ms, max age:', maxAgeMs, 'ms, expired:', expired);
        return expired;
      } catch (error) {
        console.warn(`Failed to parse stored data for ${formKey}:`, error);
        return true;
      }
    },
  };

  // Cache the instance
  persistenceInstances.set(formKey, instance);
  console.log(`Cached persistence instance for ${formKey}`);

  return instance;
}

/**
 * Utility to clear all form data (for privacy/cleanup)
 */
export function clearAllFormData(): void {
  Object.values(FORM_STORAGE_KEYS).forEach(key => {
    safeRemoveItem(key);
  });
  
  window.dispatchEvent(new CustomEvent('allFormDataCleared'));
}

/**
 * Get summary of all stored form data
 */
export function getStoredFormsSummary(): Array<{
  formType: string;
  hasData: boolean;
  age?: number;
  isExpired?: boolean;
}> {
  return Object.entries(FORM_STORAGE_KEYS).map(([formType, key]) => {
    const stored = safeGetItem(key);
    if (!stored) {
      return { formType, hasData: false };
    }

    try {
      const parsed: PersistedFormData = JSON.parse(stored);
      const age = Date.now() - parsed.timestamp;
      const maxAgeMs = DEFAULT_CONFIG.expirationDays * 24 * 60 * 60 * 1000;
      
      return {
        formType,
        hasData: true,
        age,
        isExpired: age > maxAgeMs,
      };
    } catch {
      return { formType, hasData: false };
    }
  });
}

/**
 * Format age in human-readable format
 */
export function formatDataAge(ageMs: number): string {
  const minutes = Math.floor(ageMs / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
  if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  return 'Just now';
}
