'use client';

import React from 'react';
import { Input, Button } from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  FiSearch,
  FiRefreshCw,
  FiFilter,
  FiX,
  FiDownload,
} from 'react-icons/fi';

interface EmailLogFiltersProps {
  // Filter values
  status: string;
  emailType: string;
  search: string;
  dateFrom?: string;
  dateTo?: string;

  // Filter handlers
  onStatusChange: (status: string) => void;
  onEmailTypeChange: (type: string) => void;
  onSearchChange: (search: string) => void;
  onDateFromChange?: (date: string) => void;
  onDateToChange?: (date: string) => void;
  onRefresh: () => void;
  onReset: () => void;
  onExport?: () => void;

  // State
  loading?: boolean;
  totalResults?: number;

  // Options
  emailTypes?: string[];

  className?: string;
}

const DEFAULT_EMAIL_TYPES = [
  'registration_confirmation',
  'sponsor_confirmation',
  'payment_confirmation',
  'golf_addon',
  'password_reset',
  'welcome_email',
  'welcome',
  'invoice',
  'custom',
];

const STATUS_OPTIONS = [
  { value: 'all', label: 'All Status' },
  { value: 'sent', label: 'Sent' },
  { value: 'failed', label: 'Failed' },
  { value: 'pending', label: 'Pending' },
];

export default function EmailLogFilters({
  status,
  emailType,
  search,
  dateFrom,
  dateTo,
  onStatusChange,
  onEmailTypeChange,
  onSearchChange,
  onDateFromChange,
  onDateToChange,
  onRefresh,
  onReset,
  onExport,
  loading = false,
  totalResults,
  emailTypes = DEFAULT_EMAIL_TYPES,
  className = '',
}: EmailLogFiltersProps) {
  const hasActiveFilters =
    status !== 'all' ||
    emailType !== 'all' ||
    search.trim() !== '' ||
    dateFrom ||
    dateTo;

  const formatEmailType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const emailTypeOptions = [
    { value: 'all', label: 'All Types' },
    ...emailTypes.map(type => ({
      value: type,
      label: formatEmailType(type),
    })),
  ];

  return (
    <div
      className={`bg-white border border-gray-200 rounded-lg p-4 space-y-4 ${className}`}
      role="search"
      aria-label="Email log filters"
    >
      {/* Filter Controls Row */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="flex-1 min-w-0">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search by subject, recipient, or sender..."
              value={search}
              onChange={e => onSearchChange(e.target.value)}
              className="pl-10 pr-4"
              aria-label="Search email logs"
              role="searchbox"
            />
            {search && (
              <button
                onClick={() => onSearchChange('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                title="Clear search"
              >
                <FiX className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Status Filter */}
        <div className="w-full sm:w-40">
          <label htmlFor="status-filter" className="sr-only">
            Filter by email status
          </label>
          <Select value={status} onValueChange={onStatusChange}>
            <SelectTrigger
              id="status-filter"
              aria-label="Filter by email status"
            >
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              {STATUS_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Email Type Filter */}
        <div className="w-full sm:w-48">
          <label htmlFor="email-type-filter" className="sr-only">
            Filter by email type
          </label>
          <Select value={emailType} onValueChange={onEmailTypeChange}>
            <SelectTrigger
              id="email-type-filter"
              aria-label="Filter by email type"
            >
              <SelectValue placeholder="Email Type" />
            </SelectTrigger>
            <SelectContent>
              {emailTypeOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Date Range Filters */}
      {(onDateFromChange || onDateToChange) && (
        <div className="flex flex-col sm:flex-row gap-4">
          {onDateFromChange && (
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From Date
              </label>
              <Input
                type="date"
                value={dateFrom || ''}
                onChange={e => onDateFromChange(e.target.value)}
                className="text-sm"
              />
            </div>
          )}

          {onDateToChange && (
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                To Date
              </label>
              <Input
                type="date"
                value={dateTo || ''}
                onChange={e => onDateToChange(e.target.value)}
                className="text-sm"
              />
            </div>
          )}
        </div>
      )}

      {/* Action Buttons Row */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        {/* Results Info */}
        <div className="flex items-center gap-4">
          {totalResults !== undefined && (
            <span className="text-sm text-gray-600">
              {totalResults.toLocaleString()}{' '}
              {totalResults === 1 ? 'result' : 'results'}
            </span>
          )}

          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              <FiFilter className="w-4 h-4 text-blue-600" />
              <span className="text-sm text-blue-600 font-medium">
                Filters active
              </span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onReset}
              className="text-gray-600 hover:text-gray-900"
            >
              <FiX className="w-4 h-4 mr-2" />
              Clear Filters
            </Button>
          )}

          {onExport && (
            <Button
              variant="outline"
              size="sm"
              onClick={onExport}
              disabled={loading}
              className="text-blue-600 border-blue-200 hover:bg-blue-50"
              title="Export email logs to CSV"
            >
              <FiDownload className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={loading}
            className="text-gray-600 hover:text-gray-900"
          >
            <FiRefreshCw
              className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap items-center gap-2 pt-2 border-t border-gray-100">
          <span className="text-xs text-gray-500 font-medium">
            Active filters:
          </span>

          {status !== 'all' && (
            <div className="flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs">
              <span>
                Status:{' '}
                {STATUS_OPTIONS.find(opt => opt.value === status)?.label}
              </span>
              <button
                onClick={() => onStatusChange('all')}
                className="text-blue-500 hover:text-blue-700"
              >
                <FiX className="w-3 h-3" />
              </button>
            </div>
          )}

          {emailType !== 'all' && (
            <div className="flex items-center gap-1 px-2 py-1 bg-green-50 text-green-700 rounded text-xs">
              <span>
                Type:{' '}
                {emailTypeOptions.find(opt => opt.value === emailType)?.label}
              </span>
              <button
                onClick={() => onEmailTypeChange('all')}
                className="text-green-500 hover:text-green-700"
              >
                <FiX className="w-3 h-3" />
              </button>
            </div>
          )}

          {search.trim() && (
            <div className="flex items-center gap-1 px-2 py-1 bg-purple-50 text-purple-700 rounded text-xs">
              <span>Search: &quot;{search.trim()}&quot;</span>
              <button
                onClick={() => onSearchChange('')}
                className="text-purple-500 hover:text-purple-700"
              >
                <FiX className="w-3 h-3" />
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
