# IEPA Conference Registration System - Deployment Guide

## Overview

This guide covers the deployment process for the IEPA Conference Registration System, including environment setup, configuration, and deployment procedures.

## System Requirements

### Production Environment
- **Platform**: Vercel (recommended) or compatible Node.js hosting
- **Node.js**: Version 18.x or higher
- **Database**: Supabase PostgreSQL
- **CDN**: Vercel Edge Network or CloudFlare
- **Email Service**: SendGrid
- **Payment Processing**: Stripe

### Development Environment
- **Node.js**: Version 18.x or higher
- **Package Manager**: npm, yarn, or pnpm
- **Development Server**: Next.js development server
- **Port**: 6969 (configured for development)

## Environment Configuration

### Required Environment Variables

Create a `.env.local` file with the following variables:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_or_live_key
STRIPE_SECRET_KEY=sk_test_or_live_key
STRIPE_WEBHOOK_SECRET=whsec_webhook_secret

# SendGrid Configuration
SENDGRID_API_KEY=SG.your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>

# Application Configuration
NEXT_PUBLIC_APP_URL=https://reg.iepa.com
NEXTAUTH_URL=https://reg.iepa.com
NEXTAUTH_SECRET=your-nextauth-secret

# Development Configuration (development only)
NEXT_PUBLIC_DEV_PORT=6969
```

### Environment-Specific Configuration

#### Development
```bash
NEXT_PUBLIC_APP_URL=http://localhost:6969
SENDGRID_FROM_EMAIL=<EMAIL>
# Use Stripe test keys
```

#### Staging
```bash
NEXT_PUBLIC_APP_URL=https://staging-reg.iepa.com
# Use Stripe test keys with staging webhook
```

#### Production
```bash
NEXT_PUBLIC_APP_URL=https://reg.iepa.com
SENDGRID_FROM_EMAIL=<EMAIL>
# Use Stripe live keys
```

## Database Setup

### Supabase Configuration

1. **Create Supabase Project**
   ```bash
   # Install Supabase CLI
   npm install -g supabase
   
   # Initialize project
   supabase init
   
   # Link to remote project
   supabase link --project-ref your-project-ref
   ```

2. **Run Database Migrations**
   ```bash
   # Apply all migrations
   supabase db push
   
   # Or run specific migration
   supabase migration up
   ```

3. **Configure Row Level Security**
   - Enable RLS on all tables
   - Apply security policies for user data access
   - Configure admin access policies

### Database Schema

The system uses the following main tables:
- `iepa_user_profiles`
- `iepa_attendee_registrations`
- `iepa_speaker_registrations`
- `iepa_sponsor_registrations`
- `iepa_payments`
- `iepa_email_logs`
- `iepa_email_config`
- `iepa_admin_users`

## Deployment Process

### Vercel Deployment (Recommended)

1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm install -g vercel
   
   # Login to Vercel
   vercel login
   
   # Deploy
   vercel --prod
   ```

2. **Configure Environment Variables**
   - Add all required environment variables in Vercel dashboard
   - Configure custom domain (reg.iepa.com)
   - Set up SSL certificate

3. **Configure Build Settings**
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": ".next",
     "installCommand": "npm install",
     "devCommand": "npm run dev"
   }
   ```

### Alternative Deployment (Docker)

1. **Create Dockerfile**
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production
   COPY . .
   RUN npm run build
   EXPOSE 3000
   CMD ["npm", "start"]
   ```

2. **Build and Deploy**
   ```bash
   docker build -t iepa-registration .
   docker run -p 3000:3000 iepa-registration
   ```

## Third-Party Service Configuration

### Stripe Setup

1. **Configure Webhooks**
   - Endpoint: `https://reg.iepa.com/api/payment/webhook`
   - Events: `checkout.session.completed`, `payment_intent.succeeded`

2. **Configure Products and Prices**
   - Create products for each registration type
   - Set up pricing tiers
   - Configure tax settings

### SendGrid Setup

1. **Domain Authentication**
   - Verify sending domain
   - Configure DKIM and SPF records
   - Set up link branding

2. **Email Templates**
   - Create dynamic templates
   - Configure template variables
   - Test email delivery

## SSL and Security

### SSL Certificate
- Vercel provides automatic SSL via Let's Encrypt
- Custom domains require DNS configuration
- Force HTTPS redirects in production

### Security Headers
```javascript
// next.config.js
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  }
];
```

## Monitoring and Logging

### Error Tracking
- Configure error monitoring service
- Set up alert notifications
- Monitor API response times

### Performance Monitoring
- Track Core Web Vitals
- Monitor database query performance
- Set up uptime monitoring

## Backup and Recovery

### Database Backups
- Supabase provides automatic daily backups
- Configure point-in-time recovery
- Test backup restoration procedures

### Application Backups
- Version control with Git
- Automated deployment rollbacks
- Configuration backup procedures

## Deployment Checklist

### Pre-Deployment
- [ ] Run all tests
- [ ] Update environment variables
- [ ] Verify database migrations
- [ ] Test third-party integrations
- [ ] Review security configurations

### Deployment
- [ ] Deploy to staging environment
- [ ] Run smoke tests
- [ ] Deploy to production
- [ ] Verify all functionality
- [ ] Monitor for errors

### Post-Deployment
- [ ] Verify email delivery
- [ ] Test payment processing
- [ ] Check analytics and monitoring
- [ ] Update documentation
- [ ] Notify stakeholders

---

**Last Updated**: December 2024  
**Deployment Version**: 1.0  
**Next Review**: March 2025
