// Simple PDF Test API Route
// POST /api/pdf/test-simple

import { NextResponse } from 'next/server';

export async function POST() {
  try {
    console.log('Starting simple PDF test...');

    // Test 1: Check if React-PDF can be imported
    try {
      const { renderToBuffer } = await import('@react-pdf/renderer');
      const React = await import('react');
      console.log('✅ React-PDF imported successfully');

      // Test 2: Create a simple PDF using dynamic import for JSX
      const { Document, Page, Text, View } = await import(
        '@react-pdf/renderer'
      );

      const SimpleDocument = React.createElement(
        Document,
        {},
        React.createElement(
          Page,
          { size: 'A4', style: { padding: 30 } },
          React.createElement(
            View,
            {},
            React.createElement(
              Text,
              { style: { fontSize: 20, marginBottom: 10 } },
              'IEPA Test PDF'
            ),
            React.createElement(
              Text,
              { style: { fontSize: 12 } },
              'This is a simple test PDF to verify React-PDF is working.'
            ),
            React.createElement(
              Text,
              { style: { fontSize: 12, marginTop: 10 } },
              `Generated at: ${new Date().toISOString()}`
            )
          )
        )
      );

      console.log('✅ Simple document created');

      // Test 3: Render to buffer
      const pdfBuffer = await renderToBuffer(SimpleDocument);
      console.log('✅ PDF rendered to buffer, size:', pdfBuffer.length);

      // Test 4: Return as response
      const headers = new Headers();
      headers.set('Content-Type', 'application/pdf');
      headers.set(
        'Content-Disposition',
        'attachment; filename="test-simple.pdf"'
      );
      headers.set('Content-Length', pdfBuffer.length.toString());

      return new NextResponse(pdfBuffer, {
        status: 200,
        headers,
      });
    } catch (pdfError) {
      console.error('❌ React-PDF error:', pdfError);
      return NextResponse.json(
        {
          success: false,
          error: 'React-PDF error',
          details:
            pdfError instanceof Error ? pdfError.message : 'Unknown PDF error',
          stack: pdfError instanceof Error ? pdfError.stack : undefined,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('❌ General error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'General error',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Simple PDF test endpoint. Use POST to generate a test PDF.',
    usage: 'POST /api/pdf/test-simple',
  });
}
