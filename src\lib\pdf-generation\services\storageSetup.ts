// Storage Setup for PDF Generation
// Functions to set up and manage Supabase storage buckets for PDF documents

import { createSupabaseAdmin } from '@/lib/supabase';
import { PDF_STORAGE_CONFIG } from '../config';

/**
 * Create PDF storage bucket if it doesn't exist
 */
export async function createPDFStorageBucket(): Promise<{
  success: boolean;
  message: string;
  error?: string;
}> {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    // Check if bucket already exists
    const { data: buckets, error: listError } =
      await supabaseAdmin.storage.listBuckets();

    if (listError) {
      throw listError;
    }

    const bucketExists = buckets.some(
      bucket => bucket.name === PDF_STORAGE_CONFIG.bucket
    );

    if (bucketExists) {
      return {
        success: true,
        message: `Bucket '${PDF_STORAGE_CONFIG.bucket}' already exists`,
      };
    }

    // Create the bucket
    const { error } = await supabaseAdmin.storage.createBucket(
      PDF_STORAGE_CONFIG.bucket,
      {
        public: false, // Private bucket for security
        allowedMimeTypes: PDF_STORAGE_CONFIG.allowedMimeTypes,
        fileSizeLimit: PDF_STORAGE_CONFIG.maxFileSize,
      }
    );

    if (error) {
      throw error;
    }

    return {
      success: true,
      message: `Bucket '${PDF_STORAGE_CONFIG.bucket}' created successfully`,
    };
  } catch (error) {
    console.error('Error creating PDF storage bucket:', error);
    return {
      success: false,
      message: 'Failed to create PDF storage bucket',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Set up storage policies for PDF bucket
 */
export async function setupPDFStoragePolicies(): Promise<{
  success: boolean;
  message: string;
  error?: string;
}> {
  try {
    // Policy for users to upload their own PDFs
    const uploadPolicy = {
      name: 'Users can upload their own PDFs',
      definition: `bucket_id = '${PDF_STORAGE_CONFIG.bucket}' AND auth.uid()::text = (storage.foldername(name))[1]`,
      check: `bucket_id = '${PDF_STORAGE_CONFIG.bucket}' AND auth.uid()::text = (storage.foldername(name))[1]`,
      command: 'INSERT' as const,
    };

    // Policy for users to view their own PDFs
    const viewPolicy = {
      name: 'Users can view their own PDFs',
      definition: `bucket_id = '${PDF_STORAGE_CONFIG.bucket}' AND auth.uid()::text = (storage.foldername(name))[1]`,
      check: null,
      command: 'SELECT' as const,
    };

    // Policy for service role to manage all PDFs
    const adminPolicy = {
      name: 'Service role can manage all PDFs',
      definition: `bucket_id = '${PDF_STORAGE_CONFIG.bucket}' AND auth.jwt() ->> 'role' = 'service_role'`,
      check: null,
      command: 'ALL' as const,
    };

    // Create policies (note: this would typically be done via SQL in production)
    console.log('PDF Storage policies to be created:', {
      uploadPolicy,
      viewPolicy,
      adminPolicy,
    });

    return {
      success: true,
      message: 'PDF storage policies configured (manual SQL setup required)',
    };
  } catch (error) {
    console.error('Error setting up PDF storage policies:', error);
    return {
      success: false,
      message: 'Failed to set up PDF storage policies',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Test PDF storage setup
 */
export async function testPDFStorageSetup(): Promise<{
  success: boolean;
  message: string;
  details: {
    bucketExists: boolean;
    canUpload: boolean;
    canDownload: boolean;
  };
  error?: string;
}> {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    // Check if bucket exists
    const { data: buckets, error: listError } =
      await supabaseAdmin.storage.listBuckets();

    if (listError) {
      throw listError;
    }

    const bucketExists = buckets.some(
      bucket => bucket.name === PDF_STORAGE_CONFIG.bucket
    );

    if (!bucketExists) {
      return {
        success: false,
        message: 'PDF storage bucket does not exist',
        details: {
          bucketExists: false,
          canUpload: false,
          canDownload: false,
        },
      };
    }

    // Test upload capability
    const testContent = Buffer.from('Test PDF content for storage validation');
    const testFileName = `test-${Date.now()}.pdf`;
    const testPath = `${PDF_STORAGE_CONFIG.folders.temp}/${testFileName}`;

    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from(PDF_STORAGE_CONFIG.bucket)
      .upload(testPath, testContent, {
        contentType: 'application/pdf',
        cacheControl: '3600',
      });

    const canUpload = !uploadError;

    // Test download capability
    let canDownload = false;
    if (canUpload && uploadData) {
      const { error: downloadError } = await supabaseAdmin.storage
        .from(PDF_STORAGE_CONFIG.bucket)
        .download(uploadData.path);

      canDownload = !downloadError;

      // Clean up test file
      await supabaseAdmin.storage
        .from(PDF_STORAGE_CONFIG.bucket)
        .remove([uploadData.path]);
    }

    return {
      success: bucketExists && canUpload && canDownload,
      message:
        bucketExists && canUpload && canDownload
          ? 'PDF storage setup is working correctly'
          : 'PDF storage setup has issues',
      details: {
        bucketExists,
        canUpload,
        canDownload,
      },
    };
  } catch (error) {
    console.error('Error testing PDF storage setup:', error);
    return {
      success: false,
      message: 'Failed to test PDF storage setup',
      details: {
        bucketExists: false,
        canUpload: false,
        canDownload: false,
      },
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Initialize PDF storage (create bucket and set up policies)
 */
export async function initializePDFStorage(): Promise<{
  success: boolean;
  message: string;
  steps: Array<{ step: string; success: boolean; message: string }>;
  error?: string;
}> {
  const steps: Array<{ step: string; success: boolean; message: string }> = [];

  try {
    // Step 1: Create bucket
    const bucketResult = await createPDFStorageBucket();
    steps.push({
      step: 'Create PDF storage bucket',
      success: bucketResult.success,
      message: bucketResult.message,
    });

    // Step 2: Set up policies
    const policyResult = await setupPDFStoragePolicies();
    steps.push({
      step: 'Set up storage policies',
      success: policyResult.success,
      message: policyResult.message,
    });

    // Step 3: Test setup
    const testResult = await testPDFStorageSetup();
    steps.push({
      step: 'Test storage setup',
      success: testResult.success,
      message: testResult.message,
    });

    const allSuccessful = steps.every(step => step.success);

    return {
      success: allSuccessful,
      message: allSuccessful
        ? 'PDF storage initialized successfully'
        : 'PDF storage initialization completed with some issues',
      steps,
    };
  } catch (error) {
    console.error('Error initializing PDF storage:', error);
    return {
      success: false,
      message: 'Failed to initialize PDF storage',
      steps,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get storage bucket information
 */
export async function getPDFStorageInfo(): Promise<{
  success: boolean;
  info?: {
    bucketName: string;
    bucketExists: boolean;
    folders: string[];
    maxFileSize: number;
    allowedMimeTypes: string[];
  };
  error?: string;
}> {
  try {
    const supabaseAdmin = createSupabaseAdmin();

    // Check if bucket exists
    const { data: buckets, error: listError } =
      await supabaseAdmin.storage.listBuckets();

    if (listError) {
      throw listError;
    }

    const bucket = buckets.find(b => b.name === PDF_STORAGE_CONFIG.bucket);

    return {
      success: true,
      info: {
        bucketName: PDF_STORAGE_CONFIG.bucket,
        bucketExists: !!bucket,
        folders: Object.values(PDF_STORAGE_CONFIG.folders),
        maxFileSize: PDF_STORAGE_CONFIG.maxFileSize,
        allowedMimeTypes: PDF_STORAGE_CONFIG.allowedMimeTypes,
      },
    };
  } catch (error) {
    console.error('Error getting PDF storage info:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
