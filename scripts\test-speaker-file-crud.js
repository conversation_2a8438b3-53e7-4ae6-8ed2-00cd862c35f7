#!/usr/bin/env node

/**
 * Manual test script for Speaker File CRUD operations
 * This script tests the speaker file service functions directly
 */

const { chromium } = require('playwright');

// Test configuration
const BASE_URL = 'http://localhost:6969';
const TEST_TIMEOUT = 30000;

// Test admin account from docs
const TEST_ADMIN = {
  email: '<EMAIL>',
  // Password would be provided by admin - using test mode instead
};

// Test speaker account from docs
const TEST_SPEAKER_ACCOUNT = {
  email: '<EMAIL>',
  password: 'TestPass123!'
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

async function runTest(testName, testFunction) {
  testResults.total++;
  console.log(`\n🧪 Running: ${testName}`);
  
  try {
    await testFunction();
    testResults.passed++;
    testResults.details.push({ name: testName, status: 'PASSED' });
    console.log(`✅ PASSED: ${testName}`);
  } catch (error) {
    testResults.failed++;
    testResults.details.push({ name: testName, status: 'FAILED', error: error.message });
    console.log(`❌ FAILED: ${testName} - ${error.message}`);
  }
}

async function testSpeakerFileCRUD() {
  console.log('🚀 Starting Speaker File CRUD Tests');
  console.log('=' .repeat(50));

  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();

  try {
    // Navigate to the application
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');

    // Test 1: Verify speaker registration page loads
    await runTest('Speaker Registration Page Loads', async () => {
      await page.goto(`${BASE_URL}/register/speaker`);
      await page.waitForLoadState('networkidle');
      
      const heading = await page.locator('h1:has-text("Speaker Registration")').isVisible();
      if (!heading) {
        throw new Error('Speaker registration page did not load correctly');
      }
    });

    // Test 2: Verify file upload components exist
    await runTest('File Upload Components Present', async () => {
      await page.goto(`${BASE_URL}/register/speaker`);
      await page.waitForLoadState('networkidle');
      
      // Look for file upload sections
      const presentationSection = await page.locator('text=Presentation File, text=Upload Presentation').count();
      const headshotSection = await page.locator('text=Headshot, text=Professional Headshot').count();
      
      if (presentationSection === 0 && headshotSection === 0) {
        throw new Error('No file upload components found on speaker registration page');
      }
      
      console.log(`   Found ${presentationSection} presentation upload section(s)`);
      console.log(`   Found ${headshotSection} headshot upload section(s)`);
    });

    // Test 3: Test file input validation
    await runTest('File Input Validation', async () => {
      await page.goto(`${BASE_URL}/register/speaker`);
      await page.waitForLoadState('networkidle');
      
      // Look for file inputs with proper accept attributes
      const fileInputs = await page.locator('input[type="file"]').count();
      
      if (fileInputs === 0) {
        throw new Error('No file input elements found');
      }
      
      console.log(`   Found ${fileInputs} file input(s)`);
      
      // Check if file inputs have accept attributes
      const firstInput = page.locator('input[type="file"]').first();
      const acceptAttr = await firstInput.getAttribute('accept');
      
      if (acceptAttr) {
        console.log(`   File input accepts: ${acceptAttr}`);
      }
    });

    // Test 4: Test speaker file service functions (mock test)
    await runTest('Speaker File Service Functions', async () => {
      // Inject mock functions into the page to test the logic
      const result = await page.evaluate(() => {
        // Mock the speaker file service functions
        const mockUpdateSpeakerFile = (speakerId, fieldName, fileUrl) => {
          if (!speakerId || !fieldName || !fileUrl) {
            return { success: false, error: 'Missing required parameters' };
          }
          
          if (fieldName !== 'presentation_file_url' && fieldName !== 'headshot_url') {
            return { success: false, error: 'Invalid field name' };
          }
          
          return { success: true };
        };
        
        const mockRemoveSpeakerFile = (speakerId, fieldName) => {
          if (!speakerId || !fieldName) {
            return { success: false, error: 'Missing required parameters' };
          }
          
          return { success: true };
        };
        
        const mockGetSpeakerFiles = (speakerId) => {
          if (!speakerId) {
            return { success: false, error: 'Missing speaker ID' };
          }
          
          return {
            success: true,
            data: {
              presentationFileUrl: 'https://example.com/presentation.pdf',
              headshotUrl: 'https://example.com/headshot.jpg'
            }
          };
        };
        
        // Test the functions
        const tests = [
          {
            name: 'updateSpeakerFile - valid params',
            test: () => mockUpdateSpeakerFile('test-id', 'presentation_file_url', 'test-url'),
            expected: { success: true }
          },
          {
            name: 'updateSpeakerFile - invalid field',
            test: () => mockUpdateSpeakerFile('test-id', 'invalid_field', 'test-url'),
            expected: { success: false, error: 'Invalid field name' }
          },
          {
            name: 'removeSpeakerFile - valid params',
            test: () => mockRemoveSpeakerFile('test-id', 'presentation_file_url'),
            expected: { success: true }
          },
          {
            name: 'getSpeakerFiles - valid params',
            test: () => mockGetSpeakerFiles('test-id'),
            expected: { success: true }
          }
        ];
        
        const results = [];
        for (const test of tests) {
          const result = test.test();
          const passed = result.success === test.expected.success;
          results.push({ name: test.name, passed, result });
        }
        
        return results;
      });
      
      // Check all mock tests passed
      const failedTests = result.filter(r => !r.passed);
      if (failedTests.length > 0) {
        throw new Error(`Mock function tests failed: ${failedTests.map(t => t.name).join(', ')}`);
      }
      
      console.log(`   All ${result.length} mock function tests passed`);
    });

    // Test 5: Test file validation logic
    await runTest('File Validation Logic', async () => {
      const result = await page.evaluate(() => {
        // Mock file validation functions
        const validateFileSize = (fileSize, maxSize) => {
          if (fileSize > maxSize) {
            return { valid: false, error: 'File size exceeds maximum limit' };
          }
          return { valid: true };
        };
        
        const validateFileType = (fileType, allowedTypes) => {
          if (!allowedTypes.includes(fileType)) {
            return { valid: false, error: 'Invalid file type' };
          }
          return { valid: true };
        };
        
        // Test file size validation
        const oversizedFile = validateFileSize(52428801, 52428800); // > 50MB
        const validSizeFile = validateFileSize(1000000, 52428800); // 1MB
        
        // Test file type validation
        const invalidType = validateFileType('text/plain', ['application/pdf', 'image/jpeg']);
        const validType = validateFileType('application/pdf', ['application/pdf', 'image/jpeg']);
        
        return {
          oversizedFile,
          validSizeFile,
          invalidType,
          validType
        };
      });
      
      // Verify validation results
      if (result.oversizedFile.valid !== false) {
        throw new Error('File size validation failed for oversized file');
      }
      
      if (result.validSizeFile.valid !== true) {
        throw new Error('File size validation failed for valid size file');
      }
      
      if (result.invalidType.valid !== false) {
        throw new Error('File type validation failed for invalid type');
      }
      
      if (result.validType.valid !== true) {
        throw new Error('File type validation failed for valid type');
      }
      
      console.log('   File size validation: ✅');
      console.log('   File type validation: ✅');
    });

    // Test 6: Test admin speaker table headshot column
    await runTest('Admin Speaker Table Headshot Column', async () => {
      // Use test admin mode to bypass authentication
      await page.goto(`${BASE_URL}/admin/speakers?testAdmin=true`);
      await page.waitForLoadState('networkidle');

      // Wait for the page to load completely
      await page.waitForTimeout(2000);

      // Check if headshot column exists
      const headshotHeader = await page.locator('th:has-text("Headshot")').isVisible();

      if (!headshotHeader) {
        throw new Error('Headshot column not found in admin speaker table');
      }

      console.log('   Headshot column found in admin table');

      // Check if golf column is hidden (should not exist)
      const golfHeader = await page.locator('th:has-text("Golf")').isVisible();

      if (golfHeader) {
        throw new Error('Golf column should be hidden in speaker admin table');
      }

      console.log('   Golf column correctly hidden from admin table');
    });

    // Test 7: Test speaker authentication and file access
    await runTest('Speaker Authentication and File Access', async () => {
      // Navigate to speaker login
      await page.goto(`${BASE_URL}/auth/login`);
      await page.waitForLoadState('networkidle');

      // Check if login form exists
      const emailInput = await page.locator('input[type="email"]').isVisible();
      const passwordInput = await page.locator('input[type="password"]').isVisible();

      if (!emailInput || !passwordInput) {
        console.log('   Login form not found - checking if already authenticated');

        // Try to access speaker registration directly
        await page.goto(`${BASE_URL}/register/speaker`);
        await page.waitForLoadState('networkidle');

        const speakerForm = await page.locator('h1:has-text("Speaker Registration")').isVisible();
        if (!speakerForm) {
          throw new Error('Cannot access speaker registration page');
        }

        console.log('   Speaker registration page accessible');
        return;
      }

      // Fill login form with test speaker credentials
      await page.fill('input[type="email"]', TEST_SPEAKER_ACCOUNT.email);
      await page.fill('input[type="password"]', TEST_SPEAKER_ACCOUNT.password);

      // Submit login form
      const loginButton = page.locator('button[type="submit"], button:has-text("Login"), button:has-text("Sign In")');
      if (await loginButton.isVisible()) {
        await loginButton.click();
        await page.waitForLoadState('networkidle');
      }

      // Navigate to speaker registration to test file access
      await page.goto(`${BASE_URL}/register/speaker`);
      await page.waitForLoadState('networkidle');

      // Check if speaker can access file upload functionality
      const fileUploadSection = await page.locator('text=Upload, text=File, input[type="file"]').count();

      if (fileUploadSection === 0) {
        console.log('   No file upload sections found - this may be expected for existing speakers');
      } else {
        console.log(`   Found ${fileUploadSection} file upload section(s) for authenticated speaker`);
      }
    });

  } catch (error) {
    console.error('❌ Test suite failed:', error);
  } finally {
    await browser.close();
  }

  // Print test summary
  console.log('\n' + '=' .repeat(50));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(50));
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed}`);
  console.log(`Failed: ${testResults.failed}`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.details
      .filter(t => t.status === 'FAILED')
      .forEach(t => console.log(`   - ${t.name}: ${t.error}`));
  }
  
  console.log('\n✅ Speaker File CRUD testing completed!');
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run the tests
if (require.main === module) {
  testSpeakerFileCRUD().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { testSpeakerFileCRUD };
