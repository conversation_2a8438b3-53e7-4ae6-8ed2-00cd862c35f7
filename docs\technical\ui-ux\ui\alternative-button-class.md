# Alternative Button Class Implementation

## Overview

This document tracks the creation of a reusable alternative button class (`iepa-btn-alt`) for the IEPA conference application, providing a white background button with IEPA blue text as an alternative to the standard primary and secondary button styles.

## Status: ✅ Completed

## Implementation Details

### 1. CSS Class Creation

- **File**: `src/styles/iepa-brand.css`
- **Class**: `.iepa-btn-alt`
- **Purpose**: Reusable alternative button style with white background

### 2. But<PERSON> Styling

```css
/* Alternative Button - White background with IEPA blue text */
.iepa-btn-alt {
  background-color: var(--iepa-text-white);
  border: 2px solid var(--iepa-text-white);
  color: var(--iepa-primary-blue);
  font-weight: 600;
  transition: all 0.2s ease;
}

.iepa-btn-alt:hover {
  background-color: var(--iepa-primary-blue);
  border-color: var(--iepa-primary-blue);
  color: var(--iepa-text-white);
}
```

### 3. Design Specifications

**Default State:**

- Background: White (`var(--iepa-text-white)`)
- Border: 2px solid white
- Text Color: IEPA Blue (`var(--iepa-primary-blue)`)
- Font Weight: 600 (semi-bold)
- Transition: All properties with 0.2s ease

**Hover State:**

- Background: IEPA Blue (`var(--iepa-primary-blue)`)
- Border: 2px solid IEPA blue
- Text Color: White (`var(--iepa-text-white)`)
- Smooth transition effect

## Usage Examples

### 1. Hero Section Implementation

```jsx
<Button
  as={Link}
  href="/about"
  size="lg"
  className="w-full sm:w-auto iepa-btn-alt"
>
  Learn More
</Button>
```

### 2. General Usage Pattern

```jsx
// Basic usage
<Button className="iepa-btn-alt">
  Button Text
</Button>

// With additional classes
<Button className="iepa-btn-alt w-full">
  Full Width Button
</Button>

// With Link component
<Button as={Link} href="/path" className="iepa-btn-alt">
  Link Button
</Button>
```

## Button Hierarchy

The IEPA application now has three main button styles:

1. **Primary Button** (`.iepa-btn-primary`)

   - IEPA Blue background
   - White text
   - Main call-to-action buttons

2. **Secondary Button** (`.iepa-btn-secondary`)

   - IEPA Green background
   - White text
   - Secondary actions

3. **Alternative Button** (`.iepa-btn-alt`) ✨ **NEW**
   - White background
   - IEPA Blue text
   - Alternative actions, "Learn More" style buttons

## Accessibility Features

- **High Contrast**: White background with IEPA blue text provides excellent readability
- **Focus States**: Inherits NextUI focus indicators
- **Hover Feedback**: Clear visual feedback with color inversion
- **Keyboard Navigation**: Fully accessible via keyboard
- **Screen Reader**: Compatible with screen reader technology

## Testing Results

- [x] Button displays with white background and IEPA blue text
- [x] Hover state works correctly (blue background, white text)
- [x] Responsive design works on mobile devices
- [x] Maintains accessibility standards
- [x] Integrates seamlessly with NextUI Button component
- [x] No conflicts with existing button styles

## Use Cases

This alternative button class is ideal for:

- **Secondary actions** that need more prominence than bordered buttons
- **"Learn More"** style buttons in hero sections
- **Alternative CTAs** that complement primary actions
- **Buttons on dark backgrounds** where white provides good contrast
- **Navigation buttons** that need to stand out without being primary

## Files Modified

- `src/styles/iepa-brand.css` (added `.iepa-btn-alt` class)
- `src/app/page.tsx` (updated "Learn More" button to use new class)
- `.docs/ui/alternative-button-class.md` (this documentation)

## Development Notes

- **Reusable**: Can be applied to any NextUI Button component
- **Consistent**: Uses IEPA brand colors and design tokens
- **Flexible**: Works with all NextUI Button props and variants
- **Maintainable**: Centralized styling in brand CSS file

## Future Enhancements

Potential future additions:

- Alternative button with different color schemes
- Disabled state styling
- Loading state styling
- Size-specific variations

## Development Server Status

- Server running successfully
- No build errors or console errors
- Button styling displays correctly across all screen sizes
- Ready for use throughout the application
