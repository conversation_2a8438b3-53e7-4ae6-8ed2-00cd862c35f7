// Breadcrumb Configuration for IEPA 2025 Conference Registration
// Defines route mappings, labels, and icons for automatic breadcrumb generation

export interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: string;
  isCurrentPage?: boolean;
}

export interface RouteConfig {
  label: string;
  icon?: string;
  parent?: string;
}

// Route configuration mapping
export const ROUTE_CONFIG: Record<string, RouteConfig> = {
  '/': {
    label: 'Home',
    icon: '🏠',
  },
  '/about': {
    label: 'Annual Meeting Info',
    icon: 'ℹ️',
    parent: '/',
  },
  '/contact': {
    label: 'Contact',
    icon: '📧',
    parent: '/',
  },
  '/register': {
    label: 'Registration',
    icon: '📝',
    parent: '/',
  },
  '/register/attendee': {
    label: 'Attendee Registration',
    icon: '👤',
    parent: '/register',
  },
  '/register/speaker': {
    label: 'Speaker Registration',
    icon: '🎤',
    parent: '/register',
  },
  '/register/sponsor': {
    label: 'Sponsor Registration',
    icon: '🤝',
    parent: '/register',
  },
  '/register/sponsor-attendee': {
    label: 'Sponsor Attendee Registration',
    icon: '⭐',
    parent: '/register',
  },
  '/admin': {
    label: 'Admin Dashboard',
    icon: '📊',
    parent: '/',
  },
  '/admin/attendees': {
    label: 'Attendees',
    icon: '👥',
    parent: '/admin',
  },
  '/admin/speakers': {
    label: 'Speakers',
    icon: '🎤',
    parent: '/admin',
  },
  '/admin/sponsors': {
    label: 'Sponsors',
    icon: '⭐',
    parent: '/admin',
  },
  '/admin/sponsor-relationships': {
    label: 'Sponsor Relationships',
    icon: '🔗',
    parent: '/admin',
  },
  '/admin/payments': {
    label: 'Payments',
    icon: '💳',
    parent: '/admin',
  },
  '/admin/invoices': {
    label: 'Invoices',
    icon: '📄',
    parent: '/admin',
  },
  '/admin/emails': {
    label: 'Email Center',
    icon: '📧',
    parent: '/admin',
  },
  '/admin/email-templates': {
    label: 'Email Templates',
    icon: '📧',
    parent: '/admin',
  },
  '/admin/reports': {
    label: 'Reports',
    icon: '📊',
    parent: '/admin',
  },
  '/admin/discount-codes': {
    label: 'Discount Codes',
    icon: '🏷️',
    parent: '/admin',
  },
  '/admin/export': {
    label: 'Data Export',
    icon: '📤',
    parent: '/admin',
  },
  '/admin/database': {
    label: 'Database',
    icon: '🗄️',
    parent: '/admin',
  },
  '/admin/audit': {
    label: 'Audit Log',
    icon: '📋',
    parent: '/admin',
  },
  '/admin/users': {
    label: 'Admin Users',
    icon: '👤',
    parent: '/admin',
  },
  '/admin/settings': {
    label: 'Settings',
    icon: '⚙️',
    parent: '/admin',
  },
  '/dashboard': {
    label: 'Dashboard',
    icon: '📊',
    parent: '/',
  },
  '/my-registrations': {
    label: 'My Registrations',
    icon: '📋',
    parent: '/',
  },
  '/settings': {
    label: 'Settings',
    icon: '⚙️',
    parent: '/',
  },
  '/auth/login': {
    label: 'Sign In',
    icon: '🔐',
    parent: '/',
  },
  '/auth/signup': {
    label: 'Create Account',
    icon: '✨',
    parent: '/',
  },
  '/auth/forgot-password': {
    label: 'Reset Password',
    icon: '🔑',
    parent: '/auth/login',
  },
  '/privacy': {
    label: 'Privacy Policy',
    icon: '🔒',
    parent: '/',
  },
  '/terms': {
    label: 'Terms of Service',
    icon: '📄',
    parent: '/',
  },
};

/**
 * Generates breadcrumb items for a given pathname
 * @param pathname - Current page pathname
 * @returns Array of breadcrumb items
 */
export function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [];

  // Build breadcrumb chain by following parent relationships
  let currentPath = pathname;
  const visited = new Set<string>();

  while (currentPath && !visited.has(currentPath)) {
    visited.add(currentPath);
    const config = ROUTE_CONFIG[currentPath];

    if (config) {
      breadcrumbs.unshift({
        label: config.label,
        href: currentPath,
        icon: config.icon,
        isCurrentPage: currentPath === pathname,
      });

      currentPath = config.parent || '';
    } else {
      // Handle dynamic routes or unknown paths
      break;
    }
  }

  // Ensure home is always included if not already present
  if (breadcrumbs.length > 0 && breadcrumbs[0].href !== '/') {
    breadcrumbs.unshift({
      label: 'Home',
      href: '/',
      icon: '🏠',
      isCurrentPage: false,
    });
  }

  return breadcrumbs;
}

/**
 * Gets the page title for a given pathname
 * @param pathname - Current page pathname
 * @returns Page title string
 */
export function getPageTitle(pathname: string): string {
  const config = ROUTE_CONFIG[pathname];
  return config?.label || 'Page';
}

/**
 * Checks if breadcrumbs should be shown for a given pathname
 * @param pathname - Current page pathname
 * @returns Boolean indicating if breadcrumbs should be displayed
 */
export function shouldShowBreadcrumbs(pathname: string): boolean {
  // Don't show breadcrumbs on home page
  if (pathname === '/') {
    return false;
  }

  // Don't show breadcrumbs on auth pages (they have their own flow)
  if (pathname.startsWith('/auth/')) {
    return false;
  }

  // Don't show breadcrumbs on admin pages (they have their own navigation)
  if (pathname.startsWith('/admin')) {
    return false;
  }

  // Don't show breadcrumbs on component demo page
  if (pathname === '/components-demo') {
    return false;
  }

  return true;
}
