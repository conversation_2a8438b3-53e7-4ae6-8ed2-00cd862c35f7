<!DOCTYPE html>
<html>
<head>
    <title>Test PDF Download</title>
</head>
<body>
    <h1>Test PDF Download</h1>
    
    <h2>Method 1: Direct Link</h2>
    <a href="http://localhost:3000/api/stripe/download-direct?registrationId=c3a0ffdf-8dcd-4814-9066-d11993a34db8&registrationType=attendee&documentType=invoice&testMode=true" 
       download="test-invoice.pdf" 
       target="_blank">
        Download PDF (Direct Link)
    </a>
    
    <h2>Method 2: JavaScript Blob</h2>
    <button onclick="downloadPDF()">Download PDF (JavaScript)</button>
    
    <script>
        async function downloadPDF() {
            try {
                console.log('Starting PDF download...');
                
                const response = await fetch('http://localhost:3000/api/stripe/download-direct?registrationId=c3a0ffdf-8dcd-4814-9066-d11993a34db8&registrationType=attendee&documentType=invoice&testMode=true');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                console.log('Response received, creating blob...');
                const blob = await response.blob();
                
                console.log('Blob created, size:', blob.size);
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'test-invoice.pdf';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                console.log('Download triggered successfully');
            } catch (error) {
                console.error('Download failed:', error);
                alert('Download failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
