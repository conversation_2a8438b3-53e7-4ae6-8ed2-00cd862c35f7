import { NextRequest, NextResponse } from 'next/server';
import {
  getDynamicAppUrl,
  getAuthRedirectUrl,
  getProductionAppUrl,
} from '@/lib/port-utils';

export async function GET(request: NextRequest) {
  try {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        VERCEL_URL: process.env.VERCEL_URL,
        NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
        NEXT_PUBLIC_PRODUCTION_URL: process.env.NEXT_PUBLIC_PRODUCTION_URL,
      },
      urls: {
        getDynamicAppUrl: getDynamicAppUrl(),
        getProductionAppUrl: getProductionAppUrl(),
        getAuthRedirectUrl_resetPassword: getAuthRedirectUrl(
          '/auth/reset-password'
        ),
        getAuthRedirectUrl_callback: getAuthRedirectUrl('/auth/callback'),
      },
      supabase: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      },
      request: {
        url: request.url,
        headers: {
          host: request.headers.get('host'),
          'x-forwarded-host': request.headers.get('x-forwarded-host'),
          'x-forwarded-proto': request.headers.get('x-forwarded-proto'),
        },
      },
    };

    return NextResponse.json(debugInfo, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to generate debug info',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
