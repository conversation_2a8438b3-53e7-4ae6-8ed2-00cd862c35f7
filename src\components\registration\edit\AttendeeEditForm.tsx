'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
  Label,
  Checkbox,
} from '@/components/ui';
import { PhoneInput } from '@/components/ui/phone-input';
import { STATE_PROVINCE_OPTIONS } from '@/lib/address-constants';
import { mealOptions } from '@/lib/meal-config';
import type { AttendeeRegistration } from '@/types/database';
import { FiSave, FiUser, FiMapPin, FiCalendar } from 'react-icons/fi';

interface AttendeeEditFormProps {
  registration: AttendeeRegistration;
  onSave: (data: Partial<AttendeeRegistration>) => Promise<void>;
  saving: boolean;
  error: string | null;
}

export function AttendeeEditForm({
  registration,
  onSave,
  saving,
  error,
}: AttendeeEditFormProps) {
  const [formData, setFormData] = useState({
    first_name: registration.first_name || '',
    last_name: registration.last_name || '',
    email: registration.email || '',
    phone_number: registration.phone_number || '',
    organization: registration.organization || '',
    job_title: registration.job_title || '',
    street_address: registration.street_address || '',
    city: registration.city || '',
    state: registration.state || '',
    zip_code: registration.zip_code || '',
    meals: registration.meals || [],
    dietary_needs: registration.dietary_needs || '',
    attending_golf: registration.attending_golf || false,
    golf_club_rental: registration.golf_club_rental || false,
    golf_club_handedness: registration.golf_club_handedness || '',
  });

  const handleInputChange = (
    field: string,
    value: string | boolean | string[]
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleMealChange = (mealId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      meals: checked
        ? [...(prev.meals || []), mealId]
        : (prev.meals || []).filter(id => id !== mealId),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Exclude golf-related fields from being saved as they cannot be changed
    const { attending_golf, golf_club_rental, golf_club_handedness, ...saveData } = formData;

    await onSave(saveData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody>
            <p className="text-red-600 text-sm">{error}</p>
          </CardBody>
        </Card>
      )}

      {/* Personal Information */}
      <Card>
        <CardHeader>
          <h2 className="iepa-heading-2 flex items-center gap-2">
            <FiUser className="w-5 h-5" />
            Personal Information
          </h2>
        </CardHeader>
        <CardBody>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="first_name">First Name *</Label>
              <Input
                id="first_name"
                value={formData.first_name}
                onChange={e => handleInputChange('first_name', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="last_name">Last Name *</Label>
              <Input
                id="last_name"
                value={formData.last_name}
                onChange={e => handleInputChange('last_name', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={e => handleInputChange('email', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="phone_number">Phone Number</Label>
              <PhoneInput
                value={formData.phone_number}
                onChange={value => handleInputChange('phone_number', value)}
              />
            </div>
            <div>
              <Label htmlFor="organization">Organization</Label>
              <Input
                id="organization"
                value={formData.organization}
                onChange={e =>
                  handleInputChange('organization', e.target.value)
                }
              />
            </div>
            <div>
              <Label htmlFor="job_title">Job Title</Label>
              <Input
                id="job_title"
                value={formData.job_title}
                onChange={e => handleInputChange('job_title', e.target.value)}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Address Information */}
      <Card>
        <CardHeader>
          <h2 className="iepa-heading-2 flex items-center gap-2">
            <FiMapPin className="w-5 h-5" />
            Address Information
          </h2>
        </CardHeader>
        <CardBody>
          <div className="grid gap-4">
            <div>
              <Label htmlFor="street_address">Street Address</Label>
              <Input
                id="street_address"
                value={formData.street_address}
                onChange={e =>
                  handleInputChange('street_address', e.target.value)
                }
              />
            </div>
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={e => handleInputChange('city', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="state">State</Label>
                <Select
                  value={formData.state}
                  onValueChange={value => handleInputChange('state', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select state" />
                  </SelectTrigger>
                  <SelectContent>
                    {STATE_PROVINCE_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="zip_code">ZIP Code</Label>
                <Input
                  id="zip_code"
                  value={formData.zip_code}
                  onChange={e => handleInputChange('zip_code', e.target.value)}
                />
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Conference Options */}
      <Card>
        <CardHeader>
          <h2 className="iepa-heading-2 flex items-center gap-2">
            <FiCalendar className="w-5 h-5" />
            Conference Options
          </h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-6">
            {/* Meals */}
            <div>
              <h3 className="iepa-heading-3 mb-4">
                Meals (Free for Attendees)
              </h3>
              <div className="space-y-4">
                {mealOptions.map(dayOption => (
                  <div
                    key={dayOption.day}
                    className="border border-gray-200 rounded-lg p-3"
                  >
                    <h4 className="font-semibold text-base mb-2 text-[var(--iepa-primary-blue)]">
                      {dayOption.displayDate}
                    </h4>
                    <div className="space-y-2">
                      {dayOption.meals.map(meal => (
                        <div
                          key={meal.key}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={meal.key}
                            checked={(formData.meals || []).includes(meal.key)}
                            onCheckedChange={checked =>
                              handleMealChange(meal.key, checked as boolean)
                            }
                          />
                          <Label htmlFor={meal.key}>
                            {meal.name} - {meal.time}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Dietary Needs */}
            <div>
              <Label htmlFor="dietary_needs">
                Dietary Restrictions/Allergies
              </Label>
              <Textarea
                id="dietary_needs"
                value={formData.dietary_needs}
                onChange={e =>
                  handleInputChange('dietary_needs', e.target.value)
                }
                placeholder="Please describe any dietary restrictions or allergies..."
                rows={3}
              />
            </div>

            {/* Golf */}
            <div>
              <h3 className="iepa-heading-3 mb-4">Golf Tournament</h3>
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="mb-3">
                  <p className="text-sm text-gray-600 mb-3">
                    <strong>Note:</strong> Golf tournament selections cannot be changed after registration.
                    To modify your golf options, please contact support.
                  </p>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attending_golf"
                      checked={formData.attending_golf}
                      disabled={true}
                    />
                    <Label htmlFor="attending_golf" className="text-gray-600">
                      Attending Golf Tournament ($200)
                      {formData.attending_golf ? ' ✓ Selected' : ' ✗ Not Selected'}
                    </Label>
                  </div>
                  {formData.attending_golf && (
                    <>
                      <div className="flex items-center space-x-2 ml-6">
                        <Checkbox
                          id="golf_club_rental"
                          checked={formData.golf_club_rental}
                          disabled={true}
                        />
                        <Label htmlFor="golf_club_rental" className="text-gray-600">
                          Golf Club Rental ($75)
                          {formData.golf_club_rental ? ' ✓ Selected' : ' ✗ Not Selected'}
                        </Label>
                      </div>
                      {formData.golf_club_rental && (
                        <div className="ml-6">
                          <Label htmlFor="golf_club_handedness" className="text-gray-600">
                            Club Handedness
                          </Label>
                          <Select
                            value={formData.golf_club_handedness}
                            disabled={true}
                          >
                            <SelectTrigger className="bg-gray-100">
                              <SelectValue placeholder="Select handedness" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="right">Right-handed</SelectItem>
                              <SelectItem value="left">Left-handed</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={saving}
          className="flex items-center gap-2"
        >
          <FiSave className="w-4 h-4" />
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </form>
  );
}
