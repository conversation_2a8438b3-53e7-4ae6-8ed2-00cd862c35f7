# Fix for Password Reset Emails Not Arriving

## Problem

- Magic link emails are working ✅
- Welcome emails are working ✅
- Password reset emails are NOT working ❌

## Root Cause

The production Supabase project needs SMTP configuration for password reset emails. Magic links work because they use a different email delivery mechanism, but password reset emails require proper SMTP setup.

## Solution

### Step 1: ✅ SMTP Configuration (Already Done)

SMTP is already properly configured in the Supabase dashboard with SendGrid.

### Step 2: 🔧 Fix Site URL Configuration (CRITICAL)

**This is the most likely cause of the issue!**

1. **Go to Supabase Dashboard:**

   - Visit: https://supabase.com/dashboard/project/uffhyhpcuedjsisczocy
   - Navigate to: **Authentication** → **URL Configuration**

2. **Update Site URL:**

   - Set **Site URL** to: `https://reg.iepa.com`
   - This is critical - if it's set to localhost or wrong domain, emails won't work

3. **Update Redirect URLs:**
   Add these exact URLs to the **Redirect URLs** list:
   - `https://reg.iepa.com/auth/callback`
   - `https://reg.iepa.com/auth/reset-password`
   - `https://reg.iepa.com/auth/confirm`
   - `https://reg.iepa.com/**` (wildcard for all auth routes)

### Step 3: Test Email Templates (Optional)

1. **Customize Password Reset Email Template:**
   - In Supabase Dashboard: **Authentication** → **Email Templates**
   - Select "Reset Password" template
   - Customize subject and content to match IEPA branding

### Step 3: Test URL Generation (Optional Debug)

1. **Check URL Generation:**
   - Visit: https://reg.iepa.com/api/debug/auth-urls
   - Verify that `getAuthRedirectUrl_resetPassword` shows: `https://reg.iepa.com/auth/reset-password`
   - If it shows localhost or wrong domain, there's a code issue

### Step 4: Test the Fix

1. **Test Password Reset Flow:**
   - Go to: https://reg.iepa.com/auth/forgot-password
   - Enter a valid email address (e.g., your email)
   - Check email delivery within 1-2 minutes
   - Verify reset link works correctly

## Why Magic Links Work But Password Reset Doesn't

- **Magic Links (`signInWithOtp`)**: Use Supabase's basic email delivery service
- **Password Reset (`resetPasswordForEmail`)**: Require proper SMTP configuration
- **Welcome Emails**: Use your custom SendGrid integration (separate from Supabase auth)

## Expected Result

After configuring SMTP in the production Supabase dashboard:

- Password reset emails should arrive within 1-2 minutes
- Email should come from "<EMAIL>"
- Reset links should redirect properly to https://reg.iepa.com/auth/reset-password

## Verification Commands

After making changes, test with:

```bash
# Test password reset API directly
curl -X POST 'https://uffhyhpcuedjsisczocy.supabase.co/auth/v1/recover' \
  -H 'Content-Type: application/json' \
  -H 'apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVmZmh5aHBjdWVkanNpc2N6b2N5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjQ5MTcsImV4cCI6MjA2NDE0MDkxN30.Q4XOwkhVA8_YhKY-lM_wKY_hMyoc_I5TXL1xCehbEAY' \
  -d '{"email": "<EMAIL>"}'
```

## Notes

- The SMTP configuration only needs to be done once in the Supabase dashboard
- This affects the production environment immediately
- Local development will continue using the local Supabase instance (when switched back)
- No code changes are required - this is purely a Supabase configuration issue
