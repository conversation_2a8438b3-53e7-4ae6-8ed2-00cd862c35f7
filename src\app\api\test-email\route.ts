import { NextRequest, NextResponse } from 'next/server';
import { emailService } from '@/services/email';

export async function POST(request: NextRequest) {
  try {
    console.log('[EMAIL-TEST] Starting email test...');

    // Get configuration to verify setup
    const config = await emailService.getConfig();
    console.log('[EMAIL-TEST] Email service config:', config);

    if (!config.isConfigured) {
      return NextResponse.json({
        success: false,
        error: 'SendGrid not properly configured',
        config: config
      }, { status: 500 });
    }

    const body = await request.json();
    const { testEmail, testType = 'basic' } = body;

    if (!testEmail) {
      return NextResponse.json({
        success: false,
        error: 'testEmail is required'
      }, { status: 400 });
    }

    console.log('[EMAIL-TEST] Sending test email to:', testEmail);

    let result = false;

    switch (testType) {
      case 'registration':
        result = await emailService.sendRegistrationConfirmation(
          testEmail,
          'Test User',
          {
            type: 'attendee',
            confirmationNumber: 'TEST-123456',
            eventDate: 'March 15-17, 2025',
            location: 'Convention Center'
          }
        );
        break;

      case 'payment':
        result = await emailService.sendPaymentConfirmation(
          testEmail,
          'Test User',
          {
            amount: 299.00,
            paymentId: 'pi_test_123456',
            registrationType: 'Attendee Registration',
            receiptUrl: 'https://example.com/receipt'
          }
        );
        break;

      case 'password-reset':
        result = await emailService.sendPasswordReset(
          testEmail,
          'https://iepa.com/auth/reset-password?token=test123'
        );
        break;

      default: // basic
        result = await emailService.sendEmail({
          to: testEmail,
          subject: 'Test Email from IEPA Conference System',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2563eb;">Email Test Successful! 🎉</h2>
              <p>This is a test email to verify your SendGrid integration is working properly.</p>
              <div style="background: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p><strong>Configuration Status:</strong> ✅ Working</p>
                <p><strong>Test Type:</strong> Basic Email</p>
                <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
              </div>
              <p>You can now use email functionality in your IEPA Conference application!</p>
            </div>
          `,
          text: 'Email Test Successful! This is a test email to verify your SendGrid integration is working properly.'
        });
        break;
    }

    console.log('[EMAIL-TEST] Email send result:', result);

    return NextResponse.json({
      success: result,
      message: result ? 'Test email sent successfully!' : 'Failed to send test email',
      testType,
      sentTo: testEmail,
      config: {
        fromEmail: config.fromEmail,
        fromName: config.fromName,
        isConfigured: config.isConfigured
      }
    });

  } catch (error: any) {
    console.error('[EMAIL-TEST] Test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Email test failed',
      details: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  // Simple health check for email service
  const config = await emailService.getConfig();

  return NextResponse.json({
    status: 'ready',
    configured: config.isConfigured,
    config: {
      fromEmail: config.fromEmail,
      fromName: config.fromName,
      supportEmail: config.supportEmail,
    }
  });
}