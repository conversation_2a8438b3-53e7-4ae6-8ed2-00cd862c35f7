import { NextRequest, NextResponse } from 'next/server';
import { emailTemplateService } from '@/services/email-templates';
import { emailService } from '@/services/email';
import { previewTemplate } from '@/utils/template-renderer';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log(`[EMAIL-TEMPLATES-API] Sending test email for template: ${id}`);

    const body = await request.json();
    const { testEmail, sampleData, adminEmail } = body;

    // Validate required fields
    if (!testEmail || !adminEmail) {
      return NextResponse.json({
        success: false,
        error: 'Test email address and admin email are required'
      }, { status: 400 });
    }

    // Get the template
    const template = await emailTemplateService.getTemplateById(id);
    
    if (!template) {
      return NextResponse.json({
        success: false,
        error: 'Template not found'
      }, { status: 404 });
    }

    if (!template.is_active) {
      return NextResponse.json({
        success: false,
        error: 'Cannot send test email for inactive template'
      }, { status: 400 });
    }

    // Prepare sample data with defaults
    const defaultSampleData = {
      name: 'John Doe',
      email: testEmail,
      confirmationNumber: 'TEST-12345',
      registrationDate: new Date().toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }),
      organizationName: 'Sample Organization',
      amount: '$1,200.00',
      paymentDate: new Date().toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }),
      eventDates: 'March 15-17, 2025',
      venueInfo: 'Monterey Plaza Hotel & Spa, Monterey, CA',
      contactEmail: '<EMAIL>',
      // Sponsor-specific defaults
      sponsorshipLevel: 'Gold',
      sponsorshipBenefits: '<ul><li>Premium booth location</li><li>Logo on annual meeting materials</li><li>Speaking opportunity</li></ul>',
      sponsorshipBenefitsText: '- Premium booth location\n- Logo on annual meeting materials\n- Speaking opportunity',
      // Speaker-specific defaults
      presentationTitle: 'The Future of Renewable Energy',
      sessionType: 'Keynote Presentation',
      sessionDate: 'March 16, 2025 at 9:00 AM',
      sessionDuration: '45 minutes',
      roomAssignment: 'Main Ballroom',
      speakerFee: '$2,500.00',
      accommodationInfo: 'Two nights at conference hotel included',
      travelInfo: 'Flight arrangements will be coordinated separately',
      avRequirements: 'Wireless microphone, projector, laser pointer',
      speakerBio: 'Industry expert with 15+ years of experience',
      // General defaults
      resetUrl: 'https://example.com/reset-password',
      isGolf: false,
      ...sampleData
    };

    // Render the template
    const subject = previewTemplate(template.subject_template, defaultSampleData);
    const html = previewTemplate(template.html_template, defaultSampleData);
    const text = template.text_template ? previewTemplate(template.text_template, defaultSampleData) : undefined;

    // Add test email prefix to subject
    const testSubject = `[TEST EMAIL] ${subject}`;

    // Add test email notice to HTML content
    const testHtml = `
      <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
        <p style="margin: 0; color: #856404; font-weight: bold;">
          🧪 This is a test email sent from the IEPA Admin Panel
        </p>
        <p style="margin: 5px 0 0 0; color: #856404; font-size: 14px;">
          Template: ${template.template_name} (${template.template_key})<br>
          Sent by: ${adminEmail}<br>
          Sent at: ${new Date().toLocaleString()}
        </p>
      </div>
      ${html}
    `;

    // Add test email notice to text content
    const testText = text ? `
[TEST EMAIL] - This is a test email sent from the IEPA Admin Panel
Template: ${template.template_name} (${template.template_key})
Sent by: ${adminEmail}
Sent at: ${new Date().toLocaleString()}

${text}
    `.trim() : undefined;

    // Send the test email
    const emailSent = await emailService.sendEmail({
      to: testEmail,
      subject: testSubject,
      html: testHtml,
      text: testText,
    }, {
      emailType: 'test_email',
      registrationType: undefined,
      userId: undefined,
      registrationId: undefined,
      paymentId: undefined
    });

    if (!emailSent) {
      return NextResponse.json({
        success: false,
        error: 'Failed to send test email'
      }, { status: 500 });
    }

    console.log(`[EMAIL-TEMPLATES-API] Test email sent successfully to: ${testEmail}`);

    return NextResponse.json({
      success: true,
      message: `Test email sent successfully to ${testEmail}`,
      template: {
        id: template.id,
        template_key: template.template_key,
        template_name: template.template_name
      },
      testDetails: {
        recipient: testEmail,
        subject: testSubject,
        sentBy: adminEmail,
        sentAt: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('[EMAIL-TEMPLATES-API] Failed to send test email:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send test email',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
