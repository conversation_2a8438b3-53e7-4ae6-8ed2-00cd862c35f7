'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ResponsiveTable,
  ResponsiveTableBody,
  ResponsiveTableCell,
  ResponsiveTableHead,
  ResponsiveTableHeader,
  ResponsiveTableRow,
  ActionButtons,
} from '@/components/ui/responsive-table';
import { Badge } from '@/components/ui/badge';
import {
  FiRefreshCw,
  FiDownload,
  FiEye,
  FiEdit,
  FiTrash2,
  FiMic,
  FiUser,
  // FiFile, // Removed - simplified presentation display
  // FiExternalLink, // Removed - simplified presentation display
  FiUsers,
  FiCalendar,
} from 'react-icons/fi';
import { formatDate } from '@/lib/pdf-generation/utils';
// import Link from 'next/link'; // Removed - using window.open in ActionButtons
// Note: useFileUrl is now used within HeadshotThumbnail component
import { SpeakerDetailsModal } from '@/components/admin/modals/SpeakerDetailsModal';
import { HeadshotThumbnail } from '@/components/admin/HeadshotThumbnail';

interface SpeakerRegistration {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  phone_number?: string;
  organization_name: string;
  job_title: string;
  bio: string;
  presentation_title?: string;
  presentation_description?: string;
  presentation_file_url?: string;
  headshot_url?: string;
  speaker_pricing_type: string;
  attendee_registration_id?: string;
  // Attendee information (joined from attendee registration)
  attendee_data?: {
    id: string;
    name_on_badge: string;
    gender: string;
    street_address: string;
    city: string;
    state: string;
    zip_code: string;
    attending_golf: boolean;
    golf_club_rental: boolean;
    golf_club_handedness: string;
    meals: string[];
    dietary_needs: string;
    registration_total: number;
    golf_total: number;
    golf_club_rental_total: number;
    grand_total: number;
    payment_status: string;
  };
  created_at: string;
  updated_at: string;
}

interface SpeakerFilters {
  search: string;
  paymentStatus: string;
  golfParticipation: string;
}

export default function SpeakersPage() {
  const searchParams = useSearchParams();
  const isTestMode = searchParams?.get('testAdmin') === 'true';

  const [speakers, setSpeakers] = useState<SpeakerRegistration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Modal states
  const [selectedSpeaker, setSelectedSpeaker] =
    useState<SpeakerRegistration | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const [filters, setFilters] = useState<SpeakerFilters>({
    search: '',
    paymentStatus: 'all',
    golfParticipation: 'all',
  });

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0,
  });

  // Note: Headshot signed URL generation is now handled individually by each HeadshotThumbnail component

  // Helper function to get file extension and determine if it should open in new tab
  // const getFileInfo = (url: string | null) => { // Removed - simplified presentation display
  //   if (!url) return { extension: '', shouldOpenInTab: false };
  //   const extension = url.split('.').pop()?.toLowerCase() || '';
  //   const shouldOpenInTab = ['pdf'].includes(extension);
  //   return { extension, shouldOpenInTab };
  // };

  // Note: Database linking functionality removed due to schema limitations
  // The attendee_registration_id column doesn't exist in the current database schema

  // Fetch speakers
  const fetchSpeakers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Apply pagination
      const from = (pagination.page - 1) * pagination.limit;
      const to = from + pagination.limit - 1;

      // Query speakers first, then manually fetch attendee data
      let query = supabase
        .from('iepa_speaker_registrations')
        .select('*', { count: 'exact' })
        .order('last_name', { ascending: true, nullsFirst: false })
        .order('first_name', { ascending: true, nullsFirst: false });

      // Apply filters
      if (filters.search) {
        query = query.or(
          `full_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,organization_name.ilike.%${filters.search}%,presentation_title.ilike.%${filters.search}%`
        );
      }

      // Apply pagination
      query = query.range(from, to);

      const { data, error: fetchError, count } = await query;

      if (fetchError) {
        console.error('Supabase query error:', {
          message: fetchError.message,
          code: fetchError.code,
          details: fetchError.details,
          hint: fetchError.hint,
        });
        throw fetchError;
      }

      // Manually fetch attendee data for speakers that have attendee_registration_id
      // Also look for potential attendee matches by email when no formal link exists
      const speakersWithAttendeeData = await Promise.all(
        (data || []).map(async speaker => {
          let attendeeData = null;

          // First, try to get linked attendee data
          if (speaker.attendee_registration_id) {
            try {
              const { data: linkedAttendeeData, error: attendeeError } =
                await supabase
                  .from('iepa_attendee_registrations')
                  .select('*')
                  .eq('id', speaker.attendee_registration_id)
                  .single();

              if (!attendeeError && linkedAttendeeData) {
                attendeeData = linkedAttendeeData;
              }
            } catch (error) {
              console.warn(
                `Failed to fetch linked attendee data for speaker ${speaker.id}:`,
                error
              );
            }
          }

          // If no linked attendee, look for potential match by email
          if (!attendeeData) {
            try {
              const { data: potentialAttendeeData, error: potentialError } =
                await supabase
                  .from('iepa_attendee_registrations')
                  .select('*')
                  .eq('email', speaker.email)
                  .single();

              if (!potentialError && potentialAttendeeData) {
                attendeeData = { ...potentialAttendeeData, _isUnlinked: true };
              }
            } catch {
              // No match found, which is fine
            }
          }

          return attendeeData
            ? { ...speaker, attendee_data: attendeeData }
            : speaker;
        })
      );

      setSpeakers(speakersWithAttendeeData);
      setPagination(prev => ({ ...prev, total: count || 0 }));
    } catch (err) {
      console.error('Error in fetchSpeakers:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch speakers');
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.page, pagination.limit]);

  useEffect(() => {
    fetchSpeakers();
  }, [fetchSpeakers]);

  // Delete speaker - for future implementation
  // const handleDelete = async (speaker: SpeakerRegistration) => {
  //   try {
  //     const { error: deleteError } = await supabase
  //       .from('iepa_speaker_registrations')
  //       .delete()
  //       .eq('id', speaker.id);

  //     if (deleteError) throw deleteError;

  //     await fetchSpeakers();
  //     setShowDelete(false);
  //     setSelectedSpeaker(null);
  //   } catch (err) {
  //     setError(
  //       err instanceof Error ? err.message : 'Failed to delete speaker'
  //     );
  //   }
  // };

  // Handle view speaker details
  const handleViewDetails = (speaker: SpeakerRegistration) => {
    setSelectedSpeaker(speaker);
    setShowDetails(true);
  };

  // Export speakers
  const handleExport = async () => {
    try {
      const { data, error: exportError } = await supabase
        .from('iepa_speaker_registrations')
        .select('*')
        .csv();

      if (exportError) throw exportError;

      const blob = new Blob([data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `speakers-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to export speakers'
      );
    }
  };

  // Clear all speakers
  const handleClearAllSpeakers = async () => {
    if (
      !confirm(
        'Are you sure you want to delete ALL speakers? This action cannot be undone.'
      )
    ) {
      return;
    }

    try {
      const { error: deleteError } = await supabase
        .from('iepa_speaker_registrations')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

      if (deleteError) throw deleteError;

      await fetchSpeakers();
      alert('All speakers have been deleted successfully.');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear speakers');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <Button
          onClick={handleExport}
          variant="outline"
          className="flex items-center"
        >
          <FiDownload className="w-4 h-4 mr-2" />
          Export CSV
        </Button>
        <Button
          onClick={fetchSpeakers}
          variant="outline"
          className="flex items-center"
        >
          <FiRefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
        <Button
          onClick={handleClearAllSpeakers}
          variant="outline"
          className="flex items-center text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
        >
          <FiTrash2 className="w-4 h-4 mr-2" />
          Clear All Speakers
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Input
                placeholder="Search speakers..."
                value={filters.search}
                onChange={e =>
                  setFilters(prev => ({ ...prev, search: e.target.value }))
                }
                className="w-full"
              />
            </div>
            <Select
              value={filters.paymentStatus}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, paymentStatus: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Payment Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.golfParticipation}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, golfParticipation: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Golf Participation" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="yes">Golf Participants</SelectItem>
                <SelectItem value="no">Non-Golf</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Error Display */}
      {error && (
        <Card>
          <CardBody className="p-4">
            <div className="text-red-600 text-sm">{error}</div>
          </CardBody>
        </Card>
      )}

      {/* Speakers Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Speakers ({pagination.total})</span>
            {loading && (
              <FiRefreshCw className="w-4 h-4 animate-spin text-gray-400" />
            )}
          </CardTitle>
        </CardHeader>
        <CardBody className="p-0">
          <ResponsiveTable>
            <ResponsiveTableHeader>
              <ResponsiveTableRow>
                <ResponsiveTableHead priority="high">
                  Speaker
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="medium">
                  Organization
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="low">
                  Presentation
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="medium">
                  Pricing Type
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="medium">
                  Payment Status
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="low">
                  Headshot
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="medium">
                  Submitted
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="high">
                  Actions
                </ResponsiveTableHead>
              </ResponsiveTableRow>
            </ResponsiveTableHeader>
            <ResponsiveTableBody>
              {speakers.length === 0 ? (
                <ResponsiveTableRow>
                  <ResponsiveTableCell priority="high" label="" colSpan={8}>
                    <div className="text-center py-8">
                      <div className="text-gray-500">
                        <FiMic className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-lg font-medium">No speakers found</p>
                        <p className="text-sm">
                          {loading
                            ? 'Loading speakers...'
                            : 'No speaker registrations have been submitted yet.'}
                        </p>
                      </div>
                    </div>
                  </ResponsiveTableCell>
                </ResponsiveTableRow>
              ) : (
                speakers.map(speaker => (
                  <ResponsiveTableRow key={speaker.id}>
                    <ResponsiveTableCell priority="high" label="Speaker">
                      <div>
                        <div className="font-medium">
                          {`${speaker.last_name}, ${speaker.first_name}`.trim()}
                        </div>
                        <div className="text-sm text-gray-500 truncate max-w-48">
                          {speaker.email}
                        </div>
                      </div>
                    </ResponsiveTableCell>
                    <ResponsiveTableCell priority="medium" label="Organization">
                      <div>
                        <div className="font-medium truncate max-w-32">
                          {speaker.organization_name}
                        </div>
                        <div className="text-sm text-gray-500 truncate max-w-32">
                          {speaker.job_title}
                        </div>
                      </div>
                    </ResponsiveTableCell>
                    <ResponsiveTableCell priority="low" label="Presentation">
                      <div className="max-w-xs">
                        <div className="font-medium truncate">
                          {speaker.presentation_title || 'No title provided'}
                        </div>
                        <div className="text-sm text-gray-500 truncate">
                          {speaker.presentation_description ||
                            'No description provided'}
                        </div>
                        {speaker.presentation_file_url && (
                          <div className="mt-2">
                            <Badge variant="outline" className="text-xs">
                              File Available
                            </Badge>
                          </div>
                        )}
                      </div>
                    </ResponsiveTableCell>
                    <ResponsiveTableCell priority="medium" label="Pricing Type">
                      <Badge
                        className={
                          speaker.speaker_pricing_type === 'comped-speaker'
                            ? 'bg-green-100 text-green-800'
                            : speaker.speaker_pricing_type ===
                                'full-meeting-speaker'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                        }
                      >
                        {speaker.speaker_pricing_type === 'comped-speaker'
                          ? 'Comped'
                          : speaker.speaker_pricing_type ===
                              'full-meeting-speaker'
                            ? 'Full Meeting'
                            : 'Legacy'}
                      </Badge>
                    </ResponsiveTableCell>
                    <ResponsiveTableCell
                      priority="medium"
                      label="Payment Status"
                    >
                      {speaker.attendee_data ? (
                        getStatusBadge(speaker.attendee_data.payment_status)
                      ) : (
                        <Badge className="bg-gray-100 text-gray-800">
                          No Attendee Data
                        </Badge>
                      )}
                    </ResponsiveTableCell>
                    <ResponsiveTableCell priority="low" label="Headshot">
                      <HeadshotThumbnail
                        headshotUrl={speaker.headshot_url}
                        speakerName={speaker.full_name}
                        showViewButton={true}
                      />
                    </ResponsiveTableCell>
                    <ResponsiveTableCell
                      priority="medium"
                      label="Submitted"
                      className="text-xs text-gray-500"
                    >
                      <div className="flex items-center gap-1">
                        <FiCalendar className="w-3 h-3" />
                        <div className="flex flex-col">
                          <span>{new Date(speaker.created_at).toLocaleDateString()}</span>
                          <span className="text-gray-400">{new Date(speaker.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                        </div>
                      </div>
                    </ResponsiveTableCell>
                    <ResponsiveTableCell priority="high" label="Actions">
                      <ActionButtons
                        actions={[
                          {
                            label: 'View Details',
                            icon: FiEye,
                            onClick: () => handleViewDetails(speaker),
                          },
                          {
                            label: 'View Full Page',
                            icon: FiUser,
                            onClick: () =>
                              window.open(
                                `/admin/speakers/view?id=${speaker.id}${isTestMode ? '&testAdmin=true' : ''}`,
                                '_blank'
                              ),
                          },
                          ...(speaker.attendee_data
                            ? [
                                {
                                  label: 'View Attendee Profile',
                                  icon: FiUsers,
                                  onClick: () =>
                                    window.open(
                                      `/admin/attendees/view?id=${speaker.attendee_data!.id}${isTestMode ? '&testAdmin=true' : ''}`,
                                      '_blank'
                                    ),
                                },
                              ]
                            : []),
                          {
                            label: 'Edit Speaker',
                            icon: FiEdit,
                            onClick: () =>
                              window.open(
                                `/admin/speakers/edit?id=${speaker.id}${isTestMode ? '&testAdmin=true' : ''}`,
                                '_blank'
                              ),
                          },
                          {
                            label: 'Delete Speaker',
                            icon: FiTrash2,
                            onClick: () =>
                              console.log('Delete speaker:', speaker),
                            variant: 'destructive' as const,
                          },
                        ]}
                        compact={true}
                      />
                    </ResponsiveTableCell>
                  </ResponsiveTableRow>
                ))
              )}
            </ResponsiveTableBody>
          </ResponsiveTable>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-6 py-4 border-t">
              <div className="text-sm text-gray-500">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{' '}
                of {pagination.total} speakers
              </div>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    setPagination(prev => ({
                      ...prev,
                      page: Math.max(1, prev.page - 1),
                    }))
                  }
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    setPagination(prev => ({
                      ...prev,
                      page: Math.min(totalPages, prev.page + 1),
                    }))
                  }
                  disabled={pagination.page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Speaker Details Modal */}
      <SpeakerDetailsModal
        speaker={selectedSpeaker}
        open={showDetails}
        onClose={() => {
          setShowDetails(false);
          setSelectedSpeaker(null);
        }}
      />
    </div>
  );
}
