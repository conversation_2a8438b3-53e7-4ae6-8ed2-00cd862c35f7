import { NextRequest, NextResponse } from 'next/server';
import { emailService } from '@/services/email';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { emailType, testEmail, sampleData } = body;

    console.log(`[EMAIL-PREVIEW-TEST] Sending test email: ${emailType} to ${testEmail}`);

    // Validate required fields
    if (!testEmail || !emailType) {
      return NextResponse.json({
        success: false,
        error: 'Test email address and email type are required'
      }, { status: 400 });
    }

    // Default sample data
    const defaultSampleData = {
      name: '<PERSON>',
      email: '<EMAIL>',
      type: 'attendee',
      confirmationNumber: 'CONF-12345',
      hasLodging: true,
      hasGolf: true,
      userId: 'user-123',
      speakerPricingType: 'comped-speaker',
      sponsorshipLevel: 'bronze-sponsor',
      ...sampleData
    };

    let success = false;

    switch (emailType) {
      case 'welcome':
        success = await emailService.sendWelcomeEmail(
          testEmail,
          defaultSampleData.name,
          {
            type: defaultSampleData.type as 'attendee' | 'speaker' | 'sponsor',
            confirmationNumber: defaultSampleData.confirmationNumber,
            userId: defaultSampleData.userId,
            hasLodging: defaultSampleData.hasLodging,
            hasGolf: defaultSampleData.hasGolf,
          }
        );
        break;
      
      case 'registration_confirmation':
        success = await emailService.sendRegistrationConfirmation(
          testEmail,
          defaultSampleData.name,
          {
            type: defaultSampleData.type as 'attendee' | 'speaker' | 'sponsor',
            confirmationNumber: defaultSampleData.confirmationNumber,
            userId: defaultSampleData.userId,
            speakerPricingType: defaultSampleData.speakerPricingType,
            sponsorshipLevel: defaultSampleData.sponsorshipLevel,
          }
        );
        break;
      
      case 'payment_confirmation':
        // For payment confirmation, we'd need to implement a test version
        // since the real one requires payment data
        success = await sendTestPaymentConfirmation(testEmail, defaultSampleData);
        break;
      
      default:
        return NextResponse.json({
          success: false,
          error: `Unknown email type: ${emailType}`
        }, { status: 400 });
    }

    if (success) {
      console.log(`[EMAIL-PREVIEW-TEST] Test email sent successfully to ${testEmail}`);
      return NextResponse.json({
        success: true,
        message: `Test email sent successfully to ${testEmail}`,
        timestamp: new Date().toISOString()
      });
    } else {
      console.error(`[EMAIL-PREVIEW-TEST] Failed to send test email to ${testEmail}`);
      return NextResponse.json({
        success: false,
        error: 'Failed to send test email'
      }, { status: 500 });
    }

  } catch (error: unknown) {
    console.error('[EMAIL-PREVIEW-TEST] Error sending test email:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send test email',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

async function sendTestPaymentConfirmation(testEmail: string, sampleData: Record<string, unknown>): Promise<boolean> {
  // Create a simple test payment confirmation email
  const subject = '[TEST EMAIL] Payment Confirmation - IEPA 2025';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: #3A6CA5; padding: 20px; text-align: center;">
        <h1 style="color: #ffffff; margin: 0; font-size: 24px;">💳 Payment Confirmation</h1>
        <p style="color: #ffffff; margin: 5px 0 0 0; font-size: 16px;">IEPA 2025 Annual Meeting</p>
      </div>
      
      <div style="padding: 30px;">
        <p><strong>Dear ${sampleData.name},</strong></p>
        
        <p>This is a test payment confirmation email. Your payment has been successfully processed.</p>
        
        <div style="background: #f8f9fa; border-left: 4px solid #3A6CA5; padding: 15px; margin: 20px 0;">
          <p style="margin: 0;"><strong>Confirmation Number:</strong> ${sampleData.confirmationNumber}</p>
        </div>
        
        <p>Thank you for your registration!</p>
        
        <p style="margin-top: 30px;">Best regards,<br>
        <strong>IEPA Conference Team</strong></p>
      </div>
    </div>
  `;

  return emailService.sendEmail(
    {
      to: testEmail,
      subject,
      html,
    },
    {
      emailType: 'test_payment_confirmation',
      userId: String(sampleData.userId),
    }
  );
}
