'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FiFile, FiImage, FiCheck, FiX } from 'react-icons/fi';
import { FileUpload } from '@/components/ui/FileUpload';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { updateSpeakerFile } from '@/services/speakerFileService';

interface SpeakerFileUploadButtonProps {
  speakerId: string;
  fileType: 'presentation' | 'headshot';
  currentFileUrl?: string;
  onUploadSuccess?: () => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'ghost';
}

export function SpeakerFileUploadButton({
  speakerId,
  fileType,
  currentFileUrl,
  onUploadSuccess,
  size = 'sm',
  variant = 'bordered',
}: SpeakerFileUploadButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  const fileConfig = {
    presentation: {
      label: 'Presentation File',
      description: 'Upload your presentation file (PDF, PPT, PPTX, DOC, DOCX - max 50MB)',
      icon: FiFile,
      bucket: 'iepa-presentations',
      folder: 'speaker-presentations',
      maxSize: 52428800, // 50MB
      allowedTypes: [
        'application/pdf',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ],
      accept: '.pdf,.ppt,.pptx,.doc,.docx',
    },
    headshot: {
      label: 'Professional Headshot',
      description: 'Upload your professional headshot (JPG, PNG, WebP - max 5MB)',
      icon: FiImage,
      bucket: 'iepa-presentations',
      folder: 'speaker-headshots',
      maxSize: 5242880, // 5MB
      allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
      accept: '.jpg,.jpeg,.png,.webp',
    },
  };

  const config = fileConfig[fileType];
  const IconComponent = config.icon;
  const hasFile = !!currentFileUrl;

  const getFileNameFromUrl = (url: string) => {
    try {
      const urlParts = url.split('/');
      const fileName = urlParts[urlParts.length - 1].split('?')[0];
      // Remove timestamp prefix if present (e.g., "1750275470511-biq7pdsyck9.jpg" -> "headshot.jpg")
      const cleanName = fileName.replace(/^\d+-[a-z0-9]+-/, '');
      return cleanName || fileName;
    } catch {
      return fileType === 'presentation' ? 'presentation-file' : 'headshot';
    }
  };

  const handleFileUpload = async (fileUrl: string | null) => {
    if (!fileUrl) return;

    setIsUploading(true);
    setUploadError(null);
    setUploadSuccess(false);

    try {
      const fieldName = fileType === 'presentation' ? 'presentation_file_url' : 'headshot_url';
      
      const result = await updateSpeakerFile(speakerId, fieldName, fileUrl);
      
      if (result.success) {
        setUploadSuccess(true);
        setTimeout(() => {
          setIsModalOpen(false);
          setUploadSuccess(false);
          onUploadSuccess?.();
        }, 1500);
      } else {
        setUploadError(result.error || 'Failed to update file');
      }
    } catch (error) {
      console.error('Error updating speaker file:', error);
      setUploadError('Failed to update file');
    } finally {
      setIsUploading(false);
    }
  };

  const handleModalClose = () => {
    if (!isUploading) {
      setIsModalOpen(false);
      setUploadError(null);
      setUploadSuccess(false);
    }
  };

  const handleDialogOpenChange = (open: boolean) => {
    if (!open && !isUploading) {
      setIsModalOpen(false);
      setUploadError(null);
      setUploadSuccess(false);
    }
  };

  return (
    <>
      <Button
        size={size}
        variant={variant === 'bordered' ? 'outline' : 'default'}
        className="flex items-center gap-2"
        onClick={() => setIsModalOpen(true)}
      >
        <IconComponent className="w-4 h-4" />
        {hasFile ? `Change ${config.label}` : `Add ${config.label}`}
      </Button>

      <Dialog open={isModalOpen} onOpenChange={handleDialogOpenChange}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {hasFile ? `Change ${config.label}` : `Upload ${config.label}`}
            </DialogTitle>
            <DialogDescription>
              {config.description}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {uploadSuccess ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FiCheck className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="text-lg font-semibold text-green-600 mb-2">Upload Successful!</h4>
                <p className="text-gray-600">Your {config.label.toLowerCase()} has been updated.</p>
              </div>
            ) : (
              <div className="space-y-4">
                <FileUpload
                  label={config.label}
                  description={config.description}
                  bucket={config.bucket}
                  folder={config.folder}
                  maxSize={config.maxSize}
                  allowedTypes={config.allowedTypes}
                  accept={config.accept}
                  onFileUpload={handleFileUpload}
                  placeholder={hasFile ? `Replace current ${config.label.toLowerCase()}` : `Upload your ${config.label.toLowerCase()}`}
                  disabled={isUploading}
                  showPreview={fileType === 'headshot'}
                  allowDownload={true}
                  allowView={true}
                  existingFileUrl={currentFileUrl}
                  existingFileName={currentFileUrl ? getFileNameFromUrl(currentFileUrl) : undefined}
                />

                {uploadError && (
                  <div className="p-4 bg-red-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FiX className="w-5 h-5 text-red-600" />
                      <h4 className="font-medium text-red-900">Upload Failed</h4>
                    </div>
                    <p className="text-sm text-red-700 mt-1">{uploadError}</p>
                  </div>
                )}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="ghost"
              onClick={handleModalClose}
              disabled={isUploading}
            >
              {uploadSuccess ? 'Close' : 'Cancel'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
