'use client';

import * as React from 'react';
import { useEffect, useState } from 'react';
import { Input, InputProps } from './input';
import { PhoneInput, PhoneInputProps } from './phone-input';

interface HydrationSafeInputProps extends InputProps {
  suppressHydrationWarning?: boolean;
}

interface HydrationSafePhoneInputProps extends PhoneInputProps {
  suppressHydrationWarning?: boolean;
}

/**
 * HydrationSafeInput Component
 * 
 * Wraps the regular Input component to handle hydration mismatches gracefully.
 * This is particularly useful for form inputs that may be affected by browser
 * extensions like password managers that inject DOM elements.
 */
export const HydrationSafeInput = React.forwardRef<HTMLInputElement, HydrationSafeInputProps>(
  ({ suppressHydrationWarning = true, ...props }, ref) => {
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
      setIsMounted(true);
    }, []);

    // During SSR and initial hydration, render a basic input
    if (!isMounted) {
      // Extract only standard HTML input props, excluding custom Input props
      const { label, isRequired, description, ...htmlInputProps } = props;

      return (
        <div className="space-y-1" suppressHydrationWarning={suppressHydrationWarning}>
          {label && (
            <label htmlFor={props.id} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {label}
              {isRequired && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          <input
            {...htmlInputProps}
            ref={ref}
            className="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border-[var(--iepa-gray-300)] focus-visible:ring-[var(--iepa-primary-blue)]/20 focus-visible:border-[var(--iepa-primary-blue)] h-10 px-3 py-2 min-h-[44px]"
            suppressHydrationWarning={suppressHydrationWarning}
          />
          {description && (
            <p className="text-sm text-[var(--iepa-gray-600)]">{description}</p>
          )}
        </div>
      );
    }

    // After hydration, render the full Input component
    return <Input {...props} ref={ref} />;
  }
);

HydrationSafeInput.displayName = 'HydrationSafeInput';

/**
 * HydrationSafePhoneInput Component
 * 
 * Wraps the PhoneInput component to handle hydration mismatches gracefully.
 */
export const HydrationSafePhoneInput = React.forwardRef<HTMLInputElement, HydrationSafePhoneInputProps>(
  ({ suppressHydrationWarning = true, ...props }, ref) => {
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
      setIsMounted(true);
    }, []);

    // During SSR and initial hydration, render a basic tel input
    if (!isMounted) {
      // Extract only standard HTML input props, excluding custom PhoneInput props
      const { label, isRequired, description, onChange, ...htmlInputProps } = props;

      return (
        <div className="space-y-1" suppressHydrationWarning={suppressHydrationWarning}>
          {label && (
            <label htmlFor={props.id} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {label}
              {isRequired && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          <input
            {...htmlInputProps}
            type="tel"
            ref={ref}
            placeholder="(*************"
            className="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border-[var(--iepa-gray-300)] focus-visible:ring-[var(--iepa-primary-blue)]/20 focus-visible:border-[var(--iepa-primary-blue)] h-10 px-3 py-2 min-h-[44px]"
            suppressHydrationWarning={suppressHydrationWarning}
            onChange={onChange ? (e) => onChange(e.target.value) : undefined}
          />
          {description && (
            <p className="text-sm text-[var(--iepa-gray-600)]">{description}</p>
          )}
        </div>
      );
    }

    // After hydration, render the full PhoneInput component
    return <PhoneInput {...props} ref={ref} />;
  }
);

HydrationSafePhoneInput.displayName = 'HydrationSafePhoneInput';
