'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, CardTitle, Button } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import {
  FiDatabase,
  FiTable,
  FiRefreshCw,
  FiDownload,
  FiUpload,
  FiTrash2,
  FiEdit,
  FiEye,
  FiAlertTriangle,
} from 'react-icons/fi';
import {
  ResponsiveTable,
  ResponsiveTableBody,
  ResponsiveTableCell,
  ResponsiveTableHead,
  ResponsiveTableHeader,
  ResponsiveTableRow,
  ActionButtons,
} from '@/components/ui/responsive-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface TableInfo {
  name: string;
  count: number;
  lastUpdated: string;
  description: string;
}

interface DatabaseStats {
  totalTables: number;
  totalRecords: number;
  storageUsed: string;
  lastBackup: string;
}

const IEPA_TABLES = [
  {
    name: 'iepa_attendee_registrations',
    description: 'Conference attendee registration data',
    icon: '👥',
  },
  {
    name: 'iepa_speaker_registrations',
    description: 'Speaker application and presentation data',
    icon: '🎤',
  },
  {
    name: 'iepa_sponsor_registrations',
    description: 'Sponsor registration and package information',
    icon: '⭐',
  },
  {
    name: 'iepa_payments',
    description: 'Payment transaction records',
    icon: '💳',
  },
  {
    name: 'iepa_admin_users',
    description: 'Administrative user access control',
    icon: '🛡️',
  },
];

export default function DatabasePage() {
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [tableData, setTableData] = useState<Record<string, unknown>[]>([]);
  const [tableColumns, setTableColumns] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [dataLoading, setDataLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DatabaseStats>({
    totalTables: 0,
    totalRecords: 0,
    storageUsed: '0 MB',
    lastBackup: 'Never',
  });

  // Fetch table information
  const fetchTableInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      const tableInfoPromises = IEPA_TABLES.map(async table => {
        try {
          const { count, error } = await supabase
            .from(table.name)
            .select('*', { count: 'exact', head: true });

          if (error) throw error;

          return {
            name: table.name,
            count: count || 0,
            lastUpdated: new Date().toISOString(),
            description: table.description,
          };
        } catch (err) {
          console.error(`Error fetching ${table.name}:`, err);
          return {
            name: table.name,
            count: 0,
            lastUpdated: 'Error',
            description: table.description,
          };
        }
      });

      const tableInfo = await Promise.all(tableInfoPromises);
      setTables(tableInfo);

      // Calculate stats
      const totalRecords = tableInfo.reduce(
        (sum, table) => sum + table.count,
        0
      );
      setStats(prev => ({
        ...prev,
        totalTables: tableInfo.length,
        totalRecords,
      }));
    } catch (err) {
      console.error('Error fetching table info:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch table information'
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch data for selected table
  const fetchTableData = async (tableName: string) => {
    try {
      setDataLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100); // Limit to first 100 records for performance

      if (error) throw error;

      setTableData(data || []);

      // Extract column names
      if (data && data.length > 0) {
        setTableColumns(Object.keys(data[0]));
      } else {
        setTableColumns([]);
      }
    } catch (err) {
      console.error('Error fetching table data:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch table data'
      );
    } finally {
      setDataLoading(false);
    }
  };

  // Export table data as CSV
  const exportTableData = (tableName: string) => {
    if (tableData.length === 0) return;

    const csvContent = [
      tableColumns.join(','),
      ...tableData.map(row =>
        tableColumns
          .map(col => {
            const value = row[col];
            if (value === null || value === undefined) return '';
            if (typeof value === 'string' && value.includes(',')) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return String(value);
          })
          .join(',')
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${tableName}_export_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  // Delete record
  const deleteRecord = async (tableName: string, id: string) => {
    if (
      !confirm(
        'Are you sure you want to delete this record? This action cannot be undone.'
      )
    ) {
      return;
    }

    try {
      const { error } = await supabase.from(tableName).delete().eq('id', id);

      if (error) throw error;

      // Refresh table data
      await fetchTableData(tableName);
      await fetchTableInfo();
    } catch (err) {
      console.error('Error deleting record:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete record');
    }
  };

  useEffect(() => {
    fetchTableInfo();
  }, []);

  useEffect(() => {
    if (selectedTable) {
      fetchTableData(selectedTable);
    }
  }, [selectedTable]);

  const formatValue = (value: unknown): string => {
    if (value === null || value === undefined) return 'NULL';
    if (typeof value === 'boolean') return value ? 'true' : 'false';
    if (typeof value === 'object') return JSON.stringify(value);
    if (typeof value === 'string' && value.length > 50) {
      return value.substring(0, 50) + '...';
    }
    return String(value);
  };

  const getTableIcon = (tableName: string) => {
    const table = IEPA_TABLES.find(t => t.name === tableName);
    return table?.icon || '📊';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Database Management
          </h1>
          <p className="text-gray-600 mt-1">
            Direct access to IEPA conference database tables
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button onClick={fetchTableInfo} variant="outline" size="sm">
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Warning */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardBody className="p-4">
          <div className="flex items-center space-x-3">
            <FiAlertTriangle className="w-5 h-5 text-yellow-600" />
            <div>
              <h3 className="font-medium text-yellow-800">
                Database Access Warning
              </h3>
              <p className="text-yellow-700 text-sm mt-1">
                This section provides direct access to the database. Use with
                caution. Always backup data before making changes. Deletions are
                permanent.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Database Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Tables
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalTables}
                </p>
              </div>
              <FiTable className="w-8 h-8 text-gray-400" />
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Records
                </p>
                <p className="text-2xl font-bold text-blue-600">
                  {stats.totalRecords.toLocaleString()}
                </p>
              </div>
              <FiDatabase className="w-8 h-8 text-blue-400" />
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Storage Used
                </p>
                <p className="text-2xl font-bold text-green-600">
                  {stats.storageUsed}
                </p>
              </div>
              <FiUpload className="w-8 h-8 text-green-400" />
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Last Backup</p>
                <p className="text-2xl font-bold text-purple-600">
                  {stats.lastBackup}
                </p>
              </div>
              <FiDownload className="w-8 h-8 text-purple-400" />
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Tables Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Database Tables</CardTitle>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="text-center py-8">
              <FiRefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">Loading table information...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tables.map(table => (
                <Card
                  key={table.name}
                  className={`cursor-pointer transition-colors ${
                    selectedTable === table.name
                      ? 'border-[var(--iepa-primary-blue)] bg-blue-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedTable(table.name)}
                >
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">
                          {getTableIcon(table.name)}
                        </span>
                        <h3 className="font-medium text-gray-900">
                          {table.name}
                        </h3>
                      </div>
                      <Badge variant="secondary">{table.count} records</Badge>
                    </div>
                    <p className="text-sm text-gray-600">{table.description}</p>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Table Data */}
      {selectedTable && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <span>{getTableIcon(selectedTable)}</span>
                <span>{selectedTable}</span>
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Button
                  onClick={() => exportTableData(selectedTable)}
                  variant="outline"
                  size="sm"
                  disabled={tableData.length === 0}
                >
                  <FiDownload className="w-4 h-4 mr-2" />
                  Export CSV
                </Button>
                <Select value={selectedTable} onValueChange={setSelectedTable}>
                  <SelectTrigger className="w-64">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {IEPA_TABLES.map(table => (
                      <SelectItem key={table.name} value={table.name}>
                        {table.icon} {table.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardBody>
            {dataLoading ? (
              <div className="text-center py-8">
                <FiRefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
                <p className="text-gray-500">Loading table data...</p>
              </div>
            ) : tableData.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No data found in this table</p>
              </div>
            ) : (
              <div>
                <ResponsiveTable>
                  <ResponsiveTableHeader>
                    <ResponsiveTableRow>
                      {tableColumns.slice(0, 4).map((column, index) => (
                        <ResponsiveTableHead
                          key={column}
                          priority={index < 2 ? "high" : "medium"}
                        >
                          {column}
                        </ResponsiveTableHead>
                      ))}
                      {tableColumns.length > 4 && (
                        <ResponsiveTableHead priority="low">
                          More Columns ({tableColumns.length - 4})
                        </ResponsiveTableHead>
                      )}
                      <ResponsiveTableHead priority="high">Actions</ResponsiveTableHead>
                    </ResponsiveTableRow>
                  </ResponsiveTableHeader>
                  <ResponsiveTableBody>
                      {tableData.slice(0, 50).map((row, index) => (
                        <ResponsiveTableRow
                          key={
                            ((row as Record<string, unknown>).id as string) ||
                            index
                          }
                        >
                          {tableColumns.slice(0, 4).map((column, colIndex) => (
                            <ResponsiveTableCell
                              key={column}
                              priority={colIndex < 2 ? "high" : "medium"}
                              label={column}
                            >
                              <div
                                className="truncate max-w-32"
                                title={String(row[column])}
                              >
                                {formatValue(row[column])}
                              </div>
                            </ResponsiveTableCell>
                          ))}
                          {tableColumns.length > 4 && (
                            <ResponsiveTableCell priority="low" label="Additional Data">
                              <div className="text-xs text-gray-500">
                                {tableColumns.length - 4} more fields
                              </div>
                            </ResponsiveTableCell>
                          )}
                          <ResponsiveTableCell priority="high" label="Actions">
                            <ActionButtons
                              actions={[
                                {
                                  label: 'View Record',
                                  icon: FiEye,
                                  onClick: () => console.log('View record:', row),
                                },
                                {
                                  label: 'Edit Record',
                                  icon: FiEdit,
                                  onClick: () => console.log('Edit record:', row),
                                },
                                {
                                  label: 'Delete Record',
                                  icon: FiTrash2,
                                  onClick: () =>
                                    deleteRecord(
                                      selectedTable,
                                      (row as Record<string, unknown>).id as string
                                    ),
                                  variant: 'destructive' as const,
                                },
                              ]}
                              compact={true}
                            />
                          </ResponsiveTableCell>
                        </ResponsiveTableRow>
                      ))}
                  </ResponsiveTableBody>
                </ResponsiveTable>

                {tableData.length > 50 && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg text-center">
                    <p className="text-gray-600">
                      Showing first 50 of {tableData.length} records. Use
                      filters or export for complete data.
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardBody>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody>
            <p className="text-red-600">{error}</p>
          </CardBody>
        </Card>
      )}
    </div>
  );
}
