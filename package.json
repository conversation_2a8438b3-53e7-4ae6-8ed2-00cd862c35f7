{"name": "iepa-conf-reg", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 6969", "dev:6969": "next dev --turbopack --port 6969", "dev:webpack": "next dev --port 6969", "dev:turbo": "next dev --turbopack --port 6969", "dev:alt-port": "next dev --turbopack --port 3001", "prebuild": "node scripts/inject-git-info.mjs", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "check": "npm run lint && npm run format:check && tsc --noEmit", "test:client": "node scripts/test-client-errors.js", "test:build": "npm run build", "test:full": "npm run check && npm run test:build && npm run test:client", "test:pre-commit": "node scripts/pre-commit-tests.js", "prepare": "husky install || true", "stripe:mcp": "tsx scripts/stripe-mcp-server.ts", "stripe:mcp:npx": "npx -y @stripe/mcp --tools=all --api-key=$STRIPE_SECRET_KEY", "stripe:test": "tsx scripts/test-stripe-mcp.ts", "import-attendees": "tsx scripts/import-2024-attendees.ts", "populate-organizations": "tsx scripts/populate-organizations.ts", "sync-production-data": "tsx scripts/sync-production-data.ts", "backup-production-data": "./scripts/backup-production-data.sh", "manage-data": "./scripts/manage-data.sh", "sync-production-data-safe": "tsx scripts/sync-production-data-safe.ts", "create-production-dump": "tsx scripts/create-production-dump.ts", "test:e2e": "node scripts/run-comprehensive-e2e-tests.js", "test:e2e:iepa-member": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test tests/iepa-member-registration-e2e.spec.js --project=chromium", "test:e2e:iepa-member:headed": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test tests/iepa-member-registration-e2e.spec.js --project=chromium --headed", "test:e2e:iepa-edit": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test tests/iepa-edit-registration-e2e.spec.js --project=chromium", "test:e2e:iepa-edit:headed": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test tests/iepa-edit-registration-e2e.spec.js --project=chromium --headed", "test:e2e:pdf-invoice": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test tests/pdf-invoice-e2e.spec.js --project=chromium", "test:e2e:pdf-invoice:headed": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test tests/pdf-invoice-e2e.spec.js --project=chromium --headed", "test:e2e:production": "PLAYWRIGHT_BASE_URL=https://iepa.vercel.app npx playwright test", "test:e2e:local": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test", "test:e2e:report": "npx playwright show-report", "test:email": "node scripts/test-email-setup.js", "test:email:deliverability": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test tests/email-deliverability.spec.js", "test:email:integration": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test tests/email-integration.spec.js", "test:email:all": "PLAYWRIGHT_BASE_URL=http://localhost:6969 npx playwright test tests/email-*.spec.js", "supabase:setup": "./scripts/setup-local-supabase.sh", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:restart": "supabase restart", "supabase:status": "supabase status", "supabase:studio": "open http://127.0.0.1:54323", "dev:full": "npm run supabase:setup && npm run dev", "env:check": "./scripts/check-environment.sh", "test:auth": "node scripts/test-auth-redirect.js", "clean-test-data": "npx tsx scripts/clean-test-registrations.ts", "clean-all-data": "npx tsx scripts/clean-all-registrations.ts", "check-registrations": "npx tsx scripts/check-registrations.ts"}, "dependencies": {"@heroui/react": "^2.7.8", "@heroui/theme": "^2.4.15", "@modelcontextprotocol/sdk": "^1.12.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/renderer": "^4.3.0", "@sendgrid/mail": "^8.1.5", "@stripe/agent-toolkit": "^0.7.9", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.81.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "lucide-react": "^0.511.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-window": "^1.8.11", "stripe": "^18.2.1", "tailwind-merge": "^3.3.0", "tsx": "^4.19.4"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "@types/sendgrid": "^2.0.31", "eslint": "^9", "eslint-config-next": "15.3.3", "imap-simple": "^5.1.0", "mailparser": "^3.7.1", "prettier": "^3.5.3", "puppeteer": "^24.10.0", "tailwindcss": "^4", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}}