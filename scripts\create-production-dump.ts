#!/usr/bin/env tsx

/**
 * IEPA Conference Registration - Create Production Data Dump
 * 
 * This script creates a complete SQL dump of production data that can be easily imported
 * into local development. It handles schema differences by creating the data without
 * foreign key constraints.
 */

import { createClient } from '@supabase/supabase-js';
import { writeFileSync, mkdirSync, existsSync } from 'fs';
import { join } from 'path';

// Production Supabase credentials
const PRODUCTION_CONFIG = {
  url: 'https://uffhyhpcuedjsisczocy.supabase.co',
  serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVmZmh5aHBjdWVkanNpc2N6b2N5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NDkxNywiZXhwIjoyMDY0MTQwOTE3fQ.bBd3qf8TicnfVhCKjC4aYNbe9xLQI7tEyIjWME43MQA'
};

const productionClient = createClient(PRODUCTION_CONFIG.url, PRODUCTION_CONFIG.serviceRoleKey);

const log = (message: string) => console.log(`[INFO] ${message}`);
const success = (message: string) => console.log(`[SUCCESS] ${message}`);
const error = (message: string) => console.log(`[ERROR] ${message}`);

// Tables to dump
const TABLES = [
  'iepa_user_profiles',
  'iepa_organizations', 
  'iepa_historical_registrations',
  'iepa_attendee_registrations',
  'iepa_speaker_registrations',
  'iepa_sponsor_registrations',
  'iepa_golf_registrations',
  'iepa_payments',
  'iepa_email_log'
];

// Convert value to SQL format
const formatValue = (value: any): string => {
  if (value === null || value === undefined) {
    return 'NULL';
  }
  
  if (typeof value === 'string') {
    return `'${value.replace(/'/g, "''")}'`;
  }
  
  if (typeof value === 'boolean') {
    return value ? 'TRUE' : 'FALSE';
  }
  
  if (Array.isArray(value)) {
    const arrayValues = value.map(v => formatValue(v)).join(',');
    return `ARRAY[${arrayValues}]`;
  }
  
  if (typeof value === 'object') {
    return `'${JSON.stringify(value).replace(/'/g, "''")}'::jsonb`;
  }
  
  return String(value);
};

// Create INSERT statement for a row
const createInsertStatement = (tableName: string, row: any): string => {
  const columns = Object.keys(row);
  const values = columns.map(col => formatValue(row[col]));
  
  return `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')});`;
};

// Create data dump for a table
const dumpTable = async (tableName: string): Promise<string[]> => {
  log(`Dumping ${tableName}...`);
  
  const { data, error: fetchError } = await productionClient
    .from(tableName)
    .select('*');
  
  if (fetchError) {
    error(`Failed to fetch ${tableName}: ${fetchError.message}`);
    return [];
  }
  
  if (!data || data.length === 0) {
    log(`No data in ${tableName}`);
    return [];
  }
  
  log(`Found ${data.length} rows in ${tableName}`);
  
  const statements = data.map(row => createInsertStatement(tableName, row));
  return statements;
};

// Main function
const createProductionDump = async (): Promise<void> => {
  console.log('📦 Creating IEPA Production Data Dump');
  console.log('====================================');
  console.log('');
  
  // Create output directory
  const outputDir = '.docs/production-dumps';
  if (!existsSync(outputDir)) {
    mkdirSync(outputDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().split('T')[0];
  const dumpFile = join(outputDir, `production-dump-${timestamp}.sql`);
  
  let sqlContent = `-- IEPA Production Data Dump
-- Generated: ${new Date().toISOString()}
-- 
-- This file contains production data that can be imported into local development
-- 
-- Usage:
--   1. Make sure local Supabase is running: supabase start
--   2. Import: psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -f ${dumpFile}

-- Disable foreign key checks and triggers for faster import
SET session_replication_role = replica;

-- Clear existing data (uncomment if needed)
-- TRUNCATE iepa_email_log, iepa_payments, iepa_golf_registrations, 
--          iepa_sponsor_registrations, iepa_speaker_registrations, 
--          iepa_attendee_registrations, iepa_historical_registrations, 
--          iepa_organizations, iepa_user_profiles CASCADE;

`;
  
  let totalRows = 0;
  
  // Dump each table
  for (const table of TABLES) {
    try {
      const statements = await dumpTable(table);
      
      if (statements.length > 0) {
        sqlContent += `\n-- ${table} (${statements.length} rows)\n`;
        sqlContent += statements.join('\n') + '\n';
        totalRows += statements.length;
      }
      
    } catch (err) {
      error(`Error dumping ${table}: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }
  
  sqlContent += `
-- Re-enable foreign key checks and triggers
SET session_replication_role = DEFAULT;

-- Update sequences (if needed)
SELECT setval(pg_get_serial_sequence('iepa_user_profiles', 'id'), COALESCE((SELECT MAX(id) FROM iepa_user_profiles), 1));
-- Add more sequence updates as needed

-- Summary: ${totalRows} total rows imported
`;
  
  // Write the dump file
  writeFileSync(dumpFile, sqlContent);
  
  // Create import script
  const importScript = join(outputDir, `import-${timestamp}.sh`);
  const importContent = `#!/bin/bash

# IEPA Production Data Import Script
# Generated: ${new Date().toISOString()}

set -e

echo "🔄 Importing IEPA production data..."

# Check if local Supabase is running
if ! curl -s http://127.0.0.1:54321/rest/v1/ > /dev/null 2>&1; then
    echo "❌ Local Supabase is not running"
    echo "Please run: supabase start"
    exit 1
fi

echo "✅ Local Supabase is running"

# Import data
echo "📥 Importing ${totalRows} rows..."
if psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -f "${dumpFile}"; then
    echo "✅ Data imported successfully!"
    echo ""
    echo "🔗 Access your data:"
    echo "   Studio: http://127.0.0.1:54323"
    echo "   API: http://127.0.0.1:54321"
else
    echo "❌ Import failed"
    exit 1
fi
`;
  
  writeFileSync(importScript, importContent);
  
  // Make import script executable
  try {
    const { execSync } = require('child_process');
    execSync(`chmod +x "${importScript}"`);
  } catch (err) {
    // Ignore chmod errors
  }
  
  console.log('');
  success('🎉 Production dump created successfully!');
  console.log('');
  console.log(`📁 Files created:`);
  console.log(`   SQL Dump: ${dumpFile}`);
  console.log(`   Import Script: ${importScript}`);
  console.log(`   Total Rows: ${totalRows}`);
  console.log('');
  console.log('🔄 To import the data:');
  console.log(`   cd ${outputDir}`);
  console.log(`   ./import-${timestamp}.sh`);
  console.log('');
  console.log('💡 Or manually:');
  console.log(`   psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" -f ${dumpFile}`);
};

if (require.main === module) {
  createProductionDump().catch(err => {
    error(`Dump creation failed: ${err.message}`);
    process.exit(1);
  });
}
