'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Chip } from '@/components/ui';
import { ProtectedPageBanner } from '@/components/auth/AuthStatusIndicator';
import { useAuth } from '@/contexts/AuthContext';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import {
  useUserRegistrations,
  useRegistrationPermissions,
} from '@/hooks/useUserRegistrations';
import { PDFDownloadButton } from '@/components/pdf/PDFDownloadButton';
import { formatCurrency, formatDate } from '@/lib/pdf-generation/utils';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  FiEdit,
  FiEye,
  FiCreditCard,
  FiCalendar,
  FiUser,
  FiDownload,
  FiFile,
  FiImage,
} from 'react-icons/fi';
import { showError, showSuccess } from '@/utils/notifications';
import { GolfAddOnButton } from '@/components/golf-addon';
import { SpeakerFileUploadButton } from '@/components/user/speaker/SpeakerFileUploadButton';
import { useProfileEditPermission } from '@/hooks/useAdminSettings';

export default function MyRegistrationsPage() {
  const { user } = useAuth();
  const router = useRouter();

  const {
    registrations,
    loading,
    error,
    hasRegistrations,
    registrationCount,
    totalAmount,
    refresh,
  } = useUserRegistrations();
  const permissions = useRegistrationPermissions();
  const { allowProfileEdits, isLoading: settingsLoading } =
    useProfileEditPermission();

  // Payment processing state
  const [processingPayment, setProcessingPayment] = useState<string | null>(
    null
  );

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
    }
  }, [user, router]);

  if (!user) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'danger';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      case 'refunded':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getTypeDisplayName = (type: string) => {
    switch (type) {
      case 'attendee':
        return 'Conference Attendee';
      case 'speaker':
        return 'Conference Speaker';
      case 'sponsor':
        return 'Conference Sponsor';
      default:
        return type;
    }
  };

  const formatMealName = (mealId: string) => {
    const mealNames: Record<string, string> = {
      // Current meal keys with dates
      'day1-reception': 'Sep 15 Welcome Reception & Dinner',
      'day1-breakfast': 'Sep 15 Breakfast',
      'day1-lunch': 'Sep 15 Lunch',
      'day2-breakfast': 'Sep 16 Breakfast',
      'day2-lunch': 'Sep 16 Lunch',
      'day2-dinner': 'Sep 16 Reception & Dinner',
      'day3-breakfast': 'Sep 17 Breakfast',
      'day3-lunch': 'Sep 17 Lunch',
      // Legacy meal keys
      sept15Dinner: 'Sep 15 Welcome Reception & Dinner',
      sept16Breakfast: 'Sep 16 Breakfast',
      sept16Lunch: 'Sep 16 Lunch',
      sept16Dinner: 'Sep 16 Reception & Dinner',
      sept17Breakfast: 'Sep 17 Breakfast',
      sept17Lunch: 'Sep 17 Lunch',
    };
    return mealNames[mealId] || mealId;
  };

  // Handle payment completion for pending registrations
  const handleCompletePayment = async (registration: {
    id: string;
    type: string;
    amount: number;
  }) => {
    if (processingPayment || !user) return;

    try {
      setProcessingPayment(registration.id);

      // Import Stripe utilities
      const { stripeUtils } = await import('@/lib/stripe-client');

      // Prepare payment data based on registration type
      const paymentData = {
        registrationId: registration.id,
        registrationType: registration.type,
        customerEmail: user.email || '',
        customerName:
          registration.type === 'attendee' && registrations?.attendee
            ? `${registrations.attendee.personalInfo.firstName} ${registrations.attendee.personalInfo.lastName}`.trim()
            : registration.type === 'speaker' && registrations?.speaker
              ? `${registrations.speaker.personalInfo.firstName} ${registrations.speaker.personalInfo.lastName}`.trim()
              : user.email || '',
        totalAmount: registration.amount,
        lineItems: [
          {
            name: `IEPA ${CONFERENCE_YEAR} Conference - ${getTypeDisplayName(registration.type)} Registration`,
            description: `Registration for ${user.email}`,
            price: registration.amount,
            quantity: 1,
          },
        ],
        metadata: {
          userId: user.id,
          registrationId: registration.id,
          registrationType: registration.type,
        },
      };

      console.log(
        '🔄 Initiating payment process for existing registration...',
        paymentData
      );

      // Process payment (creates checkout session and redirects)
      const paymentResult = await stripeUtils.processPayment(paymentData);

      if (!paymentResult.success) {
        console.error('❌ Payment initiation failed:', paymentResult.error);
        showError(
          'Payment Error',
          `Failed to initiate payment: ${paymentResult.error}. Please try again or contact support.`
        );
      } else {
        console.log('✅ Payment process initiated successfully');
        showSuccess(
          'Redirecting to Payment',
          'You will be redirected to Stripe checkout to complete your payment.'
        );
      }
      // If successful, user will be redirected to Stripe checkout
    } catch (paymentError) {
      console.error('💥 Payment processing error:', paymentError);
      showError(
        'Payment Error',
        'Failed to process payment. Please try again or contact support.'
      );
    } finally {
      setProcessingPayment(null);
    }
  };

  // Handle error state
  if (error) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="iepa-heading-1 mb-4 text-red-600">Error</h1>
            <p className="iepa-body mb-4">{error}</p>
            <Button onClick={refresh} color="primary">
              Try Again
            </Button>
          </div>
        </section>
      </div>
    );
  }

  // Handle loading state
  if (loading) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-4xl mx-auto text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h1 className="iepa-heading-1 mb-4">Loading...</h1>
            <p className="iepa-body">Fetching your registrations...</p>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div>
      {/* Auth Status Banner */}
      <ProtectedPageBanner />

      <div className="iepa-container">
        {/* Header */}
        <section className="iepa-section">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
              <div>
                <h1 className="iepa-heading-1 mb-2">My Registrations</h1>
                <p className="iepa-body">
                  View and manage your IEPA {CONFERENCE_YEAR} conference
                  registrations
                </p>
                {hasRegistrations && (
                  <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <FiUser className="w-4 h-4" />
                      {registrationCount} registration
                      {registrationCount !== 1 ? 's' : ''}
                    </span>
                    <span className="flex items-center gap-1">
                      <FiCreditCard className="w-4 h-4" />
                      {formatCurrency(totalAmount)} total
                    </span>
                  </div>
                )}
              </div>
              <div className="mt-4 md:mt-0 flex gap-2">
                <Button onClick={refresh} variant="bordered" size="sm">
                  Refresh
                </Button>
                <Button as={Link} href="/register" color="primary">
                  New Registration
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Registrations List */}
        <section className="iepa-section">
          <div className="max-w-6xl mx-auto">
            {!hasRegistrations ? (
              <Card>
                <CardBody>
                  <div className="text-center py-12">
                    <div className="text-6xl mb-6">📋</div>
                    <h2 className="iepa-heading-2 mb-4">
                      No Registrations Yet
                    </h2>
                    <p className="iepa-body mb-8 max-w-2xl mx-auto">
                      You haven&apos;t registered for the IEPA {CONFERENCE_YEAR}{' '}
                      conference yet. Choose from our registration options to
                      get started!
                    </p>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-3xl mx-auto mb-8">
                      {permissions.canRegisterAttendee && (
                        <Button
                          as={Link}
                          href="/register/attendee"
                          color="primary"
                          className="w-full"
                        >
                          Register - Attendee
                        </Button>
                      )}
                      {permissions.canRegisterSpeaker && (
                        <Button
                          as={Link}
                          href="/register/speaker"
                          color="secondary"
                          className="w-full"
                        >
                          Register - Speaker
                        </Button>
                      )}
                      {permissions.canRegisterSponsor && (
                        <Button
                          as={Link}
                          href="/register/sponsor"
                          variant="bordered"
                          className="w-full"
                        >
                          Register - Sponsor
                        </Button>
                      )}
                      {/* Note: Sponsor Attendee registration is only accessible via email links */}
                    </div>

                    <Button as={Link} href="/about" variant="light">
                      Learn More About the Conference
                    </Button>
                  </div>
                </CardBody>
              </Card>
            ) : (
              <div className="space-y-6">
                {Array.isArray(registrations?.summary) &&
                registrations.summary.length > 0 ? (
                  registrations.summary.map(registration => (
                    <Card key={registration.id}>
                      <CardHeader>
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full">
                          <div>
                            <h3 className="iepa-heading-3 mb-1">
                              {registration.title}
                            </h3>
                            <p className="iepa-body-small text-gray-600 mb-1">
                              {registration.description}
                            </p>
                            <p className="iepa-body-small text-gray-500">
                              Registration ID: {registration.id.slice(0, 8)}...
                            </p>
                          </div>
                          <div className="flex gap-2 mt-2 md:mt-0">
                            <Chip
                              color={getStatusColor(registration.status)}
                              size="sm"
                            >
                              {registration.status.charAt(0).toUpperCase() +
                                registration.status.slice(1)}
                            </Chip>
                            <Chip
                              color={getPaymentStatusColor(
                                registration.paymentStatus
                              )}
                              size="sm"
                            >
                              {registration.paymentStatus
                                .charAt(0)
                                .toUpperCase() +
                                registration.paymentStatus.slice(1)}
                            </Chip>
                          </div>
                        </div>
                      </CardHeader>
                      <CardBody>
                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                          <div>
                            <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                              <FiUser className="w-4 h-4" />
                              Registration Details
                            </h4>
                            <div className="space-y-1 iepa-body-small">
                              <p>
                                <strong>Type:</strong>{' '}
                                {getTypeDisplayName(registration.type)}
                              </p>
                              <p>
                                <strong>Status:</strong> {registration.status}
                              </p>
                              <p>
                                <strong>Payment:</strong>{' '}
                                {registration.paymentStatus}
                              </p>
                            </div>
                          </div>

                          <div>
                            <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                              <FiCalendar className="w-4 h-4" />
                              Dates
                            </h4>
                            <div className="space-y-1 iepa-body-small">
                              <p>
                                <strong>Registered:</strong>{' '}
                                {formatDate(new Date(registration.createdAt))}
                              </p>
                              <p>
                                <strong>Last Updated:</strong>{' '}
                                {formatDate(new Date(registration.updatedAt))}
                              </p>
                            </div>
                          </div>

                          <div>
                            <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                              <FiCreditCard className="w-4 h-4" />
                              Payment
                            </h4>
                            <div className="space-y-1 iepa-body-small">
                              <p>
                                <strong>Amount:</strong>{' '}
                                {formatCurrency(registration.amount)}
                              </p>
                              <p>
                                <strong>Status:</strong>{' '}
                                <span
                                  className={`font-medium ${
                                    registration.paymentStatus === 'completed'
                                      ? 'text-green-600'
                                      : registration.paymentStatus === 'pending'
                                        ? 'text-yellow-600'
                                        : 'text-red-600'
                                  }`}
                                >
                                  {registration.paymentStatus}
                                </span>
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Enhanced Registration Details - Only show for attendee registrations */}
                        {registration.type === 'attendee' &&
                          registrations?.attendee && (
                            <div className="mt-6 pt-6 border-t border-gray-200">
                              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {/* Contact Information */}
                                <div>
                                  <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                                    <FiUser className="w-4 h-4" />
                                    Contact Information
                                  </h4>
                                  <div className="space-y-1 iepa-body-small">
                                    <p>
                                      <strong>Name:</strong>{' '}
                                      {`${registrations.attendee.personalInfo.firstName} ${registrations.attendee.personalInfo.lastName}`.trim()}
                                    </p>
                                    <p>
                                      <strong>Email:</strong>{' '}
                                      {
                                        registrations.attendee.personalInfo
                                          .email
                                      }
                                    </p>
                                    {registrations.attendee.personalInfo
                                      .phone && (
                                      <p>
                                        <strong>Phone:</strong>{' '}
                                        {
                                          registrations.attendee.personalInfo
                                            .phone
                                        }
                                      </p>
                                    )}
                                    {registrations.attendee.personalInfo
                                      .organization && (
                                      <p>
                                        <strong>Organization:</strong>{' '}
                                        {
                                          registrations.attendee.personalInfo
                                            .organization
                                        }
                                      </p>
                                    )}
                                    {registrations.attendee.personalInfo
                                      .title && (
                                      <p>
                                        <strong>Title:</strong>{' '}
                                        {
                                          registrations.attendee.personalInfo
                                            .title
                                        }
                                      </p>
                                    )}
                                  </div>
                                </div>

                                {/* Event Participation */}
                                <div>
                                  <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                                    ⛳ Event Participation
                                  </h4>
                                  <div className="space-y-1 iepa-body-small">
                                    <p>
                                      <strong>Golf Tournament:</strong>{' '}
                                      <span
                                        className={
                                          registrations.attendee
                                            .eventParticipation.attendingGolf
                                            ? 'text-green-600'
                                            : 'text-gray-500'
                                        }
                                      >
                                        {registrations.attendee
                                          .eventParticipation.attendingGolf
                                          ? 'Yes'
                                          : 'No'}
                                      </span>
                                    </p>
                                    {registrations.attendee.eventParticipation
                                      .attendingGolf && (
                                      <>
                                        <p>
                                          <strong>Club Rental:</strong>{' '}
                                          <span
                                            className={
                                              registrations.attendee
                                                .eventParticipation
                                                .golfClubRental
                                                ? 'text-green-600'
                                                : 'text-gray-500'
                                            }
                                          >
                                            {registrations.attendee
                                              .eventParticipation.golfClubRental
                                              ? 'Yes'
                                              : 'No'}
                                          </span>
                                        </p>
                                        {registrations.attendee
                                          .eventParticipation.golfClubRental &&
                                          registrations.attendee
                                            .eventParticipation
                                            .golfClubHandedness && (
                                            <p>
                                              <strong>Club Handedness:</strong>{' '}
                                              {
                                                registrations.attendee
                                                  .eventParticipation
                                                  .golfClubHandedness
                                              }
                                            </p>
                                          )}
                                      </>
                                    )}
                                  </div>
                                </div>

                                {/* Payment Details */}
                                <div>
                                  <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                                    <FiCreditCard className="w-4 h-4" />
                                    Payment Details
                                  </h4>
                                  <div className="space-y-1 iepa-body-small">
                                    <p>
                                      <strong>Registration:</strong>{' '}
                                      {formatCurrency(
                                        registrations.attendee.financial
                                          .registrationFee || 0
                                      )}
                                    </p>
                                    {(registrations.attendee.financial
                                      .golfFee || 0) > 0 && (
                                      <p>
                                        <strong>Golf Tournament:</strong>{' '}
                                        {formatCurrency(
                                          registrations.attendee.financial
                                            .golfFee || 0
                                        )}
                                      </p>
                                    )}
                                    {(registrations.attendee.financial
                                      .golfClubRentalFee || 0) > 0 && (
                                      <p>
                                        <strong>Club Rental:</strong>{' '}
                                        {formatCurrency(
                                          registrations.attendee.financial
                                            .golfClubRentalFee || 0
                                        )}
                                      </p>
                                    )}
                                    <p className="pt-1 border-t border-gray-200">
                                      <strong>Total:</strong>{' '}
                                      {formatCurrency(
                                        registrations.attendee.financial
                                          .grandTotal || 0
                                      )}
                                    </p>
                                  </div>
                                </div>
                              </div>

                              {/* Meal Selections */}
                              {registrations.attendee.eventParticipation
                                ?.mealSelections &&
                                (() => {
                                  const meals =
                                    registrations.attendee.eventParticipation
                                      .mealSelections;
                                  const hasMeals = Array.isArray(meals)
                                    ? meals.length > 0
                                    : typeof meals === 'object' &&
                                        meals !== null
                                      ? Object.keys(meals).length > 0
                                      : false;

                                  if (!hasMeals) return null;

                                  return (
                                    <div className="mt-6 pt-6 border-t border-gray-200">
                                      <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                                        🍽️ Meal Selections
                                      </h4>
                                      <div className="space-y-1 iepa-body-small">
                                        {Array.isArray(meals)
                                          ? meals.map(mealId => (
                                              <p
                                                key={mealId}
                                                className="flex items-center gap-2"
                                              >
                                                <span className="text-green-600">
                                                  ✓
                                                </span>
                                                <strong>
                                                  {formatMealName(mealId)}
                                                </strong>
                                              </p>
                                            ))
                                          : Object.entries(meals || {})
                                              .filter(
                                                ([, selected]) => selected
                                              )
                                              .map(([mealId]) => (
                                                <p
                                                  key={mealId}
                                                  className="flex items-center gap-2"
                                                >
                                                  <span className="text-green-600">
                                                    ✓
                                                  </span>
                                                  <strong>
                                                    {formatMealName(mealId)}
                                                  </strong>
                                                </p>
                                              ))}
                                        {registrations.attendee
                                          .registrationDetails
                                          ?.dietaryRestrictions && (
                                          <p className="mt-2 pt-2 border-t border-gray-100">
                                            <strong>Dietary Needs:</strong>{' '}
                                            {
                                              registrations.attendee
                                                .registrationDetails
                                                .dietaryRestrictions
                                            }
                                          </p>
                                        )}
                                      </div>
                                    </div>
                                  );
                                })()}

                              {/* Transaction Information */}
                              <div className="mt-6 pt-6 border-t border-gray-200">
                                <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                                  🧾 Transaction Information
                                </h4>
                                <div className="space-y-1 iepa-body-small">
                                  <p>
                                    <strong>Registration ID:</strong>{' '}
                                    {registration.id}
                                  </p>
                                  {/* We'll need to get the payment ID from the payments data */}
                                  {registrations.payments &&
                                    Array.isArray(registrations.payments) &&
                                    registrations.payments.length > 0 && (
                                      <>
                                        <p>
                                          <strong>Transaction ID:</strong>{' '}
                                          {registrations.payments[0]
                                            ?.transactionId ||
                                            registrations.payments[0]
                                              ?.stripePaymentIntentId ||
                                            'N/A'}
                                        </p>
                                        <p>
                                          <strong>Payment Method:</strong>{' '}
                                          {registrations.payments[0]
                                            ?.paymentMethod || 'Card'}
                                        </p>
                                      </>
                                    )}
                                </div>
                              </div>
                            </div>
                          )}

                        {/* Enhanced Registration Details - Only show for speaker registrations */}
                        {registration.type === 'speaker' &&
                          registrations?.speaker && (
                            <div className="mt-6 pt-6 border-t border-gray-200">
                              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {/* Speaker Information */}
                                <div>
                                  <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                                    <FiUser className="w-4 h-4" />
                                    Speaker Information
                                  </h4>
                                  <div className="space-y-1 iepa-body-small">
                                    <p>
                                      <strong>Name:</strong>{' '}
                                      {`${registrations.speaker.personalInfo.firstName} ${registrations.speaker.personalInfo.lastName}`.trim()}
                                    </p>
                                    <p>
                                      <strong>Email:</strong>{' '}
                                      {registrations.speaker.personalInfo.email}
                                    </p>
                                    {registrations.speaker.personalInfo
                                      .phone && (
                                      <p>
                                        <strong>Phone:</strong>{' '}
                                        {
                                          registrations.speaker.personalInfo
                                            .phone
                                        }
                                      </p>
                                    )}
                                    {registrations.speaker.personalInfo
                                      .organization && (
                                      <p>
                                        <strong>Organization:</strong>{' '}
                                        {
                                          registrations.speaker.personalInfo
                                            .organization
                                        }
                                      </p>
                                    )}
                                    {registrations.speaker.personalInfo
                                      .title && (
                                      <p>
                                        <strong>Title:</strong>{' '}
                                        {
                                          registrations.speaker.personalInfo
                                            .title
                                        }
                                      </p>
                                    )}
                                  </div>
                                </div>

                                {/* Presentation Details */}
                                <div>
                                  <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                                    🎤 Presentation Details
                                  </h4>
                                  <div className="space-y-1 iepa-body-small">
                                    {registrations.speaker.speakerInfo
                                      ?.presentationTitle && (
                                      <p>
                                        <strong>Title:</strong>{' '}
                                        {
                                          registrations.speaker.speakerInfo
                                            .presentationTitle
                                        }
                                      </p>
                                    )}
                                    {registrations.speaker.speakerInfo
                                      ?.presentationDescription && (
                                      <p>
                                        <strong>Description:</strong>{' '}
                                        {
                                          registrations.speaker.speakerInfo
                                            .presentationDescription
                                        }
                                      </p>
                                    )}
                                    {registrations.speaker.speakerInfo?.bio && (
                                      <p>
                                        <strong>Bio:</strong>{' '}
                                        {registrations.speaker.speakerInfo.bio
                                          .length > 100
                                          ? `${registrations.speaker.speakerInfo.bio.substring(0, 100)}...`
                                          : registrations.speaker.speakerInfo
                                              .bio}
                                      </p>
                                    )}
                                  </div>
                                </div>

                                {/* Files & Media */}
                                <div>
                                  <h4 className="iepa-body font-semibold mb-3 flex items-center gap-2">
                                    📁 Files & Media
                                  </h4>

                                  {/* Guidance Text */}
                                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                    <p className="text-sm text-blue-800 mb-2">
                                      <strong>
                                        📋 Speaker File Management:
                                      </strong>
                                    </p>
                                    <ul className="text-xs text-blue-700 space-y-1 ml-4">
                                      <li>
                                        • Upload or update your presentation
                                        file (PDF, PowerPoint, Word)
                                      </li>
                                      <li>
                                        • Add or change your professional
                                        headshot for conference materials
                                      </li>
                                      <li>
                                        • Use the &quot;Upload&quot; buttons
                                        below to add/replace files anytime
                                      </li>
                                      <li>
                                        • Files are automatically saved and will
                                        appear in conference programs
                                      </li>
                                    </ul>
                                  </div>

                                  <div className="space-y-4 iepa-body-small">
                                    {/* Presentation File */}
                                    <div className="p-3 bg-gray-50 rounded-lg border">
                                      <div className="flex items-start justify-between gap-3">
                                        <div className="flex items-center gap-3 flex-1">
                                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <FiFile className="w-5 h-5 text-blue-600" />
                                          </div>
                                          <div className="flex-1 min-w-0">
                                            <p className="font-medium text-gray-900">
                                              Presentation File
                                            </p>
                                            {registrations.speaker.speakerInfo
                                              ?.presentationFileUrl ? (
                                              <p className="text-xs text-green-600 flex items-center gap-1">
                                                ✓ Uploaded • PDF/PowerPoint
                                              </p>
                                            ) : (
                                              <p className="text-xs text-gray-500">
                                                Not uploaded
                                              </p>
                                            )}
                                          </div>
                                        </div>
                                        {registrations.speaker.speakerInfo
                                          ?.presentationFileUrl && (
                                          <div className="flex gap-1">
                                            <Button
                                              as="a"
                                              href={
                                                registrations.speaker
                                                  .speakerInfo
                                                  .presentationFileUrl
                                              }
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              size="sm"
                                              variant="outline"
                                              className="h-8 px-2 text-xs bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700"
                                            >
                                              <FiEye className="w-3 h-3" />
                                            </Button>
                                            <Button
                                              as="a"
                                              href={
                                                registrations.speaker
                                                  .speakerInfo
                                                  .presentationFileUrl
                                              }
                                              download
                                              size="sm"
                                              variant="outline"
                                              className="h-8 px-2 text-xs bg-green-50 hover:bg-green-100 border-green-200 text-green-700"
                                            >
                                              <FiDownload className="w-3 h-3" />
                                            </Button>
                                          </div>
                                        )}
                                      </div>
                                    </div>

                                    {/* Professional Headshot */}
                                    <div className="p-3 bg-gray-50 rounded-lg border">
                                      <div className="flex items-start justify-between gap-3">
                                        <div className="flex items-center gap-3 flex-1">
                                          {registrations.speaker.speakerInfo
                                            ?.headshotUrl ? (
                                            <div className="w-10 h-10 rounded-lg overflow-hidden border">
                                              <Image
                                                src={
                                                  registrations.speaker
                                                    .speakerInfo.headshotUrl
                                                }
                                                alt="Professional headshot"
                                                width={40}
                                                height={40}
                                                className="w-full h-full object-cover cursor-pointer hover:opacity-80 transition-opacity"
                                                onClick={() => {
                                                  if (
                                                    registrations.speaker
                                                      ?.speakerInfo?.headshotUrl
                                                  ) {
                                                    window.open(
                                                      registrations.speaker
                                                        .speakerInfo
                                                        .headshotUrl,
                                                      '_blank'
                                                    );
                                                  }
                                                }}
                                              />
                                            </div>
                                          ) : (
                                            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                              <FiImage className="w-5 h-5 text-purple-600" />
                                            </div>
                                          )}
                                          <div className="flex-1 min-w-0">
                                            <p className="font-medium text-gray-900">
                                              Professional Headshot
                                            </p>
                                            {registrations.speaker.speakerInfo
                                              ?.headshotUrl ? (
                                              <p className="text-xs text-green-600 flex items-center gap-1">
                                                ✓ Uploaded • Click thumbnail to
                                                view
                                              </p>
                                            ) : (
                                              <p className="text-xs text-gray-500">
                                                Not uploaded
                                              </p>
                                            )}
                                          </div>
                                        </div>
                                        {registrations.speaker.speakerInfo
                                          ?.headshotUrl && (
                                          <div className="flex gap-1">
                                            <Button
                                              as="a"
                                              href={
                                                registrations.speaker
                                                  .speakerInfo.headshotUrl
                                              }
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              size="sm"
                                              variant="outline"
                                              className="h-8 px-2 text-xs bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700"
                                            >
                                              <FiEye className="w-3 h-3" />
                                            </Button>
                                            <Button
                                              as="a"
                                              href={
                                                registrations.speaker
                                                  .speakerInfo.headshotUrl
                                              }
                                              download
                                              size="sm"
                                              variant="outline"
                                              className="h-8 px-2 text-xs bg-green-50 hover:bg-green-100 border-green-200 text-green-700"
                                            >
                                              <FiDownload className="w-3 h-3" />
                                            </Button>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Transaction Information */}
                              <div className="mt-6 pt-6 border-t border-gray-200">
                                <h4 className="iepa-body font-semibold mb-2 flex items-center gap-2">
                                  🧾 Transaction Information
                                </h4>
                                <div className="space-y-1 iepa-body-small">
                                  <p>
                                    <strong>Registration ID:</strong>{' '}
                                    {registration.id}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}

                        <div className="flex flex-col sm:flex-row gap-3 mt-6">
                          <Button
                            size="sm"
                            variant="bordered"
                            className="flex items-center gap-2"
                          >
                            <FiEye className="w-4 h-4" />
                            View Details
                          </Button>

                          {registration.canEdit &&
                            allowProfileEdits &&
                            !settingsLoading && (
                              <Button
                                as={Link}
                                href={`/my-registrations/edit/${registration.type}/${registration.id}`}
                                size="sm"
                                variant="bordered"
                                className="flex items-center gap-2"
                              >
                                <FiEdit className="w-4 h-4" />
                                Edit Registration
                              </Button>
                            )}

                          {/* Show message when edits are disabled by admin */}
                          {registration.canEdit &&
                            !allowProfileEdits &&
                            !settingsLoading && (
                              <div className="text-sm text-gray-500 italic">
                                Profile editing is currently disabled by
                                administrator
                              </div>
                            )}

                          {/* Speaker File Upload Buttons - Only show for speaker registrations */}
                          {registration.type === 'speaker' &&
                            registrations?.speaker && (
                              <div className="flex flex-col gap-2">
                                <div className="text-xs text-gray-600 italic">
                                  💡 Upload or replace your presentation and
                                  headshot files:
                                </div>
                                <div className="flex flex-wrap gap-2">
                                  <SpeakerFileUploadButton
                                    speakerId={registration.id}
                                    fileType="presentation"
                                    currentFileUrl={
                                      registrations.speaker.speakerInfo
                                        ?.presentationFileUrl
                                    }
                                    onUploadSuccess={refresh}
                                  />
                                  <SpeakerFileUploadButton
                                    speakerId={registration.id}
                                    fileType="headshot"
                                    currentFileUrl={
                                      registrations.speaker.speakerInfo
                                        ?.headshotUrl
                                    }
                                    onUploadSuccess={refresh}
                                  />
                                </div>
                              </div>
                            )}

                          {registration.hasReceipt && (
                            <PDFDownloadButton
                              registrationId={registration.id}
                              registrationType={registration.type}
                              documentType="receipt"
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-2"
                            />
                          )}

                          {/* Always show Download Invoice button for completed payments */}
                          {registration.paymentStatus === 'completed' && (
                            <PDFDownloadButton
                              registrationId={registration.id}
                              registrationType={registration.type}
                              documentType="invoice"
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-2"
                            />
                          )}

                          {registration.paymentStatus === 'pending' && (
                            <Button
                              size="sm"
                              color="primary"
                              className="flex items-center gap-2"
                              onClick={() =>
                                handleCompletePayment(registration)
                              }
                              disabled={processingPayment === registration.id}
                            >
                              <FiCreditCard className="w-4 h-4" />
                              {processingPayment === registration.id
                                ? 'Processing...'
                                : 'Complete Payment'}
                            </Button>
                          )}

                          {/* Golf Add-On Button - Only show for completed attendee registrations */}
                          {registration.type === 'attendee' &&
                            registration.paymentStatus === 'completed' &&
                            registrations?.attendee && (
                              <GolfAddOnButton
                                registration={registrations.attendee}
                                onSuccess={refresh}
                                variant="outline"
                                size="sm"
                              />
                            )}
                        </div>
                      </CardBody>
                    </Card>
                  ))
                ) : (
                  <Card>
                    <CardBody>
                      <div className="text-center py-8">
                        <p className="text-gray-600">
                          No registration data available.
                        </p>
                        <Button
                          onClick={refresh}
                          variant="bordered"
                          size="sm"
                          className="mt-4"
                        >
                          Refresh
                        </Button>
                      </div>
                    </CardBody>
                  </Card>
                )}
              </div>
            )}
          </div>
        </section>

        {/* Help Section */}
        <section
          className="iepa-section"
          style={{ backgroundColor: 'var(--iepa-gray-50)' }}
        >
          <div className="max-w-4xl mx-auto">
            <h2 className="iepa-heading-2 text-center mb-8">Need Help?</h2>

            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <h3 className="iepa-heading-3">Registration Support</h3>
                </CardHeader>
                <CardBody>
                  <p className="iepa-body mb-4">
                    Having trouble with your registration or need to make
                    changes?
                  </p>
                  <Button
                    as={Link}
                    href="/contact"
                    variant="bordered"
                    size="sm"
                  >
                    Contact Support
                  </Button>
                </CardBody>
              </Card>

              <Card>
                <CardHeader>
                  <h3 className="iepa-heading-3">Conference Information</h3>
                </CardHeader>
                <CardBody>
                  <p className="iepa-body mb-4">
                    Get details about the conference schedule, venue, and
                    activities.
                  </p>
                  <Button as={Link} href="/about" variant="bordered" size="sm">
                    Conference Details
                  </Button>
                </CardBody>
              </Card>
            </div>
          </div>
        </section>

        {/* Navigation */}
        <section className="iepa-section text-center">
          <div className="max-w-2xl mx-auto">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button as={Link} href="/dashboard" variant="bordered" size="lg">
                Back to Dashboard
              </Button>
              <Button as={Link} href="/register" color="primary" size="lg">
                New Registration
              </Button>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
