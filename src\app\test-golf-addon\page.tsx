'use client';

// Golf Add-On Test Page
// Test page for golf add-on functionality

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Alert,
  AlertDescription
} from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import { checkGolfAddOnEligibility } from '@/services/golfAddOn';
import type { GolfAddOnEligibility } from '@/types/golfAddOn';

export default function TestGolfAddOnPage() {
  const { user } = useAuth();
  const [eligibility, setEligibility] = useState<GolfAddOnEligibility | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testEligibility = async () => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      console.log('🏌️ Testing golf add-on eligibility for user:', user.id);
      
      const result = await checkGolfAddOnEligibility(user.id);
      setEligibility(result);
      
      console.log('✅ Eligibility result:', result);
    } catch (err) {
      console.error('❌ Error testing eligibility:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testApiEndpoint = async (endpoint: string) => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      console.log(`🧪 Testing API endpoint: ${endpoint}`);
      
      const response = await fetch(`/api/golf-addon/${endpoint}?userId=${user.id}`);
      const result = await response.json();
      
      console.log(`✅ API ${endpoint} result:`, result);
      
      if (endpoint === 'eligibility') {
        setEligibility(result.data);
      }
    } catch (err) {
      console.error(`❌ Error testing ${endpoint}:`, err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <h1 className="iepa-heading-1 mb-8">Golf Add-On Test Page</h1>
          
          {!user && (
            <Alert>
              <AlertDescription>
                Please log in to test golf add-on functionality.
              </AlertDescription>
            </Alert>
          )}

          {user && (
            <div className="space-y-6">
              {/* User Info */}
              <Card>
                <CardHeader>
                  <CardTitle>User Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p><strong>User ID:</strong> {user.id}</p>
                    <p><strong>Email:</strong> {user.email}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Test Controls */}
              <Card>
                <CardHeader>
                  <CardTitle>Test Controls</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-3">
                    <Button 
                      onClick={testEligibility}
                      disabled={loading}
                    >
                      Test Service Function
                    </Button>
                    
                    <Button 
                      onClick={() => testApiEndpoint('eligibility')}
                      disabled={loading}
                      variant="outline"
                    >
                      Test API Endpoint
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Error Display */}
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Eligibility Results */}
              {eligibility && (
                <Card>
                  <CardHeader>
                    <CardTitle>Golf Add-On Eligibility Results</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="font-semibold">Eligible:</p>
                          <p className={eligibility.eligible ? 'text-green-600' : 'text-red-600'}>
                            {eligibility.eligible ? 'Yes' : 'No'}
                          </p>
                        </div>
                        
                        <div>
                          <p className="font-semibold">Has Existing Golf:</p>
                          <p className={eligibility.hasExistingGolf ? 'text-yellow-600' : 'text-gray-600'}>
                            {eligibility.hasExistingGolf ? 'Yes' : 'No'}
                          </p>
                        </div>
                        
                        <div>
                          <p className="font-semibold">Can Add Golf:</p>
                          <p className={eligibility.canAddGolf ? 'text-green-600' : 'text-gray-600'}>
                            {eligibility.canAddGolf ? 'Yes' : 'No'}
                          </p>
                        </div>
                        
                        <div>
                          <p className="font-semibold">Can Add Club Rental:</p>
                          <p className={eligibility.canAddClubRental ? 'text-green-600' : 'text-gray-600'}>
                            {eligibility.canAddClubRental ? 'Yes' : 'No'}
                          </p>
                        </div>
                      </div>
                      
                      {eligibility.reason && (
                        <div>
                          <p className="font-semibold">Reason:</p>
                          <p className="text-gray-600">{eligibility.reason}</p>
                        </div>
                      )}
                      
                      {eligibility.currentRegistration && (
                        <div>
                          <p className="font-semibold">Current Registration:</p>
                          <div className="bg-gray-50 p-3 rounded text-sm">
                            <p><strong>ID:</strong> {eligibility.currentRegistration.id}</p>
                            <p><strong>Name:</strong> {eligibility.currentRegistration.full_name}</p>
                            <p><strong>Golf Tournament:</strong> {eligibility.currentRegistration.attending_golf ? 'Yes' : 'No'}</p>
                            <p><strong>Club Rental:</strong> {eligibility.currentRegistration.golf_club_rental ? 'Yes' : 'No'}</p>
                            <p><strong>Payment Status:</strong> {eligibility.currentRegistration.payment_status}</p>
                            <p><strong>Grand Total:</strong> ${eligibility.currentRegistration.grand_total}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Instructions */}
              <Card>
                <CardHeader>
                  <CardTitle>Test Instructions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <p>1. Make sure you have a completed attendee registration</p>
                    <p>2. Click &quot;Test Service Function&quot; to test the service layer</p>
                    <p>3. Click &quot;Test API Endpoint&quot; to test the API layer</p>
                    <p>4. Check the browser console for detailed logs</p>
                    <p>5. If eligible, go to &quot;My Registrations&quot; to see the golf add-on button</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
