{"$schema": "http://json-schema.org/draft-07/schema#", "title": "IEPA 2025 Sponsor Registration Schema", "description": "Form schema for IEPA 2025 Conference sponsor registration", "type": "object", "properties": {"sponsorInfo": {"type": "object", "title": "Sponsor Information", "properties": {"sponsorName": {"type": "string", "title": "Sponsor Name", "description": "Please enter your organization name as you wish to be published in the IEPA Annual Meeting Attendees Roster and meeting materials.", "minLength": 1}, "sponsorUrl": {"type": "string", "title": "Sponsor URL", "description": "Please share the link of your organization's website (must include HTTP:// or HTTPS://)", "format": "uri", "pattern": "^https?://.+"}, "sponsorVideo": {"type": "string", "title": "Sponsor Video", "description": "Please link to a video you would like showcased (optional)", "format": "uri"}, "sponsorImage": {"type": "string", "title": "Company Logo", "description": "Please upload your company logo (JPG, PNG, SVG, max 10MB)", "format": "uri", "_validation": {"allowedTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/webp"], "maxSize": 10485760, "required": true}}, "sponsorDescription": {"type": "string", "title": "Company Description", "description": "Please add a description of your company or organization (50-1000 characters).", "minLength": 50, "maxLength": 1000}}, "required": ["sponsorName", "sponsorUrl", "sponsorImage", "sponsorDescription"]}, "contactInfo": {"type": "object", "title": "Primary Contact Information", "properties": {"contactName": {"type": "string", "title": "Primary Contact Name", "description": "Name of the primary contact person for this sponsorship", "minLength": 1}, "contactEmail": {"type": "string", "title": "Contact Email", "description": "Primary email for sponsorship communications", "format": "email"}, "contactPhone": {"type": "string", "title": "Contact Phone", "description": "Phone number for primary contact", "pattern": "^[\\d\\s\\-\\(\\)\\+\\.]+$"}, "contactTitle": {"type": "string", "title": "Contact Title", "description": "Job title of the primary contact person"}}, "required": ["contactName", "contactEmail", "contactPhone", "contactTitle"]}, "sponsorshipLevel": {"type": "string", "title": "Sponsorship Level", "description": "Select your sponsorship package", "enum": ["bronze-sponsor", "silver-sponsor", "gold-sponsor", "platinum-sponsor", "diamond-sponsor"], "enumNames": ["Bronze Sponsor ($5,150) - 1 registration included", "Silver Sponsor ($10,300) - 2 registrations included", "Gold Sponsor ($15,450) - 3 registrations included", "Platinum Sponsor ($20,600) - 4 registrations included", "Diamond Sponsor ($25,750) - 5 registrations included"]}, "calculatedFields": {"type": "object", "title": "Calculated Fields", "description": "These fields are calculated automatically based on sponsorship level", "properties": {"sponsorshipTotal": {"type": "number", "title": "Sponsorship Total", "description": "Calculated based on sponsorship level from pricing configuration", "minimum": 0, "default": 0}, "includedRegistrations": {"type": "number", "title": "Included Registrations", "description": "Number of complimentary registrations included with this sponsorship level", "minimum": 0, "default": 0}, "sponsorshipBenefits": {"type": "array", "title": "Sponsorship Benefits", "description": "List of benefits included with this sponsorship level", "items": {"type": "string"}, "default": []}}}}, "required": ["sponsorInfo", "contactInfo", "sponsorshipLevel"], "additionalProperties": false, "_metadata": {"version": "1.1.0", "lastUpdated": "2025-01-30", "conferenceYear": 2025, "notes": "Updated for Task 2.3: Updated sponsorship levels to match pricing config, added contact information, enhanced file validation", "dependencies": {"pricingConfig": "src/lib/pricing-config.ts", "schemaUtils": "src/utils/schema-utils.ts"}, "validationRules": {"sponsorshipLevel": "Must match SPONSORSHIP_PACKAGES enum values", "contactEmail": "Must be valid email format", "contactPhone": "Must match phone pattern", "sponsorUrl": "Must be valid HTTP/HTTPS URL", "sponsorImage": "JPG, PNG, SVG, WebP only, max 10MB", "sponsorDescription": "50-1000 characters required"}, "fileUploadLimits": {"sponsorImage": {"maxSize": "10MB", "allowedTypes": ["JPG", "PNG", "SVG", "WebP"]}}, "sponsorshipLevels": {"bronze": "$5,150 - 1 registration", "silver": "$10,300 - 2 registrations", "gold": "$15,450 - 3 registrations", "platinum": "$20,600 - 4 registrations", "diamond": "$25,750 - 5 registrations"}}}