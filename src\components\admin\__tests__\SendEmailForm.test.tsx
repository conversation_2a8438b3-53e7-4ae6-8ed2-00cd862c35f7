import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SendEmailForm from '../SendEmailForm';

// Mock the UI components
jest.mock('@/components/ui', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardHeader: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardBody: ({ children, className }: any) => <div className={className}>{children}</div>,
  Input: ({ value, onChange, placeholder, required, ...props }: any) => (
    <input 
      value={value} 
      onChange={onChange} 
      placeholder={placeholder} 
      required={required}
      {...props}
    />
  ),
  Textarea: ({ value, onChange, placeholder, rows, required }: any) => (
    <textarea 
      value={value} 
      onChange={onChange} 
      placeholder={placeholder} 
      rows={rows}
      required={required}
    />
  ),
  Button: ({ children, onClick, disabled, className, type }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      className={className} 
      type={type}
    >
      {children}
    </button>
  ),
  Badge: ({ children, className }: any) => <span className={className}>{children}</span>,
}));

jest.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      <select onChange={(e) => onValueChange(e.target.value)} value={value}>
        {children}
      </select>
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => (
    <option value={value}>{children}</option>
  ),
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: () => <span>Select value</span>,
}));

// Mock React Icons
jest.mock('react-icons/fi', () => ({
  FiSend: () => <span data-testid="send-icon" />,
  FiUsers: () => <span data-testid="users-icon" />,
  FiMail: () => <span data-testid="mail-icon" />,
  FiType: () => <span data-testid="type-icon" />,
  FiMessageSquare: () => <span data-testid="message-icon" />,
  FiCheck: () => <span data-testid="check-icon" />,
  FiAlertCircle: () => <span data-testid="alert-icon" />,
}));

const mockOnSendEmail = jest.fn();

const defaultProps = {
  onSendEmail: mockOnSendEmail,
  loading: false,
};

describe('SendEmailForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders form with all required fields', () => {
    render(<SendEmailForm {...defaultProps} />);

    expect(screen.getByText('Send Email')).toBeInTheDocument();
    expect(screen.getByText('Recipients *')).toBeInTheDocument();
    expect(screen.getByText('Email Type')).toBeInTheDocument();
    expect(screen.getByText('Subject *')).toBeInTheDocument();
    expect(screen.getByText('Message *')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Send Email/ })).toBeInTheDocument();
  });

  it('shows custom email input when custom recipients is selected', () => {
    render(<SendEmailForm {...defaultProps} />);

    // Simulate selecting custom recipients
    const recipientSelect = screen.getByTestId('select').querySelector('select');
    fireEvent.change(recipientSelect!, { target: { value: 'custom' } });

    expect(screen.getByText('Email Addresses')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter email addresses separated by commas or new lines...')).toBeInTheDocument();
  });

  it('validates required fields before submission', async () => {
    render(<SendEmailForm {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: /Send Email/ });
    
    // Try to submit without filling required fields
    fireEvent.click(submitButton);

    // Form should not be submitted (onSendEmail should not be called)
    expect(mockOnSendEmail).not.toHaveBeenCalled();
  });

  it('submits form with correct data when all fields are filled', async () => {
    mockOnSendEmail.mockResolvedValue(undefined);
    render(<SendEmailForm {...defaultProps} />);

    // Fill in the form
    const recipientSelect = screen.getByTestId('select').querySelector('select');
    fireEvent.change(recipientSelect!, { target: { value: 'all_attendees' } });

    const subjectInput = screen.getByPlaceholderText('Enter email subject...');
    fireEvent.change(subjectInput, { target: { value: 'Test Subject' } });

    const messageTextarea = screen.getByPlaceholderText('Enter your message...');
    fireEvent.change(messageTextarea, { target: { value: 'Test message content' } });

    // Submit the form
    const submitButton = screen.getByRole('button', { name: /Send Email/ });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnSendEmail).toHaveBeenCalledWith({
        recipients: 'all_attendees',
        subject: 'Test Subject',
        message: 'Test message content',
        emailType: 'announcement',
      });
    });
  });

  it('resets form after successful submission', async () => {
    mockOnSendEmail.mockResolvedValue(undefined);
    render(<SendEmailForm {...defaultProps} />);

    // Fill and submit form
    const subjectInput = screen.getByPlaceholderText('Enter email subject...');
    const messageTextarea = screen.getByPlaceholderText('Enter your message...');

    fireEvent.change(subjectInput, { target: { value: 'Test Subject' } });
    fireEvent.change(messageTextarea, { target: { value: 'Test message' } });

    const submitButton = screen.getByRole('button', { name: /Send Email/ });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(subjectInput).toHaveValue('');
      expect(messageTextarea).toHaveValue('');
    });
  });

  it('shows loading state when form is being submitted', () => {
    render(<SendEmailForm {...defaultProps} loading={true} />);

    const submitButton = screen.getByRole('button', { name: /Sending Email.../ });
    expect(submitButton).toBeDisabled();
    expect(screen.getByText('Sending Email...')).toBeInTheDocument();
  });

  it('disables submit button when required fields are empty', () => {
    render(<SendEmailForm {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: /Send Email/ });
    expect(submitButton).toBeDisabled();
  });

  it('enables submit button when all required fields are filled', () => {
    render(<SendEmailForm {...defaultProps} />);

    // Fill required fields
    const recipientSelect = screen.getByTestId('select').querySelector('select');
    fireEvent.change(recipientSelect!, { target: { value: 'all_attendees' } });

    const subjectInput = screen.getByPlaceholderText('Enter email subject...');
    fireEvent.change(subjectInput, { target: { value: 'Test Subject' } });

    const messageTextarea = screen.getByPlaceholderText('Enter your message...');
    fireEvent.change(messageTextarea, { target: { value: 'Test message' } });

    const submitButton = screen.getByRole('button', { name: /Send Email/ });
    expect(submitButton).not.toBeDisabled();
  });

  it('shows preview when toggle is clicked and message is filled', () => {
    render(<SendEmailForm {...defaultProps} />);

    const messageTextarea = screen.getByPlaceholderText('Enter your message...');
    fireEvent.change(messageTextarea, { target: { value: '<p>Test HTML message</p>' } });

    const previewToggle = screen.getByText('Show Preview');
    fireEvent.click(previewToggle);

    expect(screen.getByText('Hide Preview')).toBeInTheDocument();
    expect(screen.getByText('Preview:')).toBeInTheDocument();
  });

  it('hides preview when toggle is clicked again', () => {
    render(<SendEmailForm {...defaultProps} />);

    const messageTextarea = screen.getByPlaceholderText('Enter your message...');
    fireEvent.change(messageTextarea, { target: { value: 'Test message' } });

    const showPreviewButton = screen.getByText('Show Preview');
    fireEvent.click(showPreviewButton);

    const hidePreviewButton = screen.getByText('Hide Preview');
    fireEvent.click(hidePreviewButton);

    expect(screen.getByText('Show Preview')).toBeInTheDocument();
    expect(screen.queryByText('Preview:')).not.toBeInTheDocument();
  });

  it('shows bulk email warning for large recipient groups', () => {
    render(<SendEmailForm {...defaultProps} />);

    const recipientSelect = screen.getByTestId('select').querySelector('select');
    fireEvent.change(recipientSelect!, { target: { value: 'all_attendees' } });

    expect(screen.getByText('Bulk Email Warning')).toBeInTheDocument();
    expect(screen.getByText(/This will send emails to multiple recipients/)).toBeInTheDocument();
  });

  it('does not show bulk email warning for custom recipients', () => {
    render(<SendEmailForm {...defaultProps} />);

    const recipientSelect = screen.getByTestId('select').querySelector('select');
    fireEvent.change(recipientSelect!, { target: { value: 'custom' } });

    expect(screen.queryByText('Bulk Email Warning')).not.toBeInTheDocument();
  });

  it('handles form submission error gracefully', async () => {
    const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
    mockOnSendEmail.mockRejectedValue(new Error('Send failed'));
    
    render(<SendEmailForm {...defaultProps} />);

    // Fill and submit form
    const recipientSelect = screen.getByTestId('select').querySelector('select');
    fireEvent.change(recipientSelect!, { target: { value: 'all_attendees' } });

    const subjectInput = screen.getByPlaceholderText('Enter email subject...');
    fireEvent.change(subjectInput, { target: { value: 'Test Subject' } });

    const messageTextarea = screen.getByPlaceholderText('Enter your message...');
    fireEvent.change(messageTextarea, { target: { value: 'Test message' } });

    const submitButton = screen.getByRole('button', { name: /Send Email/ });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(consoleError).toHaveBeenCalledWith('Failed to send email:', expect.any(Error));
    });

    consoleError.mockRestore();
  });

  it('applies custom className', () => {
    const { container } = render(
      <SendEmailForm {...defaultProps} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('shows recipient description when recipient type is selected', () => {
    render(<SendEmailForm {...defaultProps} />);

    const recipientSelect = screen.getByTestId('select').querySelector('select');
    fireEvent.change(recipientSelect!, { target: { value: 'all_attendees' } });

    expect(screen.getByText('Send to all registered attendees')).toBeInTheDocument();
  });

  it('includes HTML formatting note for message field', () => {
    render(<SendEmailForm {...defaultProps} />);

    expect(screen.getByText('HTML formatting is supported. Use standard HTML tags for styling.')).toBeInTheDocument();
  });
});
