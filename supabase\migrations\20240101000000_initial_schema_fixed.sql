-- IEPA 2025 Annual Meeting Registration Database Schema
-- Run this in your Supabase SQL editor to set up the database

-- Create iepa_user_profiles table for user profile information
CREATE TABLE IF NOT EXISTS iepa_user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    phone_number TEXT,
    street_address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    organization TEXT,
    job_title TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_organizations table for organization management
CREATE TABLE IF NOT EXISTS iepa_organizations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    domain TEXT, -- Email domain for automatic matching
    organization_type TEXT CHECK (organization_type IN ('member', 'non-member', 'sponsor', 'government', 'academic')) DEFAULT 'non-member',
    website TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    -- Admin tracking
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_historical_registrations table for past event data
CREATE TABLE IF NOT EXISTS iepa_historical_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    profile_id UUID REFERENCES iepa_user_profiles(id) ON DELETE CASCADE,
    -- Event Information
    event_year INTEGER NOT NULL,
    event_name TEXT DEFAULT 'IEPA Annual Meeting',
    registration_date TIMESTAMP WITH TIME ZONE,
    -- Registration Details
    attendee_type TEXT NOT NULL,
    attendee_type_iepa TEXT,
    status TEXT NOT NULL,
    name_on_badge TEXT,
    -- Organization information (ADDED)
    organization TEXT,
    job_title TEXT,
    -- Lodging and Meals
    nights_staying TEXT,
    meals TEXT[],
    special_dietary_needs TEXT,
    -- Golf Information
    golf_participation BOOLEAN DEFAULT FALSE,
    golf_club_rental BOOLEAN DEFAULT FALSE,
    golf_club_handedness TEXT,
    -- Payment Information
    total_paid DECIMAL(10,2),
    payment_method TEXT,
    -- Additional data storage for flexibility
    original_data JSONB,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_speaker_registrations table (no foreign key dependencies)
CREATE TABLE IF NOT EXISTS iepa_speaker_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    -- Personal Information
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT NOT NULL,
    -- Contact Information
    phone_number TEXT,
    preferred_contact_method TEXT DEFAULT 'email',
    -- Professional Information
    organization_name TEXT NOT NULL,
    job_title TEXT NOT NULL,
    bio TEXT NOT NULL,
    -- Presentation Information
    presentation_title TEXT,
    presentation_description TEXT,
    presentation_duration TEXT,
    target_audience TEXT,
    learning_objectives TEXT,
    -- Speaking Experience
    speaker_experience TEXT,
    previous_speaking TEXT,
    -- Technical Requirements
    equipment_needs TEXT,
    special_requests TEXT,
    -- File Uploads
    presentation_file_url TEXT,
    headshot_url TEXT,
    speaker_pricing_type TEXT DEFAULT 'comped-speaker', -- 'comped-speaker' or 'full-meeting-speaker'
    -- PDF Documents
    receipt_url TEXT,
    receipt_generated_at TIMESTAMP WITH TIME ZONE,
    invoice_url TEXT,
    invoice_generated_at TIMESTAMP WITH TIME ZONE,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_sponsor_registrations table (no foreign key dependencies)
CREATE TABLE IF NOT EXISTS iepa_sponsor_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    sponsor_name TEXT NOT NULL,
    sponsor_url TEXT NOT NULL,
    sponsor_video TEXT,
    sponsor_image_url TEXT,
    sponsor_description TEXT NOT NULL,
    -- Attendee linking field
    linked_attendee_email TEXT, -- Email of sponsor's attendee registration
    -- Company domain for automatic discounts
    company_domain TEXT, -- Extracted from sponsor_url or manually set
    payment_status TEXT DEFAULT 'pending',
    payment_id TEXT,
    receipt_url TEXT,
    receipt_generated_at TIMESTAMP WITH TIME ZONE,
    invoice_url TEXT,
    invoice_generated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_attendee_registrations table (now with proper references)
CREATE TABLE IF NOT EXISTS iepa_attendee_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    registration_type TEXT NOT NULL,
    -- Attendee type and linking for spouse/child registrations
    attendee_type TEXT NOT NULL DEFAULT 'attendee' CHECK (attendee_type IN ('attendee', 'spouse', 'child')),
    linked_attendee_email TEXT, -- Email of primary attendee for spouse/child registrations
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    email TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    name_on_badge TEXT NOT NULL,
    gender TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    street_address TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    zip_code TEXT NOT NULL,
    organization TEXT NOT NULL,
    job_title TEXT NOT NULL,
    attending_golf BOOLEAN DEFAULT FALSE,
    golf_club_rental BOOLEAN DEFAULT FALSE,
    golf_club_handedness TEXT DEFAULT '',
    meals TEXT[] DEFAULT '{}',
    dietary_needs TEXT DEFAULT '',
    registration_total DECIMAL(10,2) DEFAULT 0,
    golf_total DECIMAL(10,2) DEFAULT 0,
    golf_club_rental_total DECIMAL(10,2) DEFAULT 0,
    meal_total DECIMAL(10,2) DEFAULT 0,
    grand_total DECIMAL(10,2) DEFAULT 0,
    -- Discount tracking
    discount_code TEXT,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    original_total DECIMAL(10,2) DEFAULT 0,
    payment_status TEXT DEFAULT 'pending',
    payment_id TEXT,
    receipt_url TEXT,
    receipt_generated_at TIMESTAMP WITH TIME ZONE,
    invoice_url TEXT,
    invoice_generated_at TIMESTAMP WITH TIME ZONE,
    -- Speaker linking fields
    speaker_registration_id UUID REFERENCES iepa_speaker_registrations(id) ON DELETE SET NULL,
    is_speaker BOOLEAN DEFAULT FALSE,
    speaker_pricing_type TEXT, -- 'comped-speaker' or 'full-meeting-speaker'
    -- Sponsor linking fields
    sponsor_id UUID REFERENCES iepa_sponsor_registrations(id) ON DELETE SET NULL,
    sponsor_discount_code TEXT, -- Track which sponsor discount code was used
    is_sponsor_attendee BOOLEAN DEFAULT FALSE, -- Flag for sponsor-linked attendees
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add the reverse reference to speaker registrations
ALTER TABLE iepa_speaker_registrations 
ADD COLUMN attendee_registration_id UUID REFERENCES iepa_attendee_registrations(id) ON DELETE SET NULL;

-- Create iepa_payments table
CREATE TABLE IF NOT EXISTS iepa_payments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    registration_id UUID NOT NULL,
    registration_type TEXT NOT NULL CHECK (registration_type IN ('attendee', 'sponsor')),
    stripe_payment_intent_id TEXT NOT NULL UNIQUE,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'usd',
    status TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_admin_users table for admin access control
CREATE TABLE IF NOT EXISTS iepa_admin_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL UNIQUE,
    role TEXT NOT NULL CHECK (role IN ('admin', 'super_admin')) DEFAULT 'admin',
    permissions JSONB NOT NULL DEFAULT '{"dashboard": true, "reports": true}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_discount_codes table for managing discount codes
CREATE TABLE IF NOT EXISTS iepa_discount_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    description TEXT,
    -- Discount configuration
    discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')) DEFAULT 'percentage',
    discount_value DECIMAL(10,2) NOT NULL CHECK (discount_value > 0),
    -- Stripe integration
    stripe_coupon_id TEXT UNIQUE,
    -- Usage limits
    max_uses INTEGER,
    max_uses_per_user INTEGER DEFAULT 1,
    current_uses INTEGER DEFAULT 0,
    -- Validity period
    valid_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE,
    -- Restrictions
    minimum_amount DECIMAL(10,2),
    applicable_registration_types TEXT[], -- ['attendee', 'sponsor'] or specific types
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    -- Admin tracking
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_discount_usage table for tracking discount code usage
CREATE TABLE IF NOT EXISTS iepa_discount_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    discount_code_id UUID REFERENCES iepa_discount_codes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    registration_id UUID, -- Can reference any registration table
    registration_type TEXT NOT NULL CHECK (registration_type IN ('attendee', 'sponsor', 'speaker')),
    -- Usage details
    original_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    final_amount DECIMAL(10,2) NOT NULL,
    -- Stripe tracking
    stripe_session_id TEXT,
    stripe_payment_intent_id TEXT,
    -- Timestamps
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create iepa_sponsor_domains table for email domain-based automatic discounts
CREATE TABLE IF NOT EXISTS iepa_sponsor_domains (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sponsor_id UUID REFERENCES iepa_sponsor_registrations(id) ON DELETE CASCADE,
    domain TEXT NOT NULL, -- e.g., 'acme.com'
    sponsor_name TEXT NOT NULL, -- Company name for reference
    -- Discount configuration
    discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')) DEFAULT 'percentage',
    discount_value DECIMAL(10,2) NOT NULL DEFAULT 100, -- Default 100% discount for sponsor attendees
    -- Auto-generated discount code
    auto_discount_code TEXT UNIQUE, -- Automatically generated code for this domain
    stripe_coupon_id TEXT, -- Associated Stripe coupon
    -- Status and limits
    is_active BOOLEAN DEFAULT TRUE,
    max_uses INTEGER, -- Optional limit on domain-based discounts
    current_uses INTEGER DEFAULT 0,
    -- Admin tracking
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Constraints
    UNIQUE(domain, sponsor_id)
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_iepa_user_profiles_updated_at
    BEFORE UPDATE ON iepa_user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_historical_registrations_updated_at
    BEFORE UPDATE ON iepa_historical_registrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_attendee_registrations_updated_at
    BEFORE UPDATE ON iepa_attendee_registrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_speaker_registrations_updated_at
    BEFORE UPDATE ON iepa_speaker_registrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_sponsor_registrations_updated_at
    BEFORE UPDATE ON iepa_sponsor_registrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_payments_updated_at
    BEFORE UPDATE ON iepa_payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_admin_users_updated_at
    BEFORE UPDATE ON iepa_admin_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_organizations_updated_at
    BEFORE UPDATE ON iepa_organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_iepa_sponsor_domains_updated_at
    BEFORE UPDATE ON iepa_sponsor_domains
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE iepa_user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_historical_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_attendee_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_speaker_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_sponsor_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE iepa_sponsor_domains ENABLE ROW LEVEL SECURITY;
