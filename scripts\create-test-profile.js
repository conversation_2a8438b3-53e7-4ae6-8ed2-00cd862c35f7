#!/usr/bin/env node

// <PERSON><PERSON><PERSON> to create a test user profile for form prefilling testing
// This creates a user profile that can be used to test the automatic form prefilling functionality

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTestProfile() {
  console.log('🚀 Creating test user profile for form prefilling...');
  
  const email = '<EMAIL>';
  const password = 'PrefillTest123!';
  
  try {
    // First, create the user through auth API
    console.log('👤 Creating test user...');
    const { data: userData, error: userError } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true,
      user_metadata: {
        full_name: 'John Prefill Tester',
        first_name: 'John',
        last_name: 'Tester'
      }
    });
    
    if (userError) {
      console.error('❌ Error creating user:', userError);
      return;
    }
    
    console.log('✅ User created successfully!');
    console.log('📧 Email:', userData.user.email);
    console.log('🆔 ID:', userData.user.id);
    
    // Now create the user profile with comprehensive test data
    console.log('📝 Creating user profile...');
    const profileData = {
      user_id: userData.user.id,
      first_name: 'John',
      last_name: 'Tester',
      email: email,
      phone_number: '5551234567', // Will be formatted as (*************
      organization: 'Clean Energy Solutions Inc.',
      job_title: 'Senior Environmental Engineer',
      street_address: '123 Green Energy Blvd',
      city: 'Sacramento',
      state: 'CA',
      zip_code: '95814',
      country: 'United States',
      gender: 'male',
      preferred_name_on_badge: 'John T.',
      imported_from_2024: false
    };
    
    const { data: profileResult, error: profileError } = await supabase
      .from('iepa_user_profiles')
      .insert(profileData)
      .select()
      .single();
    
    if (profileError) {
      console.error('❌ Error creating profile:', profileError);
      return;
    }
    
    console.log('✅ Profile created successfully!');
    console.log('📋 Profile data:', {
      name: `${profileResult.first_name} ${profileResult.last_name}`,
      email: profileResult.email,
      phone: profileResult.phone_number,
      organization: profileResult.organization,
      jobTitle: profileResult.job_title,
      address: `${profileResult.street_address}, ${profileResult.city}, ${profileResult.state} ${profileResult.zip_code}`
    });
    
    // Test the login
    console.log('\n🔐 Testing login...');
    const testClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    
    const { data: loginData, error: loginError } = await testClient.auth.signInWithPassword({
      email: email,
      password: password
    });
    
    if (loginError) {
      console.error('❌ Login test failed:', loginError);
    } else {
      console.log('✅ Login test successful!');
      console.log('🔑 Session created for:', loginData.user.email);
    }
    
    console.log('\n🎯 Test user profile creation complete!');
    console.log('📝 Test Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: PrefillTest123!');
    console.log('\n🔗 Test URLs:');
    console.log('   Login: http://localhost:6969/auth/login');
    console.log('   Attendee Form: http://localhost:6969/register/attendee');
    console.log('   Speaker Form: http://localhost:6969/register/speaker');
    console.log('\n📋 Expected Prefill Data:');
    console.log('   Name: John Tester');
    console.log('   Email: <EMAIL>');
    console.log('   Phone: (*************');
    console.log('   Organization: Clean Energy Solutions Inc.');
    console.log('   Job Title: Senior Environmental Engineer');
    console.log('   Address: 123 Green Energy Blvd, Sacramento, CA 95814');
    console.log('   Badge Name: John T.');
    
  } catch (error) {
    console.error('💥 Script error:', error);
  }
}

// Run the script
createTestProfile().then(() => {
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
