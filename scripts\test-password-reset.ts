#!/usr/bin/env tsx

/**
 * Test Password Reset Email Functionality
 *
 * This script tests the password reset email functionality to help debug
 * why emails aren't arriving.
 */

import { createClient } from '@supabase/supabase-js';

// Production Supabase credentials
const SUPABASE_URL = 'https://uffhyhpcuedjsisczocy.supabase.co';
const SUPABASE_ANON_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVmZmh5aHBjdWVkanNpc2N6b2N5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjQ5MTcsImV4cCI6MjA2NDE0MDkxN30.Q4XOwkhVA8_YhKY-lM_wKY_hMyoc_I5TXL1xCehbEAY';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testPasswordReset() {
  console.log('🔧 Testing Password Reset Email Functionality');
  console.log('='.repeat(50));

  // Test email - use a real email you can check
  const testEmail = '<EMAIL>'; // Change this to your email

  console.log(`📧 Testing password reset for: ${testEmail}`);
  console.log(`🌐 Supabase URL: ${SUPABASE_URL}`);
  console.log(`🔗 Redirect URL: https://reg.iepa.com/auth/reset-password`);

  try {
    const { data, error } = await supabase.auth.resetPasswordForEmail(
      testEmail,
      {
        redirectTo: 'https://reg.iepa.com/auth/reset-password',
      }
    );

    if (error) {
      console.error('❌ Password reset failed:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.status,
        name: error.name,
      });

      // Common error scenarios
      if (error.message.includes('SMTP')) {
        console.log('\n💡 SMTP Configuration Issue:');
        console.log('   - SMTP is not configured in Supabase dashboard');
        console.log('   - Go to: Authentication → Settings → SMTP Settings');
        console.log('   - Enable custom SMTP with SendGrid credentials');
      }

      if (error.message.includes('rate limit')) {
        console.log('\n⏰ Rate Limit Issue:');
        console.log('   - Too many password reset attempts');
        console.log('   - Wait a few minutes before trying again');
      }

      if (error.message.includes('user not found')) {
        console.log('\n👤 User Not Found:');
        console.log('   - Email address is not registered');
        console.log('   - Check if user exists in auth.users table');
      }

      return false;
    }

    console.log('✅ Password reset request successful!');
    console.log('📊 Response data:', data);
    console.log('\n📬 Check your email for the reset link');
    console.log('⏱️  Email should arrive within 1-2 minutes');

    return true;
  } catch (err) {
    console.error('💥 Unexpected error:', err);
    return false;
  }
}

async function testMagicLink() {
  console.log('\n🔧 Testing Magic Link Email (for comparison)');
  console.log('='.repeat(50));

  const testEmail = '<EMAIL>'; // Change this to your email

  console.log(`📧 Testing magic link for: ${testEmail}`);

  try {
    const { data, error } = await supabase.auth.signInWithOtp({
      email: testEmail,
      options: {
        emailRedirectTo: 'https://reg.iepa.com/auth/callback',
      },
    });

    if (error) {
      console.error('❌ Magic link failed:', error);
      return false;
    }

    console.log('✅ Magic link request successful!');
    console.log('📊 Response data:', data);

    return true;
  } catch (err) {
    console.error('💥 Unexpected error:', err);
    return false;
  }
}

async function main() {
  console.log('🚀 IEPA Password Reset Email Test');
  console.log('==================================\n');

  // Test password reset
  const passwordResetSuccess = await testPasswordReset();

  // Test magic link for comparison
  const magicLinkSuccess = await testMagicLink();

  console.log('\n📋 Test Results Summary:');
  console.log('========================');
  console.log(
    `Password Reset: ${passwordResetSuccess ? '✅ SUCCESS' : '❌ FAILED'}`
  );
  console.log(
    `Magic Link:     ${magicLinkSuccess ? '✅ SUCCESS' : '❌ FAILED'}`
  );

  if (!passwordResetSuccess && magicLinkSuccess) {
    console.log('\n🔍 Diagnosis:');
    console.log("Magic links work but password reset doesn't.");
    console.log(
      'This indicates SMTP configuration is missing in Supabase dashboard.'
    );
    console.log('\n🛠️  Next Steps:');
    console.log(
      '1. Go to Supabase Dashboard: https://supabase.com/dashboard/project/uffhyhpcuedjsisczocy'
    );
    console.log('2. Navigate to: Authentication → Settings → SMTP Settings');
    console.log('3. Enable custom SMTP with SendGrid credentials');
    console.log('4. Re-run this test script');
  }

  if (passwordResetSuccess) {
    console.log('\n🎉 Password reset emails are working!');
    console.log('Check your email and test the full reset flow.');
  }
}

// Run the test
main().catch(console.error);
