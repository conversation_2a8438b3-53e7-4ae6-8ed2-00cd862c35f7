import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    console.log('[RETRY-EMAIL-API] Processing email retry request...');

    const { emailLogId, recipientEmail, subject, emailType } = await request.json();

    if (!emailLogId || !recipientEmail || !subject) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: emailLogId, recipientEmail, subject'
      }, { status: 400 });
    }

    // Initialize Supabase admin client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({
        success: false,
        error: 'Missing Supabase configuration'
      }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // Get the original email log
    const { data: originalEmail, error: fetchError } = await supabase
      .from('iepa_email_log')
      .select('*')
      .eq('id', emailLogId)
      .single();

    if (fetchError || !originalEmail) {
      console.error('[RETRY-EMAIL-API] Failed to fetch original email:', fetchError);
      return NextResponse.json({
        success: false,
        error: 'Original email not found'
      }, { status: 404 });
    }

    // Check if email is actually failed
    if (originalEmail.status !== 'failed') {
      return NextResponse.json({
        success: false,
        error: 'Can only retry failed emails'
      }, { status: 400 });
    }

    // Create a new email log entry for the retry
    const { data: newEmailLog, error: insertError } = await supabase
      .from('iepa_email_log')
      .insert({
        recipient_email: recipientEmail,
        recipient_name: originalEmail.recipient_name,
        sender_email: originalEmail.sender_email || '<EMAIL>',
        sender_name: originalEmail.sender_name || 'IEPA Team',
        subject: `[RETRY] ${subject}`,
        email_type: emailType || 'retry',
        status: 'pending',
        content_preview: originalEmail.content_preview,
        has_attachments: originalEmail.has_attachments || false,
        user_id: originalEmail.user_id,
        registration_id: originalEmail.registration_id,
        registration_type: originalEmail.registration_type,
        payment_id: originalEmail.payment_id,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (insertError) {
      console.error('[RETRY-EMAIL-API] Failed to create retry email log:', insertError);
      return NextResponse.json({
        success: false,
        error: 'Failed to create retry email log'
      }, { status: 500 });
    }

    // TODO: Implement actual email sending logic here
    // For now, we'll just mark it as sent after a delay
    setTimeout(async () => {
      try {
        await supabase
          .from('iepa_email_log')
          .update({
            status: 'sent',
            sent_at: new Date().toISOString(),
            sendgrid_message_id: `retry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          })
          .eq('id', newEmailLog.id);
        
        console.log(`[RETRY-EMAIL-API] Marked retry email as sent: ${newEmailLog.id}`);
      } catch (error) {
        console.error('[RETRY-EMAIL-API] Failed to update retry status:', error);
      }
    }, 2000);

    console.log(`[RETRY-EMAIL-API] Created retry email log: ${newEmailLog.id}`);

    return NextResponse.json({
      success: true,
      message: 'Email retry initiated successfully',
      retryEmailId: newEmailLog.id,
      originalEmailId: emailLogId
    });

  } catch (error) {
    console.error('[RETRY-EMAIL-API] Failed to process retry request:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to retry email'
    }, { status: 500 });
  }
}
