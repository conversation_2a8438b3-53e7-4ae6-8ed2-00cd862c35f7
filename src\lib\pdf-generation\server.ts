// PDF Generation Library - Server-Side Only
// Server-side exports for PDF generation functionality (no React hooks/components)

// Types
export type {
  AttendeeRegistrationData,
  SpeakerRegistrationData,
  SponsorRegistrationData,
  PDFReceiptData,
  PDFInvoiceData,
  PDFLineItem,
  PDFCompanyInfo,
  PDFConferenceInfo,
  PDFGenerationOptions,
  PDFGenerationResult,
  PDFStorageResult,
  PDFTemplate,
  PDFEmailAttachment,
  PDFEmailData,
} from './types';

// Configuration
export {
  IEPA_COMPANY_INFO,
  CONFERENCE_INFO,
  DEFAULT_PDF_OPTIONS,
  IEPA_PDF_COLORS,
  PDF_FONTS,
  PDF_LAYOUT,
  PDF_STYLES,
  PDF_STORAGE_CONFIG,
  PDF_NAMING,
  DOCUMENT_NUMBERING,
  PDF_ERROR_MESSAGES,
  PDF_QUALITY,
  PDF_EMAIL_CONFIG,
  PDF_VALIDATION,
  PDF_DEV_CONFIG,
} from './config';

// Utilities
export {
  formatCurrency,
  formatDate,
  formatDateTime,
  generateAttendeeLineItems,
  generateSpeakerLineItems,
  generateSponsorLineItems,
  createReceiptData,
  getCustomerInfo,
  validateRegistrationData,
  generatePDFFileName,
} from './utils';

// Templates (server-side safe)
export { ReceiptTemplate } from './templates/ReceiptTemplate';
export { pdfStyles } from './templates/PDFStyles';

// Services (server-side PDF generation)
export {
  generateReceiptPDF,
  storePDFInSupabase,
  generateAndStoreReceiptPDF,
  downloadPDFFromSupabase,
  deletePDFFromSupabase,
  listPDFsForRegistration,
} from './services/pdfGenerator';

export {
  createPDFStorageBucket,
  setupPDFStoragePolicies,
  testPDFStorageSetup,
  initializePDFStorage,
  getPDFStorageInfo,
} from './services/storageSetup';

// Constants for easy access
export const PDF_CONSTANTS = {
  BUCKET_NAME: 'iepa-documents',
  FOLDERS: {
    RECEIPTS: 'receipts',
    INVOICES: 'invoices',
    TEMP: 'temp',
  },
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_MIME_TYPES: ['application/pdf'],
  RECEIPT_PREFIX: 'IEPA-RCP',
  INVOICE_PREFIX: 'IEPA-INV',
} as const;

// Helper functions for common use cases (server-side safe)
export const pdfHelpers = {
  /**
   * Check if PDF generation is available (server-side)
   */
  isAvailable: () => {
    return typeof global !== 'undefined';
  },

  /**
   * Get PDF file extension
   */
  getFileExtension: () => '.pdf',

  /**
   * Get PDF MIME type
   */
  getMimeType: () => 'application/pdf',

  /**
   * Generate unique filename with timestamp
   */
  generateUniqueFileName: (prefix: string, registrationId: string) => {
    const timestamp = new Date().toISOString().slice(0, 10);
    const shortId = registrationId.slice(-8).toUpperCase();
    return `${prefix}-${shortId}-${timestamp}.pdf`;
  },

  /**
   * Validate registration data for PDF generation
   */
  validateForPDF: (
    type: 'attendee' | 'speaker' | 'sponsor',
    data: Record<string, unknown>
  ): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!data.id) errors.push('Registration ID is required');
    if (!data.fullName && !data.sponsorName) errors.push('Name is required');
    if (!data.email && !data.contactEmail) errors.push('Email is required');

    switch (type) {
      case 'attendee':
        if (!data.registrationType)
          errors.push('Registration type is required');
        break;
      case 'speaker':
        if (!data.presentationTitle)
          errors.push('Presentation title is required');
        break;
      case 'sponsor':
        if (!data.sponsorshipLevel)
          errors.push('Sponsorship level is required');
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Format registration data for PDF generation
   */
  formatRegistrationData: (
    type: 'attendee' | 'speaker' | 'sponsor',
    data: Record<string, unknown>
  ) => {
    // Ensure all required fields are present with defaults
    const baseData = {
      id: data.id || '',
      createdAt: data.created_at || data.createdAt || new Date().toISOString(),
      updatedAt: data.updated_at || data.updatedAt || new Date().toISOString(),
    };

    switch (type) {
      case 'attendee':
        const firstName = data.first_name || data.firstName || '';
        const lastName = data.last_name || data.lastName || '';
        return {
          ...baseData,
          registrationType:
            data.registration_type || data.registrationType || '',
          firstName,
          lastName,
          nameOnBadge: data.name_on_badge || data.nameOnBadge || '',
          email: data.email || '',
          gender: data.gender || '',
          phoneNumber: data.phone_number || data.phoneNumber || '',
          organization: data.organization || '',
          jobTitle: data.job_title || data.jobTitle || '',
          streetAddress: data.street_address || data.streetAddress || '',
          city: data.city || '',
          state: data.state || '',
          zipCode: data.zip_code || data.zipCode || '',
          country: data.country || 'United States',
          golfTournament: data.attending_golf || data.golfTournament || false,
          golfClubRental: data.golf_club_rental || data.golfClubRental || false,
          golfClubHandedness:
            data.golf_club_handedness || data.golfClubHandedness || '',
          meals: data.meals || {},
          dietaryNeeds: data.dietary_needs || data.dietaryNeeds || '',
          registrationTotal:
            data.registration_total || data.registrationTotal || 0,
          golfTotal: data.golf_total || data.golfTotal || 0,
          golfClubRentalTotal:
            data.golf_club_rental_total || data.golfClubRentalTotal || 0,
          mealTotal: data.meal_total || data.mealTotal || 0,
          grandTotal: data.grand_total || data.grandTotal || 0,
          paymentStatus: data.payment_status || data.paymentStatus || 'pending',
          paymentId: data.payment_id || data.paymentId || null,
        };

      case 'speaker':
        const speakerFirstName = data.first_name || data.firstName || '';
        const speakerLastName = data.last_name || data.lastName || '';
        return {
          ...baseData,
          firstName: speakerFirstName,
          lastName: speakerLastName,
          email: data.email || '',
          phoneNumber: data.phone_number || data.phoneNumber || '',
          organizationName:
            data.organization_name || data.organizationName || '',
          jobTitle: data.job_title || data.jobTitle || '',
          bio: data.bio || '',
          presentationTitle:
            data.presentation_title || data.presentationTitle || '',
          presentationDescription:
            data.presentation_description || data.presentationDescription || '',
          presentationDuration:
            data.presentation_duration || data.presentationDuration || '',
          targetAudience: data.target_audience || data.targetAudience || '',
          learningObjectives:
            data.learning_objectives || data.learningObjectives || '',
          speakerExperience:
            data.speaker_experience || data.speakerExperience || '',
          previousSpeaking:
            data.previous_speaking || data.previousSpeaking || '',
          equipmentNeeds: data.equipment_needs || data.equipmentNeeds || '',
          specialRequests: data.special_requests || data.specialRequests || '',
          presentationFileUrl:
            data.presentation_file_url || data.presentationFileUrl || null,
          headshotUrl: data.headshot_url || data.headshotUrl || null,
        };

      case 'sponsor':
        return {
          ...baseData,
          sponsorName: data.sponsor_name || data.sponsorName || '',
          sponsorUrl: data.sponsor_url || data.sponsorUrl || '',
          sponsorVideo: data.sponsor_video || data.sponsorVideo || '',
          sponsorImageUrl:
            data.sponsor_image_url || data.sponsorImageUrl || null,
          sponsorDescription:
            data.sponsor_description || data.sponsorDescription || '',
          sponsorshipLevel:
            data.sponsorship_level || data.sponsorshipLevel || '',
          contactName: data.contact_name || data.contactName || '',
          contactEmail: data.contact_email || data.contactEmail || '',
          contactPhone: data.contact_phone || data.contactPhone || '',
          contactTitle: data.contact_title || data.contactTitle || '',
          billingAddress: data.billing_address || data.billingAddress || '',
          billingCity: data.billing_city || data.billingCity || '',
          billingState: data.billing_state || data.billingState || '',
          billingZip: data.billing_zip || data.billingZip || '',
          billingCountry:
            data.billing_country || data.billingCountry || 'United States',
          companyDescription:
            data.company_description || data.companyDescription || '',
          marketingGoals: data.marketing_goals || data.marketingGoals || '',
          exhibitRequirements:
            data.exhibit_requirements || data.exhibitRequirements || '',
          specialRequests: data.special_requests || data.specialRequests || '',
          attendeeCount: data.attendee_count || data.attendeeCount || '',
          attendeeNames: data.attendee_names || data.attendeeNames || '',
          sponsorshipTotal:
            data.sponsorship_total || data.sponsorshipTotal || 0,
          paymentStatus: data.payment_status || data.paymentStatus || 'pending',
          paymentId: data.payment_id || data.paymentId || null,
        };

      default:
        return data;
    }
  },
};
