#!/bin/bash

# IEPA Conference Registration - Data Management Script
# This script provides easy access to all data management operations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Ensure we're in the project root
cd "$(dirname "$0")/.."

show_menu() {
    clear
    print_header "🗄️  IEPA Conference Registration - Data Management"
    echo "=================================================="
    echo ""
    echo "Choose an option:"
    echo ""
    echo "📥 SYNC FROM PRODUCTION:"
    echo "  1) Quick sync (TypeScript) - Recommended"
    echo "  2) Dry run sync (see what would be synced)"
    echo "  3) Sync specific tables only"
    echo "  4) Clear local data and full sync"
    echo ""
    echo "💾 BACKUP PRODUCTION DATA:"
    echo "  5) Create production backup (SQL files)"
    echo "  6) List existing backups"
    echo "  7) Restore from backup"
    echo ""
    echo "🔧 LOCAL DEVELOPMENT:"
    echo "  8) Check local Supabase status"
    echo "  9) Start local Supabase"
    echo "  10) Open Supabase Studio"
    echo "  11) Reset local database (clear all data)"
    echo ""
    echo "📊 DATA INSPECTION:"
    echo "  12) Show table row counts (local vs production)"
    echo "  13) Show recent registrations"
    echo "  14) Export data to CSV"
    echo ""
    echo "  0) Exit"
    echo ""
    echo -n "Enter your choice [0-14]: "
}

check_local_supabase() {
    if curl -s http://127.0.0.1:54321/rest/v1/ > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

show_table_counts() {
    print_status "Checking table row counts..."
    echo ""
    echo "Table                          Local    Production"
    echo "=============================================="
    
    # This would need to be implemented with actual database queries
    # For now, show a placeholder
    echo "iepa_user_profiles             ???      ???"
    echo "iepa_attendee_registrations    ???      ???"
    echo "iepa_speaker_registrations     ???      ???"
    echo "iepa_sponsor_registrations     ???      ???"
    echo "iepa_golf_registrations        ???      ???"
    echo "iepa_payments                  ???      ???"
    echo ""
    print_warning "Use 'npm run sync-production-data --dry-run' for detailed counts"
}

list_backups() {
    BACKUP_DIR=".docs/production-backups"
    if [ -d "$BACKUP_DIR" ]; then
        print_status "Available backups:"
        echo ""
        for backup in "$BACKUP_DIR"/backup_*; do
            if [ -d "$backup" ]; then
                BACKUP_NAME=$(basename "$backup")
                BACKUP_DATE=$(echo "$BACKUP_NAME" | sed 's/backup_//' | sed 's/_/ /')
                BACKUP_SIZE=$(du -sh "$backup" 2>/dev/null | cut -f1)
                echo "  📦 $BACKUP_DATE ($BACKUP_SIZE)"
                echo "     Path: $backup"
                echo ""
            fi
        done
    else
        print_warning "No backups found. Create one with option 5."
    fi
}

# Main menu loop
while true; do
    show_menu
    read -r choice
    
    case $choice in
        1)
            clear
            print_header "📥 Quick Sync from Production"
            echo "=============================="
            echo ""
            if check_local_supabase; then
                print_status "Starting production data sync..."
                npm run sync-production-data
            else
                print_error "Local Supabase is not running"
                print_status "Starting Supabase..."
                supabase start
                print_status "Now running sync..."
                npm run sync-production-data
            fi
            echo ""
            read -p "Press Enter to continue..."
            ;;
        2)
            clear
            print_header "🔍 Dry Run Sync"
            echo "================"
            echo ""
            print_status "Running dry run to show what would be synced..."
            npm run sync-production-data -- --dry-run
            echo ""
            read -p "Press Enter to continue..."
            ;;
        3)
            clear
            print_header "📋 Sync Specific Tables"
            echo "======================="
            echo ""
            echo "Available tables:"
            echo "  - iepa_user_profiles"
            echo "  - iepa_attendee_registrations"
            echo "  - iepa_speaker_registrations"
            echo "  - iepa_sponsor_registrations"
            echo "  - iepa_golf_registrations"
            echo "  - iepa_payments"
            echo "  - iepa_email_log"
            echo ""
            echo -n "Enter table names (comma-separated): "
            read -r tables
            if [ -n "$tables" ]; then
                npm run sync-production-data -- --tables "$tables"
            fi
            echo ""
            read -p "Press Enter to continue..."
            ;;
        4)
            clear
            print_header "🗑️  Clear Local Data and Full Sync"
            echo "=================================="
            echo ""
            print_warning "This will DELETE all local data and replace it with production data"
            echo -n "Are you sure? (y/N): "
            read -r confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                npm run sync-production-data -- --clear-local
            else
                print_status "Operation cancelled"
            fi
            echo ""
            read -p "Press Enter to continue..."
            ;;
        5)
            clear
            print_header "💾 Create Production Backup"
            echo "==========================="
            echo ""
            print_status "Creating SQL backup of production data..."
            npm run backup-production-data
            echo ""
            read -p "Press Enter to continue..."
            ;;
        6)
            clear
            print_header "📋 List Existing Backups"
            echo "========================"
            echo ""
            list_backups
            echo ""
            read -p "Press Enter to continue..."
            ;;
        7)
            clear
            print_header "🔄 Restore from Backup"
            echo "======================"
            echo ""
            list_backups
            echo -n "Enter backup directory name (e.g., backup_20241207_143022): "
            read -r backup_name
            if [ -n "$backup_name" ]; then
                BACKUP_PATH=".docs/production-backups/$backup_name"
                if [ -f "$BACKUP_PATH/restore.sh" ]; then
                    cd "$BACKUP_PATH"
                    ./restore.sh
                    cd - > /dev/null
                else
                    print_error "Backup not found or invalid"
                fi
            fi
            echo ""
            read -p "Press Enter to continue..."
            ;;
        8)
            clear
            print_header "🔧 Local Supabase Status"
            echo "========================"
            echo ""
            if check_local_supabase; then
                print_success "Local Supabase is running"
                supabase status
            else
                print_warning "Local Supabase is not running"
                print_status "Run option 9 to start it"
            fi
            echo ""
            read -p "Press Enter to continue..."
            ;;
        9)
            clear
            print_header "🚀 Start Local Supabase"
            echo "======================="
            echo ""
            print_status "Starting local Supabase..."
            supabase start
            echo ""
            read -p "Press Enter to continue..."
            ;;
        10)
            clear
            print_header "🌐 Open Supabase Studio"
            echo "======================="
            echo ""
            if check_local_supabase; then
                print_status "Opening Supabase Studio..."
                open http://127.0.0.1:54323
                print_success "Studio opened in browser"
            else
                print_error "Local Supabase is not running"
                print_status "Start it with option 9 first"
            fi
            echo ""
            read -p "Press Enter to continue..."
            ;;
        11)
            clear
            print_header "🗑️  Reset Local Database"
            echo "========================"
            echo ""
            print_warning "This will DELETE ALL local data"
            echo -n "Are you sure? (y/N): "
            read -r confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                print_status "Resetting local database..."
                supabase db reset
                print_success "Database reset complete"
            else
                print_status "Operation cancelled"
            fi
            echo ""
            read -p "Press Enter to continue..."
            ;;
        12)
            clear
            print_header "📊 Table Row Counts"
            echo "==================="
            echo ""
            show_table_counts
            echo ""
            read -p "Press Enter to continue..."
            ;;
        13)
            clear
            print_header "📋 Recent Registrations"
            echo "======================="
            echo ""
            print_status "This feature would show recent registrations"
            print_warning "Not implemented yet - use Supabase Studio for now"
            echo ""
            read -p "Press Enter to continue..."
            ;;
        14)
            clear
            print_header "📤 Export Data to CSV"
            echo "====================="
            echo ""
            print_status "This feature would export data to CSV"
            print_warning "Not implemented yet - use Supabase Studio for now"
            echo ""
            read -p "Press Enter to continue..."
            ;;
        0)
            clear
            print_success "Goodbye! 👋"
            exit 0
            ;;
        *)
            print_error "Invalid option. Please try again."
            sleep 2
            ;;
    esac
done
