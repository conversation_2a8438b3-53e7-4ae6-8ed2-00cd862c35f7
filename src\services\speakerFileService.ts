import { supabase } from '@/lib/supabase';

export interface UpdateSpeakerFileResult {
  success: boolean;
  error?: string;
}

/**
 * Update a speaker's file URL in the database
 */
export async function updateSpeakerFile(
  speakerId: string,
  fieldName: 'presentation_file_url' | 'headshot_url',
  fileUrl: string
): Promise<UpdateSpeakerFileResult> {
  try {
    // Get the current user to verify they own this registration
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        success: false,
        error: 'Authentication required',
      };
    }

    // Update the speaker registration with the new file URL
    const { data, error } = await supabase
      .from('iepa_speaker_registrations')
      .update({
        [fieldName]: fileUrl,
        updated_at: new Date().toISOString(),
      })
      .eq('id', speakerId)
      .eq('user_id', user.id) // Only allow update if user owns registration
      .select('id')
      .single();

    if (error) {
      console.error('Error updating speaker file:', error);
      return {
        success: false,
        error: error.message || 'Failed to update file',
      };
    }

    if (!data) {
      return {
        success: false,
        error: 'Speaker registration not found or access denied',
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error('Error in updateSpeakerFile:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Remove a speaker's file from the database (set URL to null)
 */
export async function removeSpeakerFile(
  speakerId: string,
  fieldName: 'presentation_file_url' | 'headshot_url'
): Promise<UpdateSpeakerFileResult> {
  try {
    // Get the current user to verify they own this registration
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        success: false,
        error: 'Authentication required',
      };
    }

    // Update the speaker registration to remove the file URL
    const { data, error } = await supabase
      .from('iepa_speaker_registrations')
      .update({
        [fieldName]: null,
        updated_at: new Date().toISOString(),
      })
      .eq('id', speakerId)
      .eq('user_id', user.id) // Only allow update if user owns registration
      .select('id')
      .single();

    if (error) {
      console.error('Error removing speaker file:', error);
      return {
        success: false,
        error: error.message || 'Failed to remove file',
      };
    }

    if (!data) {
      return {
        success: false,
        error: 'Speaker registration not found or access denied',
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error('Error in removeSpeakerFile:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get speaker file information
 */
export async function getSpeakerFiles(speakerId: string) {
  try {
    // Get the current user to verify they own this registration
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        success: false,
        error: 'Authentication required',
      };
    }

    // Get the speaker registration files
    const { data, error } = await supabase
      .from('iepa_speaker_registrations')
      .select('id, presentation_file_url, headshot_url')
      .eq('id', speakerId)
      .eq('user_id', user.id) // Only allow access if user owns registration
      .single();

    if (error) {
      console.error('Error getting speaker files:', error);
      return {
        success: false,
        error: error.message || 'Failed to get files',
      };
    }

    if (!data) {
      return {
        success: false,
        error: 'Speaker registration not found or access denied',
      };
    }

    return {
      success: true,
      data: {
        presentationFileUrl: data.presentation_file_url,
        headshotUrl: data.headshot_url,
      },
    };
  } catch (error) {
    console.error('Error in getSpeakerFiles:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
