export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      iepa_admin_users: {
        Row: {
          created_at: string | null
          created_by: string | null
          email: string
          id: string
          is_active: boolean | null
          permissions: Json | null
          role: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          email: string
          id?: string
          is_active?: boolean | null
          permissions?: Json | null
          role?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          email?: string
          id?: string
          is_active?: boolean | null
          permissions?: Json | null
          role?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      iepa_attendee_registrations: {
        Row: {
          attendee_type: string
          attending_golf: boolean | null
          city: string
          country: string | null
          created_at: string | null
          dietary_needs: string | null
          discount_amount: number | null
          discount_code: string | null
          email: string
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          first_name: string
          full_name: string | null
          gender: string
          golf_club_handedness: string | null
          golf_club_rental: boolean | null
          golf_club_rental_total: number | null
          golf_total: number | null
          grand_total: number | null
          id: string
          invoice_generated_at: string | null
          invoice_url: string | null
          is_speaker: boolean | null
          is_sponsor_attendee: boolean | null
          job_title: string
          last_name: string
          linked_attendee_email: string | null
          meal_total: number | null
          meals: string[] | null
          name_on_badge: string
          night_one: boolean | null
          night_two: boolean | null
          organization: string
          original_total: number | null
          payment_id: string | null
          payment_status: string | null
          phone_number: string
          receipt_generated_at: string | null
          receipt_url: string | null
          registration_total: number | null
          registration_type: string
          speaker_pricing_type: string | null
          speaker_registration_id: string | null
          sponsor_discount_code: string | null
          sponsor_id: string | null
          state: string
          street_address: string
          updated_at: string | null
          user_id: string | null
          zip_code: string
        }
        Insert: {
          attendee_type?: string
          attending_golf?: boolean | null
          city: string
          country?: string | null
          created_at?: string | null
          dietary_needs?: string | null
          discount_amount?: number | null
          discount_code?: string | null
          email: string
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          first_name: string
          full_name?: string | null
          gender: string
          golf_club_handedness?: string | null
          golf_club_rental?: boolean | null
          golf_club_rental_total?: number | null
          golf_total?: number | null
          grand_total?: number | null
          id?: string
          invoice_generated_at?: string | null
          invoice_url?: string | null
          is_speaker?: boolean | null
          is_sponsor_attendee?: boolean | null
          job_title: string
          last_name: string
          linked_attendee_email?: string | null
          meal_total?: number | null
          meals?: string[] | null
          name_on_badge: string
          night_one?: boolean | null
          night_two?: boolean | null
          organization: string
          original_total?: number | null
          payment_id?: string | null
          payment_status?: string | null
          phone_number: string
          receipt_generated_at?: string | null
          receipt_url?: string | null
          registration_total?: number | null
          registration_type: string
          speaker_pricing_type?: string | null
          speaker_registration_id?: string | null
          sponsor_discount_code?: string | null
          sponsor_id?: string | null
          state: string
          street_address: string
          updated_at?: string | null
          user_id?: string | null
          zip_code: string
        }
        Update: {
          attendee_type?: string
          attending_golf?: boolean | null
          city?: string
          country?: string | null
          created_at?: string | null
          dietary_needs?: string | null
          discount_amount?: number | null
          discount_code?: string | null
          email?: string
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          first_name?: string
          full_name?: string | null
          gender?: string
          golf_club_handedness?: string | null
          golf_club_rental?: boolean | null
          golf_club_rental_total?: number | null
          golf_total?: number | null
          grand_total?: number | null
          id?: string
          invoice_generated_at?: string | null
          invoice_url?: string | null
          is_speaker?: boolean | null
          is_sponsor_attendee?: boolean | null
          job_title?: string
          last_name?: string
          linked_attendee_email?: string | null
          meal_total?: number | null
          meals?: string[] | null
          name_on_badge?: string
          night_one?: boolean | null
          night_two?: boolean | null
          organization?: string
          original_total?: number | null
          payment_id?: string | null
          payment_status?: string | null
          phone_number?: string
          receipt_generated_at?: string | null
          receipt_url?: string | null
          registration_total?: number | null
          registration_type?: string
          speaker_pricing_type?: string | null
          speaker_registration_id?: string | null
          sponsor_discount_code?: string | null
          sponsor_id?: string | null
          state?: string
          street_address?: string
          updated_at?: string | null
          user_id?: string | null
          zip_code?: string
        }
        Relationships: [
          {
            foreignKeyName: "iepa_attendee_registrations_sponsor_id_fkey"
            columns: ["sponsor_id"]
            isOneToOne: false
            referencedRelation: "iepa_sponsor_registrations"
            referencedColumns: ["id"]
          },
        ]
      }
      iepa_email_config: {
        Row: {
          config_key: string
          config_value: string
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          updated_at: string | null
        }
        Insert: {
          config_key: string
          config_value: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          updated_at?: string | null
        }
        Update: {
          config_key?: string
          config_value?: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      iepa_email_config_log: {
        Row: {
          change_reason: string | null
          changed_by: string | null
          config_key: string
          created_at: string | null
          id: string
          new_value: string
          old_value: string | null
        }
        Insert: {
          change_reason?: string | null
          changed_by?: string | null
          config_key: string
          created_at?: string | null
          id?: string
          new_value: string
          old_value?: string | null
        }
        Update: {
          change_reason?: string | null
          changed_by?: string | null
          config_key?: string
          created_at?: string | null
          id?: string
          new_value?: string
          old_value?: string | null
        }
        Relationships: []
      }
      iepa_email_log: {
        Row: {
          content_preview: string | null
          created_at: string | null
          email_type: string
          error_message: string | null
          has_attachments: boolean | null
          id: string
          payment_id: string | null
          recipient_email: string
          recipient_name: string | null
          registration_id: string | null
          registration_type: string | null
          sender_email: string
          sender_name: string | null
          sendgrid_message_id: string | null
          sent_at: string | null
          status: string
          subject: string
          template_used: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          content_preview?: string | null
          created_at?: string | null
          email_type: string
          error_message?: string | null
          has_attachments?: boolean | null
          id?: string
          payment_id?: string | null
          recipient_email: string
          recipient_name?: string | null
          registration_id?: string | null
          registration_type?: string | null
          sender_email: string
          sender_name?: string | null
          sendgrid_message_id?: string | null
          sent_at?: string | null
          status?: string
          subject: string
          template_used?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          content_preview?: string | null
          created_at?: string | null
          email_type?: string
          error_message?: string | null
          has_attachments?: boolean | null
          id?: string
          payment_id?: string | null
          recipient_email?: string
          recipient_name?: string | null
          registration_id?: string | null
          registration_type?: string | null
          sender_email?: string
          sender_name?: string | null
          sendgrid_message_id?: string | null
          sent_at?: string | null
          status?: string
          subject?: string
          template_used?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      iepa_email_template_log: {
        Row: {
          change_reason: string | null
          changed_by: string | null
          created_at: string | null
          field_changed: string
          id: string
          new_value: string
          old_value: string | null
          template_id: string | null
          template_key: string
        }
        Insert: {
          change_reason?: string | null
          changed_by?: string | null
          created_at?: string | null
          field_changed: string
          id?: string
          new_value: string
          old_value?: string | null
          template_id?: string | null
          template_key: string
        }
        Update: {
          change_reason?: string | null
          changed_by?: string | null
          created_at?: string | null
          field_changed?: string
          id?: string
          new_value?: string
          old_value?: string | null
          template_id?: string | null
          template_key?: string
        }
        Relationships: [
          {
            foreignKeyName: "iepa_email_template_log_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "iepa_email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      iepa_email_templates: {
        Row: {
          created_at: string | null
          description: string | null
          html_template: string
          id: string
          is_active: boolean | null
          subject_template: string
          template_key: string
          template_name: string
          text_template: string | null
          updated_at: string | null
          variables: Json | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          html_template: string
          id?: string
          is_active?: boolean | null
          subject_template: string
          template_key: string
          template_name: string
          text_template?: string | null
          updated_at?: string | null
          variables?: Json | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          html_template?: string
          id?: string
          is_active?: boolean | null
          subject_template?: string
          template_key?: string
          template_name?: string
          text_template?: string | null
          updated_at?: string | null
          variables?: Json | null
        }
        Relationships: []
      }
      iepa_historical_registrations: {
        Row: {
          attendee_type: string
          attendee_type_iepa: string | null
          created_at: string | null
          event_name: string | null
          event_year: number
          golf_cell_number: string | null
          golf_club_rental: string | null
          golf_total: number | null
          golf_tournament: boolean | null
          grand_total: number | null
          id: string
          imported_from_source: string | null
          meals: string[] | null
          name_on_badge: string | null
          nights_staying: string | null
          original_data: Json | null
          payment_status: string | null
          profile_id: string | null
          registration_date: string | null
          special_dietary_needs: string | null
          status: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          attendee_type: string
          attendee_type_iepa?: string | null
          created_at?: string | null
          event_name?: string | null
          event_year: number
          golf_cell_number?: string | null
          golf_club_rental?: string | null
          golf_total?: number | null
          golf_tournament?: boolean | null
          grand_total?: number | null
          id?: string
          imported_from_source?: string | null
          meals?: string[] | null
          name_on_badge?: string | null
          nights_staying?: string | null
          original_data?: Json | null
          payment_status?: string | null
          profile_id?: string | null
          registration_date?: string | null
          special_dietary_needs?: string | null
          status: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          attendee_type?: string
          attendee_type_iepa?: string | null
          created_at?: string | null
          event_name?: string | null
          event_year?: number
          golf_cell_number?: string | null
          golf_club_rental?: string | null
          golf_total?: number | null
          golf_tournament?: boolean | null
          grand_total?: number | null
          id?: string
          imported_from_source?: string | null
          meals?: string[] | null
          name_on_badge?: string | null
          nights_staying?: string | null
          original_data?: Json | null
          payment_status?: string | null
          profile_id?: string | null
          registration_date?: string | null
          special_dietary_needs?: string | null
          status?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "iepa_historical_registrations_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "iepa_user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      iepa_organizations: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          industry: string | null
          is_active: boolean | null
          last_used_at: string | null
          name: string
          normalized_name: string
          updated_at: string | null
          usage_count: number | null
          website_url: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          industry?: string | null
          is_active?: boolean | null
          last_used_at?: string | null
          name: string
          normalized_name: string
          updated_at?: string | null
          usage_count?: number | null
          website_url?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          industry?: string | null
          is_active?: boolean | null
          last_used_at?: string | null
          name?: string
          normalized_name?: string
          updated_at?: string | null
          usage_count?: number | null
          website_url?: string | null
        }
        Relationships: []
      }
      iepa_payments: {
        Row: {
          amount: number
          created_at: string | null
          currency: string | null
          id: string
          registration_id: string
          registration_type: string
          status: string
          stripe_payment_intent_id: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          currency?: string | null
          id?: string
          registration_id: string
          registration_type: string
          status: string
          stripe_payment_intent_id: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          currency?: string | null
          id?: string
          registration_id?: string
          registration_type?: string
          status?: string
          stripe_payment_intent_id?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      iepa_speaker_registrations: {
        Row: {
          attendee_registration_id: string | null
          bio: string
          created_at: string | null
          email: string
          equipment_needs: string | null
          first_name: string
          full_name: string | null
          headshot_url: string | null
          id: string
          invoice_generated_at: string | null
          invoice_url: string | null
          job_title: string
          last_name: string
          learning_objectives: string | null
          organization_name: string
          phone_number: string | null
          preferred_contact_method: string | null
          presentation_description: string | null
          presentation_duration: string | null
          presentation_file_url: string | null
          presentation_title: string | null
          previous_speaking: string | null
          receipt_generated_at: string | null
          receipt_url: string | null
          registration_status: string | null
          speaker_experience: string | null
          speaker_pricing_type: string | null
          special_requests: string | null
          target_audience: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          attendee_registration_id?: string | null
          bio: string
          created_at?: string | null
          email?: string
          equipment_needs?: string | null
          first_name: string
          full_name?: string | null
          headshot_url?: string | null
          id?: string
          invoice_generated_at?: string | null
          invoice_url?: string | null
          job_title: string
          last_name: string
          learning_objectives?: string | null
          organization_name: string
          phone_number?: string | null
          preferred_contact_method?: string | null
          presentation_description?: string | null
          presentation_duration?: string | null
          presentation_file_url?: string | null
          presentation_title?: string | null
          previous_speaking?: string | null
          receipt_generated_at?: string | null
          receipt_url?: string | null
          registration_status?: string | null
          speaker_experience?: string | null
          speaker_pricing_type?: string | null
          special_requests?: string | null
          target_audience?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          attendee_registration_id?: string | null
          bio?: string
          created_at?: string | null
          email?: string
          equipment_needs?: string | null
          first_name?: string
          full_name?: string | null
          headshot_url?: string | null
          id?: string
          invoice_generated_at?: string | null
          invoice_url?: string | null
          job_title?: string
          last_name?: string
          learning_objectives?: string | null
          organization_name?: string
          phone_number?: string | null
          preferred_contact_method?: string | null
          presentation_description?: string | null
          presentation_duration?: string | null
          presentation_file_url?: string | null
          presentation_title?: string | null
          previous_speaking?: string | null
          receipt_generated_at?: string | null
          receipt_url?: string | null
          registration_status?: string | null
          speaker_experience?: string | null
          speaker_pricing_type?: string | null
          special_requests?: string | null
          target_audience?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "iepa_speaker_registrations_attendee_registration_id_fkey"
            columns: ["attendee_registration_id"]
            isOneToOne: false
            referencedRelation: "iepa_attendee_registrations"
            referencedColumns: ["id"]
          },
        ]
      }
      iepa_sponsor_domains: {
        Row: {
          auto_discount_code: string | null
          created_at: string | null
          created_by: string | null
          current_uses: number | null
          discount_type: string
          discount_value: number
          domain: string
          id: string
          is_active: boolean | null
          max_uses: number | null
          sponsor_id: string | null
          sponsor_name: string
          stripe_coupon_id: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          auto_discount_code?: string | null
          created_at?: string | null
          created_by?: string | null
          current_uses?: number | null
          discount_type?: string
          discount_value?: number
          domain: string
          id?: string
          is_active?: boolean | null
          max_uses?: number | null
          sponsor_id?: string | null
          sponsor_name: string
          stripe_coupon_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          auto_discount_code?: string | null
          created_at?: string | null
          created_by?: string | null
          current_uses?: number | null
          discount_type?: string
          discount_value?: number
          domain?: string
          id?: string
          is_active?: boolean | null
          max_uses?: number | null
          sponsor_id?: string | null
          sponsor_name?: string
          stripe_coupon_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "iepa_sponsor_domains_sponsor_id_fkey"
            columns: ["sponsor_id"]
            isOneToOne: false
            referencedRelation: "iepa_sponsor_registrations"
            referencedColumns: ["id"]
          },
        ]
      }
      iepa_conference_documents: {
        Row: {
          id: string
          name: string
          description: string | null
          file_url: string
          file_size: number | null
          file_type: string | null
          display_order: number | null
          is_public: boolean | null
          is_active: boolean | null
          uploaded_by: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          file_url: string
          file_size?: number | null
          file_type?: string | null
          display_order?: number | null
          is_public?: boolean | null
          is_active?: boolean | null
          uploaded_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          file_url?: string
          file_size?: number | null
          file_type?: string | null
          display_order?: number | null
          is_public?: boolean | null
          is_active?: boolean | null
          uploaded_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      iepa_sponsor_registrations: {
        Row: {
          attendee_count: number | null
          attendee_names: string | null
          attending_golf: boolean | null
          billing_address: string | null
          billing_city: string | null
          billing_country: string | null
          billing_state: string | null
          billing_zip: string | null
          check_notes: string | null
          check_number: string | null
          check_received: boolean | null
          check_received_date: string | null
          company_domain: string | null
          contact_email: string | null
          contact_name: string | null
          contact_phone: string | null
          contact_title: string | null
          created_at: string | null
          exhibit_requirements: string | null
          id: string
          invoice_generated_at: string | null
          invoice_url: string | null
          linked_attendee_email: string | null
          marketing_goals: string | null
          payment_id: string | null
          payment_status: string | null
          receipt_generated_at: string | null
          receipt_url: string | null
          special_requests: string | null
          sponsor_description: string
          sponsor_image_url: string | null
          sponsor_name: string
          sponsor_url: string
          sponsor_video: string | null
          sponsorship_amount: number | null
          sponsorship_level: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          attendee_count?: number | null
          attendee_names?: string | null
          attending_golf?: boolean | null
          billing_address?: string | null
          billing_city?: string | null
          billing_country?: string | null
          billing_state?: string | null
          billing_zip?: string | null
          check_notes?: string | null
          check_number?: string | null
          check_received?: boolean | null
          check_received_date?: string | null
          company_domain?: string | null
          contact_email?: string | null
          contact_name?: string | null
          contact_phone?: string | null
          contact_title?: string | null
          created_at?: string | null
          exhibit_requirements?: string | null
          id?: string
          invoice_generated_at?: string | null
          invoice_url?: string | null
          linked_attendee_email?: string | null
          marketing_goals?: string | null
          payment_id?: string | null
          payment_status?: string | null
          receipt_generated_at?: string | null
          receipt_url?: string | null
          special_requests?: string | null
          sponsor_description: string
          sponsor_image_url?: string | null
          sponsor_name: string
          sponsor_url: string
          sponsor_video?: string | null
          sponsorship_amount?: number | null
          sponsorship_level?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          attendee_count?: number | null
          attendee_names?: string | null
          attending_golf?: boolean | null
          billing_address?: string | null
          billing_city?: string | null
          billing_country?: string | null
          billing_state?: string | null
          billing_zip?: string | null
          check_notes?: string | null
          check_number?: string | null
          check_received?: boolean | null
          check_received_date?: string | null
          company_domain?: string | null
          contact_email?: string | null
          contact_name?: string | null
          contact_phone?: string | null
          contact_title?: string | null
          created_at?: string | null
          exhibit_requirements?: string | null
          id?: string
          invoice_generated_at?: string | null
          invoice_url?: string | null
          linked_attendee_email?: string | null
          marketing_goals?: string | null
          payment_id?: string | null
          payment_status?: string | null
          receipt_generated_at?: string | null
          receipt_url?: string | null
          special_requests?: string | null
          sponsor_description?: string
          sponsor_image_url?: string | null
          sponsor_name?: string
          sponsor_url?: string
          sponsor_video?: string | null
          sponsorship_amount?: number | null
          sponsorship_level?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      iepa_user_profiles: {
        Row: {
          city: string | null
          country: string | null
          created_at: string | null
          email: string
          first_name: string
          full_name: string | null
          gender: string | null
          id: string
          import_date: string | null
          imported_from_2024: boolean | null
          job_title: string | null
          last_name: string
          organization: string | null
          phone_number: string | null
          preferred_name_on_badge: string | null
          state: string | null
          street_address: string | null
          updated_at: string | null
          user_id: string | null
          zip_code: string | null
        }
        Insert: {
          city?: string | null
          country?: string | null
          created_at?: string | null
          email: string
          first_name: string
          full_name?: string | null
          gender?: string | null
          id?: string
          import_date?: string | null
          imported_from_2024?: boolean | null
          job_title?: string | null
          last_name: string
          organization?: string | null
          phone_number?: string | null
          preferred_name_on_badge?: string | null
          state?: string | null
          street_address?: string | null
          updated_at?: string | null
          user_id?: string | null
          zip_code?: string | null
        }
        Update: {
          city?: string | null
          country?: string | null
          created_at?: string | null
          email?: string
          first_name?: string
          full_name?: string | null
          gender?: string | null
          id?: string
          import_date?: string | null
          imported_from_2024?: boolean | null
          job_title?: string | null
          last_name?: string
          organization?: string | null
          phone_number?: string | null
          preferred_name_on_badge?: string | null
          state?: string | null
          street_address?: string | null
          updated_at?: string | null
          user_id?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_admin_role: {
        Args: { user_email?: string }
        Returns: string
      }
      is_admin: {
        Args: { user_email?: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Export convenience types for admin pages
export type AttendeeRegistration =
  Database['public']['Tables']['iepa_attendee_registrations']['Row'];
export type SpeakerRegistration =
  Database['public']['Tables']['iepa_speaker_registrations']['Row'];
export type SponsorRegistration =
  Database['public']['Tables']['iepa_sponsor_registrations']['Row'];
export type SponsorDomain =
  Database['public']['Tables']['iepa_sponsor_domains']['Row'];

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
