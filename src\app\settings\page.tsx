'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Input,
  Checkbox,
} from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

export default function SettingsPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    organization: '',
    jobTitle: '',
    phone: '',
  });

  const [preferences, setPreferences] = useState({
    emailUpdates: true,
    marketingEmails: false,
    smsNotifications: false,
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // Redirect if not authenticated - use useEffect to avoid render-time side effects
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  // Show loading state while auth is being determined
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Return null if user is not authenticated (will redirect via useEffect)
  if (!user) {
    return null;
  }

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMessage('Profile updated successfully!');
    } catch {
      setMessage('Error updating profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setMessage('New passwords do not match.');
      setIsLoading(false);
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setMessage('Password must be at least 6 characters long.');
      setIsLoading(false);
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMessage('Password updated successfully!');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch {
      setMessage('Error updating password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreferencesUpdate = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setMessage('Preferences updated successfully!');
    } catch {
      setMessage('Error updating preferences. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="iepa-heading-1 mb-2">Account Settings</h1>
            <p className="iepa-body">
              Manage your account information and preferences
            </p>
          </div>

          {message && (
            <div
              className={`mb-6 ${message.includes('Error') ? 'iepa-status-error' : 'iepa-status-success'}`}
            >
              <p className="iepa-body-small">{message}</p>
            </div>
          )}

          <div className="space-y-8">
            {/* Profile Information */}
            <Card>
              <CardHeader>
                <h2 className="iepa-heading-2">Profile Information</h2>
              </CardHeader>
              <CardBody>
                <form onSubmit={handleProfileUpdate} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <Input
                      label="First Name"
                      placeholder="Enter your first name"
                      value={profileData.firstName}
                      onChange={e =>
                        setProfileData(prev => ({
                          ...prev,
                          firstName: e.target.value,
                        }))
                      }
                    />

                    <Input
                      label="Last Name"
                      placeholder="Enter your last name"
                      value={profileData.lastName}
                      onChange={e =>
                        setProfileData(prev => ({
                          ...prev,
                          lastName: e.target.value,
                        }))
                      }
                    />
                  </div>

                  <Input
                    label="Email Address"
                    type="email"
                    value={user.email || ''}
                    disabled
                    description="Email address cannot be changed. Contact support if needed."
                  />

                  <div className="grid md:grid-cols-2 gap-4">
                    <Input
                      label="Organization"
                      placeholder="Your organization"
                      value={profileData.organization}
                      onChange={e =>
                        setProfileData(prev => ({
                          ...prev,
                          organization: e.target.value,
                        }))
                      }
                    />

                    <Input
                      label="Job Title"
                      placeholder="Your job title"
                      value={profileData.jobTitle}
                      onChange={e =>
                        setProfileData(prev => ({
                          ...prev,
                          jobTitle: e.target.value,
                        }))
                      }
                    />
                  </div>

                  <Input
                    label="Phone Number"
                    placeholder="(*************"
                    value={profileData.phone}
                    onChange={e =>
                      setProfileData(prev => ({
                        ...prev,
                        phone: e.target.value,
                      }))
                    }
                  />

                  <Button type="submit" color="primary" disabled={isLoading}>
                    {isLoading ? 'Updating...' : 'Update Profile'}
                  </Button>
                </form>
              </CardBody>
            </Card>

            {/* Password Change */}
            <Card>
              <CardHeader>
                <h2 className="iepa-heading-2">Change Password</h2>
              </CardHeader>
              <CardBody>
                <form onSubmit={handlePasswordUpdate} className="space-y-6">
                  <Input
                    label="Current Password"
                    type="password"
                    placeholder="Enter your current password"
                    value={passwordData.currentPassword}
                    onChange={e =>
                      setPasswordData(prev => ({
                        ...prev,
                        currentPassword: e.target.value,
                      }))
                    }
                    autoComplete="current-password"
                  />

                  <div className="grid md:grid-cols-2 gap-4">
                    <Input
                      label="New Password"
                      type="password"
                      placeholder="Enter new password"
                      value={passwordData.newPassword}
                      onChange={e =>
                        setPasswordData(prev => ({
                          ...prev,
                          newPassword: e.target.value,
                        }))
                      }
                      autoComplete="new-password"
                      description="Must be at least 6 characters"
                    />

                    <Input
                      label="Confirm New Password"
                      type="password"
                      placeholder="Confirm new password"
                      value={passwordData.confirmPassword}
                      onChange={e =>
                        setPasswordData(prev => ({
                          ...prev,
                          confirmPassword: e.target.value,
                        }))
                      }
                      autoComplete="new-password"
                    />
                  </div>

                  <Button
                    type="submit"
                    color="primary"
                    disabled={
                      isLoading ||
                      !passwordData.currentPassword ||
                      !passwordData.newPassword
                    }
                  >
                    {isLoading ? 'Updating...' : 'Update Password'}
                  </Button>
                </form>
              </CardBody>
            </Card>

            {/* Notification Preferences */}
            <Card>
              <CardHeader>
                <h2 className="iepa-heading-2">Notification Preferences</h2>
              </CardHeader>
              <CardBody>
                <div className="space-y-4">
                  <div>
                    <h3 className="iepa-heading-3 mb-4">Email Notifications</h3>
                    <div className="space-y-3">
                      <Checkbox
                        checked={preferences.emailUpdates}
                        onCheckedChange={checked =>
                          setPreferences(prev => ({
                            ...prev,
                            emailUpdates: checked === true,
                          }))
                        }
                      >
                        <div>
                          <p className="iepa-body font-medium">
                            Conference Updates
                          </p>
                          <p className="iepa-body-small text-gray-600">
                            Important updates about the IEPA conference,
                            schedule changes, and announcements
                          </p>
                        </div>
                      </Checkbox>

                      <Checkbox
                        checked={preferences.marketingEmails}
                        onCheckedChange={checked =>
                          setPreferences(prev => ({
                            ...prev,
                            marketingEmails: checked === true,
                          }))
                        }
                      >
                        <div>
                          <p className="iepa-body font-medium">
                            Marketing Communications
                          </p>
                          <p className="iepa-body-small text-gray-600">
                            Information about future events, special offers, and
                            IEPA news
                          </p>
                        </div>
                      </Checkbox>

                      <Checkbox
                        checked={preferences.smsNotifications}
                        onCheckedChange={checked =>
                          setPreferences(prev => ({
                            ...prev,
                            smsNotifications: checked === true,
                          }))
                        }
                      >
                        <div>
                          <p className="iepa-body font-medium">
                            SMS Notifications
                          </p>
                          <p className="iepa-body-small text-gray-600">
                            Urgent conference updates and reminders via text
                            message
                          </p>
                        </div>
                      </Checkbox>
                    </div>
                  </div>

                  <Button
                    onClick={handlePreferencesUpdate}
                    color="primary"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Updating...' : 'Update Preferences'}
                  </Button>
                </div>
              </CardBody>
            </Card>

            {/* Account Actions */}
            <Card>
              <CardHeader>
                <h2 className="iepa-heading-2">Account Actions</h2>
              </CardHeader>
              <CardBody>
                <div className="space-y-4">
                  <div>
                    <h3 className="iepa-heading-3 mb-2">Download Your Data</h3>
                    <p className="iepa-body-small text-gray-600 mb-3">
                      Download a copy of your account information and
                      registration data
                    </p>
                    <Button variant="bordered" size="sm">
                      Request Data Export
                    </Button>
                  </div>

                  <div>
                    <h3 className="iepa-heading-3 mb-2">Delete Account</h3>
                    <p className="iepa-body-small text-gray-600 mb-3">
                      Permanently delete your account and all associated data.
                      This action cannot be undone.
                    </p>
                    <Button color="danger" variant="bordered" size="sm">
                      Delete Account
                    </Button>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Navigation */}
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Button as="a" href="/dashboard" variant="bordered" size="lg">
              Back to Dashboard
            </Button>
            <Button as="a" href="/register" color="primary" size="lg">
              Continue Registration
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
