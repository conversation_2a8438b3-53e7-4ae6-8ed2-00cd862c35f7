import { NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase';

const supabaseAdmin = createSupabaseAdmin();

export async function POST() {
  try {
    console.log('[SYNC-SENDGRID] Starting SendGrid status sync...');

    // Get all email logs with SendGrid message IDs that are still pending
    const { data: pendingEmails, error: fetchError } = await supabaseAdmin
      .from('iepa_email_log')
      .select('*')
      .not('sendgrid_message_id', 'is', null)
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
      .limit(100); // Process in batches

    if (fetchError) {
      console.error('[SYNC-SENDGRID] Error fetching pending emails:', fetchError);
      return NextResponse.json({
        success: false,
        error: fetchError.message
      }, { status: 500 });
    }

    if (!pendingEmails || pendingEmails.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No pending emails with SendGrid message IDs found',
        processed: 0
      });
    }

    console.log(`[SYNC-SENDGRID] Found ${pendingEmails.length} pending emails to check`);

    // For now, we'll mark emails older than 1 hour as likely delivered
    // In a real implementation, you'd use SendGrid's API to check status
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const updates = [];

    for (const email of pendingEmails) {
      const createdAt = new Date(email.created_at);
      
      if (createdAt < oneHourAgo) {
        // Assume emails older than 1 hour that haven't failed are delivered
        updates.push({
          id: email.id,
          status: 'sent',
          sent_at: email.sent_at || new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    if (updates.length > 0) {
      // Update emails in batches
      for (const update of updates) {
        const { error: updateError } = await supabaseAdmin
          .from('iepa_email_log')
          .update({
            status: update.status,
            sent_at: update.sent_at,
            updated_at: update.updated_at
          })
          .eq('id', update.id);

        if (updateError) {
          console.error(`[SYNC-SENDGRID] Error updating email ${update.id}:`, updateError);
        }
      }
    }

    console.log(`[SYNC-SENDGRID] Updated ${updates.length} email statuses`);

    return NextResponse.json({
      success: true,
      message: `Processed ${pendingEmails.length} emails, updated ${updates.length} statuses`,
      processed: pendingEmails.length,
      updated: updates.length,
      details: {
        totalPending: pendingEmails.length,
        updatedToSent: updates.length,
        stillPending: pendingEmails.length - updates.length
      }
    });

  } catch (error) {
    console.error('[SYNC-SENDGRID] Sync failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Sync failed'
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Get statistics about SendGrid message ID tracking
    const { data: stats, error: statsError } = await supabaseAdmin
      .from('iepa_email_log')
      .select('status, sendgrid_message_id')
      .not('sendgrid_message_id', 'is', null);

    if (statsError) {
      return NextResponse.json({
        success: false,
        error: statsError.message
      }, { status: 500 });
    }

    const totalWithMessageId = stats?.length || 0;
    const sentWithMessageId = stats?.filter(s => s.status === 'sent').length || 0;
    const pendingWithMessageId = stats?.filter(s => s.status === 'pending').length || 0;
    const failedWithMessageId = stats?.filter(s => s.status === 'failed').length || 0;

    // Get total emails without message IDs
    const { data: allStats, error: allStatsError } = await supabaseAdmin
      .from('iepa_email_log')
      .select('status, sendgrid_message_id');

    if (allStatsError) {
      return NextResponse.json({
        success: false,
        error: allStatsError.message
      }, { status: 500 });
    }

    const totalEmails = allStats?.length || 0;
    const emailsWithoutMessageId = allStats?.filter(s => !s.sendgrid_message_id).length || 0;

    return NextResponse.json({
      success: true,
      statistics: {
        totalEmails,
        totalWithMessageId,
        emailsWithoutMessageId,
        trackingCoverage: totalEmails > 0 ? ((totalWithMessageId / totalEmails) * 100).toFixed(1) + '%' : '0%',
        statusBreakdown: {
          sent: sentWithMessageId,
          pending: pendingWithMessageId,
          failed: failedWithMessageId
        }
      },
      webhook: {
        endpoint: '/api/sendgrid/webhook',
        status: 'configured',
        note: 'Configure this endpoint in your SendGrid dashboard for real-time delivery tracking'
      }
    });

  } catch (error) {
    console.error('[SYNC-SENDGRID] Stats failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Stats failed'
    }, { status: 500 });
  }
}
