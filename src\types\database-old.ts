// Database types for IEPA 2025 Conference Registration

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      iepa_attendee_registrations: {
        Row: {
          id: string;
          user_id: string;
          registration_type: string;
          full_name: string;
          email: string;
          first_name: string;
          last_name: string;
          name_on_badge: string;
          gender: string;
          phone_number: string;
          street_address: string;
          city: string;
          state: string;
          zip_code: string;
          organization: string;
          job_title: string;
          attending_golf: boolean;
          golf_club_rental: boolean;
          golf_club_handedness: string;
          golf_club_rental_total: number;
          meals: string[];
          dietary_needs: string;
          registration_total: number;
          golf_total: number;
          meal_total: number;
          grand_total: number;
          payment_status: string;
          payment_id?: string;
          receipt_url?: string;
          receipt_generated_at?: string;
          invoice_url?: string;
          invoice_generated_at?: string;
          // Speaker linking fields
          speaker_registration_id?: string;
          is_speaker: boolean;
          speaker_pricing_type?: string;
          // Sponsor linking fields
          sponsor_id?: string;
          sponsor_discount_code?: string;
          is_sponsor_attendee: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          registration_type: string;
          full_name?: string;
          email: string;
          first_name: string;
          last_name: string;
          name_on_badge: string;
          gender: string;
          phone_number: string;
          street_address: string;
          city: string;
          state: string;
          zip_code: string;
          organization: string;
          job_title: string;
          attending_golf?: boolean;
          golf_club_rental?: boolean;
          golf_club_handedness?: string;
          golf_club_rental_total?: number;
          meals: string[];
          dietary_needs: string;
          registration_total: number;
          golf_total: number;
          grand_total: number;
          payment_status?: string;
          payment_id?: string;
          // Speaker linking fields
          speaker_registration_id?: string;
          is_speaker?: boolean;
          speaker_pricing_type?: string;
          // Sponsor linking fields
          sponsor_id?: string;
          sponsor_discount_code?: string;
          is_sponsor_attendee?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          registration_type?: string;
          full_name?: string;
          email?: string;
          first_name?: string;
          last_name?: string;
          name_on_badge?: string;
          gender?: string;
          phone_number?: string;
          street_address?: string;
          city?: string;
          state?: string;
          zip_code?: string;
          organization?: string;
          job_title?: string;
          attending_golf?: boolean;
          golf_club_rental?: boolean;
          golf_club_handedness?: string;
          golf_club_rental_total?: number;
          meals?: string[];
          dietary_needs?: string;
          registration_total?: number;
          golf_total?: number;
          meal_total?: number;
          grand_total?: number;
          payment_status?: string;
          payment_id?: string;
          receipt_url?: string;
          receipt_generated_at?: string;
          invoice_url?: string;
          invoice_generated_at?: string;
          // Speaker linking fields
          speaker_registration_id?: string;
          is_speaker?: boolean;
          speaker_pricing_type?: string;
          // Sponsor linking fields
          sponsor_id?: string;
          sponsor_discount_code?: string;
          is_sponsor_attendee?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      iepa_speaker_registrations: {
        Row: {
          id: string;
          user_id: string;
          // Personal Information
          full_name: string;
          first_name: string;
          last_name: string;
          email: string;
          // Contact Information
          phone_number?: string;
          preferred_contact_method: string;
          // Professional Information
          organization_name: string;
          job_title: string;
          bio: string;
          // Presentation Information
          presentation_title?: string;
          presentation_description?: string;
          presentation_duration?: string;
          target_audience?: string;
          learning_objectives?: string;
          // Speaking Experience
          speaker_experience?: string;
          previous_speaking?: string;
          // Technical Requirements
          equipment_needs?: string;
          special_requests?: string;
          // File Uploads
          presentation_file_url?: string;
          headshot_url?: string;
          // Attendee registration linking
          attendee_registration_id?: string;
          speaker_pricing_type: string;
          // PDF Documents
          receipt_url?: string;
          receipt_generated_at?: string;
          invoice_url?: string;
          invoice_generated_at?: string;
          // Timestamps
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          // Personal Information
          full_name?: string;
          first_name: string;
          last_name: string;
          email: string;
          // Contact Information
          phone_number?: string;
          preferred_contact_method?: string;
          // Professional Information
          organization_name: string;
          job_title: string;
          bio: string;
          // Presentation Information
          presentation_title?: string;
          presentation_description?: string;
          presentation_duration?: string;
          target_audience?: string;
          learning_objectives?: string;
          // Speaking Experience
          speaker_experience?: string;
          previous_speaking?: string;
          // Technical Requirements
          equipment_needs?: string;
          special_requests?: string;
          // File Uploads
          presentation_file_url?: string;
          headshot_url?: string;
          // Attendee registration linking
          attendee_registration_id?: string;
          speaker_pricing_type?: string;
          // PDF Documents
          receipt_url?: string;
          receipt_generated_at?: string;
          invoice_url?: string;
          invoice_generated_at?: string;
          // Timestamps
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          // Personal Information
          full_name?: string;
          first_name?: string;
          last_name?: string;
          email?: string;
          // Contact Information
          phone_number?: string;
          preferred_contact_method?: string;
          // Professional Information
          organization_name?: string;
          job_title?: string;
          bio?: string;
          // Presentation Information
          presentation_title?: string;
          presentation_description?: string;
          presentation_duration?: string;
          target_audience?: string;
          learning_objectives?: string;
          // Speaking Experience
          speaker_experience?: string;
          previous_speaking?: string;
          // Technical Requirements
          equipment_needs?: string;
          special_requests?: string;
          // File Uploads
          presentation_file_url?: string;
          headshot_url?: string;
          // Attendee registration linking
          attendee_registration_id?: string;
          speaker_pricing_type?: string;
          // PDF Documents
          receipt_url?: string;
          receipt_generated_at?: string;
          invoice_url?: string;
          invoice_generated_at?: string;
          // Timestamps
          created_at?: string;
          updated_at?: string;
        };
      };
      iepa_sponsor_registrations: {
        Row: {
          id: string;
          user_id: string;
          sponsor_name: string;
          sponsor_url: string;
          sponsor_video?: string;
          sponsor_image_url?: string;
          sponsor_description: string;
          // Attendee linking field
          linked_attendee_email?: string;
          // Company domain for automatic discounts
          company_domain?: string;
          payment_status: string;
          payment_id?: string;
          receipt_url?: string;
          receipt_generated_at?: string;
          invoice_url?: string;
          invoice_generated_at?: string;
          // Check tracking fields
          check_received?: boolean;
          check_received_date?: string;
          check_number?: string;
          check_notes?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          sponsor_name: string;
          sponsor_url: string;
          sponsor_video?: string;
          sponsor_image_url?: string;
          sponsor_description: string;
          // Attendee linking field
          linked_attendee_email?: string;
          // Company domain for automatic discounts
          company_domain?: string;
          payment_status?: string;
          payment_id?: string;
          receipt_url?: string;
          receipt_generated_at?: string;
          invoice_url?: string;
          invoice_generated_at?: string;
          // Check tracking fields
          check_received?: boolean;
          check_received_date?: string;
          check_number?: string;
          check_notes?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          sponsor_name?: string;
          sponsor_url?: string;
          sponsor_video?: string;
          sponsor_image_url?: string;
          sponsor_description?: string;
          // Attendee linking field
          linked_attendee_email?: string;
          // Company domain for automatic discounts
          company_domain?: string;
          payment_status?: string;
          payment_id?: string;
          receipt_url?: string;
          receipt_generated_at?: string;
          invoice_url?: string;
          invoice_generated_at?: string;
          // Check tracking fields
          check_received?: boolean;
          check_received_date?: string;
          check_number?: string;
          check_notes?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      iepa_sponsor_domains: {
        Row: {
          id: string;
          sponsor_id: string;
          domain: string;
          sponsor_name: string;
          discount_type: 'percentage' | 'fixed_amount';
          discount_value: number;
          auto_discount_code?: string;
          stripe_coupon_id?: string;
          is_active: boolean;
          max_uses?: number;
          current_uses: number;
          created_by?: string;
          updated_by?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          sponsor_id: string;
          domain: string;
          sponsor_name: string;
          discount_type?: 'percentage' | 'fixed_amount';
          discount_value?: number;
          auto_discount_code?: string;
          stripe_coupon_id?: string;
          is_active?: boolean;
          max_uses?: number;
          current_uses?: number;
          created_by?: string;
          updated_by?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          sponsor_id?: string;
          domain?: string;
          sponsor_name?: string;
          discount_type?: 'percentage' | 'fixed_amount';
          discount_value?: number;
          auto_discount_code?: string;
          stripe_coupon_id?: string;
          is_active?: boolean;
          max_uses?: number;
          current_uses?: number;
          created_by?: string;
          updated_by?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      iepa_payments: {
        Row: {
          id: string;
          user_id: string;
          registration_id: string;
          registration_type: 'attendee' | 'sponsor';
          stripe_payment_intent_id: string;
          amount: number;
          currency: string;
          status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          registration_id: string;
          registration_type: 'attendee' | 'sponsor';
          stripe_payment_intent_id: string;
          amount: number;
          currency?: string;
          status: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          registration_id?: string;
          registration_type?: 'attendee' | 'sponsor';
          stripe_payment_intent_id?: string;
          amount?: number;
          currency?: string;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// Export convenience types for admin pages
export type AttendeeRegistration =
  Database['public']['Tables']['iepa_attendee_registrations']['Row'];
export type SpeakerRegistration =
  Database['public']['Tables']['iepa_speaker_registrations']['Row'];
export type SponsorRegistration =
  Database['public']['Tables']['iepa_sponsor_registrations']['Row'];
export type SponsorDomain =
  Database['public']['Tables']['iepa_sponsor_domains']['Row'];
export type PaymentRecord =
  Database['public']['Tables']['iepa_payments']['Row'];
