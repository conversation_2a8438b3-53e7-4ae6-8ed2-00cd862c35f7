/**
 * Version utility for displaying application version information
 * Combines package.json version with git information from environment variables
 */

import packageJson from '../../package.json';

/**
 * Get the current environment (development, production, etc.)
 */
function getEnvironment(): string {
  if (typeof window !== 'undefined') {
    return 'client';
  }
  return process.env.NODE_ENV || 'development';
}

/**
 * Get git commit hash from environment variables
 */
function getGitCommit(): string | null {
  return process.env.NEXT_PUBLIC_GIT_COMMIT_SHORT || null;
}

/**
 * Get git branch from environment variables
 */
function getGitBranch(): string | null {
  return process.env.NEXT_PUBLIC_GIT_BRANCH || null;
}

/**
 * Get build time from environment variables
 */
function getBuildTime(): string | null {
  return process.env.NEXT_PUBLIC_BUILD_TIME || null;
}

/**
 * Generate a short version string for display
 * Format: v{version} ({commit}) [{environment}]
 * Example: v0.1.0 (18e454a) [development]
 */
export function getShortVersion(): string {
  const version = packageJson.version;
  const commit = getGitCommit();
  const env = getEnvironment();

  let versionString = `v${version}`;

  if (commit) {
    versionString += ` (${commit})`;
  }

  if (env !== 'production') {
    versionString += ` [${env}]`;
  }

  return versionString;
}

/**
 * Get detailed version information
 */
export function getVersionInfo() {
  return {
    version: packageJson.version,
    commit: getGitCommit(),
    branch: getGitBranch(),
    buildTime: getBuildTime(),
    environment: getEnvironment(),
    shortVersion: getShortVersion(),
  };
}

/**
 * Get version for display in production (minimal)
 */
export function getProductionVersion(): string {
  const version = packageJson.version;
  const commit = getGitCommit();

  if (commit) {
    return `v${version} (${commit})`;
  }

  return `v${version}`;
}
