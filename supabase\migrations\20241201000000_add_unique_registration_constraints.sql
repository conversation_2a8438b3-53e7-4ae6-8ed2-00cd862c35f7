-- Add unique constraints for one-registration-per-user
-- Migration: 20241201000000_add_unique_registration_constraints.sql

-- For attendee registrations: only primary attendees (not spouse/child) can have one registration per user
-- This allows spouse/child registrations to be linked to the same user account
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_unique_primary_attendee_per_user 
    ON iepa_attendee_registrations (user_id) 
    WHERE attendee_type = 'attendee';

-- For speaker registrations: one registration per user
-- Note: We use DO block to handle the case where constraint already exists
DO $$
BEGIN
    BEGIN
        ALTER TABLE iepa_speaker_registrations 
            ADD CONSTRAINT unique_speaker_per_user UNIQUE (user_id);
    EXCEPTION
        WHEN duplicate_table THEN
            -- Constraint already exists, skip
            NULL;
    END;
END $$;

-- For sponsor registrations: one registration per user
DO $$
BEGIN
    BEGIN
        ALTER TABLE iepa_sponsor_registrations 
            ADD CONSTRAINT unique_sponsor_per_user UNIQUE (user_id);
    EXCEPTION
        WHEN duplicate_table THEN
            -- Constraint already exists, skip
            NULL;
    END;
END $$;
