// React Hook for Test Data Auto-filling
// Provides automatic form filling with test data via URL query parameters

import { useEffect, useCallback, useRef, useState } from 'react';

export interface TestDataOptions {
  onTestDataFilled?: (summary: string) => void;
  onError?: (error: string) => void;
  enabled?: boolean;
}

// Test data templates for different registration types
export const TEST_DATA_TEMPLATES = {
  attendee: {
    registrationType: 'iepa-member',
    linkedAttendeeEmail: '',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    nameOnBadge: '<PERSON>',
    email: '<EMAIL>',
    gender: 'male',
    phoneNumber: '(*************',
    organization: 'Clean Energy Solutions',
    jobTitle: 'Senior Energy Analyst',
    streetAddress: '123 Energy Way',
    city: 'Sacramento',
    state: 'CA',
    zipCode: '95814',
    country: 'United States',
    golfTournament: true,
    golfClubRental: true,
    golfClubHandedness: 'right',
    nightOne: true,
    nightTwo: true,
    meals: {
      sept15Dinner: true,
      sept16Breakfast: true,
      sept16Lunch: true,
      sept16Dinner: true,
      sept17Breakfast: true,
      sept17Lunch: true,
    },
    dietaryRestrictions: 'No allergies',
    emergencyContact: 'Jane Smith',
    emergencyPhone: '(*************',
  },

  speaker: {
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phoneNumber: '(*************',
    organizationName: 'Renewable Tech Innovations',
    jobTitle: 'Director of Research',
    bio: 'Dr. Sarah Johnson is a leading expert in renewable energy systems with over 15 years of experience. She has published numerous papers on solar and wind energy integration.',
    speakerPricingType: 'full-meeting-speaker',
    nameOnBadge: 'Dr. Sarah Johnson',
    gender: 'female',
    streetAddress: '456 Innovation Drive',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94105',
    golfTournament: true,
    golfClubRental: true,
    golfClubHandedness: 'left',
    nightOne: true,
    nightTwo: true,
    meals: {
      'day1-breakfast': true,
      'day1-lunch': true,
      'day1-reception': true,
      'day2-breakfast': true,
      'day2-lunch': true,
    },
    dietaryRestrictions: 'Vegetarian preferred',
  },

  sponsor: {
    sponsorName: 'Green Energy Corp',
    sponsorUrl: 'https://www.greenenergycorp.com',
    sponsorshipLevel: 'gold',
    contactName: 'Michael Davis',
    contactEmail: '<EMAIL>',
    contactPhone: '(*************',
    contactTitle: 'Marketing Director',
    billingAddress: '789 Corporate Blvd',
    billingCity: 'Los Angeles',
    billingState: 'CA',
    billingZip: '90210',
    billingCountry: 'United States',
    companyDescription: 'Leading provider of sustainable energy solutions for commercial and residential applications.',
  },

  'sponsor-attendee': {
    registrationType: 'sponsor-attendee',
    firstName: 'Lisa',
    lastName: 'Chen',
    nameOnBadge: 'Lisa Chen',
    email: '<EMAIL>',
    gender: 'female',
    phoneNumber: '(*************',
    organization: 'Green Energy Corp',
    jobTitle: 'VP of Business Development',
    streetAddress: '789 Corporate Blvd',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90210',
    country: 'United States',
    golfTournament: false,
    golfClubRental: false,
    golfClubHandedness: '',
    nightOne: true,
    nightTwo: true,
    meals: {
      sept15Dinner: true,
      sept16Breakfast: true,
      sept16Lunch: true,
      sept16Dinner: true,
      sept17Breakfast: true,
      sept17Lunch: true,
    },
    dietaryRestrictions: '',
    emergencyContact: 'David Chen',
    emergencyPhone: '(*************',
    emergencyRelationship: 'Spouse',
  },
};

export function useTestDataFill(
  formType: keyof typeof TEST_DATA_TEMPLATES,
  setFormData: (data: any) => void,
  options: TestDataOptions = {}
) {
  const { onTestDataFilled, onError, enabled = true } = options;
  const [isClient, setIsClient] = useState(false);
  const [searchParams, setSearchParams] = useState<URLSearchParams | null>(null);
  const hasFilledRef = useRef(false);

  // Only run on client side and get search params safely
  useEffect(() => {
    setIsClient(true);
    if (typeof window !== 'undefined') {
      setSearchParams(new URLSearchParams(window.location.search));
    }
  }, []);

  // Check if test data should be filled
  const shouldFillTestData = useCallback(() => {
    if (!enabled || !isClient || !searchParams) return false;

    // Check for ?test=true or ?autofill=true query parameter
    return searchParams.get('test') === 'true' ||
           searchParams.get('autofill') === 'true' ||
           searchParams.get('fill') === 'true';
  }, [searchParams, enabled, isClient]);

  // Fill test data function
  const fillTestData = useCallback(() => {
    if (hasFilledRef.current) {
      console.log(`Test data already filled for ${formType} form, skipping...`);
      return;
    }

    try {
      const testData = TEST_DATA_TEMPLATES[formType];
      if (!testData) {
        throw new Error(`No test data template found for form type: ${formType}`);
      }

      console.log(`Filling test data for ${formType} form...`);
      setFormData((prev: any) => ({
        ...prev,
        ...testData,
      }));

      hasFilledRef.current = true;
      const summary = `Test data filled for ${formType} registration form`;
      console.log('✓', summary);
      onTestDataFilled?.(summary);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fill test data';
      console.error('Test data fill error:', errorMessage);
      onError?.(errorMessage);
    }
  }, [formType, setFormData, onTestDataFilled, onError]);

  // Auto-fill on mount if query parameter is present (with debounce)
  useEffect(() => {
    if (shouldFillTestData() && !hasFilledRef.current) {
      // Small delay to ensure form is initialized and prevent multiple calls
      const timer = setTimeout(() => {
        fillTestData();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [shouldFillTestData, fillTestData]);

  // Expose test function to window for manual testing
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const functionName = `fillTestData${formType.charAt(0).toUpperCase() + formType.slice(1)}`;
      (window as any)[functionName] = fillTestData;
    }
  }, [fillTestData, formType]);

  return {
    fillTestData,
    shouldFillTestData: shouldFillTestData(),
    isTestMode: shouldFillTestData(),
  };
}

export default useTestDataFill;
