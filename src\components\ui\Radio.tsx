import { Radio as HeroRadio, RadioGroup, RadioProps } from '@heroui/react';
import { forwardRef } from 'react';

interface CustomRadioProps extends RadioProps {
  size?: 'sm' | 'md' | 'lg';
  color?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger';
}

export const Radio = forwardRef<HTMLInputElement, CustomRadioProps>(
  ({ children, ...props }, ref) => {
    return (
      <HeroRadio ref={ref} {...props}>
        {children}
      </HeroRadio>
    );
  }
);

Radio.displayName = 'Radio';

// Radio Group for single selection
interface RadioGroupOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

interface CustomRadioGroupProps {
  options: RadioGroupOption[];
  value?: string;
  defaultValue?: string;
  onValueChange?: (value: string) => void;
  label?: string;
  description?: string;
  isRequired?: boolean;
  isInvalid?: boolean;
  errorMessage?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  color?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger';
}

export const RadioGroupComponent = forwardRef<
  HTMLDivElement,
  CustomRadioGroupProps
>(({ options, ...props }, ref) => {
  return (
    <RadioGroup ref={ref} {...props}>
      {options.map(option => (
        <Radio
          key={option.value}
          value={option.value}
          description={option.description}
          isDisabled={option.disabled}
        >
          {option.label}
        </Radio>
      ))}
    </RadioGroup>
  );
});

RadioGroupComponent.displayName = 'RadioGroup';

// Export both individual and group components
export { RadioGroup, RadioGroupComponent as RadioGroupWithOptions };
