// Hook for handling signed URLs for private Supabase storage buckets
import { useState, useEffect, useMemo } from 'react';
import { convertToSignedUrl } from '@/lib/fileManagement';

interface UseSignedUrlOptions {
  expiresIn?: number; // Expiry time in seconds (default: 1 hour)
  enabled?: boolean; // Whether to fetch the signed URL (default: true)
}

interface UseSignedUrlResult {
  signedUrl: string | null;
  loading: boolean;
  error: string | null;
  refresh: () => void;
}

/**
 * Hook to convert public URLs to signed URLs for private bucket access
 */
export function useSignedUrl(
  publicUrl: string | null | undefined,
  bucket: string,
  options: UseSignedUrlOptions = {}
): UseSignedUrlResult {
  const { expiresIn = 3600, enabled = true } = options;
  
  const [signedUrl, setSignedUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSignedUrl = async () => {
    if (!publicUrl || !enabled) {
      setSignedUrl(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await convertToSignedUrl(publicUrl, bucket, expiresIn);
      
      if (result.success && result.url) {
        setSignedUrl(result.url);
      } else {
        throw new Error(result.error || 'Failed to generate signed URL');
      }
    } catch (err) {
      console.error('Error fetching signed URL:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setSignedUrl(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSignedUrl();
  }, [publicUrl, bucket, expiresIn, enabled]);

  return {
    signedUrl,
    loading,
    error,
    refresh: fetchSignedUrl,
  };
}

/**
 * Hook for multiple signed URLs
 */
export function useSignedUrls(
  urls: Array<{ url: string | null | undefined; bucket: string; key: string }>,
  options: UseSignedUrlOptions = {}
): Record<string, UseSignedUrlResult> {
  const { expiresIn = 3600, enabled = true } = options;

  const [results, setResults] = useState<Record<string, UseSignedUrlResult>>({});

  // Create a stable string representation of the URLs for dependency comparison
  const urlsKey = useMemo(() => {
    return JSON.stringify(
      urls.map(({ url, bucket, key }) => ({ url, bucket, key }))
    );
  }, [urls]);

  useEffect(() => {
    if (!enabled) {
      setResults({});
      return;
    }

    const parsedUrls = JSON.parse(urlsKey) as Array<{ url: string | null | undefined; bucket: string; key: string }>;

    const fetchAllUrls = async () => {
      const newResults: Record<string, UseSignedUrlResult> = {};

      // Initialize loading states
      parsedUrls.forEach(({ key }) => {
        newResults[key] = {
          signedUrl: null,
          loading: true,
          error: null,
          refresh: () => {},
        };
      });

      // Set initial loading state
      setResults(prev => ({ ...prev, ...newResults }));

      // Fetch signed URLs sequentially to avoid race conditions
      for (const { url, bucket, key } of parsedUrls) {
        try {
          if (!url) {
            setResults(prev => ({
              ...prev,
              [key]: {
                signedUrl: null,
                loading: false,
                error: null,
                refresh: () => fetchAllUrls(),
              },
            }));
            continue;
          }

          const result = await convertToSignedUrl(url, bucket, expiresIn);

          setResults(prev => ({
            ...prev,
            [key]: {
              signedUrl: result.success ? result.url || null : null,
              loading: false,
              error: result.success ? null : result.error || 'Failed to generate signed URL',
              refresh: () => fetchAllUrls(),
            },
          }));
        } catch (err) {
          setResults(prev => ({
            ...prev,
            [key]: {
              signedUrl: null,
              loading: false,
              error: err instanceof Error ? err.message : 'Unknown error',
              refresh: () => fetchAllUrls(),
            },
          }));
        }
      }
    };

    fetchAllUrls();
  }, [urlsKey, expiresIn, enabled]);

  return results;
}

/**
 * Simple hook that returns the URL as-is if it's already a signed URL,
 * or converts it to a signed URL if it's a public URL
 */
export function useFileUrl(
  url: string | null | undefined,
  bucket: string,
  options: UseSignedUrlOptions = {}
): UseSignedUrlResult {
  const { expiresIn = 3600, enabled = true } = options;

  // Check if URL is already a signed URL (contains token parameter)
  const isSignedUrl = url && url.includes('token=');

  // Always call the hook, but conditionally enable it
  const signedUrlResult = useSignedUrl(url, bucket, {
    expiresIn,
    enabled: enabled && !isSignedUrl
  });

  // If it's already a signed URL, return it as-is
  if (isSignedUrl) {
    return {
      signedUrl: url,
      loading: false,
      error: null,
      refresh: () => {},
    };
  }

  // Otherwise, return the signed URL result
  return signedUrlResult;
}
