# IEPA Conference Registration System - Database Schema

## Overview

This document describes the database schema for the IEPA Conference Registration System. The system uses PostgreSQL via Supabase with Row Level Security (RLS) enabled for data protection.

## Database Tables

### User Management

#### `iepa_user_profiles`
Stores user profile information linked to <PERSON>pabase auth users.

```sql
CREATE TABLE iepa_user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    phone TEXT,
    organization TEXT,
    country TEXT DEFAULT 'United States',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_profile UNIQUE (user_id),
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);
```

#### `iepa_admin_users`
Defines administrative users with system access.

```sql
CREATE TABLE iepa_admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_admin_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);
```

### Registration Tables

#### `iepa_attendee_registrations`
Main table for conference attendee registrations.

```sql
CREATE TABLE iepa_attendee_registrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    phone TEXT NOT NULL,
    organization TEXT,
    country TEXT DEFAULT 'United States',
    
    -- Registration details
    registration_type TEXT NOT NULL CHECK (registration_type IN (
        'iepa-member', 'non-iepa-member', 'day-use-iepa', 
        'day-use-non-iepa', 'federal-state', 'cca', 'full-meeting-speaker'
    )),
    attendee_type TEXT NOT NULL DEFAULT 'attendee' CHECK (attendee_type IN (
        'attendee', 'spouse', 'child'
    )),
    linked_attendee_email TEXT, -- For spouse/child registrations
    
    -- Event selections
    golf_tournament BOOLEAN DEFAULT false,
    golf_club_rental BOOLEAN DEFAULT false,
    
    -- Meal selections
    tuesday_lunch BOOLEAN DEFAULT false,
    tuesday_dinner BOOLEAN DEFAULT false,
    wednesday_breakfast BOOLEAN DEFAULT false,
    wednesday_lunch BOOLEAN DEFAULT false,
    wednesday_dinner BOOLEAN DEFAULT false,
    
    -- Emergency contact
    emergency_contact_name TEXT NOT NULL,
    emergency_contact_phone TEXT NOT NULL,
    
    -- Pricing and payment
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    golf_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    meal_total DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_code TEXT,
    original_total DECIMAL(10,2) NOT NULL DEFAULT 0,
    final_total DECIMAL(10,2) NOT NULL DEFAULT 0,
    
    -- Status and metadata
    payment_status TEXT NOT NULL DEFAULT 'pending' CHECK (payment_status IN (
        'pending', 'completed', 'failed', 'refunded'
    )),
    is_speaker BOOLEAN DEFAULT false,
    speaker_pricing_type TEXT CHECK (speaker_pricing_type IN ('comped', 'paid')),
    speaker_registration_id UUID,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Unique constraint: one primary attendee registration per user
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_unique_primary_attendee_per_user 
    ON iepa_attendee_registrations (user_id) 
    WHERE attendee_type = 'attendee';
```

#### `iepa_speaker_registrations`
Dedicated table for speaker-specific information.

```sql
CREATE TABLE iepa_speaker_registrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    phone TEXT NOT NULL,
    organization TEXT,
    
    -- Speaker details
    speaker_type TEXT NOT NULL CHECK (speaker_type IN ('comped', 'paid')),
    presentation_title TEXT,
    presentation_description TEXT,
    bio_information TEXT,
    presentation_file_url TEXT,
    
    -- Event selections
    golf_tournament BOOLEAN DEFAULT false,
    golf_club_rental BOOLEAN DEFAULT false,
    
    -- Emergency contact
    emergency_contact_name TEXT NOT NULL,
    emergency_contact_phone TEXT NOT NULL,
    
    -- Pricing
    speaker_fee DECIMAL(10,2) NOT NULL DEFAULT 0,
    golf_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    
    -- Status
    payment_status TEXT NOT NULL DEFAULT 'pending' CHECK (payment_status IN (
        'pending', 'completed', 'failed', 'refunded'
    )),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_speaker_per_user UNIQUE (user_id)
);
```

#### `iepa_sponsor_registrations`
Table for sponsor company registrations.

```sql
CREATE TABLE iepa_sponsor_registrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Company information
    company_name TEXT NOT NULL,
    contact_first_name TEXT NOT NULL,
    contact_last_name TEXT NOT NULL,
    contact_email TEXT NOT NULL,
    contact_phone TEXT NOT NULL,
    
    -- Sponsorship details
    sponsorship_level TEXT NOT NULL CHECK (sponsorship_level IN (
        'platinum', 'gold', 'silver', 'bronze'
    )),
    attendee_count INTEGER NOT NULL DEFAULT 0,
    special_requests TEXT,
    
    -- Payment information
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    payment_status TEXT NOT NULL DEFAULT 'pending' CHECK (payment_status IN (
        'pending', 'completed', 'failed', 'refunded'
    )),
    payment_method TEXT DEFAULT 'check' CHECK (payment_method IN ('check', 'wire', 'other')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_sponsor_per_user UNIQUE (user_id)
);
```

### Payment and Financial Tables

#### `iepa_payments`
Tracks all payment transactions.

```sql
CREATE TABLE iepa_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    registration_id UUID NOT NULL,
    registration_type TEXT NOT NULL CHECK (registration_type IN (
        'attendee', 'speaker', 'sponsor'
    )),
    
    -- Payment details
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'usd',
    payment_method TEXT,
    
    -- Stripe integration
    stripe_session_id TEXT,
    stripe_payment_intent_id TEXT,
    stripe_invoice_id TEXT,
    stripe_invoice_url TEXT,
    
    -- Status and metadata
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'completed', 'failed', 'refunded'
    )),
    failure_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Communication Tables

#### `iepa_email_logs`
Logs all email communications.

```sql
CREATE TABLE iepa_email_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_email TEXT NOT NULL,
    sender_email TEXT NOT NULL,
    subject TEXT NOT NULL,
    email_type TEXT NOT NULL CHECK (email_type IN (
        'registration_confirmation', 'payment_confirmation', 'welcome', 
        'reminder', 'admin_notification', 'password_reset'
    )),
    
    -- SendGrid integration
    sendgrid_message_id TEXT,
    sendgrid_status TEXT,
    
    -- Content and metadata
    template_id TEXT,
    template_data JSONB,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'sent', 'delivered', 'failed', 'bounced'
    )),
    error_message TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### `iepa_email_config`
Configuration for email templates and settings.

```sql
CREATE TABLE iepa_email_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key TEXT NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Indexes and Performance

### Primary Indexes
```sql
-- User lookups
CREATE INDEX idx_user_profiles_email ON iepa_user_profiles(email);
CREATE INDEX idx_user_profiles_user_id ON iepa_user_profiles(user_id);

-- Registration lookups
CREATE INDEX idx_attendee_registrations_email ON iepa_attendee_registrations(email);
CREATE INDEX idx_attendee_registrations_user_id ON iepa_attendee_registrations(user_id);
CREATE INDEX idx_attendee_registrations_payment_status ON iepa_attendee_registrations(payment_status);

-- Payment tracking
CREATE INDEX idx_payments_registration ON iepa_payments(registration_id, registration_type);
CREATE INDEX idx_payments_stripe_session ON iepa_payments(stripe_session_id);
CREATE INDEX idx_payments_status ON iepa_payments(status);

-- Email logging
CREATE INDEX idx_email_logs_recipient ON iepa_email_logs(recipient_email);
CREATE INDEX idx_email_logs_type_status ON iepa_email_logs(email_type, status);
CREATE INDEX idx_email_logs_created_at ON iepa_email_logs(created_at);
```

## Row Level Security (RLS)

### User Data Protection
```sql
-- Users can only see their own profile data
CREATE POLICY "Users can view own profile" ON iepa_user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON iepa_user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can only see their own registrations
CREATE POLICY "Users can view own registrations" ON iepa_attendee_registrations
    FOR SELECT USING (auth.uid() = user_id);
```

### Admin Access
```sql
-- Admin users have full access
CREATE POLICY "Admin full access" ON iepa_attendee_registrations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM iepa_admin_users 
            WHERE email = auth.jwt() ->> 'email' 
            AND is_active = true
        )
    );
```

## Data Validation

### Triggers for Data Integrity
```sql
-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all tables with updated_at
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON iepa_user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

---

**Last Updated**: December 2024  
**Schema Version**: 1.0  
**Database**: PostgreSQL 15+ via Supabase
