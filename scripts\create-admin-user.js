#!/usr/bin/env node

/**
 * Create Admin User Script
 * Creates the admin user through the proper Supabase auth API
 * This resolves the "Database error querying schema" issue
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  console.error('NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.error('SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

// Create admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createAdminUser() {
  console.log('🚀 Creating admin user through Supabase Auth API...');
  console.log('📋 This will resolve the "Database error querying schema" issue');
  
  const email = '<EMAIL>';
  const password = 'AdminPass123!';
  
  try {
    // First, check if user already exists
    console.log('🔍 Checking for existing user...');
    const { data: existingUsers } = await supabase.auth.admin.listUsers();
    const existingUser = existingUsers.users.find(u => u.email === email);

    if (existingUser) {
      console.log('👤 User already exists:', existingUser.id);
      console.log('🗑️ Deleting existing user to recreate properly...');

      const { error: deleteError } = await supabase.auth.admin.deleteUser(existingUser.id);
      if (deleteError) {
        console.warn('⚠️ Delete warning:', deleteError.message);
      } else {
        console.log('✅ Existing user deleted');
      }
    } else {
      console.log('🔍 User not found in list, but may exist (pagination or visibility issue)');
    }
    
    // Create new user through proper auth API
    console.log('👤 Creating new admin user...');
    const { data, error } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true, // Auto-confirm email since confirmation is disabled
      user_metadata: {
        full_name: 'Admin User',
        first_name: 'Admin',
        last_name: 'User',
        role: 'admin'
      }
    });
    
    if (error) {
      if (error.message.includes('already been registered')) {
        console.log('👤 User already exists, trying to find and link...');

        // Try to find the user by searching more thoroughly
        let foundUser = null;
        let page = 1;
        const perPage = 1000;

        while (!foundUser && page <= 5) { // Check up to 5 pages
          const { data: pageUsers } = await supabase.auth.admin.listUsers({
            page: page,
            perPage: perPage
          });

          foundUser = pageUsers.users.find(u => u.email === email);
          if (foundUser) {
            console.log('✅ Found existing user:', foundUser.id);
            break;
          }
          page++;
        }

        if (foundUser) {
          // Use the existing user
          data = { user: foundUser };
        } else {
          console.error('❌ User exists but cannot be found in user list');
          return;
        }
      } else {
        console.error('❌ Error creating user:', error);
        return;
      }
    }
    
    console.log('✅ Auth user created successfully!');
    console.log('📧 Email:', data.user.email);
    console.log('🆔 ID:', data.user.id);
    console.log('✉️ Email Confirmed:', !!data.user.email_confirmed_at);
    
    // Update the admin_users table to link to the new auth user
    console.log('🔗 Linking to admin_users table...');
    const { error: updateError } = await supabase
      .from('iepa_admin_users')
      .update({ 
        user_id: data.user.id,
        updated_at: new Date().toISOString()
      })
      .eq('email', email);
      
    if (updateError) {
      console.error('❌ Error linking admin user:', updateError);
    } else {
      console.log('✅ Admin user linked successfully');
    }
    
    // Test the login
    console.log('\n🔐 Testing login...');
    
    // Create a regular client for testing login
    const testClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    
    const { data: loginData, error: loginError } = await testClient.auth.signInWithPassword({
      email: email,
      password: password
    });
    
    if (loginError) {
      console.error('❌ Login test failed:', loginError);
      console.error('🔍 This indicates the schema issue persists');
    } else {
      console.log('✅ Login test successful!');
      console.log('👤 User ID:', loginData.user.id);
      console.log('📧 Email:', loginData.user.email);
      console.log('🎉 Authentication system is now working!');
      
      // Sign out
      await testClient.auth.signOut();
    }
    
    // Verify admin access
    console.log('\n🔐 Verifying admin access...');
    const { data: adminCheck } = await supabase
      .from('iepa_admin_users')
      .select('*')
      .eq('email', email)
      .single();
      
    if (adminCheck) {
      console.log('✅ Admin access verified');
      console.log('🔑 Role:', adminCheck.role);
      console.log('🆔 User ID:', adminCheck.user_id);
    }
    
  } catch (error) {
    console.error('💥 Script error:', error);
  }
}

// Run the script
createAdminUser().then(() => {
  console.log('\n🎯 Admin user creation complete!');
  console.log('📝 Credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: AdminPass123!');
  console.log('\n🚀 You can now access the admin dashboard at:');
  console.log('   http://localhost:6969/admin');
  console.log('   (No more ?testAdmin=true needed!)');
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
