/**
 * Global Teardown for IEPA E2E Tests
 *
 * This file runs after all tests complete and cleans up the testing environment.
 * It removes test data and generates final reports.
 */

import { chromium } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalTeardown() {
  console.log('🧹 Starting global teardown for IEPA E2E tests...');

  try {
    // Launch browser for cleanup tasks
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();

    const baseURL = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:6969';

    // Cleanup test data if needed
    console.log('🗑️ Cleaning up test data...');
    await cleanupTestData(page, baseURL);

    // Generate test summary
    console.log('📊 Generating test summary...');
    await generateTestSummary();

    // Cleanup
    await browser.close();

    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  }
}

async function cleanupTestData(page, baseURL) {
  // This would typically clean up test registrations, users, etc.
  // For now, we'll just log the cleanup attempt

  try {
    // Example: Delete test registrations
    const testEmail = '<EMAIL>';
    console.log(`🗑️ Attempting to cleanup test data for ${testEmail}...`);

    // In a real implementation, you might call an admin API to cleanup test data
    // await page.request.delete(`${baseURL}/api/admin/cleanup-test-data`, {
    //   data: { email: testEmail }
    // });

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.log('⚠️ Test data cleanup failed:', error.message);
  }
}

async function generateTestSummary() {
  try {
    const resultsPath = 'test-results/results.json';

    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));

      const summary = {
        timestamp: new Date().toISOString(),
        totalTests: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        environment: {
          baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:6969',
          nodeEnv: process.env.NODE_ENV || 'development',
          ci: !!process.env.CI,
        },
      };

      // Write summary
      const summaryPath = 'test-results/summary.json';
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));

      console.log('📊 Test Summary:');
      console.log(`   Total Tests: ${summary.totalTests}`);
      console.log(`   Passed: ${summary.passed}`);
      console.log(`   Failed: ${summary.failed}`);
      console.log(`   Skipped: ${summary.skipped}`);
      console.log(`   Duration: ${Math.round(summary.duration / 1000)}s`);
      console.log(`   Summary saved to: ${summaryPath}`);
    } else {
      console.log('⚠️ No test results found for summary generation');
    }
  } catch (error) {
    console.log('⚠️ Failed to generate test summary:', error.message);
  }
}

export default globalTeardown;
