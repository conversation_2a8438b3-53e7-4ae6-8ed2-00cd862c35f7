#!/usr/bin/env node

// <PERSON>rip<PERSON> to create a user <NAME_EMAIL>
// This will link the existing auth user to a user profile with mock data

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

// Create admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTestProfile() {
  console.log('🚀 Creating user <NAME_EMAIL>...');
  
  const email = '<EMAIL>';
  
  try {
    // First, find the user in auth.users
    console.log('🔍 Looking up user in auth.users...');
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('❌ Error fetching auth users:', authError);
      return;
    }
    
    const authUser = authUsers.users.find(user => user.email === email);
    
    if (!authUser) {
      console.error(`❌ User not found in auth.users with email: ${email}`);
      console.log('📋 Available users:');
      authUsers.users.forEach(user => {
        console.log(`   - ${user.email} (ID: ${user.id})`);
      });
      return;
    }
    
    console.log('✅ Found auth user:');
    console.log('📧 Email:', authUser.email);
    console.log('🆔 ID:', authUser.id);
    console.log('✉️ Email Confirmed:', !!authUser.email_confirmed_at);
    
    // Check if profile already exists
    console.log('🔍 Checking if user profile already exists...');
    const { data: existingProfile, error: profileCheckError } = await supabase
      .from('iepa_user_profiles')
      .select('*')
      .eq('user_id', authUser.id)
      .single();
    
    if (profileCheckError && profileCheckError.code !== 'PGRST116') {
      console.error('❌ Error checking existing profile:', profileCheckError);
      return;
    }
    
    if (existingProfile) {
      console.log('⚠️ User profile already exists:');
      console.log('📋 Profile data:', {
        name: `${existingProfile.first_name} ${existingProfile.last_name}`,
        email: existingProfile.email,
        phone: existingProfile.phone_number,
        organization: existingProfile.organization,
        jobTitle: existingProfile.job_title
      });
      return;
    }
    
    // Create the user profile with mock data
    console.log('📝 Creating user profile with mock data...');
    const profileData = {
      user_id: authUser.id,
      first_name: 'Test',
      last_name: 'User',
      email: email,
      phone_number: '5551234567', // Will be formatted as (*************
      organization: 'Noteware Digital',
      job_title: 'Software Developer',
      street_address: '123 Tech Street',
      city: 'San Francisco',
      state: 'CA',
      zip_code: '94105',
      country: 'United States',
      gender: 'prefer_not_to_say',
      preferred_name_on_badge: 'Test User',
      imported_from_2024: false
    };
    
    const { data: profileResult, error: profileError } = await supabase
      .from('iepa_user_profiles')
      .insert(profileData)
      .select()
      .single();
    
    if (profileError) {
      console.error('❌ Error creating profile:', profileError);
      return;
    }
    
    console.log('✅ Profile created successfully!');
    console.log('📋 Profile data:', {
      id: profileResult.id,
      name: `${profileResult.first_name} ${profileResult.last_name}`,
      email: profileResult.email,
      phone: profileResult.phone_number,
      organization: profileResult.organization,
      jobTitle: profileResult.job_title,
      address: `${profileResult.street_address}, ${profileResult.city}, ${profileResult.state} ${profileResult.zip_code}`
    });
    
    // Test the profile retrieval
    console.log('\n🧪 Testing profile retrieval...');
    const { data: retrievedProfile, error: retrieveError } = await supabase
      .from('iepa_user_profiles')
      .select('*')
      .eq('user_id', authUser.id)
      .single();
    
    if (retrieveError) {
      console.error('❌ Error retrieving profile:', retrieveError);
    } else {
      console.log('✅ Profile retrieval test successful!');
      console.log('🔗 Profile linked to user ID:', retrievedProfile.user_id);
    }
    
    console.log('\n🎯 Test user profile creation complete!');
    console.log('📝 Test Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   (Use the password you set in Supabase Auth)');
    console.log('\n🔗 Test URLs:');
    console.log('   Login: http://localhost:6969/auth/login');
    console.log('   Attendee Form: http://localhost:6969/register/attendee');
    console.log('   Speaker Form: http://localhost:6969/register/speaker');
    console.log('\n📋 Expected Prefill Data:');
    console.log('   Name: Test User');
    console.log('   Email: <EMAIL>');
    console.log('   Phone: (*************');
    console.log('   Organization: Noteware Digital');
    console.log('   Job Title: Software Developer');
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Run the script
createTestProfile().catch(console.error);
