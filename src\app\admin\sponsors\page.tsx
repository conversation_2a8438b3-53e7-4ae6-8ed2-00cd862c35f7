'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ResponsiveTable,
  ResponsiveTableBody,
  ResponsiveTableCell,
  ResponsiveTableHead,
  ResponsiveTableHeader,
  ResponsiveTableRow,
  ActionButtons,
} from '@/components/ui/responsive-table';
import { Badge } from '@/components/ui/badge';
import {
  // FiStar, // Removed - not used in responsive table
  FiRefreshCw,
  FiDownload,
  FiEye,
  FiEdit,
  FiTrash2,
  FiGlobe,
  FiCalendar,
  FiMail,
} from 'react-icons/fi';
import { formatCurrency } from '@/lib/pdf-generation/utils';

interface SponsorRegistration {
  id: string;
  user_id: string;
  sponsor_name: string;
  sponsor_url: string;
  sponsor_image_url?: string;
  sponsor_description: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_title?: string;
  sponsorship_level?: 'platinum' | 'gold' | 'silver' | 'bronze' | 'diamond';
  sponsorship_amount?: number;
  billing_address?: string;
  billing_city?: string;
  billing_state?: string;
  billing_zip?: string;
  billing_country?: string;

  attendee_count?: string;
  attendee_names?: string;
  attending_golf: boolean;
  linked_attendee_email?: string;
  company_domain?: string;
  payment_status: 'pending' | 'completed' | 'failed';
  payment_id?: string;
  receipt_url?: string;
  receipt_generated_at?: string;
  invoice_url?: string;
  invoice_generated_at?: string;
  created_at: string;
  updated_at: string;
}

interface SponsorFilters {
  search: string;
  sponsorshipLevel: string;
  paymentStatus: string;
  golfParticipation: string;
}

export default function SponsorsPage() {
  const searchParams = useSearchParams();
  const isTestMode = searchParams?.get('testAdmin') === 'true';

  const [sponsors, setSponsors] = useState<SponsorRegistration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Modal states for future implementation
  // const [selectedSponsor, setSelectedSponsor] =
  //   useState<SponsorRegistration | null>(null);
  // const [showDetails, setShowDetails] = useState(false);
  // const [showEdit, setShowEdit] = useState(false);
  // const [showDelete, setShowDelete] = useState(false);

  const [filters, setFilters] = useState<SponsorFilters>({
    search: '',
    sponsorshipLevel: 'all',
    paymentStatus: 'all',
    golfParticipation: 'all',
  });

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0,
  });

  // Email resending state
  const [resendingEmails, setResendingEmails] = useState<Set<string>>(
    new Set()
  );
  const [emailMessages, setEmailMessages] = useState<
    Map<string, { type: 'success' | 'error'; text: string }>
  >(new Map());

  // Fetch sponsors
  const fetchSponsors = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('iepa_sponsor_registrations')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.search) {
        query = query.or(
          `sponsor_name.ilike.%${filters.search}%,contact_name.ilike.%${filters.search}%,contact_email.ilike.%${filters.search}%`
        );
      }

      if (filters.sponsorshipLevel && filters.sponsorshipLevel !== 'all') {
        query = query.eq('sponsorship_level', filters.sponsorshipLevel);
      }

      if (filters.paymentStatus && filters.paymentStatus !== 'all') {
        query = query.eq('payment_status', filters.paymentStatus);
      }

      if (filters.golfParticipation && filters.golfParticipation !== 'all') {
        query = query.eq('attending_golf', filters.golfParticipation === 'yes');
      }

      // Apply pagination
      const from = (pagination.page - 1) * pagination.limit;
      const to = from + pagination.limit - 1;
      query = query.range(from, to);

      const { data, error: fetchError, count } = await query;

      if (fetchError) throw fetchError;

      setSponsors(data || []);
      setPagination(prev => ({ ...prev, total: count || 0 }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch sponsors');
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.page, pagination.limit]);

  useEffect(() => {
    fetchSponsors();
  }, [fetchSponsors]);

  // Delete sponsor - for future implementation
  // const handleDelete = async (sponsor: SponsorRegistration) => {
  //   try {
  //     const { error: deleteError } = await supabase
  //       .from('iepa_sponsor_registrations')
  //       .delete()
  //       .eq('id', sponsor.id);

  //     if (deleteError) throw deleteError;

  //     await fetchSponsors();
  //     setShowDelete(false);
  //     setSelectedSponsor(null);
  //   } catch (err) {
  //     setError(
  //       err instanceof Error ? err.message : 'Failed to delete sponsor'
  //     );
  //   }
  // };

  // Export sponsors
  const handleExport = async () => {
    try {
      const { data, error: exportError } = await supabase
        .from('iepa_sponsor_registrations')
        .select('*')
        .csv();

      if (exportError) throw exportError;

      const blob = new Blob([data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `sponsors-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to export sponsors'
      );
    }
  };

  // Resend welcome email
  const handleResendWelcomeEmail = async (sponsor: SponsorRegistration) => {
    try {
      setResendingEmails(prev => new Set(prev).add(sponsor.id));
      setEmailMessages(prev => {
        const newMap = new Map(prev);
        newMap.delete(sponsor.id);
        return newMap;
      });

      const response = await fetch('/api/admin/resend-welcome-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sponsorId: sponsor.id,
          type: 'sponsor',
        }),
      });

      const result = await response.json();

      if (result.success) {
        setEmailMessages(prev => {
          const newMap = new Map(prev);
          newMap.set(sponsor.id, {
            type: 'success',
            text: `Welcome email sent to ${sponsor.contact_email}`,
          });
          return newMap;
        });
      } else {
        setEmailMessages(prev => {
          const newMap = new Map(prev);
          newMap.set(sponsor.id, {
            type: 'error',
            text: result.error || 'Failed to send welcome email',
          });
          return newMap;
        });
      }
    } catch (error) {
      console.error('Error resending welcome email:', error);
      setEmailMessages(prev => {
        const newMap = new Map(prev);
        newMap.set(sponsor.id, {
          type: 'error',
          text: 'Failed to send welcome email',
        });
        return newMap;
      });
    } finally {
      setResendingEmails(prev => {
        const newSet = new Set(prev);
        newSet.delete(sponsor.id);
        return newSet;
      });
      // Clear message after 5 seconds
      setTimeout(() => {
        setEmailMessages(prev => {
          const newMap = new Map(prev);
          newMap.delete(sponsor.id);
          return newMap;
        });
      }, 5000);
    }
  };

  const getSponsorshipBadge = (level: string | undefined) => {
    if (!level) {
      return <Badge className="bg-gray-100 text-gray-800">Not Set</Badge>;
    }

    switch (level.toLowerCase()) {
      case 'diamond':
        return <Badge className="bg-cyan-100 text-cyan-800">Diamond</Badge>;
      case 'platinum':
        return (
          <Badge className="bg-purple-100 text-purple-800">Platinum</Badge>
        );
      case 'gold':
        return <Badge className="bg-yellow-100 text-yellow-800">Gold</Badge>;
      case 'silver':
        return <Badge className="bg-gray-100 text-gray-800">Silver</Badge>;
      case 'bronze':
        return <Badge className="bg-orange-100 text-orange-800">Bronze</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <Button
          onClick={handleExport}
          variant="outline"
          className="flex items-center"
        >
          <FiDownload className="w-4 h-4 mr-2" />
          Export CSV
        </Button>
        <Button
          onClick={fetchSponsors}
          variant="outline"
          className="flex items-center"
        >
          <FiRefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Input
                placeholder="Search sponsors..."
                value={filters.search}
                onChange={e =>
                  setFilters(prev => ({ ...prev, search: e.target.value }))
                }
                className="w-full"
              />
            </div>
            <Select
              value={filters.sponsorshipLevel}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, sponsorshipLevel: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Sponsorship Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="diamond">Diamond</SelectItem>
                <SelectItem value="platinum">Platinum</SelectItem>
                <SelectItem value="gold">Gold</SelectItem>
                <SelectItem value="silver">Silver</SelectItem>
                <SelectItem value="bronze">Bronze</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.paymentStatus}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, paymentStatus: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Payment Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.golfParticipation}
              onValueChange={value =>
                setFilters(prev => ({ ...prev, golfParticipation: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Golf Participation" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="yes">Golf Participants</SelectItem>
                <SelectItem value="no">Non-Golf</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Error Display */}
      {error && (
        <Card>
          <CardBody className="p-4">
            <div className="text-red-600 text-sm">{error}</div>
          </CardBody>
        </Card>
      )}

      {/* Sponsors Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Sponsors ({pagination.total})</span>
            {loading && (
              <FiRefreshCw className="w-4 h-4 animate-spin text-gray-400" />
            )}
          </CardTitle>
        </CardHeader>
        <CardBody className="p-0">
          <ResponsiveTable>
            <ResponsiveTableHeader>
              <ResponsiveTableRow>
                <ResponsiveTableHead priority="high">
                  Organization
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="high">
                  Contact
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="medium">
                  Level
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="medium">
                  Amount
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="medium">
                  Payment
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="medium">
                  Check Status
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="low">Golf</ResponsiveTableHead>
                <ResponsiveTableHead priority="medium">
                  Submitted
                </ResponsiveTableHead>
                <ResponsiveTableHead priority="high">
                  Actions
                </ResponsiveTableHead>
              </ResponsiveTableRow>
            </ResponsiveTableHeader>
            <ResponsiveTableBody>
              {sponsors.map(sponsor => (
                <ResponsiveTableRow key={sponsor.id}>
                  <ResponsiveTableCell priority="high" label="Organization">
                    <div>
                      <div className="font-medium truncate max-w-32">
                        {sponsor.sponsor_name}
                      </div>
                      {sponsor.sponsor_url && (
                        <div className="text-sm text-gray-500 flex items-center">
                          <FiGlobe className="w-3 h-3 mr-1" />
                          <a
                            href={sponsor.sponsor_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="hover:text-blue-600 truncate"
                          >
                            Website
                          </a>
                        </div>
                      )}
                    </div>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell priority="high" label="Contact">
                    <div>
                      <div className="font-medium truncate max-w-32">
                        {sponsor.contact_name}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-48">
                        {sponsor.contact_email}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-32">
                        {sponsor.contact_title}
                      </div>
                    </div>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell priority="medium" label="Level">
                    {getSponsorshipBadge(sponsor.sponsorship_level)}
                  </ResponsiveTableCell>
                  <ResponsiveTableCell
                    priority="medium"
                    label="Amount"
                    className="font-medium"
                  >
                    {formatCurrency(sponsor.sponsorship_amount || 0)}
                  </ResponsiveTableCell>
                  <ResponsiveTableCell priority="medium" label="Payment">
                    {getStatusBadge(sponsor.payment_status)}
                  </ResponsiveTableCell>
                  <ResponsiveTableCell priority="medium" label="Check Status">
                    {sponsor.check_received === true ? (
                      <Badge className="bg-green-100 text-green-800">
                        Received
                      </Badge>
                    ) : (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        Not Received
                      </Badge>
                    )}
                  </ResponsiveTableCell>
                  <ResponsiveTableCell priority="low" label="Golf">
                    {sponsor.attending_golf ? (
                      <Badge className="bg-blue-100 text-blue-800">Yes</Badge>
                    ) : (
                      <Badge className="bg-gray-100 text-gray-800">No</Badge>
                    )}
                  </ResponsiveTableCell>
                  <ResponsiveTableCell
                    priority="medium"
                    label="Submitted"
                    className="text-xs text-gray-500"
                  >
                    <div className="flex items-center gap-1">
                      <FiCalendar className="w-3 h-3" />
                      <div className="flex flex-col">
                        <span>
                          {new Date(sponsor.created_at).toLocaleDateString()}
                        </span>
                        <span className="text-gray-400">
                          {new Date(sponsor.created_at).toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </span>
                      </div>
                    </div>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell priority="high" label="Actions">
                    <ActionButtons
                      actions={[
                        {
                          label: 'View Details',
                          icon: FiEye,
                          onClick: () =>
                            window.open(
                              `/admin/sponsors/view?id=${sponsor.id}${isTestMode ? '&testAdmin=true' : ''}`,
                              '_blank'
                            ),
                        },
                        {
                          label: resendingEmails.has(sponsor.id)
                            ? 'Sending...'
                            : 'Resend Email',
                          icon: FiMail,
                          onClick: () => handleResendWelcomeEmail(sponsor),
                          disabled: resendingEmails.has(sponsor.id),
                          variant: 'secondary' as const,
                        },
                        {
                          label: 'Edit Sponsor',
                          icon: FiEdit,
                          onClick: () => console.log('Edit sponsor:', sponsor),
                        },
                        {
                          label: 'Delete Sponsor',
                          icon: FiTrash2,
                          onClick: () =>
                            console.log('Delete sponsor:', sponsor),
                          variant: 'destructive' as const,
                        },
                      ]}
                      compact={true}
                    />
                  </ResponsiveTableCell>
                </ResponsiveTableRow>
              ))}
            </ResponsiveTableBody>
          </ResponsiveTable>

          {/* Email Status Messages */}
          {emailMessages.size > 0 && (
            <div className="space-y-2 p-4">
              {Array.from(emailMessages.entries()).map(
                ([sponsorId, message]) => {
                  const sponsor = sponsors.find(s => s.id === sponsorId);
                  return (
                    <div
                      key={sponsorId}
                      className={`p-3 rounded-lg border text-sm ${
                        message.type === 'success'
                          ? 'bg-green-50 border-green-200 text-green-800'
                          : 'bg-red-50 border-red-200 text-red-800'
                      }`}
                    >
                      <strong>{sponsor?.sponsor_name}:</strong> {message.text}
                    </div>
                  );
                }
              )}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-6 py-4 border-t">
              <div className="text-sm text-gray-500">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{' '}
                of {pagination.total} sponsors
              </div>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    setPagination(prev => ({
                      ...prev,
                      page: Math.max(1, prev.page - 1),
                    }))
                  }
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    setPagination(prev => ({
                      ...prev,
                      page: Math.min(totalPages, prev.page + 1),
                    }))
                  }
                  disabled={pagination.page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
