import { NextRequest, NextResponse } from 'next/server';
import { emailTemplateService } from '@/services/email-templates';
import { previewTemplate } from '@/utils/template-renderer';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log(`[EMAIL-TEMPLATES-API] Previewing template: ${id}`);

    const body = await request.json();
    const { sampleData } = body;

    const template = await emailTemplateService.getTemplateById(id);

    if (!template) {
      return NextResponse.json({
        success: false,
        error: 'Template not found'
      }, { status: 404 });
    }

    // Prepare comprehensive sample data with defaults
    const defaultSampleData = {
      name: '<PERSON>',
      email: '<EMAIL>',
      confirmationNumber: 'CONF-12345',
      registrationDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      organizationName: 'Sample Organization',
      amount: '$1,200.00',
      paymentDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      eventDates: 'March 15-17, 2025',
      venueInfo: 'Monterey Plaza Hotel & Spa, Monterey, CA',
      contactEmail: '<EMAIL>',
      // Sponsor-specific defaults
      sponsorshipLevel: 'Gold',
      sponsorshipBenefits: '<ul><li>Premium booth location</li><li>Logo on annual meeting materials</li><li>Speaking opportunity</li><li>VIP networking reception access</li></ul>',
      sponsorshipBenefitsText: '- Premium booth location\n- Logo on annual meeting materials\n- Speaking opportunity\n- VIP networking reception access',
      paymentAmount: '$5,000.00',
      // Speaker-specific defaults
      presentationTitle: 'The Future of Grid-Scale Energy Storage',
      sessionType: 'Keynote Presentation',
      sessionDate: 'March 16, 2025 at 9:00 AM',
      sessionDuration: '45 minutes',
      roomAssignment: 'Main Ballroom',
      speakerFee: '$2,500.00',
      accommodationInfo: 'Two nights at Monterey Plaza Hotel & Spa (March 15-17). Check-in confirmation will be sent separately.',
      travelInfo: 'Flight arrangements have been made. E-tickets will be sent to your email within 48 hours.',
      avRequirements: 'Wireless microphone, projector with HDMI connection, laser pointer, confidence monitor',
      speakerBio: 'Dr. John Doe is a leading expert in energy storage technologies with over 15 years of experience in renewable energy systems.',
      // General defaults
      resetUrl: 'https://example.com/reset-password',
      isGolf: false,
      welcomeContent: '<p>We look forward to seeing you at this year\'s annual meeting!</p>',
      welcomeContentText: 'We look forward to seeing you at this year\'s annual meeting!',
      ...sampleData
    };

    const subjectPreview = previewTemplate(template.subject_template, defaultSampleData);
    const htmlPreview = previewTemplate(template.html_template, defaultSampleData);
    const textPreview = template.text_template ? previewTemplate(template.text_template, defaultSampleData) : null;

    console.log(`[EMAIL-TEMPLATES-API] Generated preview for template: ${template.template_key}`);

    const response = {
      success: true,
      preview: {
        subject: subjectPreview,
        html: htmlPreview,
        text: textPreview,
        template_key: template.template_key,
        template_name: template.template_name,
        raw: {
          subject: template.subject_template,
          html: template.html_template,
          text: template.text_template
        },
        sampleData: defaultSampleData,
        variables: template.variables
      },
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response);

  } catch (error: unknown) {
    console.error('[EMAIL-TEMPLATES-API] Failed to preview template:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to preview email template',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
