# Magic Link Authentication Implementation

## Overview

The IEPA Conference Registration system now uses Supabase magic link email authentication as the primary authentication method. This streamlines the login process for returning attendees by eliminating the need for passwords.

## Features

### ✅ Implemented Features

1. **Magic Link Authentication**

   - Email-only login process
   - Secure token-based authentication via Supabase
   - Automatic redirect to intended destination after authentication

2. **Return-to-Original-Form Functionality**

   - Users are redirected back to their intended registration form after login
   - Preserves query parameters and form state
   - Works with all registration types (attendee, speaker, sponsor, spouse, child)

3. **Backward Compatibility**

   - Password-based login still available as fallback option
   - Existing user accounts remain accessible
   - Seamless migration for existing users

4. **Updated UI Components**
   - All authentication guards now use magic link by default
   - Navigation components updated to use magic link
   - Clear messaging about passwordless authentication

## Implementation Details

### Core Components

#### 1. AuthContext (`src/contexts/AuthContext.tsx`)

- Added `signInWithMagicLink` method to the authentication context
- Handles magic link sending with proper redirect URLs
- Maintains compatibility with existing authentication methods

#### 2. Magic Link Page (`src/app/auth/magic-link/page.tsx`)

- New dedicated page for magic link authentication
- Email input form with validation
- Success state showing instructions to check email
- Fallback option to use password-based login

#### 3. Auth Confirmation (`src/app/auth/confirm/page.tsx`)

- Updated to handle magic link verification
- Proper redirect logic for magic link authentication
- Maintains existing functionality for other auth types

#### 4. Authentication Guards (`src/components/auth/AuthGuard.tsx`)

- Updated to redirect to magic link page by default
- Preserves return-to functionality
- Fallback option for password-based login

### Authentication Flow

1. **User Access Attempt**

   - User tries to access protected registration page
   - Authentication guard detects unauthenticated state
   - Redirects to `/auth/magic-link?returnTo=<original-page>`

2. **Magic Link Request**

   - User enters email address
   - System sends magic link via Supabase auth
   - User receives email with secure login link

3. **Magic Link Verification**

   - User clicks link in email
   - Redirected to `/auth/confirm` with verification token
   - Token verified via Supabase
   - User authenticated and redirected to original destination

4. **Registration Access**
   - User can now access intended registration form
   - Form may be pre-filled with existing profile data
   - Authentication state persists across sessions

## Configuration

### Supabase Settings

The system uses the following Supabase configuration:

```toml
[auth.email]
enable_signup = true
enable_confirmations = false  # Important: disabled for magic links
otp_length = 6
otp_expiry = 3600  # 1 hour
```

### Email Configuration

Magic link emails are sent using the configured email service:

- Development: `<EMAIL>`
- Production: `<EMAIL>` (when migrated)

## Testing

### Manual Testing Steps

1. **Basic Magic Link Flow**

   ```
   1. Visit http://localhost:6969/register/attendee
   2. Should redirect to magic link page
   3. Enter email address
   4. Click "Send Magic Link"
   5. Check email for magic link
   6. Click link to authenticate
   7. Should redirect back to registration form
   ```

2. **Return-to Functionality**

   ```
   1. Visit any registration page while unauthenticated
   2. Complete magic link authentication
   3. Verify redirect back to original page
   ```

3. **Fallback Authentication**
   ```
   1. From magic link page, click "Use password instead"
   2. Should redirect to password-based login
   3. Complete login with existing credentials
   ```

### Automated Testing

Run the magic link test script:

```bash
node scripts/test-magic-link.js
```

## Migration Notes

### For Existing Users

- Existing user accounts work seamlessly with magic link authentication
- No password reset required
- User profiles and registration data remain intact

### For Administrators

- Admin authentication still uses existing methods
- Magic link primarily targets conference attendee authentication
- Email configuration can be managed via admin interface

## Troubleshooting

### Common Issues

1. **Magic Link Not Received**

   - Check spam/junk folder
   - Verify email configuration in Supabase
   - Check iepa_email_config table settings

2. **Magic Link Expired**

   - Links expire after 1 hour by default
   - User can request new magic link
   - No limit on magic link requests (1 second minimum frequency)

3. **Redirect Issues**
   - Verify port-utils configuration
   - Check return-to URL encoding
   - Ensure auth confirmation page handles magic link type

### Debug Information

Magic link authentication includes extensive console logging:

- `🔗 Magic Link Debug` - Magic link request process
- `📊 Magic Link Response` - Supabase response details
- `🔄 Magic Link - User already logged in` - Redirect logic

## Security Considerations

1. **Token Security**

   - Magic links use secure tokens generated by Supabase
   - Tokens expire after 1 hour
   - One-time use tokens

2. **Email Security**

   - Links are sent to verified email addresses
   - Email delivery uses configured SMTP settings
   - No sensitive information in email content

3. **Session Management**
   - Standard Supabase session handling
   - Automatic token refresh
   - Secure session storage

## Future Enhancements

### Potential Improvements

1. **Custom Email Templates**

   - Branded magic link emails
   - Conference-specific messaging
   - Multiple language support

2. **Enhanced Security**

   - Rate limiting for magic link requests
   - IP-based restrictions
   - Device fingerprinting

3. **User Experience**
   - Remember device functionality
   - Social authentication options
   - Mobile app deep linking

## Support

For issues related to magic link authentication:

1. Check the troubleshooting section above
2. Review Supabase auth logs
3. Verify email configuration
4. Contact system administrator

---

**Document Information**
**Document Type**: Implementation Guide
**Last Updated**: December 2024
**Document Version**: 1.0
**System Version**: v0.1.0
**Implementation Status**: ✅ Active
**Prepared By**: Technical Team
