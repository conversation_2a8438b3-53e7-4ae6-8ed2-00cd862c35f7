'use client';

import { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Input } from '@/components/ui';

interface EmailConfigFormData {
  sender_email: string;
  support_email: string;
  noreply_email: string;
  from_name: string;
  reply_to_email: string;
  test_bcc_email: string;
}

export default function TestEmailConfigPage() {
  const [formData, setFormData] = useState<EmailConfigFormData>({
    sender_email: '',
    support_email: '',
    noreply_email: '',
    from_name: '',
    reply_to_email: '',
    test_bcc_email: '',
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    loadConfigurations();
  }, []);

  const loadConfigurations = async () => {
    try {
      const response = await fetch('/api/admin/email-config');
      const result = await response.json();
      
      if (result.success && result.configurations) {
        // Populate form with current values
        const configMap: Partial<EmailConfigFormData> = {};
        result.configurations.forEach((config: any) => {
          if (config.config_key in formData) {
            configMap[config.config_key as keyof EmailConfigFormData] = config.config_value;
          }
        });
        setFormData(prev => ({ ...prev, ...configMap }));
      } else {
        setMessage({ type: 'error', text: 'Failed to load email configurations' });
      }
    } catch (error) {
      console.error('Failed to load configurations:', error);
      setMessage({ type: 'error', text: 'Failed to load email configurations' });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (key: keyof EmailConfigFormData, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/admin/email-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          configurations: formData,
          changedBy: '<EMAIL>',
          changeReason: 'Updated via test interface'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setMessage({ type: 'success', text: result.message || 'Email configuration updated successfully!' });
        await loadConfigurations();
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to save configuration' });
      }
    } catch (error: any) {
      console.error('Failed to save configuration:', error);
      setMessage({ type: 'error', text: error.message || 'Failed to save configuration' });
    } finally {
      setSaving(false);
    }
  };

  const handleTestEmail = async () => {
    if (!testEmail.trim()) {
      setMessage({ type: 'error', text: 'Please enter a test email address' });
      return;
    }

    setTesting(true);
    try {
      const response = await fetch('/api/test-email-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ testEmail: testEmail.trim() }),
      });
      
      const result = await response.json();
      
      if (result.success && result.emailSent) {
        setMessage({ type: 'success', text: `Test email sent successfully to ${testEmail}!` });
      } else {
        setMessage({ type: 'error', text: result.error || 'Test email failed to send' });
      }
    } catch (error: any) {
      console.error('Test email failed:', error);
      setMessage({ type: 'error', text: error.message || 'Test email failed' });
    } finally {
      setTesting(false);
    }
  };

  if (loading) {
    return (
      <div className="iepa-container">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-iepa-blue mx-auto mb-4"></div>
          <p className="iepa-body">Loading email configuration...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-4xl mx-auto">
          {/* Message Display */}
          {message && (
            <div className={`mb-6 p-4 rounded-lg ${
              message.type === 'success' 
                ? 'bg-green-100 text-green-800 border border-green-200' 
                : 'bg-red-100 text-red-800 border border-red-200'
            }`}>
              <div className="flex justify-between items-center">
                <span>{message.text}</span>
                <button 
                  onClick={() => setMessage(null)}
                  className="ml-4 text-lg font-bold hover:opacity-70"
                >
                  ×
                </button>
              </div>
            </div>
          )}

          <div className="mb-6">
            <h1 className="iepa-heading-1 mb-2">Email Configuration Test</h1>
            <p className="iepa-body text-gray-600">
              Test interface for email configuration management (no authentication required)
            </p>
          </div>

          {/* Configuration Form */}
          <Card className="mb-6">
            <CardHeader>
              <h2 className="iepa-heading-2">Email Settings</h2>
              <p className="iepa-body-small text-gray-600">
                Configure email addresses and display names for the system
              </p>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Sender Email *
                  </label>
                  <Input
                    type="email"
                    value={formData.sender_email}
                    onChange={(e) => handleInputChange('sender_email', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Support Email *
                  </label>
                  <Input
                    type="email"
                    value={formData.support_email}
                    onChange={(e) => handleInputChange('support_email', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    No-Reply Email *
                  </label>
                  <Input
                    type="email"
                    value={formData.noreply_email}
                    onChange={(e) => handleInputChange('noreply_email', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    From Name *
                  </label>
                  <Input
                    type="text"
                    value={formData.from_name}
                    onChange={(e) => handleInputChange('from_name', e.target.value)}
                    placeholder="IEPA Conference 2025"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reply-To Email
                  </label>
                  <Input
                    type="email"
                    value={formData.reply_to_email}
                    onChange={(e) => handleInputChange('reply_to_email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Test BCC Email
                  </label>
                  <Input
                    type="email"
                    value={formData.test_bcc_email}
                    onChange={(e) => handleInputChange('test_bcc_email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <Button
                  onClick={handleSave}
                  disabled={saving}
                  className="bg-white border-2 border-iepa-blue text-iepa-blue hover:bg-iepa-blue hover:text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  {saving ? 'Saving...' : '💾 Save Configuration'}
                </Button>
              </div>
            </CardBody>
          </Card>

          {/* Test Email */}
          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2">Test Email Configuration</h2>
              <p className="iepa-body-small text-gray-600">
                Send a test email to verify the current configuration
              </p>
            </CardHeader>
            <CardBody>
              <div className="flex gap-3">
                <Input
                  type="email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="flex-1"
                />
                <Button
                  onClick={handleTestEmail}
                  disabled={testing || !testEmail.trim()}
                  variant="outline"
                  className="px-6 py-2"
                >
                  {testing ? 'Sending...' : '📧 Send Test Email'}
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>
    </div>
  );
}
