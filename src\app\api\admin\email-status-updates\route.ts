import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    console.log('[EMAIL-STATUS-UPDATES-API] Checking for email status updates...');

    const { since } = await request.json();

    // Initialize Supabase admin client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({
        success: false,
        error: 'Missing Supabase configuration'
      }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // Get recent email status changes
    let query = supabase
      .from('iepa_email_log')
      .select('id, status, updated_at, subject, recipient_email')
      .order('updated_at', { ascending: false });

    // If 'since' timestamp is provided, only get updates after that time
    if (since) {
      query = query.gt('updated_at', since);
    } else {
      // If no 'since' timestamp, get updates from the last 5 minutes
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();
      query = query.gt('updated_at', fiveMinutesAgo);
    }

    const { data: recentUpdates, error: fetchError } = await query.limit(50);

    if (fetchError) {
      console.error('[EMAIL-STATUS-UPDATES-API] Failed to fetch updates:', fetchError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch email status updates'
      }, { status: 500 });
    }

    // For demonstration, we'll simulate some status changes
    // In a real implementation, you would track actual status changes
    const changes = recentUpdates?.map(email => ({
      emailId: email.id,
      oldStatus: 'pending', // This would come from a status change log
      newStatus: email.status,
      timestamp: email.updated_at,
      subject: email.subject,
      recipientEmail: email.recipient_email,
    })) || [];

    // Filter out emails that haven't actually changed status
    const actualChanges = changes.filter(change => 
      change.oldStatus !== change.newStatus
    );

    console.log(`[EMAIL-STATUS-UPDATES-API] Found ${actualChanges.length} status changes`);

    return NextResponse.json({
      success: true,
      changes: actualChanges,
      timestamp: new Date().toISOString(),
      totalChecked: recentUpdates?.length || 0
    });

  } catch (error) {
    console.error('[EMAIL-STATUS-UPDATES-API] Failed to check status updates:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to check email status updates'
    }, { status: 500 });
  }
}

// Alternative endpoint for WebSocket-style updates (if needed in the future)
export async function GET(request: NextRequest) {
  try {
    // This could be used for Server-Sent Events (SSE) in the future
    const headers = new Headers({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });

    // For now, just return a simple response
    return new Response('data: {"type":"connected"}\n\n', {
      headers,
    });

  } catch (error) {
    console.error('[EMAIL-STATUS-UPDATES-SSE] Failed to establish SSE connection:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to establish real-time connection'
    }, { status: 500 });
  }
}
