// Golf Add-On Registration Update API
// Update existing registration with golf tournament add-on data

import { NextRequest, NextResponse } from 'next/server';
import { updateRegistrationWithGolfAddOn } from '@/services/golfAddOn';
import type { GolfAddOnUpdateRequest } from '@/types/golfAddOn';

export async function PATCH(request: NextRequest) {
  try {
    console.log('🏌️ Updating registration with golf add-on');

    const body: GolfAddOnUpdateRequest = await request.json();
    const { registrationId, golfData, paymentData } = body;

    // Validate required fields
    if (!registrationId || !golfData) {
      console.error('❌ Missing required fields for golf add-on update');
      return NextResponse.json(
        { error: 'Registration ID and golf data are required' },
        { status: 400 }
      );
    }

    // Validate golf data structure
    const requiredGolfFields = [
      'attending_golf',
      'golf_club_rental', 
      'golf_club_handedness',
      'golf_total',
      'golf_club_rental_total',
      'grand_total'
    ];

    const missingFields = requiredGolfFields.filter(field => 
      !(field in golfData) || golfData[field as keyof typeof golfData] === undefined
    );

    if (missingFields.length > 0) {
      console.error('❌ Missing golf data fields:', missingFields);
      return NextResponse.json(
        { error: `Missing golf data fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    console.log('📝 Updating registration:', registrationId, 'with golf data:', golfData);

    // Update registration
    const result = await updateRegistrationWithGolfAddOn(
      registrationId,
      golfData,
      paymentData
    );

    if (!result.success) {
      console.error('❌ Failed to update registration:', result.error);
      return NextResponse.json(
        { error: result.error || 'Failed to update registration' },
        { status: 500 }
      );
    }

    console.log('✅ Registration updated successfully:', result.registration?.id);

    return NextResponse.json({
      success: true,
      message: 'Registration updated with golf add-on',
      data: {
        registrationId: result.registration?.id,
        registration: result.registration,
      },
    });

  } catch (error) {
    console.error('❌ Error updating registration with golf add-on:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🏌️ Processing golf add-on registration update (POST)');

    const body = await request.json();
    const { registrationId, golfData, paymentData } = body;

    // Validate required fields
    if (!registrationId || !golfData) {
      console.error('❌ Missing required fields for golf add-on update');
      return NextResponse.json(
        { error: 'Registration ID and golf data are required' },
        { status: 400 }
      );
    }

    console.log('📝 Processing golf add-on update for registration:', registrationId);

    // Update registration
    const result = await updateRegistrationWithGolfAddOn(
      registrationId,
      golfData,
      paymentData
    );

    if (!result.success) {
      console.error('❌ Failed to update registration:', result.error);
      return NextResponse.json(
        { error: result.error || 'Failed to update registration' },
        { status: 500 }
      );
    }

    console.log('✅ Golf add-on registration update completed:', result.registration?.id);

    return NextResponse.json({
      success: true,
      message: 'Golf add-on processed successfully',
      data: {
        registrationId: result.registration?.id,
        registration: result.registration,
      },
    });

  } catch (error) {
    console.error('❌ Error processing golf add-on registration update:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    );
  }
}
