import { test, expect } from '@playwright/test';

test.describe('Discount Code Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the registration page
    await page.goto('/register/attendee');
  });

  test('should display discount code input on review step', async ({ page }) => {
    // Fill out the form to reach the review step
    await page.selectOption('[data-testid="registration-type-select"]', 'iepa-member');
    await page.click('button:has-text("Next Step")');

    // Fill personal information
    await page.fill('[data-testid="full-name-input"]', 'Test User');
    await page.fill('[data-testid="first-name-input"]', 'Test');
    await page.fill('[data-testid="last-name-input"]', 'User');
    await page.fill('[data-testid="name-on-badge-input"]', 'Test User');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.selectOption('[data-testid="gender-select"]', 'male');
    await page.click('button:has-text("Next Step")');

    // Fill contact information
    await page.fill('[data-testid="phone-number-input"]', '************');
    await page.fill('[data-testid="organization-input"]', 'Test Organization');
    await page.fill('[data-testid="job-title-input"]', 'Test Title');
    await page.fill('[data-testid="street-address-input"]', '123 Test St');
    await page.fill('[data-testid="city-input"]', 'Test City');
    await page.selectOption('[data-testid="state-select"]', 'CA');
    await page.fill('[data-testid="zip-code-input"]', '12345');
    await page.click('button:has-text("Next Step")');

    // Skip event options
    await page.click('button:has-text("Next Step")');

    // Fill emergency contact
    await page.fill('[data-testid="emergency-contact-input"]', 'Emergency Contact');
    await page.fill('[data-testid="emergency-phone-input"]', '************');
    await page.click('button:has-text("Next Step")');

    // Should be on review step now
    await expect(page.locator('h2:has-text("6. Review & Payment")')).toBeVisible();
    
    // Check for discount code input
    await expect(page.locator('button:has-text("Have a discount code?")')).toBeVisible();
  });

  test('should show discount code input when clicked', async ({ page }) => {
    // Navigate to review step (simplified for test)
    await page.goto('/register/attendee#step-6');
    
    // Click the discount code button
    await page.click('button:has-text("Have a discount code?")');
    
    // Should show the input form
    await expect(page.locator('input[placeholder="Enter code"]')).toBeVisible();
    await expect(page.locator('button:has-text("Apply")')).toBeVisible();
  });

  test('should validate invalid discount code', async ({ page }) => {
    // Mock the API response for invalid code
    await page.route('/api/discount-codes/validate', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Invalid discount code',
          valid: false,
        }),
      });
    });

    // Navigate to review step and open discount input
    await page.goto('/register/attendee#step-6');
    await page.click('button:has-text("Have a discount code?")');
    
    // Enter invalid code
    await page.fill('input[placeholder="Enter code"]', 'INVALID');
    await page.click('button:has-text("Apply")');
    
    // Should show error message
    await expect(page.locator('text=Invalid discount code')).toBeVisible();
  });

  test('should apply valid discount code', async ({ page }) => {
    // Mock the API response for valid code
    await page.route('/api/discount-codes/validate', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          valid: true,
          discountCode: {
            id: 'test-id',
            code: 'SAVE20',
            name: '20% Off Conference',
            description: 'Save 20% on your registration',
            discountType: 'percentage',
            discountValue: 20,
            stripeCouponId: 'save20_coupon',
          },
          calculation: {
            originalAmount: 2300,
            discountAmount: 460,
            finalAmount: 1840,
            savings: 460,
          },
        }),
      });
    });

    // Navigate to review step and open discount input
    await page.goto('/register/attendee#step-6');
    await page.click('button:has-text("Have a discount code?")');
    
    // Enter valid code
    await page.fill('input[placeholder="Enter code"]', 'SAVE20');
    await page.click('button:has-text("Apply")');
    
    // Should show applied discount
    await expect(page.locator('text=SAVE20')).toBeVisible();
    await expect(page.locator('text=20% Off Conference')).toBeVisible();
    await expect(page.locator('text=-$460.00')).toBeVisible();
    await expect(page.locator('text=$1,840')).toBeVisible();
  });

  test('should remove applied discount code', async ({ page }) => {
    // Mock the API response for valid code
    await page.route('/api/discount-codes/validate', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          valid: true,
          discountCode: {
            id: 'test-id',
            code: 'SAVE20',
            name: '20% Off Conference',
            description: 'Save 20% on your registration',
            discountType: 'percentage',
            discountValue: 20,
            stripeCouponId: 'save20_coupon',
          },
          calculation: {
            originalAmount: 2300,
            discountAmount: 460,
            finalAmount: 1840,
            savings: 460,
          },
        }),
      });
    });

    // Navigate to review step and apply discount
    await page.goto('/register/attendee#step-6');
    await page.click('button:has-text("Have a discount code?")');
    await page.fill('input[placeholder="Enter code"]', 'SAVE20');
    await page.click('button:has-text("Apply")');
    
    // Should show applied discount
    await expect(page.locator('text=SAVE20')).toBeVisible();
    
    // Remove the discount
    await page.click('button[aria-label="Remove discount"]');
    
    // Should hide the discount and show original total
    await expect(page.locator('text=SAVE20')).not.toBeVisible();
    await expect(page.locator('button:has-text("Have a discount code?")')).toBeVisible();
  });
});

test.describe('Admin Discount Code Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to admin discount codes page
    await page.goto('/admin/discount-codes');
  });

  test('should display discount codes list', async ({ page }) => {
    // Mock the API response
    await page.route('/api/admin/discount-codes', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: '1',
              code: 'SAVE20',
              name: '20% Off Conference',
              description: 'Save 20% on your registration',
              discount_type: 'percentage',
              discount_value: 20,
              stripe_coupon_id: 'save20_coupon',
              max_uses: 100,
              current_uses: 5,
              is_active: true,
              valid_until: '2025-12-31',
              created_at: '2025-01-01',
              usage_count: [{ count: 5 }],
            },
          ],
          pagination: {
            page: 1,
            limit: 50,
            total: 1,
            totalPages: 1,
          },
        }),
      });
    });

    await page.reload();

    // Should show the discount codes table
    await expect(page.locator('h1:has-text("Discount Codes")')).toBeVisible();
    await expect(page.locator('text=SAVE20')).toBeVisible();
    await expect(page.locator('text=20% Off Conference')).toBeVisible();
    await expect(page.locator('text=20%')).toBeVisible();
    await expect(page.locator('text=5 / 100')).toBeVisible();
  });

  test('should show create discount code dialog', async ({ page }) => {
    await page.click('button:has-text("Create Discount Code")');
    
    await expect(page.locator('text=Create New Discount Code')).toBeVisible();
    await expect(page.locator('text=Create a new discount code for conference registrations')).toBeVisible();
  });

  test('should toggle discount code status', async ({ page }) => {
    // Mock the initial list
    await page.route('/api/admin/discount-codes', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: '1',
              code: 'SAVE20',
              name: '20% Off Conference',
              is_active: true,
              usage_count: [{ count: 5 }],
            },
          ],
        }),
      });
    });

    // Mock the toggle API
    await page.route('/api/admin/discount-codes/1', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Discount code deactivated',
        }),
      });
    });

    await page.reload();

    // Click the toggle button
    await page.click('button[aria-label="Toggle discount code"]');
    
    // Should show success message (would need toast implementation)
    // This is a placeholder for the actual toast notification test
  });
});
