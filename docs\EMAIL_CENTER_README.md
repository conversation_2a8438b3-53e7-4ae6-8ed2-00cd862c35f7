# IEPA Email Center Documentation

## Overview

The IEPA Email Center is a comprehensive email management system designed for the Independent Energy Producers Association's Annual Meeting registration platform. It provides administrators with powerful tools to send, track, and manage email communications with attendees, speakers, and sponsors.

## Features

### 📧 Email Management

- **Send Bulk Emails**: Send emails to all attendees, speakers, sponsors, or custom recipient lists
- **Email Templates**: Pre-configured email types for different scenarios with full management interface
- **HTML Support**: Rich text formatting with HTML support and live preview
- **Attachment Support**: Handle emails with file attachments
- **Resend Functionality**: Resend welcome emails for attendees and sponsors from admin interface

### 📊 Email Tracking & Analytics

- **Real-time Status Tracking**: Monitor email delivery status (sent, failed, pending)
- **Comprehensive Statistics**: View email success rates and delivery metrics
- **SendGrid Integration**: Track emails through SendGrid with message IDs
- **Error Logging**: Detailed error messages for failed email deliveries

### 🔍 Advanced Filtering & Search

- **Multi-criteria Filtering**: Filter by status, email type, date range
- **Full-text Search**: Search across subjects, recipients, and content
- **Real-time Updates**: Live status updates with polling
- **Export Functionality**: Export email logs to CSV for analysis

### 🎯 User Experience

- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Keyboard Navigation**: Full keyboard accessibility support
- **Loading States**: Skeleton loading for better perceived performance
- **Error Handling**: Graceful error handling with user-friendly messages

## Architecture

### Frontend Components

```
src/components/admin/
├── EmailStatusSummary.tsx     # Statistics overview
├── EmailLogCard.tsx           # Individual email log display
├── EmailLogFilters.tsx        # Filtering and search controls
├── SendEmailForm.tsx          # Email composition form
├── EmailLogSkeleton.tsx       # Loading state components
├── EmailContentPreviewModal.tsx # Email content viewer
└── VirtualizedEmailList.tsx   # Performance-optimized list
```

### Backend API Endpoints

```
src/app/api/admin/
├── email-logs/route.ts        # GET, DELETE email logs
├── send-email/route.ts        # POST send emails
├── retry-email/route.ts       # POST retry failed emails
├── email-content/[id]/route.ts # GET email content details
└── email-status-updates/route.ts # POST real-time updates
```

### Database Schema

```sql
-- Email log table
CREATE TABLE iepa_email_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recipient_email VARCHAR(255) NOT NULL,
  recipient_name VARCHAR(255),
  sender_email VARCHAR(255) NOT NULL,
  sender_name VARCHAR(255),
  subject VARCHAR(500) NOT NULL,
  email_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  content_preview TEXT,
  has_attachments BOOLEAN DEFAULT FALSE,
  sendgrid_message_id VARCHAR(255),
  error_message TEXT,
  user_id UUID REFERENCES auth.users(id),
  registration_id UUID,
  payment_id UUID,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_iepa_email_log_recipient ON iepa_email_log(recipient_email);
CREATE INDEX idx_iepa_email_log_type ON iepa_email_log(email_type);
CREATE INDEX idx_iepa_email_log_status ON iepa_email_log(status);
CREATE INDEX idx_iepa_email_log_created_at ON iepa_email_log(created_at);
```

## Installation & Setup

### Prerequisites

- Node.js 18+
- Next.js 14+
- Supabase account and project
- SendGrid account (for email delivery)

### Environment Variables

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# SendGrid Configuration (optional)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
```

### Database Setup

1. Run the SQL schema in your Supabase SQL editor:

```sql
-- Copy the schema from the Database Schema section above
```

2. Enable Row Level Security (RLS):

```sql
-- Enable RLS
ALTER TABLE iepa_email_log ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Service role full access"
  ON iepa_email_log FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Admins can view all email logs"
  ON iepa_email_log FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM iepa_admin_users
      WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
      AND is_active = true
    )
  );
```

### Installation Steps

1. Install dependencies:

```bash
npm install @tanstack/react-query react-window
```

2. Add the email center route to your admin navigation
3. Configure your email service (SendGrid recommended)
4. Test the setup with sample data

## Usage Guide

### Accessing the Email Center

Navigate to `/admin/emails` in your admin dashboard. You'll see:

1. **Email Statistics Summary**: Overview of sent, failed, pending, and total emails
2. **Email Log Filters**: Search and filter controls
3. **Email Logs List**: Paginated list of all email communications
4. **Send Email Form**: Compose and send new emails

### Sending Emails

1. **Select Recipients**:

   - All Attendees: Everyone registered for the event
   - All Speakers: Registered speakers only
   - All Sponsors: Sponsor contacts
   - Custom: Enter specific email addresses

2. **Choose Email Type**:

   - Announcement: General announcements
   - Reminder: Event reminders
   - Update: Information updates
   - Notification: System notifications
   - Custom: Other email types

3. **Compose Message**:

   - Enter subject line
   - Write message content (HTML supported)
   - Use preview feature to review formatting

4. **Send**: Click "Send Email" to deliver immediately

### Managing Email Logs

#### Filtering Options

- **Status**: Filter by sent, failed, or pending emails
- **Email Type**: Filter by email category
- **Date Range**: Filter by creation or send date
- **Search**: Full-text search across all fields

#### Available Actions

- **View Content**: See full email content and details
- **Copy SendGrid ID**: Copy tracking ID for SendGrid dashboard
- **Retry Failed**: Resend failed emails
- **Export**: Download email logs as CSV

### Sponsor Email Management

#### Resending Sponsor Welcome Emails

The system provides dedicated functionality to resend welcome emails to sponsors:

**From Sponsors List Page** (`/admin/sponsors`):

1. Navigate to the sponsors management page
2. Click the action menu (three dots) for any sponsor
3. Select "Resend Email" from the dropdown
4. View success/error feedback below the table

**From Sponsor Detail Page** (`/admin/sponsors/view?id={sponsorId}`):

1. Navigate to a specific sponsor's detail page
2. Click the "Resend Welcome Email" button in the header
3. View success/error feedback in the notification area

**Email Template Used**: The system uses the `sponsor_confirmation` template which includes:

- Sponsorship details (organization, level, confirmation number, amount)
- Sponsorship benefits information
- Conference dates and venue details
- Payment instructions
- Contact information

### Email Templates Management

#### Accessing Email Templates

Navigate to email templates via:

- **Admin Sidebar**: Click "Email Templates" in the navigation menu
- **Email Center**: Click "Manage Templates" button in the header

#### Available Templates

The system includes 6 pre-configured email templates:

1. **Sponsor Registration Confirmation** (`sponsor_confirmation`) - For sponsor welcome emails
2. **Welcome Email** (`welcome_email`) - Comprehensive welcome email with conference information
3. **Registration Confirmation** (`registration_confirmation`) - For general attendee confirmations
4. **Speaker Registration Confirmation** (`speaker_confirmation`) - For speaker confirmations
5. **Password Reset** (`password_reset`) - For password reset emails
6. **Test Template** (`test_template`) - For testing purposes

#### Template Management Features

- **Preview**: View rendered templates with sample data
- **Edit**: Modify template content, subject, and variables
- **Create**: Add new custom templates
- **Variables**: Dynamic content insertion with template variables
- **Status Management**: Activate/deactivate templates

### Monitoring & Troubleshooting

#### Real-time Updates

The system automatically polls for status updates every 30 seconds. You can:

- Enable/disable real-time updates
- Manually refresh data
- View last update timestamp

#### Error Handling

Failed emails show:

- Error message details
- Retry option for failed deliveries
- SendGrid tracking information (if available)

#### Performance Features

- Virtual scrolling for large datasets
- Skeleton loading states
- Optimized API queries with caching
- Progressive enhancement with service workers

## API Reference

### GET /api/admin/email-logs

Retrieve email logs with filtering and pagination.

**Query Parameters:**

- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 50, max: 100)
- `status` (string): Filter by status (sent, failed, pending)
- `emailType` (string): Filter by email type
- `search` (string): Search term
- `dateFrom` (string): Start date filter (ISO format)
- `dateTo` (string): End date filter (ISO format)

**Response:**

```json
{
  "success": true,
  "logs": [...],
  "statistics": {
    "totalSent": 150,
    "totalFailed": 5,
    "totalPending": 2,
    "totalEmails": 157
  },
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 157,
    "totalPages": 4,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

### POST /api/admin/send-email

Send emails to specified recipients.

**Request Body:**

```json
{
  "recipients": "all_attendees",
  "subject": "Important Update",
  "message": "<p>Your message content here</p>",
  "emailType": "announcement"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Email sent successfully",
  "recipientCount": 150,
  "emailLogId": "uuid"
}
```

### POST /api/admin/retry-email

Retry a failed email delivery.

**Request Body:**

```json
{
  "emailLogId": "original-email-uuid",
  "recipientEmail": "<EMAIL>",
  "subject": "Original Subject",
  "emailType": "retry"
}
```

### POST /api/admin/resend-welcome-email

Resend welcome emails for attendees or sponsors.

**Request Body:**

```json
{
  "attendeeId": "uuid", // For attendee emails
  "sponsorId": "uuid", // For sponsor emails
  "type": "attendee" // "attendee" or "sponsor"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Welcome email sent <NAME_EMAIL>",
  "recipient": {
    "email": "<EMAIL>",
    "name": "John Doe",
    "type": "sponsor",
    "registrationId": "uuid"
  }
}
```

### GET /api/admin/email-templates

Retrieve all email templates.

**Response:**

```json
{
  "success": true,
  "templates": [
    {
      "id": "uuid",
      "template_key": "sponsor_confirmation",
      "template_name": "Sponsor Registration Confirmation",
      "description": "Email sent to confirm successful sponsor registration",
      "subject_template": "Welcome to IEPA's 2025 Annual Meeting!",
      "html_template": "...",
      "variables": ["sponsorshipLevel", "confirmationNumber"],
      "is_active": true
    }
  ],
  "count": 6
}
```

## Testing

### Running Tests

```bash
# Unit tests
npm run test

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e

# Performance tests
npm run test:performance

# Accessibility tests
npm run test:a11y

# All tests
npm run test:all
```

### Test Coverage

The email center includes comprehensive test coverage:

- **Unit Tests**: Component logic and utilities (90%+ coverage)
- **Integration Tests**: API endpoints and database interactions
- **E2E Tests**: Complete user workflows with Playwright
- **Performance Tests**: Load times and responsiveness
- **Accessibility Tests**: WCAG 2.1 AA compliance

## Security Considerations

### Authentication & Authorization

- Admin-only access through IEPA admin user system
- Row Level Security (RLS) policies in Supabase
- Service role key protection for API access

### Data Protection

- Email content logging with privacy considerations
- Secure handling of recipient information
- GDPR compliance for data retention

### Rate Limiting

- API rate limiting to prevent abuse
- Email sending limits through SendGrid
- Monitoring for suspicious activity

## Performance Optimization

### Frontend Optimizations

- Virtual scrolling for large email lists
- React Query for intelligent caching
- Skeleton loading states
- Progressive enhancement with service workers

### Backend Optimizations

- Database indexing for fast queries
- Optimized SQL queries with proper joins
- Response caching with appropriate headers
- Pagination to limit data transfer

### Monitoring

- Performance metrics tracking
- Error rate monitoring
- User experience analytics
- Resource usage monitoring

## Troubleshooting

### Common Issues

#### Email Logs Not Loading

1. Check Supabase connection
2. Verify database table exists
3. Check RLS policies
4. Review browser console for errors

#### Emails Not Sending

1. Verify SendGrid configuration
2. Check API key permissions
3. Review email content for issues
4. Check recipient email validity

#### Performance Issues

1. Check database query performance
2. Review network requests
3. Monitor memory usage
4. Check for JavaScript errors

### Support

For technical support or questions:

- Review this documentation
- Check the troubleshooting section
- Contact the development team
- Submit issues through the project repository

## Changelog

### Version 1.0.0 (Current)

- Initial release with core email management features
- Real-time status tracking
- Advanced filtering and search
- Mobile-responsive design
- Comprehensive testing suite
- Accessibility compliance (WCAG 2.1 AA)

### Planned Features

- Email template management
- Scheduled email sending
- Advanced analytics dashboard
- Webhook integration
- Multi-language support

## Deployment

### Production Deployment Checklist

#### Pre-deployment

- [ ] Run all test suites and ensure they pass
- [ ] Update environment variables for production
- [ ] Configure SendGrid for production email delivery
- [ ] Set up production Supabase database
- [ ] Review and update RLS policies
- [ ] Configure domain and SSL certificates

#### Deployment Steps

1. **Build the application**:

   ```bash
   npm run build
   ```

2. **Deploy to Vercel** (recommended):

   ```bash
   vercel --prod
   ```

3. **Configure environment variables** in Vercel dashboard:

   - `NEXT_PUBLIC_SUPABASE_URL`
   - `SUPABASE_SERVICE_ROLE_KEY`
   - `SENDGRID_API_KEY`
   - `SENDGRID_FROM_EMAIL`

4. **Set up custom domain** (reg.iepa.com):

   - Configure DNS records
   - Enable SSL/TLS
   - Test domain accessibility

5. **Database migration**:
   - Run production SQL schema
   - Set up RLS policies
   - Create admin user accounts
   - Test database connectivity

#### Post-deployment

- [ ] Verify all functionality works in production
- [ ] Test email sending with real recipients
- [ ] Monitor error logs and performance
- [ ] Set up monitoring and alerting
- [ ] Document any production-specific configurations

### Monitoring & Maintenance

#### Health Checks

- API endpoint availability
- Database connection status
- Email service connectivity
- Performance metrics

#### Regular Maintenance

- Review email logs for issues
- Monitor database performance
- Update dependencies
- Review security settings
- Backup critical data

### Rollback Procedures

If issues occur in production:

1. **Immediate rollback**:

   ```bash
   vercel rollback
   ```

2. **Database rollback** (if needed):

   - Restore from backup
   - Revert schema changes
   - Update RLS policies

3. **Communication**:
   - Notify admin users
   - Document the issue
   - Plan resolution steps
