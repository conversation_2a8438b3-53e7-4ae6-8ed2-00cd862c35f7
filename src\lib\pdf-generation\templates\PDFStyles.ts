// PDF Styles for React-PDF Templates
// Styled components and style definitions for IEPA PDF documents

import { StyleSheet } from '@react-pdf/renderer';
import { IEPA_PDF_COLORS, PDF_FONTS } from '../config';

// Main stylesheet for PDF documents
export const pdfStyles = StyleSheet.create({
  // Page and layout styles
  page: {
    flexDirection: 'column',
    backgroundColor: IEPA_PDF_COLORS.white,
    padding: 72, // 1 inch margins
    fontFamily: PDF_FONTS.primary,
    fontSize: 11,
    color: IEPA_PDF_COLORS.gray800,
  },

  // Header styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 30,
    paddingBottom: 20,
    borderBottomWidth: 2,
    borderBottomColor: IEPA_PDF_COLORS.primaryBlue,
  },

  headerLeft: {
    flexDirection: 'column',
    flex: 1,
  },

  headerRight: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    flex: 1,
  },

  // Logo and company info
  logo: {
    width: 120,
    height: 40,
    marginBottom: 10,
  },

  companyName: {
    fontSize: 18,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
    marginBottom: 5,
  },

  companyFullName: {
    fontSize: 12,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray600,
    marginBottom: 10,
  },

  companyAddress: {
    fontSize: 10,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray600,
    lineHeight: 1.4,
  },

  // Document title and info
  documentTitle: {
    fontSize: 24,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
    textAlign: 'right' as const,
    marginBottom: 10,
  },

  documentNumber: {
    fontSize: 14,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.gray800,
    textAlign: 'right' as const,
    marginBottom: 5,
  },

  documentDate: {
    fontSize: 11,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray600,
    textAlign: 'right' as const,
  },

  // Conference information
  conferenceInfo: {
    backgroundColor: IEPA_PDF_COLORS.gray50,
    padding: 15,
    marginBottom: 20,
    borderRadius: 5,
  },

  conferenceName: {
    fontSize: 16,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
    marginBottom: 5,
  },

  conferenceDates: {
    fontSize: 12,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray700,
    marginBottom: 3,
  },

  conferenceVenue: {
    fontSize: 11,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray600,
  },

  // Customer information
  customerSection: {
    marginBottom: 25,
  },

  sectionTitle: {
    fontSize: 14,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
    marginBottom: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: IEPA_PDF_COLORS.gray300,
  },

  customerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  customerLeft: {
    flex: 1,
    marginRight: 20,
  },

  customerRight: {
    flex: 1,
  },

  customerName: {
    fontSize: 13,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.gray800,
    marginBottom: 5,
  },

  customerDetail: {
    fontSize: 11,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray600,
    marginBottom: 3,
  },

  // Line items table
  tableContainer: {
    marginBottom: 25,
  },

  tableHeader: {
    flexDirection: 'row',
    backgroundColor: IEPA_PDF_COLORS.primaryBlue,
    padding: 10,
    marginBottom: 1,
  },

  tableHeaderText: {
    fontSize: 11,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.white,
  },

  tableRow: {
    flexDirection: 'row',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: IEPA_PDF_COLORS.gray200,
  },

  tableRowAlt: {
    flexDirection: 'row',
    padding: 8,
    backgroundColor: IEPA_PDF_COLORS.gray50,
    borderBottomWidth: 1,
    borderBottomColor: IEPA_PDF_COLORS.gray200,
  },

  tableCell: {
    fontSize: 10,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray800,
  },

  tableCellBold: {
    fontSize: 10,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.gray800,
  },

  // Column widths for table
  colDescription: {
    flex: 3,
    paddingRight: 10,
  },

  colQuantity: {
    flex: 1,
    textAlign: 'center' as const,
  },

  colPrice: {
    flex: 1,
    textAlign: 'right' as const,
  },

  colTotal: {
    flex: 1,
    textAlign: 'right' as const,
  },

  // Totals section
  totalsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 25,
  },

  totalsSection: {
    width: 200,
  },

  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 3,
  },

  totalLabel: {
    fontSize: 11,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray700,
  },

  totalValue: {
    fontSize: 11,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.gray800,
  },

  grandTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 5,
    paddingTop: 8,
    borderTopWidth: 2,
    borderTopColor: IEPA_PDF_COLORS.primaryBlue,
  },

  grandTotalLabel: {
    fontSize: 13,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
  },

  grandTotalValue: {
    fontSize: 13,
    fontFamily: PDF_FONTS.secondary,
    color: IEPA_PDF_COLORS.primaryBlue,
  },

  // Payment information
  paymentSection: {
    marginBottom: 25,
  },

  paymentInfo: {
    backgroundColor: IEPA_PDF_COLORS.gray50,
    padding: 15,
    borderRadius: 5,
  },

  paymentDetail: {
    fontSize: 11,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray700,
    marginBottom: 3,
  },

  // Notes and terms
  notesSection: {
    marginBottom: 20,
  },

  notesText: {
    fontSize: 10,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray600,
    lineHeight: 1.4,
  },

  // Footer
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 72,
    right: 72,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: IEPA_PDF_COLORS.gray300,
  },

  footerText: {
    fontSize: 9,
    fontFamily: PDF_FONTS.primary,
    color: IEPA_PDF_COLORS.gray500,
  },

  // Status badges
  statusPaid: {
    backgroundColor: IEPA_PDF_COLORS.secondaryGreen,
    color: IEPA_PDF_COLORS.white,
    padding: 5,
    borderRadius: 3,
    fontSize: 10,
    fontFamily: PDF_FONTS.secondary,
    textAlign: 'center' as const,
  },

  statusPending: {
    backgroundColor: IEPA_PDF_COLORS.accentTeal,
    color: IEPA_PDF_COLORS.white,
    padding: 5,
    borderRadius: 3,
    fontSize: 10,
    fontFamily: PDF_FONTS.secondary,
    textAlign: 'center' as const,
  },

  statusOverdue: {
    backgroundColor: '#dc3545',
    color: IEPA_PDF_COLORS.white,
    padding: 5,
    borderRadius: 3,
    fontSize: 10,
    fontFamily: PDF_FONTS.secondary,
    textAlign: 'center' as const,
  },

  // Utility styles
  textCenter: {
    textAlign: 'center' as const,
  },

  textRight: {
    textAlign: 'right' as const,
  },

  textBold: {
    fontFamily: PDF_FONTS.secondary,
  },

  marginBottom: {
    marginBottom: 10,
  },

  marginTop: {
    marginTop: 10,
  },

  // Responsive text sizes
  small: {
    fontSize: 9,
  },

  medium: {
    fontSize: 11,
  },

  large: {
    fontSize: 13,
  },

  xlarge: {
    fontSize: 16,
  },
});
