'use client';

import React, { useRef, useState } from 'react';
import { Button } from './button';
import { Label } from './label';
import { FiFile, FiUpload, FiCheck, FiAlertCircle, FiPlus } from 'react-icons/fi';
import { useFileUpload, FileUploadOptions } from '@/hooks/useFileUpload';

export interface SimpleFileUploadProps extends FileUploadOptions {
  label?: string;
  description?: string;
  onFileUpload?: (url: string | null, file: File | null) => void;
  accept?: string;
  placeholder?: string;
  disabled?: boolean;
}

export function SimpleFileUpload({
  label,
  description,
  onFileUpload,
  accept,
  placeholder = 'Click to upload file',
  disabled = false,
  ...uploadOptions
}: SimpleFileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { uploading, progress, error, url, uploadFile, resetState } = useFileUpload(uploadOptions);

  const handleFileSelect = async (file: File) => {
    setSelectedFile(file);
    resetState();

    const uploadedUrl = await uploadFile(file);
    onFileUpload?.(uploadedUrl, file);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleButtonClick = () => {
    if (disabled || !fileInputRef.current) {
      return;
    }
    fileInputRef.current.click();
  };

  const getStatusIcon = () => {
    if (uploading) return <FiUpload className="animate-spin w-4 h-4" />;
    if (error) return <FiAlertCircle className="text-red-500 w-4 h-4" />;
    if (url) return <FiCheck className="text-green-500 w-4 h-4" />;
    return <FiFile className="w-4 h-4" />;
  };

  const getStatusText = () => {
    if (uploading) return `Uploading... ${progress}%`;
    if (error) return error;
    if (url && selectedFile) return `Uploaded: ${selectedFile.name}`;
    if (selectedFile) return selectedFile.name;
    return placeholder;
  };

  return (
    <div className="space-y-2">
      {label && (
        <Label className="text-sm font-medium text-gray-700">
          {label}
        </Label>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        accept={accept}
        onChange={handleInputChange}
        disabled={disabled}
      />

      {/* Upload button and status */}
      <div className="border border-gray-300 rounded-lg p-4 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className="text-sm text-gray-700">
              {getStatusText()}
            </span>
          </div>
          
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleButtonClick}
            disabled={disabled || uploading}
            className="h-8 px-3 text-xs"
          >
            <FiPlus className="w-3 h-3 mr-1" />
            {url ? 'Replace' : 'Add File'}
          </Button>
        </div>

        {uploading && (
          <div className="mt-3">
            <div className="bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}

        {error && (
          <div className="mt-2 text-sm text-red-600">
            {error}
          </div>
        )}
      </div>

      {description && (
        <p className="text-sm text-gray-600">{description}</p>
      )}
    </div>
  );
}
