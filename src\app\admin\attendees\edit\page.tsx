'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AttendeeRegistration } from '@/types/database';
import {
  Button,
  Input,
  Label,
  Card,
  CardHeader,
  CardTitle,
  CardBody,
} from '@/components/ui';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { supabase } from '@/lib/supabase';
import {
  FiSave,
  FiArrowLeft,
  FiUser,
  FiMapPin,
  FiBriefcase,
  FiTarget,
  FiCoffee,
  FiSend,
} from 'react-icons/fi';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { useAuth } from '@/contexts/AuthContext';
import { showSuccess, showError } from '@/utils/notifications';

// Use AttendeeRegistration directly since all needed fields are already defined
type ExtendedAttendeeRegistration = AttendeeRegistration;

export default function EditAttendeePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const attendeeId = searchParams?.get('id');
  const { user } = useAuth();

  // Check for test mode bypass
  const isTestMode =
    process.env.NODE_ENV === 'development' &&
    searchParams?.get('testAdmin') === 'true';

  const { isAdmin, isLoading: adminLoading } = useAdminAccess();

  // Special <NAME_EMAIL>
  const isEnoteware = user?.email === '<EMAIL>';

  const [attendee, setAttendee] = useState<ExtendedAttendeeRegistration | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [resendingEmail, setResendingEmail] = useState(false);

  const [formData, setFormData] = useState({
    full_name: '',
    first_name: '',
    last_name: '',
    name_on_badge: '',
    email: '',
    phone_number: '',
    street_address: '',
    city: '',
    state: '',
    zip_code: '',
    organization: '',
    job_title: '',
    gender: '',
    registration_type: '',
    attending_golf: false,
    golf_club_rental: false,
    golf_club_handedness: '',
    dietary_needs: '',
    payment_status: '',
  });

  const fetchAttendee = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('iepa_attendee_registrations')
        .select('*')
        .eq('id', attendeeId)
        .single();

      if (error) throw error;

      setAttendee(data);
      setFormData({
        full_name: data.full_name || '',
        first_name: data.first_name || '',
        last_name: data.last_name || '',
        name_on_badge: data.name_on_badge || '',
        email: data.email || '',
        phone_number: data.phone_number || '',
        street_address: data.street_address || '',
        city: data.city || '',
        state: data.state || '',
        zip_code: data.zip_code || '',
        organization: data.organization || '',
        job_title: data.job_title || '',
        gender: data.gender || '',
        registration_type: data.registration_type || '',
        attending_golf: data.attending_golf || false,
        golf_club_rental: data.golf_club_rental || false,
        golf_club_handedness: data.golf_club_handedness || '',
        dietary_needs: data.dietary_needs || '',
        payment_status: data.payment_status || '',
      });
    } catch (err) {
      console.error('Error fetching attendee:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch attendee');
    } finally {
      setLoading(false);
    }
  }, [attendeeId]);

  useEffect(() => {
    // Allow <NAME_EMAIL>, admin users, or test mode
    const hasAccess = isAdmin || isTestMode || isEnoteware;

    // Only redirect if we're sure the user is not an admin (loading is complete) and not enoteware
    if (!adminLoading && !hasAccess && !isTestMode && !isEnoteware) {
      console.log('🚫 Access denied - redirecting to admin dashboard');
      router.push('/admin');
      return;
    }

    // Only proceed if we have admin access, are in test mode, or are enoteware
    if ((hasAccess || isTestMode || isEnoteware) && attendeeId) {
      fetchAttendee();
    } else if (!attendeeId && !adminLoading) {
      router.push('/admin/attendees');
    }
  }, [
    attendeeId,
    isAdmin,
    adminLoading,
    isTestMode,
    isEnoteware,
    router,
    fetchAttendee,
  ]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setSuccess(false);
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const { error: updateError } = await supabase
        .from('iepa_attendee_registrations')
        .update({
          ...formData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', attendeeId);

      if (updateError) throw updateError;

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error updating attendee:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to update attendee'
      );
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/admin/attendees${isTestMode ? '?testAdmin=true' : ''}`);
  };

  const handleResendWelcomeEmail = async () => {
    if (!attendee) return;

    setResendingEmail(true);
    try {
      const response = await fetch('/api/admin/resend-welcome-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          attendeeId: attendee.id,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        showSuccess('Email Sent', 'Welcome email sent successfully!');
      } else {
        showError('Email Failed', result.error || 'Failed to send welcome email');
      }
    } catch (error) {
      console.error('Error resending welcome email:', error);
      showError('Email Failed', 'Failed to send welcome email');
    } finally {
      setResendingEmail(false);
    }
  };

  if ((adminLoading && !isTestMode) || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--iepa-primary-blue)] mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading attendee...</p>
        </div>
      </div>
    );
  }

  if (!attendee) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Attendee not found</p>
        <Button onClick={handleBack} className="mt-4">
          <FiArrowLeft className="w-4 h-4 mr-2" />
          Back to Attendees
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back to Attendees
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Attendee</h1>
            <p className="text-gray-600">{attendee.full_name}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleResendWelcomeEmail}
            disabled={resendingEmail || formData.payment_status === 'draft'}
            title={
              formData.payment_status === 'draft'
                ? 'Cannot send welcome email for draft registrations'
                : 'Resend welcome email to attendee'
            }
          >
            <FiSend className="w-4 h-4 mr-2" />
            {resendingEmail ? 'Sending...' : 'Resend Welcome Email'}
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <FiSave className="w-4 h-4 mr-2" />
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <p className="text-green-600 text-sm">
            Attendee updated successfully!
          </p>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Form Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiUser className="w-5 h-5 mr-2" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <Label htmlFor="full_name">Full Name</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={e => handleInputChange('full_name', e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="first_name">First Name</Label>
                <Input
                  id="first_name"
                  value={formData.first_name}
                  onChange={e =>
                    handleInputChange('first_name', e.target.value)
                  }
                />
              </div>
              <div>
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  value={formData.last_name}
                  onChange={e => handleInputChange('last_name', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="name_on_badge">Name on Badge</Label>
              <Input
                id="name_on_badge"
                value={formData.name_on_badge}
                onChange={e =>
                  handleInputChange('name_on_badge', e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="gender">Gender</Label>
              <Select
                value={formData.gender}
                onValueChange={value => handleInputChange('gender', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={e => handleInputChange('email', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="phone_number">Phone Number</Label>
              <Input
                id="phone_number"
                value={formData.phone_number}
                onChange={e =>
                  handleInputChange('phone_number', e.target.value)
                }
              />
            </div>
          </CardBody>
        </Card>

        {/* Address Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiMapPin className="w-5 h-5 mr-2" />
              Address Information
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <Label htmlFor="street_address">Street Address</Label>
              <Input
                id="street_address"
                value={formData.street_address}
                onChange={e =>
                  handleInputChange('street_address', e.target.value)
                }
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={e => handleInputChange('city', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={e => handleInputChange('state', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="zip_code">ZIP Code</Label>
              <Input
                id="zip_code"
                value={formData.zip_code}
                onChange={e => handleInputChange('zip_code', e.target.value)}
              />
            </div>
          </CardBody>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiBriefcase className="w-5 h-5 mr-2" />
              Professional Information
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <Label htmlFor="organization">Organization</Label>
              <Input
                id="organization"
                value={formData.organization}
                onChange={e =>
                  handleInputChange('organization', e.target.value)
                }
              />
            </div>

            <div>
              <Label htmlFor="job_title">Job Title</Label>
              <Input
                id="job_title"
                value={formData.job_title}
                onChange={e => handleInputChange('job_title', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="registration_type">Registration Type</Label>
              <Select
                value={formData.registration_type}
                onValueChange={value =>
                  handleInputChange('registration_type', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="iepa-member">IEPA Member</SelectItem>
                  <SelectItem value="non-iepa-member">
                    Non-IEPA Member
                  </SelectItem>
                  <SelectItem value="day-use-iepa">Day Use - IEPA</SelectItem>
                  <SelectItem value="day-use-non-iepa">
                    Day Use - Non-IEPA
                  </SelectItem>
                  <SelectItem value="fed-state-government">
                    Federal/State Government
                  </SelectItem>
                  <SelectItem value="cca">
                    California Community Choice Association
                  </SelectItem>
                  <SelectItem value="comped-speaker">Comped Speaker</SelectItem>
                  <SelectItem value="full-meeting-speaker">
                    Full Meeting Speaker
                  </SelectItem>
                  <SelectItem value="iepa-staff">IEPA Staff</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="payment_status">Payment Status</Label>
              <Select
                value={formData.payment_status}
                onValueChange={value =>
                  handleInputChange('payment_status', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardBody>
        </Card>

        {/* Golf Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiTarget className="w-5 h-5 mr-2" />
              Golf Tournament
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <Label htmlFor="attending_golf">Golf Participation</Label>
              <Select
                value={formData.attending_golf ? 'yes' : 'no'}
                onValueChange={value =>
                  handleInputChange('attending_golf', value === 'yes')
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes</SelectItem>
                  <SelectItem value="no">No</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formData.attending_golf && (
              <>
                <div>
                  <Label htmlFor="golf_club_rental">Club Rental</Label>
                  <Select
                    value={formData.golf_club_rental ? 'yes' : 'no'}
                    onValueChange={value =>
                      handleInputChange('golf_club_rental', value === 'yes')
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="yes">Yes</SelectItem>
                      <SelectItem value="no">No</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {formData.golf_club_rental && (
                  <div>
                    <Label htmlFor="golf_club_handedness">
                      Club Handedness
                    </Label>
                    <Select
                      value={formData.golf_club_handedness}
                      onValueChange={value =>
                        handleInputChange('golf_club_handedness', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="right">Right Handed</SelectItem>
                        <SelectItem value="left">Left Handed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </>
            )}
          </CardBody>
        </Card>

        {/* Dietary Information */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiCoffee className="w-5 h-5 mr-2" />
              Dietary Information
            </CardTitle>
          </CardHeader>
          <CardBody>
            <div>
              <Label htmlFor="dietary_needs">Dietary Needs</Label>
              <Textarea
                id="dietary_needs"
                value={formData.dietary_needs}
                onChange={e =>
                  handleInputChange('dietary_needs', e.target.value)
                }
                placeholder="Any dietary restrictions or special needs..."
                rows={4}
              />
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
