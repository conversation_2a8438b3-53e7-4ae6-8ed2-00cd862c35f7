// UI Component Library - IEPA 2025 Annual Meeting Registration
// Migrated from Hero UI to shadcn/ui for better customization and consistency

// Core shadcn/ui components with IEPA branding
export { Button } from './button';
// TODO: Create SubmitButton component
// export {
//   SubmitButton,
//   IEPASubmitButton,
//   AdminSubmitButton,
//   CompactSubmitButton,
// } from './SubmitButton';
export { Input } from './input';
export { PhoneInput } from './phone-input';
export {
  HydrationSafeInput,
  HydrationSafePhoneInput,
} from './hydration-safe-input';
export { NoSSR } from './no-ssr';
export { Label } from './label';
export {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  CardContent,
  CardTitle,
  CardDescription,
} from './card';
export { Badge } from './badge';
export { Progress } from './progress';
export { Alert, AlertTitle, AlertDescription } from './alert';
export { Skeleton } from './skeleton';
export { CancellationPolicy } from './CancellationPolicy';
export { Avatar, AvatarImage, AvatarFallback } from './avatar';
export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
} from './dropdown-menu';
export {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './tooltip';
export {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from './dialog';

export {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from './breadcrumb';
export { Separator } from './separator';
export {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuIndicator,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  NavigationMenuViewport,
  navigationMenuTriggerStyle,
} from './navigation-menu';
export {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from './sheet';

// Enhanced shadcn/ui components
export {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from './select';
export { Textarea } from './textarea';
export { RegistrationCardRadio } from './registration-card-radio';
export { RegistrationTypeSelector } from './registration-type-selector';
export { RegistrationRatesTable } from './registration-rates-table';
export { SponsorCard, SponsorCardRadio } from './sponsor-card-radio';
export {
  SponsorShowcase,
  SponsorBenefitsComparison,
  SponsorHighlight,
} from './sponsor-showcase';
export { FileUpload } from './FileUpload';
export { RadioGroup, RadioGroupItem } from './radio-group';
export { default as ScrollspyNavigation } from './scrollspy-navigation';
export {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableFooter,
  TableCaption,
} from './table';

// Legacy HeroUI components (to be migrated)
export { Checkbox } from './Checkbox';
export {
  Radio,
  RadioGroup as LegacyRadioGroup,
  RadioGroupWithOptions,
} from './Radio';

// Temporary HeroUI components (to be replaced with shadcn/ui equivalents)
// TODO: Replace these with custom implementations using shadcn/ui primitives
export {
  Spinner, // TODO: Replace with custom spinner using shadcn/ui
  useDisclosure, // TODO: Replace with custom hook
  Link, // TODO: Replace with Next.js Link wrapper
  Image, // TODO: Replace with Next.js Image wrapper
  Chip, // TODO: Replace with enhanced Badge component
  Popover,
  PopoverTrigger,
  PopoverContent,
  Navbar, // TODO: Replace with custom navbar using shadcn/ui primitives
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  NavbarMenuToggle,
  NavbarMenu,
  NavbarMenuItem,
} from '@heroui/react';

// Legacy aliases for backward compatibility
// These will be removed once migration is complete
export {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from '@heroui/react';

// Custom utility components
export { Separator as Divider } from './separator'; // Use shadcn/ui separator as divider
export { Spacer } from './spacer';
