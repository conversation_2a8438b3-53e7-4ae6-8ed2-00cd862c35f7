import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

export async function GET(request: NextRequest) {
  try {
    console.log('[EMAIL-LOGS-API] Fetching email logs...');

    const { searchParams } = new URL(request.url);
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
    const limit = Math.min(100, Math.max(10, parseInt(searchParams.get('limit') || '50'))); // Limit between 10-100
    const status = searchParams.get('status');
    const emailType = searchParams.get('emailType');
    const search = searchParams.get('search');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    const offset = (page - 1) * limit;

    // Optimize query by selecting only needed fields for list view
    let query = supabase
      .from('iepa_email_log')
      .select(`
        id,
        recipient_email,
        recipient_name,
        sender_email,
        sender_name,
        subject,
        email_type,
        status,
        sent_at,
        created_at,
        updated_at,
        error_message,
        content_preview,
        has_attachments,
        sendgrid_message_id
      `, { count: 'exact' });

    // Apply filters with proper indexing
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (emailType && emailType !== 'all') {
      query = query.eq('email_type', emailType);
    }

    // Optimize search with better text search
    if (search && search.trim()) {
      const searchTerm = search.trim();
      query = query.or(`
        recipient_email.ilike.%${searchTerm}%,
        subject.ilike.%${searchTerm}%,
        recipient_name.ilike.%${searchTerm}%,
        sender_email.ilike.%${searchTerm}%,
        content_preview.ilike.%${searchTerm}%
      `);
    }

    // Add date range filtering
    if (dateFrom) {
      query = query.gte('created_at', dateFrom);
    }
    if (dateTo) {
      // Add one day to include the entire end date
      const endDate = new Date(dateTo);
      endDate.setDate(endDate.getDate() + 1);
      query = query.lt('created_at', endDate.toISOString());
    }

    // Apply pagination and ordering with optimized index usage
    const { data: emailLogs, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('[EMAIL-LOGS-API] Database error:', error);
      return NextResponse.json({
        success: false,
        error: error.message,
        logs: [],
        pagination: {
          page,
          limit,
          total: 0,
          totalPages: 0
        }
      }, { status: 500 });
    }

    // Get statistics with optimized aggregation query
    const statsPromises = [
      supabase.from('iepa_email_log').select('id', { count: 'exact', head: true }).eq('status', 'sent'),
      supabase.from('iepa_email_log').select('id', { count: 'exact', head: true }).eq('status', 'failed'),
      supabase.from('iepa_email_log').select('id', { count: 'exact', head: true }).eq('status', 'pending'),
      supabase.from('iepa_email_log').select('id', { count: 'exact', head: true })
    ];

    const [sentResult, failedResult, pendingResult, totalResult] = await Promise.all(statsPromises);

    const stats = {
      totalSent: sentResult.count || 0,
      totalFailed: failedResult.count || 0,
      totalPending: pendingResult.count || 0,
      totalEmails: totalResult.count || 0
    };

    const totalPages = Math.ceil((count || 0) / limit);

    console.log(`[EMAIL-LOGS-API] Retrieved ${emailLogs?.length || 0} email logs`);

    // Add response compression headers
    const response = NextResponse.json({
      success: true,
      logs: emailLogs || [],
      statistics: stats,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      },
      filters: {
        status: status || 'all',
        emailType: emailType || 'all',
        search: search || '',
        dateFrom,
        dateTo
      },
      timestamp: new Date().toISOString()
    });

    // Add caching headers for better performance
    response.headers.set('Cache-Control', 'private, max-age=60'); // Cache for 1 minute
    response.headers.set('ETag', `"${Date.now()}"`);

    return response;

  } catch (error) {
    console.error('[EMAIL-LOGS-API] Failed to fetch email logs:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch email logs',
      logs: [],
      statistics: {
        totalSent: 0,
        totalFailed: 0,
        totalPending: 0,
        totalEmails: 0
      },
      pagination: {
        page: 1,
        limit: 50,
        total: 0,
        totalPages: 0
      },
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    console.log('[EMAIL-LOGS-API] Deleting email log...');

    const { searchParams } = new URL(request.url);
    const logId = searchParams.get('id');

    if (!logId) {
      return NextResponse.json({
        success: false,
        error: 'Log ID is required'
      }, { status: 400 });
    }

    const { error } = await supabase
      .from('iepa_email_log')
      .delete()
      .eq('id', logId);

    if (error) {
      console.error('[EMAIL-LOGS-API] Delete error:', error);
      return NextResponse.json({
        success: false,
        error: error.message
      }, { status: 500 });
    }

    console.log(`[EMAIL-LOGS-API] Deleted email log: ${logId}`);

    return NextResponse.json({
      success: true,
      message: 'Email log deleted successfully'
    });

  } catch (error) {
    console.error('[EMAIL-LOGS-API] Failed to delete email log:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete email log'
    }, { status: 500 });
  }
}
