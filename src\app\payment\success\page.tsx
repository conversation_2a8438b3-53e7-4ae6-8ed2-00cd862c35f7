'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@/components/ui';
import Link from 'next/link';
import { FaCheckCircle, FaDownload, FaHome, FaReceipt } from 'react-icons/fa';
import { CONFERENCE_YEAR } from '@/lib/conference-config';

interface PaymentSession {
  id: string;
  payment_status: string;
  customer_email: string;
  amount_total: number;
  currency: string;
  metadata: {
    registrationId: string;
    registrationType: string;
    customerName?: string;
  };
  created: number;
}

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const sessionId = searchParams?.get('session_id');
  const paymentType = searchParams?.get('type'); // 'golf-addon' for golf add-ons

  const [session, setSession] = useState<PaymentSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isGolfAddOn = paymentType === 'golf-addon' || session?.metadata.registrationType === 'golf-addon';

  useEffect(() => {
    const fetchSessionDetails = async () => {
      try {
        const response = await fetch(
          `/api/stripe/create-checkout-session?session_id=${sessionId}`
        );
        const data = await response.json();

        if (data.success) {
          setSession(data.session);
        } else {
          setError(data.error || 'Failed to retrieve payment details');
        }
      } catch {
        setError('Failed to fetch payment details');
      } finally {
        setLoading(false);
      }
    };

    if (sessionId) {
      fetchSessionDetails();
    } else {
      setError('No session ID provided');
      setLoading(false);
    }
  }, [sessionId]);

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-2xl mx-auto text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="iepa-body">Processing your payment confirmation...</p>
          </div>
        </section>
      </div>
    );
  }

  if (error) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-2xl mx-auto">
            <Card className="border-red-200">
              <CardHeader className="text-center">
                <h1 className="iepa-heading-1 text-red-600">Payment Error</h1>
              </CardHeader>
              <CardBody className="text-center">
                <p className="iepa-body mb-6">{error}</p>
                <Button as={Link} href="/register" color="primary">
                  Return to Registration
                </Button>
              </CardBody>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-2xl mx-auto">
          {/* Success Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <FaCheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h1 className="iepa-heading-1 text-green-600 mb-2">
              {isGolfAddOn ? 'Golf Add-On Added!' : 'Payment Successful!'}
            </h1>
            <p className="iepa-body text-gray-600">
              {isGolfAddOn
                ? `Golf tournament has been successfully added to your IEPA ${CONFERENCE_YEAR} Conference registration`
                : `Thank you for registering for the IEPA ${CONFERENCE_YEAR} Annual Conference`
              }
            </p>
          </div>

          {/* Payment Details */}
          {session && (
            <Card className="mb-6">
              <CardHeader>
                <h2 className="iepa-heading-2">Payment Confirmation</h2>
              </CardHeader>
              <CardBody>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="iepa-body-small font-semibold text-gray-600">
                        {isGolfAddOn ? 'Add-On Type' : 'Registration Type'}
                      </p>
                      <p className="iepa-body capitalize">
                        {isGolfAddOn
                          ? 'Golf Tournament Add-On'
                          : `${session.metadata.registrationType} Registration`
                        }
                      </p>
                    </div>
                    <div>
                      <p className="iepa-body-small font-semibold text-gray-600">
                        Amount Paid
                      </p>
                      <p className="iepa-body font-semibold text-green-600">
                        {formatAmount(session.amount_total, session.currency)}
                      </p>
                    </div>
                    <div>
                      <p className="iepa-body-small font-semibold text-gray-600">
                        Email
                      </p>
                      <p className="iepa-body">{session.customer_email}</p>
                    </div>
                    <div>
                      <p className="iepa-body-small font-semibold text-gray-600">
                        Payment Date
                      </p>
                      <p className="iepa-body">{formatDate(session.created)}</p>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200">
                    <p className="iepa-body-small font-semibold text-gray-600">
                      Session ID
                    </p>
                    <p className="iepa-body-small font-mono text-gray-500">
                      {session.id}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Next Steps */}
          <Card className="mb-6">
            <CardHeader>
              <h3 className="iepa-heading-3">What&apos;s Next?</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <FaReceipt className="w-5 h-5 text-blue-600 mt-1" />
                  <div>
                    <p className="iepa-body font-semibold">
                      Confirmation Email
                    </p>
                    <p className="iepa-body-small text-gray-600">
                      You&apos;ll receive a confirmation email with your
                      registration details and receipt.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <FaDownload className="w-5 h-5 text-blue-600 mt-1" />
                  <div>
                    <p className="iepa-body font-semibold">
                      Registration Documents
                    </p>
                    <p className="iepa-body-small text-gray-600">
                      Download your registration confirmation and invoice from
                      your account dashboard.
                    </p>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              as={Link}
              href="/my-registrations"
              color="primary"
              size="lg"
              className="flex items-center gap-2"
            >
              <FaReceipt className="w-4 h-4" />
              View My Registrations
            </Button>

            <Button
              as={Link}
              href="/"
              variant="bordered"
              size="lg"
              className="flex items-center gap-2"
            >
              <FaHome className="w-4 h-4" />
              Return to Homepage
            </Button>
          </div>

          {/* Additional Information */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="iepa-heading-4 text-blue-800 mb-2">
              Important Information
            </h4>
            <ul className="iepa-body-small text-blue-700 space-y-1">
              <li>• Keep this confirmation for your records</li>
              <li>
                • Conference details will be sent closer to the event date
              </li>
              <li>• For questions, contact <NAME_EMAIL></li>
              <li>
                • Cancellation policy applies as outlined in terms and
                conditions
              </li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentSuccessContent />
    </Suspense>
  );
}
