'use client';

import React from 'react';
import { Card, CardBody, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  FiUsers,
  FiMic,
  FiStar,
  FiDollarSign,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiTrendingUp,
} from 'react-icons/fi';
import type { DashboardStats } from '@/types/dashboard';

interface RegistrationStatsProps {
  stats: DashboardStats;
  isLoading?: boolean;
}

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  isLoading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  trend,
  isLoading = false,
}) => {
  const colorClasses = {
    primary: 'bg-blue-50 text-blue-600 border-blue-200',
    secondary: 'bg-gray-50 text-gray-600 border-gray-200',
    success: 'bg-green-50 text-green-600 border-green-200',
    warning: 'bg-yellow-50 text-yellow-600 border-yellow-200',
    danger: 'bg-red-50 text-red-600 border-red-200',
  };

  const iconBgClasses = {
    primary: 'bg-blue-100 text-blue-600',
    secondary: 'bg-gray-100 text-gray-600',
    success: 'bg-green-100 text-green-600',
    warning: 'bg-yellow-100 text-yellow-600',
    danger: 'bg-red-100 text-red-600',
  };

  if (isLoading) {
    return (
      <Card className="iepa-card-hover">
        <CardBody className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div className="h-8 bg-gray-200 rounded animate-pulse mb-1"></div>
              <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3"></div>
            </div>
            <div className="w-12 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className={`iepa-card-hover border-l-4 ${colorClasses[color]}`}>
      <CardBody className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-3xl font-bold text-gray-900 mb-1">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
            {trend && (
              <div className="flex items-center mt-2">
                <FiTrendingUp
                  className={`w-4 h-4 mr-1 ${
                    trend.isPositive
                      ? 'text-green-500'
                      : 'text-red-500 rotate-180'
                  }`}
                />
                <span
                  className={`text-sm font-medium ${
                    trend.isPositive ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {trend.isPositive ? '+' : ''}
                  {trend.value}%
                </span>
                <span className="text-sm text-gray-500 ml-1">
                  vs last month
                </span>
              </div>
            )}
          </div>
          <div
            className={`w-12 h-12 rounded-lg flex items-center justify-center ${iconBgClasses[color]}`}
          >
            {icon}
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

const RegistrationStats: React.FC<RegistrationStatsProps> = ({
  stats,
  isLoading = false,
}) => {
  // Prevent flicker by maintaining previous stats during loading
  const [displayStats, setDisplayStats] = React.useState(stats);

  React.useEffect(() => {
    if (!isLoading && stats) {
      setDisplayStats(stats);
    }
  }, [stats, isLoading]);
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const currentStats = displayStats || stats;

  const paymentCompletionRate =
    currentStats.totalAttendees > 0
      ? Math.round(
          (currentStats.completedPayments / currentStats.totalAttendees) * 100
        )
      : 0;

  const golfParticipationRate =
    currentStats.totalAttendees > 0
      ? Math.round(
          (currentStats.golfParticipants / currentStats.totalAttendees) * 100
        )
      : 0;

  return (
    <div className="space-y-6">
      {/* Main Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Attendees"
          value={currentStats.totalAttendees}
          subtitle="Registered participants"
          icon={<FiUsers className="w-6 h-6" />}
          color="primary"
          trend={{ value: 12, isPositive: true }}
          isLoading={isLoading}
        />

        <StatCard
          title="Speakers"
          value={currentStats.totalSpeakers}
          subtitle="Confirmed presenters"
          icon={<FiMic className="w-6 h-6" />}
          color="secondary"
          trend={{ value: 8, isPositive: true }}
          isLoading={isLoading}
        />

        <StatCard
          title="Sponsors"
          value={currentStats.totalSponsors}
          subtitle="Supporting organizations"
          icon={<FiStar className="w-6 h-6" />}
          color="warning"
          trend={{ value: 25, isPositive: true }}
          isLoading={isLoading}
        />

        <StatCard
          title="Total Revenue"
          value={formatCurrency(currentStats.totalRevenue)}
          subtitle="Registration fees collected"
          icon={<FiDollarSign className="w-6 h-6" />}
          color="success"
          trend={{ value: 15, isPositive: true }}
          isLoading={isLoading}
        />
      </div>

      {/* Payment Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard
          title="Completed Payments"
          value={currentStats.completedPayments}
          subtitle={`${paymentCompletionRate}% completion rate`}
          icon={<FiCheckCircle className="w-6 h-6" />}
          color="success"
          isLoading={isLoading}
        />

        <StatCard
          title="Pending Payments"
          value={currentStats.pendingPayments}
          subtitle="Awaiting payment"
          icon={<FiClock className="w-6 h-6" />}
          color="warning"
          isLoading={isLoading}
        />

        <StatCard
          title="Golf Participants"
          value={currentStats.golfParticipants}
          subtitle={`${golfParticipationRate}% participation rate`}
          icon={<FiStar className="w-6 h-6" />}
          color="primary"
          isLoading={isLoading}
        />
      </div>

      {/* Meal Selections Summary */}
      {!isLoading && Object.keys(currentStats.mealSelections).length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              Meal Selections
            </h3>
            <p className="text-sm text-gray-600">
              Popular meal choices among attendees
            </p>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(currentStats.mealSelections)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 6)
                .map(([meal, count]) => (
                  <div
                    key={meal}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <span className="text-sm font-medium text-gray-700 capitalize">
                      {meal.replace(/-/g, ' ')}
                    </span>
                    <Badge variant="secondary" className="ml-2">
                      {count}
                    </Badge>
                  </div>
                ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          <p className="text-sm text-gray-600">Common administrative tasks</p>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              type="button"
              className="flex items-center p-3 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
            >
              <FiUsers className="w-5 h-5 text-blue-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-blue-900">
                  Export Attendees
                </p>
                <p className="text-xs text-blue-600">Download CSV</p>
              </div>
            </button>

            <button
              type="button"
              className="flex items-center p-3 text-left bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
            >
              <FiCheckCircle className="w-5 h-5 text-green-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-green-900">
                  Send Reminders
                </p>
                <p className="text-xs text-green-600">Payment pending</p>
              </div>
            </button>

            <button
              type="button"
              className="flex items-center p-3 text-left bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors"
            >
              <FiAlertCircle className="w-5 h-5 text-yellow-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-yellow-900">
                  Review Speakers
                </p>
                <p className="text-xs text-yellow-600">Pending approval</p>
              </div>
            </button>

            <button
              type="button"
              className="flex items-center p-3 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors"
            >
              <FiStar className="w-5 h-5 text-purple-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-purple-900">
                  Manage Sponsors
                </p>
                <p className="text-xs text-purple-600">Update assets</p>
              </div>
            </button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default RegistrationStats;
