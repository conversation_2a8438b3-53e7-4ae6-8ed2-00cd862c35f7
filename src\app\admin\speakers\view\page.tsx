'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { SpeakerRegistration } from '@/types/database';
import { <PERSON><PERSON>, Card, CardHeader, CardTitle, CardBody } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import { formatCurrency, formatDate } from '@/lib/pdf-generation/utils';
import {
  FiArrowLeft,
  FiEdit,
  FiEye,
  FiMail,
  FiPhone,
  FiBriefcase,
  FiCreditCard,
  FiCalendar,
  FiMic,
  FiFile,
  FiUser,
  FiTarget,
  FiCoffee,
  FiDownload,
  FiUsers,
} from 'react-icons/fi';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { useAuth } from '@/contexts/AuthContext';
import { useSignedUrls } from '@/hooks/useSignedUrl';
import { getMealDisplayNames } from '@/lib/meal-utils';
import Link from 'next/link';

// Extended interface to match the actual database structure
interface ExtendedSpeakerRegistration extends SpeakerRegistration {
  // Add any additional fields that might be in the database
  dietary_needs?: string;
  accessibility_needs?: string;
  attending_golf?: boolean;
  golf_club_rental?: boolean;
  meal_selections?: Record<string, boolean>;
  registration_fee?: number;
  payment_status?: string;
  payment_id?: string;
  receipt_url?: string;
  invoice_url?: string;
  receipt_generated_at?: string;
  invoice_generated_at?: string;
}

export default function ViewSpeakerPage() {
  console.log('🔍 ViewSpeakerPage component mounting...');

  const router = useRouter();
  const searchParams = useSearchParams();
  const speakerId = searchParams?.get('id');
  const { user } = useAuth();

  console.log('🔍 ViewSpeakerPage params:', {
    speakerId,
    userEmail: user?.email,
  });

  // Check for test mode bypass
  const isTestMode =
    process.env.NODE_ENV === 'development' &&
    searchParams?.get('testAdmin') === 'true';

  const { isAdmin, isLoading: adminLoading } = useAdminAccess();

  // Special <NAME_EMAIL>
  const isEnoteware = user?.email === '<EMAIL>';

  console.log('🔍 ViewSpeakerPage admin access:', {
    isAdmin,
    adminLoading,
    isTestMode,
    isEnoteware,
  });

  const [speaker, setSpeaker] = useState<ExtendedSpeakerRegistration | null>(
    null
  );
  const [attendeeData, setAttendeeData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mealDisplayNames, setMealDisplayNames] = useState<Record<string, string>>({});

  // Memoize URLs array to prevent infinite loops
  const urlsToFetch = useMemo(() => {
    if (!speaker) return [];
    return [
      {
        url: speaker.presentation_file_url,
        bucket: 'iepa-presentations',
        key: 'presentation',
      },
      {
        url: speaker.headshot_url,
        bucket: 'iepa-presentations',
        key: 'headshot',
      },
    ];
  }, [speaker?.presentation_file_url, speaker?.headshot_url]);

  // Get signed URLs for files
  const signedUrls = useSignedUrls(urlsToFetch, { enabled: !!speaker });

  // Load meal display names from database
  useEffect(() => {
    const loadMealDisplayNames = async () => {
      try {
        const allMealKeys = [
          'day1-reception', 'day1-breakfast', 'day1-lunch',
          'day2-breakfast', 'day2-lunch', 'day2-dinner',
          'day3-breakfast', 'day3-lunch',
          'sept15Dinner', 'sept16Breakfast', 'sept16Lunch',
          'sept16Dinner', 'sept17Breakfast', 'sept17Lunch'
        ];
        const displayNames = await getMealDisplayNames(allMealKeys);
        setMealDisplayNames(displayNames);
      } catch (error) {
        console.error('Error loading meal display names:', error);
      }
    };

    loadMealDisplayNames();
  }, []);

  useEffect(() => {
    console.log('🔍 View page access check:', {
      adminLoading,
      isAdmin,
      isTestMode,
      speakerId,
      userEmail: 'checking...',
    });

    // Allow <NAME_EMAIL>, admin users, or test mode
    const hasAccess = isAdmin || isTestMode || isEnoteware;

    // Only redirect if we're sure the user is not an admin (loading is complete) and not enoteware
    if (!adminLoading && !hasAccess && !isTestMode && !isEnoteware) {
      console.log('🚫 Access denied - redirecting to admin dashboard');
      router.push('/admin');
      return;
    }

    // Only proceed if we have admin access, are in test mode, or are enoteware
    if ((hasAccess || isTestMode || isEnoteware) && speakerId) {
      fetchSpeaker();
    } else if (!speakerId && !adminLoading) {
      router.push('/admin/speakers');
    }
  }, [speakerId, isAdmin, adminLoading, isTestMode, isEnoteware, router]);

  const fetchSpeaker = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('iepa_speaker_registrations')
        .select('*')
        .eq('id', speakerId)
        .single();

      if (error) throw error;

      setSpeaker(data);

      // Fetch related attendee data by email
      if (data?.email) {
        try {
          const { data: attendeeData, error: attendeeError } = await supabase
            .from('iepa_attendee_registrations')
            .select('*')
            .eq('email', data.email)
            .single();

          if (!attendeeError && attendeeData) {
            setAttendeeData(attendeeData);
          }
        } catch (error) {
          // No matching attendee found, which is fine
          console.log('No matching attendee found for speaker:', data.email, error);
        }
      }
    } catch (err) {
      console.error('Error fetching speaker:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch speaker');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push(`/admin/speakers${isTestMode ? '?testAdmin=true' : ''}`);
  };

  // Show loading state
  if (adminLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back to Speakers
          </Button>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  // Show not found state
  if (!speaker) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back to Speakers
          </Button>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <p className="text-yellow-600">Speaker not found</p>
        </div>
      </div>
    );
  }

  const getPaymentStatusColor = (status: string): "default" | "success" | "info" | "destructive" | "outline" | "secondary" => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'outline';
      case 'failed':
        return 'destructive';
      default:
        return 'default';
    }
  };

  // Function to get meal display name
  const getMealDisplayName = (mealKey: string): string => {
    return mealDisplayNames[mealKey] || mealKey;
  };

  const formatMealSelections = (meals: Record<string, boolean> | undefined) => {
    if (!meals) return 'No meal selections';
    const selectedMeals = Object.entries(meals)
      .filter(([, selected]) => selected)
      .map(([meal]) => getMealDisplayName(meal));
    return selectedMeals.length > 0 ? selectedMeals.join(', ') : 'No meals selected';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back to Speakers
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Speaker Details
            </h1>
            <p className="text-gray-600">{speaker.full_name}</p>
          </div>
        </div>
        <div className="flex gap-2">
          {speaker.presentation_file_url && signedUrls.presentation?.signedUrl && (
            <Button
              as="a"
              href={signedUrls.presentation.signedUrl}
              target="_blank"
              rel="noopener noreferrer"
              color="primary"
              className="flex items-center gap-2"
              disabled={signedUrls.presentation.loading}
            >
              <FiDownload className="w-4 h-4" />
              {signedUrls.presentation.loading ? 'Loading...' : 'Download Presentation'}
            </Button>
          )}
          {attendeeData && (
            <Link href={`/admin/attendees/view?id=${attendeeData.id}${isTestMode ? '&testAdmin=true' : ''}`}>
              <Button variant="bordered">
                <FiUsers className="w-4 h-4 mr-2" />
                View Attendee Profile
              </Button>
            </Link>
          )}
          <Link href={`/admin/speakers/edit?id=${speaker.id}${isTestMode ? '&testAdmin=true' : ''}`}>
            <Button variant="bordered">
              <FiEdit className="w-4 h-4 mr-2" />
              Edit Speaker
            </Button>
          </Link>
        </div>
      </div>

      {/* Speaker Details Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FiUser className="w-5 h-5" />
              <span>Personal Information</span>
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Full Name
              </label>
              <p className="text-gray-900">{speaker.full_name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                First Name
              </label>
              <p className="text-gray-900">{speaker.first_name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Last Name
              </label>
              <p className="text-gray-900">{speaker.last_name}</p>
            </div>
            <div className="flex items-center space-x-2">
              <FiMail className="w-4 h-4 text-gray-400" />
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Email
                </label>
                <p className="text-gray-900">{speaker.email}</p>
              </div>
            </div>
            {speaker.phone_number && (
              <div className="flex items-center space-x-2">
                <FiPhone className="w-4 h-4 text-gray-400" />
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Phone
                  </label>
                  <p className="text-gray-900">{speaker.phone_number}</p>
                </div>
              </div>
            )}
          </CardBody>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FiBriefcase className="w-5 h-5" />
              <span>Professional Information</span>
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Organization
              </label>
              <p className="text-gray-900">{speaker.organization_name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Job Title
              </label>
              <p className="text-gray-900">{speaker.job_title}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Bio
              </label>
              <p className="text-gray-900 whitespace-pre-wrap">{speaker.bio}</p>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Presentation Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FiMic className="w-5 h-5" />
            <span>Presentation Information</span>
          </CardTitle>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Presentation Title
              </label>
              <p className="text-gray-900">{speaker.presentation_title || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Duration
              </label>
              <p className="text-gray-900">{speaker.presentation_duration || 'Not specified'}</p>
            </div>
          </div>

          {speaker.presentation_description && (
            <div>
              <label className="text-sm font-medium text-gray-500">
                Presentation Description
              </label>
              <p className="text-gray-900 whitespace-pre-wrap">{speaker.presentation_description}</p>
            </div>
          )}

          {speaker.target_audience && (
            <div>
              <label className="text-sm font-medium text-gray-500">
                Target Audience
              </label>
              <p className="text-gray-900">{speaker.target_audience}</p>
            </div>
          )}

          {speaker.learning_objectives && (
            <div>
              <label className="text-sm font-medium text-gray-500">
                Learning Objectives
              </label>
              <p className="text-gray-900 whitespace-pre-wrap">{speaker.learning_objectives}</p>
            </div>
          )}

          {/* File Downloads */}
          {(speaker.presentation_file_url || speaker.headshot_url) && (
            <div className="mt-6 pt-6 border-t">
              <label className="text-sm font-medium text-gray-500 mb-3 block">
                Uploaded Files
              </label>
              <div className="space-y-3">
                {speaker.presentation_file_url && (
                  <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <FiFile className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Presentation File</p>
                        <p className="text-sm text-gray-600">Speaker&apos;s presentation materials</p>
                      </div>
                    </div>
                    <Button
                      as="a"
                      href={signedUrls.presentation?.signedUrl || '#'}
                      target="_blank"
                      rel="noopener noreferrer"
                      size="sm"
                      color="primary"
                      className="flex items-center gap-2"
                      disabled={signedUrls.presentation?.loading || !signedUrls.presentation?.signedUrl}
                    >
                      <FiDownload className="w-4 h-4" />
                      {signedUrls.presentation?.loading ? 'Loading...' : 'Download'}
                    </Button>
                  </div>
                )}
                {speaker.headshot_url && (
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="flex items-start gap-4">
                      {/* Thumbnail Preview */}
                      <div className="flex-shrink-0">
                        {signedUrls.headshot?.loading ? (
                          <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[var(--iepa-primary-blue)]"></div>
                          </div>
                        ) : signedUrls.headshot?.signedUrl ? (
                          <div className="relative group">
                            <img
                              src={signedUrls.headshot.signedUrl}
                              alt={`${speaker.full_name} headshot`}
                              className="w-24 h-24 object-cover rounded-lg border-2 border-gray-200 shadow-sm hover:shadow-md transition-all cursor-pointer"
                              onClick={() => signedUrls.headshot?.signedUrl && window.open(signedUrls.headshot.signedUrl, '_blank')}
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-lg transition-all duration-200 flex items-center justify-center">
                              <FiEye className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                            </div>
                          </div>
                        ) : (
                          <div className="w-24 h-24 bg-red-50 border-2 border-red-200 rounded-lg flex items-center justify-center">
                            <p className="text-xs text-red-600 text-center px-1">Error loading</p>
                          </div>
                        )}
                      </div>

                      {/* Info and Actions */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">Professional Headshot</p>
                            <p className="text-sm text-gray-600">Speaker&apos;s profile photo</p>
                          </div>
                          <Button
                            as="a"
                            href={signedUrls.headshot?.signedUrl || '#'}
                            target="_blank"
                            rel="noopener noreferrer"
                            size="sm"
                            variant="bordered"
                            className="flex items-center gap-2"
                            disabled={signedUrls.headshot?.loading || !signedUrls.headshot?.signedUrl}
                          >
                            <FiEye className="w-4 h-4" />
                            {signedUrls.headshot?.loading ? 'Loading...' : 'View Full Size'}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* No headshot state */}
                {!speaker.headshot_url && (
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-24 h-24 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                        <FiUser className="w-8 h-8 text-gray-400" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Professional Headshot</p>
                        <p className="text-sm text-gray-500">No headshot uploaded</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Speaking Experience */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FiTarget className="w-5 h-5" />
              <span>Speaking Experience</span>
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            {speaker.speaker_experience && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Experience Level
                </label>
                <p className="text-gray-900">{speaker.speaker_experience}</p>
              </div>
            )}
            {speaker.previous_speaking && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Previous Speaking Engagements
                </label>
                <p className="text-gray-900 whitespace-pre-wrap">{speaker.previous_speaking}</p>
              </div>
            )}
          </CardBody>
        </Card>

        {/* Technical Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FiCoffee className="w-5 h-5" />
              <span>Requirements & Preferences</span>
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            {speaker.equipment_needs && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Equipment Needs
                </label>
                <p className="text-gray-900 whitespace-pre-wrap">{speaker.equipment_needs}</p>
              </div>
            )}
            {speaker.special_requests && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Special Requests
                </label>
                <p className="text-gray-900 whitespace-pre-wrap">{speaker.special_requests}</p>
              </div>
            )}
            {speaker.dietary_needs && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Dietary Restrictions
                </label>
                <p className="text-gray-900">{speaker.dietary_needs}</p>
              </div>
            )}
          </CardBody>
        </Card>
      </div>

      {/* Registration & Payment Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FiCalendar className="w-5 h-5" />
              <span>Registration Information</span>
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Registration Date
              </label>
              <p className="text-gray-900">{formatDate(speaker.created_at)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Last Updated
              </label>
              <p className="text-gray-900">{formatDate(speaker.updated_at)}</p>
            </div>
            {speaker.attending_golf !== undefined && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Golf Tournament
                </label>
                <Badge variant={speaker.attending_golf ? 'success' : 'secondary'}>
                  {speaker.attending_golf ? 'Participating' : 'Not Participating'}
                </Badge>
              </div>
            )}
            {speaker.golf_club_rental !== undefined && speaker.attending_golf && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Golf Club Rental
                </label>
                <Badge variant={speaker.golf_club_rental ? 'success' : 'secondary'}>
                  {speaker.golf_club_rental ? 'Rental Requested' : 'Own Clubs'}
                </Badge>
              </div>
            )}
          </CardBody>
        </Card>

        {/* Payment Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FiCreditCard className="w-5 h-5" />
              <span>Payment Information</span>
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            {speaker.payment_status && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Payment Status
                </label>
                <div className="mt-1">
                  <Badge variant={getPaymentStatusColor(speaker.payment_status)}>
                    {speaker.payment_status.charAt(0).toUpperCase() + speaker.payment_status.slice(1)}
                  </Badge>
                </div>
              </div>
            )}
            {speaker.registration_fee && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Registration Fee
                </label>
                <p className="text-gray-900">{formatCurrency(speaker.registration_fee)}</p>
              </div>
            )}
            {speaker.payment_id && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Payment ID
                </label>
                <p className="text-gray-900 font-mono text-sm">{speaker.payment_id}</p>
              </div>
            )}

            {/* Documents */}
            {(speaker.receipt_url || speaker.invoice_url) && (
              <div className="mt-6 pt-6 border-t">
                <label className="text-sm font-medium text-gray-500 mb-3 block">
                  Documents
                </label>
                <div className="flex space-x-4">
                  {speaker.receipt_url && (
                    <a
                      href={speaker.receipt_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[var(--iepa-primary-blue)] hover:underline text-sm"
                    >
                      View Receipt
                    </a>
                  )}
                  {speaker.invoice_url && (
                    <a
                      href={speaker.invoice_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[var(--iepa-primary-blue)] hover:underline text-sm"
                    >
                      View Invoice
                    </a>
                  )}
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      </div>

      {/* Meal Selections */}
      {speaker.meal_selections && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FiCoffee className="w-5 h-5" />
              <span>Meal Selections</span>
            </CardTitle>
          </CardHeader>
          <CardBody>
            <p className="text-gray-900">{formatMealSelections(speaker.meal_selections)}</p>
          </CardBody>
        </Card>
      )}
    </div>
  );
}
