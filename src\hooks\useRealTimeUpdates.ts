'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

interface RealTimeUpdateOptions {
  enabled?: boolean;
  interval?: number;
  onUpdate?: () => void;
  onError?: (error: Error) => void;
  maxRetries?: number;
  backoffMultiplier?: number;
}

interface UsePollingReturn {
  isPolling: boolean;
  start: () => void;
  stop: () => void;
  toggle: () => void;
  retryCount: number;
}

export function usePolling({
  enabled = true,
  interval = 30000, // 30 seconds
  onUpdate,
  onError,
  maxRetries = 3,
  backoffMultiplier = 2,
}: RealTimeUpdateOptions): UsePollingReturn {
  const [isPolling, setIsPolling] = useState(enabled);
  const [retryCount, setRetryCount] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const clearTimers = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  const executeUpdate = useCallback(async () => {
    try {
      await onUpdate?.();
      setRetryCount(0); // Reset retry count on success
    } catch (error) {
      console.error('Polling update failed:', error);
      
      if (retryCount < maxRetries) {
        const retryDelay = interval * Math.pow(backoffMultiplier, retryCount);
        setRetryCount(prev => prev + 1);
        
        retryTimeoutRef.current = setTimeout(() => {
          executeUpdate();
        }, retryDelay);
      } else {
        onError?.(error instanceof Error ? error : new Error('Polling failed'));
        setIsPolling(false);
      }
    }
  }, [onUpdate, onError, retryCount, maxRetries, interval, backoffMultiplier]);

  const start = useCallback(() => {
    if (!isPolling) {
      setIsPolling(true);
      setRetryCount(0);
    }
  }, [isPolling]);

  const stop = useCallback(() => {
    setIsPolling(false);
    clearTimers();
    setRetryCount(0);
  }, [clearTimers]);

  const toggle = useCallback(() => {
    if (isPolling) {
      stop();
    } else {
      start();
    }
  }, [isPolling, start, stop]);

  useEffect(() => {
    if (isPolling && enabled) {
      // Execute immediately
      executeUpdate();
      
      // Set up interval
      intervalRef.current = setInterval(executeUpdate, interval);
    } else {
      clearTimers();
    }

    return clearTimers;
  }, [isPolling, enabled, interval, executeUpdate, clearTimers]);

  // Pause polling when page is not visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        clearTimers();
      } else if (document.visibilityState === 'visible' && isPolling && enabled) {
        // Resume polling when page becomes visible
        executeUpdate();
        intervalRef.current = setInterval(executeUpdate, interval);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isPolling, enabled, interval, executeUpdate, clearTimers]);

  return {
    isPolling,
    start,
    stop,
    toggle,
    retryCount,
  };
}

// Hook for email status updates with visual indicators
interface UseEmailStatusUpdatesOptions {
  enabled?: boolean;
  interval?: number;
  onStatusChange?: (changes: EmailStatusChange[]) => void;
}

interface EmailStatusChange {
  emailId: string;
  oldStatus: string;
  newStatus: string;
  timestamp: string;
}

interface UseEmailStatusUpdatesReturn {
  isPolling: boolean;
  hasUpdates: boolean;
  statusChanges: EmailStatusChange[];
  clearUpdates: () => void;
  togglePolling: () => void;
  lastUpdateTime: Date | null;
}

export function useEmailStatusUpdates({
  enabled = true,
  interval = 30000,
  onStatusChange,
}: UseEmailStatusUpdatesOptions): UseEmailStatusUpdatesReturn {
  const [statusChanges, setStatusChanges] = useState<EmailStatusChange[]>([]);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
  const [hasUpdates, setHasUpdates] = useState(false);

  const checkForUpdates = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/email-status-updates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          since: lastUpdateTime?.toISOString(),
        }),
      });

      if (response.ok) {
        const data = await response.json();
        
        if (data.success && data.changes?.length > 0) {
          setStatusChanges(prev => [...prev, ...data.changes]);
          setHasUpdates(true);
          setLastUpdateTime(new Date());
          onStatusChange?.(data.changes);
        }
      }
    } catch (error) {
      console.error('Failed to check for email status updates:', error);
    }
  }, [lastUpdateTime, onStatusChange]);

  const { isPolling, toggle: togglePolling } = usePolling({
    enabled,
    interval,
    onUpdate: checkForUpdates,
  });

  const clearUpdates = useCallback(() => {
    setStatusChanges([]);
    setHasUpdates(false);
  }, []);

  return {
    isPolling,
    hasUpdates,
    statusChanges,
    clearUpdates,
    togglePolling,
    lastUpdateTime,
  };
}

// Utility function to format last update time
export const formatLastUpdateTime = (date: Date | null): string => {
  if (!date) return 'Never';

  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);

  if (diffSeconds < 60) {
    return `${diffSeconds}s ago`;
  } else if (diffMinutes < 60) {
    return `${diffMinutes}m ago`;
  } else {
    return date.toLocaleTimeString();
  }
};
