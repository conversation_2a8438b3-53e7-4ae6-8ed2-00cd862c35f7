'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import {
  getFilteredDebugLogs,
  clearDebugLogs,
  exportDebugLogs,
  getDebugStats,
  type DebugLogEntry,
  type LogLevel,
  type LogCategory,
} from '@/lib/debug-logger';
import { cn } from '@/lib/utils';
import {
  FaBug,
  FaDownload,
  FaTrash,
  FaSearch,
  FaInfoCircle,
  FaExclamationTriangle,
  FaExclamationCircle,
  FaCog,
  FaChevronDown,
  FaChevronRight,
} from 'react-icons/fa';

interface DebugModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const LOG_LEVEL_COLORS: Record<LogLevel, string> = {
  debug: 'bg-gray-100 text-gray-800',
  info: 'bg-blue-100 text-blue-800',
  warn: 'bg-yellow-100 text-yellow-800',
  error: 'bg-red-100 text-red-800',
};

const LOG_LEVEL_ICONS: Record<LogLevel, React.ReactNode> = {
  debug: <FaCog className="w-3 h-3" />,
  info: <FaInfoCircle className="w-3 h-3" />,
  warn: <FaExclamationTriangle className="w-3 h-3" />,
  error: <FaExclamationCircle className="w-3 h-3" />,
};

export function DebugModal({ isOpen, onClose }: DebugModalProps) {
  const [filteredLogs, setFilteredLogs] = useState<DebugLogEntry[]>([]);
  const [stats, setStats] = useState(getDebugStats());
  const [expandedLogs, setExpandedLogs] = useState<Set<string>>(new Set());

  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState<LogLevel | 'all'>('all');
  const [categoryFilter, setCategoryFilter] = useState<LogCategory | 'all'>(
    'all'
  );
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Refresh logs
  const refreshLogs = React.useCallback(() => {
    setStats(getDebugStats());

    // Apply filters
    const filters: Parameters<typeof getFilteredDebugLogs>[0] = {};
    if (levelFilter !== 'all') filters.level = [levelFilter];
    if (categoryFilter !== 'all') filters.category = [categoryFilter];
    if (searchTerm) filters.search = searchTerm;

    const filtered = getFilteredDebugLogs(filters);
    setFilteredLogs(filtered);
  }, [levelFilter, categoryFilter, searchTerm]);

  // Auto-refresh effect
  useEffect(() => {
    if (isOpen) {
      refreshLogs();

      if (autoRefresh) {
        const interval = setInterval(refreshLogs, 2000);
        return () => clearInterval(interval);
      }
    }
  }, [isOpen, autoRefresh, refreshLogs]);

  const handleClearLogs = () => {
    clearDebugLogs();
    refreshLogs();
  };

  const handleExportLogs = () => {
    const data = exportDebugLogs();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `iepa-debug-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const toggleLogExpansion = (logId: string) => {
    const newExpanded = new Set(expandedLogs);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedLogs(newExpanded);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const renderLogEntry = (log: DebugLogEntry) => {
    const isExpanded = expandedLogs.has(log.id);

    return (
      <div key={log.id} className="border rounded-lg p-3 mb-2 bg-white">
        <div
          className="flex items-center justify-between cursor-pointer"
          onClick={() => toggleLogExpansion(log.id)}
        >
          <div className="flex items-center gap-2 flex-1">
            {isExpanded ? (
              <FaChevronDown className="w-3 h-3" />
            ) : (
              <FaChevronRight className="w-3 h-3" />
            )}
            <Badge className={cn('text-xs', LOG_LEVEL_COLORS[log.level])}>
              {LOG_LEVEL_ICONS[log.level]}
              <span className="ml-1">{log.level.toUpperCase()}</span>
            </Badge>
            <Badge variant="outline" className="text-xs">
              {log.category}
            </Badge>
            <span className="text-sm font-medium truncate">{log.message}</span>
          </div>
          <span className="text-xs text-gray-500 ml-2">
            {formatTimestamp(log.timestamp)}
          </span>
        </div>

        {isExpanded && (
          <div className="mt-3 space-y-2 text-sm">
            {log.data ? (
              <div>
                <strong>Data:</strong>
                <pre className="bg-gray-50 p-2 rounded text-xs overflow-auto max-h-32">
                  {JSON.stringify(log.data, null, 2)}
                </pre>
              </div>
            ) : null}

            {log.error && (
              <div>
                <strong>Error:</strong>
                <div className="bg-red-50 p-2 rounded">
                  <div>
                    <strong>Name:</strong> {log.error.name}
                  </div>
                  <div>
                    <strong>Message:</strong> {log.error.message}
                  </div>
                  {log.error.stack && (
                    <details className="mt-1">
                      <summary className="cursor-pointer text-xs">
                        Stack Trace
                      </summary>
                      <pre className="text-xs mt-1 overflow-auto max-h-32">
                        {log.error.stack}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            )}

            {log.context && (
              <div>
                <strong>Context:</strong>
                <pre className="bg-gray-50 p-2 rounded text-xs overflow-auto max-h-32">
                  {JSON.stringify(log.context, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FaBug className="w-5 h-5" />
            Debug Console
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="logs" className="flex-1 overflow-hidden">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="logs">Logs ({filteredLogs.length})</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="logs" className="flex-1 overflow-hidden">
            <div className="space-y-4 h-full">
              {/* Filters */}
              <div className="flex flex-wrap gap-2 items-center">
                <div className="flex items-center gap-2">
                  <FaSearch className="w-4 h-4" />
                  <Input
                    placeholder="Search logs..."
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className="w-48"
                  />
                </div>

                <Select
                  value={levelFilter}
                  onValueChange={value =>
                    setLevelFilter(value as LogLevel | 'all')
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="debug">Debug</SelectItem>
                    <SelectItem value="info">Info</SelectItem>
                    <SelectItem value="warn">Warning</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={categoryFilter}
                  onValueChange={value =>
                    setCategoryFilter(value as LogCategory | 'all')
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="form">Form</SelectItem>
                    <SelectItem value="api">API</SelectItem>
                    <SelectItem value="auth">Auth</SelectItem>
                    <SelectItem value="payment">Payment</SelectItem>
                    <SelectItem value="validation">Validation</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex gap-2 ml-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExportLogs}
                  >
                    <FaDownload className="w-3 h-3 mr-1" />
                    Export
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleClearLogs}>
                    <FaTrash className="w-3 h-3 mr-1" />
                    Clear
                  </Button>
                  <Button variant="outline" size="sm" onClick={refreshLogs}>
                    Refresh
                  </Button>
                </div>
              </div>

              {/* Logs List */}
              <div className="overflow-auto flex-1 max-h-96 border rounded-lg p-2">
                {filteredLogs.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    No logs found matching the current filters.
                  </div>
                ) : (
                  <div className="space-y-1">
                    {filteredLogs.slice().reverse().map(renderLogEntry)}
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="stats" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Total Logs</CardTitle>
                </CardHeader>
                <CardBody className="pt-0">
                  <div className="text-2xl font-bold">{stats.total}</div>
                </CardBody>
              </Card>

              {Object.entries(stats.byLevel).map(([level, count]) => (
                <Card key={level}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm flex items-center gap-1">
                      {LOG_LEVEL_ICONS[level as LogLevel]}
                      {level.charAt(0).toUpperCase() + level.slice(1)}
                    </CardTitle>
                  </CardHeader>
                  <CardBody className="pt-0">
                    <div className="text-2xl font-bold">{count}</div>
                  </CardBody>
                </Card>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>By Category</CardTitle>
                </CardHeader>
                <CardBody>
                  <div className="space-y-2">
                    {Object.entries(stats.byCategory).map(
                      ([category, count]) => (
                        <div key={category} className="flex justify-between">
                          <span className="capitalize">{category}</span>
                          <Badge variant="outline">{count}</Badge>
                        </div>
                      )
                    )}
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Time Range</CardTitle>
                </CardHeader>
                <CardBody>
                  <div className="space-y-2 text-sm">
                    <div>
                      <strong>Oldest:</strong>{' '}
                      {stats.timeRange.oldest
                        ? formatTimestamp(stats.timeRange.oldest)
                        : 'N/A'}
                    </div>
                    <div>
                      <strong>Newest:</strong>{' '}
                      {stats.timeRange.newest
                        ? formatTimestamp(stats.timeRange.newest)
                        : 'N/A'}
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Debug Settings</CardTitle>
              </CardHeader>
              <CardBody className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-refresh">Auto-refresh logs</Label>
                  <input
                    id="auto-refresh"
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={e => setAutoRefresh(e.target.checked)}
                    className="rounded"
                  />
                </div>

                <div className="text-sm text-gray-600">
                  <p>
                    Debug console captures form submissions, API calls,
                    validation errors, and other system events.
                  </p>
                  <p className="mt-2">
                    Logs are stored in memory and will be cleared when the page
                    is refreshed.
                  </p>
                </div>
              </CardBody>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
