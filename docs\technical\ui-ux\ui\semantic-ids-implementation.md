# Semantic CSS IDs and Class Names Implementation

## Overview

This document tracks the implementation of meaningful, semantic CSS IDs and class names throughout the IEPA conference registration application to improve developer experience and maintainability.

## Implementation Guidelines

### Naming Convention

- **IDs**: Use kebab-case with descriptive, human-readable names (e.g., `registration-form`, `speaker-bio-section`)
- **Classes**: Use kebab-case for reusable components (e.g., `.form-step`, `.pricing-card`, `.error-message`)
- **Test IDs**: Use data-testid attributes for automated testing

### Structure

- **IDs**: For unique elements (`#main-navigation`, `#registration-summary`)
- **Classes**: For reusable components (`.form-step`, `.pricing-card`, `.error-message`)
- **Component-specific naming**: Include component context (`registration-type-selector`, `personal-info-form`)

## Implemented Components

### 1. Navigation Component (`src/components/layout/Navigation.tsx`)

#### Main Navigation Structure

- `#main-navigation` - Main header navigation container
- `#navigation-brand-section` - Logo and brand section
- `#iepa-logo-link` - Main logo link
- `#iepa-logo-image` - IEPA logo image
- `#conference-title-full` - Full conference title (desktop)
- `#conference-title-short` - Short conference title (tablet)

#### Mobile Navigation

- `#mobile-menu-toggle` - Mobile hamburger menu button
- `#mobile-navigation-menu` - Mobile slide-out menu
- `#mobile-menu-title` - Mobile menu header title
- `#mobile-navigation-links` - Mobile navigation links container
- `#mobile-register-submenu` - Mobile register submenu section
- `#mobile-register-heading` - Mobile register section heading
- `#mobile-register-links` - Mobile register links container
- `#mobile-auth-links` - Mobile authentication links section

#### Desktop Navigation

- `#desktop-navigation-section` - Desktop navigation container
- `#desktop-navigation-list` - Desktop navigation menu list
- `#nav-about-link` - About page navigation link
- `#nav-register-trigger` - Register dropdown trigger
- `#nav-register-dropdown` - Register dropdown content
- `#nav-register-options` - Register dropdown options container
- `#nav-contact-link` - Contact page navigation link

#### User Menu Section

- `#user-menu-section` - User menu container
- `#authenticated-user-menu` - Authenticated user menu wrapper
- `#desktop-user-section` - Desktop user menu section
- `#mobile-user-section` - Mobile user menu section
- `#guest-auth-section` - Guest authentication buttons section

#### CSS Classes

- `.mobile-nav-link` - Mobile navigation link styling
- `.mobile-register-link` - Mobile register link styling
- `.mobile-auth-login` - Mobile login button styling
- `.mobile-auth-signup` - Mobile signup button styling
- `.desktop-register-link` - Desktop register dropdown link styling

### 2. Welcome Bar Component (`src/components/layout/WelcomeBar.tsx`)

#### Structure

- `#welcome-bar` - Main welcome bar container
- `#welcome-message-section` - Welcome message content section
- `#welcome-greeting` - Welcome greeting text
- `#user-email-display` - User email display container
- `#user-email-text` - User email text content
- `#welcome-bar-dismiss` - Dismiss button

### 3. Registration Card Radio Component (`src/components/ui/registration-card-radio.tsx`)

#### Main Component

- `#registration-type-selector` - Main radio group container
- `#registration-cards-container` - Cards container grid

#### Individual Cards (dynamic IDs based on option.id)

- `#registration-card-{id}` - Individual card wrapper
- `#registration-radio-{id}` - Hidden radio input
- `#registration-card-content-{id}` - Card content container
- `#registration-card-body-{id}` - Card body content
- `#registration-card-header-{id}` - Card header section
- `#registration-card-icon-{id}` - Card icon container
- `#registration-card-info-{id}` - Card title and description section
- `#registration-card-title-{id}` - Card title text
- `#registration-card-description-{id}` - Card description text
- `#registration-card-indicator-{id}` - Selection indicator
- `#registration-card-pricing-{id}` - Pricing section
- `#registration-card-price-{id}` - Main price display
- `#registration-card-price-unit-{id}` - Price unit text
- `#registration-card-group-price-{id}` - Group discount pricing

#### CSS Classes

- `.registration-card-radio-group` - Main radio group styling
- `.registration-card-label` - Card label styling

### 4. User Dropdown Component (`src/components/auth/UserDropdown.tsx`)

#### Trigger Section

- `#user-dropdown-trigger` - Dropdown trigger button
- `#user-avatar` - User avatar container
- `#user-avatar-fallback` - Avatar fallback initials
- `#user-status-indicator` - Email verification status indicator
- `#email-confirmed-indicator` - Confirmed email status dot
- `#email-unconfirmed-indicator` - Unconfirmed email status dot

#### Dropdown Content

- `#user-dropdown-menu` - Main dropdown menu container
- `#user-dropdown-header` - User info header section
- `#user-info-section` - User information container
- `#user-dropdown-avatar` - Dropdown avatar (larger)
- `#user-dropdown-avatar-fallback` - Dropdown avatar fallback
- `#user-details-section` - User details text section
- `#user-display-name` - User display name
- `#admin-badge` - Admin role badge
- `#user-email-display` - User email in dropdown
- `#user-verification-status` - Verification status section
- `#verified-badge` - Email verified badge
- `#unverified-badge` - Email unverified badge

### 5. Attendee Registration Form (`src/app/register/attendee/page.tsx`)

#### Page Structure

- `#attendee-registration-page` - Main page container
- `#registration-header` - Page header section
- `#registration-page-title` - Main page title
- `#registration-page-description` - Page description text
- `#progress-indicator-section` - Progress indicator section
- `#registration-form-section` - Main form section
- `#attendee-registration-form` - Registration form element

#### Progress Indicator

- `#form-progress-indicator` - Progress indicator container
- `#progress-header` - Progress header section
- `#progress-step-title` - Current step title
- `#progress-step-description` - Current step description
- `#progress-steps-bar` - Progress steps navigation bar
- `#progress-step-{number}` - Individual progress step (dynamic)
- `#progress-step-icon-{number}` - Progress step icon (dynamic)
- `#progress-step-label-{number}` - Progress step label (dynamic)

#### Form Steps

- `#registration-type-step` - Step 1: Registration type card
- `#registration-type-heading` - Step 1 heading
- `#registration-type-description` - Step 1 description
- `#registration-type-field` - Registration type field container
- `#personal-information-step` - Step 2: Personal information card
- `#personal-information-heading` - Step 2 heading
- `#personal-information-grid` - Personal info form grid
- `#full-name-field` - Full name field container
- `#full-name-input` - Full name input element

#### CSS Classes

- `.iepa-progress-step` - Progress step styling
- `.iepa-progress-step-active` - Active progress step
- `.iepa-progress-step-current` - Current progress step
- `.iepa-progress-step-warning` - Progress step with warnings
- `.iepa-progress-step-clickable` - Clickable progress step

## Testing Support

### Data Test IDs

All major interactive elements include `data-testid` attributes for automated testing:

- Navigation elements: `data-testid="main-navigation"`
- Form elements: `data-testid="registration-type-selector"`
- User interface: `data-testid="user-dropdown-trigger"`
- Progress tracking: `data-testid="progress-step-1"`

### Accessibility Support

- All IDs support ARIA attributes and form labels
- Proper semantic structure with role attributes
- Screen reader compatible element identification

## Next Steps

### Remaining Components to Implement

1. **File Upload Component** - Add semantic IDs for upload states
2. **Dashboard Components** - Add IDs for statistics and data visualization
3. **Form Validation** - Add consistent error message IDs
4. **Modal Components** - Add semantic IDs for dialog elements
5. **Card Components** - Add context-specific IDs for different card types

### Quality Assurance

- [ ] Run accessibility audit to verify ARIA compliance
- [ ] Test automated testing with new data-testid attributes
- [ ] Verify CSS styling hooks work with new class names
- [ ] Document any breaking changes for existing CSS

## Benefits Achieved

1. **Developer Experience**: Easier element selection during development and debugging
2. **Testing**: Reliable selectors for automated testing
3. **Accessibility**: Better screen reader support with semantic IDs
4. **Maintainability**: Consistent naming patterns across components
5. **Debugging**: Clear element identification in browser dev tools
