'use client';

import { useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  RegistrationTypeSelector,
} from '@/components/ui';
import { RegistrationRatesTable } from '@/components/ui/registration-rates-table';
import { ClickableSponsorCard } from '@/components/ui/sponsor-card-radio';
import { HeroImageSection } from '@/components/layout/HeroImageSection';
import { CONFERENCE_DATES, CONFERENCE_YEAR } from '@/lib/conference-config';
import {
  REGISTRATION_PRICING,
  ADDITIONAL_OPTIONS,
  SPONSORSHIP_PACKAGES,
  SPEAKER_PRICING,
  pricingUtils,
} from '@/lib/pricing-config';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function Home() {
  const { user, loading } = useAuth();
  const router = useRouter();

  // Redirect authenticated users to their registrations page
  useEffect(() => {
    if (!loading && user) {
      console.log(
        '🔄 Homepage - User authenticated, redirecting to my-registrations'
      );
      router.push('/my-registrations');
    }
  }, [user, loading, router]);

  return (
    <>
      {/* Hero Image Section */}
      <HeroImageSection backgroundImage="/tahoe_hero.jpg">
        <div className="text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="iepa-heading-1 mb-6">
              IEPA {CONFERENCE_YEAR} Annual Meeting
            </h1>
            <p className="iepa-body-large mb-4">
              {CONFERENCE_DATES.startDate.displayDate} -{' '}
              {CONFERENCE_DATES.endDate.displayDate}
            </p>
            <p className="iepa-body mb-8 max-w-2xl mx-auto">
              Join us for the premier event in environmental protection and
              sustainability. Connect with industry leaders, attend cutting-edge
              presentations, and network with professionals from across the
              field.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Button
                as={Link}
                href="/register"
                color="secondary"
                size="lg"
                className="w-full sm:w-auto"
              >
                Register Now
              </Button>
              <Button
                as={Link}
                href="/about"
                size="lg"
                className="w-full sm:w-auto iepa-btn-alt"
              >
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </HeroImageSection>

      <div className="iepa-container">
        {/* Registration Types Section */}
        <RegistrationTypeSelector
          showAuthButtons={true}
          isAuthenticated={!!user}
        />

        {/* Conference Highlights */}
        <section
          className="iepa-section"
          style={{ backgroundColor: 'var(--iepa-gray-50)' }}
        >
          <div className="text-center mb-12">
            <h2 className="iepa-heading-2 mb-4">Annual Meeting Highlights</h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            <div className="text-center">
              <div className="iepa-highlight-icon">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="iepa-heading-3 mb-2">Expert Sessions</h3>
              <p className="iepa-body-small">
                Learn from industry leaders and subject matter experts
              </p>
            </div>

            <div className="text-center">
              <div className="iepa-highlight-icon">
                <span className="text-2xl">🤝</span>
              </div>
              <h3 className="iepa-heading-3 mb-2">Networking</h3>
              <p className="iepa-body-small">
                Connect with professionals from across the industry
              </p>
            </div>

            <div className="text-center">
              <div className="iepa-highlight-icon">
                <span className="text-2xl">⛳</span>
              </div>
              <h3 className="iepa-heading-3 mb-2">Golf Tournament</h3>
              <p className="iepa-body-small">
                Enjoy a round of golf with colleagues and new connections
              </p>
            </div>

            <div className="text-center">
              <div className="iepa-highlight-icon">
                <span className="text-2xl">🏆</span>
              </div>
              <h3 className="iepa-heading-3 mb-2">Awards</h3>
              <p className="iepa-body-small">
                Celebrate excellence in environmental protection
              </p>
            </div>
          </div>
        </section>

        {/* Annual Meeting 2025 Registration Rates */}
        <section className="iepa-section">
          <div className="text-center mb-12">
            <h2 className="iepa-heading-2 mb-4">
              Annual Meeting 2025 Registration Rates
            </h2>
          </div>

          {/* Registration Rates Table */}
          <div className="max-w-6xl mx-auto mb-12">
            <Card>
              <CardHeader>
                <h3 className="iepa-heading-3 text-center">
                  Registration Rates
                </h3>
              </CardHeader>
              <CardBody>
                <RegistrationRatesTable
                  registrationPricing={REGISTRATION_PRICING}
                  additionalOptions={ADDITIONAL_OPTIONS}
                />
              </CardBody>
            </Card>
          </div>

          {/* Sponsorship Info */}
          <div className="max-w-6xl mx-auto mb-12">
            <Card>
              <CardHeader>
                <h3 className="iepa-heading-3 text-center">
                  Sponsorship Information
                </h3>
              </CardHeader>
              <CardBody>
                <p className="iepa-body mb-6 text-center">
                  Sponsorships include a complimentary registration at each
                  level of sponsorship. Registration includes two nights
                  lodging, all meals, two hosted receptions, two hosted
                  after-hours social gatherings and meeting materials.
                  Sponsorship is done online and can be paid online and via a
                  check or wire transfer.
                </p>

                <div className="space-y-6 mb-6">
                  {SPONSORSHIP_PACKAGES.map(pkg => (
                    <ClickableSponsorCard
                      key={pkg.id}
                      package={pkg}
                      className=""
                    />
                  ))}
                </div>

                <div className="text-center">
                  <p className="iepa-body-small mb-4">
                    <strong>
                      Sponsors are also acknowledged in various ways:
                    </strong>
                  </p>
                  <ul className="iepa-body-small text-left max-w-2xl mx-auto space-y-1">
                    <li>
                      • Sponsor Company information and company video are listed
                      on the IEPA website.
                    </li>
                    <li>
                      • Sponsors are acknowledged in IEPA printed and digital
                      agendas.
                    </li>
                    <li>
                      • Sponsor Tent Cards are placed on tables during all meals
                      and throughout the various meeting areas.
                    </li>
                    <li>
                      • Sponsor Roster that includes company information is
                      included in the meeting materials.
                    </li>
                  </ul>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Speaker Info */}
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <h3 className="iepa-heading-3 text-center">
                  Speaker Information
                </h3>
              </CardHeader>
              <CardBody>
                <div className="grid md:grid-cols-2 gap-6">
                  {SPEAKER_PRICING.map(speaker => (
                    <div key={speaker.id} className="text-center">
                      <h4 className="iepa-body font-semibold mb-3">
                        {speaker.name}
                      </h4>
                      <p className="iepa-body-small mb-3">
                        {speaker.description}
                      </p>
                      <p
                        className="iepa-body font-bold mb-3"
                        style={{ color: 'var(--iepa-primary-blue)' }}
                      >
                        {speaker.basePrice === 0
                          ? 'Complimentary'
                          : pricingUtils.formatPrice(speaker.basePrice)}
                      </p>
                      <ul className="iepa-body-small text-left space-y-1">
                        {speaker.inclusions.map((inclusion, index) => (
                          <li key={index}>• {inclusion}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>
        </section>

        {/* Call to Action */}
        {!user && (
          <section className="iepa-section text-center">
            <div className="max-w-2xl mx-auto">
              <h2 className="iepa-heading-2 mb-4">Ready to Join Us?</h2>
              <p className="iepa-body mb-6">
                Create an account to start your registration process and secure
                your spot at the IEPA {CONFERENCE_YEAR} Annual Meeting.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  as={Link}
                  href="/auth/signup"
                  color="secondary"
                  size="lg"
                  className="w-full sm:w-auto"
                >
                  Create Account
                </Button>
                <Button
                  as={Link}
                  href="/auth/login"
                  variant="bordered"
                  size="lg"
                  className="w-full sm:w-auto"
                >
                  Sign In
                </Button>
              </div>
            </div>
          </section>
        )}
      </div>
    </>
  );
}
