import { chromium, FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown for IEPA Email Center E2E tests...');

  // Launch browser for teardown
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Clean up test data if needed
    await cleanupTestData(page);

    // Generate test report summary
    await generateTestSummary();

    console.log('✅ Global teardown completed successfully');

  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  } finally {
    await browser.close();
  }
}

async function cleanupTestData(page: any) {
  try {
    console.log('🧹 Cleaning up test data...');

    // Navigate to admin page
    await page.goto('http://localhost:6969/admin/emails', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });

    // Clean up any test-specific data
    // This would depend on your test data strategy
    
    // Example: Remove test emails (if they have a specific pattern)
    // await page.request.delete('/api/admin/cleanup-test-data');

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.log('⚠️ Test data cleanup failed:', error.message);
  }
}

async function generateTestSummary() {
  try {
    console.log('📊 Generating test summary...');

    // Read test results if available
    const fs = require('fs');
    const path = require('path');
    
    const resultsPath = path.join(process.cwd(), 'test-results', 'results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      console.log('📈 Test Results Summary:');
      console.log(`   Total Tests: ${results.stats?.total || 'N/A'}`);
      console.log(`   Passed: ${results.stats?.passed || 'N/A'}`);
      console.log(`   Failed: ${results.stats?.failed || 'N/A'}`);
      console.log(`   Skipped: ${results.stats?.skipped || 'N/A'}`);
      console.log(`   Duration: ${results.stats?.duration || 'N/A'}ms`);
      
      // Log failed tests if any
      if (results.stats?.failed > 0) {
        console.log('❌ Failed Tests:');
        // This would require parsing the results structure
        // results.suites?.forEach(suite => {
        //   suite.specs?.forEach(spec => {
        //     if (spec.tests?.some(test => test.status === 'failed')) {
        //       console.log(`   - ${spec.title}`);
        //     }
        //   });
        // });
      }
    }

    console.log('✅ Test summary generated');
  } catch (error) {
    console.log('⚠️ Failed to generate test summary:', error.message);
  }
}

export default globalTeardown;
