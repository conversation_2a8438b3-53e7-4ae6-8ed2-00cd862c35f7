# Welcome Email Template

## Overview
The welcome email template is sent to new users when they first register for the IEPA conference. This email serves as an introduction to the event and provides essential information about what attendees can expect.

## Template Details
- **Template Key**: `welcome_email`
- **Template Name**: Welcome Email
- **Purpose**: Welcome new registrants and provide event overview
- **Trigger**: Sent immediately after successful registration

## Variables

### Required Variables
- `{{name}}` - The registrant's full name
- `{{confirmationNumber}}` - Unique registration confirmation number

### Optional Variables
- `{{eventDate}}` - Conference date (e.g., "October 15-16, 2025")
- `{{eventLocation}}` - Conference venue location
- `{{eventTime}}` - Conference start time

## Email Content Structure

### Subject Line
```
Welcome to IEPA 2025 Annual Meeting, {{name}}!
```

### HTML Template Features
- **IEPA Branding**: Uses official IEPA blue color (#3A6CA5)
- **Professional Layout**: Card-based design with header and content sections
- **Visual Elements**: Includes emojis for better engagement
- **Responsive Design**: Optimized for both desktop and mobile viewing

### Content Sections
1. **Welcome Header**: Branded header with IEPA 2025 branding
2. **Personal Greeting**: Personalized welcome message
3. **Event Overview**: Brief description of the conference
4. **What to Expect**: Bulleted list of conference highlights
5. **Event Details**: Date, location, and time information
6. **Confirmation Number**: Registration confirmation for reference
7. **Closing**: Professional sign-off from IEPA team

## Usage Guidelines

### When to Send
- Immediately after successful registration
- As part of the registration confirmation workflow
- Can be used for welcome campaigns

### Personalization
- Always include the registrant's name
- Include specific event details when available
- Ensure confirmation number is unique and valid

### Content Customization
The template includes placeholder content that should be customized:
- Update event dates and times
- Modify location information
- Adjust conference highlights based on actual agenda
- Update contact information as needed

## Sample Data for Testing
```json
{
  "name": "John Doe",
  "eventDate": "October 15-16, 2025",
  "eventLocation": "San Francisco Convention Center",
  "eventTime": "8:00 AM - 6:00 PM",
  "confirmationNumber": "IEPA-2025-001234"
}
```

## Integration Notes

### Database Fields
The template variables should map to these database fields:
- `name` → `first_name + " " + last_name` from user profile
- `confirmationNumber` → `confirmation_number` from registration record
- `eventDate` → Configuration setting or hardcoded value
- `eventLocation` → Configuration setting or hardcoded value
- `eventTime` → Configuration setting or hardcoded value

### Email Service Integration
- Template is compatible with SendGrid and other email services
- HTML content is optimized for email client compatibility
- Includes fallback text version for accessibility

## Maintenance

### Regular Updates
- Review content annually before conference registration opens
- Update event dates, locations, and times
- Refresh conference highlights and agenda items
- Verify contact information and links

### Testing
- Test with sample data before each conference cycle
- Verify rendering across different email clients
- Check mobile responsiveness
- Validate all variable substitutions

## Related Templates
- `registration_confirmation` - Detailed registration confirmation
- `speaker_confirmation` - Speaker-specific welcome
- `sponsor_confirmation` - Sponsor-specific welcome

## Version History
- **v1.0** - Initial welcome email template with IEPA branding
- Created: Current session
- Last Updated: Current session

---
**Template Type**: Welcome Email  
**Category**: User Onboarding  
**Priority**: High  
**Maintenance**: Annual review required
