import * as React from 'react';

import { cn } from '@/lib/utils';
import { Label } from './label';

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  // shadcn/ui specific props
  inputSize?: 'sm' | 'md' | 'lg'; // Renamed to avoid HTML size conflict
  // HeroUI compatibility props (for gradual migration)
  variant?: 'flat' | 'bordered' | 'underlined' | 'faded';
  color?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger';
  // Label support for HeroUI compatibility
  label?: string;
  description?: string;
  isRequired?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      inputSize = 'md',
      label,
      description,
      isRequired,
      id,
      ...props
    },
    ref
  ) => {
    const generatedId = React.useId();
    const inputId = id || `input-${generatedId}`;

    // Extract HeroUI compatibility props that shouldn't be passed to input
    const { variant, color, ...inputProps } = props;

    // Suppress unused variable warnings for compatibility props
    void variant;
    void color;

    if (label) {
      return (
        <div className="space-y-1">
          <Label htmlFor={inputId}>
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <input
            id={inputId}
            type={type}
            ref={ref}
            data-slot="input"
            className={cn(
              'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
              'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
              'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
              // IEPA enhanced styling and accessibility
              'border-[var(--iepa-gray-300)] focus-visible:ring-[var(--iepa-primary-blue)]/20 focus-visible:border-[var(--iepa-primary-blue)]',
              // Size variants with accessibility - Compact padding
              {
                'h-10 px-2 py-1 min-h-[44px]': inputSize === 'sm',
                'h-10 px-3 py-2 min-h-[44px]': inputSize === 'md',
                'h-11 px-3 py-2 min-h-[44px]': inputSize === 'lg',
              },
              className
            )}
            {...inputProps}
          />
          {description && (
            <p className="text-sm text-[var(--iepa-gray-600)]">{description}</p>
          )}
        </div>
      );
    }

    return (
      <input
        id={inputId}
        type={type}
        ref={ref}
        data-slot="input"
        className={cn(
          'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
          'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
          // IEPA enhanced styling and accessibility
          'border-[var(--iepa-gray-300)] focus-visible:ring-[var(--iepa-primary-blue)]/20 focus-visible:border-[var(--iepa-primary-blue)]',
          // Size variants with accessibility - Compact padding
          {
            'h-10 px-2 py-1 min-h-[44px]': inputSize === 'sm',
            'h-10 px-3 py-2 min-h-[44px]': inputSize === 'md',
            'h-11 px-3 py-2 min-h-[44px]': inputSize === 'lg',
          },
          className
        )}
        {...inputProps}
      />
    );
  }
);
Input.displayName = 'Input';

export { Input };
