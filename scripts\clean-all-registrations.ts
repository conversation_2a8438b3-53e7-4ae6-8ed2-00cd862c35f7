#!/usr/bin/env npx tsx

/**
 * Clean ALL Registrations Script
 * 
 * This script removes ALL registrations from the database since we're not live yet.
 * All current data is test data that should be cleared for fresh testing.
 * 
 * Usage: npm run clean-all-data
 */

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface CleanupStats {
  attendees: number;
  speakers: number;
  sponsors: number;
  userProfiles: number;
  emailLogs: number;
}

/**
 * Clean ALL attendee registrations
 */
async function cleanAllAttendees(): Promise<number> {
  console.log('🔍 Cleaning ALL attendee registrations...');
  
  const { data: attendees, error } = await supabase
    .from('iepa_attendee_registrations')
    .select('id, first_name, last_name, email');
  
  if (error) {
    console.error('❌ Error fetching attendees:', error);
    return 0;
  }
  
  if (!attendees || attendees.length === 0) {
    console.log('✅ No attendee registrations found');
    return 0;
  }
  
  console.log(`📋 Found ${attendees.length} attendee registrations to delete:`);
  attendees.forEach((attendee, index) => {
    console.log(`   ${index + 1}. ${attendee.first_name} ${attendee.last_name} (${attendee.email})`);
  });
  
  const { error: deleteError } = await supabase
    .from('iepa_attendee_registrations')
    .delete()
    .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records
  
  if (deleteError) {
    console.error('❌ Error deleting attendees:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${attendees.length} attendee registrations`);
  return attendees.length;
}

/**
 * Clean ALL speaker registrations
 */
async function cleanAllSpeakers(): Promise<number> {
  console.log('🔍 Cleaning ALL speaker registrations...');
  
  const { data: speakers, error } = await supabase
    .from('iepa_speaker_registrations')
    .select('id, first_name, last_name, email');
  
  if (error) {
    console.error('❌ Error fetching speakers:', error);
    return 0;
  }
  
  if (!speakers || speakers.length === 0) {
    console.log('✅ No speaker registrations found');
    return 0;
  }
  
  console.log(`📋 Found ${speakers.length} speaker registrations to delete:`);
  speakers.forEach((speaker, index) => {
    console.log(`   ${index + 1}. ${speaker.first_name} ${speaker.last_name} (${speaker.email})`);
  });
  
  const { error: deleteError } = await supabase
    .from('iepa_speaker_registrations')
    .delete()
    .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records
  
  if (deleteError) {
    console.error('❌ Error deleting speakers:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${speakers.length} speaker registrations`);
  return speakers.length;
}

/**
 * Clean ALL sponsor registrations
 */
async function cleanAllSponsors(): Promise<number> {
  console.log('🔍 Cleaning ALL sponsor registrations...');
  
  const { data: sponsors, error } = await supabase
    .from('iepa_sponsor_registrations')
    .select('id, contact_name, contact_email');
  
  if (error) {
    console.error('❌ Error fetching sponsors:', error);
    return 0;
  }
  
  if (!sponsors || sponsors.length === 0) {
    console.log('✅ No sponsor registrations found');
    return 0;
  }
  
  console.log(`📋 Found ${sponsors.length} sponsor registrations to delete:`);
  sponsors.forEach((sponsor, index) => {
    console.log(`   ${index + 1}. ${sponsor.contact_name} (${sponsor.contact_email})`);
  });
  
  const { error: deleteError } = await supabase
    .from('iepa_sponsor_registrations')
    .delete()
    .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records
  
  if (deleteError) {
    console.error('❌ Error deleting sponsors:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${sponsors.length} sponsor registrations`);
  return sponsors.length;
}

/**
 * Clean ALL user profiles
 */
async function cleanAllUserProfiles(): Promise<number> {
  console.log('🔍 Cleaning ALL user profiles...');
  
  const { data: profiles, error } = await supabase
    .from('iepa_user_profiles')
    .select('id, first_name, last_name, email');
  
  if (error) {
    console.error('❌ Error fetching user profiles:', error);
    return 0;
  }
  
  if (!profiles || profiles.length === 0) {
    console.log('✅ No user profiles found');
    return 0;
  }
  
  console.log(`📋 Found ${profiles.length} user profiles to delete`);
  console.log('   (Showing first 10 for brevity)');
  profiles.slice(0, 10).forEach((profile, index) => {
    console.log(`   ${index + 1}. ${profile.first_name} ${profile.last_name} (${profile.email})`);
  });
  
  if (profiles.length > 10) {
    console.log(`   ... and ${profiles.length - 10} more`);
  }
  
  const { error: deleteError } = await supabase
    .from('iepa_user_profiles')
    .delete()
    .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records
  
  if (deleteError) {
    console.error('❌ Error deleting user profiles:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${profiles.length} user profiles`);
  return profiles.length;
}

/**
 * Clean ALL email logs (if table exists)
 */
async function cleanAllEmailLogs(): Promise<number> {
  console.log('🔍 Cleaning ALL email logs...');
  
  const { data: emailLogs, error } = await supabase
    .from('iepa_email_logs')
    .select('id, recipient_email, subject');
  
  if (error) {
    if (error.code === '42P01') {
      console.log('ℹ️  Email logs table does not exist yet');
      return 0;
    }
    console.error('❌ Error fetching email logs:', error);
    return 0;
  }
  
  if (!emailLogs || emailLogs.length === 0) {
    console.log('✅ No email logs found');
    return 0;
  }
  
  console.log(`📋 Found ${emailLogs.length} email logs to delete`);
  
  const { error: deleteError } = await supabase
    .from('iepa_email_logs')
    .delete()
    .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records
  
  if (deleteError) {
    console.error('❌ Error deleting email logs:', deleteError);
    return 0;
  }
  
  console.log(`✅ Deleted ${emailLogs.length} email logs`);
  return emailLogs.length;
}

/**
 * Main cleanup function
 */
async function cleanAllRegistrations(): Promise<void> {
  console.log('🧹 CLEANING ALL REGISTRATIONS (PRE-LAUNCH)');
  console.log('==========================================');
  console.log('⚠️  This will delete ALL data since we are not live yet\n');
  
  const stats: CleanupStats = {
    attendees: 0,
    speakers: 0,
    sponsors: 0,
    userProfiles: 0,
    emailLogs: 0
  };
  
  try {
    // Clean all registrations
    stats.attendees = await cleanAllAttendees();
    stats.speakers = await cleanAllSpeakers();
    stats.sponsors = await cleanAllSponsors();
    
    // Clean all related data
    stats.userProfiles = await cleanAllUserProfiles();
    stats.emailLogs = await cleanAllEmailLogs();
    
    // Summary
    console.log('\n📊 Complete Cleanup Summary:');
    console.log('============================');
    console.log(`   Attendees: ${stats.attendees} deleted`);
    console.log(`   Speakers: ${stats.speakers} deleted`);
    console.log(`   Sponsors: ${stats.sponsors} deleted`);
    console.log(`   User Profiles: ${stats.userProfiles} deleted`);
    console.log(`   Email Logs: ${stats.emailLogs} deleted`);
    
    const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
    
    console.log(`\n✅ Successfully cleaned ${total} total records from the database`);
    console.log('🎯 Database is now completely clean and ready for fresh testing!');
    console.log('🚀 You can now run your end-to-end tests with a clean slate');
    
  } catch (error) {
    console.error('\n❌ Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the cleanup
if (require.main === module) {
  cleanAllRegistrations();
}

export { cleanAllRegistrations };
