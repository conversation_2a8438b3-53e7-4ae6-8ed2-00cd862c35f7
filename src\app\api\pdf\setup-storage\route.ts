// API Route for PDF Storage Setup
// POST /api/pdf/setup-storage

import { NextRequest, NextResponse } from 'next/server';
import {
  initializePDFStorage,
  testPDFStorageSetup,
  getPDFStorageInfo,
} from '@/lib/pdf-generation/services/storageSetup';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'initialize':
        const initResult = await initializePDFStorage();
        return NextResponse.json(initResult);

      case 'test':
        const testResult = await testPDFStorageSetup();
        return NextResponse.json(testResult);

      case 'info':
        const infoResult = await getPDFStorageInfo();
        return NextResponse.json(infoResult);

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid action. Use "initialize", "test", or "info"',
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in setup-storage API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'info';

    switch (action) {
      case 'test':
        const testResult = await testPDFStorageSetup();
        return NextResponse.json(testResult);

      case 'info':
      default:
        const infoResult = await getPDFStorageInfo();
        return NextResponse.json(infoResult);
    }
  } catch (error) {
    console.error('Error in setup-storage GET API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}
