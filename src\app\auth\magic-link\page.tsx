'use client';

import { useState, useEffect, Suspense } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, Input } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import { FiMail, FiArrowRight, FiCheck, FiLock } from 'react-icons/fi';

function MagicLinkContent() {
  const { signInWithMagicLink, user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      // Check if user came from a specific page and redirect back there
      const returnTo = searchParams?.get('returnTo') || '/my-registrations';
      console.log(
        '🔄 Magic Link - User already logged in, redirecting to:',
        returnTo
      );
      router.push(returnTo);
    }
  }, [user, router, searchParams]);

  const handleInputChange = (value: string) => {
    setEmail(value);
    if (error) setError(''); // Clear error when user starts typing
    if (success) setSuccess(false); // Clear success when user starts typing
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess(false);

    console.log('🔗 Magic Link Form Debug - Form submitted');
    console.log('📧 Form Email:', email);

    try {
      console.log('🚀 Calling signInWithMagicLink function...');

      // Get the return URL for after authentication
      const returnTo = searchParams?.get('returnTo') || '/my-registrations';

      const result = await signInWithMagicLink(email, returnTo);

      console.log('📊 Magic Link Result:', {
        hasResult: !!result,
        hasData: !!result?.data,
        hasError: !!result?.error,
        errorMessage: result?.error?.message,
      });

      if (result.error) {
        console.error('❌ Magic Link Error:', result.error);
        setError(
          result.error.message ||
            'An error occurred while sending the magic link'
        );
      } else {
        console.log('✅ Magic link sent successfully');
        setSuccess(true);
      }
    } catch (err) {
      console.error('💥 Magic Link Catch Block Error:', {
        error: err,
        errorType: typeof err,
        errorMessage: err instanceof Error ? err.message : 'Unknown error',
      });
      setError(
        err instanceof Error
          ? err.message
          : 'An error occurred while sending the magic link'
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="iepa-container">
        <section className="iepa-section">
          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <h1 className="iepa-heading-1 mb-4">Check Your Email</h1>
              <p className="iepa-body">
                We&apos;ve sent a secure login link to your email address
              </p>
            </div>

            <Card>
              <CardBody className="text-center space-y-6">
                <div className="flex justify-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                    <FiCheck className="w-8 h-8 text-green-600" />
                  </div>
                </div>

                <div className="space-y-3">
                  <h2 className="iepa-heading-3">Magic Link Sent!</h2>
                  <p className="iepa-body-small text-gray-600">
                    We&apos;ve sent a secure login link to:
                  </p>
                  <p
                    className="iepa-body font-semibold"
                    style={{ color: 'var(--iepa-primary-blue)' }}
                  >
                    {email}
                  </p>
                </div>

                <div className="space-y-3 text-left">
                  <p className="iepa-body-small text-gray-600">
                    <strong>Next steps:</strong>
                  </p>
                  <ul className="iepa-body-small text-gray-600 space-y-1 list-disc list-inside">
                    <li>Check your email inbox (and spam folder)</li>
                    <li>Click the &quot;Sign In&quot; link in the email</li>
                    <li>
                      You&apos;ll be automatically signed in and redirected
                    </li>
                  </ul>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <Button
                    onClick={() => {
                      setSuccess(false);
                      setEmail('');
                    }}
                    variant="outline"
                    className="w-full"
                  >
                    Send Another Link
                  </Button>
                </div>
              </CardBody>
            </Card>

            <div className="text-center mt-6">
              <p className="iepa-body-small">
                Having trouble?{' '}
                <Link
                  href="/contact"
                  className="font-semibold hover:underline"
                  style={{ color: 'var(--iepa-primary-blue)' }}
                >
                  Contact Support
                </Link>
              </p>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="iepa-heading-1 mb-4">Sign In</h1>
            <p className="iepa-body">
              Enter your email to receive a secure login link for the IEPA{' '}
              {CONFERENCE_YEAR} Annual Conference registration
            </p>
          </div>

          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2 text-center">Welcome Back</h2>
              <p className="iepa-body-small text-center text-gray-600">
                No password required - we&apos;ll send you a secure link
              </p>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="iepa-status-error">
                    <p className="iepa-body-small">{error}</p>
                  </div>
                )}

                <div className="space-y-4">
                  <div className="relative">
                    <Input
                      label="Email Address *"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={e => handleInputChange(e.target.value)}
                      isRequired
                      autoComplete="email"
                      className="pl-10"
                    />
                    <FiMail className="absolute left-3 top-9 w-4 h-4 text-gray-400" />
                  </div>
                </div>

                <div className="flex flex-col space-y-4">
                  <Button
                    type="submit"
                    color="secondary"
                    size="lg"
                    className="w-full"
                    disabled={isLoading || !email}
                  >
                    {isLoading ? (
                      'Sending Magic Link...'
                    ) : (
                      <>
                        <FiMail className="w-4 h-4 mr-2" />
                        Send Magic Link
                        <FiArrowRight className="w-4 h-4 ml-2" />
                      </>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    size="lg"
                    className="w-full"
                    onClick={() => window.location.href = '/auth/login?usePassword=true'}
                  >
                    <FiLock className="w-4 h-4 mr-2" />
                    Use password instead
                  </Button>
                </div>
              </form>
            </CardBody>
          </Card>

          <div className="text-center mt-6">
            <p className="iepa-body-small">
              Don&apos;t have an account?{' '}
              <Link
                href="/auth/signup"
                className="font-semibold hover:underline"
                style={{ color: 'var(--iepa-primary-blue)' }}
              >
                Create Account
              </Link>
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default function MagicLinkPage() {
  return (
    <Suspense
      fallback={
        <div className="iepa-container">
          <section className="iepa-section">
            <div className="max-w-md mx-auto">
              <div className="text-center">
                <h1 className="iepa-heading-1 mb-4">Loading...</h1>
                <p className="iepa-body">
                  Please wait while we prepare the sign-in page.
                </p>
              </div>
            </div>
          </section>
        </div>
      }
    >
      <MagicLinkContent />
    </Suspense>
  );
}
