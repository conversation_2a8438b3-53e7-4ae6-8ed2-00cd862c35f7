-- <PERSON><PERSON> Script to Clear All Speaker Registrations
-- Run this in the Supabase SQL Editor to clear existing speakers
-- This is necessary when transitioning to the new speaker-attendee linking system

-- First, let's see what we have
SELECT 'Current speaker count:' as info, COUNT(*) as count FROM iepa_speaker_registrations;
SELECT 'Current speaker-attendee count:' as info, COUNT(*) as count FROM iepa_attendee_registrations WHERE is_speaker = true;

-- Delete all speaker-attendee registrations first (to avoid foreign key issues)
DELETE FROM iepa_attendee_registrations WHERE is_speaker = true;

-- Delete all speaker registrations
DELETE FROM iepa_speaker_registrations;

-- Verify deletion
SELECT 'Remaining speakers:' as info, COUNT(*) as count FROM iepa_speaker_registrations;
SELECT 'Remaining speaker-attendees:' as info, COUNT(*) as count FROM iepa_attendee_registrations WHERE is_speaker = true;

-- Reset the sequences (optional, for clean IDs)
-- Note: This will only work if you have the necessary permissions
-- ALTER SEQUENCE iepa_speaker_registrations_id_seq RESTART WITH 1;
-- ALTER SEQUENCE iepa_attendee_registrations_id_seq RESTART WITH 1;

SELECT 'Cleanup completed successfully!' as status;
