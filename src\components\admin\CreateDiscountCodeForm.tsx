'use client';

import React, { useState } from 'react';
import { Button, AdminSubmitButton } from '@/components/ui';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/Checkbox';
import { showSuccess, showError } from '@/utils/notifications';
import { useAdminSubmitButton } from '@/hooks/useSubmitButton';

interface CreateDiscountCodeFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function CreateDiscountCodeForm({
  onSuccess,
  onCancel,
}: CreateDiscountCodeFormProps) {
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    discountType: 'percentage',
    discountValue: '',
    maxUses: '',
    maxUsesPerUser: '1',
    validFrom: '',
    validUntil: '',
    minimumAmount: '',
    applicableRegistrationTypes: [] as string[],
    createStripeCoupon: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Submit button state management
  const { isSubmitting, handleSubmit: handleSubmitButton } = useAdminSubmitButton((error) => {
    showError('Creation Failed', error);
  });

  const registrationTypes = [
    { value: 'attendee', label: 'Attendee' },
    { value: 'sponsor', label: 'Sponsor' },
    { value: 'speaker', label: 'Speaker' },
  ];

  const handleInputChange = (field: string, value: string | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.code.trim()) {
      newErrors.code = 'Discount code is required';
    } else if (!/^[A-Z0-9_-]+$/.test(formData.code)) {
      newErrors.code = 'Code must contain only uppercase letters, numbers, hyphens, and underscores';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.discountValue) {
      newErrors.discountValue = 'Discount value is required';
    } else {
      const value = parseFloat(formData.discountValue);
      if (isNaN(value) || value <= 0) {
        newErrors.discountValue = 'Discount value must be a positive number';
      } else if (formData.discountType === 'percentage' && value > 100) {
        newErrors.discountValue = 'Percentage discount cannot exceed 100%';
      }
    }

    if (formData.maxUses && (isNaN(parseInt(formData.maxUses)) || parseInt(formData.maxUses) <= 0)) {
      newErrors.maxUses = 'Max uses must be a positive number';
    }

    if (formData.maxUsesPerUser && (isNaN(parseInt(formData.maxUsesPerUser)) || parseInt(formData.maxUsesPerUser) <= 0)) {
      newErrors.maxUsesPerUser = 'Max uses per user must be a positive number';
    }

    if (formData.minimumAmount && (isNaN(parseFloat(formData.minimumAmount)) || parseFloat(formData.minimumAmount) < 0)) {
      newErrors.minimumAmount = 'Minimum amount must be a non-negative number';
    }

    if (formData.validUntil && formData.validFrom && new Date(formData.validUntil) <= new Date(formData.validFrom)) {
      newErrors.validUntil = 'Valid until date must be after valid from date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    await handleSubmitButton(async () => {
      const response = await fetch('/api/admin/discount-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: formData.code.toUpperCase(),
          name: formData.name,
          description: formData.description || null,
          discountType: formData.discountType,
          discountValue: parseFloat(formData.discountValue),
          maxUses: formData.maxUses ? parseInt(formData.maxUses) : null,
          maxUsesPerUser: parseInt(formData.maxUsesPerUser),
          validFrom: formData.validFrom || null,
          validUntil: formData.validUntil || null,
          minimumAmount: formData.minimumAmount ? parseFloat(formData.minimumAmount) : null,
          applicableRegistrationTypes: formData.applicableRegistrationTypes.length > 0
            ? formData.applicableRegistrationTypes
            : null,
          createStripeCoupon: formData.createStripeCoupon,
          userId: 'admin-user', // TODO: Get from auth context
        }),
      });

      const result = await response.json();

      if (result.success) {
        showSuccess('Success', 'Discount code created successfully');
        onSuccess?.();
      } else {
        throw new Error(result.error || 'Failed to create discount code');
      }
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Code */}
        <div>
          <Label htmlFor="code">Discount Code *</Label>
          <Input
            id="code"
            value={formData.code}
            onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
            placeholder="SAVE20"
            className={errors.code ? 'border-red-500' : ''}
          />
          {errors.code && <p className="text-sm text-red-600 mt-1">{errors.code}</p>}
        </div>

        {/* Name */}
        <div>
          <Label htmlFor="name">Display Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="20% Off Conference"
            className={errors.name ? 'border-red-500' : ''}
          />
          {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
        </div>
      </div>

      {/* Description */}
      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Save 20% on your conference registration"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Discount Type */}
        <div>
          <Label htmlFor="discountType">Discount Type *</Label>
          <Select value={formData.discountType} onValueChange={(value) => handleInputChange('discountType', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="percentage">Percentage</SelectItem>
              <SelectItem value="fixed_amount">Fixed Amount ($)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Discount Value */}
        <div>
          <Label htmlFor="discountValue">
            Discount Value * {formData.discountType === 'percentage' ? '(%)' : '($)'}
          </Label>
          <Input
            id="discountValue"
            type="number"
            step={formData.discountType === 'percentage' ? '1' : '0.01'}
            min="0"
            max={formData.discountType === 'percentage' ? '100' : undefined}
            value={formData.discountValue}
            onChange={(e) => handleInputChange('discountValue', e.target.value)}
            placeholder={formData.discountType === 'percentage' ? '20' : '100.00'}
            className={errors.discountValue ? 'border-red-500' : ''}
          />
          {errors.discountValue && <p className="text-sm text-red-600 mt-1">{errors.discountValue}</p>}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Max Uses */}
        <div>
          <Label htmlFor="maxUses">Max Total Uses</Label>
          <Input
            id="maxUses"
            type="number"
            min="1"
            value={formData.maxUses}
            onChange={(e) => handleInputChange('maxUses', e.target.value)}
            placeholder="100"
            className={errors.maxUses ? 'border-red-500' : ''}
          />
          {errors.maxUses && <p className="text-sm text-red-600 mt-1">{errors.maxUses}</p>}
        </div>

        {/* Max Uses Per User */}
        <div>
          <Label htmlFor="maxUsesPerUser">Max Uses Per User *</Label>
          <Input
            id="maxUsesPerUser"
            type="number"
            min="1"
            value={formData.maxUsesPerUser}
            onChange={(e) => handleInputChange('maxUsesPerUser', e.target.value)}
            className={errors.maxUsesPerUser ? 'border-red-500' : ''}
          />
          {errors.maxUsesPerUser && <p className="text-sm text-red-600 mt-1">{errors.maxUsesPerUser}</p>}
        </div>

        {/* Minimum Amount */}
        <div>
          <Label htmlFor="minimumAmount">Minimum Order Amount ($)</Label>
          <Input
            id="minimumAmount"
            type="number"
            step="0.01"
            min="0"
            value={formData.minimumAmount}
            onChange={(e) => handleInputChange('minimumAmount', e.target.value)}
            placeholder="0.00"
            className={errors.minimumAmount ? 'border-red-500' : ''}
          />
          {errors.minimumAmount && <p className="text-sm text-red-600 mt-1">{errors.minimumAmount}</p>}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Valid From */}
        <div>
          <Label htmlFor="validFrom">Valid From</Label>
          <Input
            id="validFrom"
            type="datetime-local"
            value={formData.validFrom}
            onChange={(e) => handleInputChange('validFrom', e.target.value)}
          />
        </div>

        {/* Valid Until */}
        <div>
          <Label htmlFor="validUntil">Valid Until</Label>
          <Input
            id="validUntil"
            type="datetime-local"
            value={formData.validUntil}
            onChange={(e) => handleInputChange('validUntil', e.target.value)}
            className={errors.validUntil ? 'border-red-500' : ''}
          />
          {errors.validUntil && <p className="text-sm text-red-600 mt-1">{errors.validUntil}</p>}
        </div>
      </div>

      {/* Applicable Registration Types */}
      <div>
        <Label>Applicable Registration Types (leave empty for all)</Label>
        <div className="flex flex-wrap gap-4 mt-2">
          {registrationTypes.map((type) => (
            <div key={type.value} className="flex items-center space-x-2">
              <Checkbox
                id={type.value}
                checked={formData.applicableRegistrationTypes.includes(type.value)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    handleInputChange('applicableRegistrationTypes', [
                      ...formData.applicableRegistrationTypes,
                      type.value,
                    ]);
                  } else {
                    handleInputChange(
                      'applicableRegistrationTypes',
                      formData.applicableRegistrationTypes.filter((t) => t !== type.value)
                    );
                  }
                }}
              />
              <Label htmlFor={type.value}>{type.label}</Label>
            </div>
          ))}
        </div>
      </div>

      {/* Create Stripe Coupon */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="createStripeCoupon"
          checked={formData.createStripeCoupon}
          onCheckedChange={(checked) => handleInputChange('createStripeCoupon', checked)}
        />
        <Label htmlFor="createStripeCoupon">Create corresponding Stripe coupon</Label>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <AdminSubmitButton
          isSubmitting={isSubmitting}
          submittingText="Creating Discount Code..."
          data-testid="create-discount-code-button"
        >
          Create Discount Code
        </AdminSubmitButton>
      </div>
    </form>
  );
}
