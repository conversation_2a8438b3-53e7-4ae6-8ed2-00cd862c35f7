# Supabase SendGrid Click Tracking Fix

## Problem

Magic link authentication fails with "Missing authentication code in callback URL" because Send<PERSON>rid's click tracking is wrapping Supabase authentication URLs with tracking redirects that strip the authentication code parameter.

## Root Cause

Supabase is configured to use <PERSON><PERSON>rid as the SMTP provider for authentication emails, but Send<PERSON><PERSON>'s click tracking is enabled at the account level, which interferes with magic link URLs.

## Solution Options

### Option 1: Disable Click Tracking in SendGrid Account (Recommended)

1. **Login to SendGrid Dashboard**

   - Go to https://app.sendgrid.com/
   - Navigate to **Settings** → **Tracking**

2. **Disable Click Tracking Globally**

   - Turn OFF "Click Tracking" for all emails
   - Keep "Open Tracking" enabled for analytics
   - Save settings

3. **Alternative: Disable for Specific Emails**
   - Go to **Marketing** → **Automation**
   - Find authentication email templates
   - Disable click tracking per template

### Option 2: Configure Supabase SMTP Settings

1. **Access Supabase Dashboard**

   - Go to your project dashboard
   - Navigate to **Authentication** → **Settings**

2. **Configure Custom SMTP**

   ```
   SMTP Host: smtp.sendgrid.net
   SMTP Port: 587
   SMTP User: apikey
   SMTP Pass: [Your SendGrid API Key]

   Additional Headers:
   X-SMTPAPI: {"filters":{"clicktrack":{"settings":{"enable":0}}}}
   ```

3. **Update Email Templates**
   - Go to **Authentication** → **Email Templates**
   - Ensure all templates use plain URLs without tracking

### Option 3: Switch to Alternative SMTP Provider

Consider switching to an SMTP provider that doesn't interfere with authentication URLs:

1. **Resend** (Recommended for auth emails)

   - No click tracking by default
   - Developer-friendly
   - Reliable delivery

2. **Amazon SES**

   - No click tracking interference
   - Cost-effective
   - High deliverability

3. **Postmark**
   - Designed for transactional emails
   - No tracking interference
   - Excellent for authentication

## Immediate Fix Steps

### Step 1: SendGrid Account Configuration

1. Login to SendGrid dashboard
2. Go to **Settings** → **Tracking**
3. **Disable "Click Tracking"** globally
4. Keep "Open Tracking" enabled
5. Save changes

### Step 2: Test Magic Link

1. Go to https://iepa.vercel.app/auth/login
2. Enter your email address
3. Click "Send Magic Link"
4. Check email for new magic link
5. Click the link - should work without tracking URL

### Step 3: Verify Fix

The magic link URL should look like:

```
https://iepa.vercel.app/auth/callback?code=abc123...&next=/my-registrations
```

NOT like:

```
https://u11949574.ct.sendgrid.net/ls/click?upn=...
```

## Long-term Recommendations

1. **Use Resend for Authentication Emails**

   - More reliable for auth flows
   - No click tracking interference
   - Better developer experience

2. **Keep SendGrid for Marketing Emails**

   - Use SendGrid for newsletters, confirmations
   - Use Resend specifically for authentication
   - Separate concerns for better reliability

3. **Monitor Email Deliverability**
   - Set up monitoring for auth email delivery
   - Track authentication success rates
   - Monitor for any tracking interference

## Testing Checklist

- [ ] SendGrid click tracking disabled
- [ ] Magic link emails received without tracking URLs
- [ ] Magic link authentication works successfully
- [ ] Users can log in via email links
- [ ] Callback URLs contain proper authentication codes
- [ ] No "Missing authentication code" errors

## Support

If issues persist:

1. Check SendGrid account settings
2. Verify Supabase SMTP configuration
3. Test with different email providers
4. Contact SendGrid support for tracking configuration
