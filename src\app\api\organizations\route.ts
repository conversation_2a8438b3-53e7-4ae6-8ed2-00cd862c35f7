import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    console.log('[ORG-API] Fetching organizations...');
    
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');

    let query = supabase
      .from('iepa_organizations')
      .select('id, name, normalized_name, usage_count, last_used_at')
      .eq('is_active', true)
      .order('usage_count', { ascending: false })
      .order('name', { ascending: true })
      .limit(limit);

    // Add search filter if provided
    if (search && search.trim()) {
      const searchTerm = search.trim().toLowerCase();
      query = query.or(`name.ilike.%${searchTerm}%,normalized_name.ilike.%${searchTerm}%`);
    }

    const { data: organizations, error } = await query;

    if (error) {
      console.error('[ORG-API] Error fetching organizations:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch organizations',
        details: error.message
      }, { status: 500 });
    }

    console.log('[ORG-API] Successfully fetched', organizations?.length || 0, 'organizations');

    return NextResponse.json({
      success: true,
      organizations: organizations || [],
      count: organizations?.length || 0
    });

  } catch (error: any) {
    console.error('[ORG-API] Unexpected error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('[ORG-API] Creating new organization...');
    
    const body = await request.json();
    const { name } = body;

    if (!name || typeof name !== 'string' || !name.trim()) {
      return NextResponse.json({
        success: false,
        error: 'Organization name is required'
      }, { status: 400 });
    }

    const orgName = name.trim();
    const normalizedName = orgName.toLowerCase();

    // Check if organization already exists
    const { data: existing, error: existingError } = await supabase
      .from('iepa_organizations')
      .select('id, name')
      .eq('normalized_name', normalizedName)
      .single();

    if (existingError && existingError.code !== 'PGRST116') {
      console.error('[ORG-API] Error checking existing organization:', existingError);
      return NextResponse.json({
        success: false,
        error: 'Database error',
        details: existingError.message
      }, { status: 500 });
    }

    if (existing) {
      // Organization already exists, return it
      console.log('[ORG-API] Organization already exists:', existing.name);
      return NextResponse.json({
        success: true,
        organization: existing,
        message: 'Organization already exists'
      });
    }

    // Create new organization
    const { data: newOrg, error: insertError } = await supabase
      .from('iepa_organizations')
      .insert([{
        name: orgName,
        normalized_name: normalizedName,
        usage_count: 1,
        last_used_at: new Date().toISOString()
      }])
      .select('id, name, normalized_name, usage_count')
      .single();

    if (insertError) {
      console.error('[ORG-API] Error creating organization:', insertError);
      return NextResponse.json({
        success: false,
        error: 'Failed to create organization',
        details: insertError.message
      }, { status: 500 });
    }

    console.log('[ORG-API] Successfully created organization:', newOrg.name);

    return NextResponse.json({
      success: true,
      organization: newOrg,
      message: 'Organization created successfully'
    });

  } catch (error: any) {
    console.error('[ORG-API] Unexpected error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}

// Function to increment usage count when an organization is used
export async function PATCH(request: NextRequest) {
  try {
    console.log('[ORG-API] Updating organization usage...');
    
    const body = await request.json();
    const { organizationName } = body;

    if (!organizationName || typeof organizationName !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Organization name is required'
      }, { status: 400 });
    }

    const normalizedName = organizationName.trim().toLowerCase();

    // First get the current usage count, then update it
    const { data: current, error: fetchError } = await supabase
      .from('iepa_organizations')
      .select('id, name, usage_count')
      .eq('normalized_name', normalizedName)
      .single();

    if (fetchError) {
      console.error('[ORG-API] Error fetching organization:', fetchError);
      return NextResponse.json({
        success: false,
        error: 'Organization not found',
        details: fetchError.message
      }, { status: 404 });
    }

    // Update with incremented usage count
    const { data: updated, error } = await supabase
      .from('iepa_organizations')
      .update({
        usage_count: (current.usage_count || 0) + 1,
        last_used_at: new Date().toISOString()
      })
      .eq('normalized_name', normalizedName)
      .select('id, name, usage_count')
      .single();

    if (error) {
      console.error('[ORG-API] Error updating organization usage:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to update organization usage',
        details: error.message
      }, { status: 500 });
    }

    console.log('[ORG-API] Successfully updated organization usage:', updated.name);

    return NextResponse.json({
      success: true,
      organization: updated
    });

  } catch (error: any) {
    console.error('[ORG-API] Unexpected error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
} 