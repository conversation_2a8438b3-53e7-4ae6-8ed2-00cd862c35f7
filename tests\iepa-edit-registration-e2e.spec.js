import { test, expect } from '@playwright/test';

/**
 * IEPA Edit Registration End-to-End Test
 *
 * This test verifies the edit registration flow for existing registrations,
 * including adding golf options and completing payment with promo codes.
 */

// Test configuration
const TEST_CONFIG = {
  // Timeouts and delays
  timeouts: {
    navigation: 30000,
    formFill: 5000,
    payment: 60000,
    email: 30000,
  },

  // Test promo code (should provide 100% discount)
  promoCode: 'TEST',
};

// Helper functions
class EditRegistrationHelpers {
  constructor(page) {
    this.page = page;
  }

  async navigateToMyRegistrations() {
    console.log('🔗 Navigating to My Registrations page...');
    await this.page.goto('/my-registrations');
    await this.page.waitForLoadState('networkidle');

    // Verify we're on the correct page
    await expect(this.page.locator('text=My Registrations')).toBeVisible();
    console.log('✅ My Registrations page loaded successfully');
  }

  async clickEditRegistration() {
    console.log('✏️ Clicking Edit Registration button...');

    // Look for edit registration link
    const editLink = this.page.locator('text=Edit Registration').first();
    await expect(editLink).toBeVisible();
    await editLink.click();

    // Wait for edit page to load
    await this.page.waitForLoadState('networkidle');
    console.log('✅ Edit registration page loaded');
  }

  async addGolfOptions() {
    console.log('⛳ Adding golf tournament and club rental...');

    // Navigate to event options step if not already there
    try {
      await this.page.click('text=4. Event Options');
      await this.page.waitForLoadState('networkidle');
    } catch (error) {
      console.log('ℹ️ Already on event options step or step not found');
    }

    // Enable golf tournament
    const golfCheckbox = this.page.locator('#golf-tournament');
    if (await golfCheckbox.isVisible()) {
      await golfCheckbox.check();
      await expect(golfCheckbox).toBeChecked();
      console.log('✅ Golf tournament enabled');
    }

    // Enable golf club rental
    const clubRentalCheckbox = this.page.locator('#golf-club-rental');
    if (await clubRentalCheckbox.isVisible()) {
      await clubRentalCheckbox.check();
      await expect(clubRentalCheckbox).toBeChecked();
      console.log('✅ Golf club rental enabled');
    }

    // Select club handedness
    const rightHandedOption = this.page.locator('input[value="right-handed"]');
    if (await rightHandedOption.isVisible()) {
      await rightHandedOption.click();
      console.log('✅ Right-handed clubs selected');
    }
  }

  async proceedToCheckout() {
    console.log('💳 Proceeding to checkout...');

    // Navigate to review & payment step
    await this.page.click('text=6. Review & Payment');
    await this.page.waitForLoadState('networkidle');

    console.log('✅ Reached checkout step');
  }

  async applyPromoCode() {
    console.log('🎫 Applying promo code for discount...');

    try {
      // Click "Have a discount code?" button
      await this.page.click('text=Have a discount code?');

      // Wait for discount code input to appear
      await this.page.waitForSelector('input[placeholder="Enter code"]');

      // Enter promo code
      await this.page.fill(
        'input[placeholder="Enter code"]',
        TEST_CONFIG.promoCode
      );

      // Apply the code
      await this.page.click('button:has-text("Apply")');

      // Wait for discount to be applied
      await this.page.waitForTimeout(2000);

      console.log('✅ Promo code applied successfully');
    } catch (error) {
      console.log('⚠️ Could not apply promo code:', error.message);
    }
  }

  async completePayment() {
    console.log('💰 Completing payment...');

    // Click submit/pay button
    const submitButton = this.page
      .locator(
        'button:has-text("Complete Registration"), button:has-text("Update Registration"), button:has-text("Save Changes")'
      )
      .first();
    await submitButton.click();

    // Wait for payment processing or success
    try {
      // Check if we're redirected to Stripe
      await this.page.waitForURL('**/checkout.stripe.com/**', {
        timeout: 5000,
      });

      console.log('🔄 Redirected to Stripe checkout...');
      // For test purposes, we'll assume Stripe payment completes
    } catch (error) {
      console.log(
        'ℹ️ No Stripe redirect (likely $0 payment or update), checking for success...'
      );
    }

    // Wait for success page or confirmation
    try {
      await this.page.waitForURL('**/payment/success**', {
        timeout: TEST_CONFIG.timeouts.payment,
      });
      console.log('✅ Payment completed successfully');
    } catch (error) {
      // Might be an update that doesn't require payment
      console.log(
        'ℹ️ No payment success page, checking for update confirmation...'
      );

      // Check if we're back on my registrations or see success message
      const successMessage = this.page.locator(
        'text=Registration updated, text=Changes saved, text=Update successful'
      );
      if (await successMessage.isVisible({ timeout: 5000 })) {
        console.log('✅ Registration updated successfully');
      } else {
        console.log('⚠️ Could not confirm payment/update completion');
      }
    }
  }

  async verifyUpdatedRegistration() {
    console.log('📋 Verifying updated registration...');

    // Navigate back to my registrations if not already there
    if (!this.page.url().includes('/my-registrations')) {
      await this.page.goto('/my-registrations');
      await this.page.waitForLoadState('networkidle');
    }

    // Verify registration appears with golf options
    await expect(this.page.locator('text=My Registrations')).toBeVisible();

    // Look for golf tournament indication
    const golfIndicator = this.page.locator(
      'text=Golf Tournament, text=Golf, text=⛳'
    );
    if (await golfIndicator.isVisible({ timeout: 5000 })) {
      console.log('✅ Golf tournament verified in registration');
    } else {
      console.log('⚠️ Golf tournament not visible in registration summary');
    }

    console.log('✅ Registration verification completed');
  }

  async takeScreenshot(name) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `test-results/iepa-edit-e2e-${name}-${timestamp}.png`;
    await this.page.screenshot({
      path: filename,
      fullPage: true,
    });
    console.log(`📸 Screenshot saved: ${filename}`);
  }
}

// Main test
test.describe('IEPA Edit Registration E2E Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Set longer timeout for this test suite
    test.setTimeout(120000); // 2 minutes

    // Set viewport for consistent testing
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should edit existing registration to add golf options and complete payment', async ({
    page,
  }) => {
    const helpers = new EditRegistrationHelpers(page);

    console.log('🚀 Starting IEPA Edit Registration E2E Test...');

    try {
      // Step 1: Navigate to My Registrations
      await helpers.navigateToMyRegistrations();
      await helpers.takeScreenshot('01-my-registrations');

      // Step 2: Click Edit Registration
      await helpers.clickEditRegistration();
      await helpers.takeScreenshot('02-edit-registration-page');

      // Step 3: Add golf options
      await helpers.addGolfOptions();
      await helpers.takeScreenshot('03-golf-options-added');

      // Step 4: Proceed to checkout
      await helpers.proceedToCheckout();
      await helpers.takeScreenshot('04-checkout-page');

      // Step 5: Apply promo code
      await helpers.applyPromoCode();
      await helpers.takeScreenshot('05-promo-code-applied');

      // Step 6: Complete payment/update
      await helpers.completePayment();
      await helpers.takeScreenshot('06-payment-completed');

      // Step 7: Verify updated registration
      await helpers.verifyUpdatedRegistration();
      await helpers.takeScreenshot('07-updated-registration');

      console.log('🎉 IEPA Edit Registration E2E Test completed successfully!');
    } catch (error) {
      console.error('❌ Test failed:', error);
      await helpers.takeScreenshot('error-state');
      throw error;
    }
  });

  test('should verify registration details are preserved during edit', async ({
    page,
  }) => {
    console.log('🔍 Testing registration detail preservation...');

    const helpers = new EditRegistrationHelpers(page);

    // Navigate to my registrations
    await helpers.navigateToMyRegistrations();

    // Verify existing registration details are shown
    await expect(page.locator('text=John Tester')).toBeVisible();
    await expect(page.locator('text=iepa-member')).toBeVisible();

    console.log('✅ Registration details preservation test completed');
  });
});
