'use client';

import { useState } from 'react';

// Force dynamic rendering to avoid SSG issues with useSearchParams
export const dynamic = 'force-dynamic';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Checkbox,
  CancellationPolicy,
  HydrationSafeInput,
  HydrationSafePhoneInput,
  // IEPASubmitButton, // TODO: Create this component
} from '@/components/ui';
import OrganizationCombobox from '@/components/ui/OrganizationCombobox';
import ScrollspyNavigation from '@/components/ui/scrollspy-navigation';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import { STATE_PROVINCE_OPTIONS } from '@/lib/address-constants';
import { FORM_STORAGE_KEYS } from '@/lib/form-persistence';
import { useFormPersistence } from '@/hooks/useFormPersistence';
import { useScrollspy } from '@/hooks/useScrollspy';
import {
  RestoreDataPrompt,
  FloatingSaveStatus,
  DataManagementPanel,
} from '@/components/ui/form-persistence-ui';
import { cn } from '@/lib/utils';
import {
  FaIdCard,
  FaPhone,
  FaCalendarAlt,
  FaExclamationTriangle,
  FaCheckCircle,
  FaStar,
} from 'react-icons/fa';
import { useAuth } from '@/contexts/AuthContext';
import { useFormPrefill } from '@/hooks/useFormPrefill';
import { ProtectedRegistrationPage } from '@/components/auth/ProtectedRoute';
import { supabase } from '@/lib/supabase';
import { showSuccess, showError } from '@/utils/notifications';
import { useRegistrationConstraints } from '@/hooks/useRegistrationConstraints';
import { ExistingRegistrationNotice } from '@/components/registration/ExistingRegistrationNotice';
// import { useSubmitButton } from '@/hooks/useSubmitButton';
// import { useTestDataFill } from '@/hooks/useTestDataFill';

// Form sections for single-page layout with scrollspy
const FORM_SECTIONS = [
  {
    id: 'sponsor-info',
    title: 'Sponsor Information',
    description: 'Sponsor attendee registration details',
    icon: FaStar,
  },
  {
    id: 'personal-information',
    title: 'Personal Information',
    description: 'Your personal details',
    icon: FaIdCard,
  },
  {
    id: 'contact-information',
    title: 'Contact Information',
    description: 'Contact and professional details',
    icon: FaPhone,
  },
  {
    id: 'event-options',
    title: 'Event Options',
    description: 'Optional add-ons and preferences',
    icon: FaCalendarAlt,
  },
  {
    id: 'emergency-contact',
    title: 'Emergency Contact',
    description: 'Emergency contact information',
    icon: FaExclamationTriangle,
  },
  {
    id: 'review-payment',
    title: 'Review & Payment',
    description: 'Review your registration and complete payment',
    icon: FaCheckCircle,
  },
];

export default function SponsorAttendeeRegistrationPage() {
  const { user, session } = useAuth();

  // Check registration constraints
  const { permissions, loading: constraintLoading } =
    useRegistrationConstraints({
      registrationType: 'sponsor-attendee',
      attendeeType: 'attendee',
    });

  // Form state
  const [formData, setFormData] = useState({
    registrationType: 'sponsor-attendee', // Pre-configured and locked
    firstName: '',
    lastName: '',
    nameOnBadge: '',
    email: user?.email || '',
    gender: '',
    phoneNumber: '',
    organization: '',
    jobTitle: '',
    streetAddress: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    golfTournament: false,
    golfClubRental: false,
    golfClubHandedness: '',
    // Night selections for annual meeting lodging (selectable for sponsor attendees)
    nightOne: false,
    nightTwo: false,
    // Meal selections for September 15-17, 2025 (selectable for sponsor attendees)
    meals: {
      sept15Dinner: false,
      sept16Breakfast: false,
      sept16Lunch: false,
      sept16Dinner: false,
      sept17Breakfast: false,
      sept17Lunch: false,
    },
    dietaryRestrictions: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Submit button state management
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    title: string;
    message: string;
    registrationId?: string;
    totalAmount?: number;
  } | null>(null);

  // Form persistence
  const persistence = useFormPersistence(formData, setFormData, {
    formKey: FORM_STORAGE_KEYS.SPONSOR_ATTENDEE_REGISTRATION,
    debounceMs: 1000,
    expirationDays: 7,
    excludeFields: [],
    onDataRestored: data => {
      console.log('Sponsor attendee form data restored:', data);
      showSuccess(
        'Form Data Restored',
        'Your previous progress has been restored.'
      );
    },
    onDataSaved: () => {
      console.log('Sponsor attendee form data auto-saved');
    },
    onDataCleared: () => {
      console.log('Sponsor attendee form data cleared');
    },
  });

  // Test data auto-fill functionality (disabled in production)
  // const { fillTestData, isTestMode } = useTestDataFill(
  //   'sponsor-attendee',
  //   setFormData,
  //   {
  //     onTestDataFilled: (summary) => {
  //       console.log('Test data filled:', summary);
  //       showInfo('Test Data Loaded', summary);
  //     },
  //     onError: (error) => {
  //       console.error('Test data fill error:', error);
  //       showError('Test Data Error', error);
  //     },
  //     enabled: true,
  //   }
  // );

  // Form prefill
  useFormPrefill({
    userEmail: user?.email,
    onDataLoaded: profileData => {
      setFormData(prev => ({
        ...prev,
        firstName: profileData.firstName || prev.firstName,
        lastName: profileData.lastName || prev.lastName,
        phoneNumber: profileData.phoneNumber || prev.phoneNumber,
        organization: profileData.organization || prev.organization,
        jobTitle: profileData.jobTitle || prev.jobTitle,
        streetAddress: profileData.streetAddress || prev.streetAddress,
        city: profileData.city || prev.city,
        state: profileData.state || prev.state,
        zipCode: profileData.zipCode || prev.zipCode,
        country: profileData.country || prev.country,
      }));
    },
  });

  // Conference meal options for September 15-17, 2025 (All meals are complimentary for sponsor attendees)
  const mealOptions = [
    {
      date: 'September 15, 2025',
      day: 'Monday',
      meals: [
        {
          key: 'sept15Dinner',
          name: 'Dinner',
          time: '6:00 PM',
          price: 0,
          description: 'Welcome dinner with networking reception',
        },
      ],
    },
    {
      date: 'September 16, 2025',
      day: 'Tuesday',
      meals: [
        {
          key: 'sept16Breakfast',
          name: 'Breakfast',
          time: '7:00 AM',
          price: 0,
          description: 'Continental breakfast before morning sessions',
        },
        {
          key: 'sept16Lunch',
          name: 'Lunch',
          time: '12:00 PM',
          price: 0,
          description: 'Networking lunch with keynote presentation',
        },
        {
          key: 'sept16Dinner',
          name: 'Dinner',
          time: '6:30 PM',
          price: 0,
          description: 'Reception and plated dinner',
        },
      ],
    },
    {
      date: 'September 17, 2025',
      day: 'Wednesday',
      meals: [
        {
          key: 'sept17Breakfast',
          name: 'Breakfast',
          time: '7:00 AM',
          price: 0,
          description: 'Continental breakfast before final sessions',
        },
        {
          key: 'sept17Lunch',
          name: 'Lunch',
          time: '12:00 PM',
          price: 0,
          description: 'Closing lunch and farewell',
        },
      ],
    },
  ];

  // Scrollspy
  const { activeSection } = useScrollspy(FORM_SECTIONS.map(s => s.id));

  // Handle input changes
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle meal selection changes
  const handleMealChange = (mealKey: string, value: boolean | string) => {
    setFormData(prev => ({
      ...prev,
      meals: {
        ...prev.meals,
        [mealKey]: Boolean(value),
      },
    }));
  };

  // Scrollspy click handler
  const handleScrollspyClick = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Calculate pricing (sponsor attendees get free registration, only pay for golf)
  const calculatePricing = () => {
    const registrationTotal = 0; // Free for sponsor attendees
    const golfTotal = formData.golfTournament ? 200 : 0;
    const golfClubRentalTotal = formData.golfClubRental ? 75 : 0; // Updated to $75
    const mealTotal = 0; // All meals included for sponsor attendees
    const grandTotal =
      registrationTotal + golfTotal + golfClubRentalTotal + mealTotal;

    return {
      registrationTotal,
      golfTotal,
      golfClubRentalTotal,
      mealTotal,
      grandTotal,
    };
  };

  // Check if form is valid without setting errors (for button state)
  const isFormValid = () => {
    return (
      formData.firstName.trim() &&
      formData.lastName.trim() &&
      formData.email.trim() &&
      formData.gender &&
      formData.phoneNumber.trim() &&
      formData.organization.trim() &&
      formData.jobTitle.trim() &&
      formData.streetAddress.trim() &&
      formData.city.trim() &&
      formData.state &&
      formData.zipCode.trim() &&
      formData.emergencyContactName.trim() &&
      formData.emergencyContactPhone.trim() &&
      formData.emergencyContactRelationship.trim() &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
      (!formData.golfClubRental || formData.golfClubHandedness)
    );
  };

  // Form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.firstName.trim())
      newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.gender) newErrors.gender = 'Gender selection is required';
    if (!formData.phoneNumber.trim())
      newErrors.phoneNumber = 'Phone number is required';
    if (!formData.organization.trim())
      newErrors.organization = 'Organization is required';
    if (!formData.jobTitle.trim()) newErrors.jobTitle = 'Job title is required';
    if (!formData.streetAddress.trim())
      newErrors.streetAddress = 'Street address is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.state) newErrors.state = 'State is required';
    if (!formData.zipCode.trim()) newErrors.zipCode = 'ZIP code is required';
    if (!formData.emergencyContactName.trim())
      newErrors.emergencyContactName = 'Emergency contact name is required';
    if (!formData.emergencyContactPhone.trim())
      newErrors.emergencyContactPhone = 'Emergency contact phone is required';
    if (!formData.emergencyContactRelationship.trim())
      newErrors.emergencyContactRelationship =
        'Emergency contact relationship is required';

    // Email validation
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Golf club handedness validation
    if (formData.golfClubRental && !formData.golfClubHandedness) {
      newErrors.golfClubHandedness = 'Please select golf club handedness';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return; // Prevent double submission

    try {
      setIsSubmitting(true);
      if (!validateForm()) {
        throw new Error(
          'Please correct the errors in the form before submitting.'
        );
      }

      if (!session?.user) {
        throw new Error('You must be logged in to register.');
      }

      try {
        const user = session.user;
        const pricing = calculatePricing();

        // Convert meals object to array format for database
        const selectedMeals = Object.entries(formData.meals)
          .filter(([, selected]) => selected)
          .map(([mealKey]) => mealKey);

        // Prepare registration data
        const registrationData = {
          user_id: user.id,
          registration_type: 'sponsor-attendee',
          attendee_type: 'attendee',
          email: formData.email,
          first_name: formData.firstName,
          last_name: formData.lastName,
          name_on_badge:
            formData.nameOnBadge ||
            `${formData.firstName} ${formData.lastName}`.trim(),
          gender: formData.gender,
          phone_number: formData.phoneNumber,
          street_address: formData.streetAddress,
          city: formData.city,
          state: formData.state,
          zip_code: formData.zipCode,
          organization: formData.organization,
          job_title: formData.jobTitle,
          attending_golf: formData.golfTournament,
          golf_club_rental: formData.golfClubRental,
          golf_club_handedness: formData.golfClubHandedness,
          night_one: formData.nightOne,
          night_two: formData.nightTwo,
          meals: selectedMeals,
          dietary_needs: formData.dietaryRestrictions,
          registration_total: pricing.registrationTotal,
          golf_total: pricing.golfTotal,
          golf_club_rental_total: pricing.golfClubRentalTotal,
          meal_total: pricing.mealTotal,
          grand_total: pricing.grandTotal,
          payment_status: pricing.grandTotal === 0 ? 'completed' : 'pending',
          is_sponsor_attendee: true, // Mark as sponsor attendee
        };

        console.log(
          'Submitting sponsor attendee registration:',
          registrationData
        );

        // Insert registration into database
        const { data, error } = await supabase
          .from('iepa_attendee_registrations')
          .insert([registrationData])
          .select()
          .single();

        if (error) {
          throw new Error(`Registration submission failed: ${error.message}`);
        }

        console.log('Registration submitted successfully:', data);

        // 📧 SEND REGISTRATION CONFIRMATION EMAIL
        try {
          const emailResponse = await fetch('/api/send-registration-email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: formData.email,
              fullName: `${formData.firstName} ${formData.lastName}`.trim(),
              type: 'attendee', // Use 'attendee' type for sponsor attendees
              confirmationNumber: data?.id,
              userId: user.id,
              registrationId: data?.id,
            }),
          });

          if (emailResponse.ok) {
            console.log(
              '[EMAIL-DEBUG] Sponsor attendee registration confirmation email sent to:',
              formData.email
            );
          } else {
            console.warn(
              '[EMAIL-WARNING] Email API returned non-OK status:',
              emailResponse.status
            );
          }
        } catch (emailError) {
          console.error(
            '[EMAIL-ERROR] Failed to send sponsor attendee registration confirmation email:',
            emailError
          );
          // Don't fail the registration if email fails
        }

        // Clear persisted form data after successful submission
        persistence.clearFormData();

        // Redirect to payment if amount > 0
        if (pricing.grandTotal > 0) {
          try {
            // Import Stripe utilities
            const { stripeUtils } = await import('@/lib/stripe-client');

            // Prepare payment data
            const paymentData = {
              registrationId: data.id,
              userId: user.id,
              email: formData.email,
              fullName: `${formData.firstName} ${formData.lastName}`.trim(),
              amount: pricing.grandTotal,
              registrationType: 'sponsor-attendee',
              description: `IEPA ${CONFERENCE_YEAR} Sponsor Attendee Registration - ${formData.firstName} ${formData.lastName}`,
              metadata: {
                registrationId: data.id,
                registrationType: 'sponsor-attendee',
                userId: user.id,
                golfTournament: formData.golfTournament.toString(),
                golfClubRental: formData.golfClubRental.toString(),
              },
            };

            console.log('Initiating payment process...', paymentData);

            // Process payment (creates checkout session and redirects)
            const paymentResult = await stripeUtils.processPayment(paymentData);

            if (!paymentResult.success) {
              console.error('Payment initiation failed:', paymentResult.error);
              setNotification({
                type: 'error',
                title: 'Payment Error',
                message: `Failed to initiate payment: ${paymentResult.error}. Please try again or contact support.`,
              });
            }
            // If successful, user will be redirected to Stripe checkout
          } catch (paymentError) {
            console.error('Payment processing error:', paymentError);
            setNotification({
              type: 'error',
              title: 'Payment Error',
              message:
                'Failed to process payment. Please try again or contact support.',
            });
          }
        } else {
          // For free registrations, redirect to confirmation page
          const confirmationData = {
            registrationId: data.id,
            grandTotal: pricing.grandTotal,
            email: formData.email,
            name: `${formData.firstName} ${formData.lastName}`,
            organization: formData.organization,
            sponsorName: 'Sponsor Organization', // Generic since sponsor-attendees are linked to sponsors
          };

          // Redirect to sponsor-attendee confirmation page
          window.location.href = `/register/sponsor-attendee/confirmation?data=${encodeURIComponent(JSON.stringify(confirmationData))}`;
          return; // Exit early since we're redirecting
        }

        // Reset form state
        setErrors({});

        console.log('Sponsor attendee registration submitted successfully');
        setIsSubmitting(false);
      } catch (error) {
        console.error('Error in nested try block:', error);
        throw error; // Re-throw to be caught by outer catch
      }
    } catch (error) {
      console.error('Sponsor attendee form submission error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'An error occurred';
      showError('Registration Failed', errorMessage);
      setIsSubmitting(false);
    }
  };

  // Show loading state while checking constraints
  if (constraintLoading) {
    return (
      <ProtectedRegistrationPage>
        <div className="iepa-container">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-iepa-primary mx-auto mb-4"></div>
            <p className="iepa-body">Loading registration information...</p>
          </div>
        </div>
      </ProtectedRegistrationPage>
    );
  }

  // Show existing registration notice if user already has a registration
  if (permissions && !permissions.canRegisterAttendee) {
    return (
      <ProtectedRegistrationPage>
        <div className="iepa-container">
          <ExistingRegistrationNotice
            registrationType="sponsor-attendee"
            permissions={permissions}
          />
        </div>
      </ProtectedRegistrationPage>
    );
  }

  return (
    <ProtectedRegistrationPage>
      <div
        id="sponsor-attendee-registration-page"
        className="iepa-container relative"
        data-testid="sponsor-attendee-registration-page"
      >
        {/* Scrollspy Navigation */}
        <ScrollspyNavigation
          sections={FORM_SECTIONS}
          activeSection={activeSection}
          onSectionClick={handleScrollspyClick}
          variant="desktop"
        />

        <ScrollspyNavigation
          sections={FORM_SECTIONS}
          activeSection={activeSection}
          onSectionClick={handleScrollspyClick}
          variant="mobile"
        />

        {/* Main Content with proper spacing for desktop navigation */}
        <div className="lg:ml-72 lg:pl-6">
          {/* Add left margin and padding on desktop to avoid overlap */}

          {/* Form Persistence UI */}
          <RestoreDataPrompt
            show={persistence.showRestorePrompt}
            dataAge={persistence.dataAge}
            onRestore={persistence.restoreData}
            onStartFresh={persistence.startFresh}
            onDismiss={persistence.dismissPrompt}
            formType="sponsor attendee registration"
          />

          <FloatingSaveStatus
            isSaving={persistence.isAutoSaving}
            lastSaved={persistence.lastSavedAt}
            hasUnsavedChanges={false}
          />

          <DataManagementPanel
            onClearData={persistence.clearFormData}
            onExportData={() => {
              const dataStr = JSON.stringify(formData, null, 2);
              const dataBlob = new Blob([dataStr], {
                type: 'application/json',
              });
              const url = URL.createObjectURL(dataBlob);
              const link = document.createElement('a');
              link.href = url;
              link.download = 'sponsor-attendee-form-data.json';
              link.click();
              URL.revokeObjectURL(url);
            }}
            hasStoredData={persistence.hasPersistedData}
          />

          {/* Header Section */}
          <section className="iepa-section text-center">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center justify-center gap-3 mb-4">
                <FaStar className="h-8 w-8 text-iepa-accent" />
                <h1 className="iepa-heading-1">
                  Sponsor Attendee Registration
                </h1>
              </div>
              <p className="iepa-body-large mb-2">
                IEPA {CONFERENCE_YEAR} Annual Meeting
              </p>
              <p className="iepa-body mb-8 max-w-2xl mx-auto">
                Complete your sponsor attendee registration below. As a sponsor
                attendee, your registration fee, lodging, and meals are
                complimentary. You may add optional golf tournament
                participation for an additional fee.
              </p>
            </div>
          </section>

          {/* Registration Form */}
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Sponsor Information Section */}
            <section
              id="sponsor-info"
              className="iepa-section"
              data-testid="sponsor-info-section"
            >
              <Card className="iepa-form-card">
                <CardHeader>
                  <h2 className="iepa-compact-heading-2">
                    Sponsor Attendee Information
                  </h2>
                  <p className="iepa-body-small text-iepa-gray-600">
                    This registration is specifically for sponsor attendees.
                    Your registration type is pre-configured and cannot be
                    changed.
                  </p>
                </CardHeader>
                <CardBody>
                  {/* Registration Type Display (Read-only) */}
                  <div className="iepa-form-field">
                    <Label className="iepa-form-label">Registration Type</Label>
                    <div className="bg-iepa-accent/10 border border-iepa-accent/20 rounded-lg p-4">
                      <div className="flex items-center gap-3">
                        <FaStar className="h-5 w-5 text-iepa-accent" />
                        <div>
                          <div className="font-semibold text-iepa-accent">
                            Sponsor Attendee Registration
                          </div>
                          <div className="text-sm text-iepa-gray-600">
                            Complimentary registration for sponsor company
                            attendees
                          </div>
                        </div>
                      </div>
                    </div>
                    <p className="iepa-form-help-text mt-2">
                      This registration type includes complimentary meals,
                      lodging, and meeting materials. Select your preferences
                      below. Optional golf tournament participation is available
                      for an additional fee.
                    </p>
                  </div>
                </CardBody>
              </Card>
            </section>

            {/* Personal Information Section */}
            <section
              id="personal-information"
              className="iepa-section"
              data-testid="personal-information-section"
            >
              <Card className="iepa-form-card">
                <CardHeader>
                  <h2 className="iepa-compact-heading-2">
                    Personal Information
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* First Name */}
                    <div className="iepa-form-field">
                      <HydrationSafeInput
                        label="First Name"
                        type="text"
                        placeholder="Enter your first name"
                        value={formData.firstName}
                        onChange={e =>
                          handleInputChange('firstName', e.target.value)
                        }
                        isRequired
                        className={
                          errors.firstName ? 'iepa-form-field-error' : ''
                        }
                        aria-describedby={
                          errors.firstName ? 'firstName-error' : undefined
                        }
                      />
                      {errors.firstName && (
                        <div
                          id="firstName-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.firstName}
                        </div>
                      )}
                    </div>

                    {/* Last Name */}
                    <div className="iepa-form-field">
                      <HydrationSafeInput
                        label="Last Name"
                        type="text"
                        placeholder="Enter your last name"
                        value={formData.lastName}
                        onChange={e =>
                          handleInputChange('lastName', e.target.value)
                        }
                        isRequired
                        className={
                          errors.lastName ? 'iepa-form-field-error' : ''
                        }
                        aria-describedby={
                          errors.lastName ? 'lastName-error' : undefined
                        }
                      />
                      {errors.lastName && (
                        <div
                          id="lastName-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.lastName}
                        </div>
                      )}
                    </div>

                    {/* Name on Badge */}
                    <div className="iepa-form-field">
                      <HydrationSafeInput
                        label="Name on Badge"
                        type="text"
                        placeholder="Leave blank to use first and last name"
                        value={formData.nameOnBadge}
                        onChange={e =>
                          handleInputChange('nameOnBadge', e.target.value)
                        }
                        className={
                          errors.nameOnBadge ? 'iepa-form-field-error' : ''
                        }
                        aria-describedby={
                          errors.nameOnBadge ? 'nameOnBadge-error' : undefined
                        }
                      />
                      {errors.nameOnBadge && (
                        <div
                          id="nameOnBadge-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.nameOnBadge}
                        </div>
                      )}
                      <p className="iepa-form-help-text">
                        Optional: Customize how your name appears on your
                        conference badge
                      </p>
                    </div>

                    {/* Gender */}
                    <div className="iepa-form-field">
                      <Label className="iepa-form-label">
                        Gender <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={formData.gender}
                        onValueChange={value =>
                          handleInputChange('gender', value)
                        }
                      >
                        <SelectTrigger
                          className={cn(
                            'iepa-form-input',
                            errors.gender && 'iepa-form-field-error'
                          )}
                          aria-describedby={
                            errors.gender ? 'gender-error' : undefined
                          }
                        >
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">Male</SelectItem>
                          <SelectItem value="female">Female</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.gender && (
                        <div
                          id="gender-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.gender}
                        </div>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
            </section>

            {/* Contact Information Section */}
            <section
              id="contact-information"
              className="iepa-section"
              data-testid="contact-information-section"
            >
              <Card className="iepa-form-card">
                <CardHeader>
                  <h2 className="iepa-compact-heading-2">
                    Contact Information
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="space-y-6">
                    {/* Email Address */}
                    <div className="iepa-form-field">
                      <HydrationSafeInput
                        label="Email Address"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={e =>
                          handleInputChange('email', e.target.value)
                        }
                        isRequired
                        className={errors.email ? 'iepa-form-field-error' : ''}
                        aria-describedby={
                          errors.email ? 'email-error' : undefined
                        }
                      />
                      {errors.email && (
                        <div
                          id="email-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.email}
                        </div>
                      )}
                    </div>

                    {/* Phone Number */}
                    <div className="iepa-form-field">
                      <HydrationSafePhoneInput
                        label="Phone Number"
                        placeholder="(*************"
                        value={formData.phoneNumber}
                        onChange={value =>
                          handleInputChange('phoneNumber', value)
                        }
                        isRequired
                        className={
                          errors.phoneNumber ? 'iepa-form-field-error' : ''
                        }
                        aria-describedby={
                          errors.phoneNumber ? 'phoneNumber-error' : undefined
                        }
                      />
                      {errors.phoneNumber && (
                        <div
                          id="phoneNumber-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.phoneNumber}
                        </div>
                      )}
                    </div>

                    {/* Organization */}
                    <div className="iepa-form-field">
                      <OrganizationCombobox
                        label="Organization"
                        placeholder="Enter or select your organization"
                        value={formData.organization}
                        onChange={value =>
                          handleInputChange('organization', value)
                        }
                        isRequired
                        className={
                          errors.organization ? 'iepa-form-field-error' : ''
                        }
                        aria-describedby={
                          errors.organization ? 'organization-error' : undefined
                        }
                      />
                      {errors.organization && (
                        <div
                          id="organization-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.organization}
                        </div>
                      )}
                    </div>

                    {/* Job Title */}
                    <div className="iepa-form-field">
                      <HydrationSafeInput
                        label="Job Title"
                        type="text"
                        placeholder="Enter your job title"
                        value={formData.jobTitle}
                        onChange={e =>
                          handleInputChange('jobTitle', e.target.value)
                        }
                        isRequired
                        className={
                          errors.jobTitle ? 'iepa-form-field-error' : ''
                        }
                        aria-describedby={
                          errors.jobTitle ? 'jobTitle-error' : undefined
                        }
                      />
                      {errors.jobTitle && (
                        <div
                          id="jobTitle-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.jobTitle}
                        </div>
                      )}
                    </div>

                    {/* Address Fields */}
                    <div className="grid md:grid-cols-2 gap-6">
                      {/* Street Address */}
                      <div className="iepa-form-field md:col-span-2">
                        <HydrationSafeInput
                          label="Street Address"
                          type="text"
                          placeholder="Enter your street address"
                          value={formData.streetAddress}
                          onChange={e =>
                            handleInputChange('streetAddress', e.target.value)
                          }
                          isRequired
                          className={
                            errors.streetAddress ? 'iepa-form-field-error' : ''
                          }
                          aria-describedby={
                            errors.streetAddress
                              ? 'streetAddress-error'
                              : undefined
                          }
                        />
                        {errors.streetAddress && (
                          <div
                            id="streetAddress-error"
                            className="iepa-error-message"
                            role="alert"
                          >
                            {errors.streetAddress}
                          </div>
                        )}
                      </div>

                      {/* City */}
                      <div className="iepa-form-field">
                        <HydrationSafeInput
                          label="City"
                          type="text"
                          placeholder="Enter your city"
                          value={formData.city}
                          onChange={e =>
                            handleInputChange('city', e.target.value)
                          }
                          isRequired
                          className={errors.city ? 'iepa-form-field-error' : ''}
                          aria-describedby={
                            errors.city ? 'city-error' : undefined
                          }
                        />
                        {errors.city && (
                          <div
                            id="city-error"
                            className="iepa-error-message"
                            role="alert"
                          >
                            {errors.city}
                          </div>
                        )}
                      </div>

                      {/* State */}
                      <div className="iepa-form-field">
                        <Label className="iepa-form-label">
                          State <span className="text-red-500">*</span>
                        </Label>
                        <Select
                          value={formData.state}
                          onValueChange={value =>
                            handleInputChange('state', value)
                          }
                        >
                          <SelectTrigger
                            className={cn(
                              'iepa-form-input',
                              errors.state && 'iepa-form-field-error'
                            )}
                            aria-describedby={
                              errors.state ? 'state-error' : undefined
                            }
                          >
                            <SelectValue placeholder="Select state" />
                          </SelectTrigger>
                          <SelectContent>
                            {STATE_PROVINCE_OPTIONS.map(option => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.state && (
                          <div
                            id="state-error"
                            className="iepa-error-message"
                            role="alert"
                          >
                            {errors.state}
                          </div>
                        )}
                      </div>

                      {/* ZIP Code */}
                      <div className="iepa-form-field">
                        <HydrationSafeInput
                          label="ZIP Code"
                          type="text"
                          placeholder="Enter your ZIP code"
                          value={formData.zipCode}
                          onChange={e =>
                            handleInputChange('zipCode', e.target.value)
                          }
                          isRequired
                          className={
                            errors.zipCode ? 'iepa-form-field-error' : ''
                          }
                          aria-describedby={
                            errors.zipCode ? 'zipCode-error' : undefined
                          }
                        />
                        {errors.zipCode && (
                          <div
                            id="zipCode-error"
                            className="iepa-error-message"
                            role="alert"
                          >
                            {errors.zipCode}
                          </div>
                        )}
                      </div>

                      {/* Country */}
                      <div className="iepa-form-field">
                        <HydrationSafeInput
                          label="Country"
                          type="text"
                          placeholder="Enter your country"
                          value={formData.country}
                          onChange={e =>
                            handleInputChange('country', e.target.value)
                          }
                          isRequired
                          className={
                            errors.country ? 'iepa-form-field-error' : ''
                          }
                          aria-describedby={
                            errors.country ? 'country-error' : undefined
                          }
                        />
                        {errors.country && (
                          <div
                            id="country-error"
                            className="iepa-error-message"
                            role="alert"
                          >
                            {errors.country}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </section>

            {/* Event Options Section */}
            <section
              id="event-options"
              className="iepa-section"
              data-testid="event-options-section"
            >
              <Card className="iepa-form-card">
                <CardHeader>
                  <h2 className="iepa-compact-heading-2">Event Options</h2>
                  <p className="iepa-body-small text-iepa-gray-600">
                    Select your conference preferences and optional add-ons.
                    Meals and lodging are complimentary for sponsor attendees.
                  </p>
                </CardHeader>
                <CardBody>
                  <div className="space-y-6">
                    {/* Golf Tournament */}
                    <div className="iepa-form-field">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="golfTournament"
                          checked={formData.golfTournament}
                          onCheckedChange={checked =>
                            handleInputChange('golfTournament', checked)
                          }
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <Label
                            htmlFor="golfTournament"
                            className="iepa-form-label cursor-pointer"
                          >
                            Golf Tournament Participation ($200)
                          </Label>
                          <p className="iepa-form-help-text">
                            Join the annual golf tournament. Fee includes green
                            fees, cart, and tournament prizes.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Golf Club Rental - Only show if golf tournament is selected */}
                    {formData.golfTournament && (
                      <div className="iepa-form-field ml-6 border-l-2 border-iepa-gray-200 pl-4">
                        <div className="flex items-start space-x-3">
                          <Checkbox
                            id="golfClubRental"
                            checked={formData.golfClubRental}
                            onCheckedChange={checked =>
                              handleInputChange('golfClubRental', checked)
                            }
                            className="mt-1"
                          />
                          <div className="flex-1">
                            <Label
                              htmlFor="golfClubRental"
                              className="iepa-form-label cursor-pointer"
                            >
                              Golf Club Rental ($75)
                            </Label>
                            <p className="iepa-form-help-text">
                              Rent a complete set of golf clubs for the
                              tournament.
                            </p>
                          </div>
                        </div>

                        {/* Golf Club Handedness - Only show if club rental is selected */}
                        {formData.golfClubRental && (
                          <div className="mt-4">
                            <Label className="iepa-form-label">
                              Golf Club Handedness{' '}
                              <span className="text-red-500">*</span>
                            </Label>
                            <Select
                              value={formData.golfClubHandedness}
                              onValueChange={value =>
                                handleInputChange('golfClubHandedness', value)
                              }
                            >
                              <SelectTrigger
                                className={cn(
                                  'iepa-form-input',
                                  errors.golfClubHandedness &&
                                    'iepa-form-field-error'
                                )}
                                aria-describedby={
                                  errors.golfClubHandedness
                                    ? 'golfClubHandedness-error'
                                    : undefined
                                }
                              >
                                <SelectValue placeholder="Select handedness" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="right">
                                  Right-handed
                                </SelectItem>
                                <SelectItem value="left">
                                  Left-handed
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            {errors.golfClubHandedness && (
                              <div
                                id="golfClubHandedness-error"
                                className="iepa-error-message"
                                role="alert"
                              >
                                {errors.golfClubHandedness}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Lodging Selection */}
                    <div className="iepa-form-field">
                      <Label className="iepa-form-label">
                        Lodging Accommodation
                      </Label>
                      <p className="iepa-body-small text-iepa-gray-600 mb-4">
                        Select which nights you would like lodging. Lodging is
                        complimentary for sponsor attendees.
                      </p>
                      <div className="space-y-3">
                        <div className="flex items-start space-x-3">
                          <Checkbox
                            id="nightOne"
                            checked={formData.nightOne}
                            onCheckedChange={checked =>
                              handleInputChange('nightOne', checked)
                            }
                            className="mt-1"
                          />
                          <div className="flex-1">
                            <Label
                              htmlFor="nightOne"
                              className="iepa-form-label cursor-pointer"
                            >
                              Night One (Monday, September 15, 2025)
                            </Label>
                            <p className="iepa-form-help-text">
                              Arrival day and welcome dinner night
                            </p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <Checkbox
                            id="nightTwo"
                            checked={formData.nightTwo}
                            onCheckedChange={checked =>
                              handleInputChange('nightTwo', checked)
                            }
                            className="mt-1"
                          />
                          <div className="flex-1">
                            <Label
                              htmlFor="nightTwo"
                              className="iepa-form-label cursor-pointer"
                            >
                              Night Two (Tuesday, September 16, 2025)
                            </Label>
                            <p className="iepa-form-help-text">
                              Main conference day and networking dinner night
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Meal Selection */}
                    <div className="iepa-form-field">
                      <Label className="iepa-form-label">Meal Selection</Label>
                      <p className="iepa-body-small text-iepa-gray-600 mb-4">
                        Select which meals you would like to attend. All meals
                        are complimentary for sponsor attendees.
                      </p>
                      <div className="space-y-4">
                        {mealOptions.map(dayOption => (
                          <div
                            key={dayOption.date}
                            className="border border-iepa-gray-200 rounded-lg p-4"
                          >
                            <h4 className="font-semibold text-base mb-3 text-iepa-primary">
                              {dayOption.day}, {dayOption.date}
                            </h4>
                            <div className="space-y-3">
                              {dayOption.meals.map(meal => (
                                <div
                                  key={meal.key}
                                  className="flex items-start space-x-3"
                                >
                                  <Checkbox
                                    id={`meal-${meal.key}`}
                                    checked={
                                      formData.meals[
                                        meal.key as keyof typeof formData.meals
                                      ]
                                    }
                                    onCheckedChange={checked =>
                                      handleMealChange(meal.key, checked)
                                    }
                                    className="mt-1"
                                  />
                                  <div className="flex-1">
                                    <Label
                                      htmlFor={`meal-${meal.key}`}
                                      className="iepa-form-label cursor-pointer"
                                    >
                                      {meal.name} - {meal.time}
                                    </Label>
                                    <p className="iepa-form-help-text">
                                      {meal.description}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Dietary Restrictions */}
                    <div className="iepa-form-field">
                      <Label
                        htmlFor="dietaryRestrictions"
                        className="iepa-form-label"
                      >
                        Dietary Restrictions or Special Needs
                      </Label>
                      <textarea
                        id="dietaryRestrictions"
                        placeholder="Please describe any dietary restrictions, allergies, or special accommodations needed..."
                        value={formData.dietaryRestrictions}
                        onChange={e =>
                          handleInputChange(
                            'dietaryRestrictions',
                            e.target.value
                          )
                        }
                        className="iepa-form-input min-h-[100px] resize-y"
                        rows={4}
                      />
                      <p className="iepa-form-help-text">
                        Let us know about any dietary restrictions, food
                        allergies, or accessibility needs.
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </section>

            {/* Emergency Contact Section */}
            <section
              id="emergency-contact"
              className="iepa-section"
              data-testid="emergency-contact-section"
            >
              <Card className="iepa-form-card">
                <CardHeader>
                  <h2 className="iepa-compact-heading-2">Emergency Contact</h2>
                  <p className="iepa-body-small text-iepa-gray-600">
                    Please provide emergency contact information for use during
                    the conference.
                  </p>
                </CardHeader>
                <CardBody>
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Emergency Contact Name */}
                    <div className="iepa-form-field">
                      <HydrationSafeInput
                        label="Emergency Contact Name"
                        type="text"
                        placeholder="Enter emergency contact name"
                        value={formData.emergencyContactName}
                        onChange={e =>
                          handleInputChange(
                            'emergencyContactName',
                            e.target.value
                          )
                        }
                        isRequired
                        className={
                          errors.emergencyContactName
                            ? 'iepa-form-field-error'
                            : ''
                        }
                        aria-describedby={
                          errors.emergencyContactName
                            ? 'emergencyContactName-error'
                            : undefined
                        }
                      />
                      {errors.emergencyContactName && (
                        <div
                          id="emergencyContactName-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.emergencyContactName}
                        </div>
                      )}
                    </div>

                    {/* Emergency Contact Phone */}
                    <div className="iepa-form-field">
                      <HydrationSafePhoneInput
                        label="Emergency Contact Phone"
                        placeholder="(*************"
                        value={formData.emergencyContactPhone}
                        onChange={value =>
                          handleInputChange('emergencyContactPhone', value)
                        }
                        isRequired
                        className={
                          errors.emergencyContactPhone
                            ? 'iepa-form-field-error'
                            : ''
                        }
                        aria-describedby={
                          errors.emergencyContactPhone
                            ? 'emergencyContactPhone-error'
                            : undefined
                        }
                      />
                      {errors.emergencyContactPhone && (
                        <div
                          id="emergencyContactPhone-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.emergencyContactPhone}
                        </div>
                      )}
                    </div>

                    {/* Emergency Contact Relationship */}
                    <div className="iepa-form-field md:col-span-2">
                      <HydrationSafeInput
                        label="Relationship to Emergency Contact"
                        type="text"
                        placeholder="e.g., Spouse, Parent, Sibling, Friend"
                        value={formData.emergencyContactRelationship}
                        onChange={e =>
                          handleInputChange(
                            'emergencyContactRelationship',
                            e.target.value
                          )
                        }
                        isRequired
                        className={
                          errors.emergencyContactRelationship
                            ? 'iepa-form-field-error'
                            : ''
                        }
                        aria-describedby={
                          errors.emergencyContactRelationship
                            ? 'emergencyContactRelationship-error'
                            : undefined
                        }
                      />
                      {errors.emergencyContactRelationship && (
                        <div
                          id="emergencyContactRelationship-error"
                          className="iepa-error-message"
                          role="alert"
                        >
                          {errors.emergencyContactRelationship}
                        </div>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
            </section>

            {/* Review & Payment Section */}
            <section
              id="review-payment"
              className="iepa-section"
              data-testid="review-payment-section"
            >
              <Card className="iepa-form-card">
                <CardHeader>
                  <h2 className="iepa-compact-heading-2">Review & Payment</h2>
                </CardHeader>
                <CardBody>
                  {/* Registration Summary */}
                  <div className="space-y-6">
                    {/* Pricing Breakdown */}
                    <div className="bg-iepa-gray-50 border border-iepa-gray-200 rounded-lg p-6">
                      <h3 className="iepa-heading-3 mb-4">
                        Registration Summary
                      </h3>

                      <div className="space-y-3">
                        {/* Registration Fee */}
                        <div className="flex justify-between items-center">
                          <span className="iepa-body">
                            Sponsor Attendee Registration
                          </span>
                          <span className="iepa-body font-semibold text-iepa-accent">
                            FREE
                          </span>
                        </div>

                        {/* Lodging */}
                        {(formData.nightOne || formData.nightTwo) && (
                          <div className="flex justify-between items-center">
                            <span className="iepa-body">
                              Lodging (
                              {[
                                formData.nightOne && 'Sep 15',
                                formData.nightTwo && 'Sep 16',
                              ]
                                .filter(Boolean)
                                .join(', ')}
                              )
                            </span>
                            <span className="iepa-body font-semibold text-iepa-accent">
                              INCLUDED
                            </span>
                          </div>
                        )}

                        {/* Meals */}
                        {Object.values(formData.meals).some(Boolean) && (
                          <div className="flex justify-between items-center">
                            <span className="iepa-body">
                              Selected Meals (
                              {Object.entries(formData.meals)
                                .filter(([, selected]) => selected)
                                .map(([key]) => {
                                  const mealMap: Record<string, string> = {
                                    sept15Dinner: 'Sep 15 Dinner',
                                    sept16Breakfast: 'Sep 16 Breakfast',
                                    sept16Lunch: 'Sep 16 Lunch',
                                    sept16Dinner: 'Sep 16 Dinner',
                                    sept17Breakfast: 'Sep 17 Breakfast',
                                    sept17Lunch: 'Sep 17 Lunch',
                                  };
                                  return mealMap[key] || key;
                                })
                                .join(', ')}
                              )
                            </span>
                            <span className="iepa-body font-semibold text-iepa-accent">
                              INCLUDED
                            </span>
                          </div>
                        )}

                        {/* Golf Tournament */}
                        {formData.golfTournament && (
                          <div className="flex justify-between items-center">
                            <span className="iepa-body">Golf Tournament</span>
                            <span className="iepa-body font-semibold">
                              $200.00
                            </span>
                          </div>
                        )}

                        {/* Golf Club Rental */}
                        {formData.golfClubRental && (
                          <div className="flex justify-between items-center">
                            <span className="iepa-body">Golf Club Rental</span>
                            <span className="iepa-body font-semibold">
                              $75.00
                            </span>
                          </div>
                        )}

                        {/* Total */}
                        <div className="border-t border-iepa-gray-300 pt-3 mt-3">
                          <div className="flex justify-between items-center">
                            <span className="iepa-body font-bold text-lg">
                              Total Amount
                            </span>
                            <span className="iepa-body font-bold text-lg text-iepa-primary">
                              ${calculatePricing().grandTotal.toFixed(2)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Payment Information */}
                    {calculatePricing().grandTotal > 0 ? (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-semibold text-blue-900 mb-2">
                          Payment Information
                        </h4>
                        <p className="text-blue-800 text-sm">
                          After submitting your registration, you will be
                          redirected to our secure payment processor to complete
                          your payment for the optional golf fees.
                        </p>
                      </div>
                    ) : (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 className="font-semibold text-green-900 mb-2">
                          No Payment Required
                        </h4>
                        <p className="text-green-800 text-sm">
                          Your sponsor attendee registration is complimentary.
                          You will receive a confirmation email after submitting
                          this form.
                        </p>
                      </div>
                    )}

                    {/* Cancellation Policy */}
                    <CancellationPolicy />

                    {/* Submit Button */}
                    <div className="flex justify-center pt-6">
                      <Button
                        type="submit"
                        disabled={!isFormValid() || isSubmitting}
                        className={`px-8 py-3 text-lg font-semibold transition-colors ${
                          !isFormValid() || isSubmitting
                            ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700 text-white cursor-pointer'
                        }`}
                        data-testid="submit-registration-button"
                      >
                        {isSubmitting
                          ? 'Submitting Registration...'
                          : calculatePricing().grandTotal > 0
                            ? 'Submit Registration & Proceed to Payment'
                            : 'Submit Registration'}
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </section>
          </form>

          {/* Success/Error Notification */}
          {notification && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 max-w-md mx-4">
                <div className="flex items-center gap-3 mb-4">
                  {notification.type === 'success' ? (
                    <FaCheckCircle className="h-6 w-6 text-green-500" />
                  ) : (
                    <FaExclamationTriangle className="h-6 w-6 text-red-500" />
                  )}
                  <h3 className="font-bold text-lg">{notification.title}</h3>
                </div>
                <p className="text-gray-700 mb-4">{notification.message}</p>
                {notification.registrationId && (
                  <p className="text-sm text-gray-600 mb-4">
                    Registration ID: {notification.registrationId}
                  </p>
                )}
                <div className="flex justify-end">
                  <Button
                    onClick={() => setNotification(null)}
                    variant="bordered"
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
        {/* End of main content wrapper */}
      </div>
    </ProtectedRegistrationPage>
  );
}
