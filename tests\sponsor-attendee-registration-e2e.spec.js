const { test, expect } = require('@playwright/test');

/**
 * Sponsor Attendee Registration Complete E2E Test
 *
 * This test simulates a complete sponsor attendee registration flow including:
 * 1. Sponsor attendee registration form
 * 2. Email domain validation or coupon code entry
 * 3. Automatic 100% discount application
 * 4. Golf tournament selection (charges still apply)
 * 5. $0 base registration with golf charges only
 * 6. Email verification and registration confirmation
 */
const { generateTestEmail, waitForEmail, cleanupTestEmails } = require('./utils/email-testing');

test.describe('Sponsor Attendee Registration E2E Tests', () => {
  let testEmail;
  let testData;

  test.beforeEach(async ({ page }) => {
    // Generate unique test email for each test
    testEmail = generateTestEmail('sponsor-attendee');
    
    // Test data for sponsor attendee registration
    testData = {
      firstName: 'John',
      lastName: 'Sponsor',
      email: testEmail,
      gender: 'male',
      phoneNumber: '(*************',
      organization: 'Test Sponsor Company',
      jobTitle: 'Energy Director',
      streetAddress: '123 Sponsor Street',
      city: 'Sacramento',
      state: 'California',
      zipCode: '95814',
      country: 'United States',
      emergencyContactName: '<PERSON> Sponsor',
      emergencyContactPhone: '(*************',
      emergencyContactRelationship: 'Spouse',
      dietaryRestrictions: 'No nuts please'
    };

    // Navigate to sponsor attendee registration page
    await page.goto('/register/sponsor-attendee');
    
    // Wait for page to load
    await expect(page.locator('[data-testid="sponsor-attendee-registration-page"]')).toBeVisible();
  });

  test.afterEach(async () => {
    // Clean up test emails
    await cleanupTestEmails(testEmail);
  });

  test('should display sponsor attendee registration form with correct branding', async ({ page }) => {
    // Check page title and branding
    await expect(page.locator('h1')).toContainText('Sponsor Attendee Registration');
    await expect(page.locator('text=IEPA 2025 Annual Meeting')).toBeVisible();
    
    // Check sponsor attendee benefits section
    await expect(page.locator('text=Sponsor Attendee Benefits Included')).toBeVisible();
    await expect(page.locator('text=Two nights lodging')).toBeVisible();
    await expect(page.locator('text=All meals included')).toBeVisible();
    await expect(page.locator('text=No registration fee')).toBeVisible();
    
    // Check that registration type is pre-configured
    await expect(page.locator('text=Sponsor Attendee Registration')).toBeVisible();
    await expect(page.locator('text=Complimentary registration for sponsor company attendees')).toBeVisible();
  });

  test('should complete sponsor attendee registration without golf (free registration)', async ({ page }) => {
    // Fill out personal information
    await page.fill('input[placeholder="Enter your first name"]', testData.firstName);
    await page.fill('input[placeholder="Enter your last name"]', testData.lastName);
    await page.selectOption('select[aria-describedby*="gender"]', testData.gender);
    
    // Fill out contact information
    await page.fill('input[type="email"]', testData.email);
    await page.fill('input[placeholder="(*************"]', testData.phoneNumber);
    await page.fill('input[placeholder="Enter or select your organization"]', testData.organization);
    await page.fill('input[placeholder="Enter your job title"]', testData.jobTitle);
    
    // Fill out address
    await page.fill('input[placeholder="Enter your street address"]', testData.streetAddress);
    await page.fill('input[placeholder="Enter your city"]', testData.city);
    await page.selectOption('select[aria-describedby*="state"]', testData.state);
    await page.fill('input[placeholder="Enter your ZIP code"]', testData.zipCode);
    await page.fill('input[placeholder="Enter your country"]', testData.country);
    
    // Verify lodging and meals are included (not selectable)
    await expect(page.locator('text=Two Nights Lodging Included')).toBeVisible();
    await expect(page.locator('text=All Meals Included')).toBeVisible();
    
    // Fill dietary restrictions
    await page.fill('textarea[placeholder*="dietary restrictions"]', testData.dietaryRestrictions);
    
    // Fill emergency contact
    await page.fill('input[placeholder="Enter emergency contact name"]', testData.emergencyContactName);
    await page.fill('input[placeholder="(*************"]', testData.emergencyContactPhone);
    await page.fill('input[placeholder*="Spouse, Parent"]', testData.emergencyContactRelationship);
    
    // Verify pricing shows $0.00 total (no golf selected)
    await expect(page.locator('text=FREE')).toBeVisible();
    await expect(page.locator('text=INCLUDED')).toBeVisible();
    await expect(page.locator('text=$0.00')).toBeVisible();
    
    // Submit registration
    await page.click('[data-testid="submit-registration-button"]');
    
    // Wait for success notification
    await expect(page.locator('text=Sponsor Attendee Registration Submitted Successfully!')).toBeVisible();
    await expect(page.locator('text=Your registration is complete!')).toBeVisible();
    
    // Verify email was sent
    const emailReceived = await waitForEmail(testEmail, 'IEPA 2025 Annual Meeting Registration Confirmation');
    expect(emailReceived).toBe(true);
  });

  test('should complete sponsor attendee registration with golf tournament', async ({ page }) => {
    // Fill out required personal information (abbreviated for golf test)
    await page.fill('input[placeholder="Enter your first name"]', testData.firstName);
    await page.fill('input[placeholder="Enter your last name"]', testData.lastName);
    await page.selectOption('select[aria-describedby*="gender"]', testData.gender);
    await page.fill('input[type="email"]', testData.email);
    await page.fill('input[placeholder="(*************"]', testData.phoneNumber);
    await page.fill('input[placeholder="Enter or select your organization"]', testData.organization);
    await page.fill('input[placeholder="Enter your job title"]', testData.jobTitle);
    await page.fill('input[placeholder="Enter your street address"]', testData.streetAddress);
    await page.fill('input[placeholder="Enter your city"]', testData.city);
    await page.selectOption('select[aria-describedby*="state"]', testData.state);
    await page.fill('input[placeholder="Enter your ZIP code"]', testData.zipCode);
    await page.fill('input[placeholder="Enter your country"]', testData.country);
    
    // Select golf tournament
    await page.check('#golfTournament');
    
    // Verify golf club rental option appears
    await expect(page.locator('text=Golf Club Rental ($75)')).toBeVisible();
    
    // Select golf club rental
    await page.check('#golfClubRental');
    
    // Select golf club handedness
    await page.selectOption('select[aria-describedby*="golfClubHandedness"]', 'right');
    
    // Fill emergency contact
    await page.fill('input[placeholder="Enter emergency contact name"]', testData.emergencyContactName);
    await page.fill('input[placeholder="(*************"]', testData.emergencyContactPhone);
    await page.fill('input[placeholder*="Spouse, Parent"]', testData.emergencyContactRelationship);
    
    // Verify pricing shows golf fees
    await expect(page.locator('text=Golf Tournament')).toBeVisible();
    await expect(page.locator('text=$200.00')).toBeVisible();
    await expect(page.locator('text=Golf Club Rental')).toBeVisible();
    await expect(page.locator('text=$75.00')).toBeVisible();
    await expect(page.locator('text=$275.00')).toBeVisible(); // Total
    
    // Submit registration
    await page.click('[data-testid="submit-registration-button"]');
    
    // Wait for success notification with payment redirect message
    await expect(page.locator('text=Sponsor Attendee Registration Submitted Successfully!')).toBeVisible();
    await expect(page.locator('text=Please proceed to payment')).toBeVisible();
    
    // Note: In a real test, we would verify Stripe redirect, but for now we just check the notification
  });

  test('should validate required fields', async ({ page }) => {
    // Try to submit without filling required fields
    await page.click('[data-testid="submit-registration-button"]');
    
    // Check for validation errors
    await expect(page.locator('text=First name is required')).toBeVisible();
    await expect(page.locator('text=Last name is required')).toBeVisible();
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Gender selection is required')).toBeVisible();
    await expect(page.locator('text=Phone number is required')).toBeVisible();
    await expect(page.locator('text=Organization is required')).toBeVisible();
    await expect(page.locator('text=Job title is required')).toBeVisible();
    await expect(page.locator('text=Emergency contact name is required')).toBeVisible();
  });

  test('should validate golf club handedness when club rental is selected', async ({ page }) => {
    // Fill minimum required fields
    await page.fill('input[placeholder="Enter your first name"]', testData.firstName);
    await page.fill('input[placeholder="Enter your last name"]', testData.lastName);
    await page.selectOption('select[aria-describedby*="gender"]', testData.gender);
    await page.fill('input[type="email"]', testData.email);
    await page.fill('input[placeholder="(*************"]', testData.phoneNumber);
    await page.fill('input[placeholder="Enter or select your organization"]', testData.organization);
    await page.fill('input[placeholder="Enter your job title"]', testData.jobTitle);
    await page.fill('input[placeholder="Enter your street address"]', testData.streetAddress);
    await page.fill('input[placeholder="Enter your city"]', testData.city);
    await page.selectOption('select[aria-describedby*="state"]', testData.state);
    await page.fill('input[placeholder="Enter your ZIP code"]', testData.zipCode);
    await page.fill('input[placeholder="Enter your country"]', testData.country);
    await page.fill('input[placeholder="Enter emergency contact name"]', testData.emergencyContactName);
    await page.fill('input[placeholder="(*************"]', testData.emergencyContactPhone);
    await page.fill('input[placeholder*="Spouse, Parent"]', testData.emergencyContactRelationship);
    
    // Select golf tournament and club rental but not handedness
    await page.check('#golfTournament');
    await page.check('#golfClubRental');
    
    // Try to submit without selecting handedness
    await page.click('[data-testid="submit-registration-button"]');
    
    // Check for golf club handedness validation error
    await expect(page.locator('text=Please select golf club handedness')).toBeVisible();
  });

  test('should show/hide golf club rental based on golf tournament selection', async ({ page }) => {
    // Initially, golf club rental should not be visible
    await expect(page.locator('text=Golf Club Rental ($75)')).not.toBeVisible();
    
    // Select golf tournament
    await page.check('#golfTournament');
    
    // Golf club rental should now be visible
    await expect(page.locator('text=Golf Club Rental ($75)')).toBeVisible();
    
    // Uncheck golf tournament
    await page.uncheck('#golfTournament');
    
    // Golf club rental should be hidden again
    await expect(page.locator('text=Golf Club Rental ($75)')).not.toBeVisible();
  });

  test('should show/hide golf club handedness based on club rental selection', async ({ page }) => {
    // Select golf tournament first
    await page.check('#golfTournament');
    
    // Initially, handedness should not be visible
    await expect(page.locator('text=Golf Club Handedness')).not.toBeVisible();
    
    // Select golf club rental
    await page.check('#golfClubRental');
    
    // Handedness should now be visible
    await expect(page.locator('text=Golf Club Handedness')).toBeVisible();
    
    // Uncheck golf club rental
    await page.uncheck('#golfClubRental');
    
    // Handedness should be hidden again
    await expect(page.locator('text=Golf Club Handedness')).not.toBeVisible();
  });

  test('should display correct pricing calculations', async ({ page }) => {
    // Initially should show $0.00 total
    await expect(page.locator('text=$0.00')).toBeVisible();
    
    // Add golf tournament
    await page.check('#golfTournament');
    await expect(page.locator('text=$200.00')).toBeVisible();
    
    // Add golf club rental
    await page.check('#golfClubRental');
    await expect(page.locator('text=$75.00')).toBeVisible();
    await expect(page.locator('text=$275.00')).toBeVisible(); // Total
    
    // Remove golf club rental
    await page.uncheck('#golfClubRental');
    await expect(page.locator('text=$200.00')).toBeVisible(); // Only golf tournament
    
    // Remove golf tournament
    await page.uncheck('#golfTournament');
    await expect(page.locator('text=$0.00')).toBeVisible(); // Back to free
  });

  test('should have proper mobile responsiveness', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that form is still usable on mobile
    await expect(page.locator('[data-testid="sponsor-attendee-registration-page"]')).toBeVisible();
    await expect(page.locator('h1')).toBeVisible();
    
    // Check that form fields are properly sized
    const firstNameInput = page.locator('input[placeholder="Enter your first name"]');
    await expect(firstNameInput).toBeVisible();
    
    // Check that benefits section is readable on mobile
    await expect(page.locator('text=Sponsor Attendee Benefits Included')).toBeVisible();
  });

  test('should persist form data in localStorage', async ({ page }) => {
    // Fill some form data
    await page.fill('input[placeholder="Enter your first name"]', testData.firstName);
    await page.fill('input[placeholder="Enter your last name"]', testData.lastName);
    await page.fill('input[type="email"]', testData.email);
    
    // Refresh the page
    await page.reload();
    
    // Check if restore data prompt appears
    await expect(page.locator('text=Restore Previous Data')).toBeVisible();
    
    // Click restore
    await page.click('text=Restore Data');
    
    // Verify data was restored
    await expect(page.locator('input[placeholder="Enter your first name"]')).toHaveValue(testData.firstName);
    await expect(page.locator('input[placeholder="Enter your last name"]')).toHaveValue(testData.lastName);
    await expect(page.locator('input[type="email"]')).toHaveValue(testData.email);
  });
});
