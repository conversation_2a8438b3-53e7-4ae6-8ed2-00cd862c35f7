import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Create Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export interface EmailTemplate {
  id: string;
  template_key: string;
  template_name: string;
  description: string | null;
  subject_template: string;
  html_template: string;
  text_template: string | null;
  variables: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EmailTemplateUpdate {
  template_name?: string;
  description?: string;
  subject_template?: string;
  html_template?: string;
  text_template?: string;
  variables?: string[];
  is_active?: boolean;
}

export interface EmailTemplateCreate {
  template_key: string;
  template_name: string;
  description?: string;
  subject_template: string;
  html_template: string;
  text_template?: string;
  variables?: string[];
  is_active?: boolean;
}

class EmailTemplateService {
  /**
   * Get all email templates
   */
  async getAllTemplates(): Promise<EmailTemplate[]> {
    try {
      console.log('[EMAIL-TEMPLATES] Fetching all email templates...');

      const { data, error } = await supabase
        .from('iepa_email_templates')
        .select('*')
        .order('template_name');

      if (error) {
        console.error('[EMAIL-TEMPLATES] Error fetching templates:', error);
        throw new Error(`Failed to fetch email templates: ${error.message}`);
      }

      console.log(`[EMAIL-TEMPLATES] Retrieved ${data?.length || 0} templates`);
      return data || [];
    } catch (error: unknown) {
      console.error('[EMAIL-TEMPLATES] Service error:', error);
      throw error;
    }
  }

  /**
   * Get active email templates only
   */
  async getActiveTemplates(): Promise<EmailTemplate[]> {
    try {
      console.log('[EMAIL-TEMPLATES] Fetching active email templates...');

      const { data, error } = await supabase
        .from('iepa_email_templates')
        .select('*')
        .eq('is_active', true)
        .order('template_name');

      if (error) {
        console.error('[EMAIL-TEMPLATES] Error fetching active templates:', error);
        throw new Error(`Failed to fetch active email templates: ${error.message}`);
      }

      console.log(`[EMAIL-TEMPLATES] Retrieved ${data?.length || 0} active templates`);
      return data || [];
    } catch (error: unknown) {
      console.error('[EMAIL-TEMPLATES] Service error:', error);
      throw error;
    }
  }

  /**
   * Get a specific email template by key
   */
  async getTemplateByKey(templateKey: string): Promise<EmailTemplate | null> {
    try {
      console.log(`[EMAIL-TEMPLATES] Fetching template: ${templateKey}`);

      const { data, error } = await supabase
        .from('iepa_email_templates')
        .select('*')
        .eq('template_key', templateKey)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          console.log(`[EMAIL-TEMPLATES] Template not found: ${templateKey}`);
          return null;
        }
        console.error('[EMAIL-TEMPLATES] Error fetching template:', error);
        throw new Error(`Failed to fetch email template: ${error.message}`);
      }

      console.log(`[EMAIL-TEMPLATES] Retrieved template: ${templateKey}`);
      return data;
    } catch (error: unknown) {
      console.error('[EMAIL-TEMPLATES] Service error:', error);
      throw error;
    }
  }

  /**
   * Get a specific email template by ID
   */
  async getTemplateById(id: string): Promise<EmailTemplate | null> {
    try {
      console.log(`[EMAIL-TEMPLATES] Fetching template by ID: ${id}`);

      const { data, error } = await supabase
        .from('iepa_email_templates')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          console.log(`[EMAIL-TEMPLATES] Template not found: ${id}`);
          return null;
        }
        console.error('[EMAIL-TEMPLATES] Error fetching template:', error);
        throw new Error(`Failed to fetch email template: ${error.message}`);
      }

      console.log(`[EMAIL-TEMPLATES] Retrieved template: ${id}`);
      return data;
    } catch (error: unknown) {
      console.error('[EMAIL-TEMPLATES] Service error:', error);
      throw error;
    }
  }

  /**
   * Create a new email template
   */
  async createTemplate(template: EmailTemplateCreate, changedBy: string): Promise<EmailTemplate> {
    try {
      console.log(`[EMAIL-TEMPLATES] Creating template: ${template.template_key}`);

      const { data, error } = await supabase
        .from('iepa_email_templates')
        .insert([{
          ...template,
          variables: template.variables || [],
          is_active: template.is_active !== undefined ? template.is_active : true
        }])
        .select()
        .single();

      if (error) {
        console.error('[EMAIL-TEMPLATES] Error creating template:', error);
        throw new Error(`Failed to create email template: ${error.message}`);
      }

      // Log the creation
      await this.logTemplateChange(
        data.id,
        template.template_key,
        'created',
        null,
        'Template created',
        changedBy,
        'Template created via admin interface'
      );

      console.log(`[EMAIL-TEMPLATES] Created template: ${template.template_key}`);
      return data;
    } catch (error: unknown) {
      console.error('[EMAIL-TEMPLATES] Service error:', error);
      throw error;
    }
  }

  /**
   * Update an email template
   */
  async updateTemplate(
    id: string,
    updates: EmailTemplateUpdate,
    changedBy: string,
    changeReason?: string
  ): Promise<EmailTemplate> {
    try {
      console.log(`[EMAIL-TEMPLATES] Updating template: ${id}`);

      // Get current template for logging
      const currentTemplate = await this.getTemplateById(id);
      if (!currentTemplate) {
        throw new Error('Template not found');
      }

      const { data, error } = await supabase
        .from('iepa_email_templates')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('[EMAIL-TEMPLATES] Error updating template:', error);
        throw new Error(`Failed to update email template: ${error.message}`);
      }

      // Log changes for each updated field
      for (const [field, newValue] of Object.entries(updates)) {
        if (newValue !== undefined && newValue !== (currentTemplate as unknown as Record<string, unknown>)[field]) {
          await this.logTemplateChange(
            id,
            currentTemplate.template_key,
            field,
            (currentTemplate as unknown as Record<string, unknown>)[field],
            newValue,
            changedBy,
            changeReason || 'Updated via admin interface'
          );
        }
      }

      console.log(`[EMAIL-TEMPLATES] Updated template: ${id}`);
      return data;
    } catch (error: unknown) {
      console.error('[EMAIL-TEMPLATES] Service error:', error);
      throw error;
    }
  }

  /**
   * Delete an email template (soft delete by setting is_active to false)
   */
  async deleteTemplate(id: string, changedBy: string): Promise<void> {
    try {
      console.log(`[EMAIL-TEMPLATES] Deleting template: ${id}`);

      const currentTemplate = await this.getTemplateById(id);
      if (!currentTemplate) {
        throw new Error('Template not found');
      }

      const { error } = await supabase
        .from('iepa_email_templates')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        console.error('[EMAIL-TEMPLATES] Error deleting template:', error);
        throw new Error(`Failed to delete email template: ${error.message}`);
      }

      // Log the deletion
      await this.logTemplateChange(
        id,
        currentTemplate.template_key,
        'is_active',
        true,
        false,
        changedBy,
        'Template deleted via admin interface'
      );

      console.log(`[EMAIL-TEMPLATES] Deleted template: ${id}`);
    } catch (error: unknown) {
      console.error('[EMAIL-TEMPLATES] Service error:', error);
      throw error;
    }
  }

  /**
   * Log template changes for audit trail
   */
  private async logTemplateChange(
    templateId: string,
    templateKey: string,
    fieldChanged: string,
    oldValue: unknown,
    newValue: unknown,
    changedBy: string,
    changeReason: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('iepa_email_template_log')
        .insert([{
          template_id: templateId,
          template_key: templateKey,
          field_changed: fieldChanged,
          old_value: oldValue ? String(oldValue) : null,
          new_value: String(newValue),
          changed_by: changedBy,
          change_reason: changeReason
        }]);

      if (error) {
        console.error('[EMAIL-TEMPLATES] Error logging template change:', error);
        // Don't throw error for logging failures
      }
    } catch (error) {
      console.error('[EMAIL-TEMPLATES] Error logging template change:', error);
      // Don't throw error for logging failures
    }
  }

  /**
   * Get template change history
   */
  async getTemplateHistory(templateId: string): Promise<Record<string, unknown>[]> {
    try {
      console.log(`[EMAIL-TEMPLATES] Fetching history for template: ${templateId}`);

      const { data, error } = await supabase
        .from('iepa_email_template_log')
        .select('*')
        .eq('template_id', templateId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('[EMAIL-TEMPLATES] Error fetching template history:', error);
        throw new Error(`Failed to fetch template history: ${error.message}`);
      }

      return data || [];
    } catch (error: unknown) {
      console.error('[EMAIL-TEMPLATES] Service error:', error);
      throw error;
    }
  }
}

export const emailTemplateService = new EmailTemplateService();
