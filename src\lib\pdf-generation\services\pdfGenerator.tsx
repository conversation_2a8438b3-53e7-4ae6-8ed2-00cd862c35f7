// PDF Generation Service for IEPA Conference Registration
// Service functions for generating and managing PDF documents

import React from 'react';
import { renderToBuffer } from '@react-pdf/renderer';
import {
  AttendeeRegistrationData,
  SpeakerRegistrationData,
  SponsorRegistrationData,
  PDFGenerationResult,
  PDFStorageResult,
} from '../types';
import {
  createReceiptData,
  validateRegistrationData,
  generatePDFFileName,
} from '../utils';
import { ReceiptTemplate } from '../templates/ReceiptTemplate';
import { PDF_ERROR_MESSAGES, PDF_STORAGE_CONFIG } from '../config';
import { createSupabaseAdmin } from '@/lib/supabase';

/**
 * Generate PDF receipt for registration
 */
export async function generateReceiptPDF(
  type: 'attendee' | 'speaker' | 'sponsor',
  registrationData:
    | AttendeeRegistrationData
    | SpeakerRegistrationData
    | SponsorRegistrationData,
  paymentMethod?: string,
  transactionId?: string
): Promise<PDFGenerationResult> {
  try {
    // Validate registration data
    const validation = validateRegistrationData(
      type,
      registrationData as unknown as Record<string, unknown>
    );
    if (!validation.isValid) {
      return {
        success: false,
        error: `${PDF_ERROR_MESSAGES.INVALID_DATA}: ${validation.errors.join(', ')}`,
      };
    }

    // Create receipt data
    console.log('[PDF-GEN] Creating receipt data for:', {
      type,
      registrationId: registrationData.id,
      email: registrationData.email,
    });

    const receiptData = createReceiptData(
      type,
      registrationData,
      paymentMethod,
      transactionId
    );

    console.log('[PDF-GEN] Receipt data created:', {
      type: receiptData.type,
      receiptNumber: receiptData.receiptNumber,
      total: receiptData.total,
      subtotal: receiptData.subtotal,
      tax: receiptData.tax,
      lineItemsCount: receiptData.lineItems?.length,
      registrationDataId: receiptData.registrationData?.id,
    });

    // Generate PDF buffer
    console.log('[PDF-GEN] Starting PDF rendering...');
    console.log(
      '[PDF-GEN] Receipt data being passed to template:',
      JSON.stringify(receiptData, null, 2)
    );

    let pdfBuffer;
    try {
      pdfBuffer = await renderToBuffer(<ReceiptTemplate data={receiptData} />);

      console.log('[PDF-GEN] PDF buffer generated successfully:', {
        bufferSize: pdfBuffer.length,
        bufferType: typeof pdfBuffer,
        isBuffer: Buffer.isBuffer(pdfBuffer),
      });

      // Check if buffer is suspiciously small
      if (pdfBuffer.length < 1000) {
        console.error('[PDF-GEN] WARNING: PDF buffer is suspiciously small!', {
          size: pdfBuffer.length,
          receiptDataKeys: Object.keys(receiptData),
          receiptType: receiptData.type,
        });
      }
    } catch (renderError) {
      console.error('[PDF-GEN] Error during PDF rendering:', renderError);
      throw new Error(
        `PDF rendering failed: ${renderError instanceof Error ? renderError.message : 'Unknown error'}`
      );
    }

    // Generate file name
    const fileName = generatePDFFileName('receipt', type, registrationData.id);

    return {
      success: true,
      pdfBuffer,
      fileName,
    };
  } catch (error) {
    console.error('Error generating receipt PDF:', error);
    console.error('Receipt generation error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      registrationId: registrationData.id,
      type,
    });
    return {
      success: false,
      error: `${PDF_ERROR_MESSAGES.GENERATION_FAILED}: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Store PDF in Supabase storage
 */
export async function storePDFInSupabase(
  pdfBuffer: Buffer,
  fileName: string,
  folder: 'receipts' = 'receipts'
): Promise<PDFStorageResult> {
  try {
    // Create admin client for storage operations (bypasses RLS)
    const supabaseAdmin = createSupabaseAdmin();

    // Create file path
    const filePath = `${PDF_STORAGE_CONFIG.folders[folder]}/${fileName}`;

    // Upload to Supabase storage using admin client
    const { data, error } = await supabaseAdmin.storage
      .from(PDF_STORAGE_CONFIG.bucket)
      .upload(filePath, pdfBuffer, {
        contentType: 'application/pdf',
        cacheControl: '3600',
        upsert: true, // Allow overwriting existing files
      });

    if (error) {
      throw error;
    }

    // For private buckets, we'll store the file path and generate signed URLs on demand
    // This is more secure than public URLs
    return {
      success: true,
      fileName,
      filePath: data.path,
      publicUrl: undefined, // Will be generated as signed URL when needed
    };
  } catch (error) {
    console.error('Error storing PDF in Supabase:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      fileName,
      folder,
      bucketName: PDF_STORAGE_CONFIG.bucket,
    });
    return {
      success: false,
      error: `${PDF_ERROR_MESSAGES.STORAGE_FAILED}: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Generate and store receipt PDF
 */
export async function generateAndStoreReceiptPDF(
  type: 'attendee' | 'speaker' | 'sponsor',
  registrationData:
    | AttendeeRegistrationData
    | SpeakerRegistrationData
    | SponsorRegistrationData,
  paymentMethod?: string,
  transactionId?: string
): Promise<PDFStorageResult> {
  try {
    // Generate PDF
    const pdfResult = await generateReceiptPDF(
      type,
      registrationData,
      paymentMethod,
      transactionId
    );

    if (!pdfResult.success || !pdfResult.pdfBuffer || !pdfResult.fileName) {
      return {
        success: false,
        error: pdfResult.error || 'Failed to generate PDF',
      };
    }

    // Store PDF
    const storageResult = await storePDFInSupabase(
      pdfResult.pdfBuffer,
      pdfResult.fileName,
      'receipts'
    );

    return storageResult;
  } catch (error) {
    console.error('Error generating and storing receipt PDF:', error);
    return {
      success: false,
      error: `${PDF_ERROR_MESSAGES.GENERATION_FAILED}: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Generate signed URL for PDF download
 */
export async function generateSignedPDFUrl(
  filePath: string,
  expiresIn: number = 3600 // 1 hour default
): Promise<{ success: boolean; signedUrl?: string; error?: string }> {
  try {
    // Create admin client for storage operations (bypasses RLS)
    const supabaseAdmin = createSupabaseAdmin();

    const { data, error } = await supabaseAdmin.storage
      .from(PDF_STORAGE_CONFIG.bucket)
      .createSignedUrl(filePath, expiresIn);

    if (error) {
      throw error;
    }

    return {
      success: true,
      signedUrl: data.signedUrl,
    };
  } catch (error) {
    console.error('Error generating signed PDF URL:', error);
    return {
      success: false,
      error: `Failed to generate signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Download PDF from Supabase storage
 */
export async function downloadPDFFromSupabase(
  filePath: string
): Promise<{ success: boolean; data?: Blob; error?: string }> {
  try {
    // Create admin client for storage operations (bypasses RLS)
    const supabaseAdmin = createSupabaseAdmin();

    const { data, error } = await supabaseAdmin.storage
      .from(PDF_STORAGE_CONFIG.bucket)
      .download(filePath);

    if (error) {
      throw error;
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('Error downloading PDF from Supabase:', error);
    return {
      success: false,
      error: `${PDF_ERROR_MESSAGES.DOWNLOAD_FAILED}: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Delete PDF from Supabase storage
 */
export async function deletePDFFromSupabase(
  filePath: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Create admin client for storage operations (bypasses RLS)
    const supabaseAdmin = createSupabaseAdmin();

    const { error } = await supabaseAdmin.storage
      .from(PDF_STORAGE_CONFIG.bucket)
      .remove([filePath]);

    if (error) {
      throw error;
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error('Error deleting PDF from Supabase:', error);
    return {
      success: false,
      error: `Failed to delete PDF: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * List PDFs for a registration
 */
export async function listPDFsForRegistration(
  registrationId: string,
  folder?: 'receipts' | 'invoices'
): Promise<{
  success: boolean;
  files?: Array<{ name: string; path: string; publicUrl: string }>;
  error?: string;
}> {
  try {
    // Create admin client for storage operations (bypasses RLS)
    const supabaseAdmin = createSupabaseAdmin();

    const searchPath = folder ? `${PDF_STORAGE_CONFIG.folders[folder]}/` : '';

    const { data, error } = await supabaseAdmin.storage
      .from(PDF_STORAGE_CONFIG.bucket)
      .list(searchPath, {
        search: registrationId.slice(-8).toUpperCase(),
      });

    if (error) {
      throw error;
    }

    const files = await Promise.all(
      data.map(async file => {
        const fullPath = folder
          ? `${PDF_STORAGE_CONFIG.folders[folder]}/${file.name}`
          : file.name;

        // Generate signed URL for secure access
        const signedUrlResult = await generateSignedPDFUrl(fullPath, 3600); // 1 hour expiry

        return {
          name: file.name,
          path: fullPath,
          publicUrl: signedUrlResult.success ? signedUrlResult.signedUrl! : '',
        };
      })
    );

    return {
      success: true,
      files,
    };
  } catch (error) {
    console.error('Error listing PDFs for registration:', error);
    return {
      success: false,
      error: `Failed to list PDFs: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}
