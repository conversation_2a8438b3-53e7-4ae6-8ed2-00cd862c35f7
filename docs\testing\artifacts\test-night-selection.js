// Test script to verify night selection functionality
// Run this in the browser console on the registration pages

console.log('🧪 Testing Night Selection Functionality');

// Test 1: Check if night selection checkboxes exist on attendee form
function testAttendeeNightSelection() {
  console.log('\n📋 Testing Attendee Registration Form...');
  
  // Navigate to step 4 (Event Options) if not already there
  const step4Button = document.querySelector('[data-testid="progress-step-4"]');
  if (step4Button) {
    step4Button.click();
    console.log('✅ Navigated to Step 4 (Event Options)');
  }
  
  // Wait a moment for the step to load
  setTimeout(() => {
    // Check for night selection checkboxes
    const nightOneCheckbox = document.getElementById('night-one');
    const nightTwoCheckbox = document.getElementById('night-two');
    
    if (nightOneCheckbox && nightTwoCheckbox) {
      console.log('✅ Night selection checkboxes found');
      console.log('   - Night One checkbox:', nightOneCheckbox.checked ? 'checked' : 'unchecked');
      console.log('   - Night Two checkbox:', nightTwoCheckbox.checked ? 'checked' : 'unchecked');
      
      // Test toggling
      nightOneCheckbox.click();
      console.log('✅ Night One checkbox toggled');
      
      nightTwoCheckbox.click();
      console.log('✅ Night Two checkbox toggled');
      
      // Reset to original state
      if (!nightOneCheckbox.checked) nightOneCheckbox.click();
      if (!nightTwoCheckbox.checked) nightTwoCheckbox.click();
      console.log('✅ Checkboxes reset to default state');
      
    } else {
      console.log('❌ Night selection checkboxes not found');
      console.log('   - Night One checkbox:', nightOneCheckbox ? 'found' : 'missing');
      console.log('   - Night Two checkbox:', nightTwoCheckbox ? 'found' : 'missing');
    }
  }, 1000);
}

// Test 2: Check if night selection appears in review section
function testAttendeeReviewSection() {
  console.log('\n📋 Testing Attendee Review Section...');
  
  // Navigate to step 6 (Review & Payment)
  const step6Button = document.querySelector('[data-testid="progress-step-6"]');
  if (step6Button) {
    step6Button.click();
    console.log('✅ Navigated to Step 6 (Review & Payment)');
    
    setTimeout(() => {
      // Look for lodging summary
      const lodgingSection = document.querySelector('h4')?.textContent?.includes('Conference Lodging');
      if (lodgingSection) {
        console.log('✅ Conference Lodging section found in review');
      } else {
        console.log('❌ Conference Lodging section not found in review');
      }
    }, 1000);
  }
}

// Test 3: Check speaker form night selection
function testSpeakerNightSelection() {
  console.log('\n🎤 Testing Speaker Registration Form...');
  
  // Check for night selection checkboxes
  const nightOneCheckbox = document.getElementById('nightOne');
  const nightTwoCheckbox = document.getElementById('nightTwo');
  
  if (nightOneCheckbox && nightTwoCheckbox) {
    console.log('✅ Speaker night selection checkboxes found');
    console.log('   - Night One checkbox:', nightOneCheckbox.checked ? 'checked' : 'unchecked');
    console.log('   - Night Two checkbox:', nightTwoCheckbox.checked ? 'checked' : 'unchecked');
    
    // Test toggling
    nightOneCheckbox.click();
    console.log('✅ Speaker Night One checkbox toggled');
    
    nightTwoCheckbox.click();
    console.log('✅ Speaker Night Two checkbox toggled');
    
    // Reset to original state
    if (!nightOneCheckbox.checked) nightOneCheckbox.click();
    if (!nightTwoCheckbox.checked) nightTwoCheckbox.click();
    console.log('✅ Speaker checkboxes reset to default state');
    
  } else {
    console.log('❌ Speaker night selection checkboxes not found');
    console.log('   - Night One checkbox:', nightOneCheckbox ? 'found' : 'missing');
    console.log('   - Night Two checkbox:', nightTwoCheckbox ? 'found' : 'missing');
  }
}

// Test 4: Database submission test (mock)
function testDatabaseSubmission() {
  console.log('\n💾 Testing Database Submission Format...');
  
  // Mock form data to test the structure
  const mockFormData = {
    nightOne: true,
    nightTwo: true,
    // ... other fields
  };
  
  // Check if the night fields would be included in submission
  if (mockFormData.hasOwnProperty('nightOne') && mockFormData.hasOwnProperty('nightTwo')) {
    console.log('✅ Night fields are included in form data structure');
    console.log('   - nightOne:', mockFormData.nightOne);
    console.log('   - nightTwo:', mockFormData.nightTwo);
  } else {
    console.log('❌ Night fields missing from form data structure');
  }
}

// Main test runner
function runAllNightSelectionTests() {
  console.log('🚀 Starting Night Selection Tests...');
  
  // Determine which page we're on
  const currentPath = window.location.pathname;
  
  if (currentPath.includes('/register/attendee')) {
    testAttendeeNightSelection();
    setTimeout(() => testAttendeeReviewSection(), 2000);
  } else if (currentPath.includes('/register/speaker')) {
    testSpeakerNightSelection();
  } else {
    console.log('❌ Not on a registration page. Please navigate to:');
    console.log('   - /register/attendee for attendee tests');
    console.log('   - /register/speaker for speaker tests');
  }
  
  testDatabaseSubmission();
  
  console.log('\n✅ Night Selection Tests Complete!');
  console.log('📝 Summary:');
  console.log('   - Night selection checkboxes should be visible');
  console.log('   - Default state should be both nights checked');
  console.log('   - Checkboxes should be toggleable');
  console.log('   - Night selection should appear in review section');
  console.log('   - Form data should include nightOne and nightTwo fields');
}

// Auto-run tests
runAllNightSelectionTests();

// Make functions available globally for manual testing
window.testNightSelection = {
  runAll: runAllNightSelectionTests,
  attendee: testAttendeeNightSelection,
  attendeeReview: testAttendeeReviewSection,
  speaker: testSpeakerNightSelection,
  database: testDatabaseSubmission
};

console.log('\n🔧 Manual testing functions available:');
console.log('   - testNightSelection.runAll()');
console.log('   - testNightSelection.attendee()');
console.log('   - testNightSelection.speaker()');
console.log('   - testNightSelection.database()');
