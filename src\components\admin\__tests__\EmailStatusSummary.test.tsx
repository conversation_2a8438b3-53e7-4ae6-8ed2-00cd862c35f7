import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmailStatusSummary, { calculateSuccessRate, getStatusSummary } from '../EmailStatusSummary';

// Mock the UI components
jest.mock('@/components/ui', () => ({
  Badge: ({ children, className }: any) => <span className={className}>{children}</span>,
}));

// Mock React Icons
jest.mock('react-icons/fi', () => ({
  FiCheckCircle: () => <span data-testid="check-icon" />,
  FiXCircle: () => <span data-testid="x-icon" />,
  FiClock: () => <span data-testid="clock-icon" />,
  FiMail: () => <span data-testid="mail-icon" />,
  FiRefreshCw: () => <span data-testid="refresh-icon" />,
}));

const mockStats = {
  totalSent: 150,
  totalFailed: 10,
  totalPending: 5,
  totalEmails: 165,
};

describe('EmailStatusSummary', () => {
  it('renders all status items correctly', () => {
    render(<EmailStatusSummary stats={mockStats} />);

    expect(screen.getByText('Sent:')).toBeInTheDocument();
    expect(screen.getByText('Failed:')).toBeInTheDocument();
    expect(screen.getByText('Pending:')).toBeInTheDocument();
    expect(screen.getByText('Total:')).toBeInTheDocument();

    expect(screen.getByText('150')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('165')).toBeInTheDocument();
  });

  it('displays loading state correctly', () => {
    render(<EmailStatusSummary stats={mockStats} loading={true} />);

    const loadingElements = screen.getAllByText('...');
    expect(loadingElements).toHaveLength(4); // One for each status item
  });

  it('formats large numbers with locale string', () => {
    const largeStats = {
      totalSent: 1500,
      totalFailed: 100,
      totalPending: 50,
      totalEmails: 1650,
    };

    render(<EmailStatusSummary stats={largeStats} />);

    expect(screen.getByText('1,500')).toBeInTheDocument();
    expect(screen.getByText('1,650')).toBeInTheDocument();
  });

  it('renders refresh button when onRefresh is provided', () => {
    const mockOnRefresh = jest.fn();
    render(<EmailStatusSummary stats={mockStats} onRefresh={mockOnRefresh} />);

    const refreshButton = screen.getByRole('button');
    expect(refreshButton).toBeInTheDocument();
    expect(refreshButton).toHaveAttribute('title', 'Refresh email statistics');
  });

  it('does not render refresh button when onRefresh is not provided', () => {
    render(<EmailStatusSummary stats={mockStats} />);

    const refreshButton = screen.queryByRole('button');
    expect(refreshButton).not.toBeInTheDocument();
  });

  it('calls onRefresh when refresh button is clicked', () => {
    const mockOnRefresh = jest.fn();
    render(<EmailStatusSummary stats={mockStats} onRefresh={mockOnRefresh} />);

    const refreshButton = screen.getByRole('button');
    fireEvent.click(refreshButton);

    expect(mockOnRefresh).toHaveBeenCalledTimes(1);
  });

  it('disables refresh button when loading', () => {
    const mockOnRefresh = jest.fn();
    render(
      <EmailStatusSummary 
        stats={mockStats} 
        onRefresh={mockOnRefresh} 
        loading={true} 
      />
    );

    const refreshButton = screen.getByRole('button');
    expect(refreshButton).toBeDisabled();
  });

  it('shows animate-spin class on refresh icon when loading', () => {
    const mockOnRefresh = jest.fn();
    render(
      <EmailStatusSummary 
        stats={mockStats} 
        onRefresh={mockOnRefresh} 
        loading={true} 
      />
    );

    const refreshIcon = screen.getByTestId('refresh-icon');
    expect(refreshIcon).toHaveClass('animate-spin');
  });

  it('applies custom className', () => {
    const { container } = render(
      <EmailStatusSummary stats={mockStats} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('renders correct icons for each status', () => {
    render(<EmailStatusSummary stats={mockStats} />);

    expect(screen.getByTestId('check-icon')).toBeInTheDocument(); // Sent
    expect(screen.getByTestId('x-icon')).toBeInTheDocument(); // Failed
    expect(screen.getByTestId('clock-icon')).toBeInTheDocument(); // Pending
    expect(screen.getByTestId('mail-icon')).toBeInTheDocument(); // Total
  });

  it('has proper responsive classes', () => {
    const { container } = render(<EmailStatusSummary stats={mockStats} />);

    expect(container.firstChild).toHaveClass('flex', 'flex-col', 'sm:flex-row');
  });

  it('shows abbreviated labels on mobile', () => {
    render(<EmailStatusSummary stats={mockStats} />);

    // Check for mobile abbreviated labels (hidden on sm and up)
    const mobileLabels = screen.getAllByText(/^[SFPT]:$/);
    expect(mobileLabels).toHaveLength(4);
  });
});

describe('calculateSuccessRate', () => {
  it('calculates success rate correctly', () => {
    const stats = {
      totalSent: 90,
      totalFailed: 10,
      totalPending: 0,
      totalEmails: 100,
    };

    expect(calculateSuccessRate(stats)).toBe(90);
  });

  it('returns 0 when no emails sent', () => {
    const stats = {
      totalSent: 0,
      totalFailed: 0,
      totalPending: 0,
      totalEmails: 0,
    };

    expect(calculateSuccessRate(stats)).toBe(0);
  });

  it('rounds to nearest integer', () => {
    const stats = {
      totalSent: 33,
      totalFailed: 0,
      totalPending: 0,
      totalEmails: 100,
    };

    expect(calculateSuccessRate(stats)).toBe(33);
  });
});

describe('getStatusSummary', () => {
  it('returns no emails message when total is 0', () => {
    const stats = {
      totalSent: 0,
      totalFailed: 0,
      totalPending: 0,
      totalEmails: 0,
    };

    expect(getStatusSummary(stats)).toBe('No emails sent yet');
  });

  it('returns success rate with failed count when there are failures', () => {
    const stats = {
      totalSent: 90,
      totalFailed: 10,
      totalPending: 0,
      totalEmails: 100,
    };

    expect(getStatusSummary(stats)).toBe('90% success rate (10 failed)');
  });

  it('returns pending message when there are pending emails', () => {
    const stats = {
      totalSent: 90,
      totalFailed: 0,
      totalPending: 10,
      totalEmails: 100,
    };

    expect(getStatusSummary(stats)).toBe('10 emails pending delivery');
  });

  it('returns all delivered message when all emails are sent', () => {
    const stats = {
      totalSent: 100,
      totalFailed: 0,
      totalPending: 0,
      totalEmails: 100,
    };

    expect(getStatusSummary(stats)).toBe('All 100 emails delivered successfully');
  });
});
