'use client';

import React, { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/Checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  FiDownload,
  FiRefreshCw,
  FiUsers,
  FiMic,
  FiStar,
  FiCreditCard,
  FiDatabase,
  FiCalendar,
  FiFilter,
} from 'react-icons/fi';

interface ExportOptions {
  tables: string[];
  format: 'csv' | 'json' | 'xlsx';
  dateRange: 'all' | 'today' | 'week' | 'month';
  includeHeaders: boolean;
  includeMetadata: boolean;
}

const availableTables = [
  {
    id: 'iepa_attendee_registrations',
    name: 'Attendee Registrations',
    icon: FiUsers,
  },
  {
    id: 'iepa_speaker_registrations',
    name: 'Speaker Registrations',
    icon: FiMic,
  },
  {
    id: 'iepa_sponsor_registrations',
    name: 'Sponsor Registrations',
    icon: FiStar,
  },
  { id: 'iepa_payment_records', name: 'Payment Records', icon: FiCreditCard },
  { id: 'iepa_admin_users', name: 'Admin Users', icon: FiDatabase },
];

export default function ExportPage() {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    tables: [],
    format: 'csv',
    dateRange: 'all',
    includeHeaders: true,
    includeMetadata: false,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportHistory, setExportHistory] = useState<
    Array<{
      id: string;
      tables: string[];
      format: string;
      timestamp: string;
      recordCount: number;
    }>
  >([]);

  // Handle table selection
  const handleTableToggle = (tableId: string) => {
    setExportOptions(prev => ({
      ...prev,
      tables: prev.tables.includes(tableId)
        ? prev.tables.filter(id => id !== tableId)
        : [...prev.tables, tableId],
    }));
  };

  // Export data
  const handleExport = async () => {
    if (exportOptions.tables.length === 0) {
      setError('Please select at least one table to export');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const exportData: Record<string, unknown[]> = {};
      let totalRecords = 0;

      // Fetch data from selected tables
      for (const tableId of exportOptions.tables) {
        let query = supabase.from(tableId).select('*');

        // Apply consistent sorting for tables with name fields
        if (tableId === 'iepa_attendee_registrations' ||
            tableId === 'iepa_speaker_registrations' ||
            tableId === 'iepa_admin_users') {
          query = query
            .order('last_name', { ascending: true, nullsFirst: false })
            .order('first_name', { ascending: true, nullsFirst: false });
        } else {
          // Default sort by created_at for other tables
          query = query.order('created_at', { ascending: false });
        }

        // Apply date range filter if applicable
        if (exportOptions.dateRange !== 'all') {
          const now = new Date();
          let startDate: Date;

          switch (exportOptions.dateRange) {
            case 'today':
              startDate = new Date(
                now.getFullYear(),
                now.getMonth(),
                now.getDate()
              );
              break;
            case 'week':
              startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
              break;
            case 'month':
              startDate = new Date(now.getFullYear(), now.getMonth(), 1);
              break;
            default:
              startDate = new Date(0);
          }

          query = query.gte('created_at', startDate.toISOString());
        }

        const { data, error: fetchError } = await query;

        if (fetchError) throw fetchError;

        exportData[tableId] = data || [];
        totalRecords += data?.length || 0;
      }

      // Generate export file based on format
      let blob: Blob;
      let filename: string;
      const timestamp = new Date().toISOString().split('T')[0];

      switch (exportOptions.format) {
        case 'csv':
          const csvContent = generateCSV(exportData);
          blob = new Blob([csvContent], { type: 'text/csv' });
          filename = `iepa-export-${timestamp}.csv`;
          break;
        case 'json':
          const jsonContent = JSON.stringify(exportData, null, 2);
          blob = new Blob([jsonContent], { type: 'application/json' });
          filename = `iepa-export-${timestamp}.json`;
          break;
        case 'xlsx':
          // For now, fallback to CSV format
          const xlsxContent = generateCSV(exportData);
          blob = new Blob([xlsxContent], { type: 'text/csv' });
          filename = `iepa-export-${timestamp}.csv`;
          break;
        default:
          throw new Error('Unsupported export format');
      }

      // Download file
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Add to export history
      const newExport = {
        id: Date.now().toString(),
        tables: exportOptions.tables,
        format: exportOptions.format,
        timestamp: new Date().toISOString(),
        recordCount: totalRecords,
      };
      setExportHistory(prev => [newExport, ...prev.slice(0, 9)]); // Keep last 10 exports
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export data');
    } finally {
      setLoading(false);
    }
  };

  // Generate CSV content
  const generateCSV = (data: Record<string, unknown[]>): string => {
    let csvContent = '';

    for (const [tableName, records] of Object.entries(data)) {
      if (exportOptions.includeMetadata) {
        csvContent += `# Table: ${tableName}\n`;
        csvContent += `# Exported: ${new Date().toISOString()}\n`;
        csvContent += `# Records: ${records.length}\n\n`;
      }

      if (records.length > 0) {
        // Headers
        if (exportOptions.includeHeaders) {
          const headers = Object.keys(records[0] as Record<string, unknown>);
          csvContent += headers.join(',') + '\n';
        }

        // Data rows
        records.forEach(record => {
          const values = Object.values(record as Record<string, unknown>).map(
            value => {
              if (value === null || value === undefined) return '';
              if (typeof value === 'string' && value.includes(',')) {
                return `"${value.replace(/"/g, '""')}"`;
              }
              return String(value);
            }
          );
          csvContent += values.join(',') + '\n';
        });
      }

      csvContent += '\n';
    }

    return csvContent;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <FiDownload className="w-6 h-6 mr-3 text-[var(--iepa-primary-blue)]" />
            Data Export
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Export conference data in various formats
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Export Configuration */}
        <div className="lg:col-span-2 space-y-6">
          {/* Table Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FiDatabase className="w-5 h-5 mr-2" />
                Select Tables
              </CardTitle>
            </CardHeader>
            <CardBody className="p-6">
              <div className="space-y-4">
                {availableTables.map(table => {
                  const Icon = table.icon;
                  return (
                    <div key={table.id} className="flex items-center space-x-3">
                      <Checkbox
                        checked={exportOptions.tables.includes(table.id)}
                        onCheckedChange={() => handleTableToggle(table.id)}
                      />
                      <Icon className="w-4 h-4 text-gray-500" />
                      <span className="text-sm font-medium">{table.name}</span>
                    </div>
                  );
                })}
              </div>
            </CardBody>
          </Card>

          {/* Export Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FiFilter className="w-5 h-5 mr-2" />
                Export Options
              </CardTitle>
            </CardHeader>
            <CardBody className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Format
                  </label>
                  <Select
                    value={exportOptions.format}
                    onValueChange={(value: 'csv' | 'json' | 'xlsx') =>
                      setExportOptions(prev => ({ ...prev, format: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                      <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date Range
                  </label>
                  <Select
                    value={exportOptions.dateRange}
                    onValueChange={(
                      value: 'all' | 'today' | 'week' | 'month'
                    ) =>
                      setExportOptions(prev => ({ ...prev, dateRange: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">Last 7 Days</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="mt-4 space-y-3">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={exportOptions.includeHeaders}
                    onCheckedChange={checked =>
                      setExportOptions(prev => ({
                        ...prev,
                        includeHeaders: !!checked,
                      }))
                    }
                  />
                  <span className="text-sm">Include column headers</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={exportOptions.includeMetadata}
                    onCheckedChange={checked =>
                      setExportOptions(prev => ({
                        ...prev,
                        includeMetadata: !!checked,
                      }))
                    }
                  />
                  <span className="text-sm">
                    Include metadata (table names, timestamps)
                  </span>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Error Display */}
          {error && (
            <Card>
              <CardBody className="p-4">
                <div className="text-red-600 text-sm">{error}</div>
              </CardBody>
            </Card>
          )}

          {/* Export Button */}
          <Card>
            <CardBody className="p-6">
              <Button
                onClick={handleExport}
                disabled={loading || exportOptions.tables.length === 0}
                className="w-full flex items-center justify-center"
                size="lg"
              >
                {loading ? (
                  <FiRefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <FiDownload className="w-4 h-4 mr-2" />
                )}
                {loading ? 'Exporting...' : 'Export Data'}
              </Button>
            </CardBody>
          </Card>
        </div>

        {/* Export History */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FiCalendar className="w-5 h-5 mr-2" />
                Recent Exports
              </CardTitle>
            </CardHeader>
            <CardBody className="p-6">
              {exportHistory.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">
                  No exports yet
                </p>
              ) : (
                <div className="space-y-3">
                  {exportHistory.map(exportItem => (
                    <div
                      key={exportItem.id}
                      className="p-3 border border-gray-200 rounded-lg"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          {exportItem.format.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">
                          {exportItem.recordCount} records
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(exportItem.timestamp).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        {exportItem.tables.length} table(s)
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
}
