import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

export async function POST() {
  try {
    // Initialize Supabase admin client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Missing Supabase configuration' },
        { status: 500 }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    console.log('[EMAIL-LOG-SETUP] Checking email log table status...');

    // Test if the table exists by querying it
    const { data: testData, error: testError } = await supabase
      .from('iepa_email_log')
      .select('id')
      .limit(1);

    if (testError) {
      console.error('[EMAIL-LOG-SETUP] Table does not exist:', testError);

      // Provide manual setup instructions
      const setupInstructions = {
        error: 'Email log table does not exist',
        details: testError.message,
        manualSetup: {
          step1: 'Go to your Supabase project dashboard',
          step2: 'Navigate to SQL Editor',
          step3: 'Copy and paste the SQL from: .docs/01-setup-config/email-log-table-setup.sql',
          step4: 'Execute the SQL to create the table',
          step5: 'Test again using this API endpoint'
        },
        sqlFile: '.docs/01-setup-config/email-log-table-setup.sql',
        note: 'Email sending will continue to work without logging. This only affects audit trails.'
      };

      return NextResponse.json(setupInstructions, { status: 200 });
    }

    // Test if we can insert a record (check permissions)
    const testRecord = {
      recipient_email: '<EMAIL>',
      sender_email: '<EMAIL>',
      subject: 'Setup Test',
      email_type: 'setup_test',
      content_preview: 'Testing email log table setup',
      status: 'sent'
    };

    const { data: insertData, error: insertError } = await supabase
      .from('iepa_email_log')
      .insert([testRecord])
      .select();

    if (insertError) {
      console.error('[EMAIL-LOG-SETUP] Insert test failed:', insertError);
      return NextResponse.json({
        warning: 'Table exists but insert failed',
        details: insertError.message,
        tableExists: true,
        canInsert: false,
        suggestion: 'Check Row Level Security policies for service_role'
      }, { status: 200 });
    }

    // Clean up test record
    if (insertData && insertData[0]) {
      await supabase
        .from('iepa_email_log')
        .delete()
        .eq('id', insertData[0].id);
    }

    console.log('[EMAIL-LOG-SETUP] Email log table is fully operational');

    return NextResponse.json({
      success: true,
      message: 'Email log table is fully operational',
      tableExists: true,
      canInsert: true,
      canDelete: true
    });

  } catch (error) {
    console.error('[EMAIL-LOG-SETUP] Setup failed:', error);
    return NextResponse.json(
      { 
        error: 'Setup failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        sqlFile: 'src/lib/database/email-log-schema.sql'
      },
      { status: 500 }
    );
  }
} 