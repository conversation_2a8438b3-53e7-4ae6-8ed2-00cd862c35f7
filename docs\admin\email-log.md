# 📧 Email Center Redesign – Task Plan

Redesign and refactor the IEPA Email Center to improve clarity, usability, and space efficiency.

---

## ✅ Goals

- Prioritize **email logs** for monitoring delivery status
- Reduce vertical space used by the summary cards
- Maintain full functionality with improved information density
- Ensure mobile and desktop responsiveness
- Prep design for real-world use with Tailwind CSS

---

## 🖼️ Layout Summary

### Status Bar (Compact Overview)
Replace the large dashboard cards with a minimal status bar showing stats in a horizontal row.

**Example:**

```
✅ Sent: 26   ❌ Failed: 1   ⏳ Pending: 21   ✉️ Total: 48
```

Display this above the email logs, using icon + label combinations and color-coded text for visibility.

---

### Main Grid Layout (2-Column)

```
----------------------------------------------------------
| Email Logs (2/3 width)      | Send Email (1/3 width)   |
|                             |                          |
----------------------------------------------------------
```

- Use a responsive `grid` or `flex` layout
- Stack vertically on mobile devices

---

## 📨 Email Logs Panel

### Features
- Compact cards for each email log
- Inline status badges (Sent, Failed, Pending)
- Includes:
  - Subject
  - Recipient
  - Preview
  - Sender
  - Timestamp
  - SendGrid ID
- "Copy" SendGrid ID button
- Optional: Link to view full content or retry failed emails

### Filters & Controls
- Status filter dropdown
- Email type filter (optional)
- Refresh button
- Search input (by subject/recipient)

---

### Example Email Log Card

```
[Sent]  Welcome to IEPA 2025
To: <EMAIL>
Preview: Welcome to IEPA's Ann...
From: <EMAIL>
6/21/2025, 7:40 PM
SendGrid ID: abc123xyz [Copy]
```

Use light background shading, shadow, or border to help separate items.

---

## 📝 Send Email Form Panel

### Fields
- Dropdown: Recipient list (e.g. All Attendees, Only Paid)
- Text: Subject
- Textarea: Message body
- Button: Send Email

Styled with minimal spacing and labels. Mobile-friendly.

---

## 💡 Optional Enhancements (Future Scope)

- Add **pagination or infinite scroll** to logs
- Toggle views: preview vs full message
- **Tabs** for:
  - Email Logs
  - Compose
  - Stats
- Ability to **retry/resend** failed messages
- Click-to-expand log for raw content view

---

## 🧱 Tailwind CSS Implementation

Use Tailwind classes to style:

- Grid layout: `grid grid-cols-1 lg:grid-cols-3 gap-6`
- Card containers: `border p-4 rounded bg-white shadow`
- Status bar: `flex flex-wrap gap-4 text-sm font-medium`
- Filters: `select border rounded px-2 py-1 text-sm`
- Text: `text-gray-600`, `font-semibold`, `truncate`
- Buttons: `bg-blue-600 text-white hover:bg-blue-700`

---

## 🔁 Development Tasks

### Layout
- [ ] Replace stat cards with compact status bar
- [ ] Convert main layout to responsive grid (logs + form)
- [ ] Move "Send Email" panel to right column

### Email Logs
- [ ] Redesign log item layout for compact display
- [ ] Add status, subject, recipient, sender, preview
- [ ] Add SendGrid ID display + "Copy" button
- [ ] Filter and search controls with sticky position

### Send Form
- [ ] Retain existing field structure
- [ ] Ensure responsive/mobile layout

### Styling
- [ ] Apply Tailwind styling throughout
- [ ] Light/dark mode considerations (if applicable)
- [ ] Ensure mobile stacking

---

## 📁 File Output Suggestions

- `components/EmailLogCard.tsx`
- `components/SendEmailForm.tsx`
- `components/StatusSummary.tsx`
- `pages/admin/email-center.tsx` (or `/app/email-center/page.tsx` if using App Router)
- Style as reusable and declarative for IEPA app

---

## 📌 Notes

- No need to use modals unless implementing full message preview
- Ensure refresh/resync buttons trigger backend fetch
- Keep all SendGrid message IDs visible for debugging
- Make stats dynamic from Supabase or SendGrid sync results
