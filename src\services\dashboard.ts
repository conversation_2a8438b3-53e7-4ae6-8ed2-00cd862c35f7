// Dashboard service for IEPA 2025 Conference Registration

import { supabase } from '@/lib/supabase';
import type {
  DashboardStats,
  DashboardDataResponse,
  EnhancedAttendeeRegistration,
  EnhancedSpeakerRegistration,
  EnhancedSponsorRegistration,
  ChartDataPoint,
  TimeSeriesDataPoint,
  DashboardFilters,
} from '@/types/dashboard';

// Conference date for calculations
const CONFERENCE_START_DATE = new Date('2025-04-14');

/**
 * Fetch dashboard statistics
 */
export async function fetchDashboardStats(): Promise<DashboardStats> {
  try {
    // Fetch attendee stats
    const { data: attendees, error: attendeeError } = await supabase
      .from('iepa_attendee_registrations')
      .select(
        'registration_total, golf_total, grand_total, attending_golf, meals, payment_status'
      );

    if (attendeeError) throw attendeeError;

    // Fetch speaker stats
    const { data: speakers, error: speakerError } = await supabase
      .from('iepa_speaker_registrations')
      .select('id');

    if (speakerError) throw speakerError;

    // Fetch sponsor stats
    const { data: sponsors, error: sponsorError } = await supabase
      .from('iepa_sponsor_registrations')
      .select('id');

    if (sponsorError) throw sponsorError;

    // Fetch payment stats
    const { error: paymentError } = await supabase
      .from('iepa_payments')
      .select('amount, status');

    if (paymentError) throw paymentError;

    // Calculate statistics
    const totalAttendees = attendees?.length || 0;
    const totalSpeakers = speakers?.length || 0;
    const totalSponsors = sponsors?.length || 0;

    const totalRevenue =
      attendees?.reduce(
        (sum, attendee) => sum + (attendee.grand_total || 0),
        0
      ) || 0;
    const golfParticipants =
      attendees?.filter(attendee => attendee.attending_golf).length || 0;

    const pendingPayments =
      attendees?.filter(attendee => attendee.payment_status === 'pending')
        .length || 0;
    const completedPayments =
      attendees?.filter(attendee => attendee.payment_status === 'completed')
        .length || 0;

    // Calculate meal selections
    const mealSelections: Record<string, number> = {};
    attendees?.forEach(attendee => {
      if (attendee.meals && Array.isArray(attendee.meals)) {
        attendee.meals.forEach(meal => {
          mealSelections[meal] = (mealSelections[meal] || 0) + 1;
        });
      }
    });

    return {
      totalAttendees,
      totalSpeakers,
      totalSponsors,
      totalRevenue,
      pendingPayments,
      completedPayments,
      golfParticipants,
      mealSelections,
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw error;
  }
}

/**
 * Fetch enhanced attendee registrations
 */
export async function fetchAttendeeRegistrations(
  filters?: DashboardFilters
): Promise<EnhancedAttendeeRegistration[]> {
  try {
    let query = supabase.from('iepa_attendee_registrations').select('*');

    // Apply filters
    if (filters?.searchQuery) {
      query = query.or(
        `full_name.ilike.%${filters.searchQuery}%,email.ilike.%${filters.searchQuery}%,organization.ilike.%${filters.searchQuery}%`
      );
    }

    if (filters?.registrationType && filters.registrationType.length > 0) {
      query = query.in('registration_type', filters.registrationType);
    }

    if (filters?.paymentStatus && filters.paymentStatus.length > 0) {
      query = query.in('payment_status', filters.paymentStatus);
    }

    if (filters?.dateRange?.start) {
      query = query.gte('created_at', filters.dateRange.start.toISOString());
    }

    if (filters?.dateRange?.end) {
      query = query.lte('created_at', filters.dateRange.end.toISOString());
    }

    const { data, error } = await query.order('created_at', {
      ascending: false,
    });

    if (error) throw error;

    // Enhance data with computed fields
    const enhancedData: EnhancedAttendeeRegistration[] = (data || []).map(
      attendee => ({
        ...attendee,
        status:
          attendee.payment_status === 'completed' ? 'confirmed' : 'pending',
        daysUntilEvent: Math.ceil(
          (CONFERENCE_START_DATE.getTime() - new Date().getTime()) /
            (1000 * 60 * 60 * 24)
        ),
        totalMeals: attendee.meals ? attendee.meals.length : 0,
      })
    );

    return enhancedData;
  } catch (error) {
    console.error('Error fetching attendee registrations:', error);
    throw error;
  }
}

/**
 * Fetch enhanced speaker registrations
 */
export async function fetchSpeakerRegistrations(
  filters?: DashboardFilters
): Promise<EnhancedSpeakerRegistration[]> {
  try {
    let query = supabase.from('iepa_speaker_registrations').select('*');

    // Apply filters
    if (filters?.searchQuery) {
      query = query.or(
        `full_name.ilike.%${filters.searchQuery}%,email.ilike.%${filters.searchQuery}%,organization_name.ilike.%${filters.searchQuery}%`
      );
    }

    if (filters?.dateRange?.start) {
      query = query.gte('created_at', filters.dateRange.start.toISOString());
    }

    if (filters?.dateRange?.end) {
      query = query.lte('created_at', filters.dateRange.end.toISOString());
    }

    const { data, error } = await query.order('created_at', {
      ascending: false,
    });

    if (error) throw error;

    // Enhance data with computed fields
    const enhancedData: EnhancedSpeakerRegistration[] = (data || []).map(
      speaker => ({
        ...speaker,
        status: speaker.presentation_title ? 'confirmed' : 'pending',
        hasPresentation: !!speaker.presentation_file_url,
        hasHeadshot: !!speaker.headshot_url,
      })
    );

    return enhancedData;
  } catch (error) {
    console.error('Error fetching speaker registrations:', error);
    throw error;
  }
}

/**
 * Fetch enhanced sponsor registrations
 */
export async function fetchSponsorRegistrations(
  filters?: DashboardFilters
): Promise<EnhancedSponsorRegistration[]> {
  try {
    let query = supabase.from('iepa_sponsor_registrations').select('*');

    // Apply filters
    if (filters?.searchQuery) {
      query = query.or(
        `sponsor_name.ilike.%${filters.searchQuery}%,sponsor_description.ilike.%${filters.searchQuery}%`
      );
    }

    if (filters?.paymentStatus && filters.paymentStatus.length > 0) {
      query = query.in('payment_status', filters.paymentStatus);
    }

    if (filters?.dateRange?.start) {
      query = query.gte('created_at', filters.dateRange.start.toISOString());
    }

    if (filters?.dateRange?.end) {
      query = query.lte('created_at', filters.dateRange.end.toISOString());
    }

    const { data, error } = await query.order('created_at', {
      ascending: false,
    });

    if (error) throw error;

    // Enhance data with computed fields
    const enhancedData: EnhancedSponsorRegistration[] = (data || []).map(
      sponsor => ({
        ...sponsor,
        status:
          sponsor.payment_status === 'completed' ? 'confirmed' : 'pending',
        hasAssets: !!(sponsor.sponsor_image_url || sponsor.sponsor_video),
      })
    );

    return enhancedData;
  } catch (error) {
    console.error('Error fetching sponsor registrations:', error);
    throw error;
  }
}

/**
 * Generate chart data for registration trends
 */
export async function generateRegistrationTrends(): Promise<
  TimeSeriesDataPoint[]
> {
  try {
    const { data: attendees, error } = await supabase
      .from('iepa_attendee_registrations')
      .select('created_at, registration_type')
      .order('created_at', { ascending: true });

    if (error) throw error;

    // Group by date and count registrations
    const dateGroups: Record<string, number> = {};
    attendees?.forEach(attendee => {
      const date = new Date(attendee.created_at).toISOString().split('T')[0];
      dateGroups[date] = (dateGroups[date] || 0) + 1;
    });

    return Object.entries(dateGroups).map(([date, value]) => ({
      date,
      value,
    }));
  } catch (error) {
    console.error('Error generating registration trends:', error);
    throw error;
  }
}

/**
 * Generate payment breakdown chart data
 */
export async function generatePaymentBreakdown(): Promise<ChartDataPoint[]> {
  try {
    const { data: attendees, error } = await supabase
      .from('iepa_attendee_registrations')
      .select('payment_status');

    if (error) throw error;

    const statusCounts: Record<string, number> = {};
    attendees?.forEach(attendee => {
      const status = attendee.payment_status || 'pending';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    const colors: Record<string, string> = {
      completed: '#22c55e',
      pending: '#f59e0b',
      failed: '#ef4444',
      refunded: '#6b7280',
    };

    return Object.entries(statusCounts).map(([status, value]) => ({
      label: status.charAt(0).toUpperCase() + status.slice(1),
      value,
      color: colors[status] || '#6b7280',
    }));
  } catch (error) {
    console.error('Error generating payment breakdown:', error);
    throw error;
  }
}

/**
 * Fetch complete dashboard data
 */
export async function fetchDashboardData(
  filters?: DashboardFilters
): Promise<DashboardDataResponse> {
  try {
    const [
      stats,
      attendees,
      speakers,
      sponsors,
      registrationTrends,
      paymentBreakdown,
    ] = await Promise.all([
      fetchDashboardStats(),
      fetchAttendeeRegistrations(filters),
      fetchSpeakerRegistrations(filters),
      fetchSponsorRegistrations(filters),
      generateRegistrationTrends(),
      generatePaymentBreakdown(),
    ]);

    // Generate demographics data (placeholder)
    const demographics: ChartDataPoint[] = [
      { label: 'IEPA Members', value: 45, color: '#1B4332' },
      { label: 'Non-Members', value: 35, color: '#2D5A3D' },
      { label: 'Government', value: 15, color: '#40916C' },
      { label: 'CCA', value: 5, color: '#95D5B2' },
    ];

    return {
      stats,
      attendees,
      speakers,
      sponsors,
      payments: [], // Will be populated when needed
      chartData: {
        registrationTrends,
        paymentBreakdown,
        demographics,
      },
    };
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    throw error;
  }
}
