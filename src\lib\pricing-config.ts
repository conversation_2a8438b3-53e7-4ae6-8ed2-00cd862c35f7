// IEPA 2025 Conference Pricing Configuration
// Centralized configuration for all conference pricing, registration fees, and sponsorship packages

/**
 * Pricing Configuration for IEPA 2025 Conference
 * Based on 2024 rates with adjustments for 2025
 *
 * Source: User-provided 2024 pricing information (baseline for 2025)
 * Last Updated: 2025-01-29
 */

export interface RegistrationPrice {
  id: string;
  name: string;
  displayName: string;
  basePrice: number;
  groupDiscountPrice?: number; // 3rd person discount
  description: string;
  inclusions: string[];
}

export interface SponsorshipPackage {
  id: string;
  name: string;
  price: number;
  includedRegistrations: number;
  level: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  benefits: string[];
  marketingBenefits: string[];
}

// SpeakerPricing interface removed - now using RegistrationPrice interface for consistency

export interface AdditionalOption {
  id: string;
  name: string;
  price: number;
  description: string;
  category: 'golf' | 'family' | 'addon';
}

// Registration Pricing Structure (2025 rates)
// Based on 2024 rates for Annual Meeting 2025
export const REGISTRATION_PRICING: RegistrationPrice[] = [
  {
    id: 'iepa-member',
    name: 'Member',
    displayName: 'IEPA Member Registration',
    basePrice: 2300,
    groupDiscountPrice: 2050, // 3rd Person Discount ($250 savings)
    description: 'Full conference registration for IEPA members',
    inclusions: [
      'Two (2) nights lodging',
      'All meals',
      'Two hosted receptions',
      'Two hosted after-hours social gatherings',
      'Meeting materials',
    ],
  },
  {
    id: 'non-iepa-member',
    name: 'Non Member',
    displayName: 'Non-IEPA Member Registration',
    basePrice: 2650,
    groupDiscountPrice: 2400, // 3rd Person Discount ($250 savings)
    description: 'Full conference registration for non-IEPA members',
    inclusions: [
      'Two (2) nights lodging',
      'All meals',
      'Two hosted receptions',
      'Two hosted after-hours social gatherings',
      'Meeting materials',
    ],
  },
  {
    id: 'cca',
    name: 'CCA',
    displayName: 'California Community Choice Association',
    basePrice: 2300, // Same as IEPA member rate
    description: 'Full conference registration for CCA members',
    inclusions: [
      'Two (2) nights lodging',
      'All meals',
      'Two hosted receptions',
      'Two hosted after-hours social gatherings',
      'Meeting materials',
    ],
  },
  {
    id: 'fed-state-government',
    name: 'Fed/State',
    displayName: 'Federal/State Government',
    basePrice: 2000,
    description: 'Discounted rate for federal and state government employees',
    inclusions: [
      'Two (2) nights lodging',
      'All meals',
      'Two hosted receptions',
      'Two hosted after-hours social gatherings',
      'Meeting materials',
    ],
  },
  {
    id: 'day-use-iepa',
    name: 'Day Use – Member',
    displayName: 'Day Use Only (IEPA Members)',
    basePrice: 1750,
    description: 'Day-only access for IEPA members (no lodging)',
    inclusions: [
      'All meals for selected day(s)',
      'Meeting materials',
      'Access to sessions for selected day(s)',
      'Networking opportunities',
      'No lodging included',
    ],
  },
  {
    id: 'day-use-non-iepa',
    name: 'Day Use – Non Member',
    displayName: 'Day Use Only (Non-IEPA Members)',
    basePrice: 2100,
    description: 'Day-only access for non-IEPA members (no lodging)',
    inclusions: [
      'All meals for selected day(s)',
      'Meeting materials',
      'Access to sessions for selected day(s)',
      'Networking opportunities',
      'No lodging included',
    ],
  },
  {
    id: 'spouse',
    name: 'Spouse',
    displayName: 'Spouse Registration',
    basePrice: 500,
    description: 'Registration for spouse/partner of primary attendee',
    inclusions: [
      'All meals',
      'Two hosted receptions',
      'Two hosted after-hours social gatherings',
      'Meeting materials',
      'Access to networking events',
    ],
  },
  {
    id: 'child',
    name: 'Child',
    displayName: 'Child Registration',
    basePrice: 100,
    description: 'Registration for child of primary attendee',
    inclusions: [
      'All meals',
      'Meeting materials',
      'Access to family-friendly activities',
    ],
  },
  {
    id: 'sponsor-attendee',
    name: 'Sponsor Attendee',
    displayName: 'Sponsor Attendee Registration',
    basePrice: 0,
    description: 'Complimentary registration for sponsor company attendees',
    inclusions: [
      'Two (2) nights lodging',
      'All meals',
      'Two hosted receptions',
      'Two hosted after-hours social gatherings',
      'Meeting materials',
      'Sponsor attendee benefits',
    ],
  },
];

// Additional Options and Add-ons
export const ADDITIONAL_OPTIONS: AdditionalOption[] = [
  {
    id: 'golf-tournament',
    name: 'Golf',
    price: 200,
    description: 'Annual IEPA Golf Tournament',
    category: 'golf',
  },
];

// Sponsorship Packages (2025 rates)
export const SPONSORSHIP_PACKAGES: SponsorshipPackage[] = [
  {
    id: 'bronze-sponsor',
    name: '$5,000 Level',
    price: 5000,
    includedRegistrations: 1,
    level: 'bronze',
    benefits: [
      '1 complimentary registration',
      'Two nights lodging',
      'All meals',
      'Two hosted receptions',
      'Two hosted after-hours social gatherings',
      'Meeting materials',
    ],
    marketingBenefits: [
      'Company information listed on IEPA website',
      'Acknowledgment in IEPA printed and digital agendas',
      'Sponsor tent cards on tables during meals and meetings',
      'Company information in sponsor roster in meeting materials',
    ],
  },
  {
    id: 'silver-sponsor',
    name: '$10,000 Level',
    price: 10000,
    includedRegistrations: 2,
    level: 'silver',
    benefits: [
      '2 complimentary registrations',
      'Two nights lodging per registration',
      'All meals per registration',
      'Two hosted receptions per registration',
      'Two hosted after-hours social gatherings per registration',
      'Meeting materials per registration',
    ],
    marketingBenefits: [
      'Company information listed on IEPA website',
      'Acknowledgment in IEPA printed and digital agendas',
      'Sponsor tent cards on tables during meals and meetings',
      'Company information in sponsor roster in meeting materials',
    ],
  },
  {
    id: 'gold-sponsor',
    name: '$15,000 Level',
    price: 15000,
    includedRegistrations: 3,
    level: 'gold',
    benefits: [
      '3 complimentary registrations',
      'Two nights lodging per registration',
      'All meals per registration',
      'Two hosted receptions per registration',
      'Two hosted after-hours social gatherings per registration',
      'Meeting materials per registration',
    ],
    marketingBenefits: [
      'Company information listed on IEPA website',
      'Acknowledgment in IEPA printed and digital agendas',
      'Sponsor tent cards on tables during meals and meetings',
      'Company information in sponsor roster in meeting materials',
    ],
  },
  {
    id: 'platinum-sponsor',
    name: '$20,000 Level',
    price: 20000,
    includedRegistrations: 4,
    level: 'platinum',
    benefits: [
      '4 complimentary registrations',
      'Two nights lodging per registration',
      'All meals per registration',
      'Two hosted receptions per registration',
      'Two hosted after-hours social gatherings per registration',
      'Meeting materials per registration',
    ],
    marketingBenefits: [
      'Company information listed on IEPA website',
      'Acknowledgment in IEPA printed and digital agendas',
      'Sponsor tent cards on tables during meals and meetings',
      'Company information in sponsor roster in meeting materials',
    ],
  },
  {
    id: 'diamond-sponsor',
    name: '$25,000 Level',
    price: 25000,
    includedRegistrations: 5,
    level: 'diamond',
    benefits: [
      '5 complimentary registrations',
      'Two nights lodging per registration',
      'All meals per registration',
      'Two hosted receptions per registration',
      'Two hosted after-hours social gatherings per registration',
      'Meeting materials per registration',
    ],
    marketingBenefits: [
      'Company information listed on IEPA website',
      'Acknowledgment in IEPA printed and digital agendas',
      'Sponsor tent cards on tables during meals and meetings',
      'Company information in sponsor roster in meeting materials',
    ],
  },
];

// Speaker Pricing Options (compatible with RegistrationCardRadio component)
export const SPEAKER_PRICING: RegistrationPrice[] = [
  {
    id: 'comped-speaker',
    name: 'Complimentary Speaker',
    displayName: 'Complimentary Speaker Registration',
    basePrice: 0,
    description: 'Complimentary registration for invited speakers - includes one night of lodging accommodation',
    inclusions: [
      'One night lodging accommodation (your choice)',
      '3 meals (breakfast, lunch & dinner)',
      'One hosted reception',
      'One hosted after-hours social gathering',
      'Meeting materials',
    ],
  },
  {
    id: 'full-meeting-speaker',
    name: 'Full Meeting Speaker',
    displayName: 'Full Meeting Speaker Registration',
    basePrice: 1500,
    description:
      'Full annual meeting access for speakers who want to stay for entire meeting',
    inclusions: [
      'Two nights lodging',
      'All meals',
      'Two hosted receptions',
      'Two hosted after-hours social gatherings',
      'Meeting materials',
    ],
  },

];

// Pricing Constants
export const PRICING_CONSTANTS = {
  GOLF_TOURNAMENT_FEE: 200,
  GOLF_CLUB_RENTAL_FEE: 75, // Updated from $70 to $75
  GROUP_DISCOUNT_AMOUNT: 250, // Discount for 3rd person
  CANCELLATION_FEE: 100,
  INFLATION_RATE: 0.03, // 3% applied to 2024 rates for 2025
  CURRENCY: 'USD',
  TAX_RATE: 0, // No tax currently applied
};

// Cancellation Policy
export const CANCELLATION_POLICY = {
  earlyDeadline: '2025-08-15',
  earlyRefundAmount: 'Full refund minus $100 cancellation fee',
  latePolicy: 'No refund after deadline, but substitutions accepted',
  substitutionPolicy: 'Substitutions accepted at any time before conference',
  details:
    'Cancellations received in writing or via email before August 15, 2025 will be refunded minus a $100 cancellation fee. Cancellations received in writing/email on or after Friday, August 15, 2025 will NOT be refunded; however, substitutions will be accepted.',
};

// Utility Functions
export const pricingUtils = {
  /**
   * Format price for display with currency symbol
   */
  formatPrice: (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: PRICING_CONSTANTS.CURRENCY,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  },

  /**
   * Get registration price by type
   */
  getRegistrationPrice: (
    registrationType: string,
    isGroupDiscount = false
  ): number => {
    const registration = REGISTRATION_PRICING.find(
      r => r.id === registrationType
    );
    if (!registration) return 0;

    if (isGroupDiscount && registration.groupDiscountPrice) {
      return registration.groupDiscountPrice;
    }
    return registration.basePrice;
  },

  /**
   * Get additional option price
   */
  getAdditionalOptionPrice: (optionId: string): number => {
    const option = ADDITIONAL_OPTIONS.find(o => o.id === optionId);
    return option ? option.price : 0;
  },

  /**
   * Get sponsorship package by ID
   */
  getSponsorshipPackage: (packageId: string): SponsorshipPackage | null => {
    return SPONSORSHIP_PACKAGES.find(p => p.id === packageId) || null;
  },

  /**
   * Get speaker pricing by type
   */
  getSpeakerPrice: (speakerType: string): number => {
    const speaker = SPEAKER_PRICING.find(s => s.id === speakerType);
    return speaker ? speaker.basePrice : 0;
  },

  /**
   * Calculate total registration cost
   */
  calculateRegistrationTotal: (
    registrationType: string,
    includeGolf = false,
    additionalOptions: string[] = [],
    isGroupDiscount = false
  ): number => {
    let total = pricingUtils.getRegistrationPrice(
      registrationType,
      isGroupDiscount
    );

    if (includeGolf) {
      total += PRICING_CONSTANTS.GOLF_TOURNAMENT_FEE;
    }

    additionalOptions.forEach(optionId => {
      total += pricingUtils.getAdditionalOptionPrice(optionId);
    });

    return total;
  },

  /**
   * Calculate group discount savings
   */
  calculateGroupSavings: (registrationType: string): number => {
    const registration = REGISTRATION_PRICING.find(
      r => r.id === registrationType
    );
    if (!registration || !registration.groupDiscountPrice) return 0;

    return registration.basePrice - registration.groupDiscountPrice;
  },

  /**
   * Get all registration options for forms
   */
  getRegistrationOptions: () => {
    return REGISTRATION_PRICING.map(reg => ({
      id: reg.id,
      value: reg.id,
      label: reg.displayName,
      price: reg.basePrice,
      formattedPrice: pricingUtils.formatPrice(reg.basePrice),
      description: reg.description,
      groupDiscountPrice: reg.groupDiscountPrice,
      formattedGroupPrice: reg.groupDiscountPrice
        ? pricingUtils.formatPrice(reg.groupDiscountPrice)
        : null,
    }));
  },

  /**
   * Get sponsorship options for forms
   */
  getSponsorshipOptions: () => {
    return SPONSORSHIP_PACKAGES.map(pkg => ({
      id: pkg.id,
      value: pkg.id,
      label: pkg.name,
      price: pkg.price,
      formattedPrice: pricingUtils.formatPrice(pkg.price),
      level: pkg.level,
      includedRegistrations: pkg.includedRegistrations,
      description: `${pkg.name} - ${pkg.includedRegistrations} registration${pkg.includedRegistrations > 1 ? 's' : ''} included`,
    }));
  },

  /**
   * Validate pricing configuration
   */
  validatePricing: (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Check that all registration types have valid prices
    REGISTRATION_PRICING.forEach(reg => {
      if (reg.basePrice <= 0) {
        errors.push(`Invalid base price for ${reg.name}`);
      }
    });

    // Check that sponsorship packages are in ascending order
    for (let i = 1; i < SPONSORSHIP_PACKAGES.length; i++) {
      if (SPONSORSHIP_PACKAGES[i].price <= SPONSORSHIP_PACKAGES[i - 1].price) {
        errors.push(`Sponsorship package pricing order is incorrect`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Check if early bird pricing is available
   */
  isEarlyBirdAvailable: (): boolean => {
    // For future implementation of early bird pricing
    return false;
  },

  /**
   * Check if group discount applies
   */
  isGroupDiscountEligible: (
    registrationType: string,
    attendeeCount: number
  ): boolean => {
    const registration = REGISTRATION_PRICING.find(
      r => r.id === registrationType
    );
    return !!(registration?.groupDiscountPrice && attendeeCount >= 3);
  },
};

// Configuration Metadata
export const PRICING_METADATA = {
  lastUpdated: '2025-01-29',
  version: '2.1.0',
  baseYear: 2025,
  source: 'Based on 2024 pricing information, updated for 2025',
  notes: '2025 Annual Meeting registration rates and policies based on 2024 baseline. Speaker full meeting fee updated to $1,500.',
};
