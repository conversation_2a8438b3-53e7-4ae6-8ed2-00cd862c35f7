'use client';

import { useState, useEffect, Suspense } from 'react';

// Force dynamic rendering to avoid SSG issues with useSearchParams
export const dynamic = 'force-dynamic';
import { useSearchParams, useRouter } from 'next/navigation';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Input,
  Textarea,
  SponsorCardRadio,
  // IEPASubmitButton, // TODO: Create this component
} from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import { STATE_PROVINCE_OPTIONS } from '@/lib/address-constants';
import { FORM_STORAGE_KEYS } from '@/lib/form-persistence';
import { SPONSORSHIP_PACKAGES } from '@/lib/pricing-config';
import { IEPA_COMPANY_INFO } from '@/lib/pdf-generation/config';
import { useFormPersistence } from '@/hooks/useFormPersistence';
import {
  RestoreDataPrompt,
  FloatingSaveStatus,
  DataManagementPanel,
} from '@/components/ui/form-persistence-ui';
import { useAuth } from '@/contexts/AuthContext';
import { ProtectedRegistrationPage } from '@/components/auth/ProtectedRoute';
import Link from 'next/link';
import { showSuccess, showError } from '@/utils/notifications';
import { useRegistrationConstraints } from '@/hooks/useRegistrationConstraints';
import { ExistingRegistrationNotice } from '@/components/registration/ExistingRegistrationNotice';
import { debugLog } from '@/lib/debug-logger';
// import { useSubmitButton } from '@/hooks/useSubmitButton';
// import { useTestDataFill } from '@/hooks/useTestDataFill';

function SponsorRegistrationForm() {
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const router = useRouter();

  // Debug logging for page initialization
  useEffect(() => {
    debugLog.info('general', 'Sponsor registration page loaded', {
      userId: user?.id,
      userEmail: user?.email ? '[REDACTED]' : null,
      hasUser: !!user,
      searchParams: searchParams?.toString(),
    });
  }, [user, searchParams]);

  // Check registration constraints
  const {
    constraintCheck,
    loading: constraintLoading,
    error: constraintError,
  } = useRegistrationConstraints({
    registrationType: 'sponsor',
    autoCheck: true,
  });
  const [formData, setFormData] = useState({
    sponsorName: '',
    sponsorUrl: '',
    sponsorshipLevel: '',
    contactName: '',
    contactEmail: user?.email || '',
    contactPhone: '',
    contactTitle: '',
    billingAddress: '',
    billingCity: '',
    billingState: '',
    billingZip: '',
    billingCountry: 'United States',
    companyDescription: '',
    logoFile: null,
  });

  const [selectedLevel, setSelectedLevel] = useState<string>('');

  // Test data auto-fill functionality (disabled in production)
  // const { fillTestData, isTestMode } = useTestDataFill(
  //   'sponsor',
  //   setFormData,
  //   {
  //     onTestDataFilled: (summary) => {
  //       console.log('Test data filled:', summary);
  //       showInfo('Test Data Loaded', summary);
  //     },
  //     onError: (error) => {
  //       console.error('Test data fill error:', error);
  //       showError('Test Data Error', error);
  //     },
  //     enabled: true,
  //   }
  // );

  // Submit button state management
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle URL parameter for pre-selecting sponsorship level
  useEffect(() => {
    const levelParam = searchParams?.get('level');
    if (levelParam && SPONSORSHIP_PACKAGES.find(pkg => pkg.id === levelParam)) {
      console.log('🎯 Pre-selecting sponsorship level from URL:', levelParam);
      setFormData(prev => ({ ...prev, sponsorshipLevel: levelParam }));
      setSelectedLevel(levelParam);

      // Show success notification
      const selectedPackage = SPONSORSHIP_PACKAGES.find(
        pkg => pkg.id === levelParam
      );
      if (selectedPackage) {
        showSuccess(
          'Sponsorship Level Pre-selected',
          `${selectedPackage.name} sponsorship level has been selected for you.`
        );
      }
    }
  }, [searchParams]);

  // Form persistence
  const persistence = useFormPersistence(formData, setFormData, {
    formKey: FORM_STORAGE_KEYS.SPONSOR,
    debounceMs: 1000,
    expirationDays: 7,
    excludeFields: ['logoFile'],
    onDataRestored: data => {
      console.log('Sponsor form data restored:', data);
      // Show success notification
      showSuccess(
        'Form Data Restored',
        'Your previous progress has been restored.'
      );
    },
    onDataSaved: () => {
      console.log('Sponsor form data auto-saved');
    },
    onDataCleared: () => {
      console.log('Sponsor form data cleared');
    },
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    if (field === 'sponsorshipLevel') {
      setSelectedLevel(value);
      debugLog.info('form', 'Sponsorship level selected', {
        field,
        value,
        packageDetails: SPONSORSHIP_PACKAGES.find(pkg => pkg.id === value),
      });
    } else {
      // Log form field changes (but redact sensitive info)
      const logValue =
        field.includes('email') || field.includes('phone')
          ? '[REDACTED]'
          : value;
      debugLog.debug('form', 'Form field updated', {
        field,
        value: logValue,
        valueLength: value.length,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return; // Prevent double submission

    try {
      setIsSubmitting(true);
      // Debug logging for form submission start
      debugLog.info('form', 'Sponsor form submission started', {
        formData: {
          ...formData,
          contactEmail: formData.contactEmail ? '[REDACTED]' : '',
        },
        userId: user?.id,
        selectedLevel,
      });

      // Check registration constraints before submission
      if (!user?.id) {
        debugLog.error(
          'form',
          'Authentication error during sponsor form submission',
          null,
          {
            hasUser: !!user,
            userId: user?.id,
          }
        );
        throw new Error('Please log in to submit your registration.');
      }

      try {
        debugLog.info('form', 'Checking registration constraints', {
          userId: user.id,
        });

        const { checkRegistrationConstraints } = await import(
          '@/services/registrationConstraints'
        );

        const constraintResult = await checkRegistrationConstraints(
          user.id,
          'sponsor'
        );

        debugLog.info('form', 'Registration constraints checked', {
          canRegister: constraintResult.canRegister,
          message: constraintResult.message,
        });

        if (!constraintResult.canRegister) {
          debugLog.warn(
            'form',
            'Registration not allowed by constraints',
            constraintResult
          );
          throw new Error(constraintResult.message);
        }
      } catch (error) {
        debugLog.error(
          'form',
          'Error checking registration constraints',
          error,
          {
            userId: user.id,
          }
        );
        throw new Error(
          'Unable to verify registration eligibility. Please try again.'
        );
      }

      try {
        debugLog.info('form', 'Starting database submission process');

        // Import Supabase client
        const { supabase } = await import('@/lib/supabase');

        // Get authenticated user from current session
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError || !session?.user) {
          debugLog.error('auth', 'Session validation failed', sessionError);
          throw new Error(
            'User not authenticated. Please log in and try again.'
          );
        }

        const user = session.user;
        debugLog.info('auth', 'User session validated', { userId: user.id });

        // Prepare data for database insertion
        const submissionData = {
          user_id: user.id,
          sponsor_name: formData.sponsorName,
          sponsor_url: formData.sponsorUrl,
          sponsor_image_url: formData.logoFile || null, // TODO: Implement file upload to Supabase storage
          sponsor_description: formData.companyDescription,
          // Contact information
          contact_name: formData.contactName,
          contact_email: formData.contactEmail,
          contact_phone: formData.contactPhone,
          contact_title: formData.contactTitle,
          // Sponsorship details
          sponsorship_level: formData.sponsorshipLevel,
          sponsorship_amount: selectedPackage?.price || 0,
          // Billing information
          billing_address: formData.billingAddress,
          billing_city: formData.billingCity,
          billing_state: formData.billingState,
          billing_zip: formData.billingZip,
          billing_country: formData.billingCountry,
          // Additional information (removed marketing_goals, exhibit_requirements, special_requests)
          // Golf participation (default to false for now)
          attending_golf: false,
          payment_status: 'pending',
        };

        debugLog.info('api', 'Submitting sponsor registration to database', {
          tableName: 'iepa_sponsor_registrations',
          sponsorName: submissionData.sponsor_name,
          sponsorshipLevel: submissionData.sponsorship_level,
          amount: submissionData.sponsorship_amount,
        });

        // Insert into database
        const { data, error } = await supabase
          .from('iepa_sponsor_registrations')
          .insert([submissionData])
          .select();

        if (error) {
          debugLog.error('api', 'Database insertion failed', error, {
            tableName: 'iepa_sponsor_registrations',
            errorCode: error.code,
            errorMessage: error.message,
          });
          throw error;
        }

        debugLog.info('api', 'Database submission successful', {
          registrationId: data[0]?.id,
          recordCount: data.length,
        });

        // 📧 SEND SPONSOR REGISTRATION CONFIRMATION EMAIL
        try {
          debugLog.info(
            'api',
            'Sending sponsor registration confirmation email',
            {
              email: formData.contactEmail,
              fullName: formData.contactName || formData.sponsorName,
              type: 'sponsor',
              confirmationNumber: data[0]?.id,
            }
          );

          const emailResponse = await fetch('/api/send-registration-email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: formData.contactEmail,
              fullName: formData.contactName || formData.sponsorName,
              type: 'sponsor',
              confirmationNumber: data[0]?.id,
              userId: user.id,
              sponsorshipLevel: formData.sponsorshipLevel,
            }),
          });

          if (emailResponse.ok) {
            debugLog.info(
              'api',
              'Sponsor registration confirmation email sent successfully',
              {
                email: formData.contactEmail,
                status: emailResponse.status,
              }
            );
          } else {
            debugLog.warn('api', 'Email API returned non-OK status', {
              email: formData.contactEmail,
              status: emailResponse.status,
              statusText: emailResponse.statusText,
            });
          }
        } catch (emailError) {
          debugLog.error(
            'api',
            'Failed to send sponsor registration confirmation email',
            emailError,
            {
              email: formData.contactEmail,
            }
          );
          // Don't fail the registration if email fails
        }

        debugLog.info('form', 'Sponsor registration completed successfully', {
          registrationId: data[0]?.id,
          sponsorName: formData.sponsorName,
          sponsorshipLevel: formData.sponsorshipLevel,
        });

        // Clear persisted form data after successful submission
        persistence.clearFormData();
        debugLog.info('form', 'Form data cleared from localStorage');

        // Redirect to confirmation page with registration ID
        const registrationId = data[0]?.id;
        if (registrationId) {
          debugLog.info('form', 'Redirecting to confirmation page', {
            registrationId,
            redirectUrl: `/register/sponsor/confirmation?id=${registrationId}`,
          });
          router.push(`/register/sponsor/confirmation?id=${registrationId}`);
        } else {
          // Fallback: show success notification if no registration ID
          showSuccess(
            'Sponsorship Application Submitted!',
            'You will receive a confirmation email shortly.'
          );
          debugLog.warn(
            'form',
            'No registration ID returned, showing notification instead'
          );
        }

        console.log('Sponsor registration submitted successfully');
        setIsSubmitting(false);
      } catch (error) {
        console.error('Error in nested try block:', error);
        throw error; // Re-throw to be caught by outer catch
      }
    } catch (error) {
      console.error('Sponsor form submission error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'An error occurred';
      showError('Registration Failed', errorMessage);
      setIsSubmitting(false);
    }
  };

  // Get the selected sponsorship package for display
  const selectedPackage = selectedLevel
    ? SPONSORSHIP_PACKAGES.find(pkg => pkg.id === selectedLevel)
    : null;

  // Use shared state/province options for consistency and Stripe compliance

  return (
    <ProtectedRegistrationPage>
      <div className="iepa-container">
        {/* Header */}
        <section className="iepa-section">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="iepa-heading-1 mb-4">
              Sponsorship Registration - IEPA {CONFERENCE_YEAR}
            </h1>
            <p className="iepa-body mb-6">
              Partner with IEPA to showcase your organization and connect with
              environmental professionals. Choose from our sponsorship levels to
              find the perfect fit for your marketing goals.
            </p>
          </div>
        </section>

        {/* Registration Constraint Check */}
        {constraintLoading && (
          <section className="iepa-section">
            <div className="max-w-4xl mx-auto text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="iepa-body">Checking registration eligibility...</p>
            </div>
          </section>
        )}

        {constraintError && (
          <section className="iepa-section">
            <div className="max-w-4xl mx-auto">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-800">
                  Error checking registration eligibility: {constraintError}
                </p>
              </div>
            </div>
          </section>
        )}

        {constraintCheck &&
          !constraintCheck.canRegister &&
          constraintCheck.existingRegistration && (
            <ExistingRegistrationNotice
              registration={constraintCheck.existingRegistration}
              message={constraintCheck.message}
              showEditOption={true}
              showViewOption={true}
            />
          )}

        {/* Only show the form if user can register or if we're still loading */}
        {(constraintLoading ||
          constraintError ||
          (constraintCheck && constraintCheck.canRegister)) && (
          <>
            {/* Important Notice */}
            <section className="iepa-section">
              <div className="max-w-4xl mx-auto">
                <Card className="border-blue-200 bg-blue-50">
                  <CardHeader>
                    <h2 className="iepa-heading-3 text-blue-900 mb-0">
                      📋 Please Read Regarding Sponsor Registration
                    </h2>
                  </CardHeader>
                  <CardBody>
                    <p className="iepa-body text-blue-800">
                      After submitting your sponsor form you will receive: 1)
                      Registration order/Invoice 2) Meeting detail 3) Links to
                      complete the attendee forms
                    </p>
                    <p className="iepa-body font-semibold text-blue-900 mt-3">
                      <strong>IMPORTANT:</strong> All attendees including all
                      speakers and sponsors must complete an attendee form.
                    </p>
                  </CardBody>
                </Card>
              </div>
            </section>

            {/* Form Persistence UI */}
            <section className="iepa-section">
              <div className="max-w-4xl mx-auto">
                <RestoreDataPrompt
                  show={persistence.showRestorePrompt}
                  dataAge={persistence.dataAge}
                  onRestore={persistence.restoreData}
                  onStartFresh={persistence.startFresh}
                  onDismiss={persistence.dismissPrompt}
                  formType="sponsor registration"
                />

                {/* Data Management Panel */}
                {persistence.hasPersistedData &&
                  !persistence.showRestorePrompt && (
                    <DataManagementPanel
                      hasPersistedData={persistence.hasPersistedData}
                      dataAge={persistence.dataAge}
                      isDataExpired={persistence.isDataExpired}
                      onClearData={persistence.clearFormData}
                      formType="sponsor registration"
                      className="mb-6"
                    />
                  )}
              </div>
            </section>

            {/* Sponsor Form */}
            <section className="iepa-section">
              <div className="max-w-4xl mx-auto">
                <form onSubmit={handleSubmit} className="space-y-8">
                  {/* Sponsorship Level */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">1. Sponsorship Level</h2>
                      <p className="text-[var(--iepa-gray-600)] mt-2">
                        Choose the sponsorship level that best fits your
                        marketing goals and budget. Each level includes
                        complimentary registrations and exclusive benefits.
                      </p>
                    </CardHeader>
                    <CardBody>
                      <div className="iepa-form-field">
                        <label className="text-sm font-medium text-[var(--iepa-gray-700)] mb-4 block">
                          Sponsorship Level *
                        </label>
                        <SponsorCardRadio
                          packages={SPONSORSHIP_PACKAGES}
                          value={formData.sponsorshipLevel}
                          onValueChange={value =>
                            handleInputChange('sponsorshipLevel', value)
                          }
                          name="sponsorshipLevel"
                          required
                          aria-describedby="sponsorship-level-description"
                        />
                        <p
                          id="sponsorship-level-description"
                          className="text-sm text-[var(--iepa-gray-600)] mt-4"
                        >
                          All sponsorship levels include lodging, meals, and
                          conference materials for the specified number of
                          registrations.
                        </p>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Organization Information */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">
                        2. Organization Information
                      </h2>
                    </CardHeader>
                    <CardBody>
                      <div className="iepa-form-grid">
                        <div className="iepa-form-field col-span-full">
                          <Input
                            label="Organization Name"
                            placeholder="Enter your organization name"
                            value={formData.sponsorName}
                            onChange={e =>
                              handleInputChange('sponsorName', e.target.value)
                            }
                            isRequired
                            description="This name will appear in all conference materials"
                          />
                        </div>

                        <div className="iepa-form-field col-span-full">
                          <Input
                            label="Organization Website"
                            placeholder="https://www.yourcompany.com"
                            value={formData.sponsorUrl}
                            onChange={e =>
                              handleInputChange('sponsorUrl', e.target.value)
                            }
                            isRequired
                            description="Include http:// or https://"
                          />
                        </div>

                        <div className="iepa-form-field col-span-full">
                          <label className="text-sm font-medium text-[var(--iepa-gray-700)] mb-2 block">
                            Company Description *
                          </label>
                          <Textarea
                            placeholder="Provide a brief description of your organization (100-200 words)"
                            value={formData.companyDescription}
                            onChange={e =>
                              handleInputChange(
                                'companyDescription',
                                e.target.value
                              )
                            }
                            rows={4}
                          />
                          <p className="text-sm text-[var(--iepa-gray-600)] mt-1">
                            This may be used in conference materials
                          </p>
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Contact Information */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">
                        3. Primary Contact Information
                      </h2>
                    </CardHeader>
                    <CardBody>
                      <div className="iepa-form-grid">
                        <div className="iepa-form-field">
                          <Input
                            label="Contact Name"
                            placeholder="Primary contact person"
                            value={formData.contactName}
                            onChange={e =>
                              handleInputChange('contactName', e.target.value)
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="Contact Title"
                            placeholder="Job title"
                            value={formData.contactTitle}
                            onChange={e =>
                              handleInputChange('contactTitle', e.target.value)
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="Email Address"
                            type="email"
                            placeholder="<EMAIL>"
                            value={formData.contactEmail}
                            onChange={e =>
                              handleInputChange('contactEmail', e.target.value)
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="Phone Number"
                            placeholder="(*************"
                            value={formData.contactPhone}
                            onChange={e =>
                              handleInputChange('contactPhone', e.target.value)
                            }
                            isRequired
                          />
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Billing Information */}
                  <Card>
                    <CardHeader>
                      <h2 className="iepa-heading-2">4. Billing Information</h2>
                    </CardHeader>
                    <CardBody>
                      <div className="iepa-form-grid">
                        <div className="iepa-form-field col-span-full">
                          <Input
                            label="Billing Address"
                            placeholder="Street address"
                            value={formData.billingAddress}
                            onChange={e =>
                              handleInputChange(
                                'billingAddress',
                                e.target.value
                              )
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="City"
                            placeholder="City"
                            value={formData.billingCity}
                            onChange={e =>
                              handleInputChange('billingCity', e.target.value)
                            }
                            isRequired
                          />
                        </div>

                        <div className="iepa-form-field">
                          <label className="text-sm font-medium text-[var(--iepa-gray-700)] mb-2 block">
                            State <span className="text-red-500 ml-1">*</span>
                          </label>
                          <Select
                            value={formData.billingState}
                            onValueChange={value =>
                              handleInputChange('billingState', value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select state" />
                            </SelectTrigger>
                            <SelectContent>
                              {STATE_PROVINCE_OPTIONS.map(option => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="iepa-form-field">
                          <Input
                            label="ZIP Code"
                            placeholder="12345"
                            value={formData.billingZip}
                            onChange={e =>
                              handleInputChange('billingZip', e.target.value)
                            }
                            isRequired
                          />
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Attendee Information */}
                  <Card className="border-blue-200 bg-blue-50">
                    <CardHeader>
                      <h2 className="iepa-heading-2 text-blue-900">
                        5. Attendee Registration Process
                      </h2>
                      <p className="text-blue-700 mt-2">
                        How your complimentary attendee registrations work
                      </p>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-6">
                        {/* Sponsorship Level Benefits */}
                        {selectedPackage && (
                          <div className="bg-white p-4 rounded-lg border border-blue-200">
                            <h3 className="iepa-heading-3 text-blue-900 mb-3">
                              📋 Your Sponsorship Benefits
                            </h3>
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-semibold text-blue-800">
                                {selectedPackage.name}
                              </span>
                              <span className="text-2xl font-bold text-blue-900">
                                {selectedPackage.includedRegistrations}
                                {selectedPackage.includedRegistrations === 1
                                  ? ' Registration'
                                  : ' Registrations'}
                              </span>
                            </div>
                            <p className="text-sm text-blue-700">
                              Your sponsorship level includes{' '}
                              {selectedPackage.includedRegistrations}{' '}
                              complimentary
                              {selectedPackage.includedRegistrations === 1
                                ? ' registration'
                                : ' registrations'}
                              with full conference benefits.
                            </p>
                          </div>
                        )}

                        {/* Process Steps */}
                        <div className="bg-white p-4 rounded-lg border border-blue-200">
                          <h3 className="iepa-heading-3 text-blue-900 mb-4">
                            🔄 Attendee Registration Process
                          </h3>
                          <div className="space-y-4">
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                                1
                              </div>
                              <div>
                                <p className="font-semibold text-blue-900">
                                  Payment Processing
                                </p>
                                <p className="text-blue-700 text-sm">
                                  Your sponsorship payment must be received and
                                  processed by IEPA before attendee benefits are
                                  activated.
                                </p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                                2
                              </div>
                              <div>
                                <p className="font-semibold text-blue-900">
                                  Discount Codes Issued
                                </p>
                                <p className="text-blue-700 text-sm">
                                  After payment confirmation, IEPA will send you{' '}
                                  {selectedPackage
                                    ? selectedPackage.includedRegistrations
                                    : 'the appropriate number of'}
                                  {selectedPackage &&
                                  selectedPackage.includedRegistrations === 1
                                    ? ' discount code'
                                    : ' discount codes'}
                                  for 100% off attendee registration fees.
                                </p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                                3
                              </div>
                              <div>
                                <p className="font-semibold text-blue-900">
                                  Distribute Codes
                                </p>
                                <p className="text-blue-700 text-sm">
                                  Share the discount codes with your intended
                                  attendees. Each code can be used once for a
                                  complete registration.
                                </p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                                4
                              </div>
                              <div>
                                <p className="font-semibold text-blue-900">
                                  Attendee Registration
                                </p>
                                <p className="text-blue-700 text-sm">
                                  Your attendees register individually through
                                  the regular attendee registration form using
                                  their assigned discount codes.
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Important Notes */}
                        <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                          <h3 className="iepa-heading-3 text-amber-900 mb-3">
                            ⚠️ Important Notes
                          </h3>
                          <ul className="space-y-2 text-amber-800 text-sm">
                            <li className="flex items-start space-x-2">
                              <span className="text-amber-600">•</span>
                              <span>
                                Each attendee must complete their own individual
                                registration form
                              </span>
                            </li>
                            <li className="flex items-start space-x-2">
                              <span className="text-amber-600">•</span>
                              <span>
                                Discount codes are issued only after sponsorship
                                payment is received
                              </span>
                            </li>
                            <li className="flex items-start space-x-2">
                              <span className="text-amber-600">•</span>
                              <span>
                                Attendees will receive their own confirmation
                                emails and conference materials
                              </span>
                            </li>
                            <li className="flex items-start space-x-2">
                              <span className="text-amber-600">•</span>
                              <span>
                                All attendees, including sponsors, must complete
                                the attendee registration process
                              </span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Payment Instructions */}
                  <Card className="border-green-200 bg-green-50">
                    <CardHeader>
                      <h2 className="iepa-heading-2 text-green-900">
                        💳 Payment Instructions
                      </h2>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-4">
                        <div className="bg-white p-4 rounded-lg border border-green-200">
                          <h3 className="iepa-heading-3 text-green-900 mb-3">
                            Payment by Check
                          </h3>
                          <p className="iepa-body text-green-800 mb-3">
                            Sponsors pay by check. Please make your check
                            payable to:
                          </p>
                          <div className="bg-green-100 p-4 rounded-lg border border-green-300">
                            <p className="iepa-body font-bold text-green-900 mb-2">
                              Pay to: <span className="text-lg">IEPA</span>
                            </p>
                            <p className="iepa-body font-semibold text-green-900 mb-2">
                              Mail to:
                            </p>
                            <div className="iepa-body text-green-800 leading-relaxed">
                              <p>{IEPA_COMPANY_INFO.fullName}</p>
                              <p>{IEPA_COMPANY_INFO.address.street}</p>
                              <p>
                                {IEPA_COMPANY_INFO.address.city},{' '}
                                {IEPA_COMPANY_INFO.address.state}{' '}
                                {IEPA_COMPANY_INFO.address.zipCode}
                              </p>
                              <p>{IEPA_COMPANY_INFO.address.country}</p>
                            </div>
                          </div>
                          <p className="iepa-body-small text-green-700 mt-3">
                            Please include your invoice number with your payment
                            for faster processing.
                          </p>
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Submit Section */}
                  <Card>
                    <CardBody>
                      <div className="text-center">
                        {selectedPackage && (
                          <div
                            className="mb-6 p-4 rounded-lg"
                            style={{
                              backgroundColor: 'var(--iepa-primary-blue)',
                              color: 'white',
                            }}
                          >
                            <h3 className="text-xl font-bold mb-2">
                              Sponsorship Summary
                            </h3>
                            <p className="mb-1">{selectedPackage.name}</p>
                            <p className="text-2xl font-bold">
                              ${selectedPackage.price.toLocaleString()}
                            </p>
                            <p className="text-sm mt-2 opacity-90">
                              Includes {selectedPackage.includedRegistrations}{' '}
                              registration
                              {selectedPackage.includedRegistrations > 1
                                ? 's'
                                : ''}
                            </p>
                          </div>
                        )}

                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                          <Button
                            as={Link}
                            href="/register"
                            variant="bordered"
                            size="lg"
                          >
                            Back to Registration Options
                          </Button>
                          <Button
                            type="submit"
                            disabled={
                              isSubmitting ||
                              !formData.sponsorName ||
                              !formData.contactEmail ||
                              !formData.sponsorshipLevel
                            }
                            className={`px-8 py-3 text-lg font-semibold transition-colors ${
                              isSubmitting ||
                              !formData.sponsorName ||
                              !formData.contactEmail ||
                              !formData.sponsorshipLevel
                                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                                : 'bg-green-600 hover:bg-green-700 text-white cursor-pointer'
                            }`}
                            data-testid="submit-sponsor-registration-button"
                          >
                            {isSubmitting
                              ? 'Submitting Application...'
                              : 'Submit Sponsorship Application'}
                          </Button>
                        </div>

                        <p className="iepa-body-small text-gray-600 mt-4">
                          You will receive a confirmation email and invoice
                          after submitting your application.
                        </p>
                      </div>
                    </CardBody>
                  </Card>
                </form>
              </div>
            </section>

            {/* Floating Save Status */}
            <FloatingSaveStatus
              isAutoSaving={persistence.isAutoSaving}
              lastSavedAt={persistence.lastSavedAt}
              show={true}
              position="bottom-right"
            />
          </>
        )}
      </div>
    </ProtectedRegistrationPage>
  );
}

export default function SponsorRegistrationPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SponsorRegistrationForm />
    </Suspense>
  );
}
