'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { supabase } from '@/lib/supabase';
import { Card, CardBody, CardHeader, CardTitle } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  FiArrowLeft,
  FiEdit,
  FiGlobe,
  FiMail,
  FiPhone,
  FiUser,
  FiDollarSign,
  FiCalendar,
  FiCreditCard,
  FiActivity,
  FiUsers,
  FiCheck,
  FiX,
  FiSave,
} from 'react-icons/fi';
import Link from 'next/link';

interface SponsorRegistration {
  id: string;
  user_id: string;
  sponsor_name: string;
  sponsor_url?: string;
  contact_name: string;
  contact_email: string;
  contact_phone?: string;
  contact_title?: string;
  sponsorship_level: string;
  sponsorship_amount: number;
  payment_status: string;
  payment_method?: string;
  attending_golf: boolean;
  golf_players?: number;

  // Check tracking fields
  check_received?: boolean;
  check_received_date?: string;
  check_number?: string;
  check_notes?: string;
  created_at: string;
  updated_at: string;
}

export default function SponsorViewPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sponsorId = searchParams?.get('id');
  const isTestMode = searchParams?.get('testAdmin') === 'true';

  const { isAdmin, isLoading: adminLoading } = useAdminAccess();
  const [sponsor, setSponsor] = useState<SponsorRegistration | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check tracking state
  const [isEditingCheck, setIsEditingCheck] = useState(false);
  const [checkReceived, setCheckReceived] = useState(false);
  const [checkReceivedDate, setCheckReceivedDate] = useState('');
  const [checkNumber, setCheckNumber] = useState('');
  const [checkNotes, setCheckNotes] = useState('');
  const [savingCheck, setSavingCheck] = useState(false);

  // Email resend state
  const [resendingEmail, setResendingEmail] = useState(false);
  const [emailMessage, setEmailMessage] = useState<{
    type: 'success' | 'error';
    text: string;
  } | null>(null);

  // Check if <NAME_EMAIL> for special access
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const isEnoteware = userEmail === '<EMAIL>';

  useEffect(() => {
    const getUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUserEmail(user?.email || null);
    };
    getUser();
  }, []);

  const fetchSponsor = async () => {
    if (!sponsorId) return;

    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('iepa_sponsor_registrations')
        .select('*')
        .eq('id', sponsorId)
        .single();

      if (fetchError) throw fetchError;

      setSponsor(data);

      // Initialize check tracking state
      setCheckReceived(data.check_received || false);
      setCheckReceivedDate(
        data.check_received_date ? data.check_received_date.split('T')[0] : ''
      );
      setCheckNumber(data.check_number || '');
      setCheckNotes(data.check_notes || '');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch sponsor');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('🔍 View page access check:', {
      adminLoading,
      isAdmin,
      isTestMode,
      sponsorId,
      userEmail: 'checking...',
    });

    // Allow <NAME_EMAIL>, admin users, or test mode
    const hasAccess = isAdmin || isTestMode || isEnoteware;

    // Only redirect if we're sure the user is not an admin (loading is complete) and not enoteware
    if (!adminLoading && !hasAccess && !isTestMode && !isEnoteware) {
      console.log('🚫 Access denied - redirecting to admin dashboard');
      router.push('/admin');
      return;
    }

    // Only proceed if we have admin access, are in test mode, or are enoteware
    if ((hasAccess || isTestMode || isEnoteware) && sponsorId) {
      fetchSponsor();
    } else if (!sponsorId && !adminLoading) {
      router.push('/admin/sponsors');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAdmin, adminLoading, sponsorId, isTestMode, isEnoteware, router]);

  const handleBack = () => {
    router.push(`/admin/sponsors${isTestMode ? '?testAdmin=true' : ''}`);
  };

  const handleSaveCheckInfo = async () => {
    if (!sponsor) return;

    try {
      setSavingCheck(true);

      const updateData: Record<string, unknown> = {
        check_received: checkReceived,
        check_number: checkNumber || null,
        check_notes: checkNotes || null,
        updated_at: new Date().toISOString(),
      };

      // Only set check_received_date if check is marked as received
      if (checkReceived && checkReceivedDate) {
        updateData.check_received_date = new Date(
          checkReceivedDate
        ).toISOString();
      } else if (!checkReceived) {
        updateData.check_received_date = null;
      }

      const { error: updateError } = await supabase
        .from('iepa_sponsor_registrations')
        .update(updateData)
        .eq('id', sponsor.id);

      if (updateError) throw updateError;

      // Refresh sponsor data
      await fetchSponsor();
      setIsEditingCheck(false);

      // Show success message (you could add a toast notification here)
      console.log('Check information updated successfully');
    } catch (err) {
      console.error('Error updating check information:', err);
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to update check information'
      );
    } finally {
      setSavingCheck(false);
    }
  };

  const handleCancelCheckEdit = () => {
    // Reset to original values
    setCheckReceived(sponsor?.check_received || false);
    setCheckReceivedDate(
      sponsor?.check_received_date
        ? sponsor.check_received_date.split('T')[0]
        : ''
    );
    setCheckNumber(sponsor?.check_number || '');
    setCheckNotes(sponsor?.check_notes || '');
    setIsEditingCheck(false);
  };

  const handleResendWelcomeEmail = async () => {
    if (!sponsor) return;

    try {
      setResendingEmail(true);
      setEmailMessage(null);

      const response = await fetch('/api/admin/resend-welcome-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sponsorId: sponsor.id,
          type: 'sponsor',
        }),
      });

      const result = await response.json();

      if (result.success) {
        setEmailMessage({
          type: 'success',
          text: `Welcome email sent successfully to ${sponsor.contact_email}`,
        });
      } else {
        setEmailMessage({
          type: 'error',
          text: result.error || 'Failed to send welcome email',
        });
      }
    } catch (error) {
      console.error('Error resending welcome email:', error);
      setEmailMessage({
        type: 'error',
        text: 'Failed to send welcome email',
      });
    } finally {
      setResendingEmail(false);
      // Clear message after 5 seconds
      setTimeout(() => setEmailMessage(null), 5000);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getSponsorshipLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'diamond':
        return 'bg-blue-100 text-blue-800';
      case 'platinum':
        return 'bg-gray-100 text-gray-800';
      case 'gold':
        return 'bg-yellow-100 text-yellow-800';
      case 'silver':
        return 'bg-gray-100 text-gray-600';
      case 'bronze':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCheckStatusColor = (received: boolean) => {
    return received
      ? 'bg-green-100 text-green-800'
      : 'bg-yellow-100 text-yellow-800';
  };

  if ((adminLoading && !isTestMode) || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--iepa-primary-blue)] mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading sponsor...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={handleBack}>
          <FiArrowLeft className="w-4 h-4 mr-2" />
          Back to Sponsors
        </Button>
      </div>
    );
  }

  if (!sponsor) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Sponsor not found</p>
        <Button onClick={handleBack} className="mt-4">
          <FiArrowLeft className="w-4 h-4 mr-2" />
          Back to Sponsors
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back to Sponsors
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Sponsor Details
            </h1>
            <p className="text-gray-600">{sponsor.sponsor_name}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleResendWelcomeEmail}
            disabled={resendingEmail}
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
          >
            <FiMail className="w-4 h-4 mr-2" />
            {resendingEmail ? 'Sending...' : 'Resend Welcome Email'}
          </Button>
          <Link
            href={`/admin/sponsors/edit?id=${sponsor.id}${isTestMode ? '&testAdmin=true' : ''}`}
          >
            <Button variant="outline">
              <FiEdit className="w-4 h-4 mr-2" />
              Edit Sponsor
            </Button>
          </Link>
        </div>
      </div>

      {/* Email Status Message */}
      {emailMessage && (
        <div
          className={`p-4 rounded-lg border ${
            emailMessage.type === 'success'
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          }`}
        >
          <div className="flex items-center">
            {emailMessage.type === 'success' ? (
              <FiCheck className="w-5 h-5 mr-2" />
            ) : (
              <FiX className="w-5 h-5 mr-2" />
            )}
            {emailMessage.text}
          </div>
        </div>
      )}

      {/* Sponsor Information Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Organization Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiUsers className="w-5 h-5 mr-2" />
              Organization Information
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Organization Name
              </label>
              <p className="text-gray-900">{sponsor.sponsor_name}</p>
            </div>

            {sponsor.sponsor_url && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Website
                </label>
                <div className="flex items-center">
                  <FiGlobe className="w-4 h-4 mr-2 text-gray-400" />
                  <a
                    href={sponsor.sponsor_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {sponsor.sponsor_url}
                  </a>
                </div>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-gray-500">
                Sponsorship Level
              </label>
              <div className="mt-1">
                <Badge
                  className={getSponsorshipLevelColor(
                    sponsor.sponsorship_level
                  )}
                >
                  {sponsor.sponsorship_level.charAt(0).toUpperCase() +
                    sponsor.sponsorship_level.slice(1)}
                </Badge>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Sponsorship Amount
              </label>
              <div className="flex items-center">
                <FiDollarSign className="w-4 h-4 mr-2 text-gray-400" />
                <p className="text-gray-900 font-medium">
                  {formatCurrency(sponsor.sponsorship_amount)}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiUser className="w-5 h-5 mr-2" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Contact Name
              </label>
              <p className="text-gray-900">{sponsor.contact_name}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <div className="flex items-center">
                <FiMail className="w-4 h-4 mr-2 text-gray-400" />
                <a
                  href={`mailto:${sponsor.contact_email}`}
                  className="text-blue-600 hover:text-blue-800"
                >
                  {sponsor.contact_email}
                </a>
              </div>
            </div>

            {sponsor.contact_phone && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Phone
                </label>
                <div className="flex items-center">
                  <FiPhone className="w-4 h-4 mr-2 text-gray-400" />
                  <a
                    href={`tel:${sponsor.contact_phone}`}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {sponsor.contact_phone}
                  </a>
                </div>
              </div>
            )}

            {sponsor.contact_title && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Title
                </label>
                <p className="text-gray-900">{sponsor.contact_title}</p>
              </div>
            )}
          </CardBody>
        </Card>
      </div>

      {/* Payment and Check Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payment Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiCreditCard className="w-5 h-5 mr-2" />
              Payment Information
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Payment Status
              </label>
              <div className="mt-1">
                <Badge
                  className={getPaymentStatusColor(sponsor.payment_status)}
                >
                  {sponsor.payment_status.charAt(0).toUpperCase() +
                    sponsor.payment_status.slice(1)}
                </Badge>
              </div>
            </div>

            {sponsor.payment_method && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Payment Method
                </label>
                <p className="text-gray-900">{sponsor.payment_method}</p>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-gray-500">
                Amount
              </label>
              <div className="flex items-center">
                <FiDollarSign className="w-4 h-4 mr-2 text-gray-400" />
                <p className="text-gray-900 font-medium">
                  {formatCurrency(sponsor.sponsorship_amount)}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Check Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <FiCheck className="w-5 h-5 mr-2" />
                Check Information
              </div>
              {!isEditingCheck && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditingCheck(true)}
                >
                  <FiEdit className="w-4 h-4 mr-1" />
                  Edit
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            {!isEditingCheck ? (
              <>
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Check Status
                  </label>
                  <div className="mt-1">
                    <Badge
                      className={getCheckStatusColor(
                        sponsor.check_received || false
                      )}
                    >
                      {sponsor.check_received ? 'Received' : 'Not Received'}
                    </Badge>
                  </div>
                </div>

                {sponsor.check_received && sponsor.check_received_date && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Date Received
                    </label>
                    <div className="flex items-center">
                      <FiCalendar className="w-4 h-4 mr-2 text-gray-400" />
                      <p className="text-gray-900">
                        {formatDate(sponsor.check_received_date)}
                      </p>
                    </div>
                  </div>
                )}

                {sponsor.check_number && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Check Number
                    </label>
                    <p className="text-gray-900">{sponsor.check_number}</p>
                  </div>
                )}

                {sponsor.check_notes && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Notes
                    </label>
                    <p className="text-gray-900 whitespace-pre-wrap">
                      {sponsor.check_notes}
                    </p>
                  </div>
                )}
              </>
            ) : (
              <>
                <div>
                  <label className="text-sm font-medium text-gray-500 mb-2 block">
                    Check Status
                  </label>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="checkReceived"
                        checked={!checkReceived}
                        onChange={() => setCheckReceived(false)}
                        className="mr-2"
                      />
                      Not Received
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="checkReceived"
                        checked={checkReceived}
                        onChange={() => setCheckReceived(true)}
                        className="mr-2"
                      />
                      Received
                    </label>
                  </div>
                </div>

                {checkReceived && (
                  <div>
                    <label className="text-sm font-medium text-gray-500 mb-2 block">
                      Date Received
                    </label>
                    <input
                      type="date"
                      value={checkReceivedDate}
                      onChange={e => setCheckReceivedDate(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-gray-500 mb-2 block">
                    Check Number
                  </label>
                  <input
                    type="text"
                    value={checkNumber}
                    onChange={e => setCheckNumber(e.target.value)}
                    placeholder="Enter check number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500 mb-2 block">
                    Notes
                  </label>
                  <textarea
                    value={checkNotes}
                    onChange={e => setCheckNotes(e.target.value)}
                    placeholder="Additional notes about the check"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="flex gap-2 pt-2">
                  <Button
                    onClick={handleSaveCheckInfo}
                    disabled={savingCheck}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <FiSave className="w-4 h-4 mr-1" />
                    {savingCheck ? 'Saving...' : 'Save'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCancelCheckEdit}
                    disabled={savingCheck}
                  >
                    <FiX className="w-4 h-4 mr-1" />
                    Cancel
                  </Button>
                </div>
              </>
            )}
          </CardBody>
        </Card>
      </div>

      {/* Event Participation */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FiActivity className="w-5 h-5 mr-2" />
              Event Participation
            </CardTitle>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Golf Tournament
              </label>
              <div className="flex items-center">
                <FiActivity className="w-4 h-4 mr-2 text-gray-400" />
                <p className="text-gray-900">
                  {sponsor.attending_golf ? 'Attending' : 'Not Attending'}
                </p>
              </div>
            </div>

            {sponsor.attending_golf && sponsor.golf_players && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Number of Golf Players
                </label>
                <p className="text-gray-900">{sponsor.golf_players}</p>
              </div>
            )}
          </CardBody>
        </Card>
      </div>

      {/* Registration Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FiCalendar className="w-5 h-5 mr-2" />
            Registration Information
          </CardTitle>
        </CardHeader>
        <CardBody className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-500">
              Registration Date
            </label>
            <div className="flex items-center">
              <FiCalendar className="w-4 h-4 mr-2 text-gray-400" />
              <p className="text-gray-900">{formatDate(sponsor.created_at)}</p>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-500">
              Last Updated
            </label>
            <div className="flex items-center">
              <FiCalendar className="w-4 h-4 mr-2 text-gray-400" />
              <p className="text-gray-900">{formatDate(sponsor.updated_at)}</p>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-500">
              Sponsor ID
            </label>
            <p className="text-gray-900 font-mono text-sm">{sponsor.id}</p>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
