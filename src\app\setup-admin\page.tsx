'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardBody, CardHeader, Button, Input } from '@/components/ui';
import { supabase } from '@/lib/supabase';

export default function SetupAdminPage() {
  const { user } = useAuth();
  const [email, setEmail] = useState(user?.email || '');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleAddAdmin = async () => {
    if (!email) {
      setError('Please enter an email address');
      return;
    }

    setLoading(true);
    setError('');
    setMessage('');

    try {
      // First check if admin already exists
      const { data: existing } = await supabase
        .from('iepa_admin_users')
        .select('*')
        .eq('email', email)
        .single();

      if (existing) {
        setMessage(`Admin user already exists for ${email}`);
        setLoading(false);
        return;
      }

      // Add new admin user
      const { error: insertError } = await supabase
        .from('iepa_admin_users')
        .insert({
          email: email,
          role: 'super_admin',
          permissions: {
            dashboard: true,
            users: true,
            settings: true,
            reports: true,
            database: true,
            audit: true,
          },
          is_active: true,
        })
        .select()
        .single();

      if (insertError) {
        throw insertError;
      }

      setMessage(`Successfully added ${email} as admin user!`);
    } catch (err) {
      console.error('Error adding admin:', err);
      setError(err instanceof Error ? err.message : 'Failed to add admin user');
    } finally {
      setLoading(false);
    }
  };

  const handleMakeCurrentUserAdmin = async () => {
    if (!user?.email) {
      setError('No user logged in');
      return;
    }

    setEmail(user.email);
    setLoading(true);
    setError('');
    setMessage('');

    try {
      // Check if admin already exists
      const { data: existing } = await supabase
        .from('iepa_admin_users')
        .select('*')
        .eq('email', user.email)
        .single();

      if (existing) {
        setMessage(`You are already an admin user!`);
        setLoading(false);
        return;
      }

      // Add current user as admin
      const { error: insertError } = await supabase
        .from('iepa_admin_users')
        .insert({
          user_id: user.id,
          email: user.email,
          role: 'super_admin',
          permissions: {
            dashboard: true,
            users: true,
            settings: true,
            reports: true,
            database: true,
            audit: true,
          },
          is_active: true,
        })
        .select()
        .single();

      if (insertError) {
        throw insertError;
      }

      setMessage(
        `Successfully added you (${user.email}) as admin user! You can now access the dashboard.`
      );
    } catch (err) {
      console.error('Error adding admin:', err);
      setError(err instanceof Error ? err.message : 'Failed to add admin user');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="iepa-container">
      <section className="iepa-section">
        <div className="max-w-2xl mx-auto">
          <h1 className="iepa-heading-1 mb-8">Admin Setup</h1>

          <Card className="mb-6">
            <CardHeader>
              <h2 className="iepa-heading-2">Current User</h2>
            </CardHeader>
            <CardBody>
              {user ? (
                <div className="space-y-3">
                  <div>
                    <strong>Email:</strong> {user.email}
                  </div>
                  <div>
                    <strong>ID:</strong> {user.id}
                  </div>
                  <Button
                    onClick={handleMakeCurrentUserAdmin}
                    disabled={loading}
                    className="w-full"
                  >
                    {loading ? 'Adding...' : 'Make Me Admin'}
                  </Button>
                </div>
              ) : (
                <div>
                  <p>
                    Not logged in. Please{' '}
                    <a
                      href="/auth/login"
                      className="text-blue-600 hover:underline"
                    >
                      sign in
                    </a>{' '}
                    first.
                  </p>
                </div>
              )}
            </CardBody>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <h2 className="iepa-heading-2">Add Admin User</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <Input
                  label="Email Address"
                  type="email"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  placeholder="Enter email to make admin"
                />
                <Button
                  onClick={handleAddAdmin}
                  disabled={loading || !email}
                  className="w-full"
                >
                  {loading ? 'Adding...' : 'Add Admin User'}
                </Button>
              </div>
            </CardBody>
          </Card>

          {message && (
            <Card className="mb-6 border-green-200 bg-green-50">
              <CardBody>
                <p className="text-green-800">{message}</p>
              </CardBody>
            </Card>
          )}

          {error && (
            <Card className="mb-6 border-red-200 bg-red-50">
              <CardBody>
                <p className="text-red-800">{error}</p>
              </CardBody>
            </Card>
          )}

          <Card>
            <CardHeader>
              <h2 className="iepa-heading-2">Quick Links</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                <a
                  href="/debug-admin"
                  className="block p-3 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors"
                >
                  Debug Admin Access
                </a>
                <a
                  href="/dashboard?testAdmin=true"
                  className="block p-3 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors"
                >
                  Access Dashboard (Test Mode)
                </a>
                <a
                  href="/dashboard"
                  className="block p-3 bg-purple-100 text-purple-800 rounded-lg hover:bg-purple-200 transition-colors"
                >
                  Try Dashboard (Normal Mode)
                </a>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>
    </div>
  );
}
