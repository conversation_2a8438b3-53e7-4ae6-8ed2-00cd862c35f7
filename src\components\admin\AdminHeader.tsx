'use client';

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import { AdminUser } from '@/hooks/useAdminAccess';
import { CONFERENCE_YEAR } from '@/lib/conference-config';
import { getPageTitle } from '@/lib/breadcrumb-config';
import { usePathname } from 'next/navigation';
import {
  FiShield,
  FiUser,
  FiLogOut,
  FiBell,
  FiSettings,
  FiHome,
  FiMenu,
} from 'react-icons/fi';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import Link from 'next/link';

interface AdminHeaderProps {
  adminUser: AdminUser | null;
  onMobileMenuToggle?: () => void;
}

export default function AdminHeader({ adminUser, onMobileMenuToggle }: AdminHeaderProps) {
  const { user, signOut } = useAuth();
  const pathname = usePathname();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const getInitials = (email: string) => {
    return email
      .split('@')[0]
      .split('.')
      .map(part => part.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2);
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'destructive';
      case 'admin':
        return 'default';
      default:
        return 'secondary';
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'Super Admin';
      case 'admin':
        return 'Admin';
      default:
        return 'User';
    }
  };

  // Get current page title
  const currentPageTitle = getPageTitle(pathname || '/admin');

  return (
    <header className="bg-white border-b border-gray-200 shadow-sm">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left side - Mobile menu, logo, title, and page title */}
          <div className="flex items-center space-x-6">
            {/* Mobile Menu Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onMobileMenuToggle}
              className="lg:hidden"
            >
              <FiMenu className="w-5 h-5" />
            </Button>

            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-[var(--iepa-primary-blue)] rounded-lg flex items-center justify-center">
                <FiShield className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  IEPA {CONFERENCE_YEAR} Admin
                </h1>
                <p className="text-sm text-gray-500">
                  Annual Meeting Administration
                </p>
              </div>
            </div>
            {/* Page Title */}
            <div className="hidden md:block">
              <h2 className="text-lg font-semibold text-gray-900">
                {currentPageTitle}
              </h2>
            </div>
          </div>

          {/* Right side - User menu and actions */}
          <div className="flex items-center space-x-4">
            {/* Quick Actions */}
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" asChild>
                <Link href="/">
                  <FiHome className="w-4 h-4 mr-2" />
                  Public Site
                </Link>
              </Button>

              <Button variant="outline" size="sm">
                <FiBell className="w-4 h-4" />
              </Button>
            </div>

            {/* User Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex items-center space-x-3 p-2"
                >
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="bg-[var(--iepa-primary-blue)] text-white text-sm">
                      {user?.email ? getInitials(user.email) : 'AD'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900">
                        {user?.email?.split('@')[0] || 'Admin'}
                      </span>
                      {adminUser && (
                        <Badge
                          variant={getRoleBadgeColor(adminUser.role)}
                          className="text-xs"
                        >
                          {getRoleDisplayName(adminUser.role)}
                        </Badge>
                      )}
                    </div>
                    <span className="text-xs text-gray-500">{user?.email}</span>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Admin Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/admin/profile" className="flex items-center">
                    <FiUser className="w-4 h-4 mr-2" />
                    Profile Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/admin/settings" className="flex items-center">
                    <FiSettings className="w-4 h-4 mr-2" />
                    Admin Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleSignOut}
                  className="text-red-600 focus:text-red-600"
                >
                  <FiLogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
}
